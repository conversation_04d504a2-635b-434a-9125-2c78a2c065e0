package utils

import (
	"context"
	"testing"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
)

func TestCalculateNavChangePercentage(t *testing.T) {
	ctx := context.Background()
	logger.Init(cfg.TestEnv)
	tests := []struct {
		name string
		fund *mfPb.MutualFund
		want float64
	}{
		{
			name: "positive change",
			fund: &mfPb.MutualFund{
				Nav: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100,
					Nanos:        500000000, // 100.50
				},
				DailyNavChange: 2.5,
			},
			want: 2.55, // (2.5 * 100) / (100.50 - 2.5)
		},
		{
			name: "negative change",
			fund: &mfPb.MutualFund{
				Nav: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100,
					Nanos:        0,
				},
				DailyNavChange: -1.5,
			},
			want: -1.48, // (-1.5 * 100) / (100 - (-1.5))
		},
		{
			name: "zero change",
			fund: &mfPb.MutualFund{
				Nav: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100,
					Nanos:        0,
				},
				DailyNavChange: 0,
			},
			want: 0,
		},
		{
			name: "invalid NAV",
			fund: &mfPb.MutualFund{
				Nav:            nil,
				DailyNavChange: 1.5,
			},
			want: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := CalculateNavChangePercentage(ctx, tt.fund)
			if got != tt.want {
				t.Errorf("CalculateNavChangePercentage() \ngot : %v \nwant : %v", got, tt.want)
				return
			}
		})
	}
}

func TestGetAmountFromMoney(t *testing.T) {
	ctx := context.Background()
	logger.Init(cfg.TestEnv)
	tests := []struct {
		name    string
		nav     *moneyPb.Money
		want    float64
		wantErr bool
	}{
		{
			name: "valid positive amount",
			nav: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        100,
				Nanos:        500000000, // 100.50
			},
			want:    100.50,
			wantErr: false,
		},
		{
			name: "zero amount",
			nav: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        0,
				Nanos:        0,
			},
			want:    0,
			wantErr: false,
		},
		{
			name:    "nil money",
			nav:     nil,
			want:    0,
			wantErr: true,
		},
		{
			name: "negative amount",
			nav: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        -50,
				Nanos:        -500000000, // -50.50
			},
			want:    -50.50,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetAmountFromMoney(ctx, tt.nav)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAmountFromMoney() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr == true {
				return
			}
			if got != tt.want {
				t.Errorf("GetAmountFromMoney() \ngot : %v \nwant : %v", got, tt.want)
				return
			}
		})
	}
}
