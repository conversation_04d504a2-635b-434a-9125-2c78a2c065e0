package utils

import (
	"context"
	"fmt"
	"math"
	"strconv"

	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
)

func CalculateNavChangePercentage(ctx context.Context, fund *mfPb.MutualFund) float64 {
	navAmt, err := GetAmountFromMoney(ctx, fund.GetNav())
	if err != nil {
		return 0
	}
	delta := fund.GetDailyNavChange() * 100 / float32(math.Max(1, navAmt-float64(fund.GetDailyNavChange())))
	val, err := strconv.ParseFloat(fmt.Sprintf("%.2f", delta), 64)
	if err != nil {
		return 0
	}
	return val
}

func GetAmountFromMoney(ctx context.Context, nav *moneyPb.Money) (float64, error) {
	amt, err := money.ToString(nav, 2)
	if err != nil {
		logger.Error(ctx, "error getting amount from NAV", zap.Any("NAV", nav), zap.Error(err))
		return 0, err
	}
	val, err := strconv.ParseFloat(amt, 64)
	if err != nil {
		return 0, err
	}
	return val, nil
}
