// nolint
package order_type_processor

import (
	"context"
	errors2 "errors"
	"fmt"
	"strconv"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	"github.com/epifi/gamma/api/savings"
	genConf "github.com/epifi/gamma/investment/config/genconf"
	"github.com/epifi/gamma/investment/mutualfund/dao"
	ltp "github.com/epifi/gamma/investment/mutualfund/order/lockin_type_processor"
	"github.com/epifi/gamma/investment/mutualfund/order/update_order"
	"github.com/epifi/gamma/investment/mutualfund/order/validator"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	txn "github.com/epifi/be-common/pkg/storage"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

const (
	DB_TRANSACTION_COUNT = uint(2)
)

var (
	ErrOrderAmtExceedRedeemableAmount          = fmt.Errorf("unitsToSell is greater than the totalRedeemableUnits")
	RedemptionConstraintsValidationError       = errors.New("PurchaseConstraintsValidationError")
	RedemptionConstraintsValidationMinKycError = errors.New("RedemptionConstraintsValidationMinKycError")
	ValidationErrorToFailureReasonMap          = map[error]orderPb.FailureReason{
		validator.FolioClosurePendingOrdersValidatorError: orderPb.FailureReason_REDEMPTION_FAILURE_FOLIO_CLOSURE_ORDER_PENDING_ORDERS_PRESENT,
	}
)

type SellOrderProcessor struct {
	lockinTypeProcessorFactory *ltp.LockinProcessorFactory
	idempotentTxnExecutor      storagev2.IdempotentTxnExecutor
	folioDao                   dao.FolioLedgerDao
	orderDao                   dao.OrderDao
	orderStatusDao             dao.OrderStatusUpdateDao
	cfg                        *genConf.Config
	savingsClient              savings.SavingsClient
	defaultOrderValidators     []validator.Validator
	requestValidators          []validator.Validator
	orderStatusNotifier        update_order.OrderStatusNotifier
}

func NewSellOrderProcessor(lockinTypeProcessorFactory *ltp.LockinProcessorFactory,
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor,
	folioDao dao.FolioLedgerDao,
	orderDao dao.OrderDao,
	cfg *genConf.Config,
	orderStatusDao dao.OrderStatusUpdateDao,
	savingsClient savings.SavingsClient,
	orderStatusNotifier update_order.OrderStatusNotifier,
	panAadhaarLinkValidator *validator.PanAadhaarLinkValidator) *SellOrderProcessor {
	return &SellOrderProcessor{
		lockinTypeProcessorFactory: lockinTypeProcessorFactory,
		idempotentTxnExecutor:      idempotentTxnExecutor,
		folioDao:                   folioDao,
		orderDao:                   orderDao,
		cfg:                        cfg,
		savingsClient:              savingsClient,
		defaultOrderValidators:     []validator.Validator{validator.NewFolioClosureOrderValidator(orderDao)},
		orderStatusDao:             orderStatusDao,
		orderStatusNotifier:        orderStatusNotifier,
		requestValidators:          []validator.Validator{panAadhaarLinkValidator},
	}
}

func (s *SellOrderProcessor) validateRequest(ctx context.Context, req *orderPb.CreateOrderRequest, fundInfo *mfPb.MutualFund, amcInfo *mfPb.AmcInfo) (error, string) {
	totalPendingAmount, err := s.getTotalMoneyCreditPending(ctx, req.ActorId)
	if err != nil {
		return RedemptionConstraintsValidationError, fmt.Sprintf("error getting total amount of sell orders pending, err: %v", err)
	}
	totalAmount, err := moneyPb.Sum(totalPendingAmount, req.Amount)
	if err != nil {
		return RedemptionConstraintsValidationError, fmt.Sprintf("error in calculating sum of pending sell orders and current sell order. Failed to add %s & %s",
			totalPendingAmount.String(), req.Amount.String())
	}
	resp, err := s.savingsClient.IsTxnAllowed(ctx, &savings.IsTxnAllowedRequest{
		Identifier: &savings.IsTxnAllowedRequest_PrimaryAccountHolderActor{PrimaryAccountHolderActor: req.ActorId},
		Amount:     totalAmount,
		TxnType:    savings.IsTxnAllowedRequest_CREDIT,
	})
	if err != nil {
		return RedemptionConstraintsValidationError, fmt.Sprintf("savingsClient.IsTxnAllowed RPC failed: %v", err)
	}

	if resp.GetStatus().Code == uint32(savings.IsTxnAllowedResponse_NOT_ALLOWED_MIN_KYC_CHECK_FAILURE) {
		logger.Info(ctx, fmt.Sprintf("transaction not allowed, reason: %v", resp.NotAllowedReason),
			zap.String(logger.ACTOR_ID, req.ActorId))
		return RedemptionConstraintsValidationMinKycError, resp.NotAllowedReason.String()
	}
	if !resp.Status.IsSuccess() {
		return RedemptionConstraintsValidationError, fmt.Sprintf("non success reponse from savingsClient.IsTxnAllowed RPC: %v", resp.GetStatus())
	}

	for _, requestValidator := range s.requestValidators {
		validationErr, reason := requestValidator.Validate(ctx, &validator.ValidationRequest{Order: &orderPb.Order{ActorId: req.ActorId}})
		if validationErr != nil {
			return validationErr, reason
		}
	}
	return nil, ""
}

//nolint:funlen
func (s *SellOrderProcessor) CreateOrder(ctx context.Context, req *orderPb.CreateOrderRequest, fundInfo *mfPb.MutualFund, amcInfo *mfPb.AmcInfo) ([]*orderPb.Order, error, orderPb.CreateOrderResponse_Status) {
	var err error
	err, reason := s.validateRequest(ctx, req, fundInfo, amcInfo)

	if err != nil {
		logger.Error(ctx, fmt.Sprintf("validation failed with reason: %s ", reason), zap.Error(err),
			zap.String(logger.ACTOR_ID, req.ActorId),
			zap.String(logger.MF_ID, req.GetMutualFundId()), zap.String(logger.ACTOR_ID, req.GetActorId()),
			zap.String(logger.CLIENT, req.GetClient().String()), zap.String(logger.CLIENT_REQUEST_ID, req.ClientOrderId))
		return nil, err, ValidationErrorToOrderCreationFailureReasonMap[err]
	}
	processor := s.lockinTypeProcessorFactory.GetLockinProcessor(fundInfo)

	var createdOrders []*orderPb.Order
	err = s.idempotentTxnExecutor.RunIdempotentTxn(ctx, DB_TRANSACTION_COUNT, func(txnCtx context.Context) error {

		var txnErr error
		folios, txnErr := s.fetchFolios(txnCtx, req)
		if txnErr != nil {
			return txnErr
		}

		pendingSellOrders, txnErr := s.orderDao.GetNonTerminalOrdersByFilterOptions(txnCtx,
			dao.WithActorId(req.ActorId), dao.WithMutualFundId(req.MutualFundId),
			dao.WithOrderType(orderPb.OrderType_SELL))
		if txnErr != nil {
			return txnErr
		}

		totalRedeemableUnits, txnErr := processor.GetWithdrawableUnits(txnCtx, folios, pendingSellOrders, fundInfo)
		if txnErr != nil {
			return txnErr
		}

		folioWisePendingSellUnits, txnErr := s.getPendingUnitsFolioWise(pendingSellOrders)
		if txnErr != nil {
			return txnErr
		}

		var unitsToSell decimal.Decimal
		if req.IsWithdrawAll {
			unitsToSell = totalRedeemableUnits
		} else {
			unitsToSell = moneyPb.ToDecimal(req.Amount).Div(moneyPb.ToDecimal(fundInfo.Nav))
		}

		if unitsToSell.GreaterThan(totalRedeemableUnits) {
			logger.Error(txnCtx, fmt.Sprintf("unitsToSell :%s is greater than the totalRedeemableUnits: %s",
				unitsToSell, totalRedeemableUnits))
			return ErrOrderAmtExceedRedeemableAmount
		}

		newSellOrders, txnErr := s.createSellOrdersForFolios(txnCtx, folios, req, fundInfo, amcInfo, folioWisePendingSellUnits, unitsToSell, processor)
		if txnErr != nil {
			return txnErr
		}

		folioFundIdtoFolioDetailsMap := s.getFolioFundIdToFolioDetailsMap(folios)

		/*
			ToDO(Junaid):
				If we decide to support many folios per fund willingly, then convert this into a batch
				create call. Currently, multiple folios per fund is an edge case and will be irrelevant for
				most cases.
		*/
		for _, sellOrder := range newSellOrders {
			newSellOrder, txnErr := s.orderDao.Create(txnCtx, sellOrder)
			if txnErr != nil {
				return txnErr
			}

			validators := s.getDefaultOrderValidators()
			isValidationSuccess := true
			for _, val := range validators {
				validationErr, errorReason := val.Validate(txnCtx, &validator.ValidationRequest{
					Order: newSellOrder,
				})
				if validationErr != nil {
					logger.Error(ctx, "validation error", zap.Error(validationErr), zap.String(logger.REASON, errorReason), zap.String(logger.CLIENT_REQUEST_ID, req.ClientOrderId))
					daoErr := update_order.UpdateOrder(txnCtx, s.orderDao, s.orderStatusDao, s.folioDao,
						&orderPb.Order{Id: newSellOrder.Id, OrderStatus: orderPb.OrderStatus_FAILURE,
							FailureReason: ValidationErrorToFailureReasonMap[validationErr]},
						[]orderPb.OrderFieldMask{orderPb.OrderFieldMask_ORDER_STATUS, orderPb.OrderFieldMask_FAILURE_REASON},
						true, s.idempotentTxnExecutor, s.orderStatusNotifier)
					if daoErr != nil {
						return daoErr
					}
					isValidationSuccess = false
					break
				}
			}

			if isValidationSuccess {
				txnErr = s.performStepsPostOrderCreation(txnCtx, sellOrder, folioFundIdtoFolioDetailsMap)
				if txnErr != nil {
					return txnErr
				}
			}

			createdOrders = append(createdOrders, newSellOrder)
		}
		return nil
	})

	if err != nil {
		switch {
		case txn.IsErrorAmbiguous(err):
			logger.Error(ctx, logger.TXN_AMBIGUOUS_ERR+" error while creating new sell orders",
				zap.String(logger.ACTOR_ID, req.ActorId), zap.String(logger.MF_ID, req.MutualFundId), zap.Error(err))
		case errors2.Is(err, ErrOrderAmtExceedRedeemableAmount):
			return nil, errors.Wrap(err, "order exceeded withdrawal limit"), orderPb.CreateOrderResponse_WITHDRAWAL_AMOUNT_EXCEEDS_REDEEMABLE_AMOUNT

		default:
			logger.Error(ctx, "error while creating new sell orders",
				zap.String(logger.ACTOR_ID, req.ActorId), zap.String(logger.MF_ID, req.MutualFundId), zap.Error(err))
		}
		return nil, errors.Wrap(err, "error while creating new sell orders"), orderPb.CreateOrderResponse_INTERNAL
	}

	return createdOrders, nil, orderPb.CreateOrderResponse_OK
}

//nolint:unused
func (s *SellOrderProcessor) getDefaultOrderValidators() []validator.Validator {

	var validators []validator.Validator

	validators = append(validators, s.defaultOrderValidators...)

	return validators
}

func (s *SellOrderProcessor) fetchFolios(ctx context.Context, req *orderPb.CreateOrderRequest) ([]*mfPb.FolioLedger, error) {
	var folios []*mfPb.FolioLedger
	var err error

	folios, err = s.folioDao.GetByFilterOptions(ctx, mfPb.FolioLedgerMask_CREATED_AT, false,
		dao.WithActorId(req.ActorId), dao.WithMutualFundId(req.MutualFundId))

	if err != nil {
		logger.Error(ctx, "Error while fetching folio", zap.String(logger.ACTOR_ID, req.ActorId),
			zap.String(logger.CLIENT, req.GetClient().String()), zap.String(logger.MF_ID, req.GetMutualFundId()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientOrderId()), zap.Error(err))
		return nil, errors.Wrap(err, "Failed to fetch folio details")
	}
	return folios, err
}

func (s *SellOrderProcessor) getPendingUnitsFolioWise(pendingOrders []*orderPb.Order) (map[string]float64, error) {
	if len(pendingOrders) == 0 {
		return nil, nil
	}

	folioWisePendingSellUnitsMap := make(map[string]float64)
	for _, currentOrder := range pendingOrders {
		// This is unexpected and should never happen as all sell Orders should have a folio number attached to it.
		if len(currentOrder.FolioId) == 0 {
			return nil, fmt.Errorf("folio is empty for orderID: %s", currentOrder.Id)
		}
		folioWisePendingSellUnitsMap[currentOrder.FolioId] += currentOrder.Units
	}
	return folioWisePendingSellUnitsMap, nil
}

func (s *SellOrderProcessor) createSellOrdersForFolios(ctx context.Context, folios []*mfPb.FolioLedger, req *orderPb.CreateOrderRequest,
	fundInfo *mfPb.MutualFund, amcInfo *mfPb.AmcInfo, folioWisePendingSellUnits map[string]float64, unitsToSell decimal.Decimal, processor ltp.Processor) ([]*orderPb.Order, error) {
	var orders []*orderPb.Order
	var err error

	var remainingAmount *money.Money
	if req.IsWithdrawAll {
		remainingAmount = moneyPb.Multiply(fundInfo.Nav, unitsToSell)
	} else {
		remainingAmount = req.Amount
	}
	index := 0
	for _, folio := range folios {
		pendingSellUnitsForCurrentFolio := decimal.Zero
		if _, ok := folioWisePendingSellUnits[folio.FolioId]; ok {
			pendingSellUnitsForCurrentFolio = decimal.NewFromFloat(folioWisePendingSellUnits[folio.FolioId])
		}

		var sellOrder *orderPb.Order

		// passing empty list in pendingSellOrders field as we have PendingSellUnitsForCurrentFolio already calculated.
		withddrawableUnitsForFolio, withdrawableUnitsErr := processor.GetWithdrawableUnits(ctx, []*mfPb.FolioLedger{folio}, []*orderPb.Order{}, fundInfo)
		if withdrawableUnitsErr != nil {
			return nil, withdrawableUnitsErr
		}
		// withdrawableUnitsInCurrentFolio is defined as (balanceUnits-PendingSellUnitsForCurrentFolio)
		withdrawableUnitsInCurrentFolio := withddrawableUnitsForFolio.Sub(pendingSellUnitsForCurrentFolio)

		logger.SecureInfo(ctx, 0, "trying to create order with folio", zap.String(logger.FOLIO_ID, folio.GetId()),
			zap.Float64("balanced_units", folio.GetBalanceUnits()), zap.Float64("pending_sell_units", folioWisePendingSellUnits[folio.FolioId]),
			zap.String("nav", fundInfo.GetNav().String()), zap.String("remaining_amount", remainingAmount.String()))

		if withdrawableUnitsInCurrentFolio.IsZero() || withdrawableUnitsInCurrentFolio.IsNegative() {
			logger.Error(ctx, fmt.Sprintf("withdrawable units in folio is less or equal to zero: %v", withdrawableUnitsInCurrentFolio), zap.String(logger.FOLIO_ID, folio.FolioId))
			continue
		}

		amountLeftInCurrentFolio := moneyPb.Multiply(fundInfo.Nav, withdrawableUnitsInCurrentFolio)
		isWithdrawAmountPending := false
		// if remainingAmount > amountLeftInCurrentFolio, isWithdrawAmountPending is set to true
		if moneyPb.Compare(remainingAmount, amountLeftInCurrentFolio) == 1 {
			isWithdrawAmountPending = true
		}
		if isWithdrawAmountPending {
			if !moneyPb.IsPositive(amountLeftInCurrentFolio) {
				logger.Error(ctx, fmt.Sprintf("amountLeftInCurrentFolio less than or equal to zero: %v", withdrawableUnitsInCurrentFolio), zap.String(logger.FOLIO_ID, folio.FolioId))
				continue
			}
			sellOrder = s.createNewSellOrder(ctx, req, fundInfo, amcInfo, amountLeftInCurrentFolio, index, folio.FolioId, &withdrawableUnitsInCurrentFolio)
			index++
			orders = append(orders, sellOrder)
			remainingAmount, err = moneyPb.Subtract(remainingAmount, amountLeftInCurrentFolio)
			if err != nil {
				return nil, err
			}
		} else {
			if !moneyPb.IsPositive(remainingAmount) {
				logger.Error(ctx, fmt.Sprintf("remainingAmount less than or equal to zero: %v", withdrawableUnitsInCurrentFolio), zap.String(logger.FOLIO_ID, folio.FolioId))
				continue
			}
			sellOrder = s.createNewSellOrder(ctx, req, fundInfo, amcInfo, remainingAmount, index, folio.FolioId, nil)
			orders = append(orders, sellOrder)
			remainingAmount = moneyPb.ZeroINR().Pb
			break
		}
	}

	// This is unexpected and should never happen
	if !moneyPb.IsZero(remainingAmount) {
		return nil, fmt.Errorf("remainingAmount is not equal to zero even after allocating orders, remaining_amount:%s", remainingAmount.String())
	}

	return orders, nil
}

// TODO (Ayush/Junaid) [Ticket: 18664] : Add support for subtype in Sell Orders
func (s *SellOrderProcessor) createNewSellOrder(ctx context.Context, req *orderPb.CreateOrderRequest, fundInfo *mfPb.MutualFund, amcInfo *mfPb.AmcInfo,
	sellAmount *money.Money, index int, folioId string, sellUnits *decimal.Decimal) *orderPb.Order {

	units := s.getSellUnits(ctx, req, sellUnits, fundInfo, sellAmount)

	orderSubType := orderPb.OrderSubType_ORDER_SUB_TYPE_UNSPECIFIED
	// If GetIsWithdrawAll is true or if sellAmount is less than the minimum redemption amount constraint of the fund,
	// or sellUnits is less than the minimum redemption units constraint of the fund then use
	// OrderSubType_SELL_FULL_REDEMPTION_WITH_FOLIO_CLOSURE
	if req.GetIsWithdrawAll() ||
		(fundInfo.TxnConstraints.RedMnAmt != nil && moneyPb.Compare(sellAmount, fundInfo.TxnConstraints.RedMnAmt) == -1) ||
		units < float64(fundInfo.TxnConstraints.RedMnUnt) {
		orderSubType = orderPb.OrderSubType_SELL_FULL_REDEMPTION_WITH_FOLIO_CLOSURE
	}

	clientOrderId := req.ClientOrderId + "_" + strconv.Itoa(index)
	order := &orderPb.Order{
		ActorId:       req.ActorId,
		MutualFundId:  req.MutualFundId,
		Amount:        sellAmount,
		Units:         units,
		OrderType:     orderPb.OrderType_SELL,
		OrderSubType:  orderSubType,
		OrderStatus:   orderPb.OrderStatus_CREATED,
		Client:        req.Client,
		ClientOrderId: clientOrderId,
		Rta:           amcInfo.Rta,
		PaymentMode:   req.PaymentMode,
		Amc:           fundInfo.Amc,
		FolioId:       folioId,
		AuthId:        req.AuthId,
	}

	return order
}

func (s *SellOrderProcessor) performStepsPostOrderCreation(ctx context.Context, sellOrder *orderPb.Order, folioFundIdToFolioDetailsMap map[string]*mfPb.FolioLedger) error {

	err := s.processStepsPostFolioClosureOrderCreation(ctx, sellOrder, folioFundIdToFolioDetailsMap)
	if err != nil {
		return err
	}
	return nil
}

func (s *SellOrderProcessor) processStepsPostFolioClosureOrderCreation(ctx context.Context, sellOrder *orderPb.Order, folioFundIdToFolioDetailsMap map[string]*mfPb.FolioLedger) error {

	if sellOrder.OrderSubType != orderPb.OrderSubType_SELL_FULL_REDEMPTION_WITH_FOLIO_CLOSURE {
		return nil
	}

	folio, ok := folioFundIdToFolioDetailsMap[s.getKeyForFolioFundIdToFolioDetailsMap(sellOrder.GetMutualFundId(), sellOrder.GetFolioId())]
	if !ok {
		return fmt.Errorf("key :%s is not present in folioFundIdtoFolioDetailsMap", s.getKeyForFolioFundIdToFolioDetailsMap(sellOrder.GetMutualFundId(), sellOrder.GetFolioId()))
	}

	folio.Status = mfPb.FolioStatus_FolioStatus_PENDING_CLOSURE

	_, folioUpdateErr := s.folioDao.Update(ctx, folio, []mfPb.FolioLedgerMask{mfPb.FolioLedgerMask_FOLIO_STATUS})
	if folioUpdateErr != nil {
		return folioUpdateErr
	}

	return nil
}

func (s *SellOrderProcessor) getSellOrderValidators(ctx context.Context, sellOrder *orderPb.Order, folioFundIdToFolioDetailsMap map[string]*mfPb.FolioLedger) []validator.Validator {
	return []validator.Validator{validator.NewFolioClosureOrderValidator(s.orderDao)}
}

func (s *SellOrderProcessor) getSellUnits(ctx context.Context, req *orderPb.CreateOrderRequest, sellUnits *decimal.Decimal,
	fundInfo *mfPb.MutualFund, sellAmount *money.Money) float64 {

	var sellUnitsFloat float64
	var precisionLost bool

	/*
		If sellUnits is not passed and is nil, then calculate it from the amount. If it is passed, use it directly.
	*/
	if sellUnits == nil {
		sellUnitsDecimal := moneyPb.ToDecimal(sellAmount).Div(moneyPb.ToDecimal(fundInfo.Nav))
		sellUnitsFloat, precisionLost = sellUnitsDecimal.Float64()
		if !precisionLost {
			logger.Debug(ctx,
				fmt.Sprintf("precision lost while converting decimal to float, sellUnitsDecimal: %s  to sellUnitsFloat: %v",
					sellUnitsDecimal, sellUnits), zap.String(logger.CLIENT_REQUEST_ID, req.ClientOrderId))
		}
	} else {
		sellUnitsFloat, precisionLost = sellUnits.Float64()
		if !precisionLost {
			logger.Info(ctx,
				fmt.Sprintf("precision lost while converting decimal to float, sellUnitDecimal: %s  to sellUnitsFloat: %v",
					sellUnits, sellUnitsFloat), zap.String(logger.CLIENT_REQUEST_ID, req.ClientOrderId))
		}
	}
	return sellUnitsFloat
}

// getTotalMoneyCreditPending returns total amount pending credit for an actor considering all the active sell orders
func (s *SellOrderProcessor) getTotalMoneyCreditPending(ctx context.Context, actorId string) (*money.Money, error) {
	pendingSellOrders, txnErr := s.orderDao.GetNonTerminalOrdersByFilterOptions(ctx,
		dao.WithActorId(actorId),
		dao.WithOrderType(orderPb.OrderType_SELL))
	if txnErr != nil {
		return nil, txnErr
	}

	if len(pendingSellOrders) == 0 {
		return moneyPb.ZeroINR().Pb, nil
	}

	totalAmount := moneyPb.ZeroINR().Pb
	for _, currentOrder := range pendingSellOrders {
		var err error
		totalAmount, err = moneyPb.Sum(totalAmount, currentOrder.Amount)
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error in calculating total amount of pending sell orders. Failed to add %s & %s",
				totalAmount.String(), currentOrder.Amount.String()),
				zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
		}
	}
	return totalAmount, nil
}

func (s *SellOrderProcessor) getFolioFundIdToFolioDetailsMap(folios []*mfPb.FolioLedger) map[string]*mfPb.FolioLedger {

	folioFundIdToFolioDetailsMap := make(map[string]*mfPb.FolioLedger)

	for _, val := range folios {
		folioFundIdToFolioDetailsMap[s.getKeyForFolioFundIdToFolioDetailsMap(val.GetMutualFundId(), val.GetFolioId())] = val
	}
	return folioFundIdToFolioDetailsMap
}

func (s *SellOrderProcessor) getKeyForFolioFundIdToFolioDetailsMap(mutualFundID, folioID string) string {
	return folioID + "_" + mutualFundID
}
