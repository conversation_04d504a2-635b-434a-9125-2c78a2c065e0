package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"

	pkgErrors "github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	upiActivityPb "github.com/epifi/gamma/api/upi/activity"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	upiPayloadPb "github.com/epifi/gamma/api/upi/payload"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/upi/dao"
)

// CreateUpiOnboardingEntityForLinking - takes account detail and vendor as input, it validates certain conditions required for link action and creates entity in DB
func (p *Processor) CreateUpiOnboardingEntityForLinking(ctx context.Context, req *activityPb.Request) (*activityPb.Response, error) {
	res := &activityPb.Response{}
	lg := activity.GetLogger(ctx)

	linkUpiAccountInfo := &upiPayloadPb.LinkUpiAccountInfo{}
	err := protojson.Unmarshal(req.GetPayload(), linkUpiAccountInfo)
	if err != nil {
		lg.Error("error while unmarshalling payload", zap.Error(err))
		return res, epifitemporal.NewPermanentError(fmt.Errorf("error while unmarshalling payload: %w", err))
	}

	err = p.checkIfOnbDetailsAlreadyPresent(ctx, linkUpiAccountInfo.GetClientId().GetId())
	switch {
	case errors.Is(err, epifierrors.ErrAlreadyExists):
		lg.Info("upi onboarding entity already exists", zap.String(logger.CLIENT_REQUEST_ID, linkUpiAccountInfo.GetClientId().GetId()))
		return res, nil
	case err != nil:
		lg.Error("failed to validate creation request idempotency", zap.Error(err))
		return nil, err
	}

	err = p.validateCreationRequestForLinking(ctx, linkUpiAccountInfo.GetVendor(), linkUpiAccountInfo.GetAccountId())
	if err != nil {
		lg.Error("failed to validate entity creation request", zap.Error(err))
		return res, err
	}

	upiOnboardingDetail := &upiOnboardingPb.UpiOnboardingDetail{
		AccountId:   linkUpiAccountInfo.GetAccountId(),
		Vendor:      linkUpiAccountInfo.GetVendor(),
		ClientReqId: linkUpiAccountInfo.GetClientId().GetId(),
		Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
		Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
	}

	err = p.upiOnbDetailsDao.Create(ctx, upiOnboardingDetail)
	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return res, epifitemporal.NewPermanentError(fmt.Errorf("invalid parameter passed: %w", err))
	case err != nil:
		return res, epifitemporal.NewTransientError(fmt.Errorf("failed to create upi onboarding detail in DB: %w", err))
	}

	return res, nil
}

// validateCreationRequestForLiknking - Validates received req - checks that if given account id with given vendor is already under process of linking and de-linking or not
// if it is, we will not allow new entity creation and pass permanent error
func (p *Processor) validateCreationRequestForLinking(ctx context.Context, vendor commonvgpb.Vendor, accountId string) error {
	filterOption := []storagev2.FilterOption{
		dao.WithVendorFilter(vendor),
	}
	upiOnbDetail, err := p.upiOnbDetailsDao.GetLatestByAccountId(ctx, accountId, filterOption...)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return epifitemporal.NewPermanentError(fmt.Errorf("invalid parameter passed: %w", err))
	case err != nil:
		return epifitemporal.NewTransientError(fmt.Errorf("error fetching latest upi onboarding detail: %w", err))
	}

	switch upiOnbDetail.GetAction() {
	case upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK:
		if upiOnbDetail.GetStatus() != upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED &&
			upiOnbDetail.GetStatus() != upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_INVALID {
			return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("account already linked or found under linking process with given vendor: %s %s", accountId, upiOnbDetail.GetStatus()))
		}
	case upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK:
		if upiOnbDetail.GetStatus() != upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL {
			return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("account already found in de-linking process with given vendor: %s %s", accountId, upiOnbDetail.GetStatus()))
		}
	case upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK, upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_DELINK:
		return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("last action should be delink account, but found something else,  %s %s", accountId, upiOnbDetail.GetAction()))
	default:
		return epifitemporal.NewPermanentError(fmt.Errorf("unsupported action found: %s %s", accountId, upiOnbDetail.GetAction()))
	}

	return nil
}

// CreateUpiOnboardingEntityForDelinking - takes account detail and vendor as input, it validates certain conditions required for delink action and creates entity in DB
func (p *Processor) CreateUpiOnboardingEntityForDelinking(ctx context.Context, req *upiActivityPb.CreateUpiOnboardingEntityForDelinkingRequest) (*upiActivityPb.CreateUpiOnboardingEntityForDelinkingResponse, error) {
	res := &upiActivityPb.CreateUpiOnboardingEntityForDelinkingResponse{}
	lg := activity.GetLogger(ctx)

	delinkUpiAccountWithVendorInfo := &upiPayloadPb.DelinkUpiAccountWithVendorInfo{}
	err := protojson.Unmarshal(req.GetRequestHeader().GetPayload(), delinkUpiAccountWithVendorInfo)
	if err != nil {
		lg.Error("error while unmarshalling payload", zap.Error(err))
		return res, epifitemporal.NewPermanentError(fmt.Errorf("error while unmarshalling payload: %w", err))
	}

	err = p.checkIfOnbDetailsAlreadyPresent(ctx, req.GetRequestHeader().GetClientReqId())
	switch {
	case errors.Is(err, epifierrors.ErrAlreadyExists):
		lg.Info("upi onboarding entity already exists", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return res, nil
	case err != nil:
		lg.Error("failed to validate creation request idempotency", zap.Error(err))
		return nil, err
	}

	err = p.validateCreationRequestForDelinking(ctx, delinkUpiAccountWithVendorInfo.GetVendor(), delinkUpiAccountWithVendorInfo.GetAccountId())
	if err != nil {
		lg.Error("failed to validate entity creation request", zap.String(logger.ACCOUNT_ID, delinkUpiAccountWithVendorInfo.GetAccountId()),
			zap.String(logger.VENDOR, delinkUpiAccountWithVendorInfo.GetVendor().String()), zap.Error(err))
		return res, err
	}

	upiOnboardingDetail := &upiOnboardingPb.UpiOnboardingDetail{
		AccountId:   delinkUpiAccountWithVendorInfo.GetAccountId(),
		Vendor:      delinkUpiAccountWithVendorInfo.GetVendor(),
		Vpa:         delinkUpiAccountWithVendorInfo.GetVpa(),
		ClientReqId: req.GetRequestHeader().GetClientReqId(),
		Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK,
		Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
	}

	err = p.upiOnbDetailsDao.Create(ctx, upiOnboardingDetail)
	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		lg.Error("invalid argument passed for creating upi onboarding detail", zap.String(logger.ACCOUNT_ID, delinkUpiAccountWithVendorInfo.GetAccountId()),
			zap.String(logger.VENDOR, delinkUpiAccountWithVendorInfo.GetVendor().String()), zap.Error(err))
		return res, epifitemporal.NewPermanentError(fmt.Errorf("invalid parameter passed while creating upi onboarding detail: %w", err))
	case err != nil:
		lg.Error("error while creating upi onboarding detail", zap.String(logger.ACCOUNT_ID, delinkUpiAccountWithVendorInfo.GetAccountId()),
			zap.String(logger.VENDOR, delinkUpiAccountWithVendorInfo.GetVendor().String()), zap.Error(err))
		return res, epifitemporal.NewTransientError(fmt.Errorf("failed to create upi onboarding detail in DB: %w", err))
	}

	return res, nil
}

// validateCreationRequestForDelinking - Validates received req - checks that if given account id with given vendor is already under process of delinking and linking or not
// if it is, we will not allow new entity creation and pass permanent error
// nolint
func (p *Processor) validateCreationRequestForDelinking(ctx context.Context, vendor commonvgpb.Vendor, accountId string) error {
	filterOption := []storagev2.FilterOption{
		dao.WithVendorFilter(vendor),
	}
	upiOnbDetail, err := p.upiOnbDetailsDao.GetLatestByAccountId(ctx, accountId, filterOption...)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return epifitemporal.NewPermanentError(fmt.Errorf("invalid parameter passed: %w", err))
	case err != nil:
		return epifitemporal.NewTransientError(fmt.Errorf("error fetching latest upi onboarding detail: %w", err))
	}

	switch {
	case !isStatusTerminal(upiOnbDetail.GetStatus()): // any ongoing action should be completed before delinking the account
		return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("some other action already in progress for this account %s %s", accountId, upiOnbDetail.GetStatus()))

	case upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK:
		if upiOnbDetail.GetStatus() != upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED &&
			upiOnbDetail.GetStatus() != upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_INVALID {
			return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("account already delinked or found under delinking process with given vendor: %s %s", accountId, upiOnbDetail.GetStatus()))
		}
	case upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK:
		if upiOnbDetail.GetStatus() != upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL {
			return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("account already found in linking process with given vendor: %s %s", accountId, upiOnbDetail.GetStatus()))
		}
	}

	return nil
}

// CreateUpiOnboardingEntity - takes account detail and vendor as input, it validates certain conditions required for link action and creates entity in DB
func (p *Processor) CreateUpiOnboardingEntity(ctx context.Context, req *upiActivityPb.CreateUpiOnboardingEntityRequest) (*upiActivityPb.CreateUpiOnboardingEntityResponse, error) {
	var (
		lg  = activity.GetLogger(ctx)
		res = &upiActivityPb.CreateUpiOnboardingEntityResponse{}
	)

	err := p.checkIfOnbDetailsAlreadyPresent(ctx, req.GetRequestHeader().GetClientReqId())
	switch {
	case errors.Is(err, epifierrors.ErrAlreadyExists):
		lg.Info("upi onboarding entity already exists", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return res, nil
	case err != nil:
		lg.Error("failed to validate creation request idempotency", zap.Error(err))
		return nil, err
	}

	switch req.GetAction() {
	case upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK:
		if err = p.validateForLinkingUpiNumber(ctx, req.GetVendor(), req.GetAccountId()); err != nil {
			lg.Error("validation failed for linking upi number", zap.Error(err), zap.String(logger.ACCOUNT_ID, req.GetAccountId()))
			return nil, err
		}
	case upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_DELINK:
		if err = p.validateForDelinkingUpiNumber(ctx, req.GetVendor(), req.GetAccountId()); err != nil {
			lg.Error("validation failed for delinking upi number", zap.Error(err), zap.String(logger.ACCOUNT_ID, req.GetAccountId()))
			return nil, err
		}
	case upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS:
		if err = p.validateCreationRequestForActivatingIntPayments(ctx, req.GetVendor(), req.GetAccountId()); err != nil {
			lg.Error("validation failed for activating international payments", zap.Error(err), zap.String(logger.ACCOUNT_ID, req.GetAccountId()))
			return nil, err
		}
	case upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_INTERNATIONAL_PAYMENTS:
		if err = p.validateCreationRequestForDeactivatingIntPayments(ctx, req.GetVendor(), req.GetAccountId()); err != nil {
			lg.Error("validation failed for deactivating international payments", zap.Error(err), zap.String(logger.ACCOUNT_ID, req.GetAccountId()))
			return nil, err
		}
	case upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
		upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE,
		upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ZERO_BALANCE_LRN_DISABLEMENT:
		if err = p.validateActivationOrDeactivationRequestForLite(ctx, req.GetVendor(), req.GetAccountId(), req.GetAction()); err != nil {
			lg.Error("validation failed for activation/deactivation of upi lite", zap.Error(err), zap.String(logger.ACCOUNT_ID, req.GetAccountId()))
			return nil, err
		}
	default:
		return nil, pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("unsupported action found: %s %s", req.GetAccountId(), req.GetAction()))
	}

	upiOnboardingDetail := &upiOnboardingPb.UpiOnboardingDetail{
		AccountId:   req.GetAccountId(),
		Vendor:      req.GetVendor(),
		Vpa:         req.GetVpa(),
		ClientReqId: req.GetRequestHeader().GetClientReqId(),
		Action:      req.GetAction(),
		Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
		VendorReqId: req.GetVendorReqId(),
		Payload:     req.GetPayload(),
	}

	err = p.upiOnbDetailsDao.Create(ctx, upiOnboardingDetail)
	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		lg.Error("invalid argument passed for creating upi onboarding detail", zap.String(logger.ACCOUNT_ID, req.GetAccountId()),
			zap.String(logger.VENDOR, req.GetVendor().String()), zap.Error(err))
		return res, epifitemporal.NewPermanentError(fmt.Errorf("invalid parameter passed while creating upi onboarding detail: %w", err))
	case err != nil:
		lg.Error("error while creating upi onboarding detail", zap.String(logger.ACCOUNT_ID, req.GetAccountId()),
			zap.String(logger.VENDOR, req.GetVendor().String()), zap.Error(err))
		return res, epifitemporal.NewTransientError(fmt.Errorf("failed to create upi onboarding detail in DB: %w", err))
	}

	return res, nil
}

// validateForLinkingUpiNumber - validates that the upi number can be linked or not
func (p *Processor) validateForLinkingUpiNumber(ctx context.Context, vendor commonvgpb.Vendor, accountId string) error {
	filterOption := []storagev2.FilterOption{
		dao.WithVendorFilter(vendor),
	}
	upiOnbDetail, err := p.upiOnbDetailsDao.GetLatestByAccountId(ctx, accountId, filterOption...)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("no onborading detail found for given account id: %s", accountId))
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("invalid parameter passed: %s", accountId))
	case err != nil:
		return pkgErrors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error fetching latest upi onboarding detail for given account id %s: %s", accountId, err.Error()))
	}

	switch {
	case !isStatusTerminal(upiOnbDetail.GetStatus()):
		// should not allow linking upi number if any account action is going on
		return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("upi account action %s is in invalid state %s", upiOnbDetail.GetAction(), upiOnbDetail.GetStatus()))
	case upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK &&
		upiOnbDetail.GetStatus() != upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED &&
		upiOnbDetail.GetStatus() != upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_INVALID:
		// if last action was to delink the account, it should be failed or invalid
		return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("account already delinked or found under delinking process with given vendor: %s, %s", accountId, upiOnbDetail.GetStatus()))
	case upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK &&
		upiOnbDetail.GetStatus() != upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL:
		// if last action was link the account, it should be successful
		return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("account already found in linking process with given vendor: %s %s", accountId, upiOnbDetail.GetStatus()))
	}

	return nil
}

// validateForDelinkingUpiNumber - validates that the upi number can be delinked or not
func (p *Processor) validateForDelinkingUpiNumber(ctx context.Context, vendor commonvgpb.Vendor, accountId string) error {
	filterOption := []storagev2.FilterOption{
		dao.WithVendorFilter(vendor),
	}
	_, err := p.upiOnbDetailsDao.GetLatestByAccountId(ctx, accountId, filterOption...)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("no onborading detail found for given account id: %s", accountId))
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("invalid parameter passed: %s", accountId))
	case err != nil:
		return pkgErrors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error fetching latest upi onboarding detail for given account id %s: %s", accountId, err.Error()))
	}

	return nil
}

// isStatusTerminal - checks if the status is internal
func isStatusTerminal(status upiOnboardingEnumsPb.UpiOnboardingStatus) bool {
	return status == upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL ||
		status == upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED ||
		status == upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_INVALID
}

// checkIfOnbDetailsAlreadyPresent - validates if upi onb already created/exists for given client request id
func (p *Processor) checkIfOnbDetailsAlreadyPresent(ctx context.Context, clientReqId string) error {
	onbDetail, err := p.upiOnbDetailsDao.GetByClientRequestId(ctx, clientReqId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("invalid parameter passed: %s", clientReqId))
	case err != nil:
		return pkgErrors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error fetching upi onboarding detail: %s", err.Error()))
	case onbDetail.GetStatus() == upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED:
		return pkgErrors.Wrap(epifierrors.ErrAlreadyExists, fmt.Sprintf("upi onb detail already created for given client req id: %s", clientReqId))
	}

	return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("upi onb detail already exists in unexpected state: %s %s", clientReqId, onbDetail.GetStatus()))
}

// validateCreationRequestForActivatingIntPayments - Validates received req - checks that if given account id with given vendor has already raised request for activating or deactivating international payments or not
// if it is, we will not allow new entity creation and pass permanent error
func (p *Processor) validateCreationRequestForActivatingIntPayments(ctx context.Context, vendor commonvgpb.Vendor, accountId string) error {
	filterOption := []storagev2.FilterOption{
		dao.WithVendorFilter(vendor),
	}
	upiOnbDetail, err := p.upiOnbDetailsDao.GetLatestByAccountId(ctx, accountId, filterOption...)
	switch {
	case storagev2.IsRecordNotFoundError(err):
		return nil
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return epifitemporal.NewPermanentError(fmt.Errorf("invalid parameter passed: %w", err))
	case err != nil:
		return epifitemporal.NewTransientError(fmt.Errorf("error fetching latest upi onboarding detail: %w", err))
	}

	if upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS ||
		upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_INTERNATIONAL_PAYMENTS {
		if !isStatusTerminal(upiOnbDetail.GetStatus()) {
			return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("some international payment action is already going on given account: %s %s %s", accountId, upiOnbDetail.GetAction(), upiOnbDetail.GetStatus()))
		}
	}
	return nil
}

// validateCreationRequestForDeactivatingIntPayments -
// Validates received req - checks that if given account id with given vendor
// has already raised request for activating or deactivating international payments
// or not if it is, we will not allow new entity creation and throw permanent error
func (p *Processor) validateCreationRequestForDeactivatingIntPayments(ctx context.Context, vendor commonvgpb.Vendor, accountId string) error {
	filterOption := []storagev2.FilterOption{
		dao.WithVendorFilter(vendor),
	}
	upiOnbDetail, err := p.upiOnbDetailsDao.GetLatestByAccountId(ctx, accountId, filterOption...)
	switch {
	case storagev2.IsRecordNotFoundError(err):
		return nil
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return epifitemporal.NewPermanentError(fmt.Errorf("invalid parameter passed: %w", err))
	case err != nil:
		return epifitemporal.NewTransientError(fmt.Errorf("error fetching latest upi onboarding detail: %w", err))
	}

	if upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS ||
		upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_INTERNATIONAL_PAYMENTS {
		if !isStatusTerminal(upiOnbDetail.GetStatus()) {
			return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("some international payment action is already going on given account: %s %s %s", accountId, upiOnbDetail.GetAction(), upiOnbDetail.GetStatus()))
		}
	}
	return nil
}

// validateActivationOrDeactivationRequestForLite -
// Validates received req - checks that if given account id with given vendor
// has already raised request for activating or deactivating upi lite
// or not if it is, we will not allow new entity creation and throw permanent error
func (p *Processor) validateActivationOrDeactivationRequestForLite(ctx context.Context, vendor commonvgpb.Vendor, accountId string,
	action upiOnboardingEnumsPb.UpiOnboardingAction) error {
	filterOption := []storagev2.FilterOption{
		dao.WithVendorFilter(vendor),
		dao.WithUpiAccountActionFilter([]upiOnboardingEnumsPb.UpiOnboardingAction{upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
			upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE}),
	}
	upiOnbDetail, err := p.upiOnbDetailsDao.GetLatestByAccountId(ctx, accountId, filterOption...)
	switch {
	case storagev2.IsRecordNotFoundError(err):
		return nil
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return epifitemporal.NewPermanentError(fmt.Errorf("invalid parameter passed: %w", err))
	case err != nil:
		return epifitemporal.NewTransientError(fmt.Errorf("error fetching latest upi onboarding detail: %w", err))
	}

	switch {
	case !isStatusTerminal(upiOnbDetail.GetStatus()):
		return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("some upi lite action is already going on with given vendor: %s %s %s", accountId, upiOnbDetail.GetAction(), upiOnbDetail.GetStatus()))
	// If action is for Active UPI Lite then last action should be successful deactivate or unsuccessful activate
	case action == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE:
		// last activate action was successful
		if (upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE &&
			upiOnbDetail.GetStatus() == upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL) ||

			// last deactivation action failed
			((upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE ||
				upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ZERO_BALANCE_LRN_DISABLEMENT) &&
				upiOnbDetail.GetStatus() != upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL) {
			return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("upi lite is already in active state: %s", upiOnbDetail.GetAccountId()))
		}
	// If action is for deactivating upi lite then last action should be successful activate or unsuccessful deactivate
	case action == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE ||
		action == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ZERO_BALANCE_LRN_DISABLEMENT:
		// last activate action failed
		if (upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE &&
			upiOnbDetail.GetStatus() != upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL) ||

			// last deactivation action was successful
			((upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE ||
				upiOnbDetail.GetAction() == upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ZERO_BALANCE_LRN_DISABLEMENT) &&
				upiOnbDetail.GetStatus() == upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL) {
			return pkgErrors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("upi lite is already deactivated: %s", upiOnbDetail.GetAccountId()))
		}
	}
	return nil
}
