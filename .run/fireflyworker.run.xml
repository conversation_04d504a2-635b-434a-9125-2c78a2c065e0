<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="FireflyWorker" type="GoApplicationRunConfiguration" factoryName="Go Application">
    <module name="gamma" />
    <working_directory value="$PROJECT_DIR$" />
    <envs>
      <env name="ENVIRONMENT" value="development" />
      <env name="DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP" value="true" />
      <env name="CONFIG_DIR" value="$PROJECT_DIR$/cmd/worker/firefly/config" />
	  <env name="TEMPORAL_DEBUG" value="true" />
    </envs>
    <kind value="PACKAGE" />
    <package value="github.com/epifi/gamma/cmd/worker/firefly" />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/cmd/worker/firefly/worker.go" />
    <method v="2" />
  </configuration>
</component>
