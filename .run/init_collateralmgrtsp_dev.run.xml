<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="init_collateralmgrtsp_dev" type="ShConfigurationType" singleton="false">
    <option name="SCRIPT_TEXT" value="" />
    <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
    <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/makefile/go_build_helper.sh" />
    <option name="SCRIPT_OPTIONS" value="setup_server_output_dir collateralmgrtsp development" />
    <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
    <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="INDEPENDENT_INTERPRETER_PATH" value="true" />
    <option name="INTERPRETER_PATH" value="/bin/zsh" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="EXECUTE_IN_TERMINAL" value="false" />
    <option name="EXECUTE_SCRIPT_FILE" value="true" />
    <envs>
      <env name="APP" value="collateralmgrtsp" />
      <env name="ENVIRONMENT" value="development" />
    </envs>
    <method v="2" />
  </configuration>
</component>