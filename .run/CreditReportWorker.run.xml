<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="CreditReportWorker" type="GoApplicationRunConfiguration" factoryName="Go Application">
    <module name="gamma" />
    <working_directory value="$PROJECT_DIR$" />
    <envs>
      <env name="CONFIG_DIR" value="$PROJECT_DIR$/cmd/worker/creditreport/config" />
      <env name="ENVIRONMENT" value="development" />
      <env name="TEMPORAL_DEBUG" value="true" />
	  <env name="DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP" value="true" />
    </envs>
    <kind value="PACKAGE" />
    <package value="github.com/epifi/gamma/cmd/worker/creditreport" />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/cmd/worker/creditreport/worker.go" />
    <method v="2" />
  </configuration>
</component>
