Application:
  Environment: "prod"
  Name: "upcomingtransactions"

Aws:
  Region: "ap-south-1"

Server:
  Ports:
    GrpcPort: 8096
    GrpcSecurePort: 9511
    HttpPort: 9999

BudgetingDb:
  AppName: "upcomingtransactions"
  StatementTimeout: 10s
  Name: "budgeting"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Secrets:
  Ids:
    DbCredentials: "prod/rds/epifimetis/budgeting_dev_user"

ProcessRecurringTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 1
  QueueName: "prod-recurring-txns-ds-file-upload-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 6
      TimeUnit: "Minute"

UpdateUpcomingTxnStateSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-upcoming-transactions-update-order-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 8
      TimeUnit: "Second"

DsSubscriptionsIngestionParams:
  S3BucketName: "epifi-data-services"
  NumOfGoroutinesToUpsert: 3
  AllowedFailurePercentage: 0.1
  FilePathInBucket: "subscriptions_recurring_txns/active_subscriptions.json"

DisableDSSuggestions: true
