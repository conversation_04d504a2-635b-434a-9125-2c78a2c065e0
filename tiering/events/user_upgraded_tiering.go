//nolint:dupl
package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

type UserUpgradedTiering struct {
	ActorId             string
	ProspectId          string
	EventId             string
	Timestamp           time.Time
	EventType           string
	EventName           string
	Provenance          string
	FromTier            string
	ToTier              string
	Reason              string
	IsUserActivityEvent bool
}

func NewUserUpgradedTiering(actorId string, timestamp time.Time, provenance, fromTier, toTier, reason string) *UserUpgradedTiering {
	return &UserUpgradedTiering{
		ActorId:             actorId,
		ProspectId:          "",
		Timestamp:           timestamp,
		EventId:             uuid.New().String(),
		EventType:           events.EventTrack,
		EventName:           EventUserUpgradedTiering,
		Provenance:          provenance,
		FromTier:            fromTier,
		ToTier:              toTier,
		Reason:              reason,
		IsUserActivityEvent: true,
	}
}

func (s *UserUpgradedTiering) GetEventType() string {
	return s.EventType
}

func (s *UserUpgradedTiering) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *UserUpgradedTiering) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *UserUpgradedTiering) GetEventId() string {
	return s.EventId
}

func (s *UserUpgradedTiering) GetUserId() string {
	return s.ActorId
}

func (s *UserUpgradedTiering) GetProspectId() string {
	return s.ProspectId
}

func (s *UserUpgradedTiering) GetEventName() string {
	return s.EventName
}
