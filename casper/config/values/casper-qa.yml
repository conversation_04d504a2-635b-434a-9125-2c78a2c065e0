Application:
  Environment: "qa"
  Name: "casper"

Server:
  Ports:
    GrpcPort: 9091
    GrpcSecurePort: 9515
    HttpPort: 9999
    HttpPProfPort: 9990

CasperDb:
  AppName: "casper"
  StatementTimeout: 30s
  Name: "casper"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 30
  MaxIdleConn: 10
  MaxConnTtl: "30m"

RetryRedemptionEventsSqsPublisher:
  QueueName: "qa-retry-offer-redemption-queue"

RetryRedemptionEventsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 3
  PollingDuration: 20
  MaxMessages: 7
  QueueName: "qa-retry-offer-redemption-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 15
      MaxAttempts: 9
      TimeUnit: "Second"

ExchangerOrderOrchestrationEventsSqsPublisher:
  QueueName: "qa-casper-exchanger-order-orchestration-queue"

ExchangerOrderOrchestrationEventsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "qa-casper-exchanger-order-orchestration-queue"
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 8
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 10
          MaxAttempts: 1
          TimeUnit: "Minute"
      MaxAttempts: 9
      CutOff: 8

ExchangerOrderOrchestrationEventsSqsPublisherDlq:
  QueueName: "qa-dlq-casper-exchanger-order-orchestration-queue"

ExchangerOrderOrchestrationEventsSqsSubscriberDlq:
  StartOnServerStart: false
  NumWorkers: 10
  PollingDuration: 20
  MaxMessages: 2
  QueueName: "qa-dlq-casper-exchanger-order-orchestration-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 1
      MaxAttempts: 10
      TimeUnit: "Hour"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1s
    Namespace: "casper"

ExchangerOrderNotificationCustomDelayPublisher:
  DestQueueName: "qa-casper-exchanger-order-notification-delay-queue"
  OrchestratorSqsPublisher:
    QueueName: "qa-custom-delay-orchestrator-queue"

ExchangerOrderNotificationSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-casper-exchanger-order-notification-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

VistaraUploadFileSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-casper-vistara-upload-file-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

VistaraDownloadFileSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-casper-vistara-download-file-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

ItcUploadFileSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-casper-itc-upload-file-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

ItcDownloadFileSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-casper-itc-download-file-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbCredentials: "qa/rds/postgres/casper"
    RudderWriteKey: "qa/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "qa/gcloud/profiling-service-account-key"
    SlackOauthToken: "qa/rewards/slack-oauth-token"
    ThriweSecretKey: "qa/rewards/thriwe-secret-key"


RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

KmsKeys:
  RedeemedOfferEncryptionKeyId : "bc23f4f7-ebd8-4961-b8e1-a107deb4074a"

Flags:
  TrimDebugMessageFromStatus: false
  EnableExchangerOffersForInternalUsersOnly: false

PostProcessorConfig:
  ExchangerOfferRewardValueMultiplier: 10
  ExchangerOfferMaxRedemptionsForMultiplier: 30

ThriweVendorConfig:
  BaseUrl: "https://staging-epifi.thriwe.com"

Tracing:
  Enable: true

PayloadValidationInfo:
  OfferValidationInfo:
    FiCoinConversionValidation:
      IsEnabled: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

OfferRedemptionStatusUpdateEventsSnsPublisher:
  TopicName: "qa-casper-offer-redemption-status-update-event-topic"

ItcVendorConfig:
  GreenPointsTransferCsvFileS3BucketName: "epifi-qa-rewards"

DynamicElementConfig:
  FiStoreWidgetConfig:
    IsWebPageWithCardDetailsScreenEnabled: true
    MinAndroidVersionForWebPageWithCardDetailsScreen: 330
    MinIosVersionForWebPageWithCardDetailsScreen: 330

DPandaVendorConfig:
  MinAndroidVersionSupportingUpiIntent: 330
  MinIosVersionSupportingUpiIntent:  330

FiStoreOrderNotificationSqsPublisher:
  QueueName: "qa-casper-fi-store-order-notification-queue"

FiStoreOrderNotificationSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-casper-fi-store-order-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

WatsonMeta:
  IsEnabled: true
  IsErrorActivityEnabled: true
  IsUserActivityEnabled: true
  DefaultIssueCategoryIdForCasperRedemptionEvents: "709e904e-be63-507a-ad24-2e4f4aef2832"

IsFiLiteCardSegmentEnabledOnPoshVine: true
