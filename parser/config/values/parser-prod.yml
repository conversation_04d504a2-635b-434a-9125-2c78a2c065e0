SmartParserParams:
  Firehose:
    DeliveryStreamName: "prod-smart-parser-epifi-wealth-txns-delivery-stream"

Application:
  Environment: "prod"
  Name: "parser"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "redis-15404.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15404"
    AuthDetails:
      SecretPath: "prod/redis/pay/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: parser-worker

Server:
  Ports:
    GrpcPort: 8097
    GrpcSecurePort: 9518
    HttpPort: 9999

AWS:
  Region: "ap-south-1"

Secrets:
  Ids:
    TemporalCodecAesKey: "prod/temporal/codec-encryption-key"

Flags:
  TrimDebugMessageFromStatus: true

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

RawTxnProcessorSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-txn-backfill-raw-data-processor-queue"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 2
        Period: 1s
    Namespace: "parser-consumer"
