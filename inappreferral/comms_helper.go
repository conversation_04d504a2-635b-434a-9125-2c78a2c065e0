package inappreferral

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/golang/protobuf/ptypes"
	"github.com/golang/protobuf/ptypes/timestamp"
	"go.uber.org/zap"

	rpcstatus "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	commsPb "github.com/epifi/gamma/api/comms"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"
	inAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	internalerrors "github.com/epifi/gamma/inappreferral/errors"
	"github.com/epifi/gamma/inappreferral/helper"
)

var (
	refereeQualifyingActionPendingExpiryInDays = 7
	finiteCodeChannelToNotificationBody        = map[inAppReferralEnumPb.FiniteCodeChannel]map[inAppReferralEnumPb.FiniteCodeType]string{
		inAppReferralEnumPb.FiniteCodeChannel_IN_APP_REFERRAL: {
			inAppReferralEnumPb.FiniteCodeType_REGULAR: "Add ₹3000 to your account within 7 days of account opening to claim your joining bonus. Tap to add money now",
		},
		inAppReferralEnumPb.FiniteCodeChannel_INFLUENCER_REFERRAL: {
			inAppReferralEnumPb.FiniteCodeType_REGULAR: "Add ₹5000 or more to your Fi Savings Account & earn ₹100 or more. Only 1 reward can be claimed. T&C Apply.",
		},
		inAppReferralEnumPb.FiniteCodeChannel_PHONEPE: {
			inAppReferralEnumPb.FiniteCodeType_PHONEPE_TYPE1: "Add ₹5000 or more to your Fi Savings Account & earn ₹100 or more. Only 1 reward can be claimed. T&C Apply.",
			inAppReferralEnumPb.FiniteCodeType_PHONEPE_TYPE2: "Add ₹5000 or more to your Fi Savings Account & earn ₹100 or more. Only 1 reward can be claimed. T&C Apply.",
		},
		inAppReferralEnumPb.FiniteCodeChannel_GPAY: {
			inAppReferralEnumPb.FiniteCodeType_GPAY_TYPE1: "Add ₹5000 or more to your Fi Savings Account & earn upto ₹250. Only 1 reward can be claimed. T&C Apply.",
			inAppReferralEnumPb.FiniteCodeType_GPAY_TYPE2: "Add ₹5000 or more to your Fi Savings Account & earn flat ₹100. Only 1 reward can be claimed. T&C Apply.",
			inAppReferralEnumPb.FiniteCodeType_GPAY_TYPE3: "Add ₹5000 or more to your Fi Savings Account & earn ₹100 or more. Only 1 reward can be claimed. T&C Apply.",
			inAppReferralEnumPb.FiniteCodeType_GPAY_TYPE4: "Add ₹2000 or more to your Fi Savings Account & earn upto ₹250. Only 1 reward can be claimed. T&C Apply.",
		},
		inAppReferralEnumPb.FiniteCodeChannel_VANTAGE_CIRCLE: {
			inAppReferralEnumPb.FiniteCodeType_VANTAGE_CIRCLE_TYPE1: "Add ₹5000 or more to your Fi Savings Account & earn ₹100 or more. Only 1 reward can be claimed. T&C Apply.",
		},
	}
)

// getNotificationExpiryTimestamp gets the expiry timestamp for a referral notification
func (s *Service) getNotificationExpiryTimestamp(ctx context.Context, actorId string, referralCommsEvent inAppReferralPb.ReferralCommsInfo_ReferralCommsEvent) (*timestamp.Timestamp, error) {
	switch referralCommsEvent {
	case inAppReferralPb.ReferralCommsInfo_REFEREE_QUALIFYING_ACTION_PENDING:
		debitCardPinSetTime, err := s.getDebitCardPinSetTime(ctx, actorId)
		if err != nil {
			return nil, err
		}
		return ptypes.TimestampProto(debitCardPinSetTime.AsTime().AddDate(0, 0, refereeQualifyingActionPendingExpiryInDays))
	default:
		return nil, fmt.Errorf("no matching referral comms event found")
	}
}

// method to check debit card pin set timestamp to set notificaion expiry for claim reward for referral post performing
// qualifying action
func (s *Service) getDebitCardPinSetTime(ctx context.Context, actorId string) (*timestamp.Timestamp, error) {
	onbDetailsRes, err := s.onboardingClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(onbDetailsRes, err); te != nil {
		return nil, fmt.Errorf("onbClient.GetDetails call failed, %w", te)
	}

	onbStageDetails := onbDetailsRes.GetDetails().GetStageDetails()
	debitCardPinSetStageInfo := onbStageDetails.GetStageMapping()[onbPb.OnboardingStage_DEBIT_CARD_PIN_SETUP.String()]
	if debitCardPinSetStageInfo == nil {
		return nil, fmt.Errorf("DEBIT_CARD_PIN_SETUP not found in onb details: %w", internalerrors.ErrDebitCardPinNotSet)
	}
	if debitCardPinSetStageInfo.GetState() != onbPb.OnboardingState_SUCCESS {
		return nil, fmt.Errorf("DEBIT_CARD_PIN_SETUP stage not yet completed: %w", internalerrors.ErrDebitCardPinNotSet)
	}

	debitCardPinSetTime := debitCardPinSetStageInfo.GetLastUpdatedAt().AsTime()
	return ptypes.TimestampProto(debitCardPinSetTime)
}

// sends a notification to the user
func (s *Service) sendReferralNotification(
	ctx context.Context,
	notificationType fcm.NotificationType,
	commonFields *fcm.CommonTemplateFields,
	afterClickAction fcm.AfterClickAction,
	actorId string,
) (string, error) {
	fcmNotification := &fcm.Notification{
		NotificationType: notificationType,
	}
	switch notificationType {
	case fcm.NotificationType_IN_APP:
		fcmNotification.NotificationTemplates = &fcm.Notification_InAppTemplate{
			InAppTemplate: &fcm.InAppTemplate{
				CommonTemplateFields: commonFields,
				AfterClickAction:     afterClickAction,
				NotificationPriority: fcm.InAppNotificationPriority_NOTIFICATION_PRIORITY_CRITICAL,
			},
		}
	case fcm.NotificationType_FULL_SCREEN:
		fcmNotification.NotificationTemplates = &fcm.Notification_FullscreenTemplate{
			FullscreenTemplate: &fcm.FullScreenTemplate{
				CommonTemplateFields:           commonFields,
				FullscreenNotificationCategory: 0,
			},
		}
	default:
		return "", fmt.Errorf("unknonw notification type, %v", notificationType)
	}

	entityId, err := s.getEntityDetailsByActorId(ctx, actorId)
	if err != nil {
		return "", fmt.Errorf("failed to fetch entity details for actor, %w", err)
	}

	res, err := s.commsClient.SendMessage(ctx, &commsPb.SendMessageRequest{
		Type:           commsPb.QoS_BEST_EFFORT,
		Medium:         commsPb.Medium_NOTIFICATION,
		UserIdentifier: &commsPb.SendMessageRequest_UserId{UserId: entityId},
		Message: &commsPb.SendMessageRequest_Notification{
			Notification: &commsPb.NotificationMessage{
				Priority: commsPb.NotificationPriority_NORMAL,
				AndroidConfig: &commsPb.AndroidConfig{
					CollapseKey:          "",
					TimeToLive:           0,
					NotificationDelivery: commsPb.DeliveryQoS_IMMEDIATE,
				},
				Notification: fcmNotification,
			},
		},
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return "", fmt.Errorf("failed to send notification, %w", te)
	}
	return res.GetMessageId(), nil
}

// sendPerformQualifyingActionNotificationToReferee sends a notification reminder to the referee to perform qualifying
// action to claim referral reward
// the notification is only sent if the actor hasn't performed qualifying action, and if the notification has not been
// sent already
// Note: notification is only to be sent if the actor hasn't performed qualifying action and has onboarded via in-app-referral
// nolint:funlen
func (s *Service) sendPerformQualifyingActionNotificationToReferee(ctx context.Context, refereeActorId string) {
	if refereeActorId == "" {
		logger.Error(ctx, "referee actor id is empty. can't proceed for sending notification")
		return
	}
	/*
		take redis lock to prevent sending duplicate notification
	*/
	releaseLock, seqErr := s.acquireLockForSequentialProcessing(ctx, 5*time.Second,
		fmt.Sprintf("INAPP_REFERRAL:REF_PERFORM_QUALIFYING_ACTION_COMM::%s", refereeActorId),
	)
	if seqErr != nil {
		if !errors.Is(seqErr, lock.LockAlreadyAcquired) {
			logger.Error(ctx, "error acquiring lock for sending referrals perform-qualifying-action notification", zap.Error(seqErr))
		}
		return
	}
	defer releaseLock()

	// fetch finite code claim for the referee actor id in order to store the notification id in order to avoid sending the
	// notification multiple times
	fcc, err := s.finiteCodeClaimDao.GetByRefereeActorId(ctx, refereeActorId)
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Info(ctx, "user has not onboarded via in-app-referral, skip sending notification",
			zap.String(logger.ACTOR_ID_V2, refereeActorId),
		)
		return
	}
	if err != nil {
		logger.Error(ctx, "failed to get finite code claim for referee actor id",
			zap.String(logger.ACTOR_ID_V2, refereeActorId), zap.Error(err),
		)
		return
	}

	// Check if notification is already sent
	// If already sent, don't send again
	if fcc.GetCommsInfo() != nil {
		commsEventMap, ok := fcc.GetCommsInfo().ReferralCommsEventMap[inAppReferralPb.ReferralCommsInfo_REFEREE_QUALIFYING_ACTION_PENDING.String()]
		if ok {
			_, messageIdExists := commsEventMap.CommsMediumMessageIdMap[commsPb.Medium_NOTIFICATION.String()]
			if messageIdExists {
				// notification already sent, don't send again.
				logger.Info(ctx, "REFEREE_QUALIFYING_ACTION_PENDING notification already sent for actor",
					zap.String(logger.ACTOR_ID_V2, refereeActorId),
				)
				return
			}
		}
	}

	// fetch notification expiry time
	notificationExpiry, err := s.getNotificationExpiryTimestamp(ctx, refereeActorId, inAppReferralPb.ReferralCommsInfo_REFEREE_QUALIFYING_ACTION_PENDING)
	if err != nil {
		logger.Error(ctx, "failed to fetch notification expiry timestamp",
			zap.String(logger.ACTOR_ID_V2, refereeActorId), zap.Error(err),
		)
		return
	}

	finiteCode, err := s.finiteCodeDao.GetById(ctx, fcc.FiniteCodeId)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to get finite code by id: %s", fcc.FiniteCodeId),
			zap.String(logger.ACTOR_ID_V2, refereeActorId), zap.Error(err),
		)
		return
	}

	finiteCodeTypeToNotificationBody, isFiniteCodeChannelPresent := finiteCodeChannelToNotificationBody[finiteCode.Channel]
	if !isFiniteCodeChannelPresent {
		logger.WarnWithCtx(ctx, fmt.Sprintf("notification body missing for channel: %s, finiteCodeId: %s", finiteCode.Channel, fcc.FiniteCodeId),
			zap.String(logger.ACTOR_ID_V2, refereeActorId),
		)
		return
	}

	notificationBody, isFiniteCodeTypePresent := finiteCodeTypeToNotificationBody[finiteCode.Type]
	if !isFiniteCodeTypePresent {
		logger.WarnWithCtx(ctx, fmt.Sprintf("notification body missing for type: %s, finiteCodeId: %s", finiteCode.Type, fcc.FiniteCodeId),
			zap.String(logger.ACTOR_ID_V2, refereeActorId),
		)
		return
	}

	// TODO (harish): get common field templates from config rather than hard-coding in code
	commonFields := &fcm.CommonTemplateFields{
		Title: "",
		Body:  notificationBody,
		IconAttributes: &fcm.IconAttributes{
			IconUrl: "https://epifi-icons.pointz.in/referrals/referralRewardIcon.png",
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_TRANSFER_IN,
		},
		ExpireAt:                notificationExpiry,
		NotificationReferenceId: fcc.GetId(),
	}

	messageId, err := s.sendReferralNotification(ctx, fcm.NotificationType_IN_APP, commonFields, fcm.AfterClickAction_DISMISS, refereeActorId)
	if err != nil {
		logger.Error(ctx, "failed to send referral perform qualifying action and claim reward notification",
			zap.String(logger.ACTOR_ID_V2, refereeActorId), zap.Error(err),
		)
		return
	}

	logger.Info(ctx, "successfully sent notification to actor for REFEREE_QUALIFYING_ACTION_PENDING",
		zap.String(logger.ACTOR_ID_V2, refereeActorId),
	)

	// update finite code claims' comms_info to hold new comms notification message id for REFEREE_QUALIFYING_ACTION_PENDING
	appendCommsInfo(&inAppReferralPb.ReferralCommsInfo{
		ReferralCommsEventMap: map[string]*inAppReferralPb.ReferralCommsInfo_CommsMediumMessageId{
			inAppReferralPb.ReferralCommsInfo_REFEREE_QUALIFYING_ACTION_PENDING.String(): {
				CommsMediumMessageIdMap: map[string]string{
					commsPb.Medium_NOTIFICATION.String(): messageId,
				},
			},
		},
	}, fcc)

	if err := s.finiteCodeClaimDao.UpdateCommsInfo(ctx, fcc); err != nil {
		logger.Error(ctx, "failed to update comms_info with message id for REFEREE_QUALIFYING_ACTION_PENDING",
			zap.String(logger.ACTOR_ID_V2, refereeActorId),
			zap.Error(err),
		)
		return
	}
}

func appendCommsInfo(commsInfo *inAppReferralPb.ReferralCommsInfo, finiteCodeClaim *inAppReferralPb.FiniteCodeClaim) {
	if (finiteCodeClaim.CommsInfo == nil && commsInfo != nil) ||
		(finiteCodeClaim.CommsInfo.ReferralCommsEventMap == nil && commsInfo != nil) {
		finiteCodeClaim.CommsInfo = commsInfo
		return
	}
	if commsInfo == nil {
		return
	}
	if finiteCodeClaim.CommsInfo.ReferralCommsEventMap == nil {
		finiteCodeClaim.CommsInfo.ReferralCommsEventMap = make(map[string]*inAppReferralPb.ReferralCommsInfo_CommsMediumMessageId)
	}
	for key, val := range commsInfo.ReferralCommsEventMap {
		finiteCodeClaim.CommsInfo.ReferralCommsEventMap[key] = val
	}
}

// sendReferralUnlockedNotification sends an in-app notification to the actor to inform that referrals feature has been
// unlocked for them.
// The notification is sent despite the fact that actor onboarded using referral or not.
//
//nolint:funlen
func (s *Service) sendReferralUnlockedNotification(
	ctx context.Context,
	actorId string,
	inAppReferralUnlock *inAppReferralPb.InAppReferralUnlock,
) {
	// Check if notification is already sent
	// If already sent, don't send again
	if inAppReferralUnlock.GetCommsInfo() != nil {
		commsEventMap, ok := inAppReferralUnlock.GetCommsInfo().ReferralCommsEventMap[inAppReferralPb.ReferralCommsInfo_REFERRER_IN_APP_REFERRAL_UNLOCK.String()]
		if ok {
			_, messageIdExists := commsEventMap.CommsMediumMessageIdMap[commsPb.Medium_NOTIFICATION.String()]
			if messageIdExists {
				return
			}
		}
	}

	commonFields := &fcm.CommonTemplateFields{
		Title: "Referral offer activated for you ✅",
		Body:  "Now earn assured cash rewards of at least ₹300 per referral by inviting your friends to Fi. Start referring now",
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_REFERRALS_INVITE_FRIENDS,
		},
		IconAttributes: &fcm.IconAttributes{
			IconUrl: "https://epifi-icons.pointz.in/referrals/ReferralUnlocked.png",
		},
		// 7 days expiry
		ExpireAt: &timestamp.Timestamp{
			Seconds: time.Now().AddDate(0, 0, 7).Unix(),
		},
		NotificationReferenceId: inAppReferralUnlock.Id,
		ExpiryTimerType:         fcm.ExpiryTimerType_EXPIRY_TIMER_TYPE_HIDDEN,
		EventMeta:               &fcm.EventMeta{TreatmentId: helper.InAppReferralUnlockPushNotifToEventTreatmentMap[inAppReferralPb.ReferralCommsInfo_REFERRER_IN_APP_REFERRAL_UNLOCK]},
	}

	messageID, err := s.sendReferralNotification(ctx, fcm.NotificationType_IN_APP, commonFields, fcm.AfterClickAction_DISMISS, actorId)
	if err != nil {
		if rpcstatus.StatusFromError(err).IsRecordNotFound() {
			logger.Info(ctx, "rnf in sending notif")
		} else {
			logger.Error(ctx, "failed to send unlocked referrals notification", zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
		}
		return
	}

	logger.Info(ctx, "successfully sent in-app notification to user for unlocking referrals",
		zap.String(logger.ACTOR_ID, actorId),
		zap.String("comms id", messageID),
	)

	// update InAppReferralUnlock's comms_info to hold new comms notification message id for REFERRER_IN_APP_REFERRAL_UNLOCK
	helper.AppendCommsInfoInAppReferralUnlock(&inAppReferralPb.ReferralCommsInfo{
		ReferralCommsEventMap: map[string]*inAppReferralPb.ReferralCommsInfo_CommsMediumMessageId{
			inAppReferralPb.ReferralCommsInfo_REFERRER_IN_APP_REFERRAL_UNLOCK.String(): {
				CommsMediumMessageIdMap: map[string]string{
					commsPb.Medium_NOTIFICATION.String(): messageID,
				},
			},
		},
	}, inAppReferralUnlock)

	if err := s.inAppReferralUnlockDao.UpdateCommsInfo(ctx, inAppReferralUnlock); err != nil {
		logger.Error(ctx, "failed to update comms_info with message id for REFERRER_IN_APP_REFERRAL_UNLOCK",
			zap.String(logger.ACTOR_ID, actorId),
			zap.Error(err),
		)
		return
	}
}

// acquireLockForSequentialProcessing acquires lock on the provided key for the given duration and returns a release fn
// to be called in defer
func (s *Service) acquireLockForSequentialProcessing(ctx context.Context, lockDuration time.Duration, lockKey string) (deferRelease func(), err error) {
	lock, lockErr := s.lockManager.GetLock(ctx, lockKey, lockDuration)
	if lockErr != nil {
		return nil, fmt.Errorf("error while taking lock on key: %s, err: %w", lockKey, lockErr)
	}

	return func() {
		if err := lock.Release(epificontext.CloneCtx(ctx)); err != nil {
			logger.Error(epificontext.CloneCtx(ctx), "error while release lock in referrals", zap.Error(err), zap.String("lockKey", lockKey))
		}
	}, nil
}
