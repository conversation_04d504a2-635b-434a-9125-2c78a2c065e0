package datacollector

/*
	NOTE: TEMPORARY FILE TO SUPPORT TransactionCompleted EVENT TRIGGER FOR OPTIMISING MARKETING CAMPAIGNS
	THIS WILL BE MOVED TO MIDDLEWARE SYSTEM
*/

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	actorPb "github.com/epifi/gamma/api/actor"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/paymentinstrument"
	types2 "github.com/epifi/gamma/api/typesv2"
	events2 "github.com/epifi/gamma/inappreferral/events"
	"github.com/epifi/gamma/inappreferral/helper"
)

// init random number generator
// todo: validate the source
// nolint:gosec
var rng = rand.New(rand.NewSource(time.Now().UnixNano()))

var (
	// allowOrderWorkflowsTxnCompleted mentions the order workflows which are allowed for this event
	allowOrderWorkflowsTxnCompleted = []orderPb.OrderWorkflow{orderPb.OrderWorkflow_P2P_FUND_TRANSFER, orderPb.OrderWorkflow_P2P_COLLECT, orderPb.OrderWorkflow_P2P_COLLECT_SHORT_CIRCUIT,
		orderPb.OrderWorkflow_NO_OP, orderPb.OrderWorkflow_URN_TRANSFER, orderPb.OrderWorkflow_ADD_FUNDS, orderPb.OrderWorkflow_ADD_FUNDS_SD, orderPb.OrderWorkflow_ADD_FUNDS_COLLECT,
		orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT, orderPb.OrderWorkflow_COLLECT_ONE_TIME_RECURRING_PAYMENT, orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH,
		orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH, orderPb.OrderWorkflow_MUTUAL_FUNDS_INVEST_POST_PAID, orderPb.OrderWorkflow_P2P_INVESTMENT, orderPb.OrderWorkflow_OFF_APP_UPI}

	// allowedOrderStatusesTxnCompleted mentions the order statuses which are allowed for this event
	allowedOrderStatusesTxnCompleted = []orderPb.OrderStatus{orderPb.OrderStatus_FULFILLED, orderPb.OrderStatus_PAID, orderPb.OrderStatus_SETTLED}

	// overrideOrderWorkflowsAndAllowedStatusesMapTxnCompleted mentions the specific statuses which are allowed for some workflows.
	// Why? There is a possibility that there are multiple legs for a workflow, and we want to tap only on some of them.
	// This will also prevent duplicate emit for the same order event in case we receive multiple events with statuses from the allowed list.
	overrideOrderWorkflowsAndAllowedStatusesMapTxnCompleted = map[orderPb.OrderWorkflow][]orderPb.OrderStatus{
		orderPb.OrderWorkflow_ADD_FUNDS:         {orderPb.OrderStatus_SETTLED},
		orderPb.OrderWorkflow_ADD_FUNDS_COLLECT: {orderPb.OrderStatus_SETTLED},
		// checking only FULFILLED (and not PAID) because we are fine with capturing the intent here
		orderPb.OrderWorkflow_MUTUAL_FUNDS_INVEST_POST_PAID: {orderPb.OrderStatus_FULFILLED},
		// checking only PAID as that's the first leg, and we are fine with capturing the intent here
		orderPb.OrderWorkflow_P2P_INVESTMENT: {orderPb.OrderStatus_PAID},
	}

	// allowedOrderPaymentProtocolsTxnCompleted mentions the allowed list of payment protocols
	allowedOrderPaymentProtocolsTxnCompleted = []paymentPb.PaymentProtocol{paymentPb.PaymentProtocol_INTRA_BANK, paymentPb.PaymentProtocol_NEFT, paymentPb.PaymentProtocol_IMPS,
		paymentPb.PaymentProtocol_CARD, paymentPb.PaymentProtocol_RTGS, paymentPb.PaymentProtocol_UPI, paymentPb.PaymentProtocol_SWIFT}

	// allowedOrderTagsTxnCompleted mentions the tags which are allowed to be present in the order
	// todo: SI,EMI,UPI_MANDATES are not added. Confirm on this
	allowedOrderTagsTxnCompleted = []orderPb.OrderTag{orderPb.OrderTag_DEPOSIT, orderPb.OrderTag_FD, orderPb.OrderTag_SD, orderPb.OrderTag_RD, orderPb.OrderTag_MERCHANT, orderPb.OrderTag_FIT,
		orderPb.OrderTag_CASH, orderPb.OrderTag_WALLET, orderPb.OrderTag_MUTUAL_FUND, orderPb.OrderTag_BHARAT_QR, orderPb.OrderTag_US_STOCKS, orderPb.OrderTag_JUMP_P2P_INVESTMENT,
		orderPb.OrderTag_INTERNATIONAL}

	// allowedOrderTxnTypesTxnCompleted mentions the inferred txn type, i.e. whether it was a DEBIT or CREDIT txn for the actor
	allowedOrderTxnTypesTxnCompleted = []string{"DEBIT"} // DEBIT, CREDIT possible
)

// nolint:funlen
func (c *Service) validateAndPublishTransactionCompletedEvent(ctx context.Context, orderEvent *orderPb.OrderUpdate) error {
	var (
		order        = orderEvent.GetOrderWithTransactions().GetOrder()
		merchantName string
		countryCode  string
	)

	// check for workflow in the allowed list
	if !lo.Contains(allowOrderWorkflowsTxnCompleted, order.GetWorkflow()) {
		logger.Debug(ctx, "ignoring order update event as the workflow is not in the allowed list",
			zap.String(logger.WORKFLOW, order.GetWorkflow().String()), zap.String(logger.ORDER_ID, order.GetId()),
		)
		return nil
	}

	// check for order status in the allowed list
	if !lo.Contains(allowedOrderStatusesTxnCompleted, order.GetStatus()) {
		logger.Debug(ctx, "ignoring order update event as the status is not in the allowed list",
			zap.String(logger.STATUS, order.GetStatus().String()), zap.String(logger.ORDER_ID, order.GetId()),
			zap.String(logger.WORKFLOW, order.GetWorkflow().String()),
		)
		return nil
	}

	// for some workflows, check for the overridden allowed statuses
	if allowedStatuses, ok := overrideOrderWorkflowsAndAllowedStatusesMapTxnCompleted[order.GetWorkflow()]; ok {
		if !lo.Contains(allowedStatuses, order.GetStatus()) {
			logger.Debug(ctx, "ignoring order update event as the status is not in the overridden statuses for the workflow",
				zap.String(logger.STATUS, order.GetStatus().String()), zap.String(logger.ORDER_ID, order.GetId()),
				zap.String(logger.WORKFLOW, order.GetWorkflow().String()),
			)
			return nil
		}
	}

	// ignore if none of the allowed payment protocols exist
	if !c.anyPaymentProtocolsUsedInOrder(orderEvent.GetOrderWithTransactions(), allowedOrderPaymentProtocolsTxnCompleted) {
		logger.Debug(ctx, "ignoring order update event as the protocol is not in the allowed list",
			zap.String(logger.ORDER_ID, order.GetId()), zap.String(logger.STATUS, order.GetStatus().String()),
			zap.String(logger.WORKFLOW, order.GetWorkflow().String()),
		)
		return nil
	}

	// ignore the event if none of the tags are present in the allowed list
	if !lo.Some(allowedOrderTagsTxnCompleted, order.GetTags()) {
		logger.Debug(ctx, "ignoring order update event as the tag is not in the allowed list",
			zap.String(logger.ORDER_ID, order.GetId()), zap.String(logger.WORKFLOW, order.GetWorkflow().String()),
			zap.String(logger.STATUS, order.GetStatus().String()),
		)
		return nil
	}

	/*
		ignore if txn type is not from the allowed ones.
		Note: currently we are concerned only with DEBIT txn-type.
		Reaching till this particular check would mean that we are sure it's not a user-to-user txn, i.e. it's either
		P2M/SD/FD/WEALTH.
	*/
	txnType, err := c.getOrderTxnType(ctx, orderEvent.GetOrderWithTransactions())
	if err != nil {
		logger.Error(ctx, "error in checking order txn type", zap.Error(err), zap.String(logger.ORDER_ID, order.GetId()))
		return fmt.Errorf("error checking order txn type")
	}
	if !lo.Contains(allowedOrderTxnTypesTxnCompleted, txnType) {
		logger.Debug(ctx, "ignoring order update event as the txn type is not in the allowed list",
			zap.String(logger.ORDER_ID, order.GetId()), zap.String(logger.TXN_TYPE, txnType),
			zap.String(logger.STATUS, order.GetStatus().String()), zap.String(logger.WORKFLOW, order.GetWorkflow().String()),
		)
		return nil
	}

	gaid, _ := helper.GetGAIDByBestEffort(ctx, c.userClient, order.GetFromActorId())
	if gaid == "" {
		logger.WarnWithCtx(ctx, "couldn't fetch GAID while emitting TransactionCompleted event")
	}

	if len(orderEvent.GetOrderWithTransactions().GetTransactions()) > 0 {
		piData, err := c.piClient.GetPiById(ctx, &paymentinstrument.GetPiByIdRequest{Id: orderEvent.GetOrderWithTransactions().GetTransactions()[0].GetPiTo()})
		if err != nil {
			logger.Error(ctx, "error in getting pi data for fetching merchant name", zap.Error(err), zap.String(logger.PI_TO, orderEvent.GetOrderWithTransactions().GetTransactions()[0].GetPiTo()))
		}
		merchantName = piData.GetPaymentInstrument().GetVerifiedName()
		rawNotificationDetails := orderEvent.GetOrderWithTransactions().GetTransactions()[0].GetRawNotificationDetails()

		for _, value := range rawNotificationDetails {
			if value.GetCountryCode() != "" {
				countryCode = value.GetCountryCode()
				break
			}
		}
	}

	// trigger rudder event
	c.eventsBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewTransactionCompletedEvent(order.GetFromActorId(),
		c.getRandomSecondsN(c.dyconf.TimestampJitterForTxnCompletedEventInSeconds()), gaid, merchantName, countryCode, order.IsInAppOrder()))
	return nil
}

// anyPaymentProtocolsUsedInOrder checks is any of the txns within an order went via the given payment protocols
func (c *Service) anyPaymentProtocolsUsedInOrder(orderWithTxns *orderPb.OrderWithTransactions, paymentProtocols []paymentPb.PaymentProtocol) bool {
	for _, txn := range orderWithTxns.GetTransactions() {
		if lo.Contains(paymentProtocols, txn.GetPaymentProtocol()) {
			return true
		}
	}

	return false
}

// getOrderTxnType checks if the order is a CREDIT or DEBIT txn.
// Note: It currently prioritises `fromActorId` of the order as we need to check first whether it's a `DEBIT` order or not.
// If it's found that it's a DEBIT order, we don't check for the `toActorId` case.
// In ideal scenario, we should check for both.
func (c *Service) getOrderTxnType(ctx context.Context, orderWithTxns *orderPb.OrderWithTransactions) (string, error) {
	var (
		fromActorId = orderWithTxns.GetOrder().GetFromActorId()
		toActorId   = orderWithTxns.GetOrder().GetToActorId()
	)

	// check type of fromActorId
	fromActorType, err := c.getActorType(ctx, fromActorId)
	if err != nil {
		return "", fmt.Errorf("error fetching fromActorId actor type while checking order txn type: %w", err)
	}
	if fromActorType == types2.Actor_USER {
		return "DEBIT", nil
	}

	// check type of toActorId
	toActorType, err := c.getActorType(ctx, toActorId)
	if err != nil {
		return "", fmt.Errorf("error fetching toActorId actor type while checking order txn type: %w", err)
	}
	if toActorType == types2.Actor_USER {
		return "CREDIT", nil
	}

	return "UNSPECIFIED", nil
}

// checkIfTagExistsInOrder checks for the presence of the given tag in the order
func (c *Service) checkIfTagExistsInOrder(order *orderPb.Order, tag orderPb.OrderTag) bool {
	return orderPb.IsTagExist(order.GetTags(), tag)
}

// getActorType returns the type of the Actor
func (c *Service) getActorType(ctx context.Context, actorId string) (types2.Actor_Type, error) {
	actorRes, err := c.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if rpcErr := epifigrpc.RPCError(actorRes, err); rpcErr != nil {
		logger.Error(ctx, "error fetching actor by id for checking actor type", zap.Error(err), zap.Any(logger.RPC_STATUS, actorRes.GetStatus()),
			zap.String(logger.ACTOR_ID_V2, actorId),
		)
		return types2.Actor_TYPE_UNSPECIFIED, fmt.Errorf("error fetching actor by id for checking type")
	}

	return actorRes.GetActor().GetType(), nil
}

// getRandomSecondsN returns a random seconds value (in duration) in the interval [1, N]
func (c *Service) getRandomSecondsN(n uint) time.Duration {
	randInt := rng.Intn(int(n) + 1) // adding +1 to prevent panic in case the arg value received is 0
	return time.Duration(randInt) * time.Second
}
