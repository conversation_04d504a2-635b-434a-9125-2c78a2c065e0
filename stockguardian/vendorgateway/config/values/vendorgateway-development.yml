# On dev & test env, VNGW server listens on 9098 without any load balancer mapping 443 to 9098
# Hence, callback URL should be to 9098 port and not 443 port
# TLS is terminated at load balancer for VNGw.
# Load balancers are not present in dev localstack and hence VNGw will run on Http in dev & test envs

Application:
  Environment: "development"
  Name: "vendorgateway"

Server:
  Ports:
    GrpcPort: 8081
    GrpcSecurePort: 9522
    HttpPort: 9885

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:

Flags:
  TrimDebugMessageFromStatus: false
  TokenValidation: true

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/tmp/secure.log"
  MaxSizeInMBs: 5
  MaxBackups: 20
