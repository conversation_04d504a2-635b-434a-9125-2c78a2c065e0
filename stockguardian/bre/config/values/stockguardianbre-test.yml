Application:
  Environment: "test"
  Name: "stockguardianbre"
  ServerName: "lending"

Server:
  Ports:
    GrpcPort: 8111
    GrpcSecurePort: 9528
    HttpPort: 9999

AWS:
  Region: "ap-south-1"

RawInhouseBreBucketName: "epifi-raw-dev"
InhouseBreBucketName: "epifi-cc-inhouse-bre-responses"

InHouseBreConfig:
  InhouseBreRawS3BucketName: "epifi-raw-dev"
  InhouseBreS3BucketName: "epifi-cc-inhouse-bre-responses"

Secrets:
  Ids:
    BreCredentials: '{"loanDecisioningSubventionBearerToken": "tIis1v3dkjdj"}'
