CREATE TABLE IF NOT EXISTS recurring_payments (
   id STRING NOT NULL,
   from_actor_id STRING NOT NULL,
   to_actor_id STRING NOT NULL,
   type STRING NOT NULL,
   pi_from STRING NOT NULL,
   pi_to STRING NOT NULL,
   amount JSONB NOT NULL,
   amount_type STRING NOT NULL,
   start_date TIMESTAMPTZ NULL,
   end_date TIMESTAMPTZ NULL,
   recurrence_rule JSONB NOT NULL,
   maximum_allowed_txns INT8 NULL,
   partner_bank STRING NOT NULL,
   preferred_payment_protocol STRING NULL,
   state STRING NOT NULL,
   ownership STRING NOT NULL,
   provenance STRING NULL,
   ui_entry_point STRING NULL,
   initiated_by STRING NULL,
   created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
   updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
   deleted_at TIMESTAMPTZ NULL,
   external_id STRING NOT NULL,
   share_to_payee BOOL NOT NULL DEFAULT true,
   remarks STRING NULL,
   pause_interval JSONB NULL,
   payment_route STRING NOT NULL DEFAULT 'RECURRING_PAYMENT_ROUTE_NATIVE':::STRING,
   entity_ownership STRING NOT NULL DEFAULT 'EPIFI_TECH_V2':::STRING,
   CONSTRAINT recurring_payments_pkey PRIMARY KEY (id ASC),
   UNIQUE INDEX recurring_payments_id_key (id ASC),
   INDEX from_actor_id_lookup_idx (from_actor_id ASC),
   UNIQUE INDEX recurring_payments_external_id_unique_idx (external_id ASC),
   FAMILY "primary" (id, from_actor_id, to_actor_id, type, pi_from, pi_to, partner_bank, preferred_payment_protocol, ownership, provenance, ui_entry_point, initiated_by, amount_type, created_at, deleted_at, external_id, share_to_payee, remarks, payment_route, entity_ownership),
   FAMILY frequently_updated (amount, start_date, end_date, recurrence_rule, maximum_allowed_txns, state, updated_at),
   FAMILY seldom_updated (pause_interval)
);
COMMENT ON TABLE recurring_payments IS 'table to store information related to recurring payments';
COMMENT ON COLUMN recurring_payments.type IS '{"proto_type": "RecurringPayment.RecurringPaymentType", "comment":"types of recurring payment such as standing instruction, upi mandates, e-mandates"}';
COMMENT ON COLUMN recurring_payments.state IS '{"proto_type": "RecurringPayment.RecurringPaymentState", "comment":"current state, it can be ACTIVE, PAUSED, REVOKED, EXPIRED"}';
COMMENT ON COLUMN recurring_payments.pause_interval IS '{"proto_type":"recurringpayment.pauseInterval", "comment": "time interval for which recurring payment is paused"}';
COMMENT ON COLUMN recurring_payments.payment_route IS 'column to identify if a  recurring payment is happening within the fi-federal ecosystem or is it an external';
COMMENT ON COLUMN recurring_payments.entity_ownership IS 'column to identify the ownership of the entities that will be created as a result of this recurring payment flow';
