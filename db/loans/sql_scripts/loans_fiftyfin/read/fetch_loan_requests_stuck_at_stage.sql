select
	lr.id as lr_id,
	lse.id as lse_id,
	lr.orch_id as orch_id,
	lr.actor_id as actor_id,
	lr.type as lr_type,
	lse.step_name as step_name,
	lse.status as lse_status,
	lr.status as lr_status,
	lse.group_stage as group_stage,
	lse.created_at as lse_created_at,
	lse.completed_at as lse_completed_at,
	lr.created_at as lr_created_at
from
	loan_step_executions lse LEFT JOIN loan_requests lr ON lse.ref_id = lr.id
where
    	lr.updated_at > '2023-01-20' AND
		lr.loan_program = 'LOAN_PROGRAM_LAMF' AND
		lse.step_name in (
		            'LOAN_STEP_EXECUTION_STEP_NAME_MARK_LIEN',
		            'LOAN_STEP_EXECUTION_STEP_NAME_KFS',
		            'LOAN_STEP_EXECUTION_STEP_NAME_E_AGREEMENT',
		            'LOAN_STEP_EXECUTION_STEP_NAME_MANDATE',
		            'LOAN_STEP_EXECUTION_STEP_NAME_DRAWDOWN',
		            'LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION'
		            )
order by (lr.id, lse.created_at);
