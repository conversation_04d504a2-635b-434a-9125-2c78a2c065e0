---- deactivating to see abfl offer as per priority to do CUG ---
UPDATE loan_offers
SET deactivated_at = current_timestamp, updated_at = current_timestamp
WHERE id IN ('PALOx7YegXWPS7W/xR7grIqfFQ240717==') AND deactivated_at IS NULL;

UPDATE loan_requests
SET status = 'LOAN_REQUEST_STATUS_CANCELLED', completed_at = current_timestamp , updated_at=current_timestamp
WHERE id in ('PALRZsA4WyS5TpmeJNI8W6WAPQ240802==');
