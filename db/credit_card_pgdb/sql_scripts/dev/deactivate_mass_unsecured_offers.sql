-- add offer for internal user to check intro screen
INSERT INTO cc_offers (id, actor_id, vendor_offer_id, vendor, offer_constraints, valid_since, valid_till, deactivated_at, created_at, updated_at, deleted_at, cc_offer_eligibility_criteria_id, card_program, offer_details) VALUES
('secured-offer-11', 'AC221213FLHlKGRVT3Wx92okHyzSlQ==', 'temp-vendor-offer-id-1', 'FEDERAL', '{"limit": {"units": "0", "currencyCode": "INR"}}', current_timestamp, '2023-12-18 16:00:00.000000 +00:00', null, current_timestamp, current_timestamp, null, 'secured-offer-eligibility-1', 'FEDERAL:REALTIME_BRE:SECURED:FD:FI', null);

INSERT INTO public.cc_offer_eligibility_criteria (id, actor_id, vendor, status, vendor_response, created_at, updated_at, deleted_at, provenance) VALUES
('secured-offer-eligibility-11', 'AC221213FLHlKGRVT3Wx92okHyzSlQ==', 'FEDERAL', 'CC_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED', null, current_timestamp, current_timestamp, null, null);

-- deactivating offers for internal users for mass unsecured card
UPDATE cc_offers set deactivated_at = current_timestamp, updated_at=current_timestamp where id in ('CCOlVsDWCq1SZWx+yRNZ9xfyQ231127==', 'CCO4UdyR9jURSG8K9zIJBIdzw231128==');
