INSERT INTO credit_cards
(id, state, actor_id, account_id, form, network_type, vendor_identifier, vendor, controls_data, issuance_fee, basic_info, limits, deleted_at, card_sku_type)
VALUES
	('card_id_x123', 'CARD_STATE_ACTIVATED',  'act_2', 'account_2', 'CARD_FORM_PHYSICAL', 'CARD_NETWORK_TYPE_VISA', 'bank_id_1', 'VENDOR_M2P', '{}', '{}', '{}', '{}', null, 'CARD_SKU_TYPE_UNSPECIFIED'),
	('card_id_x124', 'CARD_STATE_ACTIVATED', 'act_2', 'account_2', 'CARD_FORM_PHYSICAL', 'CARD_NETWORK_TYPE_VISA', 'bank_id_2', 'VENDOR_M2P', '{}', '{}', '{}', '{}', null, 'CARD_SKU_TYPE_UNSPECIFIED');

INSERT INTO credit_cards (id,state,actor_id,created_at,updated_at,account_id,form,network_type,vendor_identifier,vendor,controls_data,issuance_fee,basic_info,limits,card_sku_type,deleted_at) VALUES
																																																   ('card_id_1','CARD_STATE_ACTIVATED','act_11','2023-05-04 00:00:00.560000 +00:00','2023-05-04 00:00:00.560000 +00:00','acc_id_11','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_11','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_2','CARD_STATE_ACTIVATED','act_12','2023-05-04 00:00:00.560000 +00:00','2023-05-05 00:00:00.560000 +00:00','acc_id_12','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_12','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_3','CARD_STATE_ACTIVATED','act_13','2023-05-04 00:00:00.560000 +00:00','2023-05-06 00:00:00.560000 +00:00','acc_id_13','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_13','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_4','CARD_STATE_ACTIVATED','act_14','2023-05-04 00:00:00.560000 +00:00','2023-05-07 00:00:00.560000 +00:00','acc_id_14','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_14','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_5','CARD_STATE_ACTIVATED','act_15','2023-05-04 00:00:00.560000 +00:00','2023-05-07 00:00:00.560000 +00:00','acc_id_15','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_15','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_6','CARD_STATE_ACTIVATED','act_16','2023-05-04 00:00:00.560000 +00:00','2023-05-07 00:00:00.560000 +00:00','acc_id_16','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_16','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_7','CARD_STATE_ACTIVATED','act_17','2023-05-04 00:00:00.560000 +00:00','2023-05-08 00:00:00.560000 +00:00','acc_id_17','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_17','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_8','CARD_STATE_ACTIVATED','act_18','2023-05-04 00:00:00.560000 +00:00','2023-05-08 00:00:00.560000 +00:00','acc_id_18','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_18','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_9','CARD_STATE_ACTIVATED','act_19','2023-05-04 00:00:00.560000 +00:00','2023-05-08 00:00:00.560000 +00:00','acc_id_19','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_19','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_91','CARD_STATE_ACTIVATED','act_20','2023-05-04 00:00:00.560000 +00:00','2023-05-09 00:00:00.560000 +00:00','acc_id_20','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_20','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_92','CARD_STATE_ACTIVATED','act_21','2023-05-04 00:00:00.560000 +00:00','2023-05-09 00:00:00.560000 +00:00','acc_id_21','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_21','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_93','CARD_STATE_ACTIVATED','act_22','2023-05-04 00:00:00.560000 +00:00','2023-05-10 00:00:00.560000 +00:00','acc_id_22','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_22','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_94','CARD_STATE_ACTIVATED','act_23','2023-05-04 00:00:00.560000 +00:00','2023-05-10 00:00:00.560000 +00:00','acc_id_23','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_23','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_95','CARD_STATE_ACTIVATED','act_24','2023-05-04 00:00:00.560000 +00:00','2023-05-10 00:00:00.560000 +00:00','acc_id_24','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_24','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_96','CARD_STATE_ACTIVATED','act_26','2023-05-04 00:00:00.560000 +00:00','2023-05-09 00:00:00.560000 +00:00','acc_id_25','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_25','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null),
																																																   ('card_id_97','CARD_STATE_CLOSED','act_27','2023-05-04 00:00:00.560000 +00:00','2023-05-09 00:00:00.560000 +00:00','acc_id_25','CARD_FORM_PHYSICAL','CARD_NETWORK_TYPE_VISA','bank_id_25','VENDOR_M2P','{}','{}','{}','{}','CARD_SKU_TYPE_UNSPECIFIED',null);

INSERT INTO card_audits
(id, card_id, change_type, details, created_at, updated_at, deleted_at)
VALUES
	('a5004f18-5d52-4991-82a9-xa1e5010e993', 'card_id_x123', 'CREDIT_CARD_CHANGE_TYPE_UNSPECIFIED', '{}', '2022-08-29 00:00:00.000000 +05:30', '2022-08-29 00:00:00.000000 +05:30', null);


-- cc_offers table
INSERT INTO cc_offers (id, actor_id, vendor_offer_id, vendor, offer_constraints, valid_since, valid_till, deactivated_at, created_at, updated_at,offer_details,card_program) VALUES
	('cc-offer-id-1', 'actor-1', 'vendor-offer-id-1', 'FEDERAL', '{}', '2022-07-25 00:00:00.000000 +05:30', '2030-07-25 00:00:00.000000 +05:30', NULL, '2022-07-25 00:00:00.000000 +05:30', '2022-07-25 00:00:00.000000 +05:30','{"parent_offer_id":"123455"}','FEDERAL:PREAPPROVED:UNSECURED:');

-- cc_offer_eligibility_criteria table
INSERT INTO cc_offer_eligibility_criteria (id, actor_id, vendor, status, vendor_response, created_at, updated_at, provenance) VALUES
																																  ('cc-offer-eligibility-id-1', 'actor-1', 'FEDERAL', 'CC_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED', '{}', '2022-08-15 00:00:00.000000 +05:30', '2022-08-15 00:00:00.000000 +05:30', 'CREDIT_CARD_OFFER_ELIGIBILITY_PROVENANCE_REALTIME_BRE'),
																																  ('cc-offer-eligibility-id-2', 'actor-2', 'FEDERAL', 'CC_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED', '{}', '2022-08-15 00:00:00.000000 +05:30', '2022-08-15 00:00:00.000000 +05:30', 'CREDIT_CARD_OFFER_ELIGIBILITY_PROVENANCE_UNSPECIFIED');


INSERT INTO credit_card_bills
(id,actor_id,account_id,last_statement_balance,current_statement_amount,total_credit,total_debit,cash,purchase,min_due,total_due,statement_date,soft_due_date,hard_due_date,rewards_info,analytics_info,available_limit,statement_summary,s3_path,reward_id)
VALUES ('test-bill-1', 'test-actor-id', 'test-account-id', '{"currency_code": "INR", "units": 10}',
		'{"currency_code": "INR", "units": 10}', '{"currency_code": "INR", "units": 10}',
		'{"currency_code": "INR", "units": 10}', '{"currency_code": "INR", "units": 10}',
		'{"currency_code": "INR", "units": 10}', '{"currency_code": "INR", "units": 10}',
		'{"currency_code": "INR", "units": 10}', '2022-08-18 18:20:00 + 00:00', '2022-08-18 18:20:00 + 00:00', '2022-08-18 18:20:00 + 00:00', '{"rewardCoinsEarned": 1000, "topMerchantRewards": [{"amountSpent": {"currencyCode": "INR", "units": "10"}, "merchantId": "UBER", "merchantRewardType": "MERCHANT_REWARD_TYPE_TWO_X", "totalRewardCoins": 10}, {"amountSpent": {"currencyCode": "INR", "units": "10"}, "merchantId": "ZOMATO", "merchantRewardType": "MERCHANT_REWARD_TYPE_TWO_X", "totalRewardCoins": 10}], "totalRewardsCoins": 1000}', '{}','{"currency_code": "INR", "units": 10}','{"availableLimit": {"currencyCode": "INR", "units": "10"}, "fees": {"currencyCode": "INR", "units": "10"}, "interestCharges": {"currencyCode": "INR", "units": "10"}, "minAmountDue": {"currencyCode": "INR", "units": "10"}, "openingBalance": {"currencyCode": "INR", "units": "10"}, "paymentDueDate": {"day": 18, "month": 8, "year": 2022}, "repaymentAndRefunds": {"currencyCode": "INR", "units": "10"}, "spendConvertedToEmi": {"currencyCode": "INR", "units": "10"}, "spends": {"currencyCode": "INR", "units": "10"}, "statementDate": {"day": 18, "month": 8, "year": 2022}, "totalAmountDue": {"currencyCode": "INR", "units": "10"}}','test-s3-path','reward-id-1'),
	   ('test-bill-2', 'test-actor-id', 'test-account-id', '{"currency_code": "INR", "units": 10}',
		'{"currency_code": "INR", "units": 10}', '{"currency_code": "INR", "units": 10}',
		'{"currency_code": "INR", "units": 10}', '{"currency_code": "INR", "units": 10}',
		'{"currency_code": "INR", "units": 10}', '{"currency_code": "INR", "units": 10}',
		'{"currency_code": "INR", "units": 10}', '2022-08-18 18:20:00 + 00:00', '2022-08-18 18:20:00 + 00:00', '2022-08-18 18:20:00 + 00:00', '{"rewardCoinsEarned": 1000, "topMerchantRewards": [{"amountSpent": {"currencyCode": "INR", "units": "10"}, "merchantId": "UBER", "merchantRewardType": "MERCHANT_REWARD_TYPE_TWO_X", "totalRewardCoins": 10}, {"amountSpent": {"currencyCode": "INR", "units": "10"}, "merchantId": "ZOMATO", "merchantRewardType": "MERCHANT_REWARD_TYPE_TWO_X", "totalRewardCoins": 10}], "totalRewardsCoins": 1000}', '{}','{"currency_code": "INR", "units": 10}','{"availableLimit": {"currencyCode": "INR", "units": "10"}, "fees": {"currencyCode": "INR", "units": "10"}, "interestCharges": {"currencyCode": "INR", "units": "10"}, "minAmountDue": {"currencyCode": "INR", "units": "10"}, "openingBalance": {"currencyCode": "INR", "units": "10"}, "paymentDueDate": {"day": 18, "month": 8, "year": 2022}, "repaymentAndRefunds": {"currencyCode": "INR", "units": "10"}, "spendConvertedToEmi": {"currencyCode": "INR", "units": "10"}, "spends": {"currencyCode": "INR", "units": "10"}, "statementDate": {"day": 18, "month": 8, "year": 2022}, "totalAmountDue": {"currencyCode": "INR", "units": "10"}}','test-s3-path','reward-id-2'),
	   ('test-bill-3', 'test-actor-id-2', 'test-account-id-2', '{"currency_code": "INR", "units": 10}',
		'{"currency_code": "INR", "units": 10}', '{"currency_code": "INR", "units": 10}',
		'{"currency_code": "INR", "units": 10}', '{"currency_code": "INR", "units": 10}',
		'{"currency_code": "INR", "units": 10}', '{"currency_code": "INR", "units": 10}',
		'{"currency_code": "INR", "units": 10}', '2022-08-18 18:20:00 + 00:00', '2022-08-18 18:20:00 + 00:00', '2022-08-18 18:20:00 + 00:00', '{"rewardCoinsEarned": 1000, "topMerchantRewards": [{"amountSpent": {"currencyCode": "INR", "units": "10"}, "merchantId": "UBER", "merchantRewardType": "MERCHANT_REWARD_TYPE_TWO_X", "totalRewardCoins": 10}, {"amountSpent": {"currencyCode": "INR", "units": "10"}, "merchantId": "ZOMATO", "merchantRewardType": "MERCHANT_REWARD_TYPE_TWO_X", "totalRewardCoins": 10}], "totalRewardsCoins": 1000}', '{}','{"currency_code": "INR", "units": 10}','{"availableLimit": {"currencyCode": "INR", "units": "10"}, "fees": {"currencyCode": "INR", "units": "10"}, "interestCharges": {"currencyCode": "INR", "units": "10"}, "minAmountDue": {"currencyCode": "INR", "units": "10"}, "openingBalance": {"currencyCode": "INR", "units": "10"}, "paymentDueDate": {"day": 18, "month": 8, "year": 2022}, "repaymentAndRefunds": {"currencyCode": "INR", "units": "10"}, "spendConvertedToEmi": {"currencyCode": "INR", "units": "10"}, "spends": {"currencyCode": "INR", "units": "10"}, "statementDate": {"day": 18, "month": 8, "year": 2022}, "totalAmountDue": {"currencyCode": "INR", "units": "10"}}','test-s3-path','reward-id-3');


INSERT INTO credit_card_payment_info (id,bill_info_id,external_txn_id,vendor_txn_ref_no,order_id,payment_date,amount,payment_status,payment_sub_status,account_id)
VALUES ('test-bill-payment-info-1','test-bill-1','test-transaction-id-1','test-vendor-txn-ref-no-1','test-order-id-1','2022-10-17 23:14:13.531','{"currency_code": "INR", "units": 10}','COMPLETED','TRANSACTION_SUB_STATUS_UNSPECIFIED','account-id-1');

INSERT INTO credit_card_payment_info (id,bill_info_id,external_txn_id,vendor_txn_ref_no,order_id,payment_date,amount,payment_status,payment_sub_status,account_id)
VALUES ('test-bill-payment-info-2','test-bill-2','test-transaction-id-2','test-vendor-txn-ref-no-2','test-order-id-2','2022-10-17 23:14:13.531','{"currency_code": "INR", "units": 10}','COMPLETED','TRANSACTION_SUB_STATUS_UNSPECIFIED','account-id-2');

INSERT INTO credit_card_payment_info (id,bill_info_id,external_txn_id,vendor_txn_ref_no,order_id,payment_date,amount,payment_status,payment_sub_status,account_id)
VALUES ('test-bill-payment-info-3','test-bill-3','test-transaction-id-3','test-vendor-txn-ref-no-3','test-order-id-3','2022-10-17 23:14:13.531','{"currency_code": "INR", "units": 10}','COMPLETED','TRANSACTION_SUB_STATUS_UNSPECIFIED','account-id-3');

INSERT INTO card_requests
(id, actor_id, card_id, orch_id, vendor, request_details, workflow, status, provenance, created_at, updated_at, deleted_at)
VALUES
	('a5004f18-5d52-4991-82a9-xa1e5010e991','act_2',  'card_id_x123', 'orch_id_1', 'VENDOR_M2P', '{"address_type" : "PERMANENT"}', 'CARD_REQUEST_WORKFLOW_TYPE_UNSPECIFIED', 'CARD_REQUEST_STATUS_UNSPECIFIED', 'PROVENANCE_UNSPECIFIED', '2022-08-29 00:00:00.000000 +05:30','2022-08-29 00:00:00.000000 +05:30',null);

INSERT INTO card_request_stages
(id, card_request_id, orch_id, stage, stage_execution_details, status, sub_status, deleted_at, staled_at, created_at, updated_at, completed_at, external_request_id)
VALUES
	('a5004f18-5d52-4991-82a9-xa1e5010e993', 'a5004f18-5d52-4991-82a9-xa1e5010e991', 'orch_id_1', 'CARD_REQUEST_STAGE_NAME_UNSPECIFIED', '{}', 'CARD_REQUEST_STAGE_STATUS_UNSPECIFEID', 'CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFEID', null, '2022-08-29 00:00:00.000000 +05:30', '2022-08-29 00:00:00.000000 +05:30', '2022-08-29 00:00:00.000000 +05:30', '2022-08-29 00:00:00.000000 +05:30', 'vendor_request_id_1');

INSERT INTO credit_card_skus
(sku_type, type, feature_info, vendor_card_sku, created_at, updated_at, deleted_at)
VALUES
	('CARD_SKU_TYPE_UNSPECIFIED', 'CARD_TYPE_CREDIT', '{}', '{}', '2022-08-29 00:00:00.000000 +05:30', '2022-08-29 00:00:00.000000 +05:30', null);

INSERT INTO credit_card_sku_overrides
(actor_id, card_sku_type, feature_override_info, created_at, updated_at, deleted_at)
VALUES
	('act_2', 'CARD_SKU_TYPE_UNSPECIFIED', '{}', '2022-08-29 00:00:00.000000 +05:30', '2022-08-29 00:00:00.000000 +05:30', null),
	('act_2', 'CLASSIC', '{}', '2022-08-29 00:00:00.000000 +05:30', '2022-08-29 00:00:00.000000 +05:30', null);

INSERT INTO disputed_transactions
(id, transaction_id, actor_id, account_id, ext_dispute_ref, dispute_state, disputed_at)
VALUES
	('0b0b7df9-7103-4d40-9fa1-d5346f978ca8','txn-2', 'actor-2', 'acc-2', 'ext-2', 'DISPUTE_STATE_INITIATED', '2022-12-07 00:00:00.000000 +05:30');

INSERT INTO transaction_loan_offers
(id, actor_id, transaction_id, tenure_in_months, vendor_loan_request_id, amount_info, interest_info, processing_fee_info, summary)
VALUES
	('loan-offer-id-2', 'actor-2', 'transaction-id-2', 3, 'vendor-loan-request-id-2', '{}', '{}', '{}', '{}'),
	('loan-offer-id-3', 'actor-2', 'transaction-id-3', 6, 'vendor-loan-request-id-3', '{}', '{}', '{}', '{}');

INSERT INTO loan_account
(id, actor_id, transaction_loan_offers_id, vendor_loan_id, tenure_in_months, amount_info, interest_info, fee_info, repayment_info, summary, loan_schedule, status, disbursed_date, loan_end_date)
VALUES
	('loan-account-id-1', 'actor-id-1', 'loan-offer-id-1', 'vendor-loan-id-1', 3, '{"loanAmount":{"currencyCode":"INR","units":"5000"},"emiAmount":{"currencyCode":"INR","units":"5000"}}',
	 '{"interestRate":1.1,"totalInterest":{"currencyCode":"INR","units":"5000"},"interestOutstanding":{"currencyCode":"INR","units":"5000"},"interestOverdue":{"currencyCode":"INR","units":"5000"},"interestPaid":{"currencyCode":"INR","units":"5000"},"interestWaived":{"currencyCode":"INR","units":"5000"}}',
	 '{"totalFee":{"currencyCode":"INR","units":"5000"},"feeOutstanding":{"currencyCode":"INR","units":"5000"},"feeOverdue":{"currencyCode":"INR","units":"5000"},"totalFeePaid":{"currencyCode":"INR","units":"5000"},"feeWaived":{"currencyCode":"INR","units":"5000"}}',
	 '{"numberOfDueRepayments":1}',
	 '{}',
	 '{"schedules":[{"installmentNumber":1,"principal":{"currencyCode":"INR","units":"5000"},"interest":{"currencyCode":"INR","units":"5000"},"fee":{"currencyCode":"INR","units":"5000"},"closingPrincipalAmount":{"currencyCode":"INR","units":"5000"},"totalDue":{"currencyCode":"INR","units":"5000"},"dueDate":{"year":2022,"month":8,"day":15}}]}',
	 'LOAN_ACCOUNT_STATUS_ACTIVE',
	 '2022-08-15 00:00:00','2022-08-15 00:00:00'
	);
