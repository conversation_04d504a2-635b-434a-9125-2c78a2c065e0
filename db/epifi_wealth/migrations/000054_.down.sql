DROP TABLE IF EXISTS mf_folio_ledger;
CREATE TABLE IF NOT EXISTS mf_folio_ledger
(
	id           UUID        NOT NULL DEFAULT gen_random_uuid(),
	folio_id     STRING      NOT NULL,
	actor_id         STRING      NOT NULL,
	mutual_fund_id   STRING      NOT NULL,
	investment_goal  STRING      NOT NULL DEFAULT 'NO_GOAL',
	balance_units     FLOAT8      NOT NULL DEFAULT 0.0,
	redeemable_units FLOAT8      NOT NULL DEFAULT 0.0,
	created_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
	updated_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
	deleted_at          TIMESTAMPTZ     NULL,
	CONSTRAINT mutual_fund_exists FOREIGN KEY (mutual_fund_id) REFERENCES mutual_funds (id),
	PRIMARY KEY (id),
	INDEX fetch_folios_by_ASO (actor_id, mutual_fund_id, investment_goal),
	FAMILY "frequently_updated" (balance_units, redeemable_units, updated_at),
	FAMILY "seldom_updated" (folio_id, actor_id, mutual_fund_id, investment_goal, created_at, deleted_at)
);
COMMENT ON TABLE mf_folio_ledger IS 'table to maintain the folio ledger for mutual funds';
COMMENT ON COLUMN mf_folio_ledger.actor_id IS 'stores the fi actor id for whom this Folio is created';
COMMENT ON COLUMN mf_folio_ledger.folio_id IS 'folio number mapping to  "actor + ASO + goal"';
COMMENT ON COLUMN mf_folio_ledger.investment_goal IS 'the goal for which the mutual fund investments were made in this folio';
