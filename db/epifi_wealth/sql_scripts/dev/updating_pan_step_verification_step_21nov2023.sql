--updating PAN step verification & pan upload exhaust state to success after manual review

UPDATE onboarding_step_details
SET status = 'STEP_STATUS_COMPLETED' WHERE id in ('f8837932-e227-49a4-b599-78239fd65372',
												  '891030b7-e215-499b-9250-e9909bc042b3',
												  '878c6f84-4e75-4938-962a-be134bb7afa1',
												  '6ddbde80-9a6a-4620-a6d3-e7090a5a7291',
												  'aec1de7f-175e-40ad-9501-c56875ad44a4',
												  '817de205-0261-4359-a34e-f22704979f3e',
												  'dec9632c-e4f0-49b3-8b30-dbc74e291198');

update onboarding_details
set status ='ONBOARDING_STATUS_IN_PROGRESS', current_step='ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA'
where id in ('fa0075b0-e8a1-46f6-a465-50d9e9d76a6e',
			 '29c6f8ea-4e38-43db-8f88-93d9855d0464',
			 'c24a386f-e9b2-4f6c-ac6f-e9638a4a0038',
			 '4da3f4f0-769a-498d-8429-434ed073e360',
			 '926210d8-677f-4c43-bf59-f488bbc4471a',
			 'b534dba2-74d7-4cc7-a589-c6a6933b3fad',
			 '3de9e62f-9ae2-487c-bf55-0a29d8c55b37');

--Pan upload exhaust cases

update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/AC220121DYTsDeWaT1KskvoiUj3EYA==/a4b6bdc8-b12e-440d-9fed-e532b790fc00/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
	customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/AC220121DYTsDeWaT1KskvoiUj3EYA==/a4b6bdc8-b12e-440d-9fed-e532b790fc00/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where id = 'cd04954d-8714-49aa-b0dd-6b88df9e4c3e';


update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/AC211231NzY+TK0xRvaERIgIvoOUAQ==/00108ac8-7aac-4787-8535-2d37c6eab828/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
	customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/AC211231NzY+TK0xRvaERIgIvoOUAQ==/00108ac8-7aac-4787-8535-2d37c6eab828/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where id = '6b1a4c11-50e6-471d-85cc-8f83ae21db1b';


update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/AC211105tUIgoSo1QMyObeSicYlRYQ==/b5267dd6-e904-43af-974d-b9a31f9e6cc7/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
	customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/AC211105tUIgoSo1QMyObeSicYlRYQ==/b5267dd6-e904-43af-974d-b9a31f9e6cc7/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where id = '7f87cf38-562e-4f40-a108-b674876a234c';
