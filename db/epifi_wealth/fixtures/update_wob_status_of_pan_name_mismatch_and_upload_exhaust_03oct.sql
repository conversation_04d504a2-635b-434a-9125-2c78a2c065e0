--subStatus": "STEP_SUB_STATUS_PAN_UPLOAD_ATTEMPTS_EXHAUSTED",
update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/AC2201318owoaZlDRE2QppBFnVsJVg==/2e8c3971-c273-47b6-8bee-05f2514d5032/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
	customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/AC2201318owoaZlDRE2QppBFnVsJVg==/2e8c3971-c273-47b6-8bee-05f2514d5032/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where id = '55159362-9747-4904-a56b-6e29d0254c88';


update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/AC220531tFDK0wbHQg27HSQ+Zmm21g==/8e689682-16a4-49b5-ad1c-9a1ca05d0826/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
	customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/AC220531tFDK0wbHQg27HSQ+Zmm21g==/8e689682-16a4-49b5-ad1c-9a1ca05d0826/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where id = '56414d58-c015-4f08-b3c9-f7439e1d9d3b';


update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/AC220523UjIq3I87SnWoA5pZQ1iKcQ==/1ab11365-14b7-4aae-bc49-3025ea32f902/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
	customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/AC220523UjIq3I87SnWoA5pZQ1iKcQ==/1ab11365-14b7-4aae-bc49-3025ea32f902/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where id = 'b3ae0648-9812-417a-ad3d-7e721f4c3484';



update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/AC2208018L/GTiQKSe64kXn+EtRzZA==/f77823c0-afec-460a-be5f-be3f34b8bc7c/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
	customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/AC2208018L/GTiQKSe64kXn+EtRzZA==/f77823c0-afec-460a-be5f-be3f34b8bc7c/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where id = '3eb271b5-4004-4406-be16-3c477e9e49a6';


update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/ACtiSObGbJTMir0sN8H80R2A230626==/331fb4f9-b487-4c0e-9624-b75ca033c756/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
	customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/ACtiSObGbJTMir0sN8H80R2A230626==/331fb4f9-b487-4c0e-9624-b75ca033c756/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where id = '4fc2e8e3-f2f2-4eaf-a47c-c64dddc7e95e';


update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/AC220624BT03dfYRRCGDKZ4mC2fCxQ==/62eaa4e4-0ee3-4b4a-8e93-f5c7b50b960f/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
	customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/AC220624BT03dfYRRCGDKZ4mC2fCxQ==/62eaa4e4-0ee3-4b4a-8e93-f5c7b50b960f/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where id = 'b0e2ad07-c8a0-4b06-974e-d9f6c90be927';

update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/AC2202072w8fzTD1QS6LLDe8bKs9AQ==/52a769d5-adce-4ebf-bb6d-b79c900601bf/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
	customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/AC2202072w8fzTD1QS6LLDe8bKs9AQ==/52a769d5-adce-4ebf-bb6d-b79c900601bf/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where id = '2f06ffdb-719d-4c0a-a5c7-4c8e885465dc';



update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/AC221027Vh38+SUZSfqiBBKJe4KbQA==/33566602-0407-485e-ae82-0074c8ebba6b/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
	customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/AC221027Vh38+SUZSfqiBBKJe4KbQA==/33566602-0407-485e-ae82-0074c8ebba6b/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where id = 'ce6f5501-9fe1-4a96-8c18-5c2a4ff1ed79';

--below are PAN Name match failed

UPDATE onboarding_step_details
SET status = 'STEP_STATUS_COMPLETED' WHERE id in ('770ccb43-adfe-4802-9515-f9ad48286884',
												  '1d630d49-064a-477c-a630-d24f7d9cca2f');

update onboarding_details
set status ='ONBOARDING_STATUS_IN_PROGRESS', current_step='ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA'
where id in ('4224930d-bbc7-42d2-810c-e3caf846aebb',
			 '859cadb8-8704-499e-a601-f1b518df90db');
