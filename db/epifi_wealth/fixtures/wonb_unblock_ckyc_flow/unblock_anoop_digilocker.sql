-- mark the collect missing data step as stale
update onboarding_step_details
set staled_at=NOW()
where id IN (
    -- fetch the latest step row
    select id
    from onboarding_step_details
    where onboarding_details_id IN ('ade58f92-18c1-4360-b069-3e379f357446')
      AND step = 'ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO'
);

-- set current onboarding step to digi locker and status as in progress
update onboarding_details
set current_step='ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER', status='ONBOARDING_STATUS_IN_PROGRESS'
where id = 'ade58f92-18c1-4360-b069-3e379f357446';
