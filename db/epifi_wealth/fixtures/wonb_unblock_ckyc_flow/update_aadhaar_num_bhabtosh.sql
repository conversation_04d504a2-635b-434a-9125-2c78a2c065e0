-- mark create and sign step as stale
update onboarding_step_details
set staled_at=NOW()
where id = (
    -- fetch the latest step row
    select id
    from onboarding_step_details
    where onboarding_details_id = (
        select id
        from onboarding_details
        where actor_id = 'AC220214av4CmEjQTNOPwgRwS9RIWg=='
          and onboarding_type = 'ONBOARDING_TYPE_WEALTH'
    )
      AND step = 'ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET'
    order by created_at desc
    limit 1
);

-- empty kraDocketInfo
-- set current step as ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET
-- set status to in progress
-- update id in poa details
update onboarding_details
set kra_data = kra_data || jsonb_build_object('kraDocketInfo', null),
	current_step = 'ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET',
	status = 'ONBOARDING_STATUS_IN_PROGRESS',
    personal_details = personal_details || jsonb_build_object('poaDetails', personal_details->'poaDetails' || jsonb_build_object('id', 'XXXXXXXX6616'))
where id = (
    select id
    from onboarding_details
    where actor_id = 'AC220214av4CmEjQTNOPwgRwS9RIWg=='
      and onboarding_type = 'ONBOARDING_TYPE_WEALTH'
);
