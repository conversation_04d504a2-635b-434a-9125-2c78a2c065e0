CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE loan_offers (
									id VARCHAR NOT NULL,
									actor_id VARCHAR NOT NULL,
									vendor_offer_id VARCHAR NOT NULL,
									vendor VARCHAR NOT NULL,
									offer_constraints JSONB NOT NULL,
									processing_info JSONB NOT NULL,
									valid_since TIMESTAMPTZ NOT NULL,
									valid_till TIMESTAMPTZ NOT NULL,
									deactivated_at TIMESTAMPTZ NULL,
									created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
									updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
									deleted_at TIMESTAMPTZ NULL,
									loan_offer_eligibility_criteria_id VARCHAR NULL,
									loan_program VARCHAR NOT NULL DEFAULT 'LOAN_PROGRAM_PRE_APPROVED_LOAN'
);
COMMENT ON TABLE loan_offers IS 'table to store all the loan offers received from vendor for an actor';
COMMENT ON COLUMN loan_offers.actor_id IS 'actor for which loan offer is available';
COMMENT ON COLUMN loan_offers.vendor_offer_id IS 'id for the offer provided by vendor';
COMMENT ON COLUMN loan_offers.vendor IS '{"proto_type":"preapprovedloan.Vendor", "comment":"vendor who has offered loan"}';
COMMENT ON COLUMN loan_offers.offer_constraints IS '{"proto_type":"preapprovedloanPb.OfferConstraints", "comment":"loan offer constraints like max loan amount, max EMI amount, max loan tenure"}';
COMMENT ON COLUMN loan_offers.processing_info IS '{"proto_type":"preapprovedloanPb.ProcessingInfo", "comment":"loan offer processing info like interest rate, processing fee"}';
COMMENT ON COLUMN loan_offers.valid_since IS 'loan offer validity start time';
COMMENT ON COLUMN loan_offers.valid_till IS 'loan offer validity end time';
COMMENT ON COLUMN loan_offers.deactivated_at IS 'loan offer deactivation time';
COMMENT ON COLUMN loan_offers.created_at IS 'loan offer creation time';
COMMENT ON COLUMN loan_offers.updated_at IS 'loan offer latest update time';
COMMENT ON COLUMN loan_offers.loan_offer_eligibility_criteria_id IS 'loan_offer_eligibility_criteria_id maps a loan offer from vendor with the loan_offer_eligibility_criteria table, can be used to check in which cycle this loan offer got created.';
COMMENT ON COLUMN loan_offers.loan_program IS 'loan program is the type of loan applied by user, eg: pre-approved loan, early salary by default setting as pre-approved-loan';

CREATE TABLE loan_offer_eligibility_criteria (
														id VARCHAR NOT NULL,
														actor_id VARCHAR NOT NULL,
														vendor VARCHAR NOT NULL,
														status VARCHAR NOT NULL,
														vendor_response JSONB NULL,
														created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
														updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
														deleted_at TIMESTAMPTZ NULL,
														offer_id VARCHAR NULL,
														batch_id VARCHAR NULL,
														sub_status VARCHAR NULL,
														loan_scheme VARCHAR NULL,
														loan_program VARCHAR NULL
);
COMMENT ON TABLE loan_offer_eligibility_criteria IS 'table to store all the actors who are eligible for loan from BA side';
COMMENT ON COLUMN loan_offer_eligibility_criteria.actor_id IS 'actor who is eligible for loan from BA';
COMMENT ON COLUMN loan_offer_eligibility_criteria.vendor IS '{"proto_type":"preapprovedloan.Vendor", "comment":"vendor to whom loan offer is requested"}';
COMMENT ON COLUMN loan_offer_eligibility_criteria.status IS '{"proto_type":"preapprovedloan.LoanOfferEligibilityCriteriaStatus", "comment":"status of the eligibility"}';
COMMENT ON COLUMN loan_offer_eligibility_criteria.vendor_response IS '{"proto_type":"preapprovedloan.VendorResponse", "comment":"response timestamp from vendor"}';
COMMENT ON COLUMN loan_offer_eligibility_criteria.batch_id IS 'the parameter for identifying the approved base from Fi for the same batch';
COMMENT ON COLUMN loan_offer_eligibility_criteria.sub_status IS 'indicates loan offer rejection reason';
COMMENT ON COLUMN loan_offer_eligibility_criteria.loan_scheme IS 'indicates the loan scheme the offer is a part of';



CREATE TABLE loan_accounts (
									  id VARCHAR NOT NULL,
									  actor_id VARCHAR NOT NULL,
									  loan_account_id VARCHAR NULL,
									  loan_type VARCHAR NOT NULL,
									  ifsc_code VARCHAR NULL,
									  loan_amount JSONB NOT NULL DEFAULT '{}',
									  disbursed_amount JSONB NOT NULL DEFAULT '{}',
									  outstanding_amount JSONB NOT NULL DEFAULT '{}',
									  total_payable_amount JSONB NOT NULL DEFAULT '{}',
									  loan_end_date DATE NULL,
									  maturity_date DATE NULL,
									  vendor VARCHAR NOT NULL,
									  details JSONB NULL,
									  status VARCHAR NOT NULL,
									  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
									  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
									  deleted_at TIMESTAMPTZ NULL,
									  loan_program VARCHAR NULL
);
COMMENT ON TABLE loan_accounts IS 'Table to keep a track of all the loan account by a user';
COMMENT ON COLUMN loan_accounts.actor_id IS 'actor for which loan request is created';
COMMENT ON COLUMN loan_accounts.loan_account_id IS 'Loan account number returned by vendor';
COMMENT ON COLUMN loan_accounts.loan_type IS '{"proto_type":"preapprovedloan.LoanType", "comment":"Loan Type"}';
COMMENT ON COLUMN loan_accounts.ifsc_code IS 'IFSC code returned by vendor';
COMMENT ON COLUMN loan_accounts.loan_amount IS 'Loan Amount';
COMMENT ON COLUMN loan_accounts.disbursed_amount IS e'Total amount credited to user\U00002019s account';
COMMENT ON COLUMN loan_accounts.outstanding_amount IS 'Amount remaining to be paid against the loan taken. This will keep on reducing after every EMI/Lump sum payment';
COMMENT ON COLUMN loan_accounts.total_payable_amount IS 'Total amount to be paid against the loan taken. This will include interest amount, fees';
COMMENT ON COLUMN loan_accounts.loan_end_date IS 'Loan End date';
COMMENT ON COLUMN loan_accounts.maturity_date IS 'Maturity date';
COMMENT ON COLUMN loan_accounts.vendor IS '{"proto_type":"preapprovedloan.Vendor", "comment":"Vendor string who provided this loan offer"}';
COMMENT ON COLUMN loan_accounts.details IS '{"proto_type":"preapprovedloan.LoanAccountDetails", "comment":"Details provided by vendor for the loan"}';
COMMENT ON COLUMN loan_accounts.status IS '{"proto_type":"preapprovedloan.LoanAccountStatus", "comment":"active/closed/pre-closed"}';
CREATE TABLE loan_requests (
									  id VARCHAR NOT NULL,
									  actor_id VARCHAR NOT NULL,
									  offer_id VARCHAR NULL,
									  orch_id VARCHAR NULL,
									  loan_account_id VARCHAR NULL,
									  vendor_request_id VARCHAR NULL,
									  vendor VARCHAR NOT NULL,
									  details JSONB NULL,
									  type VARCHAR NULL,
									  status VARCHAR NOT NULL,
									  sub_status VARCHAR NOT NULL,
									  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
									  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
									  deleted_at TIMESTAMPTZ NULL,
									  completed_at TIMESTAMPTZ NULL,
									  next_action JSONB NULL,
									  loan_program VARCHAR NOT NULL DEFAULT 'LOAN_PROGRAM_PRE_APPROVED_LOAN',
									  client_request_id VARCHAR NULL UNIQUE,
									  redirect_deeplink JSONB NULL
);
COMMENT ON TABLE loan_requests IS 'Table to keep a track of all the loan applications that have been raised with the vendor and to pre-close a loan account with vendor';
COMMENT ON COLUMN loan_requests.actor_id IS 'actor for which loan request is created';
COMMENT ON COLUMN loan_requests.offer_id IS 'Unique id returned by vendor';
COMMENT ON COLUMN loan_requests.orch_id IS 'Client request ID used for loan request orchestration purpose';
COMMENT ON COLUMN loan_requests.loan_account_id IS 'Loan account associated with a loan request';
COMMENT ON COLUMN loan_requests.vendor_request_id IS 'ID to uniquely identify a loan request at vendor';
COMMENT ON COLUMN loan_requests.vendor IS '{"proto_type":"preapprovedloan.Vendor", "comment":"Vendor string who provided this loan offer"}';
COMMENT ON COLUMN loan_requests.details IS '{"proto_type":"preapprovedloan.LoanRequestDetails", "comment":"Details passed to vendor while creating the request and info got in response"}';
COMMENT ON COLUMN loan_requests.type IS '{"proto_type":"preapprovedloan.LoanRequestType", "comment":"Creation, Closure"}';
COMMENT ON COLUMN loan_requests.status IS '{"proto_type":"preapprovedloan.LoanRequestStatus", "comment":"status of the request"}';
COMMENT ON COLUMN loan_requests.sub_status IS '{"proto_type":"preapprovedloan.LoanRequestSubStatus", "comment":"Granular info on status"}';
COMMENT ON COLUMN loan_requests.next_action IS 'next action provides the deeplink name for the next screen';
COMMENT ON COLUMN loan_requests.loan_program IS 'loan program is the type of loan applied by user, eg: pre-approved loan, early salary';
CREATE TABLE loan_step_executions (
											 id VARCHAR NOT NULL,
											 actor_id VARCHAR NOT NULL,
											 ref_id VARCHAR NOT NULL,
											 orch_id VARCHAR NULL,
											 flow VARCHAR NOT NULL,
											 step_name VARCHAR NOT NULL,
											 details JSONB NULL,
											 status VARCHAR NOT NULL,
											 sub_status VARCHAR NOT NULL,
											 staled_at TIMESTAMPTZ NULL,
											 completed_at TIMESTAMPTZ NULL,
											 created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
											 updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
											 deleted_at TIMESTAMPTZ NULL,
											 group_stage VARCHAR NULL
);
COMMENT ON TABLE loan_step_executions IS 'Table to keep a track of all the steps in verification for loan';
COMMENT ON COLUMN loan_step_executions.ref_id IS 'Ref to the entity for which step is executing';
COMMENT ON COLUMN loan_step_executions.orch_id IS 'Orchestration identifier which has started this execution';
COMMENT ON COLUMN loan_step_executions.flow IS '{"proto_type":"preapprovedloan.LoanStepExecutionFlow", "comment":"Either loan application/loan closure/offers"}';
COMMENT ON COLUMN loan_step_executions.step_name IS '{"proto_type":"preapprovedloan.LoanStepExecutionStep", "comment":"Step specific details"}';
COMMENT ON COLUMN loan_step_executions.details IS '{"proto_type":"preapprovedloan.LoanStepExecutionDetails", "comment":"Details passed to vendor while creating the request and info got in response"}';
COMMENT ON COLUMN loan_step_executions.status IS '{"proto_type":"preapprovedloan.LoanStepExecutionStatus", "comment":"status of the request"}';
COMMENT ON COLUMN loan_step_executions.sub_status IS '{"proto_type":"preapprovedloan.LoanStepExecutionSubStatus", "comment":"Granular info on status"}';
COMMENT ON COLUMN loan_step_executions.staled_at IS 'will be used to make step stale so that re-execution of the step can be done';
COMMENT ON COLUMN loan_step_executions.completed_at IS 'will be used to mark step completed at and note the time with respect to it';
COMMENT ON COLUMN loan_step_executions.group_stage IS 'group stage to which the loan step belongs';
CREATE TABLE loan_activities (
										id VARCHAR NOT NULL,
										loan_account_id VARCHAR NULL,
										details JSONB NULL,
										type VARCHAR NULL,
										created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
										updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
										deleted_at TIMESTAMPTZ NULL,
										reference_id VARCHAR NULL
);
COMMENT ON TABLE loan_activities IS 'Table to keep a track of any activity related to a loan account such as an EMI Payment, Lump-sum payment, penalty fee payment, etc. and not just Fi initiated, but from external vendors too';
COMMENT ON COLUMN loan_activities.loan_account_id IS 'Loan account associated with the loan';
COMMENT ON COLUMN loan_activities.details IS '{"proto_type":"preapprovedloan.LoanActivitiesDetails", "comment":""}';
COMMENT ON COLUMN loan_activities.type IS '{"proto_type":"preapprovedloan.LoanActivitiesType", "comment":""}';
COMMENT ON COLUMN loan_activities.reference_id IS 'reference id of the loan activity, could be transaction id for financial activities';
CREATE TABLE loan_installment_info (
											  id VARCHAR NOT NULL,
											  account_id VARCHAR NOT NULL,
											  total_amount JSONB NOT NULL DEFAULT '{}',
											  start_date DATE NOT NULL,
											  end_date DATE NOT NULL,
											  total_installment_count INT NOT NULL,
											  next_installment_date DATE NULL,
											  details JSONB NULL,
											  status VARCHAR NOT NULL,
											  deactivated_at TIMESTAMPTZ NULL,
											  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
											  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
											  deleted_at TIMESTAMPTZ NULL
);
COMMENT ON TABLE loan_installment_info IS 'Represents the entity holding high level information related to the Installments associated with a loan account. It will also hold historical information if the installment details/schedule for a loan account is changed before the loan end date';
COMMENT ON COLUMN loan_installment_info.account_id IS 'In case of loan this will be loan account id';
COMMENT ON COLUMN loan_installment_info.total_amount IS 'Total amount to be collected through EMI';
COMMENT ON COLUMN loan_installment_info.total_installment_count IS 'Total installment count under this schedule/info';
COMMENT ON COLUMN loan_installment_info.details IS '{"proto_type":"preapprovedloan.LoanInstallmentInfoDetails", "comment":"Additional info in like payout mode, penalty rate, schedule etc"}';
COMMENT ON COLUMN loan_installment_info.status IS '{"proto_type":"preapprovedloan.LoanInstallmentInfoStatus", "comment":"active/completed/closed"}';
CREATE TABLE loan_installment_payout (
												id VARCHAR NOT NULL,
												loan_installment_info_id VARCHAR NOT NULL,
												amount JSONB NOT NULL DEFAULT '{}',
												due_date DATE NOT NULL,
												payout_date DATE NULL,
												details JSONB NULL DEFAULT '{}',
												status VARCHAR NOT NULL,
												created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
												updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
												deleted_at TIMESTAMPTZ NULL,
												vendor_installment_id VARCHAR NULL,
												principal_amount JSONB NULL,
												interest_amount JSONB NULL,
												due_amount JSONB NULL,
												loan_account_id VARCHAR NULL
);
COMMENT ON TABLE loan_installment_payout IS 'Represents an installment that has been paid to the loan account or was supposed to be paid, but failed';
COMMENT ON COLUMN loan_installment_payout.loan_installment_info_id IS 'Reference to loan_installment_info table';
COMMENT ON COLUMN loan_installment_payout.amount IS 'Total installment amount paid';
COMMENT ON COLUMN loan_installment_payout.due_date IS 'Date on which installment needs to be paid';
COMMENT ON COLUMN loan_installment_payout.payout_date IS 'Date on which installment was actually paid';
COMMENT ON COLUMN loan_installment_payout.details IS '{"proto_type":"preapprovedloan.LoanInstallmentPayoutDetails", "comment":"Additional info like actual EMI amount, penalty etc"}';
COMMENT ON COLUMN loan_installment_payout.status IS '{"proto_type":"preapprovedloan.LoanInstallmentPayoutStatus", "comment":"pending/processing/success/failed"}';
COMMENT ON COLUMN loan_installment_payout.vendor_installment_id IS 'installment id assigned by the vendor';
COMMENT ON COLUMN loan_installment_payout.principal_amount IS 'principal amount of a given installment';
COMMENT ON COLUMN loan_installment_payout.interest_amount IS 'interest amount of a given installment';
COMMENT ON COLUMN loan_installment_payout.due_amount IS 'due amount of an installment, due_amount will not change when user makes a payment. Any charges or fees applied will be on top of the due amount';
CREATE TABLE loan_payment_requests (
											  id VARCHAR NOT NULL,
											  actor_id VARCHAR NOT NULL,
											  account_id VARCHAR NOT NULL,
											  amount JSONB NOT NULL DEFAULT '{}',
											  type VARCHAR NOT NULL,
											  orch_id VARCHAR NOT NULL,
											  details JSONB NULL,
											  status VARCHAR NOT NULL,
											  sub_status VARCHAR NOT NULL,
											  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
											  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
											  deleted_at TIMESTAMPTZ NULL,
											  parent_id VARCHAR NULL
);
COMMENT ON TABLE loan_payment_requests IS 'Table used for orchestrating payments against a loan account, be it EMIs or Lump sum payments';
COMMENT ON COLUMN loan_payment_requests.account_id IS 'Loan account against which we are making a payment';
COMMENT ON COLUMN loan_payment_requests.amount IS 'Loan Payment Amount';
COMMENT ON COLUMN loan_payment_requests.type IS '{"proto_type":"preapprovedloan.LoanPaymentRequestType", "comment":"emi/lumpsum"}';
COMMENT ON COLUMN loan_payment_requests.orch_id IS 'Orchestration identifier which has started this execution';
COMMENT ON COLUMN loan_payment_requests.details IS '{"proto_type":"preapprovedloan.LoanPaymentRequestDetails", "comment":"Additional info in transactions. Will hold payment ref ids for both remitter and beneficiary"}';
COMMENT ON COLUMN loan_payment_requests.status IS '{"proto_type":"preapprovedloan.LoanPaymentRequestStatus", "comment":"status of the request"}';
COMMENT ON COLUMN loan_payment_requests.sub_status IS '{"proto_type":"preapprovedloan.LoanPaymentRequestSubStatus", "comment":"Granular info on status"}';
COMMENT ON COLUMN loan_payment_requests.parent_id IS 'stores the lpr ID pointing to batch payment request';
CREATE TABLE loan_applicants (
										id VARCHAR NOT NULL,
										actor_id VARCHAR NOT NULL,
										vendor VARCHAR NOT NULL,
										vendor_applicant_id VARCHAR NULL,
										vendor_request_id VARCHAR NULL,
										loan_program VARCHAR NULL,
										status VARCHAR NOT NULL,
										sub_status VARCHAR NOT NULL,
										created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
										updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
										deleted_at TIMESTAMPTZ NULL
);

-- Collection Leads Table
CREATE TABLE IF NOT EXISTS collection_leads
(
	id                                 VARCHAR      NOT NULL,
	actor_id                           VARCHAR      NOT NULL,
	account_id                         VARCHAR      NOT NULL,
	vendor                             VARCHAR      NOT NULL,
	vendor_id                          VARCHAR      NOT NULL,
	state 							   VARCHAR      NOT NULL,
	created_at                         TIMESTAMPTZ 	NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at                         TIMESTAMPTZ 	NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at                         TIMESTAMPTZ 	NULL
);

COMMENT ON TABLE collection_leads IS 'This table stores the leads that are created in our system for collection purposes of a credit product.';
COMMENT ON COLUMN collection_leads.id IS 'unique identifier of the lead in our system';
COMMENT ON COLUMN collection_leads.actor_id IS 'actor_id of the user whom the loan belongs to';
COMMENT ON COLUMN collection_leads.account_id IS 'unique identifier of the credit product that the lead is for';
COMMENT ON COLUMN collection_leads.vendor IS 'vendor we are using for collection purposes (e.g. Credgenics)';
COMMENT ON COLUMN collection_leads.vendor_id IS 'a unique identifier that we generate for each lead and use it at the vendor''s end (e.g. Credgenics)';
COMMENT ON COLUMN collection_leads.state IS 'tells whether the lead is active or not';

-- Collection Allocation Table

CREATE TABLE IF NOT EXISTS collection_allocations
(
	id                                 VARCHAR      NOT NULL,
	lead_id                            VARCHAR      NOT NULL,
	date_of_allocation                 DATE        	NOT NULL,
	default_date                       DATE        	NOT NULL,
	vendor_status                      VARCHAR      NOT NULL,
	recovery_status                    VARCHAR      NOT NULL,
	created_at                         TIMESTAMPTZ 	NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at                         TIMESTAMPTZ 	NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at                         TIMESTAMPTZ 	NULL
);

COMMENT ON TABLE collection_allocations IS 'This table stores the allocations that are created in our system for collection purposes of a credit product.';
COMMENT ON COLUMN collection_allocations.id IS 'unique identifier of the allocation of a lead in our system';
COMMENT ON COLUMN collection_allocations.lead_id IS 'the lead to which this allocation belongs to';
COMMENT ON COLUMN collection_allocations.date_of_allocation IS 'date on which the allocation is created at vendor';
COMMENT ON COLUMN collection_allocations.default_date IS 'date on which the allocation is defaulted';
COMMENT ON COLUMN collection_allocations.vendor_status IS 'represents whether the allocation is created at vendor or not';
COMMENT ON COLUMN collection_allocations.recovery_status IS 'represents whether the allocation is recovered or not';

-- Collection Communication Table

CREATE TABLE IF NOT EXISTS collection_communications
(
	id                                 UUID        	NOT NULL DEFAULT uuid_generate_v4(),
	lead_id                            VARCHAR      NOT NULL,
	type                               VARCHAR      NOT NULL,
	vendor_id                          VARCHAR      NOT NULL,
	payload                            JSONB       	NOT NULL,
	provenance                         VARCHAR      NOT NULL,
	created_at                         TIMESTAMPTZ 	NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at                         TIMESTAMPTZ 	NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at                         TIMESTAMPTZ 	NULL
);

COMMENT ON TABLE collection_communications IS 'This table stores the communications that are created in our system for collection purposes of a credit product.';
COMMENT ON COLUMN collection_communications.id IS 'unique identifier of the communication in our system';
COMMENT ON COLUMN collection_communications.lead_id IS 'references the lead that this communication is for';
COMMENT ON COLUMN collection_communications.type IS 'type of the communication (e.g. call, sms, email)';
COMMENT ON COLUMN collection_communications.vendor_id IS 'unique identifier of the communication at the vendor''s end';
COMMENT ON COLUMN collection_communications.payload IS 'payload specific to the communication type';
COMMENT ON COLUMN collection_communications.provenance IS 'source of the communication entry in our system';

CREATE TABLE fetched_assets
(
	id                    UUID         NOT NULL DEFAULT uuid_generate_v4(),
	actor_id              VARCHAR      NOT NULL,
	vendor                VARCHAR      NOT NULL,
	asset_type            VARCHAR      NOT NULL,
	vendor_asset_id       VARCHAR      NOT NULL,
	user_asset_identifier VARCHAR      NOT NULL,
	details               JSONB       NULL,
	fetched_at            TIMESTAMPTZ NULL,
	created_at            TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at            TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at            TIMESTAMPTZ NULL
);

COMMENT ON TABLE fetched_assets IS 'table to store the vendor asset ids for an actor that were fetched.';
