CREATE TABLE IF NOT EXISTS epf_passbook_est_details
(
	id                							VARCHAR                  	NOT NULL PRIMARY KEY,
	actor_id          							VARCHAR                  	NOT NULL,
	uan_id										VARCHAR                  	NOT NULL,
	uan_number									VARCHAR						NOT NULL,
	epf_passbook_employee_details_id			VARCHAR						NOT NULL,
	establishment_name							VA<PERSON>HAR						NULL,
	member_id									VARCHAR						NULL,
	office_code									VARCHAR					    NULL,
	date_of_joining_epf							DATE						NULL,
	date_of_enquiry_epf							DATE						NULL,
	date_of_enquiry_employee_pension_scheme		DATE						NULL,
	provident_fund_balance						JSONB						NULL,
	created_at        							TIMESTAMP WITH TIME ZONE 	NOT NULL DEFAULT now(),
	updated_at        							TIMESTAMP WITH TIME ZONE 	NOT NULL DEFAULT now(),
	deleted_at        							TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS epf_passbook_est_details_updated_at_idx ON epf_passbook_est_details USING BTREE (updated_at);

COMMENT ON COLUMN epf_passbook_est_details.establishment_name IS 'Name of the Employer Establishment';
COMMENT ON COLUMN epf_passbook_est_details.office_code IS 'Epf Office Code applicable to the Employer Establishment';
COMMENT ON COLUMN epf_passbook_est_details.date_of_joining_epf IS 'Date of Joining EPF';
COMMENT ON COLUMN epf_passbook_est_details.date_of_enquiry_epf IS 'Date of Enquiry of EPF Data';
COMMENT ON COLUMN epf_passbook_est_details.date_of_enquiry_employee_pension_scheme IS 'Date of Enquiry of Employee Pension Scheme Data';
COMMENT ON COLUMN epf_passbook_est_details.provident_fund_balance IS 'Provident fund balance details of employer (Net balance, PF ammount status)';
