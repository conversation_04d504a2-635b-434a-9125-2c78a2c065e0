TRUNCATE merchant_queries;

INSERT INTO public.merchant_queries (merchant, query_type, query) VALUES
('AMAZON', 'ORDER_AMOUNT', 'from:<EMAIL> subject:"Your amazon.in order"'),
('AMAZON', 'ORDER_REFUND', 'from:<EMAIL> subject:Your refund for'),
('SWIGGY', 'ORDER_AMOUNT', 'from:<EMAIL> subject:Your receipt for Swiggy ["order"|"summary"]'),
('SWIGGY', 'ORDER_REFUND', 'from:<EMAIL> subject:Your Swiggy order has been cancelled'),
('ZOMATO', 'ORDER_AMOUNT', '{from:<EMAIL> from:<EMAIL>} subject:Your Zomato Order from'),
('FLIPKART', 'ORDER_AMOUNT', 'from:<EMAIL> subject:Your order has been successfully placed'),
('FLIPKART', 'ORDER_REFUND', 'from:no-reply@????flipkart.com subject:The refund amount for your'),
('MYNTRA', 'ORDER_AMOUNT', 'from:<EMAIL> subject:Your Myntra Order Confirmation. Please share feedback'),
('MYNTRA', 'ORDER_REFUND', 'from:<EMAIL> subject:Your Myntra return request processed. Please share your feedback'),
('MYNTRA', 'ORDER_CANCEL', 'from:<EMAIL> subject:Your Myntra cancellation request completed'),
('BIGBASKET', 'ORDER_AMOUNT', 'from:<EMAIL> subject:Your bigbasket order confirmation'),
('BIGBASKET', 'ORDER_REFUND', 'from:<EMAIL> subject:Refund due to return of products for bigbasket order'),
('BIGBASKET', 'ORDER_CANCEL', 'from:<EMAIL> subject:Cancellation of your Order') ON CONFLICT DO NOTHING;
