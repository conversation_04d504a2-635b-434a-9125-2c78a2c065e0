CREATE TABLE IF NOT EXISTS questions
(
    id VARCHAR DEFAULT public.uuid_generate_v4() NOT NULL,
    code VARCHAR NOT NULL,
    text VARCHAR NOT NULL,
    description VARCHAR NULL,
    tip VARCHAR NULL,
    placeholder VARCHAR NULL,
    type VARCHAR NOT NULL,
    options JSONB NULL,
    version INT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS questions_updated_at_idx ON questions (updated_at);
CREATE UNIQUE INDEX IF NOT EXISTS questions_code_version_unique_idx ON questions (code, version);
COMMENT ON TABLE  questions is 'Table to store questions that can be sent to users';
COMMENT ON COLUMN questions.code is 'Limited length code assigned to a question that does not change on version upgrade';
COMMENT ON COLUMN questions.text is 'Question text to be shown to the user';
COMMENT ON COLUMN questions.tip is 'Help text to be shown to the user with the question.';
COMMENT ON COLUMN questions.placeholder is 'Placeholder text to shown in response box';
COMMENT ON COLUMN questions.description is 'Question description for internal use';
COMMENT ON COLUMN questions.type is '"proto_type":"risk.case_management.form.QuestionType", "comment":"Type of question, can be on of multiple choice, file upload, text etc."';
COMMENT ON COLUMN questions.options is 'Specific additional params for question. e.g., all choices for multiple choice type';
COMMENT ON COLUMN questions.version is 'Stores question version that increases on updates';
COMMENT ON INDEX questions_code_version_unique_idx is 'Uniquely identifies a question with code and version.';

CREATE TABLE IF NOT EXISTS question_entity_mappings (
    id VARCHAR DEFAULT public.uuid_generate_v4() NOT NULL,
    entity_type VARCHAR NOT NULL,
    entity_id VARCHAR NOT NULL,
    question_code VARCHAR NOT NULL,
    entity_order_score INTEGER NOT NULL,
    is_mandatory BOOLEAN NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS question_entity_mappings_updated_at_idx ON question_entity_mappings (updated_at);
CREATE UNIQUE INDEX IF NOT EXISTS question_entity_mappings_entity_type_entity_id_code_unique_idx ON
    question_entity_mappings (entity_type, entity_id, question_code);
COMMENT ON COLUMN question_entity_mappings.entity_type is '"proto_type":"risk.case_management.form.EntityType", "comment":"Type of entity against which question is mapped"';
COMMENT ON COLUMN question_entity_mappings.question_code is 'Question code which is mapped against entity';
COMMENT ON COLUMN question_entity_mappings.entity_order_score is 'Assigned score for logical ordering of questions at entity level';
COMMENT ON COLUMN question_entity_mappings.is_mandatory is 'Indicates whether a question is mandatory wrt an entity';

CREATE TABLE  IF NOT EXISTS forms (
    id VARCHAR DEFAULT public.uuid_generate_v4() NOT NULL,
    case_id VARCHAR NULL,
    actor_id VARCHAR NOT NULL,
    status VARCHAR NOT NULL,
    origin VARCHAR NOT NULL,
    added_by VARCHAR NOT NULL,
    client_req_id VARCHAR NOT NULL,
    workflow_req_id VARCHAR NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ NULL,
    expire_at TIMESTAMPTZ NULL,
    PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS forms_updated_at_idx ON forms (updated_at);
CREATE INDEX IF NOT EXISTS forms_actor_id_idx ON forms (actor_id);
CREATE INDEX IF NOT EXISTS forms_client_req_id_idx on forms (client_req_id);
COMMENT ON TABLE  forms is 'Table to store forms sent to users';
COMMENT ON COLUMN forms.status is '"proto_type":"risk.case_management.form.Status", "comment":"Current status of form. e.g, created, sent, submitted etc."';
COMMENT ON COLUMN forms.origin is '"proto_type":"risk.case_management.form.FormOrigin", "comment":"Inception point from where form generation is triggered."';
COMMENT ON COLUMN forms.client_req_id is 'Stores request id populated by client.';
COMMENT ON COLUMN forms.workflow_req_id is 'Orchestration workflow request id';
COMMENT ON COLUMN forms.expire_at is 'Timestamp at which form expired/will expire.';

CREATE TABLE  IF NOT EXISTS form_question_mappings (
    id VARCHAR DEFAULT public.uuid_generate_v4() NOT NULL,
    form_id VARCHAR NOT NULL,
    question_code VARCHAR NOT NULL,
    is_mandatory BOOLEAN NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS form_question_mappings_updated_at_idx ON form_question_mappings (updated_at);
CREATE UNIQUE INDEX IF NOT EXISTS form_question_mappings_form_id_question_code_unique_idx ON form_question_mappings (form_id, question_code);
COMMENT ON TABLE  form_question_mappings is 'Table to store questions linked with form';

CREATE TABLE  IF NOT EXISTS question_responses (
    id VARCHAR DEFAULT public.uuid_generate_v4() NOT NULL,
    form_id VARCHAR NOT NULL,
    question_id VARCHAR NOT NULL,
    response JSONB NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ NULL,
    PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS question_responses_updated_at_idx ON question_responses (updated_at);
CREATE UNIQUE INDEX IF NOT EXISTS question_responses_form_id_question_id_unique_idx ON question_responses (form_id, question_id);
COMMENT ON TABLE  question_responses is 'Table to store user responses on questions sent with form';
COMMENT ON COLUMN question_responses.response is 'User response for question, empty if no response for optional question';
