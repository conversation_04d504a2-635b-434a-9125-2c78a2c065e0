-- Customer for Epifi business account
UPSERT
INTO customers (id, request_id, phone_no)
VALUES ('epifi-business-customer-id', 'NEOEPIFIAccount', '************');

-- Epifi business account
UPSERT
INTO accounts (id, customer_id, account_id, request_id, partner_bank, balance)
VALUES ('**************', 'epifi-business-customer-id', '**************', 'NEOEPIFIAccount', 'FEDERAL', ***********);

-- Customer for Epifi business account
UPSERT INTO customers (id, request_id, phone_no)
VALUES ('epifi-business-customer-id-2', 'NEOEPIFIAccount2', '************');

-- Epifi business account
UPSERT INTO accounts (id, customer_id, account_id, request_id, partner_bank, balance)
VALUES ('**************', 'epifi-business-customer-id-2', '**************', 'NEOEPIFIAccount2', 'FEDERAL', ***********);

-- epifi ppol account
INSERT INTO accounts (id, request_id, partner_bank, balance, account_id, account_reference)
VALUES ('account-add_funds_pool', '********', 'FEDERAL', ***********, '**************',
		'account_refId-add_funds_pool') ON CONFLICT DO NOTHING;

INSERT INTO aa_otp_references(vua, otp_reference)
VALUES ('test@vua', '0fbb52c7-cba1-4edf-8f10-157a36cf6262') ON CONFLICT DO NOTHING;
