CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.aa_account_pis (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    account_id character varying NOT NULL,
    account_type character varying NOT NULL,
    pi_id character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.account_pis (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    account_id character varying NOT NULL,
    account_type character varying NOT NULL,
    pi_id character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    apo character varying DEFAULT 'ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED'::character varying NOT NULL
);
COMMENT ON COLUMN public.account_pis.apo IS 'denotes the account product offering associated with the AccountType. Refer to types.account.AccountProductOffering enum.';
CREATE TABLE public.payment_instrument_purge_audits (
    pi_id character varying NOT NULL,
    payload jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.payment_instruments (
    id character varying NOT NULL,
    type character varying NOT NULL,
    verified_name character varying,
    identifier jsonb,
    state character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    capabilities jsonb DEFAULT '{}'::jsonb NOT NULL,
    spam_count bigint DEFAULT 0 NOT NULL,
    computed_unique_card character varying GENERATED ALWAYS AS (
CASE
    WHEN ((((identifier -> 'card'::text) ->> 'secure_card_number'::text) IS NOT NULL) AND (((identifier -> 'card'::text) ->> 'expiry'::text) IS NOT NULL) AND (((identifier -> 'card'::text) ->> 'name'::text) IS NOT NULL)) THEN ((((((identifier -> 'card'::text) ->> 'secure_card_number'::text) || '_'::text) || ((identifier -> 'card'::text) ->> 'expiry'::text)) || '_'::text) || ((identifier -> 'card'::text) ->> 'name'::text))
    ELSE NULL::text
END) STORED,
    issuer_classification character varying NOT NULL,
    computed_unique_account character varying GENERATED ALWAYS AS (
CASE
    WHEN (((type)::text = 'BANK_ACCOUNT'::text) AND (((identifier -> 'account'::text) ->> 'actual_account_number'::text) IS NOT NULL) AND (((identifier -> 'account'::text) ->> 'ifsc_code'::text) IS NOT NULL)) THEN ((((identifier -> 'account'::text) ->> 'actual_account_number'::text) || '_'::text) || ((identifier -> 'account'::text) ->> 'ifsc_code'::text))
    WHEN ((((type)::text = 'PARTIAL_BANK_ACCOUNT'::text) OR ((type)::text = 'GENERIC'::text)) AND (((identifier -> 'account'::text) ->> 'actual_account_number'::text) IS NOT NULL) AND (((identifier -> 'account'::text) ->> 'name'::text) IS NOT NULL) AND (((identifier -> 'account'::text) ->> 'ifsc_code'::text) IS NOT NULL)) THEN ((((replace(((identifier -> 'account'::text) ->> 'name'::text), ' '::text, ''::text) || '_'::text) || ((identifier -> 'account'::text) ->> 'actual_account_number'::text)) || '_'::text) || ((identifier -> 'account'::text) ->> 'ifsc_code'::text))
    ELSE NULL::text
END) STORED,
    computed_unique_upi character varying GENERATED ALWAYS AS (
CASE
    WHEN (((type)::text = 'UPI'::text) AND (((identifier -> 'upi'::text) ->> 'vpa'::text) IS NOT NULL)) THEN ((identifier -> 'upi'::text) ->> 'vpa'::text)
    WHEN (((type)::text = 'PARTIAL_UPI'::text) AND (((identifier -> 'upi'::text) ->> 'vpa'::text) IS NOT NULL) AND (((identifier -> 'upi'::text) ->> 'name'::text) IS NOT NULL)) THEN ((replace(((identifier -> 'upi'::text) ->> 'name'::text), ' '::text, ''::text) || '_'::text) || ((identifier -> 'upi'::text) ->> 'vpa'::text))
    ELSE NULL::text
END) STORED,
    ownership character varying DEFAULT 'EPIFI_TECH'::character varying,
    last_verified_at timestamp with time zone,
    derived_upi_id character varying,
    computed_unique_credit_card_id character varying GENERATED ALWAYS AS (
CASE
    WHEN (((type)::text = 'CREDIT_CARD'::text) AND (((identifier -> 'credit_card'::text) ->> 'id'::text) IS NOT NULL)) THEN (((type)::text || '_'::text) || ((identifier -> 'credit_card'::text) ->> 'id'::text))
    ELSE NULL::text
END) STORED,
    computed_unique_lrn character varying GENERATED ALWAYS AS (
CASE
    WHEN (((type)::text = 'UPI_LITE'::text) AND (((identifier -> 'upi_lite'::text) ->> 'lrn'::text) IS NOT NULL)) THEN ((identifier -> 'upi_lite'::text) ->> 'lrn'::text)
    ELSE NULL::text
END) STORED,
    computed_unique_international_account character varying GENERATED ALWAYS AS (
CASE
    WHEN ((((identifier -> 'international_account'::text) ->> 'actual_account_number'::text) IS NOT NULL) AND (((identifier -> 'international_account'::text) ->> 'swift_code'::text) IS NOT NULL) AND (((identifier -> 'international_account'::text) ->> 'funds_forwarding_account_identifier'::text) IS NOT NULL)) THEN ((((((identifier -> 'international_account'::text) ->> 'actual_account_number'::text) || '_'::text) || ((identifier -> 'international_account'::text) ->> 'swift_code'::text)) || '_'::text) || ((identifier -> 'international_account'::text) ->> 'funds_forwarding_account_identifier'::text))
    WHEN ((((identifier -> 'international_account'::text) ->> 'actual_account_number'::text) IS NOT NULL) AND (((identifier -> 'international_account'::text) ->> 'swift_code'::text) IS NOT NULL)) THEN ((((identifier -> 'international_account'::text) ->> 'actual_account_number'::text) || '_'::text) || ((identifier -> 'international_account'::text) ->> 'swift_code'::text))
    ELSE NULL::text
END) STORED,
    computed_upi_identifier character varying GENERATED ALWAYS AS (
CASE
    WHEN (((type)::text = 'UPI'::text) AND (((identifier -> 'upi'::text) ->> 'vpa'::text) IS NOT NULL)) THEN ((((COALESCE((((identifier -> 'upi'::text) -> 'merchant_details'::text) ->> 'legal_name'::text), ''::text) || '_'::text) || (COALESCE(verified_name, ''::character varying))::text) || '_'::text) || ((identifier -> 'upi'::text) ->> 'vpa'::text))
    ELSE NULL::text
END) STORED
);
COMMENT ON COLUMN public.payment_instruments.ownership IS '{"proto_type":"types.ownership", "comment": "Enum to store pi ownership.", "ref": "api.types.ownership.proto"}';
COMMENT ON COLUMN public.payment_instruments.last_verified_at IS 'timestamp at which the payment instrument was last verified at';
COMMENT ON COLUMN public.payment_instruments.derived_upi_id IS 'Stores hash of concatenation of verifiedName, legalName and vpa';
CREATE TABLE public.pi_last_transactions (
    pi_id character varying NOT NULL,
    action_type character varying NOT NULL,
    last_transaction_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.pi_last_transactions IS 'Stores the last transaction time by the pi_id for the specified action type';
COMMENT ON COLUMN public.pi_last_transactions.last_transaction_at IS 'The last transaction time for the pi and action type';
CREATE TABLE public.pi_state_logs (
    id_v2 uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    pi_id character varying NOT NULL,
    source character varying NOT NULL,
    state character varying NOT NULL,
    reason character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
ALTER TABLE ONLY public.aa_account_pis
    ADD CONSTRAINT aa_account_pis_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.account_pis
    ADD CONSTRAINT account_pis_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.payment_instrument_purge_audits
    ADD CONSTRAINT payment_instrument_purge_audits_pkey PRIMARY KEY (pi_id);
ALTER TABLE ONLY public.payment_instruments
    ADD CONSTRAINT payment_instruments_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.pi_last_transactions
    ADD CONSTRAINT pi_last_transactions_pi_id_action_type_pkey PRIMARY KEY (pi_id, action_type);
ALTER TABLE ONLY public.pi_state_logs
    ADD CONSTRAINT pi_state_logs_pkey PRIMARY KEY (pi_id, id_v2);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
CREATE UNIQUE INDEX aa_account_pis_account_id_account_type_actor_id_pi_id_key ON public.aa_account_pis USING btree (account_id, account_type, actor_id, pi_id);
CREATE INDEX aa_account_pis_actor_id_key ON public.aa_account_pis USING btree (actor_id);
CREATE UNIQUE INDEX aa_account_pis_pi_id_key ON public.aa_account_pis USING btree (pi_id);
CREATE INDEX aa_account_pis_updated_at_idx ON public.aa_account_pis USING btree (updated_at DESC);
CREATE UNIQUE INDEX account_pis_account_id_account_type_actor_id_pi_id_key ON public.account_pis USING btree (account_id, account_type, actor_id, pi_id);
CREATE INDEX account_pis_auto_index_fk_actor_id_ref_actors ON public.account_pis USING btree (actor_id);
CREATE UNIQUE INDEX account_pis_pi_id_key ON public.account_pis USING btree (pi_id);
CREATE INDEX account_pis_updated_at_idx ON public.account_pis USING btree (updated_at DESC);
CREATE INDEX payment_instrument_purge_audits_updated_at_idx ON public.payment_instrument_purge_audits USING btree (updated_at DESC);
CREATE UNIQUE INDEX payment_instruments_computed_unique_account_key ON public.payment_instruments USING btree (computed_unique_account);
CREATE UNIQUE INDEX payment_instruments_computed_unique_card_key ON public.payment_instruments USING btree (computed_unique_card);
CREATE UNIQUE INDEX payment_instruments_computed_unique_credit_card_id_key ON public.payment_instruments USING btree (computed_unique_credit_card_id);
CREATE UNIQUE INDEX payment_instruments_computed_unique_international_account_key ON public.payment_instruments USING btree (computed_unique_international_account);
CREATE UNIQUE INDEX payment_instruments_computed_unique_lrn_idx ON public.payment_instruments USING btree (computed_unique_lrn);
CREATE UNIQUE INDEX payment_instruments_computed_unique_upi_key ON public.payment_instruments USING btree (computed_unique_upi);
CREATE UNIQUE INDEX payment_instruments_computed_upi_identifier_key ON public.payment_instruments USING btree (computed_upi_identifier);
CREATE UNIQUE INDEX payment_instruments_derived_upi_id_key ON public.payment_instruments USING btree (derived_upi_id);
CREATE INDEX payment_instruments_updated_at_idx ON public.payment_instruments USING btree (updated_at DESC);
CREATE INDEX pi_last_transactions_last_trans_at_idx ON public.pi_last_transactions USING btree (last_transaction_at);
CREATE UNIQUE INDEX pi_state_log_id_v2 ON public.pi_state_logs USING btree (id_v2);
CREATE INDEX pi_state_logs_updated_at_idx ON public.pi_state_logs USING btree (updated_at DESC);
ALTER TABLE ONLY public.account_pis
    ADD CONSTRAINT account_pis_pi_id_ref_payment_instruments_fkey FOREIGN KEY (pi_id) REFERENCES public.payment_instruments(id);
ALTER TABLE ONLY public.payment_instrument_purge_audits
    ADD CONSTRAINT payment_instrument_purge_audits_pi_id_fkey FOREIGN KEY (pi_id) REFERENCES public.payment_instruments(id);
ALTER TABLE ONLY public.pi_state_logs
    ADD CONSTRAINT pi_state_logs_pi_state_log_pi_id_fkey FOREIGN KEY (pi_id) REFERENCES public.payment_instruments(id);
