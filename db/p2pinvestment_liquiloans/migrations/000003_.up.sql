CREATE TABLE IF NOT EXISTS p2p_maturity_action (
	   id							string			NOT NULL,
	   investment_transaction_id 	string 			NOT NULL,
	   actor_id						string			NOT NULL,
	   link_token 					string 			NOT NULL,
	   scheme_name 					string 			NOT NULL,
	   status              			STRING     		NOT NULL,
	   sub_status          			STRING      	NOT NULL,
	   type                         STRING          NOT NULL,
	   created_at          			TIMESTAMPTZ 	NOT NULL DEFAULT now():::TIMESTAMPTZ,
	   updated_at          			TIMESTAMPTZ 	NOT NULL DEFAULT now():::TIMESTAMPTZ,
	   deleted_at          			TIMESTAMPTZ 	NULL,
	   CONSTRAINT p2p_maturity_action_pkey PRIMARY KEY (id ASC),
	   INDEX index_on_updated_at (updated_at ASC),
	   INDEX index_on_investment_transaction_id (investment_transaction_id ASC),
	   INDEX index_on_actor_id (actor_id ASC)
);

COMMENT ON TABLE p2p_maturity_action IS 'table to store all the details related to any action of investor on an investment which is near maturity';
COMMENT ON COLUMN p2p_maturity_action.investment_transaction_id IS 'foreign key from p2p_investment_transactions, inveestment transaction which is investor wants to renew investment';
COMMENT ON COLUMN p2p_maturity_action.actor_id IS 'actor for which maturity action is created';
COMMENT ON COLUMN p2p_maturity_action.scheme_name IS 'foreign key from p2p_investment_schemes, scheme in which investor wants to renew investment';
COMMENT ON COLUMN p2p_maturity_action.type IS '{"proto_type":"p2pinvestment.MaturityActionType", "comment":"type of action principalReinvestment/PrincipalAndInterestReinvestment/principalAndInterestRedemption"}';
COMMENT ON COLUMN p2p_maturity_action.status IS '{"proto_type":"p2pinvestment.MaturityActionStatus", "comment":"status of maturity action"}';
COMMENT ON COLUMN p2p_maturity_action.sub_status IS '{"proto_type":"p2pinvestment.MaturityActionSubStatus", "comment":"granular info on status"}';

ALTER TABLE p2p_maturity_action ADD CONSTRAINT fk_investment_transaction_id_ref_investment_transactions FOREIGN KEY (investment_transaction_id) REFERENCES p2p_investment_transactions(id);
ALTER TABLE p2p_maturity_action ADD CONSTRAINT fk_scheme_name_ref_investment_schemes FOREIGN KEY (scheme_name) REFERENCES p2p_investment_schemes(name);
