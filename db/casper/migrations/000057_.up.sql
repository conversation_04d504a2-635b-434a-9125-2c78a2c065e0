create table if not exists dreamfolks_redemptions
(
	id uuid default uuid_generate_v4() not null constraint dreamfolks_redemption_pk primary key,
	-- internal ref id.
	ref_id varchar not null,
	-- vendor ref id for linking redemption
	vendor_ref_id varchar not null,
	-- actor who performed the redemption
	actor_id varchar not null,
	-- type of offer redeemed.
	offer_type varchar not null,
	-- metadata required for redemption
	dreamfolks_redemption_metadata jsonb,
	request_source varchar default 'CATALOG_OFFER_REDEMPTION' not null,
	created_at timestamp with time zone default now() not null,
	updated_at timestamp with time zone default now() not null,
	deleted_at timestamp with time zone default null,
	-- each redemption should have a unique internal ref id from a request source
	constraint dreamfolks_redemption_ref_id_request_source_unique unique (ref_id, request_source),
	-- vendor ref id should be unique for each redemption.
	constraint dreamfolks_redemption_unique_vendor_ref_id unique (vendor_ref_id)
);
-- data team need this index for periodic snapshot query.
CREATE INDEX ca_df_updated_at_index ON dreamfolks_redemptions USING btree (updated_at);

comment on table dreamfolks_redemptions is 'stores the offer redemptions that were initiated to dreamfolks vendor';
comment on column dreamfolks_redemptions.ref_id is 'denotes a unique internal refId for offer redemption';
comment on column dreamfolks_redemptions.vendor_ref_id is 'denotes a unique refId that is shared with the vendor and is used for linking our offer redemption entity to vendor redemption';
comment on column dreamfolks_redemptions.actor_id is 'actor who performed the redemption';
comment on column dreamfolks_redemptions.offer_type is 'denotes the type of offer redeemed';
comment on column dreamfolks_redemptions.dreamfolks_redemption_metadata is 'metadata required to perform the redemption';
comment on column dreamfolks_redemptions.request_source IS 'stores the source from which redemption request originated';
