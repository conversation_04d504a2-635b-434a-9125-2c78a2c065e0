-- table to track fi coins accounting details done by external vendor by redemptions.
create table if not exists external_vendor_redemptions (
    id uuid default uuid_generate_v4() not null constraint external_vendor_redemptions_pk primary key,
    actor_id varchar not null,
    vendor varchar not null,
    txn_type varchar not null,
    txn_category varchar not null,
    fi_coin_units integer not null,
    vendor_ref_id varchar not null,
    processing_ref_id varchar,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    deleted_at timestamp with time zone default null,
    constraint external_vendor_redemptions_vendor_ref_id_txn_type_unique unique (vendor, vendor_ref_id, txn_type)
);

comment on table external_vendor_redemptions is 'stores the fi coins txn details done by external vendor for redemptions';
create index if not exists external_vendor_redemptions_updated_at_idx on external_vendor_redemptions using btree (updated_at);
comment on column external_vendor_redemptions.vendor is 'denotes the name of the external vendor';
comment on column external_vendor_redemptions.txn_type is 'denotes what type of txn is, credit or debit';
comment on column external_vendor_redemptions.txn_category is 'denotes the category of txn';
comment on column external_vendor_redemptions.vendor_ref_id is 'denotes the vendor reference if like order id made by vendor';
comment on column external_vendor_redemptions.processing_ref_id is 'denotes transaction id';
