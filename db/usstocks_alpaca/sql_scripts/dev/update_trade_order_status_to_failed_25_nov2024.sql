-- US Stocks orders in manual intervention due to failure at vendor
-- users account was not tradable due to some issue at vendor, now the same is fixed
-- email sub: Trade orders failing with account is not allowed to trade on Nov 12, 2024



UPDATE orders
set state = 'ORDER_INITIATION_FAILED', updated_at = now()
where id in ('USSO2QaDcxoa1G241117',
			 'USSOEdfgpWbkua241117',
			 'USSOVCXhWzRCpc241117',
			 'USSO2V6gaEWkMx241108',
			 'USSOkpV6RTZaev241108',
			 'USSOZKSgE4Xtmv241102',
			 'USSOrrzLMXGk8Y241102',
			 'USSO3ZwgrXyo6j241010')
  and state = 'ORDER_MANUAL_INTERVENTION';


UPDATE account_activities
set order_state = 'ORDER_INITIATION_FAILED', updated_at = now()
where order_id in ('USSO2QaDcxoa1G241117',
				   'USSOEdfgpWbkua241117',
				   'USSOVCXhWzRCpc241117',
				   'USSO2V6gaEWkMx241108',
				   'USSOkpV6RTZaev241108',
				   'USSOZKSgE4Xtmv241102',
				   'USSOrrzLMXGk8Y241102',
				   'USSO3ZwgrXyo6j241010')
  and order_state = 'ORDER_MANUAL_INTERVENTION';
