-- updating old wallet orders stuck in manual intervention


UPDATE wallet_orders
set status = 'WALLET_ORDER_STATUS_INITIATION_FAILED',
    updated_at = now()
where id in ('USSWO4DQGQkBQuY240105',
			'USSWO29WsLG4FPo231229',
			'USSWOdGbgawBqiw240621'
	) and status = 'WALLET_ORDER_STATUS_MANUAL_INTERVENTION';



UPDATE account_activities
set order_state = 'ORDER_INITIATION_FAILED',
    updated_at = now()
where order_id in ('USSWO4DQGQkBQuY240105',
				  'USSWO29WsLG4FPo231229',
				  'USSWOdGbgawBqiw240621'
	) and order_state = 'ORDER_MANUAL_INTERVENTION';
