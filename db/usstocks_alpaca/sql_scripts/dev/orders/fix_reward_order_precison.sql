-- updating precision of wallet order and market order
update wallet_orders SET amount_requested = '{"nanos": 180000000, "units": 1, "currency_code": "USD"}'
WHERE id='USSWO3XP9ZwLWRT240503';

update orders SET amount_requested = '{"nanos": 180000000, "units": 1, "currency_code": "USD"}'
WHERE id='USSOt1xz5KUDwV240503';

UPDATE wallet_orders
SET invoice_details = jsonb_set(invoice_details, '{amountInUSD}', '{"nanos": 180000000, "units": 1, "currency_code": "USD"}')
WHERE id = 'USSWO3XP9ZwLWRT240503';
