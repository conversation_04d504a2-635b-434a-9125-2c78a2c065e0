CREATE TABLE IF NOT EXISTS sherlock_feedbacks
(
	id 							UUID DEFAULT public.uuid_generate_v4() NOT NULL,
	feedback_category 		    VARCHAR NOT NULL,
	feedback_message        	VARCHAR NOT NULL,
	agent_email             	VARCHAR NOT NULL,
	feedback_identifier_type    VARCHAR,
	feedback_identifier_value  	VARCHAR,
	created_at 		    		TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at 	        		TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (id)
	);

CREATE INDEX IF NOT EXISTS sherlock_feedbacks_created_at_agent_email_index ON sherlock_feedbacks(created_at ASC, agent_email ASC);
CREATE INDEX IF NOT EXISTS sherlock_feedbacks_created_at_category_index ON sherlock_feedbacks(created_at ASC, feedback_category ASC);
CREATE INDEX IF NOT EXISTS sherlock_feedbacks_created_at_identifier_index ON sherlock_feedbacks(created_at ASC, feedback_identifier_value ASC) INCLUDE (feedback_identifier_type);
CREATE INDEX IF NOT EXISTS sherlock_feedbacks_updated_at_index ON sherlock_feedbacks(updated_at ASC);

comment on table sherlock_feedbacks is 'table to store feedback reported by agents through sherlock';
    comment on column sherlock_feedbacks.feedback_category is '{"proto_type":"cx.sherlock_feedbacks.FeedbackCategory", "comment": "enum denoting types of feedbacks ex: sherlock issue, product issue, user opinion etc}';
    comment on column sherlock_feedbacks.feedback_message is '{"comment": "raw text describing more about the issue"}';
    comment on column sherlock_feedbacks.feedback_identifier_type is '{"proto_type":"cx.sherlock_feedbacks.FeedbackIdentifierType", "comment": "enum denoting what type of identifier to is used to search feedback ex: ticket id"}';
    comment on column sherlock_feedbacks.feedback_identifier_value is '{"comment": "contains value of that is defined in feedback_identifier_type"}';

CREATE TABLE IF NOT EXISTS sherlock_feedback_meta_data_mappings
(
	id 							UUID DEFAULT public.uuid_generate_v4() NOT NULL,
	feedback_id     			UUID NOT NULL,
	frequency       			VARCHAR DEFAULT 'FEEDBACK_FREQUENCY_UNSPECIFIED',
	is_highlighted_before 		VARCHAR DEFAULT 'BOOLEAN_ENUM_UNSPECIFIED',
	is_urgent      				VARCHAR DEFAULT 'BOOLEAN_ENUM_UNSPECIFIED',
	created_at 		    		TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at 	        		TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (id),
	UNIQUE (feedback_id)
	);

CREATE INDEX IF NOT EXISTS sherlock_feedback_meta_data_mappings_frequency_index ON sherlock_feedback_meta_data_mappings(frequency ASC, is_urgent ASC);
CREATE INDEX IF NOT EXISTS sherlock_feedback_meta_data_mappings_is_highlighted_before_index ON sherlock_feedback_meta_data_mappings(is_highlighted_before ASC, frequency ASC);
CREATE INDEX IF NOT EXISTS sherlock_feedback_meta_data_mappings_is_urgent_index ON sherlock_feedback_meta_data_mappings(is_urgent ASC, is_highlighted_before ASC);
CREATE INDEX IF NOT EXISTS sherlock_feedback_meta_data_mappings_updated_at_index ON sherlock_feedback_meta_data_mappings(updated_at ASC);

comment on table sherlock_feedback_meta_data_mappings is 'table to store extended attributes like is_urgent, frequency, etc received on feedback reported by agents through sherlock';
comment on column sherlock_feedback_meta_data_mappings.feedback_id is '{"comment": "feedback_id references id value from sherlock_feedbacks table, this is to reference extended feedback information received on the same feedback."}';
comment on column sherlock_feedback_meta_data_mappings.frequency is '{"proto_type": "cx.sherlock_feedbacks.FeedbackFrequency", "comment": "enum denoting what is the frequency of the issue"}';
comment on column sherlock_feedback_meta_data_mappings.is_highlighted_before is '{"proto_type": "types.BooleanEnum", comment": "boolean value to describe weather current issue is highlighted before by agent"}';
comment on column sherlock_feedback_meta_data_mappings.is_urgent is '{"proto_type": "types.BooleanEnum", "comment": "boolean value to describe weather current issue is urgent or not"}';
