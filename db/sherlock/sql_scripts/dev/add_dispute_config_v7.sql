INSERT INTO dispute_configs(channel, dispute_type, transaction_status, escalation_mode, cool_off_days, receiver, provenance, config_version, cool_off_duration,
							questionnaire_root_node_id, questionnaire_root_node_answer, previous_attempt_status) VALUES

-- UDIR escalations
('UPI', 'AUTHORISED', 'DEEMED_SUCCESS', 'ESCALATION_MODE_UDIR', '0', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI-10', 'P2P', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'AUTHORISED', 'PENDING', 'ESCALATION_MODE_UDIR', '0', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI-10', 'P2P', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'AUTHORISED', 'SUCCESS', 'ESCALATION_MODE_UDIR', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI-10', 'P2M', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'AUTHORISED', 'DEEMED_SUCCESS', 'ESCALATION_MODE_UDIR', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI-10', 'P2M', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'AUTHORISED', 'PENDING', 'ESCALATION_MODE_UDIR', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI-10', 'P2M', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),

-- UPI UDIR  ESCALATION FAILED RECORDS
-- same records are eligible for UDIR
-- but if earlier escalation has failed, subsequent tries should go to non-UDIR (escalation mode: ESCALATION_MODE_FEDERAL_BANK)
-- earlier failed attempt is denoted by previous_attempt_status: PREVIOUS_ATTEMPT_STATUS_ESCALATING_TO_UDIR_FAILED
('UPI', 'AUTHORISED', 'DEEMED_SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_ESCALATING_TO_UDIR_FAILED'),
('UPI', 'AUTHORISED', 'PENDING', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_ESCALATING_TO_UDIR_FAILED'),
('UPI', 'AUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_ESCALATING_TO_UDIR_FAILED'),
('UPI', 'AUTHORISED', 'DEEMED_SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_ESCALATING_TO_UDIR_FAILED'),
('UPI', 'AUTHORISED', 'PENDING', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_ESCALATING_TO_UDIR_FAILED'),

-- UPI PROVENANCE_NA -- Federal escalation
('UPI', 'AUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '2', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'UNAUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'UNAUTHORISED', 'DEEMED_SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'UNAUTHORISED', 'PENDING', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'UNAUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'UNAUTHORISED', 'DEEMED_SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'UNAUTHORISED', 'PENDING', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),


-- UPI EXTERNAL PROVENANCE -- Federal escalation
('UPI', 'AUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '2', 'P2P', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'AUTHORISED', 'DEEMED_SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2P', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'AUTHORISED', 'PENDING', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2P', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'UNAUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2P', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'UNAUTHORISED', 'DEEMED_SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2P', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'UNAUTHORISED', 'PENDING', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2P', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'AUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2M', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'AUTHORISED', 'DEEMED_SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2M', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'AUTHORISED', 'PENDING', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2M', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'UNAUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2M', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'UNAUTHORISED', 'DEEMED_SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2M', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('UPI', 'UNAUTHORISED', 'PENDING', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'P2M', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),

-- IMPS - Federal escalation
('IMPS', 'AUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'IMPS', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('IMPS', 'AUTHORISED', 'PENDING', 'ESCALATION_MODE_FEDERAL_BANK', '2', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '48h', 'IMPS', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('IMPS', 'UNAUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'IMPS', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('IMPS', 'UNAUTHORISED', 'PENDING', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'IMPS', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),

-- CARD - Federal escalation
('CARD', 'AUTHORISED', 'PENDING', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'NA', 'ECOMM', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('CARD', 'AUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0','NA', 'ECOMM', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('CARD', 'UNAUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0','NA', 'ECOMM', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('CARD', 'AUTHORISED', 'PENDING', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'NA', 'POS', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('CARD', 'AUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0','NA', 'POS', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('CARD', 'UNAUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0','NA', 'POS', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('CARD', 'AUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '1','NA', 'ATM', 'DISPUTE_CONFIG_VERSION_V7', '24h', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('CARD', 'AUTHORISED', 'PENDING', 'ESCALATION_MODE_FEDERAL_BANK', '0', 'NA', 'VENDOR_BANK', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('CARD', 'AUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0','NA', 'VENDOR_BANK', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('CARD', 'UNAUTHORISED', 'SUCCESS', 'ESCALATION_MODE_FEDERAL_BANK', '0','NA', 'VENDOR_BANK', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),


-- these disputes are eligible for UDIR
-- if UDIR fails then we redirect them to Federal
-- but if user is still raising dispute after federal escalation has reached terminal stage then redirect those disputes to Freshdesk
('UPI', 'AUTHORISED', 'DEEMED_SUCCESS', 'FRESHDESK', '0', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'AUTHORISED', 'PENDING', 'FRESHDESK', '0', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'AUTHORISED', 'SUCCESS', 'FRESHDESK', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'AUTHORISED', 'DEEMED_SUCCESS', 'FRESHDESK', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'AUTHORISED', 'PENDING', 'FRESHDESK', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),


-- UPI PROVENANCE_NA -- Federal escalation failed, escalate to FRESHDESK
('UPI', 'AUTHORISED', 'SUCCESS', 'FRESHDESK', '2', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'UNAUTHORISED', 'SUCCESS', 'FRESHDESK', '0', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'UNAUTHORISED', 'DEEMED_SUCCESS', 'FRESHDESK', '0', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'UNAUTHORISED', 'PENDING', 'FRESHDESK', '0', 'P2P', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'UNAUTHORISED', 'SUCCESS', 'FRESHDESK', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'UNAUTHORISED', 'DEEMED_SUCCESS', 'FRESHDESK', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'UNAUTHORISED', 'PENDING', 'FRESHDESK', '0', 'P2M', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),


-- UPI EXTERNAL PROVENANCE -- Federal escalation failed, escalate to FRESHDESK
('UPI', 'AUTHORISED', 'SUCCESS', 'FRESHDESK', '2', 'P2P', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'AUTHORISED', 'DEEMED_SUCCESS', 'FRESHDESK', '0', 'P2P', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'AUTHORISED', 'PENDING', 'FRESHDESK', '0', 'P2P', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'UNAUTHORISED', 'SUCCESS', 'FRESHDESK', '0', 'P2P', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'UNAUTHORISED', 'DEEMED_SUCCESS', 'FRESHDESK', '0', 'P2P', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'UNAUTHORISED', 'PENDING', 'FRESHDESK', '0', 'P2P', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'AUTHORISED', 'SUCCESS', 'FRESHDESK', '0', 'P2M', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'AUTHORISED', 'DEEMED_SUCCESS', 'FRESHDESK', '0', 'P2M', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'AUTHORISED', 'PENDING', 'FRESHDESK', '0', 'P2M', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'UPI', 'NON_UDIR_AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'UNAUTHORISED', 'SUCCESS', 'FRESHDESK', '0', 'P2M', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'UNAUTHORISED', 'DEEMED_SUCCESS', 'FRESHDESK', '0', 'P2M', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('UPI', 'UNAUTHORISED', 'PENDING', 'FRESHDESK', '0', 'P2M', 'EXTERNAL', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'UPI', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),

-- IMPS -- Federal escalation failed, escalate to FRESHDESK
('IMPS', 'AUTHORISED', 'SUCCESS', 'FRESHDESK', '0', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'IMPS', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('IMPS', 'AUTHORISED', 'PENDING', 'FRESHDESK', '2', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '48h', 'IMPS', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('IMPS', 'UNAUTHORISED', 'SUCCESS', 'FRESHDESK', '0', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'IMPS', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('IMPS', 'UNAUTHORISED', 'PENDING', 'FRESHDESK', '0', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'IMPS', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),

-- CARD -- Federal escalation failed, escalate to FRESHDESK
('CARD', 'AUTHORISED', 'PENDING', 'FRESHDESK', '0', 'NA', 'ECOMM', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('CARD', 'AUTHORISED', 'SUCCESS', 'FRESHDESK', '0','NA', 'ECOMM', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('CARD', 'UNAUTHORISED', 'SUCCESS', 'FRESHDESK', '0','NA', 'ECOMM', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('CARD', 'AUTHORISED', 'PENDING', 'FRESHDESK', '0', 'NA', 'POS', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('CARD', 'AUTHORISED', 'SUCCESS', 'FRESHDESK', '0','NA', 'POS', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('CARD', 'UNAUTHORISED', 'SUCCESS', 'FRESHDESK', '0','NA', 'POS', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('CARD', 'AUTHORISED', 'SUCCESS', 'FRESHDESK', '1','NA', 'ATM', 'DISPUTE_CONFIG_VERSION_V7', '24h', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('CARD', 'AUTHORISED', 'PENDING', 'FRESHDESK', '0', 'NA', 'VENDOR_BANK', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('CARD', 'AUTHORISED', 'SUCCESS', 'FRESHDESK', '0','NA', 'VENDOR_BANK', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),
('CARD', 'UNAUTHORISED', 'SUCCESS', 'FRESHDESK', '0','NA', 'VENDOR_BANK', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'CARD', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_FEDERAL_BANK_ESCALATION_IN_TERMINAL_STAGE'),

('RTGS', 'AUTHORISED', 'SUCCESS', 'FRESHDESK', '0', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'RTGS', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('RTGS', 'AUTHORISED', 'PENDING', 'FRESHDESK', '1', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '24h', 'RTGS', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('RTGS', 'UNAUTHORISED', 'SUCCESS', 'FRESHDESK', '0', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'RTGS', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('RTGS', 'UNAUTHORISED', 'PENDING', 'FRESHDESK', '0', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'RTGS', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),

('NEFT', 'AUTHORISED', 'SUCCESS', 'FRESHDESK', '0', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'NEFT', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('NEFT', 'AUTHORISED', 'PENDING', 'FRESHDESK', '2', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '48h', 'NEFT', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('NEFT', 'UNAUTHORISED', 'SUCCESS', 'FRESHDESK', '0', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'NEFT', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('NEFT', 'UNAUTHORISED', 'PENDING', 'FRESHDESK', '0', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '0s', 'NEFT', 'UNAUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),


('INTRA_BANK', 'AUTHORISED', 'PENDING', 'FRESHDESK', '0', 'NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'INTRA_BANK', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED'),
('INTRA_BANK', 'AUTHORISED', 'SUCCESS', 'FRESHDESK', '0','NA', 'PROVENANCE_NA', 'DISPUTE_CONFIG_VERSION_V7', '5m', 'INTRA_BANK', 'AUTHORISED', 'PREVIOUS_ATTEMPT_STATUS_UNSPECIFIED')
ON CONFLICT (channel, dispute_type, transaction_status, receiver, provenance, config_version, previous_attempt_status)
DO UPDATE
SET
escalation_mode = EXCLUDED.escalation_mode,
cool_off_days = EXCLUDED.cool_off_days,
cool_off_duration = EXCLUDED.cool_off_duration,
questionnaire_root_node_id = EXCLUDED.questionnaire_root_node_id,
questionnaire_root_node_answer = EXCLUDED.questionnaire_root_node_answer;
