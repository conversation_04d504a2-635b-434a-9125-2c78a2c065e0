-- salary_lite_mandate_execution_requests table
create table if not exists salary_lite_mandate_execution_requests
(
    id                                   uuid    not null         default uuid_generate_v4() primary key,
    recurring_payment_id varchar not null ,
    request_status varchar not null default 'SALARY_LITE_MANDATE_EXECUTION_REQUEST_STATUS_UNSPECIFIED',
    created_at                           timestamp with time zone default now() not null,
    updated_at                           timestamp with time zone default now() not null,
    deleted_at                           timestamp with time zone default null
);

comment on table salary_lite_mandate_execution_requests is 'stores the salary lite mandate execution request';
comment on column salary_lite_mandate_execution_requests.recurring_payment_id is 'denotes the recurring payment id for which mandate execution has to be initiated';
comment on column salary_lite_mandate_execution_requests.request_status is '{"proto_type":"salaryprogram.SalaryLiteMandateExecutionRequestStatus", "comment":"denotes the status of the salary lite mandate execution request"}';

-- data team need this index for periodic snapshot query
create index if not exists salary_lite_mandate_execution_requests_updated_at_idx ON salary_lite_mandate_execution_requests USING btree (updated_at);

create index if not exists salary_lite_mandate_execution_requests_recurring_payment_id_idx ON salary_lite_mandate_execution_requests USING btree (recurring_payment_id);
