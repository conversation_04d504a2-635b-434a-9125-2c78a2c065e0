-- dropping and adding preferred_execution_date because ALTER was not possible due to typecast not being possible from date to integer
ALTER TABLE IF EXISTS salary_lite_mandate_requests DROP COLUMN preferred_execution_date;
ALTER TABLE IF EXISTS salary_lite_mandate_requests ADD COLUMN preferred_execution_day_of_month integer not null;
ALTER TABLE IF EXISTS salary_lite_mandate_requests ADD COLUMN bank varchar not null;

COMMENT ON COLUMN public.salary_lite_mandate_requests.preferred_execution_day_of_month IS 'stores the user preferred mandate execution date of month';
COMMENT ON COLUMN public.salary_lite_mandate_requests.bank IS 'stores the mandate remitter bank detailss';

CREATE INDEX salary_lite_mandate_req_preferred_execution_day_of_month_idx ON public.salary_lite_mandate_requests USING btree (preferred_execution_day_of_month);