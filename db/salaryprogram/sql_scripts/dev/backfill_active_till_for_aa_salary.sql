-- Make active_till = active_from + 120 days for aa salary users
WITH latest_records AS (
    SELECT
        salary_program_registration_id AS reg_id,
        MAX(created_at) AS latest_created_at
    FROM
        public.salary_program_activation_history
    WHERE
            activation_action = 'ADD_FUNDS'
    GROUP BY
        salary_program_registration_id
)
UPDATE
    public.salary_program_activation_history t
SET
    active_till = t.active_from + INTERVAL '2880 hours'
FROM
    latest_records lr
WHERE
    t.salary_program_registration_id = lr.reg_id
  AND t.created_at = lr.latest_created_at
  AND t.activation_action = 'ADD_FUNDS';