-- analyze query for getting salary active users with their salary txn verification requests.
explain analyse select * from (
								  select *, row_number() over (partition by actor_id order by txn_timestamp desc) as rn
								  from salary_program_activation_history
										   inner join salary_txn_verification_requests
													  on salary_program_activation_history.salary_txn_verification_request_id =
														 salary_txn_verification_requests.id
								  where active_till >= now()
							  ) as tb where tb.rn = 1 and tb.verified_by = 'VERIFIED_BY_OPS' and tb.verification_status IN ('REQUEST_STATUS_VERIFIED', 'REQUEST_STATUS_VERIFICATION_FAILED');
