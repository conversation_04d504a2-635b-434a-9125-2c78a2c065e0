-- for each lead, there should be only one allocation per month
CREATE UNIQUE INDEX collection_allocations_unique_month_idx ON collection_allocations (lead_id, EXTRACT(year FROM date_of_allocation), EXTRACT(month FROM date_of_allocation));
-- dropping these columns as they are not needed anymore
ALTER TABLE collection_allocations DROP COLUMN IF EXISTS installment_id;
ALTER TABLE collection_allocations DROP COLUMN IF EXISTS dpd_at_allocation;
