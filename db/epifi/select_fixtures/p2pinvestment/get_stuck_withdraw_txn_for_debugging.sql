-- Withdraw Internal
select a.vendor_investor_id,
	   b.VENDOR_REF_ID,
	   b.TYPE,
	   b.created_at,
	   b.updated_at,
	   b.details -> 'amount' ->> 'units' as amount,
	   (b.details ->> 'eta')             as eta
from p2p_investors as a
		 join p2p_investment_transactions as b
			  on a.id = b.investor_id
where b.type = 'INVESTMENT_TRANSACTION_TYPE_WITHDRAWAL'
  AND b.status = 'UNKNOWN'
  AND (b.details ->> 'eta')::TIMESTAMP <= '2022-11-01 11:30:00';
