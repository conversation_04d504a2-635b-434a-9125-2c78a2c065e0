-- updating the DOB via fixture
-- Users have updated their DOB post onboarding and also at federal's end
-- Need to update their DOB at our end for successful VKYC completion
update users
set profile = profile || jsonb_build_object('date_of_birth', '{"day": 5, "month": 18, "year": 2002}'::json),
    updated_at = now()
where id = '63dce232-7736-449e-b9a4-c47c44fb0e83';

update users
set profile = profile || jsonb_build_object('date_of_birth', '{"day": 5, "month": 10, "year": 1987}'::json),
    updated_at = now()
where id = '23bf43dc-36a4-4eb8-9022-c001cd6be33b';

update users
set profile = profile || jsonb_build_object('date_of_birth', '{"day": 1, "month": 1, "year": 1996}'::json),
    updated_at = now()
where id = '89fa94b5-dcf3-4aa9-b3a4-c1b878611981';

update users
set profile = profile || jsonb_build_object('date_of_birth', '{"day": 26, "month": 1, "year": 1996}'::json),
    updated_at = now()
where id = '8a3f7215-7afc-4917-9149-0336c891b32c';

update users
set profile = profile || jsonb_build_object('date_of_birth', '{"day": 9, "month": 3, "year": 1980}'::json),
    updated_at = now()
where id = 'd43f5215-7dcd-46c8-9cb4-b40fd0d001cf';
