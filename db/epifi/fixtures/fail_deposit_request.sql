-- Due to an ongoing issue at Federal, pre-closure deposit request are stuck in SUSPECT state as a result user is
-- getting blocked.
-- To unblock the user Federal has confirmed over email that any deposit requests stuck in SUSPECT even after 24 hours
-- of creation can be treated as failure.
-- The user can create another preclosure request
--
-- Already ran for DR211121odDgg4yQTSOA11YHAMZXug==, OD211121s138u0q8Q+WVDAI5cAqXBA==
-- 'DR211201aT7AVBNLRx2xvJhl7jRO0w==', 'OD211201cp/RWcBvRheu6GQwnun3jg==
-- 'DR211201xCZ2fSQOT6OnLeW2xtzHMg==', 'OD2112014v26yrv7S8q53orF4O4Myw=='
-- 'DR210828bpb0NXvkT8KjU01R3S3T4A==', 'OD210828iyaGrDzqSgOlq5inwhxc9g=='
-- 'DR211201U+lesme4Sqq5pNpbQ4iBaA==', 'OD2112013C9zzfJtSWSkYqMJDVsfKQ=='
-- 'DR211101Ruh7QxUcS3eNc/9aTH5h8A==', 'OD211101A0hSrbSqTe2WCw8Dw0tC7Q=='
-- 'DR220101MBli6TrARYSNzh9yd300Yg==', 'OD220101qdazTtZKQ/u7fkTLg+vBIQ=='
-- 'DR2201014tlFK04YSwaaXwutPoGvHg==', 'OD220101WZSSZDWVQ7ePBW2c8ddD4A=='
-- 'DR220101EmxC/l+GTD+wvTuF8mFPgg==', 'OD220101bJ3NhwXnRriVH9EPkYKpbA=='
-- 'DR220101QrasDHukSVO6f+lpeCZruQ==', 'OD220101+alwlpL2T8SQ+QkDKnIm1A=='
-- 'DR220101eTpFP4wVTbuc+nuDnqZW1w==', 'OD2201012+GjSVDvRnG/UVy/+ypY+A=='
-- 'DR211228p4EwsIR0QC+cs9fdpNGA9g==', 'OD211228L8i2K48USI2xo5W5cj0EMg=='
-- 'DR220101+RqPFqfKQC+ZT65yum/0+g==', 'OD22010187az8YvjRX235ggv3BJ0CA=='
-- 'DR220103JpLW1B/VT9qf7CywyFLgnQ==', 'OD220103BB4F2QZSTlK1ViatsL/k6A=='
-- 'DR220101Jf3MclFxSPGqBjIdF5cmHQ==', 'OD220101VJa9zil2R++5cBTG8odf7A=='
-- 'DR220101Jcc88cYVTIaqnyvmFksETg==', 'OD2201018QFTtgQyRUu3Dp3DX72Y3Q=='
-- 'DR220101B9SwYyRQRRy++wZyLdRFRQ==', 'OD220101sAnkDIi9SryRD6+gR4CYxw=='
-- 'DR220107WVkQTn7XTsyqywsnDuKjkw==', 'OD220107wWnEdjHAR7yF9U0DHTMgJQ=='
-- 'DR220101IJdZ0/aZTvq4xo7sQhK0rQ==', 'OD2201013m44+8t2Rq2ZdKJZRjAVyg=='
-- 'DR220103levDBXR9QYa/pclzrW7ePw==', 'OD220103Ll7BTAreR4y8cTF3QUmaEA=='
-- 'DR220108eLOB4xhWTWizo7k2V5sF5Q==', 'OD220108gDMsEIpmRHa0C16BhhNCXw=='
-- 'DR220101B9SwYyRQRRy++wZyLdRFRQ==', 'OD220101sAnkDIi9SryRD6+gR4CYxw=='
-- 'DR220101rOWaCgq4Rrq7i0qsPRHoFQ==', 'OD220101FXdcZciPQ4Cq3proTxRtig=='
-- 'DR220123eJm+Cw5YS9OfJ+ozVU40Ug==', 'OD220123/A5uJUJRRfOYwtM8b7Xvkw=='
-- 'DR220108WVYCxGNCRt+i0AJGfb2i3g==', 'OD220108gDzo/Y8sTmuw7wE46GIcIQ=='
-- 'DR220108CBT4fMeYSHa2Q7e19t3NvA==', 'OD220108H0AP3hIsRuW0LShD93tyKg=='
-- 'DR211125lwSByOgkSBK2kVjKa6+j/Q==', 'OD211125u792bF65RCiss4wxJGtU8A=='
-- 'DR2201081c73uSjASJKFv6hbvlXV9g==', 'OD220108ATmwKzNJSPGq1uffN2Vw4A=='
-- 'DR211031+Fm85mwpTt6jlLCFCkX0tw=='  'OD211031CSKYpoc+Q6uz8PHw8H9eDg=='
-- 'DR220101mquEN7NoTMqRqWYYgBUkUw=='
-- 'DR220314JynW7R2DQo60nNQ415HbLg==', 'OD220314J1JCtJ/tSWCD1bGThkr5GA=='
-- 'DR220401EuyjZyNaSnSWCymQuty8cQ==', 'OD220401m93eknjET8ujPt9cVCvOAw=='

UPDATE deposit_requests
SET state = 'REQUEST_FAILED', last_attempt = true
where id in ('DR220409uuITOmv9QkWkXWKmRGPzzg==')
  and state = 'REQUEST_IN_PROGRESS'
  and type = 'PRECLOSE';

UPDATE orders
SET status='FULFILLMENT_FAILED'
where id in ('OD220409sEQVGqkbR6aMTRFIRk68Tw==')
  and status = 'MANUAL_INTERVENTION'
  and workflow = 'PRECLOSE_DEPOSIT';

-- UPDATE deposit_requests
-- SET last_attempt = true
-- where id in ('DR220314JynW7R2DQo60nNQ415HbLg==')
--   and type = 'PRECLOSE';
