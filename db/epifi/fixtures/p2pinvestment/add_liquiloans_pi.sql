-- This fixture adds a payment instrument for Liquiloans account to be used by P2P investment
-- Rather than using a generic payment instrument we have versioned the PI as in the future we might get more
-- accounts from Liquiloans
INSERT
INTO payment_instruments (id, type, verified_name, identifier, state, deleted_at, capabilities, issuer_classification, ownership)
VALUES ('paymentinstrument-liquiloans-1', 'BANK_ACCOUNT', 'NDX P2P PRIVATE LIMITED',
		'{"account": {"account_type": "CURRENT", "actual_account_number": "****************", "ifsc_code": "IBKL0000004", "secure_account_number": "xxxxxxxxxxxx9880", "name":"NDX P2P Pvt Ltd Lenders Funding Account"}, "card": null, "upi": null}',
		'CREATED',
		NULL, '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 'EXTERNAL', 'EPIFI_TECH')
ON CONFLICT DO NOTHING;
