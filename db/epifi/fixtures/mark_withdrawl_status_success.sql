-- https://monorail.pointz.in/p/fi-app/issues/detail?id=26011

-- Mark p2p_investment_transaction with id = 'P2PIT220711Tppi1DjkQ2CXs8snRF671w==' as Success
UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
    payment_ref_id = '2:12072022:S2994648'
where id in ('P2PIT220711Tppi1DjkQ2CXs8snRF671w==');

UPDATE p2p_investment_transactions
SET details = jsonb_set(
	details::jsonb,
	array['timeline'],
	(details->'timeline')::jsonb || json_build_object('status','SUCCESS','subStatus','SUCCESS_WITHDRAWAL_COMPLETE','createdAt','2022-07-20T18:45:05.513719990Z')::jsonb)
where id in ('P2PIT220711Tppi1DjkQ2CXs8snRF671w==');

UPDATE orders
SET status = 'PAID'
where id = 'OD220711+dfelY9IRtam6LlyoSTQfg==';

-- update payment_ref_id for p2p_investment_transaction with id = 'P2PIT220622vXlers76SaKtHC3y7AMWgQ==' and mark orders as success

UPDATE p2p_investment_transactions
SET payment_ref_id = '2:24062022:S33290850'
where id = 'P2PIT220622vXlers76SaKtHC3y7AMWgQ==';

UPDATE orders
SET status = 'PAID'
where id = 'OD220622yVSxV/vMRS6e9wQExxpKhA==';
