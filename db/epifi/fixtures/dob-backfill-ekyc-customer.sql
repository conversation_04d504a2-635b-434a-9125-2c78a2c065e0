-- EKYC Customers where allowed to proceed with incorrect dob. The check was later added. Following are customer who were added before that
update users set profile = profile || jsonb_build_object('date_of_birth', '{"day": 13, "month": 9, "year": 1992}'::json) where id = 'e347ad97-359f-4ca1-be3a-4c5b9c174ebc';
update users set profile = profile || jsonb_build_object('date_of_birth', '{"day": 22, "month": 12, "year": 1997}'::json) where id = '1aee174c-4d97-4466-9559-996863f6ee97';
update users set profile = profile || jsonb_build_object('date_of_birth', '{"day": 16, "month": 9, "year": 1995}'::json) where id = '65fc8411-d910-4015-9fb2-4061acba000a';
update users set profile = profile || jsonb_build_object('date_of_birth', '{"day": 26, "month": 7, "year": 1996}'::json) where id = 'b4d4e47b-9576-4c51-9b1a-923a0d7d36ed';
update users set profile = profile || jsonb_build_object('date_of_birth', '{"day": 18, "month": 6, "year": 1989}'::json) where id = '1e627c4a-b818-4773-af85-70814b110403';
update users set profile = profile || jsonb_build_object('date_of_birth', '{"day": 25, "month": 10, "year": 1988}'::json) where id = '8e6733b7-d212-4ea8-8a15-f1cab6210133';
update users set profile = profile || jsonb_build_object('date_of_birth', '{"day": 26, "month": 5, "year": 1993}'::json) where id = 'b6b12ee3-4c20-4cad-bf70-9adee1d23e49';
update users set profile = profile || jsonb_build_object('date_of_birth', '{"day": 23, "month": 11, "year": 1996}'::json) where id = 'fa305c69-f21a-45cc-bada-8c2af1b820d9';
update users set profile = profile || jsonb_build_object('date_of_birth', '{"day": 13, "month": 5, "year": 1995}'::json) where id = '485f4f45-dcda-4a8d-9d88-7f9624f9baa2';
update users set profile = profile || jsonb_build_object('date_of_birth', '{"day": 29, "month": 3, "year": 1990}'::json) where id = 'e40f74c0-3e9c-4683-815d-7a3241c280f0';
update users set profile = profile || jsonb_build_object('date_of_birth', '{"day": 7, "month": 6, "year": 1993}'::json) where id = '32d0df57-3dd0-40d3-be2a-f79467e17c94';
