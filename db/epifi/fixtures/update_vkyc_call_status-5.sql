-- unexpected events from ka<PERSON><PERSON> made the state inconsistent for the below user even after getting kyc upgrade callback
update vkyc_summaries set status = 'VKYC_SUMMARY_STATUS_IN_REVIEW', sub_status = 'VKYC_SUMMARY_SUB_STATUS_ATTEMPT_IN_REVIEW' where actor_id = 'AC211101YlTfFBlwTu+FRsCeqwl95A==';
update vkyc_attempts set status = 'VKYC_ATTEMPT_STATUS_IN_REVIEW', sub_status = 'VKYC_ATTEMPT_SUB_STATUS_CALL_IN_REVIEW' where id = '92122fa9-8b4e-4803-af92-02b8fb2de9b1';
update vkyc_karza_call_infos set status = 'VKYC_KARZA_CALL_INFO_STATUS_IN_REVIEW', sub_status = 'VKYC_KARZA_CALL_INFO_SUB_STATUS_CALL_ENDED' where id = 'ccaa9347-a5c9-4bfb-a176-8e643ad02648';

update vkyc_summaries set status = 'VKYC_SUMMARY_STATUS_IN_REVIEW', sub_status = 'VKYC_SUMMARY_SUB_STATUS_ATTEMPT_IN_REVIEW' where actor_id = 'AC211228Zoig+i0AR6SbPdc06IJrwA==';
update vkyc_attempts set status = 'VKYC_ATTEMPT_STATUS_IN_REVIEW', sub_status = 'VKYC_ATTEMPT_SUB_STATUS_CALL_IN_REVIEW' where id = 'cd3e8e31-7ad6-4480-8445-32affb1d3b8f';
update vkyc_karza_call_infos set status = 'VKYC_KARZA_CALL_INFO_STATUS_IN_REVIEW', sub_status = 'VKYC_KARZA_CALL_INFO_SUB_STATUS_CALL_ENDED' where id = '232e426c-b1ac-4de4-91c8-392a11281826';
