-- Users were stuck in customer creation due to a previous init request's ack response timed out.
-- To unblock these customer, we update their request ids manually and restart enquiry check.
update users set fed_customer_info = fed_customer_info || jsonb_build_object('QueueRetryInfo', '{"req_id": "NEOCIFCRSat20210529074447T7c5v"}'::json) where id = 'acce2f3f-53f8-4990-92fc-28a46f72e89c';
update users set fed_customer_info = fed_customer_info || jsonb_build_object('QueueRetryInfo', '{"req_id": "NEOCIFCRSun20210530092825BPfbq"}'::json) where id = '02422851-01cb-4a7b-b190-f2e074b1898a';
update users set fed_customer_info = fed_customer_info || jsonb_build_object('QueueRetryInfo', '{"req_id": "NEOCIFCRSat20210529083102EjKsr"}'::json) where id = '5e07a619-5f19-4770-8dd6-28feac3a75f0';
update users set fed_customer_info = fed_customer_info || jsonb_build_object('QueueRetryInfo', '{"req_id": "NEOCIFCRSat20210529082528K94rR"}'::json) where id = '4f5d246d-7af1-40ac-ab58-6c8772365611';
update users set fed_customer_info = fed_customer_info || jsonb_build_object('QueueRetryInfo', '{"req_id": "NEOCIFCRSun20210530092348Ci2vt"}'::json) where id = '71eacdb7-bdef-4768-bf2d-a1a1faa5b6cd';
update users set fed_customer_info = fed_customer_info || jsonb_build_object('QueueRetryInfo', '{"req_id": "NEOCIFCRSat20210529083452H4eWp"}'::json) where id = 'cbb13555-9501-4275-b312-b8d3b81023d2';
