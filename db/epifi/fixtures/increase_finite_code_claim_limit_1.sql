-- Increase by 150 for these users who have completed 1 yr since unlock and current claim limit is > 150
-- select fc.id from finite_codes fc JOIN
--                   (
--                       select id, actor_id, unlocked_at, datediff('year', unlocked_at, CURRENT_DATE()) as years_since_unlock from inappreferral_unlocks where is_unlocked = true and years_since_unlock >= 1 and unlocked_at >= '2021-09-01' and unlocked_at <= '2022-10-14'
--                   ) iu ON iu.actor_id  = fc.actor_id where fc.channel = 'IN_APP_REFERRAL' and fc.type = 'REGULAR' and claim_limit > 150 order by fc.claim_limit desc;

-- Running this till 14th Oct 2022. Update the data range each time this is being triggered.

update finite_codes
set claim_limit = claim_limit + 150,
    is_active   = true,
    updated_at  = now()
where id in (
             'FC210808fd7HRUlARSGiaNIC9lb6zA==',
             'FC210808sTyDI7iRQs2g3mculVdCUA==',
             'FC210808dJGW0yvGTV+yRkfXbDHMzw==',
             'FC210808um5LSd7fR3ufYa+LXgURrw==',
             'FC210808QMa3UxhuSL2xW2uR0et5+w==',
             'FC210808EcKSHzCqQFyLCErCiRpF/w==',
             'FC210808+5pqaVpXRL6lBrmW85TiFw==',
             'FC210809IS4uDu40Sp6OHjw9BUW6Yg==',
             'FC210808XMwcG/KYTbO/ukxO1FNNAQ==',
             'FC2108084JOrU9AKSZS+iBBH9ugj/w==',
             'FC210808ylE3XZ4gTvGt5if3vpcJVw==',
             'FC210808nd6PvwcqSOyaVNzZj09EwA==',
             'FC210807IZ/WKSx8RxKPQfbg4HJkAg==',
             'FC210808Y0Ya1/nIT3qxT9vH81x68w==',
             'FC210806lYsCMRtSTEyt49K4C08GDw==',
             'FC211003wpIfyvPuRJKWHQdchl8FWg==',
             'FC210808lRyr1s0FTZKjkcjC88+Tuw==',
             'FC210808cWYn4aF5SH21AaQPiW5m1w==',
             'FC210816Q2+uHpB1SsG86lyAX/vPuw==',
             'FC211120WfAqqYZTQEulnCaC4S2WrQ==',
             'FC210814sLDi1omLSVmhmJ7B8sQh+A==',
             'FC210813noAgiTBuT9m7mhSgfadSjA==',
             'FC211220BkKK6eaYRMW0sAealT/6OQ==',
             'FC211216rJchtpeRRKmZAsGBuvg6nw==',
             'FC210816ymXq5q6HSUC6ACGOKXwX9A==',
             'FC211115KgeTrzilQL+E1CEBMAAsng==',
             'FC211122v3pSPZ9yT7KK8V8rPE9mng==',
             'FC21111914gRdjoUTZG4MjzqfGnytw==',
             'FC2108088HSMnSpESn6Z4q9qcvWz+A==',
             'FC2112159qUpBMTaTs6W0+NDUX+ybA==',
             'FC211127X02ET8KoQJa/iWNeeW+m8g==',
             'FC211127+4npG5dvSQW7NeHbeByV8g==',
             'FC2112036hLTzsXYTw60+GWblyC0IQ==',
             'FC211202FTqCzOe0RXmGIIfy4V3d4w==',
             'FC211025i4g/9CvZTHGfttLDh5ft1w==',
             'FC2112104uMFuc2BTF2Ej1egmL6x8w==',
             'FC211227Dz7zf4sbRtiKTF7LYKpnTA==',
             'FC211221EqttHjleTeCetrkSupuh2Q==',
             'FC210809mSry5hvFT66ZmtmAYM0fYQ==',
             'FC210814t4rBxgjAQ6yd0+M2+ICtRA==',
             'FC211128P/mvcTZiQKOFbQKmlE3/iw==',
             'FC211006iERQ4rGJQuuy5bNwTyfyxw==',
             'FC210831P0UX3DYyT0S3JeSEwPpPEQ==',
             'FC211127EG9yM276TwWjeb9gitcenA==',
             'FC211202oChGBl0JQAys4a2A1Rl40g==',
             'FC211202+p8/KKRDT/C1BxQFSNWSaA==',
             'FC210808W788eet6SMeekKi9yQwifQ==',
             'FC210809tyRGvj9fQVWUkc0njsTw2Q==',
             'FC210810ElaykIf+Sji1gd6+13CIIw==',
             'FC210808vfgaz9hZTqatRwrWIonOMg==',
             'FC211119R1Rfqz0YTbeZwsIAkhWABw==',
             'FC210810vpfEW4LjRYqh6IuPZ/WTiA==',
             'FC211223ba3CdHuWSu20XMcxB5qeCg==',
             'FC211218eylORuNBQrSjoheTZLXv5Q==',
             'FC211018FjFCJ2hyRyWn4EeDectRjQ==',
             'FC2108271cEC7E+7SIi4XoQyRPKhkQ==',
             'FC210806XZCoH1o1SOqwFaVaioZF1Q==',
             'FC210816NobRnendSD+UgTLw4Zhz5A==',
             'FC211202AZhlKVBQQ2GESXCJygASWw==',
             'FC2110240BGX4WFsQ8uKnD0s3wwdrg==',
             'FC210925dg1NU+CcTPaGrP1Y+M5clw==',
             'FC2112096f47ckCuTFKqbdLSIv1idw==',
             'FC210926h+mD7XMiTFCFPYuEXVIkcA==',
             'FC2108080WFxJxe+TQ2zQVxtnA3ChA=='
    );
