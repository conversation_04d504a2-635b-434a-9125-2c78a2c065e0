--https://monorail.pointz.in/p/fi-app/issues/detail?id=48087&q=owner%3Ame&can=2
-- one year old 42 Deposit requests are stuck in "Request_Unknown" or "Request_IN_Progress" due to issue from federal end
--where we got null deposit number in response, As the deposit request is not in terminal state
--respective actors are unable to create new deposits.
UPDATE orders SET status = 'FULFILLMENT_FAILED', updated_at = NOW() where id in ('OD220330xDTiQOq6SKGuQRC4d16G+A==',
																				'OD220402qzH96MpBS8K/Kg9rIFmn/A==',
																				'OD22050526LkVJP9ToqkIlTEr40OoQ==',
																				'OD220407C6BoC23kRmqDp9kdtMFlVw==',
																				'OD2203048BSMvOnZThuWFUWQuuSXOA==',
																				'OD220412gOETfpRESw2HihLCP+DmoA==',
																				'OD220505O2bWq017QySz0e/N/KI3qg==',
																				'OD220407hYoaArTFQ9mnKobAWXhMzQ==',
																				'OD220505zU8VlHMWQyCY71X1bo/9qg==',
																				'OD220407KUnapVZ7RS2OMSfMKbqIyQ==',
																				'OD220513oGeFaTIOS/uEj+qWxybh+g==',
																				'OD220625+otiPtBISW+WBWlUg/TPFg==',
																				'OD2204092u1CVt7SQdCv2dUaKyr7QA==',
																				'OD2206254dAqhsjlTF+OpiD5V9TZqw==',
																				'OD220505JR7g8wdUQySpXkVz59yWxw==',
																				'OD220412VC2guQlTRd+93TSedjmALg==',
																				'OD220407cGYpqUKpRdCuu5hDllp3bA==',
																				'OD220513sJeTNNJlRAmNa1zjlUu3vw==',
																				'OD220202RQwiUmJbQLGY4udCAE3vjQ==',
																				'OD2110098U6UVLIaQVmFCPI0VPCPhQ==',
                                                                                'OD211006YYLa63f1TUygs1zOZu0L+g==')
AND status = 'MANUAL_INTERVENTION' and WORKFLOW = 'CREATE_DEPOSIT' ;



UPDATE deposit_requests SET state = 'REQUEST_FAILED', last_attempt = true, updated_at = NOW() where id in ('DR220330Twdqm6oST8emfBSM82NN/w==',
																					                       'DR220402xqgBi77ORDu5nLe3UzlH4w==',
																					                       'DR220505jeb5zjxrQtSTmekwzuORGg==',
																					                       'DR220413UAKdusKrQfKb6r1WnZtn+A==',
																					                       'DR220405epKH0/w6T46cg4jblOiM4w==',
																					                       'DR220407/MOxrEZpTXC6sFp06L28pA==',
																					                       'DR220304hD5C925DSA22/ykbgYcb0w==',
																					                       'DR2204126I7klmF/Qh+Y13E22IW6qA==',
																					                       'DR2206011y81PYNDTj6jTZA8Jx8uHA==',
																					                       'DR220505k4fasdqWTMmKFnp+I8Zj2A==',
																					                       'DR2204070NsNFq0iSJGL8/RngptUDw==',
																					                       'DR220505Yl8yIcvOSfOwZWGFTSM5Ow==',
																					                       'DR220407WH8y9q7bSkaT091OwhEWrQ==',
																					                       'DR220513gTTCcdjdRA2MfwMkp+WJRg==',
																					                       'DR220514qHj7PnvyQ0OHLJhvb0bpuA==',
																					                       'DR220410LJc6HnatQ2K6+fonfkV29w==',
																					                       'DR2206255fvd4IliQ/irEYHeoEuy2A==',
																					                       'DR220409DiL0G5f2T061hfidw7hZdw==',
																					                       'DR220625WTkiyTLVScuqEwGDdx2hmQ==',
																					                       'DR220413viCAdTsRSOiv0zltCxunGw==',
																					                       'DR220505Q4+ashAATmW6ECPgX/Yqjg==',
																					                       'DR220414OCokmgajTmuxi3Zpgb+n0w==',
																					                       'DR220412lWTdbniuQai+UDA1Ckv71Q==',
																					                       'DR220407qR8qb0L9Rz+9EuFlT3wvRA==',
																					                       'DR220513YMFgKI/2TM6mIjGA2iqKrA==',
																					                       'DR210808b8IdMHFER4eDBkhM09wfSA==',
																					                       'DR211210FHq2VmqiRxWbMPYDZ3Sfzg==',
																					                       'DR220101nsit1SZMQ0mDL2CfY6dh3A==',
																					                       'DR220101/w+y143JTbmGbVTmgFFOgw==',
																					                       'DR220101v95pd+7ST2KQ5f8xM7cFpA==',
																					                       'DR220101VIah86Y5Ru+slGEVZAHQNg==',
																					                       'DR220202nRJoVELHTPmg26DCj+t/yQ==',
																					                       'DR2202023umrHsYVT1aT4xlE078Qeg==',
																					                       'DR220205ZAh+qP8BTp+fX1B4Kw+unw==',
																					                       'DR220205M2Ia8SIsQ0uCsqhl8+ir/Q==',
																	 				                       'DR220211iMBFOFHDS7Cna3tuDek+fA==',
																					                       'DR220218xbpizaZpQbSxBo6bngSKPg==',
																					                       'DR22030114JlXnHKQDO99CFru4y5NQ==',
																					                       'DR2209192UdJOMaiQY2yCYtDhVQGUg==',
																					                       'DR211006OeVjYLzuS3WRMPmm+2JB1g==',
																					                       'DR211009zI/mHVUmQ2mljEJAqkRBBw==',
																					                       'DR220919XZ7RTdMURsKEq34yN+rFrw==')
and state in ('REQUEST_IN_PROGRESS','REQUEST_UNKNOWN');
