-- https://monorail.pointz.in/p/fi-app/issues/detail?id=28844
-- REWARDS_CREATE_SD orders are stuck in IN_PAYMENT/MANUAL_INTERVENTION state
-- when FUND_TRANSFER_PAYMENT_ENQUIRY api returned "rawStatusCode": "F016" "rawStatusDescription": "DATA NOT FOUND"
-- Looked at orders with same error : OD220928s5IQNbZXRCShHqONU6ulnw==, OD220927utpiALHMSIufb/WsmuQaPQ== and
-- order is in PAYMENT_FAILED, txn in FAILED status

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN221001WZrfKlr5Rv6D0VrnRbMkFA==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220206T2wehH5ISbCElnOgSbuTbA==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220206CqK5rvW6RHmlhM+seVrbrA==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220928m9Oql1RGSwCH4FXP0o0pHQ==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220205RDGWgPuBT++l6wXb+rGcww==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220204izSGrtUtQ/mstNxl1YMzVg==' AND status = 'MANUAL_INTERVENTION' ;
