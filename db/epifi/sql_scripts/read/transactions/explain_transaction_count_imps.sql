EXPLAIN ANALYZE SELECT COUNT(*) FROM transactions
WHERE (pi_from in ('PIlBBad/+OTSimlrSEQLsLdA240214==','PI220102OJ8beN2QTmqlG9pELvz0ng==','PI220102f/xkTYkmQtuEw1YTy0jbmA==','PI220102CbgNc63TQCKb8Ty+X2OS8Q=='))
AND (COALESCE(debited_at, credited_at, created_at) >= '2025-01-29 00:00:00' ::TIMESTAMPTZ )
AND (COALESCE(debited_at, credited_at, created_at) <= '2025-01-31 00:00:00' ::TIMESTAMPTZ)
AND payment_protocol IN ('IMPS')
AND status IN ('SUCCESS','UNKNOWN','MANUAL_INTERVENTION','IN_PROGRESS','INITIATED')
