-- update overallStatus as in progress which got marked as failure due to OBE0170 in enquiry
-- https://monorail.pointz.in/p/fi-app/issues/detail?id=80092

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_FAILED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = '9a8ac1c5-6f63-4aac-8036-627ee93e9289';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_IN_PROGRESS',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = '091faa4b-f04e-4503-be87-850151a31a34';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{failureType}', to_jsonb('UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED'))
where id = '091faa4b-f04e-4503-be87-850151a31a34';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_UNSPECIFIED'))
where id = '091faa4b-f04e-4503-be87-850151a31a34';
