CREATE TABLE IF NOT EXISTS p2p_investors
(
	id                 STRING      NOT NULL,
	actor_id           STRING      NOT NULL,
	vendor_investor_id STRING      NULL,
	vendor             STRING      NOT NULL,
	status             STRING      NOT NULL,
	sub_status         STRING      NOT NULL,
	details            JSONB       NULL,
	created_at         TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	updated_at         TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	deleted_at         TIMESTAMPTZ NULL,

	PRIMARY KEY (id ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX unique_index_on_actor_id_and_vendor (actor_id, vendor ASC),

	FAMILY "frequently_updated" (details, status, sub_status, updated_at),
	FAMILY "seldom_updated" (vendor_investor_id, deleted_at)
);

COMMENT ON TABLE p2p_investors IS 'table to store all the details related to investor created at vendor for p2p investment';
COMMENT ON COLUMN p2p_investors.actor_id IS 'actor for which investor is created';
COMMENT ON COLUMN p2p_investors.vendor_investor_id IS 'identifier of investor received from vendor';
COMMENT ON COLUMN p2p_investors.vendor IS '{"proto_type":"p2pinvestment.Vendor", vendor in which investor is created"}';
COMMENT ON COLUMN p2p_investors.status IS '{"proto_type":"p2pinvestment.InvestorStatus", "comment":"investor creation status"}';
COMMENT ON COLUMN p2p_investors.sub_status IS '{"proto_type":"p2pinvestment.InvestorSubStatus", "comment":"investor creation status or granular info on status"}';
COMMENT ON COLUMN p2p_investors.details IS '{"proto_type":"p2pinvestment.InvestorDetails", "comment":"details of investor which are not queryable"}';

CREATE TABLE IF NOT EXISTS p2p_investment_schemes
(
	id               STRING      NOT NULL,
	vendor_scheme_id STRING      NOT NULL,
	vendor           STRING      NOT NULL,
	details          JSONB       NULL,
	created_at       TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	updated_at       TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	deleted_at       TIMESTAMPTZ NULL,

	PRIMARY KEY (id ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX unique_index_on_vendor_scheme_id_and_vendor (vendor_scheme_id, vendor ASC),

	FAMILY "seldom_updated" (details, deleted_at, updated_at)
);

COMMENT ON TABLE p2p_investment_schemes IS 'stores all the schemes for investments';
COMMENT ON COLUMN p2p_investment_schemes.vendor_scheme_id IS 'corresponding vendor scheme id';
COMMENT ON COLUMN p2p_investment_schemes.vendor IS '{"proto_type":"p2pinvestment.Vendor", "comment":"vendor through which corresponding scheme id mapped"}';
COMMENT ON COLUMN p2p_investment_schemes.details IS '{"proto_type":"p2pinvestment.SchemeDetails", "comment":"scheme details provided by vendor and info added by epifi"}';

CREATE TABLE IF NOT EXISTS p2p_investment_transactions
(
	id                  STRING      NOT NULL,
	investor_id         STRING      NOT NULL,
	scheme_id           STRING      NOT NULL,
	order_client_req_id STRING      NOT NULL,
	payment_ref_id      STRING      NULL,
	vendor_ref_id       STRING      NULL,
	type                STRING      NOT NULL,
	vendor              STRING      NOT NULL,
	status              STRING      NOT NULL,
	sub_status          STRING      NOT NULL,
	details             JSONB       NULL,
	created_at          TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	updated_at          TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	deleted_at          TIMESTAMPTZ NULL,

	PRIMARY KEY (id ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX unique_index_on_order_client_req_id (order_client_req_id ASC),
	INDEX index_on_investor_id (investor_id ASC),
	CONSTRAINT fk_investor_id_ref_investors FOREIGN KEY (investor_id) REFERENCES p2p_investors (id),
	CONSTRAINT fk_scheme_id_ref_schemes FOREIGN KEY (scheme_id) REFERENCES p2p_investment_schemes (id),

	FAMILY "frequently_updated" (details, status, sub_status, updated_at),
	FAMILY "seldom_updated" (payment_ref_id, vendor_ref_id, deleted_at)
);

COMMENT ON TABLE p2p_investment_transactions IS 'stores all the transaction done by an investor';
COMMENT ON COLUMN p2p_investment_transactions.investor_id IS 'foreign key from investors table';
COMMENT ON COLUMN p2p_investment_transactions.payment_ref_id IS 'payment reference provided by bank when money is transferred from customer account to vendor account';
COMMENT ON COLUMN p2p_investment_transactions.vendor_ref_id IS 'transaction reference provided by vendor';
COMMENT ON COLUMN p2p_investment_transactions.order_client_req_id IS 'request id passed to order service in create call';
COMMENT ON COLUMN p2p_investment_transactions.scheme_id IS 'scheme id for which transaction is done';
COMMENT ON COLUMN p2p_investment_transactions.type IS '{"proto_type":"p2pinvestment.TransactionType", "comment":"type of transaction Investment/Withdrawal"}';
COMMENT ON COLUMN p2p_investment_transactions.vendor IS '{"proto_type":"p2pinvestment.Vendor", "comment":"vendor for which transaction is done"}';
COMMENT ON COLUMN p2p_investment_transactions.status IS '{"proto_type":"p2pinvestment.TransactionStatus", "comment":"status of transaction"}';
COMMENT ON COLUMN p2p_investment_transactions.sub_status IS '{"proto_type":"p2pinvestment.TransactionSubStatus", "comment":"transaction sub-status or granular info on status"}';
COMMENT ON COLUMN p2p_investment_transactions.details IS '{"proto_type":"p2pinvestment.InvestmentTransactionDetails", "comment":"stores details of an investment transaction"}';
