CREATE TABLE IF NOT EXISTS reward_offers
(
    -- id will be auto generated UUID
    id               UUID      NOT NULL DEFAULT gen_random_uuid(),
    -- status of the config
    status           STRING    NULL,
    -- this config will be active after this timestamp
    active_since     TIMESTAMP NULL,
    -- this config will be active till this timestamp
    active_till      TIMESTAMP NULL,
    -- this config will be displayed this timestamp
    display_since    TIMESTAMP NULL,
    -- this config will be displayed till this timestamp
    display_till     TIMESTAMP NULL,
    created_at       TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
    updated_at       TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
    -- identifier of the creator normally email
    created_by       STRING    NULL,
    -- action type for which this offer will be evaluated
    action_type      STRING    NULL,
    -- constraints meta data used in rule to check the condition for which offer is applicable
    constraints_meta JSONB     NULL,
    -- reward meta data used in rule to create the reward after all the constraints are passed
    reward_meta      JSONB     NULL,
    -- reward meta data used in rule to create the reward after all the constraints are passed
    display_meta     JSONB     NULL
);
