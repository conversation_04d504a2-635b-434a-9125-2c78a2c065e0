CREATE TABLE IF NOT EXISTS public.device_params_store (

    id UUID NOT NULL DEFAULT gen_random_uuid(),

	token_id STRING NOT NULL,

	param_type STRING NOT NULL,

	param_value STRING NOT NULL,

	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::T<PERSON>ESTAMPTZ,

	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,

	deleted_at TIMESTAMPTZ NULL,

	CONSTRAINT "primary" PRIMARY KEY (id ASC),

	CONSTRAINT fk_token_id_ref_token_store FOREIGN KEY (token_id) REFERENCES token_stores(id),

	FAMIL<PERSON> "primary" (id, created_at, updated_at, deleted_at, token_id, param_type, param_value)
);

comment on table device_params_store is 'table to store device parameters such as ip address, location etc.';
