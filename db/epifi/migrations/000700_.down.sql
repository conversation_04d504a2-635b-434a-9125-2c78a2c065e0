CREATE TABLE public.actor_tier_infos (
										 actor_id STRING NOT NULL,
										 tier STRING NOT NULL,
										 movement_reference_id UUID NOT NULL,
										 criteria_reference_id UUID NOT NULL,
										 created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
										 updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
										 CONSTRAINT actor_tier_infos_pkey PRIMARY KEY (actor_id ASC),
										 INDEX updated_at_idx (updated_at DESC)
);
COMMENT ON TABLE public.actor_tier_infos IS 'master table to store current tier of the user, this will serve as a source of truth of the users tier';
COMMENT ON COLUMN public.actor_tier_infos.tier IS '{"proto_type":"tiering.enums.Tier", "comment":"tier name"}';

CREATE TABLE public.tier_movement_histories (
												id UUID NOT NULL DEFAULT gen_random_uuid(),
												eligibility_movement_reference_id UUID NOT NULL,
												criteria_reference_id UUID NOT NULL,
												from_tier STRING NOT NULL,
												to_tier STRING NOT NULL,
												actor_id STRING NOT NULL,
												movement_type STRING NOT NULL,
												provenance STRING NOT NULL,
												reason JSONB NULL,
												created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
												updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
												CONSTRAINT tier_movement_histories_pkey PRIMARY KEY (actor_id ASC, id ASC),
												UNIQUE INDEX id_idx (id ASC),
												INDEX updated_at_idx (updated_at DESC),
												INDEX eligibility_movement_reference_id_idx (eligibility_movement_reference_id ASC),
												INDEX actor_id_from_tier_movement_type_created_at_idx (actor_id ASC, from_tier ASC, movement_type ASC, created_at DESC),
												INDEX actor_id_movement_type_created_at_idx (actor_id ASC, movement_type ASC, created_at DESC),
												INDEX actor_id_to_tier_created_at_idx (actor_id ASC, to_tier ASC, created_at DESC)
);
COMMENT ON TABLE public.tier_movement_histories IS 'capture tier change of a user';
COMMENT ON COLUMN public.tier_movement_histories.from_tier IS '{"proto_type":"tiering.enums.Tier", "comment":"from tier"}';
COMMENT ON COLUMN public.tier_movement_histories.to_tier IS '{"proto_type":"tiering.enums.Tier", "comment":"to tier"}';
COMMENT ON COLUMN public.tier_movement_histories.movement_type IS '{"proto_type":"tiering.enums.TierMovementType", "comment":"movement type"}';
COMMENT ON COLUMN public.tier_movement_histories.provenance IS '{"proto_type":"tiering.enums.Provenance", "comment":"provenance of upgrade or downgrade"}';


CREATE TABLE public.eligible_tier_movements (
												id UUID NOT NULL DEFAULT gen_random_uuid(),
												criteria_reference_id UUID NOT NULL,
												from_tier STRING NOT NULL,
												to_tier STRING NOT NULL,
												actor_id STRING NOT NULL,
												movement_type STRING NOT NULL,
												movement_timestamp TIMESTAMPTZ NOT NULL,
												status STRING NOT NULL,
												created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
												updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
												deleted_at TIMESTAMPTZ NULL,
												details JSONB NOT NULL,
												CONSTRAINT eligible_tier_movements_pkey PRIMARY KEY (actor_id ASC, id ASC),
												UNIQUE INDEX id_idx (id ASC),
												INDEX updated_at_idx (updated_at DESC),
												INDEX actor_id_status_created_at_idx (actor_id ASC, status ASC, created_at DESC),
												INDEX actor_id_movement_type_created_at_idx (actor_id ASC, movement_type ASC, created_at DESC)
);
COMMENT ON TABLE public.eligible_tier_movements IS 'store users eligible tier movement due to QC evaluation changes';
COMMENT ON COLUMN public.eligible_tier_movements.from_tier IS '{"proto_type":"tiering.enums.Tier", "comment":"from tier"}';
COMMENT ON COLUMN public.eligible_tier_movements.to_tier IS '{"proto_type":"tiering.enums.Tier", "comment":"to tier"}';
COMMENT ON COLUMN public.eligible_tier_movements.movement_type IS '{"proto_type":"tiering.enums.TierMovementType", "comment":"movement type"}';
COMMENT ON COLUMN public.eligible_tier_movements.status IS '{"proto_type":"tiering.enums.EligibleTierMovementStatus", "comment":"eligible tier movement status"}';
COMMENT ON COLUMN public.eligible_tier_movements.details IS '{"proto_type":"tiering.EligibilityDetails", "comment":"details of the eligible movement stored as jsonb"}';


CREATE TABLE public.tier_criteria (
									  id UUID NOT NULL DEFAULT gen_random_uuid(),
									  criteria_name STRING NOT NULL,
									  details JSONB NOT NULL,
									  status STRING NOT NULL,
									  executed_at TIMESTAMPTZ NULL,
									  created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
									  updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
									  CONSTRAINT tier_criteria_pkey PRIMARY KEY (id ASC),
									  INDEX updated_at_idx (updated_at DESC),
									  UNIQUE INDEX criteria_name_idx (criteria_name ASC)
);
COMMENT ON TABLE public.tier_criteria IS 'stores scheme related information which is the set of qualifying criteria';
COMMENT ON COLUMN public.tier_criteria.criteria_name IS '{"proto_type":"tiering.enums.CriteriaName", "comment":"tier criteria name"}';
COMMENT ON COLUMN public.tier_criteria.details IS 'details of a tier criteria';
COMMENT ON COLUMN public.tier_criteria.status IS '{"proto_type":"tiering.enums.TierCriteriaStatus", "comment":"status of tier criteria"}';
COMMENT ON COLUMN public.tier_criteria.executed_at IS 'timestamp at which criteria got first triggered';

ALTER TABLE public.actor_tier_infos ADD CONSTRAINT fk_tier_criteria FOREIGN KEY (criteria_reference_id) REFERENCES public.tier_criteria(id);
ALTER TABLE public.actor_tier_infos ADD CONSTRAINT fk_tier_movement_histories FOREIGN KEY (movement_reference_id) REFERENCES public.tier_movement_histories(id);
ALTER TABLE public.actor_tier_infos VALIDATE CONSTRAINT fk_tier_criteria;
ALTER TABLE public.actor_tier_infos VALIDATE CONSTRAINT fk_tier_movement_histories;
ALTER TABLE public.tier_movement_histories ADD CONSTRAINT fk_tier_criteria FOREIGN KEY (criteria_reference_id) REFERENCES public.tier_criteria(id);
ALTER TABLE public.tier_movement_histories ADD CONSTRAINT fk_eligible_tier_movements FOREIGN KEY (eligibility_movement_reference_id) REFERENCES public.eligible_tier_movements(id);
ALTER TABLE public.tier_movement_histories VALIDATE CONSTRAINT fk_tier_criteria;
ALTER TABLE public.tier_movement_histories VALIDATE CONSTRAINT fk_eligible_tier_movements;
ALTER TABLE public.eligible_tier_movements ADD CONSTRAINT fk_tier_criteria FOREIGN KEY (criteria_reference_id) REFERENCES public.tier_criteria(id);
ALTER TABLE public.eligible_tier_movements VALIDATE CONSTRAINT fk_tier_criteria;
