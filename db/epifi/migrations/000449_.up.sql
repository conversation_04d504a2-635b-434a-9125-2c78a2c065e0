CREATE TABLE IF NOT EXISTS public.risk_data (
    id UUID NOT NULL DEFAULT gen_random_uuid(),
    actor_id STRING NOT NULL,
    risk_param STRING NOT NULL,
    payload JSONB,
    result STRING,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
    deleted_at_unix INT8 NOT NULL DEFAULT 0,
    CONSTRAINT "primary" PRIMARY KEY (actor_id, risk_param, deleted_at_unix, id),
    CONSTRAINT "risk_data_actor_id_risk_param_deleted_at_unix_unique_idx" UNIQUE(actor_id, risk_param, deleted_at_unix),
    CONSTRAINT "risk_data_id_unique_idx" UNIQUE(id)
    );

comment on table risk_data is 'table to store data for risk model';
CREATE INDEX IF NOT EXISTS risk_data_updated_at_idx ON risk_data(updated_at);