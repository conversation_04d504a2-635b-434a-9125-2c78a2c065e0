CREATE TABLE IF NOT EXISTS card_tracking_requests (
    -- auto generated id of the table, primary key.
    id UUID NOT NULL DEFAULT gen_random_uuid(),
    -- unique identifier for each card
    card_id UUID NOT NULL,
    -- Unique identifier for a given shipment
    order_id UUID NOT NULL DEFAULT gen_random_uuid(),
    -- awb number of the shipment
    awb STRING,
    -- carrier of the shipment (DELHIVERY, BLUEDART etc)
    carrier STRING,
    -- request_state of the card shipment.
    request_state STRING,
    -- delivery status of the shipment
    delivery_state STRING,
    -- A package is scanned multiple times throughout the journey. On every scan, there is an update in tracking information.
    scans JSONB NULL,
    created_at          TIMESTAMPTZ        NOT NULL DEFAULT NOW(),
    updated_at          TIMESTAMPTZ        NOT NULL DEFAULT NOW(),
    deleted_at          TIMESTAMPTZ,
    CONSTRAINT "primary" PRIMARY KEY (id ASC),
    CONSTRAINT fk_card_tracking_requests_card_id FOREIGN KEY (card_id) REFERENCES cards(id),
    -- Index for fetching tracking requests by order id. It will be used for processing notifications
    UNIQUE INDEX card_tracking_requests_order_id_unique_idx (order_id),
    -- Index for fetching latest tracking details for a given card id. Will be used to show tracking details on UI
    INDEX card_id_lookup_idx (card_id, created_at),
    -- Index for fetching all tracking requests for a given state. Will be used while periodically fetching awb numbers from bank.
    INDEX request_state_lookup_idx (request_state),
    -- Index required by data team
    INDEX card_tracking_requests_updated_at_idx (updated_at)
);

comment on table card_tracking_requests is 'table to store card tracking requests information such as awb number, carrier, order_id(unique identifier for each shipment), card_id(unique card identifier), tracking states and scans for a given shipment.';
comment on column card_tracking_requests.request_state is '{"proto_type":"cardTrackingRequest.RequestState", "comment": "enum to store various states such as AWB_FETCHED_FROM_BANK, REGISTERED_AT_SHIPWAY etc}';
comment on column card_tracking_requests.scans is '{"proto_type":"cardTrackingRequest.Scans", "comment": A package is scanned multiple times throughout the journey. On every scan, there is an update in tracking information. Storing information for all the scans"}';
comment on column card_tracking_requests.delivery_state is '{"proto_type":"cardTrackingRequest.DeliveryState", "comment": "enum to store delivery states of shipment such as SHIPPED, IN_TRANSIT, OUT_FOR_DELIVERY, DELIVERED etc}';
