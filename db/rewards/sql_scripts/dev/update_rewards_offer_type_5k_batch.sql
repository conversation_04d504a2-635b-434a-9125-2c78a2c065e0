-- update offer_type for salary or infinite tier rewards (internal and external offer)
WITH limited_rewards AS (
	SELECT id
	FROM rewards
	WHERE offer_id IN (
					   '9600f127-e676-4bf0-9355-00e795d2ef96',
					   '10d0e63d-1ba3-4661-9405-a2253d3eade3',
					   '9acbba3b-2369-4336-8cb2-5f0e0d503883',
					   '1664ea55-fbe0-464a-b5c0-dac768a72b01',
					   '916ae659-dad4-4449-b6ba-e581ac75f166',
					   '6f87ce6b-1a3b-484e-b3bf-84b10cf8dc62',
					   'aa95b255-42f0-4106-b49f-bf0548a9085c'
		)
	  AND action_type = 'TIERING_PERIODIC_REWARD_EVENT'
	  AND offer_type = 'UNSPECIFIED_REWARD_OFFER_TYPE'
	  AND type != 'NO_REWARD'
	LIMIT 5000
	)
UPDATE rewards
SET offer_type = 'INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER'
WHERE id IN (SELECT id FROM limited_rewards);

