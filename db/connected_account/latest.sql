CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.aa_account_column_histories (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    account_id uuid NOT NULL,
    column_name character varying NOT NULL,
    column_value character varying NOT NULL,
    update_timestamp timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.aa_account_column_histories IS 'table to maintain history of value changes for mutable fields of aa accounts';
COMMENT ON COLUMN public.aa_account_column_histories.column_name IS '{"proto_type":"connected_account.enums.AaAccountMutableColumn", "comment":"stores column name for which value has changed"}';
CREATE TABLE public.aa_accounts (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    masked_account_number character varying NOT NULL,
    linked_account_ref character varying NOT NULL,
    version character varying,
    profile jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    acc_instrument_type character varying NOT NULL,
    deleted_at_unix bigint DEFAULT 0 NOT NULL,
    fip_id character varying NOT NULL,
    status character varying,
    sub_status character varying,
    sub_type jsonb,
    last_synced_at timestamp with time zone,
    aa_entity character varying NOT NULL,
    tags character varying[]
);
COMMENT ON TABLE public.aa_accounts IS 'table to store all account info of a user';
COMMENT ON COLUMN public.aa_accounts.status IS '{"proto_type":"connected_account.enums.AccountStatus", "comment":"status of account"}';
COMMENT ON COLUMN public.aa_accounts.sub_status IS '{"proto_type":"connected_account.enums.AccountSubStatus", "comment":"sub status of account"}';
COMMENT ON COLUMN public.aa_accounts.tags IS '{"proto_type":"connected_account.AccountTag", "comment":"stores list of tags associated with an account"}';
CREATE TABLE public.aa_analysed_users (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    actor_id character varying(255) NOT NULL,
    analysis jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.aa_analysis_requests (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    orch_id character varying(255) NOT NULL,
    actor_id character varying(255) NOT NULL,
    details jsonb,
    status character varying(255) NOT NULL,
    completed_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.aa_batch_process_transactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    fetch_attempt_id uuid NOT NULL,
    process_attempt_id uuid NOT NULL,
    batch_number bigint NOT NULL,
    txn_start_timestamp timestamp with time zone NOT NULL,
    txn_end_timestamp timestamp with time zone NOT NULL,
    status character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.aa_batch_process_transactions IS 'table to store all batch process attempts made inorder to store transactions';
COMMENT ON COLUMN public.aa_batch_process_transactions.status IS '{"proto_type":"connected_account.BatchProcessStatus", "comment":"stores batch status"}';
CREATE TABLE public.aa_connection_flows (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    ca_flow_name character varying DEFAULT 'CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT'::character varying NOT NULL,
    ca_flow_params jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    client_req_id character varying
);
COMMENT ON TABLE public.aa_connection_flows IS 'stores data related to flows started for connecting accounts';
COMMENT ON COLUMN public.aa_connection_flows.id IS 'unique identifier of a flow initiated';
COMMENT ON COLUMN public.aa_connection_flows.ca_flow_name IS 'a unique name identifying a particular type of flows';
COMMENT ON COLUMN public.aa_connection_flows.ca_flow_params IS 'parameters that may be used later in the flow to take actions like redirecting to a specific screen';
COMMENT ON COLUMN public.aa_connection_flows.client_req_id IS 'unique identifier for client request tracking';
CREATE TABLE public.aa_consent_account_mappings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    consent_reference_id uuid NOT NULL,
    account_reference_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE TABLE public.aa_consent_requests (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    transaction_id character varying NOT NULL,
    consent_handle character varying,
    consent_handle_status character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    status character varying NOT NULL,
    aa_entity character varying,
    consent_id character varying,
    consent_request_purpose character varying NOT NULL,
    ca_flow_name character varying DEFAULT 'CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT'::character varying NOT NULL,
    hashed_phone_number character varying
);
COMMENT ON TABLE public.aa_consent_requests IS 'table to store all consent requests for aa data fetch';
COMMENT ON COLUMN public.aa_consent_requests.transaction_id IS 'unique transaction id generated for the consent request';
COMMENT ON COLUMN public.aa_consent_requests.consent_handle_status IS '{"proto_type":"connected_account.ConsentHandleStatus", "comment":"consent handle status"}';
COMMENT ON COLUMN public.aa_consent_requests.status IS '{"proto_type":"connected_account.ConsentRequestStatus", "comment":"consent request status"}';
CREATE TABLE public.aa_consents (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    consent_request_id uuid NOT NULL,
    consent_id character varying NOT NULL,
    actor_id character varying NOT NULL,
    consent_status character varying NOT NULL,
    start timestamp with time zone NOT NULL,
    expiry timestamp with time zone NOT NULL,
    fetch_type character varying NOT NULL,
    mode character varying NOT NULL,
    consent_types jsonb NOT NULL,
    fi_types jsonb NOT NULL,
    data_consumer jsonb NOT NULL,
    data_provider jsonb NOT NULL,
    accounts jsonb NOT NULL,
    customer_id character varying NOT NULL,
    consent_purpose character varying NOT NULL,
    data_range_from timestamp with time zone NOT NULL,
    data_range_to timestamp with time zone NOT NULL,
    data_life jsonb NOT NULL,
    frequency jsonb NOT NULL,
    data_filter jsonb,
    next_fetch_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    signature character varying NOT NULL,
    hashed_phone_number character varying,
    consent_data_refresh_status character varying DEFAULT 'CONSENT_DATA_REFRESH_STATUS_UNSPECIFIED'::character varying NOT NULL
);
COMMENT ON TABLE public.aa_consents IS 'table to store all consents for a user for aa data fetch';
COMMENT ON COLUMN public.aa_consents.consent_types IS '{"proto_type":"connected_account.ConsentTypes", "comment":"consent types taken from user"}';
COMMENT ON COLUMN public.aa_consents.fi_types IS '{"proto_type":"connected_account.FITypes", "comment":"types of FI for which consent is taken"}';
COMMENT ON COLUMN public.aa_consents.data_consumer IS '{"proto_type":"connected_account.DataConsumer", "comment":"data consumer who will fetch data from AA, in this case epifi"}';
COMMENT ON COLUMN public.aa_consents.data_provider IS '{"proto_type":"connected_account.DataProvider", "comment":"data provider who provides data, in this case AA one money"}';
COMMENT ON COLUMN public.aa_consents.accounts IS '{"proto_type":"connected_account.Accounts", "comment":"list of accounts for which consent is taken"}';
COMMENT ON COLUMN public.aa_consents.data_life IS '{"proto_type":"connected_account.DataLife", "comment":"time unit for which we can store data at our end"}';
COMMENT ON COLUMN public.aa_consents.frequency IS '{"proto_type":"connected_account.Frequency", "comment":"frequency of periodic pull of data"}';
COMMENT ON COLUMN public.aa_consents.data_filter IS '{"proto_type":"connected_account.DataFilter", "comment":"any filters applied while fetching data"}';
CREATE TABLE public.aa_data_fetch_attempts (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    consent_id character varying NOT NULL,
    transaction_id character varying NOT NULL,
    session_id character varying,
    fetch_status character varying NOT NULL,
    data_range_from timestamp with time zone NOT NULL,
    data_range_to timestamp with time zone NOT NULL,
    key_material jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    consent_reference_id uuid NOT NULL,
    initiated_by character varying DEFAULT 'DATA_FETCH_ATTEMPT_INITIATED_BY_JOB'::character varying NOT NULL,
    purpose character varying DEFAULT 'DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC'::character varying NOT NULL,
    fi_notification_info jsonb
);
COMMENT ON TABLE public.aa_data_fetch_attempts IS 'table to store all data fetch attempts made for a consent';
COMMENT ON COLUMN public.aa_data_fetch_attempts.key_material IS 'key material generated for the data fetch attempt';
COMMENT ON COLUMN public.aa_data_fetch_attempts.initiated_by IS '{"proto_type":"connected_account.enums.DataFetchAttemptInitiatedBy", "comment":"source of data fetch attempt whether user or job etc"}';
CREATE TABLE public.aa_data_process_attempts (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    attempt_id uuid NOT NULL,
    fip_id character varying NOT NULL,
    link_ref_number character varying NOT NULL,
    data_process_status character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.aa_data_process_attempts IS 'table to store data process status for each encrypted block of data fetched in one attempt';
CREATE TABLE public.aa_data_purge_requests (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    account_id character varying NOT NULL,
    txn_execution_from timestamp with time zone NOT NULL,
    txn_execution_to timestamp with time zone NOT NULL,
    data_identifiers jsonb NOT NULL,
    status character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.aa_data_purge_requests IS 'table to store and track aa data purge requests, where each row represents a batch of data to be deleted';
COMMENT ON COLUMN public.aa_data_purge_requests.data_identifiers IS '{"proto_type": "order.aa.DataPurgeIdentifiers", "comment":"stores a pre-computed list of all the identifiers to be purged as a part of the request"}';
COMMENT ON COLUMN public.aa_data_purge_requests.status IS '{"proto_type": "order.aa.DataPurgeStatus", "comment":"status for data purge request"}';
CREATE TABLE public.aa_deposit_accounts (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    aa_account_id uuid NOT NULL,
    current_balance character varying,
    currency character varying,
    exchange_rate character varying,
    balance_date timestamp with time zone,
    type character varying,
    branch character varying,
    facility character varying,
    ifsc_code character varying,
    micr_code character varying,
    opening_date timestamp with time zone,
    current_od_limit character varying,
    drawing_limit character varying,
    status character varying,
    pending jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.aa_deposit_accounts IS 'table to store deposit account info of a user';
CREATE TABLE public.aa_deposit_transactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    transaction_ref_id uuid NOT NULL,
    deposit_txn_meta jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.aa_deposit_transactions IS 'table to store transactions related to deposit account of a user';
CREATE TABLE public.aa_enriched_transactions (
    id character varying NOT NULL,
    pi_from character varying NOT NULL,
    pi_to character varying NOT NULL,
    utr character varying NOT NULL,
    amount jsonb NOT NULL,
    computed_amount bigint GENERATED ALWAYS AS (((COALESCE(((amount ->> 'units'::text))::bigint, (0)::bigint) * (100)::bigint) + ((COALESCE(((amount ->> 'nanos'::text))::bigint, (0)::bigint) * (100)::bigint) / (**********)::bigint))) STORED NOT NULL,
    payment_protocol character varying NOT NULL,
    payload jsonb,
    transaction_type character varying NOT NULL,
    remarks character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    executed_at timestamp with time zone NOT NULL,
    partner_ref_id character varying NOT NULL,
    bank character varying NOT NULL,
    aa_txn_id character varying NOT NULL,
    computed_account_id character varying GENERATED ALWAYS AS ((payload ->> 'accountId'::text)) STORED NOT NULL,
    computed_from_actor_id character varying GENERATED ALWAYS AS ((payload ->> 'fromActorId'::text)) STORED NOT NULL,
    computed_to_actor_id character varying GENERATED ALWAYS AS ((payload ->> 'toActorId'::text)) STORED NOT NULL,
    template_id character varying
);
COMMENT ON TABLE public.aa_enriched_transactions IS '{"comment":"aa_enriched_transactions stores transactions from connected account."}';
COMMENT ON COLUMN public.aa_enriched_transactions.bank IS '{"proto_type":"api.types.bank", "comment": "connected bank involved in the transaction"}';
COMMENT ON COLUMN public.aa_enriched_transactions.aa_txn_id IS '{"comment": "internal transaction id shared by account aggregator service"}';
COMMENT ON COLUMN public.aa_enriched_transactions.template_id IS '{"proto_type":"string", "comment": "template id of the smart parser used for parsing"}';
CREATE TABLE public.aa_equity_accounts (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    aa_account_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    current_value character varying
);
COMMENT ON TABLE public.aa_equity_accounts IS 'This table stores equity shares account information for all users which represents the form of part ownership of shareholders in a business venture';
COMMENT ON COLUMN public.aa_equity_accounts.id IS 'The unique identifier for each equity account.';
COMMENT ON COLUMN public.aa_equity_accounts.aa_account_id IS 'The ID of the aa_accounts table.';
COMMENT ON COLUMN public.aa_equity_accounts.created_at IS 'The timestamp when the equity account was created.';
COMMENT ON COLUMN public.aa_equity_accounts.updated_at IS 'The timestamp when the equity account was last updated.';
COMMENT ON COLUMN public.aa_equity_accounts.deleted_at IS 'The timestamp when the equity account was soft-deleted (if applicable).';
COMMENT ON COLUMN public.aa_equity_accounts.current_value IS 'The current value of investment as on date.';
CREATE TABLE public.aa_equity_holdings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    aa_account_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    isin character varying NOT NULL,
    issuer_name character varying,
    type character varying,
    units character varying,
    last_traded_price character varying,
    meta_data jsonb DEFAULT '{}'::jsonb
);
COMMENT ON TABLE public.aa_equity_holdings IS 'This table stores equity shares account holding information for each equity account which represents the form of part ownership of shareholders in a business venture';
COMMENT ON COLUMN public.aa_equity_holdings.id IS 'The unique identifier for each equity holding.';
COMMENT ON COLUMN public.aa_equity_holdings.aa_account_id IS 'The ID of the aa_accounts table.';
COMMENT ON COLUMN public.aa_equity_holdings.created_at IS 'The timestamp when the equity holding was created.';
COMMENT ON COLUMN public.aa_equity_holdings.updated_at IS 'The timestamp when the equity holding was last updated.';
COMMENT ON COLUMN public.aa_equity_holdings.deleted_at IS 'The timestamp when the equity holding was soft-deleted (if applicable).';
COMMENT ON COLUMN public.aa_equity_holdings.isin IS 'International Securities Identification Number (ISIN) uniquely identifies a security.';
COMMENT ON COLUMN public.aa_equity_holdings.issuer_name IS 'Name of party who issued the equities in which investment made';
COMMENT ON COLUMN public.aa_equity_holdings.type IS '{"proto_type":"", "comment":"represents the equity holding type"}';
COMMENT ON COLUMN public.aa_equity_holdings.units IS 'units allotted in folio till date';
COMMENT ON COLUMN public.aa_equity_holdings.last_traded_price IS 'Last trade price of security or closing price';
COMMENT ON COLUMN public.aa_equity_holdings.meta_data IS '{"proto_type":"", "comment":"meta data for the equity holding which includes: isinDescription"}';
CREATE TABLE public.aa_equity_transactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    transaction_ref_id uuid NOT NULL,
    isin character varying NOT NULL,
    type character varying,
    units character varying,
    rate character varying,
    meta_data jsonb DEFAULT '{}'::jsonb,
    aa_account_id uuid,
    transacted_at timestamp with time zone
);
COMMENT ON TABLE public.aa_equity_transactions IS 'This table stores all of equity account transactions details that have been posted in an account.';
COMMENT ON COLUMN public.aa_equity_transactions.id IS 'The unique identifier for each equity transaction.';
COMMENT ON COLUMN public.aa_equity_transactions.created_at IS 'The timestamp when the equity transaction was created.';
COMMENT ON COLUMN public.aa_equity_transactions.updated_at IS 'The timestamp when the equity transaction was last updated.';
COMMENT ON COLUMN public.aa_equity_transactions.deleted_at IS 'The timestamp when the equity transaction was soft-deleted (if applicable).';
COMMENT ON COLUMN public.aa_equity_transactions.transaction_ref_id IS 'The ID of aa_transaction table.';
COMMENT ON COLUMN public.aa_equity_transactions.isin IS 'International Securities Identification Number (ISIN) uniquely identifies a security.';
COMMENT ON COLUMN public.aa_equity_transactions.meta_data IS '{"proto_type":"", "comment":"meta data for the equity holding which includes: exchange, companyName, orderId, isinDescription"}';
CREATE TABLE public.aa_etf_accounts (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    aa_account_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    current_value jsonb DEFAULT '{}'::jsonb NOT NULL
);
COMMENT ON TABLE public.aa_etf_accounts IS 'This table stores aa etf account information for all users';
COMMENT ON COLUMN public.aa_etf_accounts.id IS 'The unique identifier for each etf account.';
COMMENT ON COLUMN public.aa_etf_accounts.aa_account_id IS 'The ID of the aa_accounts table.';
COMMENT ON COLUMN public.aa_etf_accounts.created_at IS 'The timestamp when the etf account was created.';
COMMENT ON COLUMN public.aa_etf_accounts.updated_at IS 'The timestamp when the etf account was last updated.';
COMMENT ON COLUMN public.aa_etf_accounts.deleted_at IS 'The timestamp when the etf account was soft-deleted (if applicable).';
COMMENT ON COLUMN public.aa_etf_accounts.current_value IS '{"struct_type":"pkg.money.Money", "comment": "The current value of investment as on date."}';
CREATE TABLE public.aa_etf_holdings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    aa_account_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    isin character varying NOT NULL,
    units double precision,
    nav jsonb DEFAULT '{}'::jsonb NOT NULL,
    last_nav_date timestamp with time zone,
    folio_no character varying,
    meta_data jsonb DEFAULT '{}'::jsonb
);
COMMENT ON TABLE public.aa_etf_holdings IS 'This table stores etf holdings for each etf account';
COMMENT ON COLUMN public.aa_etf_holdings.id IS 'The unique identifier for each etf holding.';
COMMENT ON COLUMN public.aa_etf_holdings.aa_account_id IS 'The ID of the aa_accounts table.';
COMMENT ON COLUMN public.aa_etf_holdings.created_at IS 'The timestamp when the etf holding was created.';
COMMENT ON COLUMN public.aa_etf_holdings.updated_at IS 'The timestamp when the etf holding was last updated.';
COMMENT ON COLUMN public.aa_etf_holdings.deleted_at IS 'The timestamp when the etf holding was soft-deleted (if applicable).';
COMMENT ON COLUMN public.aa_etf_holdings.isin IS 'International Securities Identification Number (ISIN) uniquely identifies a security.';
COMMENT ON COLUMN public.aa_etf_holdings.units IS 'No of units for a holding';
COMMENT ON COLUMN public.aa_etf_holdings.nav IS '{"struct_type":"pkg.money.Money", "comment": "Trade price of one unit of given etf holding"}';
COMMENT ON COLUMN public.aa_etf_holdings.last_nav_date IS 'Last trade price of security or closing price';
COMMENT ON COLUMN public.aa_etf_holdings.folio_no IS 'Folio No of etf holding';
COMMENT ON COLUMN public.aa_etf_holdings.meta_data IS '{"proto_type":"", "comment":"meta data for the etf holding which includes: isinDescription and narration"}';
CREATE TABLE public.aa_etf_transactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    transaction_ref_id uuid NOT NULL,
    isin character varying NOT NULL,
    type character varying,
    units double precision,
    nav jsonb DEFAULT '{}'::jsonb NOT NULL,
    amount jsonb DEFAULT '{}'::jsonb NOT NULL,
    meta_data jsonb DEFAULT '{}'::jsonb,
    aa_account_id uuid NOT NULL,
    transaction_date_time timestamp with time zone,
    broker_code character varying
);
COMMENT ON TABLE public.aa_etf_transactions IS 'This table stores all of etf account transactions details that have been posted in an account.';
COMMENT ON COLUMN public.aa_etf_transactions.id IS 'The unique identifier for each etf transaction.';
COMMENT ON COLUMN public.aa_etf_transactions.created_at IS 'The timestamp when the etf transaction was created.';
COMMENT ON COLUMN public.aa_etf_transactions.updated_at IS 'The timestamp when the etf transaction was last updated.';
COMMENT ON COLUMN public.aa_etf_transactions.deleted_at IS 'The timestamp when the etf transaction was soft-deleted (if applicable).';
COMMENT ON COLUMN public.aa_etf_transactions.transaction_ref_id IS 'The ID of aa_transaction table.';
COMMENT ON COLUMN public.aa_etf_transactions.isin IS 'International Securities Identification Number (ISIN) uniquely identifies a security.';
COMMENT ON COLUMN public.aa_etf_transactions.nav IS '{"struct_type":"pkg.money.Money", "comment": "Trade price of one unit of given etf holding"}';
COMMENT ON COLUMN public.aa_etf_transactions.amount IS '{"struct_type":"pkg.money.Money", "comment": "Total value of transaction"}';
COMMENT ON COLUMN public.aa_etf_transactions.meta_data IS '{"proto_type":"", "comment":"meta data for the etf holding which includes: isinDescription, narration"}';
CREATE TABLE public.aa_invit_accounts (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    aa_account_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    current_value jsonb DEFAULT '{}'::jsonb NOT NULL
);
COMMENT ON TABLE public.aa_invit_accounts IS 'This table stores aa invit account information for all users';
COMMENT ON COLUMN public.aa_invit_accounts.id IS 'The unique identifier for each invit account.';
COMMENT ON COLUMN public.aa_invit_accounts.aa_account_id IS 'The ID of the aa_accounts table.';
COMMENT ON COLUMN public.aa_invit_accounts.created_at IS 'The timestamp when the invit account was created.';
COMMENT ON COLUMN public.aa_invit_accounts.updated_at IS 'The timestamp when the invit account was last updated.';
COMMENT ON COLUMN public.aa_invit_accounts.deleted_at IS 'The timestamp when the invit account was soft-deleted (if applicable).';
COMMENT ON COLUMN public.aa_invit_accounts.current_value IS '{"struct_type":"pkg.money.Money", "comment": "The current value of investment as on date."}';
CREATE TABLE public.aa_invit_holdings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    aa_account_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    isin character varying NOT NULL,
    units double precision,
    metadata jsonb DEFAULT '{}'::jsonb
);
COMMENT ON TABLE public.aa_invit_holdings IS 'This table stores invit holdings for each invit account';
COMMENT ON COLUMN public.aa_invit_holdings.id IS 'The unique identifier for each invit holding.';
COMMENT ON COLUMN public.aa_invit_holdings.aa_account_id IS 'The ID of the aa_accounts table.';
COMMENT ON COLUMN public.aa_invit_holdings.created_at IS 'The timestamp when the invit holding was created.';
COMMENT ON COLUMN public.aa_invit_holdings.updated_at IS 'The timestamp when the invit holding was last updated.';
COMMENT ON COLUMN public.aa_invit_holdings.deleted_at IS 'The timestamp when the invit holding was soft-deleted (if applicable).';
COMMENT ON COLUMN public.aa_invit_holdings.isin IS 'International Securities Identification Number (ISIN) uniquely identifies a security.';
COMMENT ON COLUMN public.aa_invit_holdings.units IS 'No of units for a holding';
COMMENT ON COLUMN public.aa_invit_holdings.metadata IS '{"proto_type":"", "comment":"meta data for the invit holding which includes: isinDescription and narration"}';
CREATE TABLE public.aa_invit_transactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    transaction_ref_id uuid NOT NULL,
    aa_account_id uuid NOT NULL,
    isin character varying NOT NULL,
    transaction_date_time timestamp with time zone,
    units double precision,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.aa_invit_transactions IS 'This table stores all of invit account transactions details that have been posted in an account.';
COMMENT ON COLUMN public.aa_invit_transactions.id IS 'The unique identifier for each invit transaction.';
COMMENT ON COLUMN public.aa_invit_transactions.transaction_ref_id IS 'The ID of aa_transaction table.';
COMMENT ON COLUMN public.aa_invit_transactions.isin IS 'International Securities Identification Number (ISIN) uniquely identifies a security.';
COMMENT ON COLUMN public.aa_invit_transactions.metadata IS '{"proto_type":"", "comment":"meta data for the invit holding which includes: isinDescription, narration"}';
COMMENT ON COLUMN public.aa_invit_transactions.created_at IS 'The timestamp when the invit transaction was created.';
COMMENT ON COLUMN public.aa_invit_transactions.updated_at IS 'The timestamp when the invit transaction was last updated.';
COMMENT ON COLUMN public.aa_invit_transactions.deleted_at IS 'The timestamp when the invit transaction was soft-deleted (if applicable).';
CREATE TABLE public.aa_notifications (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    comms_msg_id character varying,
    comms_type character varying NOT NULL,
    comms_channel character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    comms_call_status character varying DEFAULT 'COMMS_CALL_STATUS_UNSPECIFIED'::character varying NOT NULL,
    meta_data jsonb
);
COMMENT ON TABLE public.aa_notifications IS 'table to store details of all user notifications (from backend)';
COMMENT ON COLUMN public.aa_notifications.comms_type IS '{"proto_type":"connected_account.enums.CommsType", "comment":"comm type of the notification"}';
COMMENT ON COLUMN public.aa_notifications.comms_channel IS '{"proto_type":"connected_account.enums.CommsChannel", "comment":"channel for the notification"}';
COMMENT ON COLUMN public.aa_notifications.comms_call_status IS '{"proto_type":"connected_account.enums.CommsCallStatus", "comment":"status of call to comms"}';
COMMENT ON COLUMN public.aa_notifications.meta_data IS '{"proto_type":"connected_account.notification.MetaData", "comment":"meta data for the notification"}';
CREATE TABLE public.aa_nps_accounts (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    aa_account_id uuid NOT NULL,
    pran_id character varying NOT NULL,
    summary jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    current_value jsonb
);
COMMENT ON TABLE public.aa_nps_accounts IS 'This table stores aa nps account information for all users';
COMMENT ON COLUMN public.aa_nps_accounts.id IS 'The unique identifier for each nps account.';
COMMENT ON COLUMN public.aa_nps_accounts.aa_account_id IS 'The ID of the aa_accounts table.';
COMMENT ON COLUMN public.aa_nps_accounts.pran_id IS 'Permanent Retirement Account Number is a unique 12 digit number that identifies those individuals who have registered themselves under the National Pension Scheme (NPS)';
COMMENT ON COLUMN public.aa_nps_accounts.summary IS 'contains summary of the corresponding NPS account';
COMMENT ON COLUMN public.aa_nps_accounts.created_at IS 'The timestamp when the nps account was created.';
COMMENT ON COLUMN public.aa_nps_accounts.updated_at IS 'The timestamp when the nps account was last updated.';
COMMENT ON COLUMN public.aa_nps_accounts.deleted_at IS 'The timestamp when the nps account was soft-deleted (if applicable).';
COMMENT ON COLUMN public.aa_nps_accounts.current_value IS '{"struct_type":"pkg.money.Money", "comment": "The current value of investment as on date."}';
CREATE TABLE public.aa_nps_holdings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    aa_account_id uuid NOT NULL,
    scheme_id character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    total_units double precision,
    metadata jsonb DEFAULT '{}'::jsonb
);
COMMENT ON TABLE public.aa_nps_holdings IS 'This table stores nps holdings for each nps account';
COMMENT ON COLUMN public.aa_nps_holdings.id IS 'The unique identifier for each nps holding.';
COMMENT ON COLUMN public.aa_nps_holdings.aa_account_id IS 'The ID of the aa_accounts table.';
COMMENT ON COLUMN public.aa_nps_holdings.scheme_id IS 'ID of scheme chosen by person for the investment.';
COMMENT ON COLUMN public.aa_nps_holdings.created_at IS 'The timestamp when the nps holding was created.';
COMMENT ON COLUMN public.aa_nps_holdings.updated_at IS 'The timestamp when the nps holding was last updated.';
COMMENT ON COLUMN public.aa_nps_holdings.deleted_at IS 'The timestamp when the nps holding was soft-deleted (if applicable).';
COMMENT ON COLUMN public.aa_nps_holdings.total_units IS 'Total number of units issued to the investor in the fund opted';
COMMENT ON COLUMN public.aa_nps_holdings.metadata IS '{"proto_type":"", "comment":"meta data for the nps holding which includes: isinDescription and narration"}';
CREATE TABLE public.aa_recurring_deposit_accounts (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    aa_account_id uuid NOT NULL,
    branch character varying,
    type character varying,
    maturity_amount character varying,
    maturity_date timestamp with time zone,
    description character varying,
    interest_payout character varying,
    interest_rate character varying,
    principal_amount character varying,
    tenure_days character varying,
    tenure_months character varying,
    tenure_years character varying,
    recurring_amount character varying,
    recurring_deposit_day character varying,
    interest_computation character varying,
    compounding_frequency character varying,
    interest_periodic_payout_amount character varying,
    interest_on_maturity character varying,
    current_value character varying,
    ifsc_code character varying,
    opening_date timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.aa_recurring_deposit_accounts IS 'table to store recurring deposit account info of a user';
CREATE TABLE public.aa_recurring_deposit_transactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    transaction_ref_id uuid NOT NULL,
    recurring_deposit_txn_meta jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.aa_recurring_deposit_transactions IS 'table to store transactions related to recurring deposit account of a user';
CREATE TABLE public.aa_reit_accounts (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    aa_account_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    current_value jsonb DEFAULT '{}'::jsonb NOT NULL
);
COMMENT ON TABLE public.aa_reit_accounts IS 'This table stores aa reit account information for all users';
COMMENT ON COLUMN public.aa_reit_accounts.id IS 'The unique identifier for each reit account.';
COMMENT ON COLUMN public.aa_reit_accounts.aa_account_id IS 'The ID of the aa_accounts table.';
COMMENT ON COLUMN public.aa_reit_accounts.created_at IS 'The timestamp when the reit account was created.';
COMMENT ON COLUMN public.aa_reit_accounts.updated_at IS 'The timestamp when the reit account was last updated.';
COMMENT ON COLUMN public.aa_reit_accounts.deleted_at IS 'The timestamp when the reit account was soft-deleted (if applicable).';
COMMENT ON COLUMN public.aa_reit_accounts.current_value IS '{"struct_type":"pkg.money.Money", "comment": "The current value of investment as on date."}';
CREATE TABLE public.aa_reit_holdings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    aa_account_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    isin character varying NOT NULL,
    units double precision,
    investment_timestamp timestamp with time zone,
    metadata jsonb DEFAULT '{}'::jsonb
);
COMMENT ON TABLE public.aa_reit_holdings IS 'This table stores reit holdings for each reit account';
COMMENT ON COLUMN public.aa_reit_holdings.id IS 'The unique identifier for each reit holding.';
COMMENT ON COLUMN public.aa_reit_holdings.aa_account_id IS 'The ID of the aa_accounts table.';
COMMENT ON COLUMN public.aa_reit_holdings.created_at IS 'The timestamp when the reit holding was created.';
COMMENT ON COLUMN public.aa_reit_holdings.updated_at IS 'The timestamp when the reit holding was last updated.';
COMMENT ON COLUMN public.aa_reit_holdings.deleted_at IS 'The timestamp when the reit holding was soft-deleted (if applicable).';
COMMENT ON COLUMN public.aa_reit_holdings.isin IS 'International Securities Identification Number (ISIN) uniquely identifies a security.';
COMMENT ON COLUMN public.aa_reit_holdings.units IS 'No of units for a holding';
COMMENT ON COLUMN public.aa_reit_holdings.investment_timestamp IS 'The timestamp when the investment was made.';
COMMENT ON COLUMN public.aa_reit_holdings.metadata IS '{"proto_type":"", "comment":"meta data for the reit holding which includes: isinDescription and narration"}';
CREATE TABLE public.aa_reit_transactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    transaction_ref_id uuid NOT NULL,
    aa_account_id uuid NOT NULL,
    isin character varying NOT NULL,
    transaction_date_time timestamp with time zone,
    units double precision,
    exchange character varying NOT NULL,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.aa_reit_transactions IS 'This table stores all of reit account transactions details that have been posted in an account.';
COMMENT ON COLUMN public.aa_reit_transactions.id IS 'The unique identifier for each reit transaction.';
COMMENT ON COLUMN public.aa_reit_transactions.transaction_ref_id IS 'The ID of aa_transaction table.';
COMMENT ON COLUMN public.aa_reit_transactions.isin IS 'International Securities Identification Number (ISIN) uniquely identifies a security.';
COMMENT ON COLUMN public.aa_reit_transactions.metadata IS '{"proto_type":"", "comment":"meta data for the reit holding which includes: isinDescription, narration"}';
COMMENT ON COLUMN public.aa_reit_transactions.created_at IS 'The timestamp when the reit transaction was created.';
COMMENT ON COLUMN public.aa_reit_transactions.updated_at IS 'The timestamp when the reit transaction was last updated.';
COMMENT ON COLUMN public.aa_reit_transactions.deleted_at IS 'The timestamp when the reit transaction was soft-deleted (if applicable).';
CREATE TABLE public.aa_term_deposit_accounts (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    aa_account_id uuid NOT NULL,
    branch character varying,
    opening_date timestamp with time zone,
    ifsc_code character varying,
    type character varying,
    maturity_amount character varying,
    maturity_date timestamp with time zone,
    description character varying,
    interest_payout character varying,
    interest_rate character varying,
    principal_amount character varying,
    tenure_days character varying,
    tenure_months character varying,
    tenure_years character varying,
    interest_computation character varying,
    compounding_frequency character varying,
    interest_periodic_payout_amount character varying,
    interest_on_maturity character varying,
    current_value character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.aa_term_deposit_accounts IS 'table to store term deposit account info of a user';
CREATE TABLE public.aa_term_deposit_transactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    transaction_ref_id uuid NOT NULL,
    term_deposit_txn_meta jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.aa_term_deposit_transactions IS 'table to store transactions related to term deposit account of a user';
CREATE TABLE public.aa_transactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    txn_id character varying NOT NULL,
    actor_id character varying NOT NULL,
    amount character varying,
    narration character varying,
    transaction_date timestamp with time zone,
    type character varying,
    mode character varying,
    reference character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    txn_instrument_type character varying NOT NULL,
    deleted_at_unix bigint DEFAULT 0 NOT NULL,
    account_reference_id uuid NOT NULL,
    derived_txn_id character varying NOT NULL,
    txn_id_source character varying DEFAULT 'TXN_ID_SOURCE_FIP'::character varying NOT NULL
);
COMMENT ON TABLE public.aa_transactions IS 'table to store all transactions of a user';
CREATE TABLE public.aa_user_bank_preferences (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    bank character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.aa_user_bank_preferences IS 'table to store all user bank preferences';
COMMENT ON COLUMN public.aa_user_bank_preferences.bank IS '{"proto_type":"types.Bank", "comment":"bank name taken from user"}';
CREATE TABLE public.aa_user_heartbeats (
    actor_id character varying NOT NULL,
    notification_status character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.aa_user_heartbeats IS 'to store system up notification status of an user';
COMMENT ON COLUMN public.aa_user_heartbeats.notification_status IS '{"proto_type":"connected_account.enums.AaUserHeartbeatNotificationStatus", "comment":"status of system up notification"}';
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
ALTER TABLE ONLY public.aa_account_column_histories
    ADD CONSTRAINT aa_account_column_histories_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_accounts
    ADD CONSTRAINT aa_accounts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_analysed_users
    ADD CONSTRAINT aa_analysed_users_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_analysis_requests
    ADD CONSTRAINT aa_analysis_requests_orch_id_key UNIQUE (orch_id);
ALTER TABLE ONLY public.aa_analysis_requests
    ADD CONSTRAINT aa_analysis_requests_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_batch_process_transactions
    ADD CONSTRAINT aa_batch_process_transactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_connection_flows
    ADD CONSTRAINT aa_connection_flows_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_consent_account_mappings
    ADD CONSTRAINT aa_consent_account_mappings_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_consent_requests
    ADD CONSTRAINT aa_consent_requests_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_consents
    ADD CONSTRAINT aa_consents_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_data_fetch_attempts
    ADD CONSTRAINT aa_data_fetch_attempts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_data_process_attempts
    ADD CONSTRAINT aa_data_process_attempts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_data_purge_requests
    ADD CONSTRAINT aa_data_purge_requests_pkey PRIMARY KEY (account_id, txn_execution_from, txn_execution_to);
ALTER TABLE ONLY public.aa_deposit_accounts
    ADD CONSTRAINT aa_deposit_accounts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_deposit_transactions
    ADD CONSTRAINT aa_deposit_transactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_enriched_transactions
    ADD CONSTRAINT aa_enriched_transactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_equity_accounts
    ADD CONSTRAINT aa_equity_accounts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_equity_holdings
    ADD CONSTRAINT aa_equity_holdings_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_equity_transactions
    ADD CONSTRAINT aa_equity_transactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_etf_accounts
    ADD CONSTRAINT aa_etf_accounts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_etf_holdings
    ADD CONSTRAINT aa_etf_holdings_aa_account_id_isin_key UNIQUE (aa_account_id, isin);
ALTER TABLE ONLY public.aa_etf_holdings
    ADD CONSTRAINT aa_etf_holdings_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_etf_transactions
    ADD CONSTRAINT aa_etf_transactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_invit_accounts
    ADD CONSTRAINT aa_invit_accounts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_invit_holdings
    ADD CONSTRAINT aa_invit_holdings_aa_account_id_isin_key UNIQUE (aa_account_id, isin);
ALTER TABLE ONLY public.aa_invit_holdings
    ADD CONSTRAINT aa_invit_holdings_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_invit_transactions
    ADD CONSTRAINT aa_invit_transactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_notifications
    ADD CONSTRAINT aa_notifications_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_nps_accounts
    ADD CONSTRAINT aa_nps_accounts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_nps_holdings
    ADD CONSTRAINT aa_nps_holdings_aa_account_id_scheme_id UNIQUE (aa_account_id, scheme_id);
ALTER TABLE ONLY public.aa_nps_holdings
    ADD CONSTRAINT aa_nps_holdings_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_recurring_deposit_accounts
    ADD CONSTRAINT aa_recurring_deposit_accounts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_recurring_deposit_transactions
    ADD CONSTRAINT aa_recurring_deposit_transactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_reit_accounts
    ADD CONSTRAINT aa_reit_accounts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_reit_holdings
    ADD CONSTRAINT aa_reit_holdings_aa_account_id_isin_key UNIQUE (aa_account_id, isin);
ALTER TABLE ONLY public.aa_reit_holdings
    ADD CONSTRAINT aa_reit_holdings_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_reit_transactions
    ADD CONSTRAINT aa_reit_transactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_term_deposit_accounts
    ADD CONSTRAINT aa_term_deposit_accounts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_term_deposit_transactions
    ADD CONSTRAINT aa_term_deposit_transactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_transactions
    ADD CONSTRAINT aa_transactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_user_bank_preferences
    ADD CONSTRAINT aa_user_bank_preferences_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.aa_user_heartbeats
    ADD CONSTRAINT aa_user_heartbeats_pkey PRIMARY KEY (actor_id);
ALTER TABLE ONLY public.aa_analysed_users
    ADD CONSTRAINT analysed_users_unq_idx_actor_id UNIQUE (actor_id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.aa_equity_holdings
    ADD CONSTRAINT unique_aa_account_id_isin UNIQUE (aa_account_id, isin);
CREATE UNIQUE INDEX aa_acc_unique_idx_actor_id_linked_acc_ref_aa_entity_del_at_unix ON public.aa_accounts USING btree (actor_id, linked_account_ref, aa_entity, deleted_at_unix);
CREATE INDEX aa_account_column_histories_idx_updated_at ON public.aa_account_column_histories USING btree (updated_at);
CREATE INDEX aa_accounts_idx_updated_at_idx ON public.aa_accounts USING btree (updated_at);
CREATE INDEX aa_accounts_linked_account_ref_unique_idx_deleted_at_unix ON public.aa_accounts USING btree (linked_account_ref, deleted_at_unix);
CREATE UNIQUE INDEX aa_batch_process_transactions_idx_fetch_process_batch_uniq ON public.aa_batch_process_transactions USING btree (fetch_attempt_id, process_attempt_id, batch_number);
CREATE INDEX aa_batch_process_transactions_idx_updated_at ON public.aa_batch_process_transactions USING btree (updated_at);
CREATE INDEX aa_connection_flows_updated_at_idx ON public.aa_connection_flows USING btree (updated_at DESC);
CREATE UNIQUE INDEX aa_consent_acc_mappings_idx_consent_ref_id_account_ref_id_unq ON public.aa_consent_account_mappings USING btree (consent_reference_id, account_reference_id);
CREATE INDEX aa_consent_account_mappings_idx_account_reference_id ON public.aa_consent_account_mappings USING btree (account_reference_id);
CREATE INDEX aa_consent_account_mappings_idx_updated_at ON public.aa_consent_account_mappings USING btree (updated_at DESC);
CREATE INDEX aa_consent_requests_idx_actor_id ON public.aa_consent_requests USING btree (actor_id);
CREATE INDEX aa_consent_requests_idx_updated_at ON public.aa_consent_requests USING btree (updated_at DESC);
CREATE UNIQUE INDEX aa_consent_requests_unique_idx_consent_handle_aa_entity ON public.aa_consent_requests USING btree (consent_handle, aa_entity);
CREATE UNIQUE INDEX aa_consent_requests_unique_idx_txn_id_aa_entity ON public.aa_consent_requests USING btree (transaction_id, aa_entity);
CREATE INDEX aa_consents_idx_actor_id ON public.aa_consents USING btree (actor_id);
CREATE UNIQUE INDEX aa_consents_idx_consent_id ON public.aa_consents USING btree (consent_id);
CREATE INDEX aa_consents_idx_customer_id ON public.aa_consents USING btree (customer_id);
CREATE INDEX aa_consents_idx_updated_at ON public.aa_consents USING btree (updated_at DESC);
CREATE UNIQUE INDEX aa_consents_unique_idx_consent_request_id ON public.aa_consents USING btree (consent_request_id);
CREATE INDEX aa_data_fetch_attempts_consent_reference_id_created_at_idx ON public.aa_data_fetch_attempts USING btree (consent_reference_id, created_at DESC);
CREATE INDEX aa_data_fetch_attempts_idx_actor_id ON public.aa_data_fetch_attempts USING btree (actor_id);
CREATE UNIQUE INDEX aa_data_fetch_attempts_idx_session_id ON public.aa_data_fetch_attempts USING btree (session_id);
CREATE UNIQUE INDEX aa_data_fetch_attempts_idx_transaction_id ON public.aa_data_fetch_attempts USING btree (transaction_id);
CREATE INDEX aa_data_fetch_attempts_idx_updated_at ON public.aa_data_fetch_attempts USING btree (updated_at DESC);
CREATE INDEX aa_data_process_attempts_idx_attempt_id ON public.aa_data_process_attempts USING btree (attempt_id);
CREATE INDEX aa_data_process_attempts_idx_updated_at ON public.aa_data_process_attempts USING btree (updated_at DESC);
CREATE UNIQUE INDEX aa_data_process_attempts_unq_idx_attempt_id_fip_id_link_ref_num ON public.aa_data_process_attempts USING btree (attempt_id, fip_id, link_ref_number);
CREATE UNIQUE INDEX aa_data_purge_requests_id_key ON public.aa_data_purge_requests USING btree (id);
CREATE INDEX aa_data_purge_requests_updated_at_idx ON public.aa_data_purge_requests USING btree (updated_at DESC);
CREATE INDEX aa_deposit_accounts_idx_updated_at_aa_deposit_accounts ON public.aa_deposit_accounts USING btree (updated_at);
CREATE UNIQUE INDEX aa_deposit_accounts_unique_idx_aa_account_id ON public.aa_deposit_accounts USING btree (aa_account_id);
CREATE INDEX aa_deposit_transactions_idx_transaction_ref_id ON public.aa_deposit_transactions USING btree (transaction_ref_id);
CREATE INDEX aa_deposit_transactions_idx_updated_at ON public.aa_deposit_transactions USING btree (updated_at);
CREATE UNIQUE INDEX aa_enriched_transactions_aa_txn_id_key ON public.aa_enriched_transactions USING btree (aa_txn_id);
CREATE INDEX aa_enriched_transactions_computed_account_id_executed_at_idx ON public.aa_enriched_transactions USING btree (computed_account_id, executed_at);
CREATE INDEX aa_enriched_transactions_computed_account_id_idx ON public.aa_enriched_transactions USING btree (computed_account_id);
CREATE INDEX aa_enriched_transactions_computed_from_actor_id_idx ON public.aa_enriched_transactions USING btree (computed_from_actor_id);
CREATE INDEX aa_enriched_transactions_computed_to_actor_id_idx ON public.aa_enriched_transactions USING btree (computed_to_actor_id);
CREATE INDEX aa_enriched_transactions_executed_at_idx ON public.aa_enriched_transactions USING btree (executed_at);
CREATE INDEX aa_enriched_transactions_pi_from_computed_account_id_idx ON public.aa_enriched_transactions USING btree (pi_from, computed_account_id);
CREATE INDEX aa_enriched_transactions_pi_to_computed_account_id_idx ON public.aa_enriched_transactions USING btree (pi_to, computed_account_id);
CREATE INDEX aa_enriched_transactions_updated_at_idx ON public.aa_enriched_transactions USING btree (updated_at DESC);
CREATE INDEX aa_enriched_transactions_utr_idx ON public.aa_enriched_transactions USING btree (utr);
CREATE UNIQUE INDEX aa_equity_accounts_idx_aa_account_id ON public.aa_equity_accounts USING btree (aa_account_id);
CREATE INDEX aa_equity_accounts_idx_updated_at ON public.aa_equity_accounts USING btree (updated_at DESC);
CREATE INDEX aa_equity_holdings_idx_updated_at ON public.aa_equity_holdings USING btree (updated_at DESC);
CREATE INDEX aa_equity_transactions_aa_account_id_isin_transacted_at_idx ON public.aa_equity_transactions USING btree (aa_account_id, isin, transacted_at DESC) WHERE (deleted_at IS NULL);
CREATE INDEX aa_equity_transactions_idx_transaction_ref_id ON public.aa_equity_transactions USING btree (transaction_ref_id);
CREATE INDEX aa_equity_transactions_idx_updated_at ON public.aa_equity_transactions USING btree (updated_at DESC);
CREATE UNIQUE INDEX aa_etf_accounts_idx_aa_account_id ON public.aa_etf_accounts USING btree (aa_account_id);
CREATE INDEX aa_etf_accounts_updated_at_idx ON public.aa_etf_accounts USING btree (updated_at DESC);
CREATE INDEX aa_etf_holdings_updated_at_idx ON public.aa_etf_holdings USING btree (updated_at DESC);
CREATE INDEX aa_etf_transactions_aa_account_id_isin_txn_date_time_idx ON public.aa_etf_transactions USING btree (aa_account_id, isin, transaction_date_time DESC) WHERE (deleted_at IS NULL);
CREATE INDEX aa_etf_transactions_transaction_ref_id_idx ON public.aa_etf_transactions USING btree (transaction_ref_id);
CREATE INDEX aa_etf_transactions_updated_at_idx ON public.aa_etf_transactions USING btree (updated_at DESC);
CREATE UNIQUE INDEX aa_invit_accounts_aa_account_id_unique_idx ON public.aa_invit_accounts USING btree (aa_account_id);
CREATE INDEX aa_invit_accounts_updated_at_idx ON public.aa_invit_accounts USING btree (updated_at DESC);
CREATE INDEX aa_invit_holdings_updated_at_idx ON public.aa_invit_holdings USING btree (updated_at DESC);
CREATE INDEX aa_invit_transactions_aa_account_id_isin_txn_date_time_idx ON public.aa_invit_transactions USING btree (aa_account_id, isin, transaction_date_time DESC) WHERE (deleted_at IS NULL);
CREATE INDEX aa_invit_transactions_transaction_ref_id_idx ON public.aa_invit_transactions USING btree (transaction_ref_id);
CREATE INDEX aa_invit_transactions_updated_at_idx ON public.aa_invit_transactions USING btree (updated_at DESC);
CREATE INDEX aa_notifications_idx_actor_id ON public.aa_notifications USING btree (actor_id);
CREATE UNIQUE INDEX aa_notifications_unique_idx_comms_msg_id ON public.aa_notifications USING btree (comms_msg_id);
CREATE INDEX aa_notifications_updated_at_idx ON public.aa_notifications USING btree (updated_at);
CREATE UNIQUE INDEX aa_nps_accounts_aa_account_id_unique_idx ON public.aa_nps_accounts USING btree (aa_account_id);
CREATE INDEX aa_nps_accounts_updated_at_idx ON public.aa_nps_accounts USING btree (updated_at DESC);
CREATE INDEX aa_nps_holdings_updated_at_idx ON public.aa_nps_holdings USING btree (updated_at DESC);
CREATE UNIQUE INDEX aa_recurring_deposit_accounts_aa_account_id_unique_idx ON public.aa_recurring_deposit_accounts USING btree (aa_account_id);
CREATE INDEX aa_recurring_deposit_accounts_updated_at ON public.aa_recurring_deposit_accounts USING btree (updated_at);
CREATE INDEX aa_recurring_deposit_transactions_idx_transaction_ref_id ON public.aa_recurring_deposit_transactions USING btree (transaction_ref_id);
CREATE INDEX aa_recurring_deposit_transactions_idx_updated_at ON public.aa_recurring_deposit_transactions USING btree (updated_at);
CREATE UNIQUE INDEX aa_reit_accounts_aa_account_id_unique_idx ON public.aa_reit_accounts USING btree (aa_account_id);
CREATE INDEX aa_reit_accounts_updated_at_idx ON public.aa_reit_accounts USING btree (updated_at DESC);
CREATE INDEX aa_reit_holdings_updated_at_idx ON public.aa_reit_holdings USING btree (updated_at DESC);
CREATE INDEX aa_reit_transactions_aa_account_id_isin_txn_date_time_idx ON public.aa_reit_transactions USING btree (aa_account_id, isin, transaction_date_time DESC) WHERE (deleted_at IS NULL);
CREATE INDEX aa_reit_transactions_transaction_ref_id_idx ON public.aa_reit_transactions USING btree (transaction_ref_id);
CREATE INDEX aa_reit_transactions_updated_at_idx ON public.aa_reit_transactions USING btree (updated_at DESC);
CREATE UNIQUE INDEX aa_term_deposit_accounts_unique_idx_aa_account_id ON public.aa_term_deposit_accounts USING btree (aa_account_id);
CREATE INDEX aa_term_deposit_idx_accounts_updated_at ON public.aa_term_deposit_accounts USING btree (updated_at);
CREATE INDEX aa_term_deposit_transactions_idx_transaction_ref_id ON public.aa_term_deposit_transactions USING btree (transaction_ref_id);
CREATE INDEX aa_term_deposit_transactions_idx_updated_at ON public.aa_term_deposit_transactions USING btree (updated_at);
CREATE UNIQUE INDEX aa_transactions_acc_unq_idx_ref_id_derived_txn_id_del_at_unix ON public.aa_transactions USING btree (account_reference_id, derived_txn_id, deleted_at_unix);
CREATE INDEX aa_transactions_idx_acc_reference_id_actor_id_transaction_date ON public.aa_transactions USING btree (account_reference_id, actor_id, transaction_date);
CREATE INDEX aa_transactions_idx_account_ref_id_deleted_at_unix ON public.aa_transactions USING btree (account_reference_id, deleted_at_unix);
CREATE INDEX aa_transactions_idx_account_reference_id_txn_id_deleted_at_unix ON public.aa_transactions USING btree (account_reference_id, txn_id, deleted_at_unix);
CREATE INDEX aa_transactions_updated_at_idx ON public.aa_transactions USING btree (updated_at);
CREATE INDEX aa_user_bank_preferences_idx_actor_id ON public.aa_user_bank_preferences USING btree (actor_id);
CREATE INDEX aa_user_bank_preferences_idx_updated_at ON public.aa_user_bank_preferences USING btree (updated_at);
CREATE UNIQUE INDEX aa_user_bank_preferences_unique_idx_actor_id_bank ON public.aa_user_bank_preferences USING btree (actor_id, bank);
CREATE INDEX aa_user_heartbeats_updated_at_idx ON public.aa_user_heartbeats USING btree (updated_at);
CREATE INDEX idx_aa_analysis_requests_updated_at ON public.aa_analysis_requests USING btree (updated_at DESC);
CREATE INDEX idx_aa_connection_flows_name_client_req_id ON public.aa_connection_flows USING btree (ca_flow_name, client_req_id);
CREATE INDEX idx_user_analyses_updated_at_desc ON public.aa_analysed_users USING btree (updated_at DESC);
CREATE UNIQUE INDEX unique_idx_acc_id_col_name_col_value_update_timestamp ON public.aa_account_column_histories USING btree (account_id, column_name, column_value, update_timestamp);
CREATE UNIQUE INDEX unq_idx_active_aa_analysis_requests ON public.aa_analysis_requests USING btree (actor_id) WHERE (completed_at IS NULL);
ALTER TABLE ONLY public.aa_etf_accounts
    ADD CONSTRAINT aa_etf_accounts_aa_account_id_fkey FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_etf_holdings
    ADD CONSTRAINT aa_etf_holdings_aa_account_id_fkey FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_etf_transactions
    ADD CONSTRAINT aa_etf_transactions_aa_account_id_fkey FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_etf_transactions
    ADD CONSTRAINT aa_etf_transactions_transaction_ref_id_fkey FOREIGN KEY (transaction_ref_id) REFERENCES public.aa_transactions(id);
ALTER TABLE ONLY public.aa_invit_accounts
    ADD CONSTRAINT aa_invit_accounts_aa_account_id_fkey FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_invit_holdings
    ADD CONSTRAINT aa_invit_holdings_aa_account_id_fkey FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_invit_transactions
    ADD CONSTRAINT aa_invit_transactions_aa_account_id_fkey FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_invit_transactions
    ADD CONSTRAINT aa_invit_transactions_transaction_ref_id_fkey FOREIGN KEY (transaction_ref_id) REFERENCES public.aa_transactions(id);
ALTER TABLE ONLY public.aa_nps_accounts
    ADD CONSTRAINT aa_nps_accounts_aa_account_id_fkey FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_nps_holdings
    ADD CONSTRAINT aa_nps_holdings_aa_account_id_fkey FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_reit_accounts
    ADD CONSTRAINT aa_reit_accounts_aa_account_id_fkey FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_reit_holdings
    ADD CONSTRAINT aa_reit_holdings_aa_account_id_fkey FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_reit_transactions
    ADD CONSTRAINT aa_reit_transactions_aa_account_id_fkey FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_reit_transactions
    ADD CONSTRAINT aa_reit_transactions_transaction_ref_id_fkey FOREIGN KEY (transaction_ref_id) REFERENCES public.aa_transactions(id);
ALTER TABLE ONLY public.aa_account_column_histories
    ADD CONSTRAINT fk_aa_account_column_histories_account_id FOREIGN KEY (account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_batch_process_transactions
    ADD CONSTRAINT fk_aa_batch_process_transactions_fetch_attempt_id FOREIGN KEY (fetch_attempt_id) REFERENCES public.aa_data_fetch_attempts(id);
ALTER TABLE ONLY public.aa_batch_process_transactions
    ADD CONSTRAINT fk_aa_batch_process_transactions_process_attempt_id FOREIGN KEY (process_attempt_id) REFERENCES public.aa_data_process_attempts(id);
ALTER TABLE ONLY public.aa_consent_account_mappings
    ADD CONSTRAINT fk_aa_consent_account_mappings_account_reference_id FOREIGN KEY (account_reference_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_consent_account_mappings
    ADD CONSTRAINT fk_aa_consent_account_mappings_consent_reference_id FOREIGN KEY (consent_reference_id) REFERENCES public.aa_consents(id);
ALTER TABLE ONLY public.aa_consents
    ADD CONSTRAINT fk_aa_consents_consent_request_id FOREIGN KEY (consent_request_id) REFERENCES public.aa_consent_requests(id);
ALTER TABLE ONLY public.aa_data_fetch_attempts
    ADD CONSTRAINT fk_aa_data_fetch_attempts_consent_id FOREIGN KEY (consent_id) REFERENCES public.aa_consents(consent_id);
ALTER TABLE ONLY public.aa_data_fetch_attempts
    ADD CONSTRAINT fk_aa_data_fetch_attempts_consent_reference_id FOREIGN KEY (consent_reference_id) REFERENCES public.aa_consents(id);
ALTER TABLE ONLY public.aa_data_process_attempts
    ADD CONSTRAINT fk_aa_data_process_attempts_attempt_id FOREIGN KEY (attempt_id) REFERENCES public.aa_data_fetch_attempts(id);
ALTER TABLE ONLY public.aa_deposit_accounts
    ADD CONSTRAINT fk_aa_deposit_accounts_aa_account_id FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_deposit_transactions
    ADD CONSTRAINT fk_aa_deposit_transactions_transaction_ref_id FOREIGN KEY (transaction_ref_id) REFERENCES public.aa_transactions(id);
ALTER TABLE ONLY public.aa_equity_accounts
    ADD CONSTRAINT fk_aa_equity_accounts_aa_account_id FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_equity_holdings
    ADD CONSTRAINT fk_aa_equity_holdings_aa_account_id FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_equity_transactions
    ADD CONSTRAINT fk_aa_equity_transactions_transaction_ref_id FOREIGN KEY (transaction_ref_id) REFERENCES public.aa_transactions(id);
ALTER TABLE ONLY public.aa_recurring_deposit_accounts
    ADD CONSTRAINT fk_aa_recurring_deposit_accounts_aa_account_id FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_recurring_deposit_transactions
    ADD CONSTRAINT fk_aa_recurring_deposit_transactions_transaction_ref_id FOREIGN KEY (transaction_ref_id) REFERENCES public.aa_transactions(id);
ALTER TABLE ONLY public.aa_term_deposit_accounts
    ADD CONSTRAINT fk_aa_term_deposit_accounts_aa_account_id FOREIGN KEY (aa_account_id) REFERENCES public.aa_accounts(id);
ALTER TABLE ONLY public.aa_term_deposit_transactions
    ADD CONSTRAINT fk_aa_term_deposit_transactions_transaction_ref_id FOREIGN KEY (transaction_ref_id) REFERENCES public.aa_transactions(id);
ALTER TABLE ONLY public.aa_transactions
    ADD CONSTRAINT fk_aa_transactions_account_reference_id FOREIGN KEY (account_reference_id) REFERENCES public.aa_accounts(id);
