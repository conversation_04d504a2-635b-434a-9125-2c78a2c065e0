/* Updating folder visibility to ONLY_WEBSITE where parent category has the same visibility for latest version in DB */
UPDATE folders SET folder_visibility='ONLY_WEBSITE' WHERE version_id=(SELECT MAX(id) from faq_versions)
AND category_id IN (SELECT id FROM categories WHERE visibility='ONLY_WEBSITE'
AND version_id=(SELECT MAX(id) from faq_versions));

/* Updating article visibility to ONLY_WEBSITE where parent folder has the same visibility for latest version in DB */
UPDATE articles SET article_visibility='ONLY_WEBSITE' WHERE version_id=(SELECT MAX(id) from faq_versions)
AND folder_id IN (SELECT id FROM folders WHERE folder_visibility='ONLY_WEBSITE'
AND version_id=(SELECT MAX(id) from faq_versions));
