-- THINKING AHEAD --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    'bb093acb-fb94-4aa4-abe6-1d85a91feba2','THINKING AHEAD', '{"displayStr": "On the {configuredDateOfMonth}th every month, put aside {depositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredDateOfMonth", "inputType": "INT_INPUT"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'DAILY_EVENT', 'FITTT',
    'AUTO_SAVE', '{"condition": "configuredDateOfMonth == getDateFromDay(day)", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '1', '**********',
    'RULE_STATE_ACTIVE'
  );

-- CONSISTENCY IS KEY --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    '7b251ad6-a653-4c8a-88fa-a3714b45b480','CONSISTENCY IS KEY', '{"displayStr": "On {configuredDayOfWeek} every week, put aside {depositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredDayOfWeek", "inputType": "STRING_INPUT"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'DAILY_EVENT', 'FITTT',
    'AUTO_SAVE', '{"condition": "configuredDayOfWeek == getWeekdayFromDay(day)", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '1', '**********',
    'RULE_STATE_ACTIVE'
  );

--- Rule Display Entries --
INSERT INTO rule_display_infos (category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color) values
    ('AUTO_SAVE', 'THINKING AHEAD', 'THINKING AHEAD', 'Thinking Ahead', 'On the <u>subscribedIntVal</u>th every month, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/ThinkingAhead_Home.png','On the <u>defaultIntVal</u>th every month, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/ThinkingAhead.png','#F9D0D0','#BF7D7D'),
    ('AUTO_SAVE', 'CONSISTENCY IS KEY', 'CONSISTENCY IS KEY', 'Consistency is Key', 'On <u>subscribedStrVal</u> every week, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ConsistencyIsKey_Home.png', 'On <u>defaultStrVal</u> every week, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ConsistencyIsKey.png', '#D9F2CB', '#87BA6B');
