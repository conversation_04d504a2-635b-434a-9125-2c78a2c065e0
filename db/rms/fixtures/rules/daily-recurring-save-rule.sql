-- DAILY DEEDS --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state, weight
)
VALUES
  (
    '43dd9b50-786b-47c0-bf0b-61401bc8da42','DAILY DEEDS', '{"displayStr": "Every day, put aside {depositAmount} into {depositAccountId}", "inputParams": [{"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'DAILY_EVENT', 'FITTT',
    'AUTO_SAVE', '{"condition": "true"}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '1', '**********',
    'RULE_STATE_ACTIVE', 65
  );

--- Rule Display Entries --
INSERT INTO rule_display_infos (name, category, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color) values
    ('DAILY DEEDS', 'AUTO_SAVE', 'DAILY DEEDS', 'Daily Deeds', 'At the end of every day, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/DailyDeeds_Home2.png','At the end of every day, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/DailyDeedsV2.png','#DEEEF2','#7FBECE');
