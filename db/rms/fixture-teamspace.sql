-- collections defines a set of similar kind of rules --
INSERT INTO public.collections (id, state, weight, display_info, child_ids, created_at, updated_at, deleted_at, is_featured, type, allowed_user_groups, version_support_info) VALUES
  	 ('1e9ba29f-6d70-4fbc-8728-e20b30158dab', 'COLLECTION_ACTIVE', 10, '{"name":{"text":"Invest regularly"},"cardDisplayInfo":{"imgUrl":"https://epifi-icons.pointz.in/clock_hybrid_collection.png", "homePageImgUrl":"https://epifi-icons.pointz.in/shopping_bag_with_eclipse.png","bgColor":"#DEEEF2"},"description":{"text":"Invest using Daily, Weekly, Monthly SIPs","fontColor":"#333333"}}', '{"ruleIds":{"list":["d157d5c9-8b32-4f1b-9185-a60a8263ea25","********-f4d1-421f-9c1c-1a7f65612e66","0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0","43dd9b50-786b-47c0-bf0b-61401bc8da42", "bb093acb-fb94-4aa4-abe6-1d85a91feba2", "7b251ad6-a653-4c8a-88fa-a3714b45b480"]}}', '2023-03-16 13:05:25.205128 +00:00', '2023-03-16 13:05:25.205128 +00:00', null, true, 'COLLECTION_TYPE_HYBRID', null, null),
	 ('85adfaac-59fd-437c-acef-74c0230016f7', 'COLLECTION_ACTIVE', 20, '{"name":{"text":"Save when you shop"},"cardDisplayInfo":{"imgUrl":"https://epifi-icons.pointz.in/shopping_bag_hybrid_col.png","bgColor":"#DEEEF2"},"description":{"text":"Invest every-time you spend online","fontColor":"#333333"}}', '{"ruleIds":{"list":["e52fb0c5-8a07-4f59-a70b-18c6f2669163","bc925bcb-ff12-43f9-8396-b6383242d939"]}}', '2023-03-16 13:05:25.409756 +00:00', '2023-03-16 13:05:25.409756 +00:00', null, true, 'COLLECTION_TYPE_HYBRID', null, null),
	 ('2bda17b4-0525-4423-a379-48661b973140', 'COLLECTION_ACTIVE', 3, '{"tag": {"id": "football_fever_save_tag", "text": "NEW RULES LAUNCHED", "bgColor": "#809287BD"}, "name": {"text": "Football Fever", "font_color": "#333333"}, "dynamicTag": {"tag": {"id": "football_fever_save_tag", "text": "NEW RULES LAUNCHED", "bg_color": "#809287BD"}, "expiry": "2022-03-16T13:15:25Z"}, "description": {"text": "Set aside money \nevery football match", "fontColor": "#333333"}, "cardDisplayInfo": {"imgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/FootballFever_Explore.png", "bgColor": "#CDC6E8", "homePageImgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/FootballFever_Home.png"}}', '{"tagIds": {"list": ["EPL2021", "FIFA2022"]}}', '2021-12-10 07:59:55.425396 +00:00', '2021-12-10 07:59:55.425396 +00:00', null, false, 'COLLECTION_TYPE_AUTO_SAVE', null, null),
	 ('d67c8498-26a6-4cc1-a089-42a860eb0cf0', 'COLLECTION_ACTIVE', 10, '{"name": {"text": "Timely Payments"}, "dynamicTag": {"tag": {"text": "NEW", "bg_color": "#80478295"}, "expiry": "2022-09-12T10:30:07Z"}, "description": {"text": "Pay rent, EMIs, send money home, and more", "fontColor": "#333333"}, "cardDisplayInfo": {"imgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/TimelyPayments_Explore.png", "bgColor": "#DEEEF2", "homePageImgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/TimelyPayments_Home.png"}}', '{"ruleIds": {"list": ["95d7aa89-f081-4a07-90bc-7950775ce1b4", "1c44b98f-0b75-4568-a06d-7474b00e4c55", "c61fe3d4-80ff-4908-bc56-9ea042d3c046"]}}', '2022-08-19 10:30:03.240144 +00:00', '2022-08-19 10:30:03.240144 +00:00', null, true, 'COLLECTION_TYPE_AUTO_PAY', null, '{"minSupportedIosAppVersion": 255, "minSupportedAndroidAppVersion": 169}'),
	 ('2bebb285-8883-40e3-8839-f09c8e87a89b', 'COLLECTION_ACTIVE', 6, '{"tag": {"id": "systematic_savings_tag", "text": "NEW RULES LAUNCHED", "bgColor": "#809287BD"}, "name": {"text": "Systematic Savings", "font_color": "#333333"}, "dynamicTag": {"tag": {"id": "systematic_savings_tag", "text": "NEW RULES LAUNCHED", "bg_color": "#809287BD"}, "expiry": "2022-03-16T13:15:25Z"}, "description": {"text": "Set aside money \non a regular basis", "fontColor": "#333333"}, "cardDisplayInfo": {"imgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/SystematicSavings_Explore.png", "bgColor": "#CDC6E8", "homePageImgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/SystematicSavings_Home.png"}}', '{"ruleIds":{"list":["bb093acb-fb94-4aa4-abe6-1d85a91feba2","7b251ad6-a653-4c8a-88fa-a3714b45b480","43dd9b50-786b-47c0-bf0b-61401bc8da42"]}}', '2021-12-10 07:59:55.425396 +00:00', '2021-12-10 07:59:55.425396 +00:00', null, true, 'COLLECTION_TYPE_AUTO_SAVE', null, '{"minSupportedIosAppVersion": 209, "minSupportedAndroidAppVersion": 153}'),
	 ('d682c912-9e9d-46e9-83e5-43fb4ad73aa7', 'COLLECTION_ACTIVE', 4, '{"tag": {"id": "digital_wellness_save_tag", "text": "NEW RULES LAUNCHED", "bgColor": "#804F71AB"}, "name": {"text": "Digital Wellness", "font_color": "#333333"}, "dynamicTag": {"tag": {"id": "digital_wellness_save_tag", "text": "NEW RULES LAUNCHED", "bg_color": "#804F71AB"}, "expiry": "2022-03-16T13:15:25Z"}, "description": {"text": "Save when \nyou reduce screen time", "fontColor": "#333333"}, "cardDisplayInfo": {"imgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/DigitalWellness_Explore.png", "bgColor": "#D1DAF1", "homePageImgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/DigitalWellness_Home.png"}}', '{"ruleIds":{"list":["046b769b-c6e9-4edb-9d7e-0b20f06ad50d"]}}', '2021-12-10 07:59:55.425396 +00:00', '2021-12-10 07:59:55.425396 +00:00', null, false, 'COLLECTION_TYPE_AUTO_SAVE', null, null),
	 ('c8e66cbe-3c4a-4ea0-9686-5ccf9ba8cbe2', 'COLLECTION_ACTIVE', 1, '{"tag": {"id": "systematic_investments_tag", "text": "NEW RULES LAUNCHED", "bgColor": "#805D7D4C"}, "name": {"text": "SIP", "font_color": "#333333"}, "dynamicTag": {"tag": {"id": "systematic_investments_tag", "text": "NEW RULES", "bg_color": "#805D7D4C"}, "expiry": "2022-03-16T13:15:25Z"}, "description": {"text": "SIP for your Mutual Fund portfolio", "fontColor": "#333333"}, "cardDisplayInfo": {"imgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/TimelyPayments_Explore.png", "bgColor": "#D9F2CC", "homePageImgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/RetailTherapy_Home.png"}}', '{"ruleIds":{"list":["0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0", "********-f4d1-421f-9c1c-1a7f65612e66", "d157d5c9-8b32-4f1b-9185-a60a8263ea25"]}}', '2022-02-07 14:29:45.092699 +00:00', '2022-02-07 14:29:45.092699 +00:00', null, true, 'COLLECTION_TYPE_AUTO_INVEST', null, null),
	 ('c61c89bc-1828-40e4-a734-e29e10a8ad36', 'COLLECTION_ACTIVE', 7, '{"tag": {"id": "retail_therapy_save_tag", "text": "NEW RULES LAUNCHED", "bgColor": "#809E5A57"}, "name": {"text": "Retail Therapy", "font_color": "#333333"}, "dynamicTag": {"tag": {"id": "retail_therapy_save_tag", "text": "NEW RULES LAUNCHED", "bg_color": "#809E5A57"}, "expiry": "2022-03-16T13:15:25Z"}, "description": {"text": "Set aside money \nwhenever you shop", "fontColor": "#333333"}, "cardDisplayInfo": {"imgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/RetailTherapy_Explore.png", "bgColor": "#FAD0D0", "homePageImgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/RetailTherapy_Home.png"}}', '{"ruleIds":{"list":["79bf2744-5781-4667-8b7f-7056a1e34a9c","bc925bcb-ff12-43f9-8396-b6383242d939","e52fb0c5-8a07-4f59-a70b-18c6f2669163", "78d18f6c-03bf-499a-aed7-f49eef104ba8"]}}', '2021-12-10 07:59:55.425396 +00:00', '2021-12-10 07:59:55.425396 +00:00', null, false, 'COLLECTION_TYPE_AUTO_SAVE', null, null),
	 ('9544aeac-697e-4c61-b3f9-78f9d547d280', 'COLLECTION_ACTIVE', 8, '{"tag": {"id": "cricket_craze_save_tag", "text": "NEW RULES LAUNCHED", "bgColor": "#80CDA428"}, "name": {"text": "Cricket Craze", "font_color": "#333333"}, "dynamicTag": {"tag": {"id": "cricket_craze_save_tag", "text": "NEW RULES LAUNCHED", "bg_color": "#80CDA428"}, "expiry": "2022-03-16T13:15:25Z"}, "description": {"text": "Set aside money \nevery cricket match", "fontColor": "#333333"}, "cardDisplayInfo": {"imgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/CricketCraze_Explore.png", "bgColor": "#F4E7BF", "homePageImgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/CricketCraze_Home.png"}}', '{"tagIds": {"list": ["wplt20_2023", "iplt20_2023", "asiacup_2023", "icc_wc_2023"]}}', '2021-12-10 07:59:55.425396 +00:00', '2022-10-20 08:49:54.546488 +00:00', null, false, 'COLLECTION_TYPE_AUTO_SAVE', null, '{"minSupportedIosAppVersion": 255, "minSupportedAndroidAppVersion": 169}'),
	 ('54a44408-62cb-4914-aae8-c3026bb4a3a4', 'COLLECTION_ACTIVE', 1, '{ "name":{ "text":"Shark Tank" }, "cardDisplayInfo":{ "imgUrl":"https://epifi-icons.pointz.in/fittt-images/collections/shark-tank-explore.png", "homePageImgUrl":"https://epifi-icons.pointz.in/fittt-images/collections/shark-tank-home.png", "bgColor":"#DEEEF2" }, "description":{ "text":"Build wealth every time a Shark offers a deal", "fontColor":"" }, "disclaimer":{ "text":"Fi: Official Money Partner on Shark Tank India Season 2", "fontColor":"#606265" } }', '{"ruleIds":{"list":["e2535f52-1ac1-41cb-ab94-e3bada5746ed","b9f19ba9-ab94-4bf7-85c3-38f1f0ad0b6c", "de166023-fc3f-4d99-b7e2-e3ec1f1daa42", "6b0a69a5-ca57-47a3-b9c1-75e4cc15f3d7", "9d4a2ac0-c9ad-4d5c-99b3-9c68d35c52ea", "dbc2347b-3068-412e-b38f-de42f2817a5e", "a8c118a5-5aa3-4c00-8e79-90c45d8fed55"]}}', '2023-01-18 17:48:36.204203 +00:00', '2023-01-18 17:48:36.204203 +00:00', null, true, 'COLLECTION_TYPE_AUTO_INVEST', null, null);

-- rule definitions for different types --
INSERT INTO public.rules (id, name, description, event_type, category, client, condition, actions, created_at, updated_at, deleted_at, max_subscriptions_per_actor, max_subscriptions, default_subscription_expiry_data, state, allowed_user_groups, weight, subscriptions_aggregation_type, version_support_info, aggregation_info, rule_type_for_special_handling) VALUES
 	('0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 'MONTHLY SIP', '{"displayStr": "Make an investment in {mutualFundVal} for {purchaseAmount} on {configuredDateOfMonth} of every month", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}, {"name": "configuredDateOfMonth", "inputType": "INT_INPUT"}]}', 'DAILY_EVENT', 'AUTO_INVEST', 'FITTT', '{"condition": "configuredDateOfMonth == getDateFromDay(day)", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}', '{"actionArr": [{"data": {}, "type": "PURCHASE_MUTUAL_FUND"}]}', '2022-01-23 05:02:54.734284 +00:00', '2022-06-27 06:00:26.745092 +00:00', null, **********, **********, null, 'RULE_STATE_ACTIVE', null, 1, 'AGGREGATE_ON_RULE', null, null, 'RULE_TYPE_AUTO_INVEST_MONTHLY'),
	('95d7aa89-f081-4a07-90bc-7950775ce1b4', 'ONE-TIME PAYMENT', '{"displayStr": "Send {recurringPaymentInfo} {paymentAmount} on {date}", "input_params": [{"name": "recurringPaymentInfo", "input_type": "PAYMENT_RECIPIENT"}, {"name": "paymentAmount", "inputType": "MONEY"}, {"name": "date", "inputType": "VALID_FROM_DATE"}]}', 'DAILY_EVENT', 'AUTO_PAY', 'FITTT', '{"condition": "isCurrentDate(date)", "valuePath": {}}', '{"actionArr": [{"data": {}, "type": "PAYMENT"}]}', '2022-08-19 10:27:58.899679 +00:00', '2023-06-07 05:40:10.503009 +00:00', null, **********, **********, null, 'RULE_STATE_ACTIVE', '{INTERNAL}', 90, 'AGGREGATE_ON_RULE', '{"minSupportedIosAppVersion": 255, "minSupportedAndroidAppVersion": 169}', null, 'RULE_TYPE_AUTO_PAY_ONE_TIME'),
	('d157d5c9-8b32-4f1b-9185-a60a8263ea25', 'DAILY SIP', '{"displayStr": "Make an investment in {mutualFundVal} for {purchaseAmount} everyday", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}]}', 'DAILY_EVENT', 'AUTO_INVEST', 'FITTT', '{"condition": "currentWeekdayOneOf(day, \"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\",\"Sunday\")", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}', '{"actionArr": [{"data": {}, "type": "PURCHASE_MUTUAL_FUND"}]}', '2022-01-23 05:02:54.734284 +00:00', '2022-09-29 08:27:39.490075 +00:00', null, **********, **********, null, 'RULE_STATE_ACTIVE', null, 1, 'AGGREGATE_ON_RULE', null, null, 'RULE_TYPE_AUTO_INVEST_DAILY'),
	('c61fe3d4-80ff-4908-bc56-9ea042d3c046', 'MAINTAIN BALANCE', '{"displayStr": "When my salary is credited to Fi, send {paymentAmount} to {recurringPaymentInfo}", "input_params": [{"name": "paymentAmount", "inputType": "MONEY"}, {"name": "recurringPaymentInfo", "input_type": "PAYMENT_RECIPIENT"}]}', 'SALARY_DETECTED_EVENT', 'AUTO_PAY', 'FITTT', '{"condition": "true", "valuePath": {}}', '{"actionArr": [{"data": {}, "type": "PAYMENT"}]}', '2022-08-22 10:38:27.019461 +00:00', '2022-08-22 10:38:27.019461 +00:00', null, **********, **********, null, 'RULE_STATE_ACTIVE', '{INTERNAL}', 100, 'AGGREGATE_ON_RULE', '{"minSupportedIosAppVersion": 255, "minSupportedAndroidAppVersion": 169}', null, 'RULE_TYPE_UNSPECIFIED'),
	('43dd9b50-786b-47c0-bf0b-61401bc8da42', 'DAILY DEEDS', '{"displayStr": "Put aside {depositAmount} into {depositAccountId} everyday", "inputParams": [{"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}', 'DAILY_EVENT', 'AUTO_SAVE', 'FITTT', '{"condition": "true"}', '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}', '2021-11-30 05:02:40.275206 +00:00', '2023-02-07 10:12:52.950533 +00:00', null, **********, **********, null, 'RULE_STATE_ACTIVE', null, 70, 'AGGREGATE_ON_RULE', null, null, 'RULE_TYPE_AUTO_SAVE_DAILY_RULE'),
	('d81b1323-0700-4eaa-adad-a72b5effeaf1', 'CATEGORY SPENDS REMINDER', '{"displayStr": "Remind me when I spend more than {configuredAmount} on {configuredCategory} in a {configuredDuration}", "inputParams": [{"name": "configuredDuration", "inputType": "DURATION"}, {"name": "configuredAmount", "inputType": "MONEY"}, {"name": "configuredCategory", "inputType": "STRING_INPUT"}]}', 'CATEGORY_SPENDS_REMINDER', 'REMINDER', 'BUDGETING', '{"condition": "configuredAmount <= amount", "valuePath": {"amount": {"path": ["Data", "ReminderEvent", "Val", "TxnAmount"]}}}', '{"actionArr": [{"data": {}, "type": "ACTION_TYPE_REMINDER"}]}', '2023-03-28 08:25:04.557971 +00:00', '2023-03-28 08:25:04.557971 +00:00', null, **********, **********, null, 'RULE_STATE_ACTIVE', null, 1, 'AGGREGATE_ON_RULE', null, null, 'RULE_TYPE_CATEGORY_SPENDS_REMINDER'),
	('e31c6513-cadd-4e8a-8eb4-2ec696a7be75', 'PRE-EMPTIVE NOTIFICATION', null, 'DAILY_EVENT', 'NOTIFY', 'FITTT', '{"condition": "shouldSendPreEmptiveNotification(1, 3)"}', '{"actionArr": [{"data": {}, "type": "NOTIFY"}]}', '2022-07-12 08:12:55.992798 +00:00', '2022-07-12 08:12:55.992798 +00:00', null, 1, **********, null, 'RULE_STATE_ACTIVE', null, 1, 'AGGREGATE_ON_RULE', null, null, 'RULE_TYPE_PRE_EMPTIVE_NOTIFICATIONS'),
	('1c44b98f-0b75-4568-a06d-7474b00e4c55', 'RECURRING PAYMENT', '{"displayStr": "Send {recurringPaymentInfo} {paymentAmount} {frequency} starting from {startDate} till {endDate}", "input_params": [{"name": "recurringPaymentInfo", "input_type": "PAYMENT_RECIPIENT"}, {"name": "paymentAmount", "inputType": "MONEY"}, {"name": "frequency", "inputType": "FREQUENCY"}, {"name": "startDate", "inputType": "VALID_FROM_DATE"}, {"name": "endDate", "inputType": "VALID_TILL_DATE"}]}', 'DAILY_EVENT', 'AUTO_PAY', 'FITTT', '{"condition": "shouldExecuteBasedOnFrequency(startDate, endDate, frequency)", "valuePath": {}}', '{"actionArr": [{"data": {}, "type": "PAYMENT"}]}', '2022-08-19 10:27:58.533411 +00:00', '2023-06-07 05:40:10.474379 +00:00', null, **********, **********, null, 'RULE_STATE_ACTIVE', null, 100, 'AGGREGATE_ON_RULE', '{"minSupportedIosAppVersion": 255, "minSupportedAndroidAppVersion": 169}', null, 'RULE_TYPE_AUTO_PAY_RECURRING'),
	('bb093acb-fb94-4aa4-abe6-1d85a91feba2', 'THINKING AHEAD', '{"displayStr": "On {configuredDateOfMonth} of every month, put aside {depositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredDateOfMonth", "inputType": "INT_INPUT"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}', 'DAILY_EVENT', 'AUTO_SAVE', 'FITTT', '{"condition": "configuredDateOfMonth == getDateFromDay(day)", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}', '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}', '2021-10-07 07:19:25.315164 +00:00', '2023-06-23 07:11:07.934739 +00:00', '2021-10-07 07:19:25.315164 +00:00', **********, **********, null, 'RULE_STATE_ACTIVE', null, 1, 'AGGREGATE_ON_RULE', null, null, 'RULE_TYPE_AUTO_SAVE_MONTHLY_RULE'),
	('********-f4d1-421f-9c1c-1a7f65612e66', 'WEEKLY SIP', '{"displayStr": "Make an investment in {mutualFundVal} for {purchaseAmount} on {configuredDayOfWeek} every week", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}, {"name": "configuredDayOfWeek", "inputType": "STRING_INPUT"}]}', 'DAILY_EVENT', 'AUTO_INVEST', 'FITTT', '{"condition": "configuredDayOfWeek == getWeekdayFromDay(day)", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}', '{"actionArr": [{"data": {}, "type": "PURCHASE_MUTUAL_FUND"}]}', '2022-01-23 05:02:54.734284 +00:00', '2022-06-27 06:01:45.552497 +00:00', null, **********, **********, null, 'RULE_STATE_ACTIVE', null, 1, 'AGGREGATE_ON_RULE', null, null, 'RULE_TYPE_AUTO_INVEST_WEEKLY'),
	('78d18f6c-03bf-499a-aed7-f49eef104ba8', 'KEEP THE CHANGE', '{"display_str": "When I spend with Fi, round-up to the next {configuredRoundAmount} and save the difference in {depositAccountId}", "input_params": [{"name": "configuredRoundAmount", "input_type": "MONEY"}, {"name": "depositAccountId", "input_type": "SMART_DEPOSIT"}]}', 'PAYMENT', 'AUTO_SAVE', 'FITTT', '{"condition": "paymentAmount.Units >= 50", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}}', '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "calculateChangeAmountV2(paymentAmount, configuredRoundAmount)"}]}, "type": "DEPOSIT"}]}', '2021-12-27 11:35:45.350440 +00:00', '2022-10-17 16:22:50.613821 +00:00', null, 1, **********, null, 'RULE_STATE_ACTIVE', null, 70, 'AGGREGATE_ON_RULE', null, null, 'RULE_TYPE_UNSPECIFIED'),
	('bc925bcb-ff12-43f9-8396-b6383242d939', 'DON''T SHOP TILL YOU DROP', '{"displayStr": "When I order from {configuredMerchant} with Fi, put aside {depositAmount} in {depositAccountId}", "inputParams": [{"name": "configuredMerchant", "inputType": "MERCHANT"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}', 'PAYMENT', 'AUTO_SAVE', 'FITTT', '{"condition": "prefixMatch(getMerchantNameFromOrder(order), configuredMerchant)", "valuePath": {"order": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order"]}}}', '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}', '2021-08-25 08:24:30.858109 +00:00', '2021-08-25 08:24:30.858109 +00:00', null, 6, **********, null, 'RULE_STATE_ACTIVE', null, 1, 'AGGREGATE_ON_RULE', null, null, 'RULE_TYPE_UNSPECIFIED'),
	('b86d96a3-d0a8-4558-a180-d64c4d875a1e', 'CREDIT CARD DUE DATE REMINDER', '{"displayStr": "Remind me to pay my 5x credit card bill on the  {configuredDateOfMonth} of every month", "inputParams": [{"name": "configuredDateOfMonth", "inputType": "INT_INPUT"}]}', 'DAILY_EVENT', 'REMINDER', 'BUDGETING', '{"condition": "configuredDateOfMonth == getDateFromDay(day) && getCreditCardOutstandingBalance() > 0", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}', '{"actionArr": [{"data": {}, "type": "ACTION_TYPE_REMINDER"}]}', '2023-03-30 08:46:02.109245 +00:00', '2023-03-30 08:46:02.109245 +00:00', null, 1, **********, null, 'RULE_STATE_ACTIVE', null, 1, 'AGGREGATE_ON_RULE', null, null, 'RULE_TYPE_CREDIT_CARD_DUE_DATE_REMINDER'),
	('d889238f-a21b-4bda-b51d-155075071119', 'AMOUNT SPENDS REMINDER', '{"displayStr": "Remind me if I spend more than {configuredAmount} in {configuredDuration}", "inputParams": [{"name": "configuredDuration", "inputType": "DURATION"}, {"name": "configuredAmount", "inputType": "MONEY"}]}', 'AMOUNT_SPENDS_REMINDER', 'REMINDER', 'BUDGETING', '{"condition": "configuredAmount <= amount", "valuePath": {"amount": {"path": ["Data", "ReminderEvent", "Val", "TxnAmount"]}}}', '{"actionArr": [{"data": {}, "type": "ACTION_TYPE_REMINDER"}]}', '2023-04-03 11:53:58.887683 +00:00', '2023-04-03 11:53:58.887683 +00:00', null, 1, **********, null, 'RULE_STATE_ACTIVE', null, 1, 'AGGREGATE_ON_RULE', null, null, 'RULE_TYPE_AMOUNT_SPENDS_REMINDER'),
	('7b251ad6-a653-4c8a-88fa-a3714b45b480', 'CONSISTENCY IS KEY', '{"displayStr": "On {configuredDayOfWeek} every week, put aside {depositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredDayOfWeek", "inputType": "STRING_INPUT"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}', 'DAILY_EVENT', 'AUTO_SAVE', 'FITTT', '{"condition": "configuredDayOfWeek == getWeekdayFromDay(day)", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}', '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}', '2021-10-07 07:19:25.315164 +00:00', '2022-11-28 13:24:42.967673 +00:00', '2021-10-07 07:19:25.315164 +00:00', 100, **********, null, 'RULE_STATE_ACTIVE', null, 1, 'AGGREGATE_ON_RULE', null, null, 'RULE_TYPE_AUTO_SAVE_WEEKLY_RULE'),
	('e52fb0c5-8a07-4f59-a70b-18c6f2669163', 'TIP YOURSELF', '{"display_str": "When I make a payment, round up to the nearest {configuredRoundAmount} and set aside the difference in {depositAccountId}", "input_params": [{"name": "configuredRoundAmount", "input_type": 8}, {"name": "depositAccountId", "input_type": 7}]}', 'PAYMENT', 'AUTO_SAVE', 'FITTT', '{"condition": "paymentAmount >= 50", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount", "Units"]}}}', '{"actionArr": [{"data": {"expressions": [{"var_name": "depositAmount", "expression": "differenceFromNearestMultiple(paymentAmount, configuredRoundAmount)"}]}, "type": "DEPOSIT"}]}', '2021-08-25 08:24:30.858109 +00:00', '2023-05-26 07:40:45.356186 +00:00', null, 1, **********, null, 'RULE_STATE_ACTIVE', null, 1, 'AGGREGATE_ON_RULE', null, null, 'RULE_TYPE_UNSPECIFIED');

-- param value selector ctas associated with rules --
INSERT INTO public.param_value_selector_ctas (id, param_value_type, cta, state, rule_id, created_at, updated_at, deleted_at) VALUES
	 ('b07b54e1-eba4-4601-833b-14aebf06e504', 'MONEY', '{"customAmtCta": {"textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}', 'CTA_STATE_ACTIVE', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', '2022-04-28 10:04:00.628036 +00:00', '2022-04-28 10:04:00.628036 +00:00', null),
	 ('3b90ef1c-96dc-4ca9-8bf1-820e8ee49cce', 'MUTUAL_FUND', '{"mfSelectorCta": {"text": "Select a mutual fund", "iconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}', 'CTA_STATE_ACTIVE', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', '2022-01-23 05:02:54.734284 +00:00', '2022-01-23 05:02:54.734284 +00:00', null),
	 ('f347914c-b1a7-4b89-b11f-c25a20b5b505', 'PAYMENT_RECIPIENT', '{"recurringPaymentCta": {"text": "Select a payee", "iconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/plus.png", "editIconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/pencil.png", "payeeBgColor": "#2A496F"}}', 'CTA_STATE_ACTIVE', '95d7aa89-f081-4a07-90bc-7950775ce1b4', '2022-08-19 10:27:58.969027 +00:00', '2022-08-19 10:27:58.969027 +00:00', null),
	 ('63460c74-45f6-40aa-a975-af0dfcc16cec', 'MONEY', '{"customAmtCta": {"type": "CUSTOM_AMOUNT_SELECTOR", "iconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/plus.png", "constraint": {"maxAmount": {"units": "200000", "currencyCode": "INR"}, "minAmount": {"units": "100", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "editIconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/pencil.png", "currencyIconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/onetime_payment_selector_currency.png", "textForRuleDesc": "amount", "textForPossibleValues": "Add Amount"}}', 'CTA_STATE_ACTIVE', '95d7aa89-f081-4a07-90bc-7950775ce1b4', '2022-08-19 10:27:58.969027 +00:00', '2022-08-19 10:27:58.969027 +00:00', null),
	 ('94cc0bdf-f7c4-4aa4-bf8e-c451e8be28e3', 'MONEY', '{"customAmtCta": {"textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}', 'CTA_STATE_ACTIVE', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25', '2022-04-28 10:04:00.628036 +00:00', '2022-04-28 10:04:00.628036 +00:00', null),
	 ('9fd4a047-2b12-489a-8dcf-154eca04c301', 'MUTUAL_FUND', '{"mfSelectorCta": {"text": "Select a mutual fund", "iconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}', 'CTA_STATE_ACTIVE', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25', '2022-01-23 05:02:54.734284 +00:00', '2022-01-23 05:02:54.734284 +00:00', null),
	 ('c34bdc09-c7d3-4829-9bfc-19c178a8ab18', 'PAYMENT_RECIPIENT', '{"recurringPaymentCta": {"text": "Select a payee", "iconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/plus.png", "editIconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/pencil.png", "payeeBgColor": "#AC7C44"}}', 'CTA_STATE_ACTIVE', 'c61fe3d4-80ff-4908-bc56-9ea042d3c046', '2022-08-22 10:38:27.154445 +00:00', '2022-08-22 10:38:27.154445 +00:00', null),
	 ('7320a075-0916-4d16-9c79-ce7dca92537c', 'MONEY', '{"customAmtCta": {"type": "CUSTOM_AMOUNT_SELECTOR", "iconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/plus.png", "constraint": {"maxAmount": {"units": "100000", "currencyCode": "INR"}, "minAmount": {"units": "1000", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "editIconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/pencil.png", "currencyIconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/recurring_payment_selector_currency.png", "textForRuleDesc": "amount", "textForPossibleValues": "Add Amount"}}', 'CTA_STATE_ACTIVE', 'c61fe3d4-80ff-4908-bc56-9ea042d3c046', '2022-08-22 10:38:27.154445 +00:00', '2022-08-22 10:38:27.154445 +00:00', null),
	 ('05dca7f2-e49f-4366-a363-b6e057bd68e5', 'MONEY', '{"customAmtCta": {"constraint": {"maxAmount": {"units": "10000", "currencyCode": "INR"}, "minAmount": {"units": "10", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}', 'CTA_STATE_ACTIVE', '43dd9b50-786b-47c0-bf0b-61401bc8da42', '2023-05-20 19:44:34.299582 +00:00', '2023-05-20 19:44:34.299582 +00:00', null),
	 ('3532e0a2-6192-42ea-9504-f5b9286b5a61', 'PAYMENT_RECIPIENT', '{"recurringPaymentCta": {"text": "Select a payee", "iconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/plus.png", "editIconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/pencil.png", "payeeBgColor": "#AC7C44"}}', 'CTA_STATE_ACTIVE', '1c44b98f-0b75-4568-a06d-7474b00e4c55', '2022-08-19 10:27:58.872002 +00:00', '2022-08-19 10:27:58.872002 +00:00', null),
	 ('ae65b7aa-0e76-4ebc-a64b-a96c6955b334', 'MONEY', '{"customAmtCta": {"type": "CUSTOM_AMOUNT_SELECTOR", "iconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/plus.png", "constraint": {"maxAmount": {"units": "200000", "currencyCode": "INR"}, "minAmount": {"units": "100", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "editIconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/pencil.png", "currencyIconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/recurring_payment_selector_currency.png", "textForRuleDesc": "amount", "textForPossibleValues": "Add Amount"}}', 'CTA_STATE_ACTIVE', '1c44b98f-0b75-4568-a06d-7474b00e4c55', '2022-08-19 10:27:58.872002 +00:00', '2022-08-19 10:27:58.872002 +00:00', null),
	 ('5eca8fa7-a972-415a-ad08-3d5819512d92', 'MONEY', '{"customAmtCta": {"constraint": {"maxAmount": {"units": "10000", "currencyCode": "INR"}, "minAmount": {"units": "100", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}', 'CTA_STATE_ACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', '2023-05-20 19:44:34.299582 +00:00', '2023-05-20 19:44:34.299582 +00:00', null),
	 ('6d4a10c9-2a9e-474b-bc1c-32e57d423af2', 'MONEY', '{"customAmtCta": {"textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}', 'CTA_STATE_ACTIVE', '********-f4d1-421f-9c1c-1a7f65612e66', '2022-04-28 10:04:00.628036 +00:00', '2022-04-28 10:04:00.628036 +00:00', null),
	 ('b36d66c9-2900-4165-a5da-0e44c3515c07', 'MUTUAL_FUND', '{"mfSelectorCta": {"text": "Select a mutual fund", "iconUrl": "https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}', 'CTA_STATE_ACTIVE', '********-f4d1-421f-9c1c-1a7f65612e66', '2022-01-23 05:02:54.734284 +00:00', '2022-01-23 05:02:54.734284 +00:00', null),
	 ('f241ca37-64eb-4477-a319-51b62beb1a6d', 'MONEY', '{"customAmtCta": {"constraint": {"maxAmount": {"units": "10000", "currencyCode": "INR"}, "minAmount": {"units": "10", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}', 'CTA_STATE_ACTIVE', '78d18f6c-03bf-499a-aed7-f49eef104ba8', '2023-05-20 19:44:34.299582 +00:00', '2023-05-20 19:44:34.299582 +00:00', null),
	 ('78c54f44-2d28-4d3c-9f90-ddaaa7ba0f03', 'MONEY', '{"customAmtCta": {"constraint": {"maxAmount": {"units": "10000", "currencyCode": "INR"}, "minAmount": {"units": "10", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}', 'CTA_STATE_ACTIVE', 'bc925bcb-ff12-43f9-8396-b6383242d939', '2023-05-20 19:44:34.299582 +00:00', '2023-05-20 19:44:34.299582 +00:00', null),
	 ('6586307c-3d10-4492-bdb5-4a900128bb41', 'MONEY', '{"customAmtCta": {"constraint": {"maxAmount": {"units": "10000", "currencyCode": "INR"}, "minAmount": {"units": "50", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}', 'CTA_STATE_ACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', '2023-05-20 19:44:34.299582 +00:00', '2023-05-20 19:44:34.299582 +00:00', null);

-- parameter values to be presented to user for associated rules --
INSERT INTO public.possible_param_values (id, param_value_type, value, is_default_value, state, rule_id, weight) VALUES
	 ('4681dfd4-4080-41d3-8240-e5a6dbf1c090', 'INT_INPUT', '{"int_val": 20, "icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/20.png"}', false, 'PARAM_STATE_ACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 5),
	 ('4e619a2f-80e9-4f41-9b5d-106fac30fcdc', 'INT_INPUT', '{"int_val": 10, "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/10.png"}', false, 'PARAM_STATE_ACTIVE', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 15),
	 ('c9332f58-caf5-4e90-b86d-c2611a84c6b9', 'INT_INPUT', '{"int_val": 15, "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/15.png"}', false, 'PARAM_STATE_ACTIVE', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 10),
	 ('443c26fe-bc29-4db4-9c65-bf1044c062bc', 'INT_INPUT', '{"int_val": 5, "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/05.png"}', false, 'PARAM_STATE_ACTIVE', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 20),
	 ('77cc573c-b4e8-442d-99f2-1e10ede3177a', 'STRING_INPUT', '{"str_val": "Tuesday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/Tuesday.png"}', false, 'PARAM_STATE_ACTIVE', '********-f4d1-421f-9c1c-1a7f65612e66', 10),
	 ('d3c0f95e-102f-4171-a14b-6b2bab5ff017', 'FREQUENCY', '{"frequency_val": {"frequency": "FREQUENCY_WEEKLY", "display_text": "Weekly"}}', false, 'PARAM_STATE_ACTIVE', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 5),
	 ('aa61a0d1-4506-4864-88fb-79080c818217', 'STRING_INPUT', '{"str_val": "Wednesday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/Wednesday.png"}', false, 'PARAM_STATE_ACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 50),
	 ('4ed1b5cf-386b-4ae4-a578-ae99e9ccd6d4', 'FREQUENCY', '{"frequency_val": {"frequency": "FREQUENCY_FORTNIGHTLY", "display_text": "Fortnightly (every 15 days)"}}', false, 'PARAM_STATE_ACTIVE', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 1),
	 ('cd54c635-49f8-40d1-9be5-2b7a3cbcd4e1', 'MONEY', '{"money_val": {"units": "20", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', 'e52fb0c5-8a07-4f59-a70b-18c6f2669163', 1),
	 ('5d05ef1d-df14-4aab-b40b-32f9ffd8f238', 'MONEY', '{"money_val": {"units": "50", "currency_code": "INR"}}', true, 'PARAM_STATE_ACTIVE', 'e52fb0c5-8a07-4f59-a70b-18c6f2669163', 1),
	 ('36f5a711-cc9f-4cd1-980c-7c0d5c392286', 'MONEY', '{"money_val": {"units": "100", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', 'e52fb0c5-8a07-4f59-a70b-18c6f2669163', 1),
	 ('914edfaf-4da4-4268-bbc8-b96bdceed57a', 'MONEY', '{"money_val": {"units": "200", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', 'e52fb0c5-8a07-4f59-a70b-18c6f2669163', 1),
	 ('c878126a-af1f-4d51-8cae-72cfc93807cd', 'MONEY', '{"money_val": {"units": "20", "currency_code": "INR"}}', true, 'PARAM_STATE_ACTIVE', 'bc925bcb-ff12-43f9-8396-b6383242d939', 15),
	 ('b45dfb83-cdd4-4911-9a58-8cca89284e95', 'MONEY', '{"money_val": {"units": "50", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', 'bc925bcb-ff12-43f9-8396-b6383242d939', 10),
	 ('ee4eb2c4-8f68-42cc-a152-a5ecfc9707a4', 'MONEY', '{"money_val": {"units": "100", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', 'bc925bcb-ff12-43f9-8396-b6383242d939', 5),
	 ('0232a881-db07-4f49-9e36-ea2ab2117c66', 'FREQUENCY', '{"frequency_val": {"frequency": "FREQUENCY_MONTHLY", "display_text": "Monthly"}}', true, 'PARAM_STATE_ACTIVE', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 3),
	 ('4c908674-69bd-437a-8ed5-866c67ee8431', 'FREQUENCY', '{"frequency_val": {"frequency": "FREQUENCY_QUARTERLY", "display_text": "Quarterly"}}', false, 'PARAM_STATE_ACTIVE', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 1),
	 ('9416af15-9b24-46ae-8b09-ce0152ce4f66', 'FREQUENCY', '{"frequency_val": {"frequency": "FREQUENCY_HALF_YEARLY", "display_text": "Half-Yearly"}}', false, 'PARAM_STATE_ACTIVE', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 1),
	 ('2ecb79fe-819a-4812-9bed-6f97653863cc', 'FREQUENCY', '{"frequency_val": {"frequency": "FREQUENCY_YEARLY", "display_text": "Yearly"}}', false, 'PARAM_STATE_ACTIVE', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 2),
	 ('ed98b262-7ee7-4897-8c0e-45d39a381aa3', 'STRING_INPUT', '{"str_val": "Sunday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/Sunday.png"}', false, 'PARAM_STATE_INACTIVE', '********-f4d1-421f-9c1c-1a7f65612e66', 35),
	 ('0a51d2f4-6b35-4428-9b2e-b6dbe22556bc', 'MONEY', '{"money_val": {"units": "20", "currency_code": "INR"}}', false, 'PARAM_STATE_INACTIVE', '78d18f6c-03bf-499a-aed7-f49eef104ba8', 1),
	 ('4e33df12-ee7a-4ff9-a142-0d4ee2abd2b6', 'STRING_INPUT', '{"str_val": "Tuesday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/Tuesday.png"}', false, 'PARAM_STATE_ACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 60),
	 ('0e832905-a485-4c99-8401-c29638b2650f', 'STRING_INPUT', '{"str_val": "Thursday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/Thursday.png"}', false, 'PARAM_STATE_ACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 40),
	 ('5fc0c21b-1e79-458e-8c21-a0d60a30f927', 'STRING_INPUT', '{"str_val": "Sunday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/Sunday.png"}', false, 'PARAM_STATE_ACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 10),
	 ('9e78df18-6039-4780-a6b8-f7d21dee516b', 'MONEY', '{"money_val": {"units": "50", "currency_code": "INR"}}', true, 'PARAM_STATE_INACTIVE', '78d18f6c-03bf-499a-aed7-f49eef104ba8', 1),
	 ('f7c5e08e-feda-43a7-bcac-331396021581', 'MONEY', '{"money_val": {"units": "200", "currency_code": "INR"}}', false, 'PARAM_STATE_INACTIVE', '78d18f6c-03bf-499a-aed7-f49eef104ba8', 1),
	 ('23c76718-48f9-43d7-bdeb-f4b5417fb980', 'MONEY', '{"money_val": {"units": "100", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', '78d18f6c-03bf-499a-aed7-f49eef104ba8', 1),
	 ('44421925-05d8-4182-ae1d-3d9d693e8e16', 'MONEY', '{"money_val": {"units": "10", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', '78d18f6c-03bf-499a-aed7-f49eef104ba8', 10),
	 ('f52b9aef-122d-4f8c-9c64-a391d0daa3a4', 'INT_INPUT', '{"int_val": 5, "icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/05.png"}', true, 'PARAM_STATE_ACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 20),
	 ('0220e5e8-67d1-4a22-8c39-1b6fc0d8ca54', 'INT_INPUT', '{"int_val": 15, "icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/15.png"}', false, 'PARAM_STATE_ACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 10),
	 ('8a8922d4-7a9d-4b4b-ad7a-454fd7a6b977', 'MERCHANT', '{"icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/amazon.png", "merchant_val": {"icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/amazon.png", "merchant_name": "Amazon"}}', true, 'PARAM_STATE_ACTIVE', 'bc925bcb-ff12-43f9-8396-b6383242d939', 20),
	 ('2de0f557-8231-430e-8977-10736dbe7bbe', 'MERCHANT', '{"icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/flipkart.png", "merchant_val": {"icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/flipkart.png", "merchant_name": "Flipkart"}}', false, 'PARAM_STATE_ACTIVE', 'bc925bcb-ff12-43f9-8396-b6383242d939', 15),
	 ('14ae619f-66f4-4c65-9958-59042d0eedd9', 'INT_INPUT', '{"int_val": 10, "icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/10.png"}', false, 'PARAM_STATE_ACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 15),
	 ('2993d1cd-d63f-4118-947f-a4230c1673e9', 'MERCHANT', '{"icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/big-basket.png", "merchant_val": {"icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/big-basket.png", "merchant_name": "Big Basket"}}', false, 'PARAM_STATE_ACTIVE', 'bc925bcb-ff12-43f9-8396-b6383242d939', 1),
	 ('59f7f335-0fff-45dd-a05e-fe47dd5876c3', 'MERCHANT', '{"icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/myntra.png", "merchant_val": {"icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/myntra.png", "merchant_name": "Myntra"}}', false, 'PARAM_STATE_ACTIVE', 'bc925bcb-ff12-43f9-8396-b6383242d939', 1),
	 ('0ca7fb3e-67cc-400d-8b51-3f75d5df8dfa', 'MERCHANT', '{"icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/nykaa.png", "merchant_val": {"icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/nykaa.png", "merchant_name": "Nykaa"}}', false, 'PARAM_STATE_ACTIVE', 'bc925bcb-ff12-43f9-8396-b6383242d939', 1),
	 ('6ab55381-7673-4f84-b48c-4b227885fc57', 'MERCHANT', '{"icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/dunzo.png", "merchant_val": {"icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/dunzo.png", "merchant_name": "Dunzo"}}', false, 'PARAM_STATE_ACTIVE', 'bc925bcb-ff12-43f9-8396-b6383242d939', 1),
	 ('0c7a4d88-d46a-4c1b-982d-6ee6e1636044', 'STRING_INPUT', '{"str_val": "Friday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/Friday.png"}', true, 'PARAM_STATE_ACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 30),
	 ('ee60abfc-5dbb-4212-9e29-7fb08a1ad766', 'STRING_INPUT', '{"str_val": "Saturday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/Saturday.png"}', false, 'PARAM_STATE_ACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 20),
	 ('a5a10ad1-6790-40ef-b705-c3c50f0f7daf', 'INT_INPUT', '{"int_val": 20, "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/20.png"}', true, 'PARAM_STATE_ACTIVE', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 5),
	 ('dddc83d5-9f5d-440d-abf6-b205bd3e7f19', 'STRING_INPUT', '{"str_val": "Saturday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/Saturday.png"}', false, 'PARAM_STATE_INACTIVE', '********-f4d1-421f-9c1c-1a7f65612e66', 30),
	 ('5b836e7a-8c15-41e9-8848-292f7ed4e8f8', 'MONEY', '{"money_val": {"units": "2500", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 5),
	 ('e705fcbd-7eb4-4e97-b52d-4fb9a27a83e4', 'MONEY', '{"money_val": {"units": "5000", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 10),
	 ('ea0c89ca-f167-43bf-95e6-5b441796d34c', 'MONEY', '{"money_val": {"units": "7500", "currency_code": "INR"}}', true, 'PARAM_STATE_ACTIVE', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 15),
	 ('e0006f9a-90b8-45bf-a655-cf87b603e410', 'MONEY', '{"money_val": {"units": "10000", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 20),
	 ('5dd1ba73-3379-4767-99cb-a81a5f5f781b', 'MONEY', '{"money_val": {"units": "500", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', '********-f4d1-421f-9c1c-1a7f65612e66', 5),
	 ('4e46419b-67e8-44eb-a882-1a3c5965c028', 'MONEY', '{"money_val": {"units": "1000", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', '********-f4d1-421f-9c1c-1a7f65612e66', 10),
	 ('fecae468-e087-4ac4-bcf5-fcf87c13cac8', 'MONEY', '{"money_val": {"units": "2000", "currency_code": "INR"}}', true, 'PARAM_STATE_ACTIVE', '********-f4d1-421f-9c1c-1a7f65612e66', 15),
	 ('fc7cae1d-890b-4d3f-9ffa-a2788aca1313', 'MONEY', '{"money_val": {"units": "2500", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', '********-f4d1-421f-9c1c-1a7f65612e66', 20),
	 ('bfffc9a4-099b-48b6-baf3-3996a5223c53', 'MONEY', '{"money_val": {"units": "100", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25', 5),
	 ('82186042-a0b2-4708-ae45-02765b04a341', 'MONEY', '{"money_val": {"units": "200", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25', 10),
	 ('6495d20e-0d2c-4274-8f9f-8d142d3961ed', 'MONEY', '{"money_val": {"units": "400", "currency_code": "INR"}}', true, 'PARAM_STATE_ACTIVE', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25', 15),
	 ('d9cfccb2-5452-4cd3-b4fb-c721b950917d', 'MONEY', '{"money_val": {"units": "500", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25', 20),
	 ('4499f41d-5f7e-4f51-af4b-b021ad8981b2', 'STRING_INPUT', '{"str_val": "Thursday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/Thursday.png"}', false, 'PARAM_STATE_ACTIVE', '********-f4d1-421f-9c1c-1a7f65612e66', 20),
	 ('194fcd74-13bd-41b9-9665-b06c7219ce11', 'STRING_INPUT', '{"str_val": "Friday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/Friday.png"}', true, 'PARAM_STATE_ACTIVE', '********-f4d1-421f-9c1c-1a7f65612e66', 25),
	 ('529c4fb3-abdf-4833-80f3-bb2419f2a495', 'STRING_INPUT', '{"str_val": "Monday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/Monday.png"}', false, 'PARAM_STATE_ACTIVE', '********-f4d1-421f-9c1c-1a7f65612e66', 5),
	 ('71746c64-e816-4a2e-87a6-e9ce253f1565', 'STRING_INPUT', '{"str_val": "Wednesday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/Wednesday.png"}', false, 'PARAM_STATE_ACTIVE', '********-f4d1-421f-9c1c-1a7f65612e66', 15),
	 ('bcacf8a5-59f5-4db0-96b7-8db9e74bd9a5', 'MONEY', '{"money_val": {"units": "100", "currency_code": "INR"}}', true, 'PARAM_STATE_INACTIVE', '43dd9b50-786b-47c0-bf0b-61401bc8da42', 15),
	 ('c48ce8e0-0369-4226-902c-7dbc31641270', 'MONEY', '{"money_val": {"units": "250", "currency_code": "INR"}}', false, 'PARAM_STATE_INACTIVE', '43dd9b50-786b-47c0-bf0b-61401bc8da42', 10),
	 ('2e65521a-404c-4ebb-bd50-424a8f6a2c2f', 'MONEY', '{"money_val": {"units": "500", "currency_code": "INR"}}', false, 'PARAM_STATE_INACTIVE', '43dd9b50-786b-47c0-bf0b-61401bc8da42', 5),
	 ('7d97410a-7794-41ea-a9d7-43bb0a00f576', 'MONEY', '{"money_val": {"units": "50", "currency_code": "INR"}}', false, 'PARAM_STATE_INACTIVE', '43dd9b50-786b-47c0-bf0b-61401bc8da42', 20),
	 ('f976b4eb-3663-41e1-9c50-cc7e2002e463', 'MONEY', '{"money_val": {"units": "10", "currency_code": "INR"}}', true, 'PARAM_STATE_ACTIVE', '43dd9b50-786b-47c0-bf0b-61401bc8da42', 30),
	 ('fe58a263-017e-42b1-96b0-b4b52ff211d3', 'MONEY', '{"money_val": {"units": "50", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', '43dd9b50-786b-47c0-bf0b-61401bc8da42', 20),
	 ('48958336-0440-43e5-9635-35ad72634fcd', 'MONEY', '{"money_val": {"units": "100", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', '43dd9b50-786b-47c0-bf0b-61401bc8da42', 10),
	 ('a357c10f-04ae-48db-8af3-59b5a2276fd4', 'MONEY', '{"money_val": {"units": "1000", "currency_code": "INR"}}', false, 'PARAM_STATE_INACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 50),
	 ('4399516c-fad9-4bef-9a22-502e32758943', 'MONEY', '{"money_val": {"units": "2000", "currency_code": "INR"}}', true, 'PARAM_STATE_INACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 40),
	 ('94936b9b-780a-46e7-b6f6-eddd4ea99590', 'MONEY', '{"money_val": {"units": "2500", "currency_code": "INR"}}', false, 'PARAM_STATE_INACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 30),
	 ('ebedf0e0-b9cb-4766-938d-81da35c0780f', 'MONEY', '{"money_val": {"units": "3000", "currency_code": "INR"}}', false, 'PARAM_STATE_INACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 20),
	 ('37fb3261-852d-4c9f-8a9e-f54feeb19282', 'MONEY', '{"money_val": {"units": "10", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', 'bc925bcb-ff12-43f9-8396-b6383242d939', 20),
	 ('36e7dfa4-863c-4ed4-aa82-515de16fe6a1', 'STRING_INPUT', '{"str_val": "Monday", "icon_url": "https://epifi-icons.pointz.in/fittt-images/v1/icons/Monday.png"}', false, 'PARAM_STATE_ACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 70),
	 ('ac954330-887a-46fd-b7ba-c25300058536', 'MONEY', '{"money_val": {"units": "3500", "currency_code": "INR"}}', false, 'PARAM_STATE_INACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 10),
	 ('ac9c9ebe-3334-4d89-a636-8f2b647146db', 'MONEY', '{"money_val": {"units": "250", "currency_code": "INR"}}', false, 'PARAM_STATE_INACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 1),
	 ('7899f73f-9d93-40ef-a3c1-46a247bdbd44', 'MONEY', '{"money_val": {"units": "500", "currency_code": "INR"}}', false, 'PARAM_STATE_INACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 1),
	 ('6b9c4fcb-3524-457e-aed7-6ed626da69b9', 'MONEY', '{"money_val": {"units": "750", "currency_code": "INR"}}', true, 'PARAM_STATE_INACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 1),
	 ('689b0677-cf7e-459d-a77c-c577dea542f6', 'MONEY', '{"money_val": {"units": "1000", "currency_code": "INR"}}', false, 'PARAM_STATE_INACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 1),
	 ('2c99b4a7-0b43-4109-8b04-a08dbd9bca32', 'MONEY', '{"money_val": {"units": "2000", "currency_code": "INR"}}', false, 'PARAM_STATE_INACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 1),
	 ('85f08415-af82-4dc3-abf3-0eac421f2e11', 'MONEY', '{"money_val": {"units": "50", "currency_code": "INR"}}', true, 'PARAM_STATE_ACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 30),
	 ('81b2c79c-03a3-4cac-958c-b836e5021931', 'MONEY', '{"money_val": {"units": "100", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 20),
	 ('ba5bfaa9-e103-4672-970e-ec0830df77b9', 'MONEY', '{"money_val": {"units": "500", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 10),
	 ('c27f4d34-3635-41f2-8a63-36e9afc5c62d', 'MONEY', '{"money_val": {"units": "100", "currency_code": "INR"}}', true, 'PARAM_STATE_ACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 30),
	 ('285d05bd-6ca1-47fe-b7d4-8a420d2d9f56', 'MONEY', '{"money_val": {"units": "500", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 20),
	 ('c25e509b-b9d0-4fc1-92b4-a9a437165834', 'MONEY', '{"money_val": {"units": "1000", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 10),
	 ('914940b7-044f-4c81-9b7d-c2eb27d804c8', 'MONEY', '{"money_val": {"units": "2500", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 1),
	 ('743b84ee-159b-4f94-a028-6a7d50e13efb', 'MONEY', '{"money_val": {"units": "1000", "currency_code": "INR"}}', false, 'PARAM_STATE_ACTIVE', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 1);

-- display info for associated rules --
INSERT INTO public.rule_display_infos (id, name, formatted_name, home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color, sentence_case_name, category, stats_bg_color, display_info) VALUES
	  ('eabde4af-8759-4370-a7ae-65fcd3d86cc0', 'KEEP THE CHANGE', 'KEEP THE CHANGE', 'When I spend with Fi, round-up and save', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Home.png', 'When I spend with Fi, round-up to the next <u>defaultMoneyValue</u> and save the difference', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Landing.png', '#CDC6E8', '#9287BD', 'Keep the Change', 'AUTO_SAVE', '#C0B7E1', null),
	  ('b112306e-aadd-438a-9e99-e7c41288ed26', 'RECURRING PAYMENT', 'RECURRING PAYMENT', 'Send my landlord ₹10,000 every month', 'https://epifi-icons.pointz.in/fittt-images/rules/recurring-payment-home.png', 'Send my landlord ₹10,000 every month', 'https://epifi-icons.pointz.in/fittt-images/rules/recurring-payment-landing.png', '#D1DAF1', '#768CC3', 'Recurring Payment', 'AUTO_PAY', '#BBC8E9', null),
	  ('0e78c2ed-4b43-4a8e-a47e-b2686a9debf4', 'ONE-TIME PAYMENT', 'ONE-TIME PAYMENT', 'Send mom ₹3,000 on July 07', 'https://epifi-icons.pointz.in/fittt-images/rules/one-time-payment-home.png', 'Send mom ₹3,000 on July 07', 'https://epifi-icons.pointz.in/fittt-images/rules/one-time-payment-landing.png', '#F4E7BF', '#D3B250', 'One-Time Payment', 'AUTO_PAY', '#EAD8A3', null),
	  ('9108c557-07f8-4791-bad9-c9d758ab4df6', 'MONTHLY SIP', 'MONTHLY SIP', 'On the <u>subscribedIntVal</u>th of every month, invest <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ThinkingAhead_Home.png', 'On 1st of every month, invest ₹3,000', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ThinkingAhead.png', '#FAD0D0', '#CF8888', 'Monthly SIP', 'AUTO_INVEST', '#EFC0C0', null),
	  ('b8405887-f91b-4a10-aed3-ebd2dd4b3977', 'DAILY DEEDS', 'DAILY DEEDS', 'At the end of every day, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DailyDeeds_Home2.png', 'Everyday, put aside ₹10', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DailyDeedsV2.png', '#DEEEF2', '#7FBECE', 'Daily Deeds', 'AUTO_SAVE', '#C0DAE0', null),
	  ('1c817dba-cf9f-41f6-b34b-4b06feddff9b', 'MAINTAIN BALANCE', 'MAINTAIN BALANCE', 'When my salary is credited to Fi, send ₹10,000 to other account', 'https://epifi-icons.pointz.in/fittt-images/rules/maintain-balance-home.png', 'When my salary is credited to Fi, send ₹10,000 to other account', 'https://epifi-icons.pointz.in/fittt-images/rules/maintain-balance-landing.png', '#DEEEF2', '#7FBECE', 'Maintain Balance', 'AUTO_PAY', '#C0DAE0', null),
	  ('cc0565e0-0cb2-4362-8da2-fcb95001e1bb', 'DON''T SHOP TILL YOU DROP', 'DON''T SHOP TILL \nYOU DROP', 'When I order on <u>defaultUniqueValue</u> with Fi, invest <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DontShopTillYouDrop-Home.png', 'When I order on <u>defaultUniqueValue</u> with Fi, invest ₹100', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DontShopTillYouDrop-Landing.png', '#D1DAF1', '#768CC3', 'Don''t Shop till you Drop', 'AUTO_INVEST', '#BBC8E9', null),
	  ('2119a128-32b7-4b6f-b4fb-fca67a21dac7', 'DON''T SHOP TILL YOU DROP', e'DON\'T SHOP TILL YOU DROP', 'When I order on <u>subscribedUniqueValue</u>, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DontShopTillYouDrop-Home.png', 'When I order from <u>defaultUniqueValue</u> with Fi, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DontShopTillYouDrop-Landing.png', '#D1DAF1', '#768CC3', 'Don''t Shop till you Drop', 'AUTO_SAVE', '#BBC8E9', null),
	  ('dfbc5e20-4a4a-4875-8112-7b2afd869515', 'CONSISTENCY IS KEY', 'CONSISTENCY IS KEY', 'On <u>subscribedStrVal</u> every week, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ConsistencyIsKey-Home.png', 'On Friday every week, put aside ₹50', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ConsistencyIsKey.png', '#D9F2CC', '#87BA6B', 'Consistency is Key', 'AUTO_SAVE', '#C5E9B2', null),
	  ('9def84df-b517-41b8-80e0-21c724cf47d9', 'THINKING AHEAD', 'THINKING AHEAD', 'On the <u>subscribedIntVal</u>th every month, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ThinkingAhead-Home.png', 'On 1st of every month, put aside ₹100', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ThinkingAhead.png', '#FAD0D0', '#CF8888', 'Thinking Ahead', 'AUTO_SAVE', '#EFC0C0', null),
	  ('676e1a56-bf08-42a8-8759-4920c3eebce9', 'TIP YOURSELF', e'TIP YOURSELF', 'When I make a payment, round up and save', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Home.png', 'When I make a payment, round up to nearest <u>defaultMoneyValue</u> and <u>save</u> the difference', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Landing.png', '#CDC6E8', '#9287BD', 'Tip Yourself', 'AUTO_SAVE', '#C0B7E1', null),
	  ('e7e27cb6-3ee3-4f59-baa6-c40854be830b', 'WEEKLY SIP', 'WEEKLY SIP', 'On <u>subscribedStrVal</u> every week, invest <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ConsistencyIsKey_Home.png', 'On Friday every week, invest ₹1,000', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ConsistencyIsKey.png', '#D9F2CC', '#87BA6B', 'Weekly SIP', 'AUTO_INVEST', '#C5E9B2', null),
	  ('dd42c626-13e5-4184-b1ad-3d7c668850ab', 'DAILY SIP', 'DAILY SIP', 'At the end of every day, invest <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DailyDeeds_Home2.png', 'Everyday, invest ₹100', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DailyDeedsV2.png', '#DEEEF2', '#7FBECE', 'Daily SIP', 'AUTO_INVEST', '#C0DAE0', null);

-- 	tag definition for rules --
INSERT INTO public.rule_tags (id, name, type, path, created_at, updated_at, deleted_at, is_display_tag, state, display_info) VALUES
	 ('TipYourself', 'Tip Yourself', 'ROOT', 'TipYourself', '2021-08-27 10:58:59.624718 +00:00', '2021-08-27 10:58:59.624718 +00:00', null, false, 'TAG_ACTIVE', null),
	 ('Shopping', 'Shopping', 'TERMINAL', 'Merchant.Shopping', '2021-06-25 03:23:31.242941 +00:00', '2021-06-25 03:23:31.242941 +00:00', null, true, 'TAG_ACTIVE', '{"iconUrls": ["https://epifi-icons.pointz.in/fittt-images/Shopping.svg"]}'),
	 ('Growth', 'Growth', 'TERMINAL', 'Growth', '2021-08-31 15:35:10.127462 +00:00', '2021-08-31 15:35:10.127462 +00:00', null, true, 'TAG_ACTIVE', '{"iconUrls": ["https://epifi-icons.pointz.in/fittt-images/icons/Growth.png"]}'),
	 ('INVESTMENTS', 'INVESTMENTS', 'TERMINAL', 'INVESTMENTS', '2022-01-23 05:02:54.734284 +00:00', '2022-01-23 05:02:54.734284 +00:00', null, true, 'TAG_ACTIVE', null);

-- mapping of rules to tags --
INSERT INTO public.rule_tag_mappings (id, rule_id, tag_id, created_at, updated_at, deleted_at) VALUES
	   ('dd0530a7-a070-4d34-a1f0-2fd850d42403', 'e52fb0c5-8a07-4f59-a70b-18c6f2669163', 'TipYourself', '2021-08-27 10:59:34.439389 +00:00', '2021-08-27 10:59:34.439389 +00:00', null),
	   ('8c7a18e4-8ded-4a4d-9658-27f0183fe600', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2', 'Growth', '2021-08-31 15:35:10.135239 +00:00', '2021-08-31 15:35:10.135239 +00:00', null),
	   ('3a19c4a9-7a64-45a2-a453-52ed1b7cacf2', '7b251ad6-a653-4c8a-88fa-a3714b45b480', 'Growth', '2021-08-31 15:35:10.135239 +00:00', '2021-08-31 15:35:10.135239 +00:00', null),
	   ('66d4ebc6-34c1-4ced-837d-69d632ae06bb', 'bc925bcb-ff12-43f9-8396-b6383242d939', 'Shopping', '2021-08-31 15:38:53.413183 +00:00', '2021-08-31 15:38:53.413183 +00:00', null),
	   ('67208d0a-0808-428d-a9ca-0cf6b5cd8f33', '43dd9b50-786b-47c0-bf0b-61401bc8da42', 'Growth', '2021-11-30 05:03:37.331243 +00:00', '2021-11-30 05:03:37.331243 +00:00', null),
	   ('366143f9-63ab-451f-a193-12571d67f706', '78d18f6c-03bf-499a-aed7-f49eef104ba8', 'Shopping', '2021-12-27 11:35:45.377031 +00:00', '2021-12-27 11:35:45.377031 +00:00', null),
	   ('6a2ca7f3-e3d5-4d5e-b716-05b5b7a29523', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 'INVESTMENTS', '2022-01-23 05:02:54.734284 +00:00', '2022-01-23 05:02:54.734284 +00:00', null),
	   ('6ce1b765-ba72-4ffe-ac7d-96d6393557dd', '********-f4d1-421f-9c1c-1a7f65612e66', 'INVESTMENTS', '2022-01-23 05:02:54.734284 +00:00', '2022-01-23 05:02:54.734284 +00:00', null),
	   ('5263af51-5404-49e5-af99-2d3e6e5d7215', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25', 'INVESTMENTS', '2022-01-23 05:02:54.734284 +00:00', '2022-01-23 05:02:54.734284 +00:00', null);

INSERT INTO public.tag_display_infos (tag_id, icon_urls) VALUES
	 ('Shopping', '{https://epifi-icons.pointz.in/fittt-images/Shopping.svg}'),
	('Growth', '{https://epifi-icons.pointz.in/fittt-images/icons/Growth.png}');
