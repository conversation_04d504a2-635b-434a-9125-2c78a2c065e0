-- schema changes --
CREATE TABLE home_cards (
    id uuid default uuid_generate_v4() not null constraint home_cards_pkey PRIMARY KEY,
    state varchar not null,
    deeplink jsonb,
    weight integer not null default 1,
    card_data jsonb not null,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    deleted_at timestamp with time zone
);

-- comments --
COMMENT ON COLUMN home_cards.state is '{"proto_type":"rule.HomeCard.state", "comment": "State can be Active/Inactive"}';
COMMENT ON COLUMN home_cards.card_data is '{"proto_type":"rule.HomeCardData", "comment": "Contains all information required to construct description text of the card"}';
COMMENT ON COLUMN home_cards.deeplink is '{"proto_type":"frontend.deeplink.Deeplink", "comment": "provides data which the redirected screen would use to invoke API and load the page"}';
COMMENT ON COLUMN home_cards.weight is '{"proto_type":"rule.HomeCard.weight", "comment": "weight will help in ordering cards"}';

-- indexes --
CREATE INDEX state_idx ON home_cards (state);
