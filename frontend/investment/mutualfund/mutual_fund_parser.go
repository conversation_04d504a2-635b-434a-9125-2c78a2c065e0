// nolint
package mutualfund

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"math"
	"strings"

	"github.com/shopspring/decimal"
	"github.com/thoas/go-funk"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/money"

	fePb "github.com/epifi/gamma/api/frontend/investment/mutualfund"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	types "github.com/epifi/gamma/api/typesv2"
	mfUtils "github.com/epifi/gamma/investment/utils"
)

var (
	timeFramesHybrid = []fePb.TimeFrame{fePb.TimeFrame_TIMEFRAME_5_YR, fePb.TimeFrame_TIMEFRAME_3_YR, fePb.TimeFrame_TIMEFRAME_1_YR, fePb.TimeFrame_TIMEFRAME_6_MTH, fePb.TimeFrame_TIMEFRAME_1_MTH}
	timeFramesEquity = []fePb.TimeFrame{fePb.TimeFrame_TIMEFRAME_5_YR, fePb.TimeFrame_TIMEFRAME_3_YR, fePb.TimeFrame_TIMEFRAME_1_YR, fePb.TimeFrame_TIMEFRAME_6_MTH, fePb.TimeFrame_TIMEFRAME_1_MTH}
	timeFramesDebt   = []fePb.TimeFrame{fePb.TimeFrame_TIMEFRAME_3_YR, fePb.TimeFrame_TIMEFRAME_1_YR, fePb.TimeFrame_TIMEFRAME_6_MTH, fePb.TimeFrame_TIMEFRAME_1_MTH}
	timeFramesCash   = []fePb.TimeFrame{fePb.TimeFrame_TIMEFRAME_1_YR, fePb.TimeFrame_TIMEFRAME_6_MTH, fePb.TimeFrame_TIMEFRAME_1_MTH}
)

func getPerfIndicators(ctx context.Context, fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) *fePb.PerformanceIndicators {
	perfIndicator := &fePb.PerformanceIndicators{
		DisplayHeader:     DisplayPerformanceIndicatorHeader,
		ReturnsTile:       getReturnsTile(fund, categoryAvgData),
		ExpenseTile:       getExpenseTile(fund, categoryAvgData),
		FundSizeTile:      getFundSizeTile(fund, categoryAvgData),
		NavTile:           getNavTile(ctx, fund),
		TrackingErrorTile: getTrackingErrorTile(fund, categoryAvgData),
	}
	return perfIndicator
}

func getTrackingErrorTile(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) *fePb.TrackingErrorTile {
	if fund.CategoryName != mfPb.MutualFundCategoryName_INDEX_FUNDS {
		return nil
	}
	timeframeData := getTimeFrameWiseTrackingError(fund, categoryAvgData)
	if len(timeframeData) == 0 {
		return nil
	}
	return &fePb.TrackingErrorTile{
		Header:                     DisplayTrackingErrorHeader,
		InfoStr:                    DisplayTrackingErrorDesc,
		TimeFrameWiseTrackingError: timeframeData,
		TileInfo: &fePb.KeyValInfo{
			InfoHeader: DisplayTrackingErrorDescHeader,
			InfoStr:    DisplayTrackingErrorDesc,
		},
	}
}

// nolint:dupl
func getTimeFrameWiseTrackingError(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) map[string]*fePb.TrackingErrorIndicatorData {
	if fund.PerformanceMetrics != nil && fund.PerformanceMetrics.TrackingError != nil {
		trackingErrData := make(map[string]*fePb.TrackingErrorIndicatorData)
		trackingErr := fund.PerformanceMetrics.TrackingError
		trackingErrData = getTrackingErrData(fePb.TimeFrame_TIMEFRAME_1_YR, trackingErr.TrackingErrorOneYear, categoryAvgData.GetTrackingError().GetCatAvgTrackingErrorOneYear(), trackingErrData)
		trackingErrData = getTrackingErrData(fePb.TimeFrame_TIMEFRAME_3_YR, trackingErr.TrackingErrorThreeYear, categoryAvgData.GetTrackingError().GetCatAvgTrackingErrorThreeYear(), trackingErrData)
		trackingErrData = getTrackingErrData(fePb.TimeFrame_TIMEFRAME_5_YR, trackingErr.TrackingErrorFiveYear, categoryAvgData.GetTrackingError().GetCatAvgTrackingErrorFiveYear(), trackingErrData)
		return trackingErrData
	}
	return nil
}

// nolint:dupl
func getTrackingErrData(timeFrame fePb.TimeFrame, fundTrackingErr float32, catAvgTrackingError float32, trackingErrData map[string]*fePb.TrackingErrorIndicatorData) map[string]*fePb.TrackingErrorIndicatorData {
	if fundTrackingErr != 0 {
		if trackingErrData[timeFrame.String()] == nil {
			trackingErrData[timeFrame.String()] = &fePb.TrackingErrorIndicatorData{}
		}
		if trackingErrData[timeFrame.String()].TrackingErrorData == nil {
			trackingErrData[timeFrame.String()].TrackingErrorData = make([]*fePb.KeyValDisplayData, 0)
		}
		trackingErrData[timeFrame.String()].TrackingErrorData = append(trackingErrData[timeFrame.String()].TrackingErrorData,
			&fePb.KeyValDisplayData{
				KeyName:  DisplayKeyThisFundKey,
				KeyValue: fmt.Sprintf("%.2f", fundTrackingErr) + "%",
			})
		if catAvgTrackingError != 0 {
			trackingErrData[timeFrame.String()].TrackingErrorData = append(trackingErrData[timeFrame.String()].TrackingErrorData,
				&fePb.KeyValDisplayData{
					KeyName:  DisplayKeyCatAvgKey,
					KeyValue: fmt.Sprintf("%.2f", catAvgTrackingError) + "%",
				})
		}
	}
	return trackingErrData
}

func getNavTile(ctx context.Context, fund *mfPb.MutualFund) *fePb.NavTile {
	amt, err := mfUtils.GetAmountFromMoney(ctx, fund.Nav)
	if err != nil {
		amt = 0
	}
	return &fePb.NavTile{
		NavDescription:   fmt.Sprintf("Price of a single unit (NAV) is ₹%.2f", amt),
		NavChangePercent: mfUtils.CalculateNavChangePercentage(ctx, fund),
	}
}

func getFundSizeTile(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) *fePb.FundSizeTile {
	fundSizeData := getFundSizeData(fund, categoryAvgData)
	if len(fundSizeData) == 0 {
		return nil
	}
	return &fePb.FundSizeTile{
		Header:       DisplayFundSizeHeader,
		InfoStr:      DisplayFundSizeInfo,
		FundSizeData: fundSizeData,
		TileInfo: &fePb.KeyValInfo{
			InfoHeader: DisplayFundSizeInfoHeader,
			InfoStr:    DisplayFundSizeInfo,
		},
	}
}

func getFundSizeData(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) []*fePb.KeyValDisplayData {
	if fund.FundFundamentalDetails != nil && fund.FundFundamentalDetails.Aum != nil {
		fundSizeArr := make([]*fePb.KeyValDisplayData, 0)
		if fund.FundFundamentalDetails.Aum.FundAum != 0 {
			fundSizeArr = append(fundSizeArr, &fePb.KeyValDisplayData{
				KeyName:  DisplayKeyThisFundKey,
				KeyValue: fmt.Sprintf("%.1f", fund.FundFundamentalDetails.Aum.FundAum/10000000) + " Cr",
			})
			if categoryAvgData.GetAum().GetCategoryAvgAum() != 0 {
				fundSizeArr = append(fundSizeArr, &fePb.KeyValDisplayData{
					KeyName:  DisplayKeyCatAvgKey,
					KeyValue: fmt.Sprintf("%.1f", categoryAvgData.GetAum().GetCategoryAvgAum()/10000000) + " Cr",
				})
			}
			if categoryAvgData.GetAum().GetCategoryTotalAum() != 0 {
				fundSizeArr = append(fundSizeArr, &fePb.KeyValDisplayData{
					KeyName:  DisplayKeyCategoryShare,
					KeyValue: fmt.Sprintf("%.2f%%", math.Min((fund.FundFundamentalDetails.Aum.FundAum/(categoryAvgData.GetAum().GetCategoryTotalAum()+1))*100, 100)),
				})
			}
			return fundSizeArr
		}
		return nil
	}
	return nil
}

func getExpenseTile(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) *fePb.ExpenseTile {
	timeframeData := getTimeFrameWiseExpense(fund, categoryAvgData)
	if len(timeframeData) == 0 {
		return nil
	}
	return &fePb.ExpenseTile{
		Header:               DisplayExpenseTileHeader,
		InfoStr:              DisplayExpenseTileInfo,
		TimeFrameWiseExpense: timeframeData,
		TileInfo: &fePb.KeyValInfo{
			InfoHeader: DisplayExpenseTileInfoHeader,
			InfoStr:    DisplayExpenseTileInfo,
		},
	}
}

// nolint:dupl
func getTimeFrameWiseExpense(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) map[string]*fePb.ExpenseIndicatorData {
	if fund.FundFundamentalDetails != nil && fund.FundFundamentalDetails.ExpenseRatio != nil {
		expenseIndicatorData := make(map[string]*fePb.ExpenseIndicatorData)
		expenseRatios := fund.FundFundamentalDetails.ExpenseRatio
		expenseIndicatorData = getExpenseIndicatorData(fePb.TimeFrame_TIMEFRAME_NOW, expenseRatios.FundCurrent, categoryAvgData.GetExpenseRatio().GetCategoryCurrent(), expenseIndicatorData)
		expenseIndicatorData = getExpenseIndicatorData(fePb.TimeFrame_TIMEFRAME_1_YR, expenseRatios.FundOneYearAvg, categoryAvgData.GetExpenseRatio().GetCategoryOneYearAvg(), expenseIndicatorData)
		expenseIndicatorData = getExpenseIndicatorData(fePb.TimeFrame_TIMEFRAME_3_YR, expenseRatios.FundThreeYearAvg, categoryAvgData.GetExpenseRatio().GetCategoryThreeYearAvg(), expenseIndicatorData)
		return expenseIndicatorData
	} else if categoryAvgData.GetExpenseRatio() != nil {
		expenseIndicatorData := make(map[string]*fePb.ExpenseIndicatorData)
		expenseIndicatorData = getExpenseIndicatorData(fePb.TimeFrame_TIMEFRAME_NOW, 0, categoryAvgData.GetExpenseRatio().GetCategoryCurrent(), expenseIndicatorData)
		expenseIndicatorData = getExpenseIndicatorData(fePb.TimeFrame_TIMEFRAME_1_YR, 0, categoryAvgData.GetExpenseRatio().GetCategoryOneYearAvg(), expenseIndicatorData)
		expenseIndicatorData = getExpenseIndicatorData(fePb.TimeFrame_TIMEFRAME_3_YR, 0, categoryAvgData.GetExpenseRatio().GetCategoryThreeYearAvg(), expenseIndicatorData)
		return expenseIndicatorData
	}
	return nil
}

// nolint:dupl
func getExpenseIndicatorData(timeFrame fePb.TimeFrame, fundVal float32, catVal float32, expenseIndicatorData map[string]*fePb.ExpenseIndicatorData) map[string]*fePb.ExpenseIndicatorData {
	if fundVal != 0 {
		if expenseIndicatorData[timeFrame.String()] == nil {
			expenseIndicatorData[timeFrame.String()] = &fePb.ExpenseIndicatorData{}
		}
		if expenseIndicatorData[timeFrame.String()].ExpenseData == nil {
			expenseIndicatorData[timeFrame.String()].ExpenseData = make([]*fePb.KeyValDisplayData, 0)
		}
		expenseIndicatorData[timeFrame.String()].ExpenseData = append(expenseIndicatorData[timeFrame.String()].ExpenseData,
			&fePb.KeyValDisplayData{
				KeyName:  DisplayKeyThisFundKey,
				KeyValue: fmt.Sprintf("%.2f", fundVal) + "%",
			})
		if catVal != 0 {
			expenseIndicatorData[timeFrame.String()].ExpenseData = append(expenseIndicatorData[timeFrame.String()].ExpenseData,
				&fePb.KeyValDisplayData{
					KeyName:  DisplayKeyCatAvgKey,
					KeyValue: fmt.Sprintf("%.2f", catVal) + "%",
				})
		}
	} else if catVal != 0 {
		if expenseIndicatorData[timeFrame.String()] == nil {
			expenseIndicatorData[timeFrame.String()] = &fePb.ExpenseIndicatorData{}
		}
		if expenseIndicatorData[timeFrame.String()].ExpenseData == nil {
			expenseIndicatorData[timeFrame.String()].ExpenseData = make([]*fePb.KeyValDisplayData, 0)
		}
		expenseIndicatorData[timeFrame.String()].ExpenseData = append(expenseIndicatorData[timeFrame.String()].ExpenseData,
			&fePb.KeyValDisplayData{
				KeyName:  DisplayKeyThisFundKey,
				KeyValue: DisplayDash,
			},
			&fePb.KeyValDisplayData{
				KeyName:  DisplayKeyCatAvgKey,
				KeyValue: fmt.Sprintf("%.2f", catVal) + "%",
			})
	}
	return expenseIndicatorData
}

func getReturnsTile(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) *fePb.ReturnIndicatorsTile {
	timeframeData := getTimeFrameWiseReturn(fund, categoryAvgData)
	if len(timeframeData) == 0 {
		return nil
	}
	return &fePb.ReturnIndicatorsTile{
		Header:              DisplayReturnsTileHeader,
		InfoStr:             DisplayReturnsTileInfo,
		TimeFrameWiseReturn: timeframeData,
		TileInfo: &fePb.KeyValInfo{
			InfoHeader: DisplayReturnsTileInfoHeader,
			InfoStr:    DisplayReturnsTileInfo,
		},
	}
}

// nolint:funlen
func getTimeFrameWiseReturn(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) map[string]*fePb.ReturnIndicatorData {
	returnIndicator := make(map[string]*fePb.ReturnIndicatorData)
	if fund.Returns != nil {
		returns := fund.Returns
		returnIndicator = getReturnKeyValDisplayData(fePb.TimeFrame_TIMEFRAME_1_YR, returns.AvgFundReturnOneYear, categoryAvgData.GetReturns().GetAvgCategoryReturnOneYear(), returnIndicator)
		returnIndicator = getReturnKeyValDisplayData(fePb.TimeFrame_TIMEFRAME_3_YR, returns.AvgFundReturnThreeYear, categoryAvgData.GetReturns().GetAvgCategoryReturnThreeYear(), returnIndicator)
		returnIndicator = getReturnKeyValDisplayData(fePb.TimeFrame_TIMEFRAME_5_YR, returns.AvgFundReturnFiveYear, categoryAvgData.GetReturns().GetAvgCategoryReturnFiveYear(), returnIndicator)
		returnIndicator = getReturnKeyValDisplayData(fePb.TimeFrame_TIMEFRAME_1_MTH, returns.AvgFundReturnOneMonth, categoryAvgData.GetReturns().GetAvgFundReturnOneMonth(), returnIndicator)
		returnIndicator = getReturnKeyValDisplayData(fePb.TimeFrame_TIMEFRAME_6_MTH, returns.AvgFundReturnSixMonth, categoryAvgData.GetReturns().GetAvgFundReturnSixMonth(), returnIndicator)
	} else if categoryAvgData.GetReturns() != nil {
		returnIndicator = getReturnKeyValDisplayData(fePb.TimeFrame_TIMEFRAME_1_YR, 0, categoryAvgData.GetReturns().GetAvgCategoryReturnOneYear(), returnIndicator)
		returnIndicator = getReturnKeyValDisplayData(fePb.TimeFrame_TIMEFRAME_3_YR, 0, categoryAvgData.GetReturns().GetAvgCategoryReturnThreeYear(), returnIndicator)
		returnIndicator = getReturnKeyValDisplayData(fePb.TimeFrame_TIMEFRAME_5_YR, 0, categoryAvgData.GetReturns().GetAvgCategoryReturnFiveYear(), returnIndicator)
		returnIndicator = getReturnKeyValDisplayData(fePb.TimeFrame_TIMEFRAME_1_MTH, 0, categoryAvgData.GetReturns().GetAvgFundReturnOneMonth(), returnIndicator)
		returnIndicator = getReturnKeyValDisplayData(fePb.TimeFrame_TIMEFRAME_6_MTH, 0, categoryAvgData.GetReturns().GetAvgFundReturnSixMonth(), returnIndicator)
	}
	return returnIndicator
}

func getReturnKeyValDisplayData(timeFrame fePb.TimeFrame, avgFundReturn float32, avgCatReturn float32, returnIndicator map[string]*fePb.ReturnIndicatorData) map[string]*fePb.ReturnIndicatorData {
	if avgFundReturn != 0 {
		if returnIndicator[timeFrame.String()] == nil {
			returnIndicator[timeFrame.String()] = &fePb.ReturnIndicatorData{}
		}
		if returnIndicator[timeFrame.String()].ReturnData == nil {
			returnIndicator[timeFrame.String()].ReturnData = make([]*fePb.KeyValDisplayData, 0)
		}
		returnIndicator[timeFrame.String()].ReturnData = append(returnIndicator[timeFrame.String()].ReturnData,
			&fePb.KeyValDisplayData{
				KeyName:  DisplayKeyFundAvgKey,
				KeyValue: fmt.Sprintf("%.2f", avgFundReturn) + "% p.a.",
				KeyColor: ThisFundReturnColor,
			})
		if avgCatReturn != 0 {
			returnIndicator[timeFrame.String()].ReturnData = append(returnIndicator[timeFrame.String()].ReturnData,
				&fePb.KeyValDisplayData{
					KeyName:  DisplayKeyCatAvgKey,
					KeyValue: fmt.Sprintf("%.2f", avgCatReturn) + "% p.a.",
					KeyColor: CategoryReturnColor,
				})
		}
	} else if avgCatReturn != 0 {
		if returnIndicator[timeFrame.String()] == nil {
			returnIndicator[timeFrame.String()] = &fePb.ReturnIndicatorData{}
		}
		if returnIndicator[timeFrame.String()].ReturnData == nil {
			returnIndicator[timeFrame.String()].ReturnData = make([]*fePb.KeyValDisplayData, 0)
		}
		returnIndicator[timeFrame.String()].ReturnData = append(returnIndicator[timeFrame.String()].ReturnData,
			&fePb.KeyValDisplayData{
				KeyName:  DisplayKeyFundAvgKey,
				KeyValue: DisplayDash,
				KeyColor: ThisFundReturnColor,
			})
		if avgCatReturn != 0 {
			returnIndicator[timeFrame.String()].ReturnData = append(returnIndicator[timeFrame.String()].ReturnData,
				&fePb.KeyValDisplayData{
					KeyName:  DisplayKeyCatAvgKey,
					KeyValue: fmt.Sprintf("%.2f", avgCatReturn) + "% p.a.",
					KeyColor: CategoryReturnColor,
				})
		}
	}
	return returnIndicator
}

func getEntryPointSpecificTagString(tagString string, entry string) string {
	switch entry {
	case ENTRY_POINT_ALL_FUNDS_LIST:
		return strings.ToUpper(tagString)
	case ENTRY_POINT_FUND_DETAILS:
		return tagString
	case ENTRY_POINT_INVESTED_FUND_DETAILS:
		return tagString
	default:
		return tagString
	}

}

func getDisplayTags(fund *mfPb.MutualFund, entry string) []*fePb.MutualFundDisplayTag {
	tags := make([]*fePb.MutualFundDisplayTag, 0)

	if entry == ENTRY_POINT_FUND_DETAILS || entry == ENTRY_POINT_INVESTED_FUND_DETAILS {
		if fund.FiContent != nil && fund.FiContent.UniqueSpecs != "" {
			tags = append(tags, &fePb.MutualFundDisplayTag{
				Type:       fePb.MutualFundDisplayTagType_MF_DISPLAY_TAG_UNIQUE_SPEC,
				DisplayStr: getEntryPointSpecificTagString(fund.FiContent.UniqueSpecs, entry),
			})
		}
	}

	if entry == ENTRY_POINT_ALL_FUNDS_LIST {
		if fund.PlanType != mfPb.PlanType_PLAN_TYPE_UNSPECIFIED {
			tags = append(tags, &fePb.MutualFundDisplayTag{
				Type:       fePb.MutualFundDisplayTagType_MF_DISPLAY_TAG_PLAN_TYPE,
				DisplayStr: getEntryPointSpecificTagString(PlanTypeToText[fund.PlanType], entry),
			})
		}
	}

	if fund.AssetClass != mfPb.AssetClass_ASSET_CLASS_UNSPECIFIED {
		tags = append(tags, &fePb.MutualFundDisplayTag{
			Type:       fePb.MutualFundDisplayTagType_MF_DISPLAY_TAG_ASSET_CLASS,
			DisplayStr: getEntryPointSpecificTagString(getAssetClass(fund.AssetClass, fund.CategoryName), entry),
			InfoStr:    AssetClassDescFromEnum[fund.AssetClass],
			TagInfo: &fePb.KeyValInfo{
				InfoHeader: getAssetClassInfoHeader(fund.AssetClass, fund.CategoryName),
				InfoStr:    getAssetClassInfoDescription(fund.AssetClass, fund.CategoryName),
			},
		})
	}

	if fund.ComputedMinSipAmount > 0 && fund.ComputedMinSipAmount != math.MaxInt32 {
		if fund.ComputedMinSipAmount <= int32(fund.GetTxnConstraints().GetNewpMnval().GetUnits()) {
			tags = append(tags, &fePb.MutualFundDisplayTag{
				Type:       fePb.MutualFundDisplayTagType_MF_DISPLAY_TAG_MIN_SIP_AMOUNT,
				DisplayStr: fmt.Sprintf("Min SIP ₹%v", fund.ComputedMinSipAmount),
			})
		} else {
			tags = append(tags, &fePb.MutualFundDisplayTag{
				Type:       fePb.MutualFundDisplayTagType_MF_DISPLAY_TAG_MIN_OTI_AMOUNT,
				DisplayStr: fmt.Sprintf("Min One-time ₹%v", fund.GetTxnConstraints().GetNewpMnval().GetUnits()),
			})
		}
	}

	if entry == ENTRY_POINT_ALL_FUNDS_LIST {
		if fund.OptionType != mfPb.OptionType_OPTION_TYPE_UNSPECIFIED {
			tags = append(tags, &fePb.MutualFundDisplayTag{
				Type:       fePb.MutualFundDisplayTagType_MF_DISPLAY_TAG_OPTION_TYPE,
				DisplayStr: getEntryPointSpecificTagString(OptionTypeToText[fund.OptionType], entry),
			})
		}
	}

	if fund.FundhouseDefinedRiskLevel != mfPb.FundhouseDefinedRiskLevel_FundhouseDefinedRiskLevel_UNSPECIFIED {
		tags = append(tags, &fePb.MutualFundDisplayTag{
			Type:       fePb.MutualFundDisplayTagType_MF_DISPLAY_TAG_RISK_PROFILE,
			DisplayStr: getEntryPointSpecificTagString(FundRiskToText[fund.FundhouseDefinedRiskLevel], entry),
		})
	}

	if fund.CategoryName != mfPb.MutualFundCategoryName_MutualFundCategoryName_UNSPECIFIED {
		tags = append(tags, &fePb.MutualFundDisplayTag{
			Type:       fePb.MutualFundDisplayTagType_MF_DISPLAY_TAG_CATEGORY,
			DisplayStr: getEntryPointSpecificTagString(FundCategoryToText[fund.CategoryName], entry),
			InfoStr:    FundCategoryDescFromEnum[fund.CategoryName],
			TagInfo: &fePb.KeyValInfo{
				InfoHeader: FundCategoryToText[fund.CategoryName],
				InfoStr:    FundCategoryDescFromEnum[fund.CategoryName],
			},
		})
		if fund.CategoryName == mfPb.MutualFundCategoryName_ELSS_TAX_SAVING {
			tags = append(tags, &fePb.MutualFundDisplayTag{
				Type:       fePb.MutualFundDisplayTagType_MF_DISPLAY_TAG_CATEGORY,
				DisplayStr: DisplayText3YearLockIn,
			})
		}
	}

	return tags
}

func getThresholdAndTaxes(fund *mfPb.MutualFund, isSipEnabled bool) *fePb.ThresholdsAndTaxes {
	return &fePb.ThresholdsAndTaxes{
		DisplayHeader:    DisplayThresholdAndTaxesHeader,
		TxnThresholdTile: getTxnThreshHoldTile(fund, isSipEnabled),
		PenaltiesTile:    getPenaltiesTile(fund),
		TaxesTile:        getTaxesTile(fund),
	}
}

func getTaxesTile(fund *mfPb.MutualFund) *fePb.TaxesTile {
	desc := getTaxDescription(fund)
	if desc != nil {
		return &fePb.TaxesTile{
			Header:              DisplayTaxesHeader,
			TaxDescriptionLines: desc,
		}
	}
	return nil
}

func getTaxDescription(fund *mfPb.MutualFund) []string {
	if fund.FiContent != nil && fund.FiContent.TaxTreatment != "" {
		allLines := strings.Split(fund.FiContent.TaxTreatment, SeparatorFiContent)
		for i := 0; i < len(allLines); i++ {
			allLines[i] = strings.TrimSpace(allLines[i])
		}
		return allLines
	}
	return nil
}

func getPenaltiesTile(fund *mfPb.MutualFund) *fePb.TransactionPenaltyTile {
	return &fePb.TransactionPenaltyTile{
		Header:                  DisplayEarlySellingPenalty,
		PenaltyDescriptionLines: getPenaltyDescriptionFromFund(fund),
	}
}

// TODO(MIHIR) Add logic for fixed penalty also
func getPenaltyDescriptionFromFund(fund *mfPb.MutualFund) []string {
	resDescs := make([]string, 0)
	if fund.FundFundamentalDetails != nil && fund.FundFundamentalDetails.ExitLoad != nil && fund.FundFundamentalDetails.ExitLoad.Loads != nil {
		loads := fund.FundFundamentalDetails.ExitLoad.Loads
		var loadAfterBreakpointUnit string
		var loadAfterBreakpointValue int32
		minPenalty, maxPenalty := float64(math.MaxInt32), float64(math.MinInt32)
		for i := 0; i < len(loads); i++ {
			currentLoad := loads[i]
			if currentLoad.HighBreakPoint == 0 {
				if currentLoad.LowBreakPoint == 0 {
					return []string{"No penalty on withdrawal"}
				} else {
					loadAfterBreakpointValue = currentLoad.LowBreakPoint
					loadAfterBreakpointUnit = strings.ToLower(currentLoad.BreakpointUnit)
					resDescs = []string{
						fmt.Sprintf("After %v %v: no penalty", currentLoad.LowBreakPoint, loadAfterBreakpointUnit),
					}
				}
			} else {
				minPenalty = math.Min(minPenalty, float64(currentLoad.LoadValue))
				maxPenalty = math.Max(maxPenalty, float64(currentLoad.LoadValue))
			}
		}
		var desc string
		if minPenalty == maxPenalty {
			// for singular time unit, "During the first year, its 2%"
			// else "During the first 2 years, its 2%"
			if loadAfterBreakpointValue == 1 {
				desc = fmt.Sprintf("During the first %v, its %v%%",
					strings.TrimSuffix(loadAfterBreakpointUnit, "s"), getPrecisionString(minPenalty))
			} else {
				desc = fmt.Sprintf("During the first %v %v, its %v%%", loadAfterBreakpointValue,
					loadAfterBreakpointUnit, getPrecisionString(minPenalty))
			}
			resDescs = append(resDescs, desc)
		} else {
			// for singular time unit, "During the first year, its 2% to 4%"
			// else "During the first 2 years, its 2% to 4%"
			if loadAfterBreakpointValue == 1 {
				desc = fmt.Sprintf("During the first %v, its %v%% to %v%%",
					strings.TrimSuffix(loadAfterBreakpointUnit, "s"), getPrecisionString(minPenalty), getPrecisionString(maxPenalty))
			} else {
				desc = fmt.Sprintf("During the first %v %v, its %v%% to %v%%", loadAfterBreakpointValue,
					loadAfterBreakpointUnit, getPrecisionString(minPenalty), getPrecisionString(maxPenalty))
			}
			resDescs = append(resDescs, desc)
		}
		funk.ReverseStrings(resDescs)
	}
	return resDescs
}

func getPrecisionString(penalty float64) string {
	dec, err := decimal.NewFromString(fmt.Sprintf("%.4f", penalty))
	if err != nil {
		return fmt.Sprintf("%.4f", penalty)
	}
	return dec.String()
}

func getTxnThreshHoldTile(fund *mfPb.MutualFund, isSipEnabled bool) *fePb.TransactionThresholdTile {
	return &fePb.TransactionThresholdTile{
		Header: DisplayTxnThresholdHeader,
		Data: []*fePb.KeyValDisplayData{
			{
				KeyName:  DisplayTxnThresholdMinAutoPay,
				KeyValue: calculateMinSipValue(fund.TxnConstraints, isSipEnabled),
			},
			{
				KeyName:  DisplayTxnThresholdMinOneTime,
				KeyValue: calculateMinOneTime(fund),
			},
		},
	}
}

func calculateMinOneTime(fund *mfPb.MutualFund) string {
	if fund.TxnConstraints != nil && fund.TxnConstraints.NewpMnval != nil {
		return money.ToDisplayStringWithPrecision(fund.TxnConstraints.NewpMnval, 0)
	}
	return ""
}

// calculateMinSipValue returns min investment value for sip
func calculateMinSipValue(txnConstraints *mfPb.TransactionConstraints, sipEnabled bool) string {
	if !sipEnabled && txnConstraints.GetNewpMnval() != nil {
		return money.ToDisplayStringWithPrecision(txnConstraints.NewpMnval, 0)
	}
	if sipEnabled && len(txnConstraints.GetAllowedSipFrequencies()) > 0 {
		sipDetails := txnConstraints.GetSipMetadata().GetSiDetails()
		if sipDetails == nil {
			return "_"
		}
		minAmount := int32(-1)
		for _, frequency := range txnConstraints.GetAllowedSipFrequencies() {
			if minAmount == -1 {
				minAmount = txnConstraints.GetSipMetadata().GetSiDetails()[frequency.String()].GetMinAmount()
			} else {
				minAmount = int32(MinInt(int(minAmount), int(txnConstraints.GetSipMetadata().GetSiDetails()[frequency.String()].GetMinAmount())))
			}
		}
		if minAmount != -1 {
			return money.ToDisplayStringWithPrecision(money.AmountINR(int64(minAmount)).GetPb(), 0)
		}
		return "-"
	}
	return "-"
}

func getFundManagement(fund *mfPb.MutualFund) *fePb.FundManagement {
	return &fePb.FundManagement{
		DisplayHeader:            DisplayHeaderFundManagement,
		FundInceptionDescription: getFundInceptionDateString(fund),
		FundManagerTile:          getFundManagerTile(fund),
	}
}

func getFundManagerTile(fund *mfPb.MutualFund) *fePb.FundManagersTile {
	return &fePb.FundManagersTile{
		Header:   DisplayFundManagerHeader,
		InfoStr:  DisplayFundManagersInfo,
		Managers: getFundManagers(fund),
		TileInfo: &fePb.KeyValInfo{
			InfoHeader: DisplayFundManagersInfoHeader,
			InfoStr:    DisplayFundManagersInfo,
		},
	}
}

func getFundManagers(fund *mfPb.MutualFund) []*fePb.KeyValDisplayData {
	resp := make([]*fePb.KeyValDisplayData, 0)
	if fund.FundFundamentalDetails != nil && fund.FundFundamentalDetails.FundManagers != nil {
		managers := fund.FundFundamentalDetails.FundManagers.Managers
		if managers != nil {
			for i := 0; i < len(managers); i++ {
				if managers[i].Role == DisplayLeadManagerRole {
					tmpArr := make([]*fePb.KeyValDisplayData, 0)
					tmpArr = append(tmpArr, &fePb.KeyValDisplayData{
						KeyName:  strings.ToUpper(managers[i].Role),
						KeyValue: fmt.Sprintf("%v, managing since %v", managers[i].Name, managers[i].StartDate.AsTime().Format("02 Jan 2006")),
					})
					resp = append(tmpArr, resp...)
				} else {
					resp = append(resp, &fePb.KeyValDisplayData{
						KeyName:  strings.ToUpper(managers[i].Role),
						KeyValue: fmt.Sprintf("%v, managing since %v", managers[i].Name, managers[i].StartDate.AsTime().Format("02 Jan 2006")),
					})
				}
			}
		}
	}
	return resp
}

func getFundInceptionDateString(fund *mfPb.MutualFund) string {
	if fund.FundFundamentalDetails != nil && fund.FundFundamentalDetails.InceptionDate != nil {
		inceptionDate := fund.FundFundamentalDetails.InceptionDate
		return fmt.Sprintf("This fund was launched on %v", inceptionDate.AsTime().Format("02 Jan 2006"))
	}
	return ""
}

func getFundBreakup(fund *mfPb.MutualFund) *fePb.FundBreakup {
	breakUpData := getFundBreakUpData(fund)
	if len(breakUpData) == 0 {
		return nil
	}
	return &fePb.FundBreakup{
		DisplayHeader:   DisplayFundBreakUpHeader,
		FundBreakupData: breakUpData,
	}
}

// getFundBreakUpData returns the data needed to show fund breakup based on Fund category and Asset class
// For Equity Funds
//   - Holdings(Company Share like Reliance Lmt., HDFC etc)
//   - Sectors(Finance, Technology, Communication Services etc)
//   - Market Cap(Large Cap, Mid Cao, Small Cap, Micro Cap, Giant Cap)
//
// For Hybrid Funds
//   - Holdings(Company Share like Reliance Lmt., HDFC etc)
//   - Sectors(Finance, Technology, Communication Services etc)
//   - Asset Allocation(Cash, Bond, Equity)
//
// For Debt Funds
//   - Credit Quality(A, AAA, B, BB etc)
//   - Maturity BreakDown
//
// nolint:dupl
func getFundBreakUpData(fund *mfPb.MutualFund) map[string]*fePb.FundBreakupData {
	resp := make(map[string]*fePb.FundBreakupData)
	switch fund.AssetClass {
	case mfPb.AssetClass_EQUITY:
		if holdings := getFundBreakupHoldings(fund); holdings != nil && len(holdings.Allocations) > 0 {
			resp[DisplayFundBreakUpHoldings] = holdings
		}
		if sectors := getFundBreakupSectors(fund); sectors != nil && len(sectors.Allocations) > 0 {
			resp[DisplayFundBreakUpSectors] = sectors
		}
		if marketCap := getFundBreakupMarketCap(fund); marketCap != nil && len(marketCap.Allocations) > 0 {
			resp[DisplayFundBreakUpMarketCap] = marketCap
		}
	case mfPb.AssetClass_HYBRID:
		if holdings := getFundBreakupHoldings(fund); holdings != nil && len(holdings.Allocations) > 0 {
			resp[DisplayFundBreakUpHoldings] = holdings
		}
		if sectors := getFundBreakupSectors(fund); sectors != nil && len(sectors.Allocations) > 0 {
			resp[DisplayFundBreakUpSectors] = sectors
		}
		if assetAllocation := getFundBreakupAssetAllocation(fund); assetAllocation != nil && len(assetAllocation.Allocations) > 0 {
			resp[DisplayFundBreakUpAssetAllocation] = assetAllocation
		}
	case mfPb.AssetClass_DEBT:
		if creditQuality := getFundBreakupCreditQuality(fund); creditQuality != nil && len(creditQuality.Allocations) > 0 {
			resp[DisplayFundBreakUpCreditQuality] = creditQuality
		}
		if maturityBreakdown := getFundBreakupMaturity(fund); maturityBreakdown != nil && len(maturityBreakdown.Allocations) > 0 {
			resp[DisplayMaturity] = maturityBreakdown
		}
	default:
		return nil
	}
	return resp
}

func getFundBreakupMarketCap(fund *mfPb.MutualFund) *fePb.FundBreakupData {
	allocations := make([]*fePb.KeyValDisplayData, 0)
	if fund.FundFundamentalDetails != nil && fund.FundFundamentalDetails.MarketCap != nil {
		marketCap := fund.FundFundamentalDetails.MarketCap
		allocations = []*fePb.KeyValDisplayData{
			{
				KeyName:  DisplayFundBreakUpMarketCapGiantCap,
				KeyValue: fmt.Sprintf("%0.2f%%", marketCap.GiantLongRescaled),
			},
			{
				KeyName:  DisplayFundBreakUpMarketCapLargeCap,
				KeyValue: fmt.Sprintf("%0.2f%%", marketCap.LargeLongRescaled),
			},
			{
				KeyName:  DisplayFundBreakUpMarketCapMidCap,
				KeyValue: fmt.Sprintf("%0.2f%%", marketCap.MidLongRescaled),
			},
			{
				KeyName:  DisplayFundBreakUpMarketCapSmallCap,
				KeyValue: fmt.Sprintf("%0.2f%%", marketCap.SmallLongRescaled),
			},
			{
				KeyName:  DisplayFundBreakUpMarketCapMicroCap,
				KeyValue: fmt.Sprintf("%0.2f%%", marketCap.MicroLongRescaled),
			},
		}
		if marketCap.GiantLongRescaled+marketCap.LargeLongRescaled+marketCap.MidLongRescaled+
			marketCap.SmallLongRescaled+marketCap.MicroLongRescaled == 0 {
			return nil
		}
	}
	if len(allocations) == 0 {
		return nil
	}
	return &fePb.FundBreakupData{
		Allocations: allocations,
	}
}

func getFundBreakupSectors(fund *mfPb.MutualFund) *fePb.FundBreakupData {
	allocations := make([]*fePb.KeyValDisplayData, 0)
	if fund.FundFundamentalDetails != nil && fund.FundFundamentalDetails.GlobalEquitySectors != nil {
		sectors := SortStringFloatMap(fund.FundFundamentalDetails.GlobalEquitySectors.EquitySectors)
		for _, pair := range sectors {
			if pair.Value == 0 {
				continue
			}
			allocations = append(allocations, &fePb.KeyValDisplayData{
				KeyName:  EquitySectorToText[pair.Key],
				KeyValue: fmt.Sprintf("%.2f%%", pair.Value),
			})
		}
	}
	if len(allocations) == 0 {
		return nil
	}
	return &fePb.FundBreakupData{
		Allocations: allocations,
	}
}

func getFundBreakupHoldings(fund *mfPb.MutualFund) *fePb.FundBreakupData {
	allocations := make([]*fePb.KeyValDisplayData, 0)
	if fund.FundFundamentalDetails != nil && fund.FundFundamentalDetails.Holdings != nil {
		holdings := SortStringFloatMap(fund.FundFundamentalDetails.Holdings.Holdings)
		isOthersPresent := false
		var othersVal float32 = 0.0
		var othersHolding *fePb.KeyValDisplayData
		for i, val := range holdings {
			// TODO(Mihir): make number of holdings to show config driven
			// show at most NumberOfHoldingsVisibleForFund holdings
			if strings.EqualFold(val.Key, "OTHERS") || i >= NumberOfHoldingsVisibleForFund {
				isOthersPresent = true
				if othersHolding == nil {
					othersHolding = &fePb.KeyValDisplayData{
						KeyName:  DisplayOthers,
						KeyValue: fmt.Sprintf("%.2f%%", othersVal),
					}
				}
				othersVal += val.Value
				othersHolding.KeyValue = fmt.Sprintf("%.2f%%", othersVal)
				continue
			}
			allocations = append(allocations, &fePb.KeyValDisplayData{
				KeyName:  val.Key,
				KeyValue: fmt.Sprintf("%.2f%%", val.Value),
			})
		}
		if isOthersPresent {
			allocations = append(allocations, othersHolding)
		}
	}
	if len(allocations) == 0 {
		return nil
	}
	return &fePb.FundBreakupData{
		Allocations: allocations,
	}
}

func getMutualFundDecisionFactorSections(ctx context.Context, fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage, device *commontypes.Device, isGraphEnabled bool, returnCalculatorReq *ReturnCalculatorReq) []*fePb.Section {
	sections := make([]*fePb.Section, 0)
	keyFactorSections := &fePb.Section{
		Header:        nil,
		IsCollapsible: false,
		IsCollapsed:   false,
	}
	tiles := getDecisionFactorTiles(ctx, fund, categoryAvgData, device, isGraphEnabled, returnCalculatorReq)
	keyFactorSections.Tiles = tiles

	sections = append(sections, keyFactorSections)
	return sections
}

type ReturnCalculatorReq struct {
	Platform            commontypes.Platform
	MaxInvestmentAmount int32
	ReturnGained        float64
	TotalInvestment     *moneyPb.Money
	CurrentValue        *moneyPb.Money
	DefaultAmt          *types.Money
	InvestmentCycle     ReturnCalculatorCycleType
	InvestmentDuration  ReturnCalculatorDurationType
	IsFeatureEnable     bool
}

//nolint:funlen
func getDecisionFactorTiles(ctx context.Context, fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage, device *commontypes.Device, isGraphEnabled bool, returnCalculatorReq *ReturnCalculatorReq) []*fePb.Tile {
	tiles := make([]*fePb.Tile, 0)

	nuggetTile := getNuggetTile(fund)
	//nolint:staticcheck
	if nuggetTile != nil {
		tiles = append(tiles, nuggetTile)
	}

	// either the new return calculator is send to client or send old graph tile
	// if feature is not enable or error while getting return data, then show old graph tile
	if returnCalculatorReq != nil && returnCalculatorReq.IsFeatureEnable {
		investmentCalculatorTile := getReturnCalculator(ctx, fund, returnCalculatorReq, categoryAvgData)
		if investmentCalculatorTile != nil {
			tiles = append(tiles, investmentCalculatorTile)
		}
	} else {
		returnsTile := getReturnGraphTile(fund, categoryAvgData, isGraphEnabled)
		if returnsTile != nil {
			tiles = append(tiles, returnsTile)
		}
	}

	if fund.GetReturns().GetYieldToMaturity() != 0 && (fund.AssetClass == mfPb.AssetClass_DEBT || fund.AssetClass == mfPb.AssetClass_CASH) {
		if device.Platform == commontypes.Platform_IOS {
			expectedInterestRateTile := getExpectedInterestRateTileIOS(fund)
			if expectedInterestRateTile != nil {
				tiles = append(tiles, expectedInterestRateTile)
			}
		} else {
			expectedInterestRateTile := getExpectedInterestRateTileAndroid(fund)
			if expectedInterestRateTile != nil {
				tiles = append(tiles, expectedInterestRateTile)
			}
		}
	}

	unitPriceTile := getUnitPriceTile(ctx, fund)
	if unitPriceTile != nil {
		tiles = append(tiles, unitPriceTile)
	}

	fundHouseExpenseTile := getFundHouseExpenseTile(fund, categoryAvgData)
	if fundHouseExpenseTile != nil {
		tiles = append(tiles, fundHouseExpenseTile)
	}

	fundSizeTile := getAumTile(fund, categoryAvgData)
	if fundSizeTile != nil {
		tiles = append(tiles, fundSizeTile)
	}

	trackingErrTile := getTrackingErrTile(fund, categoryAvgData)
	if trackingErrTile != nil {
		tiles = append(tiles, trackingErrTile)
	}

	alphaTile := getAlphaTile(fund, categoryAvgData)
	if alphaTile != nil {
		tiles = append(tiles, alphaTile)
	}

	sharpeRatioTile := getSharpeRatioTile(fund, categoryAvgData)
	if sharpeRatioTile != nil {
		tiles = append(tiles, sharpeRatioTile)
	}

	creditQualTile := getCreditQualityTile(fund)
	if creditQualTile != nil {
		tiles = append(tiles, creditQualTile)
	}

	interestRateSensitivityTile := getInterestRateSensitivityTile(fund)
	if interestRateSensitivityTile != nil {
		tiles = append(tiles, interestRateSensitivityTile)
	}

	return tiles
}

// nolint:dupl
func getInterestRateSensitivityTile(fund *mfPb.MutualFund) *fePb.Tile {
	if fund.AssetClass != mfPb.AssetClass_DEBT || fund.FundFundamentalDetails == nil ||
		fund.FundFundamentalDetails.BondStyleBox == "" {
		return nil
	}
	bondStyleBox := fund.FundFundamentalDetails.BondStyleBox
	tile := &fePb.Tile{}
	creditQualityTile := &fePb.MultiKeyValTile{
		Header: &fePb.Header{
			Name: DisplayInterestRateSensitivity,
			Info: &fePb.KeyValInfo{
				InfoHeader: DisplayInterestRateSensitivityDescHeader,
				InfoStr:    DisplayInterestRateSensitivityDesc,
			},
		},
		DisplayData: getMultiKeyValData([]*fePb.KeyValDisplayData{
			getKeyValDisplayData(DisplayKeyThisFundKey, FirstHalfOfBondStyleBox[strings.ToUpper(bondStyleBox)], ""),
		}, fePb.DisplayAlignment_HORIZONTAL),
	}
	tile.DisplayTile = &fePb.Tile_MultiKeyValTile{MultiKeyValTile: creditQualityTile}
	return tile
}

// nolint:dupl
func getCreditQualityTile(fund *mfPb.MutualFund) *fePb.Tile {
	if fund.AssetClass != mfPb.AssetClass_DEBT || fund.FundFundamentalDetails == nil ||
		fund.FundFundamentalDetails.BondStyleBox == "" {
		return nil
	}
	bondStyleBox := fund.FundFundamentalDetails.BondStyleBox
	tile := &fePb.Tile{}
	creditQualityTile := &fePb.MultiKeyValTile{
		Header: &fePb.Header{
			Name: DisplayCreditQuality,
			Info: &fePb.KeyValInfo{
				InfoHeader: DisplayCreditQualityDescHeader,
				InfoStr:    DisplayCreditQualityDesc,
			},
		},
		DisplayData: getMultiKeyValData([]*fePb.KeyValDisplayData{
			getKeyValDisplayData(DisplayKeyThisFundKey, SecondHalfOfBondStyleBox[strings.ToUpper(bondStyleBox)], ""),
		}, fePb.DisplayAlignment_HORIZONTAL),
	}
	tile.DisplayTile = &fePb.Tile_MultiKeyValTile{MultiKeyValTile: creditQualityTile}
	return tile
}

func getSharpeRatioTile(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) *fePb.Tile {
	if fund.AssetClass != mfPb.AssetClass_HYBRID || fund.PerformanceMetrics == nil ||
		fund.PerformanceMetrics.SharpeRatio == nil {
		return nil
	}
	sharpeRatio := fund.PerformanceMetrics.SharpeRatio
	tile := &fePb.Tile{
		TimeFrameDisplayData: timeFrameDisplayData,
		SortedTimeFrames:     SortedTimeFrames,
	}
	timeframeWiseData := make(map[string]*fePb.MultiKeyValData)

	sharpeRatioTile := &fePb.MultiTimeFrameTile{
		Header: &fePb.Header{
			Name: DisplaySharpeRatio,
			Info: &fePb.KeyValInfo{
				InfoHeader: DisplaySharpeRatioDescHeader,
				InfoStr:    DisplaySharpeRatioDesc,
			},
		},
		TimeFrameWiseData: timeframeWiseData,
	}
	if data := getPercentFundAndCatAvgData(sharpeRatio.FundOneYear, categoryAvgData.GetSharpeRatio().GetCategoryAvgOneYear()); data != nil {
		relativeRating := calculateRelativeRating(fund.GetPerformanceMetrics().GetSharpeRatio().GetPercentileRankOneYear())
		data = dataAfterRelativeRating(relativeRating, data)
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_1_YR.String()] = data
	}
	if data := getPercentFundAndCatAvgData(sharpeRatio.FundThreeYear, categoryAvgData.GetSharpeRatio().GetCategoryAvgThreeYear()); data != nil {
		relativeRating := calculateRelativeRating(fund.GetPerformanceMetrics().GetSharpeRatio().GetPercentileRankThreeYear())
		data = dataAfterRelativeRating(relativeRating, data)
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_3_YR.String()] = data
	}
	if data := getPercentFundAndCatAvgData(sharpeRatio.FundFiveYear, categoryAvgData.GetSharpeRatio().GetCategoryAvgFiveYear()); data != nil {
		relativeRating := calculateRelativeRating(fund.GetPerformanceMetrics().GetSharpeRatio().GetPercentileRankFiveYear())
		data = dataAfterRelativeRating(relativeRating, data)
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_5_YR.String()] = data
	}
	if len(timeframeWiseData) == 0 {
		return nil
	}
	tile.DisplayTile = &fePb.Tile_MultiTimeFrameTile{MultiTimeFrameTile: sharpeRatioTile}
	return tile
}

func getAlphaTile(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) *fePb.Tile {
	if fund.AssetClass != mfPb.AssetClass_EQUITY || fund.CategoryName == mfPb.MutualFundCategoryName_INDEX_FUNDS ||
		fund.PerformanceMetrics == nil || fund.PerformanceMetrics.Alpha == nil {
		return nil
	}
	tile := &fePb.Tile{
		TimeFrameDisplayData: timeFrameDisplayData,
		SortedTimeFrames:     SortedTimeFrames,
	}
	alpha := fund.PerformanceMetrics.Alpha
	timeframeWiseData := make(map[string]*fePb.MultiKeyValData)

	alphaTile := &fePb.MultiTimeFrameTile{
		Header: &fePb.Header{
			Name: DisplayAlpha,
			Info: &fePb.KeyValInfo{
				InfoHeader: DisplayAlphaDescHeader,
				InfoStr:    DisplayAlphaDesc,
			},
		},
		TimeFrameWiseData: timeframeWiseData,
	}
	if data := getPercentFundAndCatAvgData(alpha.FundOneYear, categoryAvgData.GetAlpha().GetCategoryAvgOneYear()); data != nil {
		relativeRating := calculateRelativeRating(fund.GetPerformanceMetrics().GetAlpha().GetPercentileRankOneYear())
		data = dataAfterRelativeRating(relativeRating, data)
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_1_YR.String()] = data
	}
	if data := getPercentFundAndCatAvgData(alpha.FundThreeYear, categoryAvgData.GetAlpha().GetCategoryAvgThreeYear()); data != nil {
		relativeRating := calculateRelativeRating(fund.GetPerformanceMetrics().GetAlpha().GetPercentileRankThreeYear())
		data = dataAfterRelativeRating(relativeRating, data)
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_3_YR.String()] = data
	}
	if data := getPercentFundAndCatAvgData(alpha.FundFiveYear, categoryAvgData.GetAlpha().GetCategoryAvgFiveYear()); data != nil {
		relativeRating := calculateRelativeRating(fund.GetPerformanceMetrics().GetAlpha().GetPercentileRankFiveYear())
		data = dataAfterRelativeRating(relativeRating, data)
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_5_YR.String()] = data
	}
	if len(timeframeWiseData) == 0 {
		return nil
	}
	tile.DisplayTile = &fePb.Tile_MultiTimeFrameTile{MultiTimeFrameTile: alphaTile}
	return tile
}

func getTrackingErrTile(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) *fePb.Tile {
	if fund.CategoryName != mfPb.MutualFundCategoryName_INDEX_FUNDS || fund.PerformanceMetrics == nil ||
		fund.PerformanceMetrics.TrackingError == nil {
		return nil
	}
	tile := &fePb.Tile{
		TimeFrameDisplayData: timeFrameDisplayData,
		SortedTimeFrames:     SortedTimeFrames,
	}
	trackingErrData := fund.PerformanceMetrics.TrackingError
	timeframeWiseData := make(map[string]*fePb.MultiKeyValData)
	trackingErrTile := &fePb.MultiTimeFrameTile{
		Header: &fePb.Header{
			Name: DisplayTrackingErrorHeader,
			Info: &fePb.KeyValInfo{
				InfoHeader: DisplayTrackingErrorDescHeader,
				InfoStr:    DisplayTrackingErrorDesc,
			},
		},
		TimeFrameWiseData: timeframeWiseData,
	}
	if data := getPercentFundAndCatAvgData(trackingErrData.TrackingErrorOneYear,
		categoryAvgData.GetTrackingError().GetCatAvgTrackingErrorOneYear()); data != nil {
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_1_YR.String()] = data
	}
	if data := getPercentFundAndCatAvgData(trackingErrData.TrackingErrorThreeYear,
		categoryAvgData.GetTrackingError().CatAvgTrackingErrorThreeYear); data != nil {
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_3_YR.String()] = data
	}
	if data := getPercentFundAndCatAvgData(trackingErrData.TrackingErrorFiveYear,
		categoryAvgData.GetTrackingError().CatAvgTrackingErrorFiveYear); data != nil {
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_5_YR.String()] = data
	}
	if len(timeframeWiseData) == 0 {
		return nil
	}
	tile.DisplayTile = &fePb.Tile_MultiTimeFrameTile{MultiTimeFrameTile: trackingErrTile}
	return tile
}

func getAumTile(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) *fePb.Tile {
	if fund.FundFundamentalDetails == nil || fund.FundFundamentalDetails.Aum == nil {
		return nil
	}
	tile := &fePb.Tile{}
	aumTile := &fePb.MultiKeyValTile{
		Header: &fePb.Header{
			Name: DisplayFundSizeHeader,
			Info: &fePb.KeyValInfo{
				InfoHeader: DisplayFundSizeInfoHeader,
				InfoStr:    DisplayFundSizeInfo,
			},
		},
		DisplayData: getMultiKeyValData(getFundSizeData(fund, categoryAvgData), fePb.DisplayAlignment_HORIZONTAL),
	}
	tile.DisplayTile = &fePb.Tile_MultiKeyValTile{
		MultiKeyValTile: aumTile,
	}
	return tile
}

// nolint:dupl
func getFundHouseExpenseTile(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) *fePb.Tile {
	if fund.FundFundamentalDetails == nil || fund.FundFundamentalDetails.ExpenseRatio == nil || categoryAvgData.GetExpenseRatio() == nil {
		return nil
	}
	expenseRatios := fund.GetFundFundamentalDetails().GetExpenseRatio()
	tile := &fePb.Tile{
		TimeFrameDisplayData: timeFrameDisplayData,
		SortedTimeFrames:     SortedTimeFrames,
	}
	timeframeWiseData := make(map[string]*fePb.MultiKeyValData)
	expRatioTile := &fePb.MultiTimeFrameTile{
		Header: &fePb.Header{
			Name: DisplayExpenseTileHeader,
			Info: &fePb.KeyValInfo{
				InfoHeader: DisplayExpenseTileInfoHeader,
				InfoStr:    DisplayExpenseTileInfo,
			},
		},
		TimeFrameWiseData: timeframeWiseData,
	}
	if data := getFundExpenseRatioAndCatAvgData(expenseRatios.GetFundCurrent(), categoryAvgData.GetExpenseRatio().GetCategoryCurrent()); data != nil {
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_NOW.String()] = data
	}
	if data := getFundExpenseRatioAndCatAvgData(expenseRatios.GetFundOneYearAvg(), categoryAvgData.GetExpenseRatio().GetCategoryOneYearAvg()); data != nil {
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_1_YR.String()] = data
	}
	if data := getFundExpenseRatioAndCatAvgData(expenseRatios.GetFundThreeYearAvg(), categoryAvgData.GetExpenseRatio().GetCategoryThreeYearAvg()); data != nil {
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_3_YR.String()] = data
	}
	if len(timeframeWiseData) == 0 {
		return nil
	}
	tile.DisplayTile = &fePb.Tile_MultiTimeFrameTile{MultiTimeFrameTile: expRatioTile}
	return tile
}

func getFundExpenseRatioAndCatAvgData(fundVal float32, categoryVal float32) *fePb.MultiKeyValData {
	ratios := make([]*fePb.KeyValDisplayData, 0)
	if fundVal != 0 {
		ratios = append(ratios, getKeyValDisplayData(DisplayKeyThisFundKey, fmt.Sprintf("%.2f", fundVal)+"%", ""))
		if categoryVal != 0 {
			ratios = append(ratios, getKeyValDisplayData(DisplayKeyCatAvgKey, fmt.Sprintf("%.2f", categoryVal)+"%", ""))
		}
		return getMultiKeyValData(ratios, fePb.DisplayAlignment_HORIZONTAL)
	} else if categoryVal != 0 {
		ratios = append(ratios, getKeyValDisplayData(DisplayKeyThisFundKey, DisplayDash, ""),
			getKeyValDisplayData(DisplayKeyCatAvgKey, fmt.Sprintf("%.2f", categoryVal)+"%", ""))
		return getMultiKeyValData(ratios, fePb.DisplayAlignment_HORIZONTAL)
	}
	return nil
}

func getPercentFundAndCatAvgData(fundVal float32, categoryVal float32) *fePb.MultiKeyValData {
	ratios := make([]*fePb.KeyValDisplayData, 0)
	if fundVal != 0 {
		ratios = append(ratios, getKeyValDisplayData(DisplayKeyThisFundKey, fmt.Sprintf("%.2f", fundVal)+"%", ""))
		if categoryVal != 0 {
			ratios = append(ratios, getKeyValDisplayData(DisplayKeyCatAvgKey, fmt.Sprintf("%.2f", categoryVal)+"%", ""))
		}
		return getMultiKeyValData(ratios, fePb.DisplayAlignment_HORIZONTAL)
	}
	return nil
}

// nolint:unparam
func getMultiKeyValData(data []*fePb.KeyValDisplayData, alignment fePb.DisplayAlignment) *fePb.MultiKeyValData {
	return &fePb.MultiKeyValData{
		MultiKeyValData: data,
		Alignment:       alignment,
	}
}

func getUnitPriceTile(ctx context.Context, fund *mfPb.MutualFund) *fePb.Tile {
	amt, err := mfUtils.GetAmountFromMoney(ctx, fund.Nav)
	if err != nil {
		return nil
	}
	tile := &fePb.Tile{
		DisplayTile: &fePb.Tile_PercentageChangeTile{PercentageChangeTile: &fePb.PercentageChangeTile{
			Header: &fePb.Header{
				Name: DisplayUnitPrice,
				Info: &fePb.KeyValInfo{
					InfoHeader: DisplayUnitPrice,
					InfoStr:    DisplayUnitPriceInfoDesc,
				},
			},
			ChangeDescription: fmt.Sprintf("Price of a single unit (NAV) is ₹%.2f", amt),
			ChangePercent:     mfUtils.CalculateNavChangePercentage(ctx, fund),
		}},
	}
	return tile
}

func getReturnGraphTile(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage, isGraphEnabled bool) *fePb.Tile {
	if fund.Returns == nil && categoryAvgData.GetReturns() == nil {
		return nil
	}
	tile := &fePb.Tile{
		TimeFrameDisplayData: timeFrameDisplayData,
		SortedTimeFrames:     SortedTimeFrames,
	}
	timeFrameTile := &fePb.MultiTimeFrameWithGraphTile{
		Header: &fePb.Header{
			Name: DisplayReturnsTileHeader,
			Info: &fePb.KeyValInfo{
				InfoHeader: DisplayReturnsTileInfoHeader,
				InfoStr:    DisplayReturnsTileInfo,
			},
		},
		TimeFrameWiseData: make(map[string]*fePb.MultiKeyValWithGraphData),
		CalculatorTile:    getCalculatorTile(fund, categoryAvgData, isGraphEnabled),
	}
	tile.DisplayTile = &fePb.Tile_MultiTimeFrameGraphTile{MultiTimeFrameGraphTile: timeFrameTile}
	timeframes := []fePb.TimeFrame{fePb.TimeFrame_TIMEFRAME_1_YR, fePb.TimeFrame_TIMEFRAME_5_YR,
		fePb.TimeFrame_TIMEFRAME_3_YR, fePb.TimeFrame_TIMEFRAME_1_MTH, fePb.TimeFrame_TIMEFRAME_6_MTH}
	for _, timeFrame := range timeframes {
		fillTimeframeWiseData(timeFrameTile, fund, categoryAvgData, isGraphEnabled, timeFrame)
	}
	timeFrameVsReturnsMap := getTimeFrameVsReturnsMap(fund)
	timeFrameTile.DefaultTimeframe = getDefaultReturnTimeFrame(fund, timeFrameVsReturnsMap)
	return tile
}

func getDefaultReturnTimeFrame(fund *mfPb.MutualFund, timeFrameVsFundReturnMap map[fePb.TimeFrame]float32) string {
	switch fund.AssetClass {
	case mfPb.AssetClass_HYBRID:
		return getTimeFrameWithMaxReturn(timeFrameVsFundReturnMap, timeFramesHybrid)
	case mfPb.AssetClass_EQUITY:
		return getTimeFrameWithMaxReturn(timeFrameVsFundReturnMap, timeFramesEquity)
	case mfPb.AssetClass_DEBT:
		return getTimeFrameWithMaxReturn(timeFrameVsFundReturnMap, timeFramesDebt)
	case mfPb.AssetClass_CASH:
		return getTimeFrameWithMaxReturn(timeFrameVsFundReturnMap, timeFramesCash)
	default:
		return fePb.TimeFrame_TIMEFRAME_1_YR.String()
	}
}
func getReturnMultiKeyValWithGraphData(fundAvg float32, catAvg float32, fundReturnsGraph *fePb.MultiKeyValNumericData,
	catAvgReturnsGraph *fePb.MultiKeyValNumericData, months int) *fePb.MultiKeyValWithGraphData {
	returns := make([]*fePb.KeyValDisplayData, 0)
	durationText := ""
	growthText := ""
	switch {
	case months == 12:
		durationText = "year"
	case months > 12:
		durationText = "years"
	case months == 1:
		durationText = "month"
	case months > 1 && months < 12:
		durationText = "months"
	default:
	}
	duration := months/12 + months%12
	growthText = fmt.Sprintf("₹%v invested every month over %v %s", DefaultInvestmentAmountForGraph, duration, durationText)
	if fundAvg == 0 && catAvg == 0 {
		return nil
	}
	if fundAvg != 0 {
		returns = append(returns, getKeyValDisplayData(DisplayKeyFundAvgKey, fmt.Sprintf("%.2f", fundAvg)+"% p.a.", ThisFundReturnColor))

		if catAvg != 0 {
			returns = append(returns, getKeyValDisplayData(DisplayKeyCatAvgKey, fmt.Sprintf("%.2f", catAvg)+"% p.a.", CategoryReturnColor))
		}
		return getMultiKeyValWithGraphData(returns, fePb.DisplayAlignment_HORIZONTAL, fundReturnsGraph, catAvgReturnsGraph, growthText)
	} else if catAvg != 0 {
		returns = append(returns, getKeyValDisplayData(DisplayKeyFundAvgKey, DisplayDash, ThisFundReturnColor),
			getKeyValDisplayData(DisplayKeyCatAvgKey, fmt.Sprintf("%.2f", catAvg)+"% p.a.", CategoryReturnColor))
		return getMultiKeyValWithGraphData(returns, fePb.DisplayAlignment_HORIZONTAL, fundReturnsGraph, catAvgReturnsGraph, growthText)
	}
	return nil
}

func getMultiKeyValWithGraphData(returns []*fePb.KeyValDisplayData, alignment fePb.DisplayAlignment, fundReturnGraph *fePb.MultiKeyValNumericData,
	catAvgGraph *fePb.MultiKeyValNumericData, growthText string) *fePb.MultiKeyValWithGraphData {
	return &fePb.MultiKeyValWithGraphData{
		MultiKeyValData: &fePb.MultiKeyValData{
			MultiKeyValData: returns,
			Alignment:       alignment,
		},
		Graphs: []*fePb.MultiKeyValNumericData{
			fundReturnGraph,
			catAvgGraph,
		},
		GrowthText: growthText,
	}
}

func getKeyValDisplayData(key string, val string, color string) *fePb.KeyValDisplayData {
	return &fePb.KeyValDisplayData{
		KeyName:  key,
		KeyValue: val,
		KeyColor: color,
	}
}

func getNuggetTile(fund *mfPb.MutualFund) *fePb.Tile {
	if fund.FiContent == nil || len(fund.GetFiContent().GetNuggets()) == 0 {
		return nil
	}
	tile := &fePb.Tile{}
	nuggetTile := &fePb.NuggetsTile{}
	nuggets := fund.GetFiContent().GetNuggets()
	tags := make([]*fePb.MutualFundDisplayTag, len(nuggets))
	for i, nugget := range nuggets {
		tags[i] = &fePb.MutualFundDisplayTag{
			DisplayStr:       nugget.GetNuggetText(),
			BackgroundColour: getBackgroundColorForNugget(nugget),
			TextColour:       getTextColourForNugget(nugget),
		}
	}
	nuggetTile.DisplayTags = tags
	tile.DisplayTile = &fePb.Tile_NuggetsTile{NuggetsTile: nuggetTile}
	return tile
}

func getTextColourForNugget(nugget *mfPb.Nugget) string {
	switch nugget.Sentiment {
	case mfPb.NuggetSentiment_POSITIVE:
		return PositiveSentimentTextColor
	case mfPb.NuggetSentiment_NEGATIVE:
		return NegativeSentimentTextColor
	case mfPb.NuggetSentiment_NEUTRAL:
		return NeutralSentimentTextColor
	default:
		return ""
	}
}

func getBackgroundColorForNugget(nugget *mfPb.Nugget) string {
	switch nugget.Sentiment {
	case mfPb.NuggetSentiment_POSITIVE:
		return PositiveSentimentBgColor
	case mfPb.NuggetSentiment_NEGATIVE:
		return NegativeSentimentBgColor
	case mfPb.NuggetSentiment_NEUTRAL:
		return NeutralSentimentBgColor
	default:
		return ""
	}
}

// nolint:funlen
func getMutualFundDetailsSections(ctx context.Context, fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage, sipEnabled bool) []*fePb.Section {
	sections := make([]*fePb.Section, 0)

	thresholdSections := &fePb.Section{
		Header:        &fePb.Header{Name: DisplayThresholdAndTaxesHeader},
		IsCollapsible: true,
		IsCollapsed:   false,
	}
	if tiles := getThresholdSectionsTiles(ctx, fund, sipEnabled); len(tiles) != 0 {
		thresholdSections.Tiles = tiles
		sections = append(sections, thresholdSections)
	}

	fundManagementSection := &fePb.Section{
		Header:        &fePb.Header{Name: DisplayHeaderFundManagement},
		IsCollapsible: true,
		IsCollapsed:   false,
	}
	if tiles := getFundManagementTiles(ctx, fund); len(tiles) != 0 {
		fundManagementSection.Tiles = tiles
		sections = append(sections, fundManagementSection)
	}

	fundBreakupSection := &fePb.Section{
		Header:        &fePb.Header{Name: DisplayFundBreakUpHeader},
		IsCollapsible: true,
		IsCollapsed:   false,
	}
	if tiles := getFundBreakupTiles(ctx, fund); len(tiles) != 0 {
		fundBreakupSection.Tiles = tiles
		sections = append(sections, fundBreakupSection)
	}

	if fund.AssetClass == mfPb.AssetClass_EQUITY && fund.CategoryName != mfPb.MutualFundCategoryName_INDEX_FUNDS {
		advancedSection := &fePb.Section{
			Header:        &fePb.Header{Name: DisplayAdvanced},
			IsCollapsible: true,
			IsCollapsed:   false,
		}
		if tiles := getAdvancedTiles(ctx, fund, categoryAvgData); len(tiles) != 0 {
			advancedSection.Tiles = tiles
			sections = append(sections, advancedSection)
		}
	}

	if fund.AssetClass == mfPb.AssetClass_DEBT {
		nerdStatsSection := &fePb.Section{
			Header:        &fePb.Header{Name: DisplayNerdStats},
			IsCollapsible: true,
			IsCollapsed:   false,
		}
		if tiles := getNerdStatsTiles(ctx, fund, categoryAvgData); len(tiles) != 0 {
			nerdStatsSection.Tiles = tiles
			sections = append(sections, nerdStatsSection)
		}
	}

	if fund.FundFundamentalDetails != nil && fund.FundFundamentalDetails.SchemeDocument != "" {
		sections = append(sections, &fePb.Section{
			Header:        nil,
			IsCollapsible: false,
			IsCollapsed:   false,
			Tiles: []*fePb.Tile{
				{
					DisplayTile: &fePb.Tile_UrlTile{UrlTile: &fePb.UrlTile{
						Url:      fund.FundFundamentalDetails.SchemeDocument,
						IconUrl:  IconUrl,
						UrlTitle: DisplayDocumentTitle,
					}},
				},
			},
		})
	}

	return sections
}

// nolint:unparam
func getNerdStatsTiles(ctx context.Context, fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) []*fePb.Tile {
	tiles := make([]*fePb.Tile, 0)
	infoRatioTile := getInformationRatioTile(fund, categoryAvgData)
	if infoRatioTile != nil {
		tiles = append(tiles, infoRatioTile)
	}
	return tiles
}

// nolint:unparam
func getAdvancedTiles(ctx context.Context, fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) []*fePb.Tile {
	tiles := make([]*fePb.Tile, 0)
	sharpeRatioTile := getSharpeRatioTile(fund, categoryAvgData)
	if sharpeRatioTile != nil {
		tiles = append(tiles, sharpeRatioTile)
	}
	informationRatioTile := getInformationRatioTile(fund, categoryAvgData)
	if informationRatioTile != nil {
		tiles = append(tiles, informationRatioTile)
	}

	return tiles
}

// nolint:dupl
func getInformationRatioTile(fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage) *fePb.Tile {
	if fund.PerformanceMetrics == nil || fund.PerformanceMetrics.InformationRatio == nil {
		return nil
	}
	informationRatio := fund.PerformanceMetrics.InformationRatio
	tile := &fePb.Tile{
		TimeFrameDisplayData: timeFrameDisplayData,
		SortedTimeFrames:     SortedTimeFrames,
	}
	timeframeWiseData := make(map[string]*fePb.MultiKeyValData)
	infoRatioTile := &fePb.MultiTimeFrameTile{
		Header: &fePb.Header{
			Name: DisplayInfoRatio,
			Info: &fePb.KeyValInfo{
				InfoHeader: DisplayInfoRatioDescHeader,
				InfoStr:    DisplayInfoRatioDesc,
			},
		},
		TimeFrameWiseData: timeframeWiseData,
	}
	if data := getPercentFundAndCatAvgData(informationRatio.FundOneYear, categoryAvgData.GetInformationRatio().GetCategoryAvgOneYear()); data != nil {
		relativeRating := calculateRelativeRating(fund.GetPerformanceMetrics().GetInformationRatio().GetPercentileRankOneYear())
		data = dataAfterRelativeRating(relativeRating, data)
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_1_YR.String()] = data
	}
	if data := getPercentFundAndCatAvgData(informationRatio.FundThreeYear, categoryAvgData.GetInformationRatio().GetCategoryAvgThreeYear()); data != nil {
		relativeRating := calculateRelativeRating(fund.GetPerformanceMetrics().GetInformationRatio().GetPercentileRankThreeYear())
		data = dataAfterRelativeRating(relativeRating, data)
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_3_YR.String()] = data
	}
	if data := getPercentFundAndCatAvgData(informationRatio.FundFiveYear, categoryAvgData.GetInformationRatio().GetCategoryAvgFiveYear()); data != nil {
		relativeRating := calculateRelativeRating(fund.GetPerformanceMetrics().GetInformationRatio().GetPercentileRankFiveYear())
		data = dataAfterRelativeRating(relativeRating, data)
		timeframeWiseData[fePb.TimeFrame_TIMEFRAME_5_YR.String()] = data
	}
	if len(timeframeWiseData) == 0 {
		return nil
	}
	tile.DisplayTile = &fePb.Tile_MultiTimeFrameTile{MultiTimeFrameTile: infoRatioTile}
	return tile
}

// nolint:unparam
func getFundBreakupTiles(ctx context.Context, fund *mfPb.MutualFund) []*fePb.Tile {
	tiles := make([]*fePb.Tile, 0)

	breakUpTile := getFundBreakupsTile(fund)
	if breakUpTile != nil {
		tiles = append(tiles, breakUpTile)
	}

	return tiles
}

func getFundBreakupsTile(fund *mfPb.MutualFund) *fePb.Tile {
	tile := &fePb.Tile{}
	fundBreakUpData, fundBreakupOn := getFundBreakupDataV2(fund)
	if len(fundBreakUpData) == 0 || len(fundBreakupOn) == 0 {
		return nil
	}

	fundBreakupTile := &fePb.TabbedTile{
		TabData:  fundBreakUpData,
		TabOrder: fundBreakupOn,
	}
	tile.DisplayTile = &fePb.Tile_TabbedTile{TabbedTile: fundBreakupTile}
	return tile
}

// getFundBreakupDataV2 returns the data needed to show fund breakup based on Fund category and Asset class
// Note: This function is to support new mutual fund 3 tabs view getFundBreakupData is now deprecated
// For Equity Funds
//   - Holdings(Company Share like Reliance Lmt., HDFC etc)
//   - Sectors(Finance, Technology, Communication Services etc)
//   - Market Cap(Large Cap, Mid Cao, Small Cap, Micro Cap, Giant Cap)
//
// For Hybrid Funds
//   - Holdings(Company Share like Reliance Lmt., HDFC etc)
//   - Sectors(Finance, Technology, Communication Services etc)
//   - Asset Allocation(Cash, Bond, Equity)
//
// For Debt Funds
//   - Credit Quality(A, AAA, B, BB etc)
//   - Maturity BreakDown
//
// nolint:dupl
func getFundBreakupDataV2(fund *mfPb.MutualFund) (map[string]*fePb.MultiKeyValData, []string) {
	breakupTab := make(map[string]*fePb.MultiKeyValData)
	tabOrder := make([]string, 0)
	switch fund.AssetClass {
	case mfPb.AssetClass_EQUITY:
		if holdings := getFundBreakupHoldings(fund); holdings != nil && len(holdings.Allocations) > 0 {
			breakupTab[DisplayFundBreakUpHoldings] = getMultiKeyValData(holdings.Allocations, fePb.DisplayAlignment_VERTICAL)
			tabOrder = append(tabOrder, DisplayFundBreakUpHoldings)
		}
		if sectors := getFundBreakupSectors(fund); sectors != nil && len(sectors.Allocations) > 0 {
			breakupTab[DisplayFundBreakUpSectors] = getMultiKeyValData(sectors.Allocations, fePb.DisplayAlignment_VERTICAL)
			tabOrder = append(tabOrder, DisplayFundBreakUpSectors)
		}
		if marketCap := getFundBreakupMarketCap(fund); marketCap != nil && len(marketCap.Allocations) > 0 {
			breakupTab[DisplayFundBreakUpMarketCap] = getMultiKeyValData(marketCap.Allocations, fePb.DisplayAlignment_VERTICAL)
			tabOrder = append(tabOrder, DisplayFundBreakUpMarketCap)
		}
	case mfPb.AssetClass_HYBRID:
		if holdings := getFundBreakupHoldings(fund); holdings != nil && len(holdings.Allocations) > 0 {
			breakupTab[DisplayFundBreakUpHoldings] = getMultiKeyValData(holdings.Allocations, fePb.DisplayAlignment_VERTICAL)
			tabOrder = append(tabOrder, DisplayFundBreakUpHoldings)
		}
		if sectors := getFundBreakupSectors(fund); sectors != nil && len(sectors.Allocations) > 0 {
			breakupTab[DisplayFundBreakUpSectors] = getMultiKeyValData(sectors.Allocations, fePb.DisplayAlignment_VERTICAL)
			tabOrder = append(tabOrder, DisplayFundBreakUpSectors)
		}
		if assetAllocation := getFundBreakupAssetAllocation(fund); assetAllocation != nil && len(assetAllocation.Allocations) > 0 {
			breakupTab[DisplayFundBreakUpAssetAllocation] = getMultiKeyValData(assetAllocation.Allocations, fePb.DisplayAlignment_VERTICAL)
			tabOrder = append(tabOrder, DisplayFundBreakUpAssetAllocation)
		}
	case mfPb.AssetClass_DEBT:
		if creditQuality := getFundBreakupCreditQuality(fund); creditQuality != nil && len(creditQuality.Allocations) > 0 {
			breakupTab[DisplayFundBreakUpCreditQuality] = getMultiKeyValData(creditQuality.Allocations, fePb.DisplayAlignment_VERTICAL)
			tabOrder = append(tabOrder, DisplayFundBreakUpCreditQuality)
		}
		if maturityBreakdown := getFundBreakupMaturity(fund); maturityBreakdown != nil && len(maturityBreakdown.Allocations) > 0 {
			breakupTab[DisplayMaturity] = getMultiKeyValData(maturityBreakdown.Allocations, fePb.DisplayAlignment_VERTICAL)
			tabOrder = append(tabOrder, DisplayMaturity)
		}
	default:
		return nil, nil
	}

	return breakupTab, tabOrder
}

func getFundBreakupAssetAllocation(fund *mfPb.MutualFund) *fePb.FundBreakupData {
	allocations := make([]*fePb.KeyValDisplayData, 0)
	if fund.FundFundamentalDetails != nil && fund.FundFundamentalDetails.AssetAllocationBreakdown != nil {
		assetAllocation := fund.FundFundamentalDetails.AssetAllocationBreakdown
		allocations = []*fePb.KeyValDisplayData{
			{
				KeyName:  DisplayBonds,
				KeyValue: fmt.Sprintf("%0.2f%%", assetAllocation.BondNet),
			},
			{
				KeyName:  DisplayCash,
				KeyValue: fmt.Sprintf("%0.2f%%", assetAllocation.CashNet),
			},
			{
				KeyName:  DisplayEquity,
				KeyValue: fmt.Sprintf("%0.2f%%", assetAllocation.EquityNet),
			},
			{
				KeyName:  DisplayOthers,
				KeyValue: fmt.Sprintf("%0.2f%%", assetAllocation.OtherNet),
			},
		}
	}
	if len(allocations) == 0 {
		return nil
	}
	return &fePb.FundBreakupData{
		Allocations: allocations,
	}
}

func getFundBreakupCreditQuality(fund *mfPb.MutualFund) *fePb.FundBreakupData {
	allocations := make([]*fePb.KeyValDisplayData, 0)
	if fund.FundFundamentalDetails != nil && fund.FundFundamentalDetails.CreditQualityBreakdown != nil {
		creditQualBreakDown := fund.FundFundamentalDetails.CreditQualityBreakdown
		allocations = []*fePb.KeyValDisplayData{
			{
				KeyName:  DisplayAAA,
				KeyValue: fmt.Sprintf("%0.2f%%", creditQualBreakDown.CreditQualAaa),
			},
			{
				KeyName:  DisplayAA,
				KeyValue: fmt.Sprintf("%0.2f%%", creditQualBreakDown.CreditQualAa),
			},
			{
				KeyName:  DisplayA,
				KeyValue: fmt.Sprintf("%0.2f%%", creditQualBreakDown.CreditQualA),
			},
			{
				KeyName:  DisplayBBB,
				KeyValue: fmt.Sprintf("%0.2f%%", creditQualBreakDown.CreditQualBbb),
			},
			{
				KeyName:  DisplayBB,
				KeyValue: fmt.Sprintf("%0.2f%%", creditQualBreakDown.CreditQualBb),
			},
			{
				KeyName:  DisplayB,
				KeyValue: fmt.Sprintf("%0.2f%%", creditQualBreakDown.CreditQualB),
			},
			{
				KeyName:  DisplayBelowB,
				KeyValue: fmt.Sprintf("%0.2f%%", creditQualBreakDown.CreditQualBelowB),
			},
			{
				KeyName:  DisplayNotRated,
				KeyValue: fmt.Sprintf("%0.2f%%", creditQualBreakDown.CreditQualNotrated),
			},
		}
	}
	if len(allocations) == 0 {
		return nil
	}
	return &fePb.FundBreakupData{
		Allocations: allocations,
	}
}

// nolint:funlen
func getFundBreakupMaturity(fund *mfPb.MutualFund) *fePb.FundBreakupData {
	allocations := make([]*fePb.KeyValDisplayData, 0)
	if fund.TxnConstraints != nil && fund.TxnConstraints.MaturityBreakdown != nil {
		maturityBreakDown := fund.TxnConstraints.MaturityBreakdown
		tmpAllocations := []*fePb.KeyValDisplayData{
			{
				KeyName:  DisplayBreakdown1To7Days,
				KeyValue: fmt.Sprintf("%0.2f%%", maturityBreakDown.Breakdown_1To7Days),
			},
			{
				KeyName:  DisplayBreakdown8To30Days,
				KeyValue: fmt.Sprintf("%0.2f%%", maturityBreakDown.Breakdown_8To30Days),
			},
			{
				KeyName:  DisplayBreakdown31To90Days,
				KeyValue: fmt.Sprintf("%0.2f%%", maturityBreakDown.Breakdown_31To90Days),
			},
			{
				KeyName:  DisplayBreakdown91To182Days,
				KeyValue: fmt.Sprintf("%0.2f%%", maturityBreakDown.Breakdown_91To182Days),
			},
			{
				KeyName:  DisplayBreakdown183To364Days,
				KeyValue: fmt.Sprintf("%0.2f%%", maturityBreakDown.Breakdown_183To364Days),
			},
			{
				KeyName:  DisplayBreakdown1To3Years,
				KeyValue: fmt.Sprintf("%0.2f%%", maturityBreakDown.Breakdown_1To3Years),
			},
			{
				KeyName:  DisplayBreakdown3To5Years,
				KeyValue: fmt.Sprintf("%0.2f%%", maturityBreakDown.Breakdown_3To5Years),
			},
			{
				KeyName:  DisplayBreakdown5To7Years,
				KeyValue: fmt.Sprintf("%0.2f%%", maturityBreakDown.Breakdown_5To7Years),
			},
			{
				KeyName:  DisplayBreakdown7To10Years,
				KeyValue: fmt.Sprintf("%0.2f%%", maturityBreakDown.Breakdown_7To10Years),
			},
			{
				KeyName:  DisplayBreakdown10To15Years,
				KeyValue: fmt.Sprintf("%0.2f%%", maturityBreakDown.Breakdown_10To15Years),
			},
			{
				KeyName:  DisplayBreakdown15To20Years,
				KeyValue: fmt.Sprintf("%0.2f%%", maturityBreakDown.Breakdown_15To20Years),
			},
			{
				KeyName:  DisplayBreakdown20To30Years,
				KeyValue: fmt.Sprintf("%0.2f%%", maturityBreakDown.Breakdown_20To30Years),
			},
			{
				KeyName:  DisplayBreakdownOver30Years,
				KeyValue: fmt.Sprintf("%0.2f%%", maturityBreakDown.BreakdownOver30Years),
			},
		}
		for _, allocation := range tmpAllocations {
			if allocation.KeyValue != "0.00%" {
				allocations = append(allocations, allocation)
			}
		}
	}
	if len(allocations) == 0 {
		return nil
	}
	return &fePb.FundBreakupData{
		Allocations: allocations,
	}
}

// nolint:unparam
func getFundManagementTiles(ctx context.Context, fund *mfPb.MutualFund) []*fePb.Tile {
	tiles := make([]*fePb.Tile, 0)

	inceptionDateTile := getInceptionDateTile(fund)
	if inceptionDateTile != nil {
		tiles = append(tiles, inceptionDateTile)
	}

	fundManagerTile := getFundManagersTile(fund)
	if fundManagerTile != nil {
		tiles = append(tiles, fundManagerTile)
	}

	return tiles
}

func getFundManagersTile(fund *mfPb.MutualFund) *fePb.Tile {
	managers := getFundManagers(fund)
	if len(managers) == 0 {
		return nil
	}
	tile := &fePb.Tile{}
	managersTile := &fePb.MultiKeyValTile{
		Header: &fePb.Header{
			Name: DisplayFundManagerHeader,
			Info: &fePb.KeyValInfo{
				InfoHeader: DisplayFundManagersInfoHeader,
				InfoStr:    DisplayFundManagersInfo,
			},
		},
		DisplayData: &fePb.MultiKeyValData{
			MultiKeyValData: managers,
			Alignment:       fePb.DisplayAlignment_VERTICAL,
		},
	}
	tile.DisplayTile = &fePb.Tile_MultiKeyValTile{MultiKeyValTile: managersTile}
	return tile
}

func getInceptionDateTile(fund *mfPb.MutualFund) *fePb.Tile {
	inceptionDateTxt := getFundInceptionDateString(fund)
	if inceptionDateTxt == "" {
		return nil
	}
	tile := &fePb.Tile{}
	inceptionDateTile := &fePb.TextTile{DescriptionLines: []string{inceptionDateTxt}}
	tile.DisplayTile = &fePb.Tile_TextTile{TextTile: inceptionDateTile}
	return tile
}

// nolint:unparam
func getThresholdSectionsTiles(ctx context.Context, fund *mfPb.MutualFund, sipEnabled bool) []*fePb.Tile {
	tiles := make([]*fePb.Tile, 0)

	minInvestmentTile := getMinInvestmentTile(fund, sipEnabled)
	if minInvestmentTile != nil {
		tiles = append(tiles, minInvestmentTile)
	}

	earlySellingPenaltyTile := getEarlySellingPenaltyTile(fund)
	if earlySellingPenaltyTile != nil {
		tiles = append(tiles, earlySellingPenaltyTile)
	}

	taxImplicationTile := getTaxImplicationTile(fund)
	if taxImplicationTile != nil {
		tiles = append(tiles, taxImplicationTile)
	}

	return tiles
}

func getTaxImplicationTile(fund *mfPb.MutualFund) *fePb.Tile {
	desc := getTaxDescription(fund)
	if len(desc) == 0 {
		return nil
	}
	tile := &fePb.Tile{}
	taxTile := &fePb.TextTile{
		Header:           &fePb.Header{Name: DisplayTaxImplications},
		DescriptionLines: desc,
	}
	tile.DisplayTile = &fePb.Tile_TextTile{TextTile: taxTile}
	return tile
}

func getEarlySellingPenaltyTile(fund *mfPb.MutualFund) *fePb.Tile {
	descLines := getPenaltyDescriptionFromFund(fund)
	if len(descLines) == 0 {
		return nil
	}
	tile := &fePb.Tile{}
	penaltyTile := &fePb.TextTile{
		Header:           &fePb.Header{Name: DisplayEarlySellingPenalty},
		DescriptionLines: descLines,
	}
	tile.DisplayTile = &fePb.Tile_TextTile{TextTile: penaltyTile}
	return tile
}

func getMinInvestmentTile(fund *mfPb.MutualFund, sipEnabled bool) *fePb.Tile {
	tile := &fePb.Tile{}
	minOneTime := calculateMinOneTime(fund)
	if minOneTime == "" {
		return nil
	}
	txnTile := &fePb.MultiKeyValTile{
		Header: &fePb.Header{Name: DisplayTxnThresholdHeader},
		DisplayData: &fePb.MultiKeyValData{
			MultiKeyValData: []*fePb.KeyValDisplayData{
				{
					KeyName:  DisplayTxnThresholdMinAutoPay,
					KeyValue: calculateMinSipValue(fund.TxnConstraints, sipEnabled),
				},
				{
					KeyName:  DisplayTxnThresholdMinOneTime,
					KeyValue: calculateMinOneTime(fund),
				},
			},
			Alignment: fePb.DisplayAlignment_HORIZONTAL,
		},
	}
	tile.DisplayTile = &fePb.Tile_MultiKeyValTile{MultiKeyValTile: txnTile}
	return tile
}

// calculateRelativeRating calculates the relative rating on the basis of percentile rank
func calculateRelativeRating(percentileRank float32) string {

	percentileRank = 100 - percentileRank
	switch {
	case percentileRank <= 10.0:
		return DisplayRelativeRatingValueExcellent
	case percentileRank > 10.0 && percentileRank <= 15.0:
		return DisplayRelativeRatingValueGood
	case percentileRank > 15.0 && percentileRank <= 25.0:
		return DisplayRelativeRatingValueAverage
	default:
		return DisplayRelativeRatingValueBelowAverage
	}

}

// dataAfterRelativeRating returns MultiKeyValData after calculating the relative rating
func dataAfterRelativeRating(relativeRating string, data *fePb.MultiKeyValData) *fePb.MultiKeyValData {
	ratios := data.GetMultiKeyValData()
	ratios = append(ratios, getKeyValDisplayData(DisplayRelativeRatingKey, relativeRating, ""))
	data = getMultiKeyValData(ratios, fePb.DisplayAlignment_HORIZONTAL)
	return data
}

func getExpectedInterestRateTileIOS(fund *mfPb.MutualFund) *fePb.Tile {
	tile := &fePb.Tile{
		DisplayTile: &fePb.Tile_TextTile{TextTile: &fePb.TextTile{
			Header: &fePb.Header{
				Name: DisplayExpectedInterestRateHeader,
				Info: &fePb.KeyValInfo{
					InfoHeader: DisplayExpectedInterestRateHeader,
					InfoStr:    DisplayExpectedInterestRateInfoDesc,
				},
			},
			DescriptionLines: []string{fmt.Sprintf("Based on current fund holdings: %0.2f%%", fund.GetReturns().GetYieldToMaturity())},
		},
		}}
	return tile
}

func getExpectedInterestRateTileAndroid(fund *mfPb.MutualFund) *fePb.Tile {
	tile := &fePb.Tile{
		DisplayTile: &fePb.Tile_MultiKeyValTile{
			MultiKeyValTile: &fePb.MultiKeyValTile{
				Header: &fePb.Header{
					Name: DisplayExpectedInterestRateHeader,
					Info: &fePb.KeyValInfo{
						InfoHeader: DisplayExpectedInterestRateInfoHeader,
						InfoStr:    DisplayExpectedInterestRateInfoDesc,
					},
				},
				DisplayData: getMultiKeyValData([]*fePb.KeyValDisplayData{
					getKeyValDisplayData("", fmt.Sprintf("Based on current fund holdings: %0.2f%%", fund.GetReturns().GetYieldToMaturity()), "")}, fePb.DisplayAlignment_HORIZONTAL),
			},
		}}
	return tile
}

func getTimeFrameVsReturnsMap(fund *mfPb.MutualFund) map[fePb.TimeFrame]float32 {
	mp := map[fePb.TimeFrame]float32{
		fePb.TimeFrame_TIMEFRAME_1_MTH: fund.GetReturns().GetAvgFundReturnOneMonth(),
		fePb.TimeFrame_TIMEFRAME_6_MTH: fund.GetReturns().GetAvgFundReturnSixMonth(),
		fePb.TimeFrame_TIMEFRAME_1_YR:  fund.GetReturns().GetAvgFundReturnOneYear(),
		fePb.TimeFrame_TIMEFRAME_3_YR:  fund.GetReturns().GetAvgFundReturnThreeYear(),
		fePb.TimeFrame_TIMEFRAME_5_YR:  fund.GetReturns().GetAvgFundReturnFiveYear(),
	}
	return mp
}

// getTimeFrameWithMaxReturn returns the timeframe with maximum returns from given timeFrameRanges
func getTimeFrameWithMaxReturn(timeFrameVsReturnsMap map[fePb.TimeFrame]float32, timeFrameRanges []fePb.TimeFrame) string {

	var maxTimeFrame fePb.TimeFrame
	var maxReturns float32
	// checking for maximum time frame in given timeFrameRanges
	for _, timeFrame := range timeFrameRanges {
		if returns, exist := timeFrameVsReturnsMap[timeFrame]; exist {
			fundReturns := returns
			if maxReturns < fundReturns {
				maxReturns = fundReturns
				maxTimeFrame = timeFrame
			}
		}
	}
	return maxTimeFrame.String()
}

// isReturnsGraphEnabled determines whether the app version is suitable for enabling graph or not
func (s *Service) isReturnsGraphEnabled(device *commontypes.Device, appVersionCode uint32) bool {
	appPlatform := device.GetPlatform()

	switch {
	case appPlatform == commontypes.Platform_ANDROID:
		if appVersionCode < s.config.Investment().MinAndroidVersionToSupportGraph() {
			return false
		}
	case appPlatform == commontypes.Platform_IOS:
		if appVersionCode < s.config.Investment().MinIOSVersionToSupportGraph() {
			return false
		}
	}
	return true
}

// fillTimeframeWiseData populates the multiKeyValWithGraphData for a particular time frame, this function will not populate the nil multiKeyValWithGraphData
// in the TimeFrameWiseData
func fillTimeframeWiseData(timeFrameTile *fePb.MultiTimeFrameWithGraphTile, fund *mfPb.MutualFund, categoryAvgData *mfPb.MutualFundCategoryAverage, isGraphEnabled bool, timeFrame fePb.TimeFrame) {
	multiKeyValWithGraphData := &fePb.MultiKeyValWithGraphData{}
	switch timeFrame {
	case fePb.TimeFrame_TIMEFRAME_1_YR:
		multiKeyValWithGraphData = getReturnMultiKeyValWithGraphData(
			fund.GetReturns().GetAvgFundReturnOneYear(),
			categoryAvgData.GetReturns().GetAvgCategoryReturnOneYear(),
			getAmountGrowthInvestedEveryMonth(12, fund.GetHistoricalReturns(), DefaultInvestmentAmountForGraph, ThisFundReturnColor, isGraphEnabled),
			getAmountGrowthInvestedEveryMonth(12, categoryAvgData.GetHistoricalReturns(), DefaultInvestmentAmountForGraph, CategoryReturnColor, isGraphEnabled),
			12,
		)
	case fePb.TimeFrame_TIMEFRAME_3_YR:
		multiKeyValWithGraphData = getReturnMultiKeyValWithGraphData(
			fund.GetReturns().GetAvgFundReturnThreeYear(),
			categoryAvgData.GetReturns().GetAvgCategoryReturnThreeYear(),
			getAmountGrowthInvestedEveryMonth(36, fund.GetHistoricalReturns(), DefaultInvestmentAmountForGraph, ThisFundReturnColor, isGraphEnabled),
			getAmountGrowthInvestedEveryMonth(36, categoryAvgData.GetHistoricalReturns(), DefaultInvestmentAmountForGraph, CategoryReturnColor, isGraphEnabled),
			36)
	case fePb.TimeFrame_TIMEFRAME_5_YR:
		multiKeyValWithGraphData = getReturnMultiKeyValWithGraphData(
			fund.GetReturns().GetAvgFundReturnFiveYear(),
			categoryAvgData.GetReturns().GetAvgCategoryReturnFiveYear(),
			getAmountGrowthInvestedEveryMonth(60, fund.GetHistoricalReturns(), DefaultInvestmentAmountForGraph, ThisFundReturnColor, isGraphEnabled),
			getAmountGrowthInvestedEveryMonth(60, categoryAvgData.GetHistoricalReturns(), DefaultInvestmentAmountForGraph, CategoryReturnColor, isGraphEnabled),
			60)

	case fePb.TimeFrame_TIMEFRAME_1_MTH:
		multiKeyValWithGraphData = getReturnMultiKeyValWithGraphData(
			fund.GetReturns().GetAvgFundReturnOneMonth(),
			categoryAvgData.GetReturns().GetAvgFundReturnOneMonth(),
			nil,
			nil,
			1)
	case fePb.TimeFrame_TIMEFRAME_6_MTH:
		multiKeyValWithGraphData = getReturnMultiKeyValWithGraphData(
			fund.GetReturns().GetAvgFundReturnSixMonth(),
			categoryAvgData.GetReturns().GetAvgFundReturnSixMonth(),
			getAmountGrowthInvestedEveryMonth(6, fund.GetHistoricalReturns(), DefaultInvestmentAmountForGraph, ThisFundReturnColor, isGraphEnabled),
			getAmountGrowthInvestedEveryMonth(6, categoryAvgData.GetHistoricalReturns(), DefaultInvestmentAmountForGraph, CategoryReturnColor, isGraphEnabled),
			6)
	default:
		return
	}
	if multiKeyValWithGraphData == nil {
		return
	}
	timeFrameTile.TimeFrameWiseData[timeFrame.String()] = multiKeyValWithGraphData
	return
}
