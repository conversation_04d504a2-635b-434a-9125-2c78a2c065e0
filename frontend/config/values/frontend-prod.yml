Application:
  Environment: "prod"
  Name: "frontend"
  EnableDeviceIntegrityCheck: true
  SkipDeviceIntegrityCheckForExistingActors: false
  EnableLocationInterceptor: true
  MaxGRPCTimeout: "2m"

Server:
  Ports:
    GrpcPort: 8082
    GrpcSecurePort: 9509
    HttpPort: 9999
    HttpPProfPort: 9990

SecureLogging:
  EnablePartnerLog: true
  EnableSecureLog: true
  PartnerLogPath: "/var/log/frontend/partner/partner.log"
  SecureLogPath: "/var/log/frontend/secure.log"
  MaxSizeInMBs: 50
  MaxBackups: 20

SalaryProgramBucketName: "epifi-prod-salaryprogram"

GetNextOnboardingActionRetryStrategy:
  RegularInterval:
    Interval: 3
    MaxAttempts: 200
    TimeUnit: "Second"

GetNextOnboardingActionFiLiteRedirectTime: 20s

LegalDocuments:
  FiTncUrl: "https://fi.money/T&C"
  FederalBankTncUrl: "https://www.federalbank.co.in/epifi-tandc#CASA"
  FiPrivacyPolicyUrl: "https://fi.money/privacy"
  FiWealthTncUrl: "https://fi.money/wealth/TnC"
  OpenSourceLicenses:
    Firebase: ""
    Cronet: ""
    ChromeWebView: ""

#json file path
PayErrorViewJson: "./mappingJson/errorViewMapping.json"
CardErrorViewJson: "./mappingJson/cardErrorViewMapping.json"
DisplayCategoryMappingJson: "./mappingJson/displayCategoryMapping.json"

Flags:
  ReconVendorIds: true
  ShowNrConsentOnPhoneScreen:
    MinAndroidVersion: 432
    MinIOSVersion: 597
    FallbackToEnableFeature: false
    DisableFeature: false
  UseNewLivenessFlowForAFU:
    MinAndroidVersion: 430
    MinIOSVersion: 100000
    UnsupportedPlatforms: [ 2 ]
    FallbackToEnableFeature: false
    DisableFeature: true
  AllowConsentCollectionOnLogin:
    MinAndroidVersion: 427
    MinIOSVersion: 590
    FallbackToEnableFeature: false
    DisableFeature: false
  BlockNrOnboarding: true
  AllowQatarForSignup: true
  EnableGetNextOnboardingActionFiLiteRedirection: false
  ReturnRetryStatusForOIDCExpiry:
    MinAndroidVersion: 347
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: false
  AllowClosedAccountReOnboarding: true
  EnableDevDeactivatedHandling: true
  EnableConflictingAFU: true
  SkipAddMoney: true
  TrimDebugMessageFromStatus: true
  EnableCardTracking: true
  EnableCardBlock: true
  EnableCardQRCodeScan: true
  EnableCardOnlineTxnEnabledTile: true
  EnableMotherFatherKycNameCheck: true
  EnableVKYCOnlyForInternalUsers: true
  EnableVkycScheduleFlow: false
  EnableCardOffers: true
  EnableBharatQrToUserGroup:
    IsBharatQRRestricted: false
    AllowedUserGrpForBharatQr:
      - 1 # INTERNAL = user belongs to epiFi
  SkipUserRevokeStateCheck: false
  EnableCardTrackingAndActivationChanges:
    IsEnableOnAndroid: true
    MinAndroidVersion: 120
    IsEnableOnIos: true
    MinIosVersion: 115
  EnablePhysicalCardChargesFlow:
    IsEnableOnAndroid: true
    MinAndroidVersion: 217
    IsEnableOnIos: true
    MinIosVersion: 329
  EnablePhysicalCardChargesFlowWithTiering:
    IsEnableOnAndroid: true
    MinAndroidVersion: 229
    IsEnableOnIos: true
    MinIosVersion: 331
  EnableLivenessManualReviewInAfu: false
  EnableCheckForAccessRevoke: true
  EnableAcqSourceAndIntentPropagation: true
  EnableRecentUserActivities: true
  AllTransactionWithCCFeatureFlag:
    IsFeatureRestricted: false
  TimelineEventsFromMultipleSourcesFlag:
    IsFeatureRestricted: false
    AllowedUserGroups:
  EnableCCAllTransactionFeatureFlag:
    IsFeatureRestricted: false
  EnableCCRecentActivityFeatureFlag:
    IsFeatureRestricted: false
  EnableCCTimelineEventFeatureFlag:
    IsFeatureRestricted: false
  EnableCcEmiConversionFeatureFlag:
    IsEnableOnAndroid: true
    MinAndroidVersion: 266
    IsEnableOnIos: false
    MinIosVersion: 500
  EnableCcCustomRemindersFeatureFlag:
    IsEnableOnAndroid: true
    MinAndroidVersion: 240
    IsEnableOnIos: true
    MinIosVersion: 335
  EnableCcDetailsAndBenefitsFeatureFlag:
    IsEnableOnAndroid: true
    MinAndroidVersion: 251
    IsEnableOnIos: true
    MinIosVersion: 347
  EnableUnsecuredNewCvpCcDetailsAndBenefitsFeatureFlag:
    IsEnableOnAndroid: true
    MinAndroidVersion: 300
    IsEnableOnIos: true
    MinIosVersion: 424
  EnableCcFreezeUnfreezeHomeCardIconFlag:
    IsEnableOnAndroid: false
    MinAndroidVersion: 250
    IsEnableOnIos: false
    MinIosVersion: 500
  EnableRedListedAppsCheck: true
  EnableRateLimitingInterceptor: true
  EnableAFUBeforeDevReg: true
  EnableAFUBeforeCustomerCreation: true
  EnableAutoInvestComponentOnInvestLanding:
    MinAndroidVersion: 231
    MinIOSVersion: 333
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableUSStocksInstrumentCardFlag: true
  EnableMFInstrumentCardFlag: true
  EnableSDInstrumentCardFlag: true
  EnableFDInstrumentCardFlag: true
  EnableJumpInstrumentCardFlag: true
  EnableOnboardingDetailsMin: true
  EnableInvestmentLandingQuickLinksComponent:
    MinAndroidVersion: 250
    MinIOSVersion: 346
    FallbackToEnableFeature: false
    DisableFeature: false
  InvestmentLandingQuickLinksComponentAllowedUserGroups:
  EnableCcLoungeAccessFeatureFlag:
    IsEnableOnAndroid: true
    MinAndroidVersion: 247
    IsEnableOnIos: true
    MinIosVersion: 344
  EnableCCCopyCardDetails:
    IsEnableOnAndroid: false
    MinAndroidVersion: 100
    IsEnableOnIos: true
    MinIosVersion: 346
  EnableCreditCardRepaymentScreenTopBanner: false
  EnablePartnersComponentInvestLanding:
    IsEnableOnAndroid: false
    MinAndroidVersion: 1000
    IsEnableOnIos: false
    MinIosVersion: 1000
  UseAppsFlyerClientKeyV2: true
  EnableCCBillDashboardV2FeatureFlag:
    IsEnableOnAndroid: true
    MinAndroidVersion: 281
    IsEnableOnIos: true
    MinIosVersion: 381
  EnableSimID:
    UnsupportedPlatforms: [ 2 ]
    MinAndroidVersion: 315
    MinIOSVersion: 1000
    FallbackToEnableFeature: false
    DisableFeature: false
  EnablePhonePermissionCheck:
    UnsupportedPlatforms: [ 2 ]
    MinAndroidVersion: 427
    MinIOSVersion: 590
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableSecuredCardsRewardsDashboard:
    IsEnableOnAndroid: false
    MinAndroidVersion: 2000
    IsEnableOnIos: false
    MinIosVersion: 2000
  BlockRiskScreeningFailedUsersWithFiLiteIntent: false
  EnableLoungeAccessV2FeatureFlag:
    IsEnableOnAndroid: true
    MinAndroidVersion: 312
    IsEnableOnIos: true
    MinIosVersion: 446
  EnableGenericRewardsDashboard:
    IsEnableOnAndroid: true
    MinAndroidVersion: 359
    IsEnableOnIos: true
    MinIosVersion: 508
  EnableInhouseBiometricLiveness:
    MinAndroidVersion: 430
    MinIOSVersion: 100000
    FallbackToEnableFeature: false
    DisableFeature: false
    UnsupportedPlatforms: [ 2 ]
  EnableLAMFPrePayV2:
    MinAndroidVersion: 414
    MinIOSVersion: 576
    FallbackToEnableFeature: false
    DisableFeature: false
  ReOOBECooldownBottomSheetScreenOptionsV2:
    MinAndroidVersion: 417
    MinIOSVersion: 578
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableCardDesignEnhancement:
    MinAndroidVersion: 444
    MinIOSVersion: 1000000
    FallbackToEnableFeature: false
    DisableFeature: false

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

RewardsFrontendMeta:
  WaysToEarnRewardsV2ScreenFeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 338
    MinIOSVersion: 494
  ClaimedRewardScreenDeeplinkFeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 375
    MinIOSVersion: 10000
  BeDrivenRewardPostClaimScreenFeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 375
    MinIOSVersion: 10000
  MyRewardsScreenCtaCarouselFeatureConfig:
    DisableFeature: true
    MinAndroidVersion: 10000
    MinIOSVersion: 10000
  MyRewardsScreenOfferWidgetFeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 368
    MinIOSVersion: 520
  MyRewardsScreenNewOpenedRewardsWidgetFeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 368
    MinIOSVersion: 520
  CollectedOffersPageFiltersFeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 382
    MinIOSVersion: 542
  IsAllFiltersCtaEnabledOnCollectedOffersPage: true
  AndroidAppVersionsNotSupportingMultiChoiceRewards: [ 28 ]
  MinAndroidAppVersionWithAllRewardTileStatesClickable: 64
  MinAndroidAppVersionSupportingGiftHamperReward: 87
  MinIosAppVersionSupportingGiftHamperReward: 51
  MinAndroidAppVersionSupportingCBRV2: 144
  MinAndroidVersionSupportingLockingOfRewardsForMinKycUsers: 183
  MinIosVersionSupportingLockingOfRewardsForMinKycUsers: 270
  MinAndroidAppVersionSupportingSalaryExclusiveOffer: 167
  MinIosAppVersionSupportingSalaryExclusiveOffer: 238
  MinAndroidVersionForEmptyAddressesGracefulHandling: 220
  MinIosVersionForEmptyAddressesGracefulHandling: 271
  MinAndroidAppVersionSupportingUnredeemableOfferLabel: 195
  MinIosAppVersionSupportingUnredeemableOfferLabel: 340
  MinAndroidAppVersionSupportingComingSoonOffer: 229
  MinIosAppVersionSupportingComingSoonOffer: 340
  MinAndroidAppVersionSupportingInventoryExhaustedOffer: 229
  MinIosAppVersionSupportingInventoryExhaustedOffer: 530
  MinAndroidVersionSupportingBoosterFields: 226
  MinIosVersionSupportingBoosterFields: 328
  MinAndroidAppVersionSupportingCardOfferCatalogV2: 230
  MinAndroidAppVersionSupportingCardOfferDetailsV2: 230
  MinIosAppVersionSupportingCardOfferCatalogV2: 332
  MinIosAppVersionSupportingCardOfferDetailsV2: 332
  MinAndroidAppVersionSupportingCreditCardOffersOnHome: 253
  MinIosAppVersionSupportingCreditCardOffersOnHome: 346
  MinAndroidAppVersionSupportingVistaraAirMilesOffer: 249
  MinIosAppVersionSupportingVistaraAirMilesOffer: 346
  MinAndroidAppVersionSupportingThriweBenefitsPackageOffers: 246
  MinIosAppVersionSupportingThriweBenefitsPackageOffers: 343
  MinAndroidAppVersionSupportingYourRewardsCardV2: 251
  MinIosAppVersionSupportingYourRewardsCardV2: 347
  MinAndroidAppVersionSupportingNewRewardTiles: 253
  MinIosAppVersionSupportingYourNewRewardTiles: 354
  MinAndroidAppVersionSupportingDefaultOfferType: 265
  MinIosAppVersionSupportingDefaultOfferType: 366
  MinIosVersionSupportingMyRewardsV2Screen: 376
  MinIosVersionWithRedeemedOffersScreenPaginationFix: 387
  MinAndroidAppVersionSupportingExternalVendorOffer: 309
  MinIosAppVersionSupportingExternalVendorOffer: 436
  MinAndroidAppVersionSupportingFiCoinsToPointsOfferType: 321
  MinIosAppVersionSupportingFiCoinsToPointsOfferType: 487
  IsWebPageWithCardDetailsScreenEnabled: true
  MinAndroidVersionForWebPageWithCardDetailsScreen: 329
  MinIosVersionForWebPageWithCardDetailsScreen: 481
  MinIOSAppVersionToSupportTopNavBarHighlight: 531
  MinAndroidAppVersionToSupportTopNavBarHighlight: 378
  CardOffersV2WidgetFeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 343
    MinIOSVersion: 501
  CatalogOffersV2WidgetConfig:
    FeatureConfig:
      DisableFeature: false
      MinAndroidVersion: 343
      MinIOSVersion: 501
  DebitCardRewardsConfig:
    InternationalSpendsRewardOfferId: "8ec99d77-b4c8-4d1e-a5b4-008d24030e60"
  OffersCatalogPageConfig:
    FeatureConfig:
      DisableFeature: false
      MinAndroidVersion: 402
      MinIOSVersion: 563
    SectionsConfig:
      BannersSection:
        FeatureConfig:
          DisableFeature: false
          MinAndroidVersion: 407
          MinIOSVersion: 564
      OffersSection:
        LoanDefaultReminderBottomSheetFeatureConfig:
          MinAndroidVersion: 407
          MinIOSVersion: 567

SalaryProgram:
  EnableSalaryReferralsSeason: false
  AaSalaryTpapCooloffDuration: 24h # 24 hours
  SalaryLiteConfig:
    FeatureReleaseConfig:
      DisableFeature: false
      MinAndroidVersion: 338
      MinIOSVersion: 496
    LandingPageBannerDisplaySegmentExpression: "IsMember('d703d137-de90-4d8f-b11c-683d83315875')"
    IsEnabledForInternalActiveUsers: false
    IsSalaryLiteMandateSetupDropOffFeedbackFlowEnabled: true
    IsSalaryLiteEnachDropOffFeedbackFlowEnabled: true
  SalaryAccountVerificationStepperInfo:
    IsEnabled: true
    MinAndroidAppVersionSupported: 289
    MinIosAppVersionSupported: 409
  EmpConfirmationFailureScreenFeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 254
    MinIOSVersion: 361
  LandingPageEligibilityBannerFeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 289
    MinIOSVersion: 409
  LandingPageCommsInfoSectionFeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 309
    MinIOSVersion: 440
  LandingPageQuickLinkTilesV1FeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 309
    MinIOSVersion: 440
  ShareDetailsOnMailV1FeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 249
    MinIOSVersion: 343
  HomeCard:
    ShowCard: true
    HeaderTitle: "Fi Salary Benefits"
    BenefitsInactiveTitle: "Get flat 10% of your salary as Fi-Coins"
    RegCompleteBenefitsInactiveTitle: "Get flat 10% of your salary as Fi-Coins"
    BenefitsActiveTitle: "Get flat 10% of your salary as Fi-Coins"
    BenefitsInactiveCta:
      BgColor: "#7FBECE"
      IsVisible: true
      Text: "Get Salary Account"
      TextColor: "#FFFFFF"
    RegCompleteBenefitsInactiveCta:
      BgColor: "#7FBECE"
      IsVisible: true
      Text: "Upgrade Now"
      TextColor: "#FFFFFF"
    BenefitsActiveCta:
      BgColor: "#7FBECE"
      IsVisible: true
      Text: "View All Benefits"
      TextColor: "#FFFFFF"
    BenefitsInactiveTag:
      BgColor: "#DEEEF2"
      Tag: ""
      TextColor: "#4E9199"
    RegCompleteBenefitsInactiveTag:
      BgColor: "#DEEEF2"
      Tag: ""
      TextColor: "#4E9199"
    BenefitsActiveTag:
      BgColor: "#DEEEF2"
      Tag: ""
      TextColor: "#4E9199"
    BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
    RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
    BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
    BenefitsInactiveBgColor: "#C0DAE0"
    RegCompleteBenefitsInactiveBgColor: "#C0DAE0"
    BenefitsActiveBgColor: "#C0DAE0"
  HomeCardsTimeBound:
    Custom1:
      ActiveFrom: "2022-12-17T00:00:00+05:30"
      ActiveTill: "2022-12-21T23:59:59+05:30"
      ShowCard: true
      BenefitsActiveBgColor: "#EAD8A3"
      BenefitsActiveCta:
        BgColor: "#D3B250"
        IsVisible: true
        Text: "View All Benefits"
        TextColor: "#FFFFFF"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Home_icon_christmastree.png"
      BenefitsActiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      BenefitsActiveTitle: "Christmas gifts from 4700BC, The Whole Truth & more"
      BenefitsInactiveBgColor: "#C0B7E1"
      BenefitsInactiveCta:
        BgColor: "#9287BD"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
      BenefitsInactiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      BenefitsInactiveTitle: "Limited offer: Get ₹1,000 Amazon gift card"
      HeaderTitle: "Fi Salary Benefits"
      RegCompleteBenefitsInactiveBgColor: "#C0B7E1"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#9287BD"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      RegCompleteBenefitsInactiveTitle: "Limited offer: Get ₹1,000 Amazon gift card"
    Custom2:
      ActiveFrom: "2022-12-22T00:00:00+05:30"
      ActiveTill: "2022-12-26T23:59:59+05:30"
      ShowCard: true
      BenefitsActiveBgColor: "#C0DAE0"
      BenefitsActiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Refer Now"
        TextColor: "#FFFFFF"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/salary-referral-speaker.png"
      BenefitsActiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsActiveTitle: "Your colleagues can get you higher rewards"
      BenefitsInactiveBgColor: "#C0DAE0"
      BenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Get Salary Account"
        TextColor: "#FFFFFF"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
      BenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsInactiveTitle: "Join now & get ₹1,000 Amazon gift card"
      HeaderTitle: "Fi Salary Benefits"
      RegCompleteBenefitsInactiveBgColor: "#C0DAE0"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      RegCompleteBenefitsInactiveTitle: "Join now & get ₹1,000 Amazon gift card"
    Custom3:
      ActiveFrom: "2022-12-27T00:00:00+05:30"
      ActiveTill: "2022-12-30T23:59:59+05:30"
      ShowCard: true
      BenefitsActiveBgColor: "#C5E9B2"
      BenefitsActiveCta:
        BgColor: "#87BA6B"
        IsVisible: true
        Text: "Invite Now"
        TextColor: "#FFFFFF"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/giftbox_money_home"
      BenefitsActiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      BenefitsActiveTitle: "Don't miss extra rewards. Invite your colleagues"
      BenefitsInactiveBgColor: "#C5E9B2"
      BenefitsInactiveCta:
        BgColor: "#87BA6B"
        IsVisible: true
        Text: "Get Salary Account"
        TextColor: "#FFFFFF"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
      BenefitsInactiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      BenefitsInactiveTitle: "Offer expires soon: Get ₹1,000 Amazon gift card"
      HeaderTitle: "Fi Salary Benefits"
      RegCompleteBenefitsInactiveBgColor: "#C5E9B2"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#87BA6B"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      RegCompleteBenefitsInactiveTitle: "Offer expires soon: Get ₹1,000 Amazon gift card"
  HomeCardsB2BTimeBound:
    Custom1:
      ActiveFrom: "2022-12-17T00:00:00+05:30"
      ActiveTill: "2022-12-21T23:59:59+05:30"
      ShowCard: true
      BenefitsActiveBgColor: "#EAD8A3"
      BenefitsActiveCta:
        BgColor: "#D3B250"
        IsVisible: true
        Text: "View All Benefits"
        TextColor: "#FFFFFF"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Home_icon_christmastree.png"
      BenefitsActiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsActiveTitle: "Christmas gifts from 4700BC, The Whole Truth & more"
      BenefitsInactiveBgColor: "#C0B7E1"
      BenefitsInactiveCta:
        BgColor: "#9287BD"
        IsVisible: true
        Text: "Get Salary Account"
        TextColor: "#FFFFFF"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins_salary.png"
      BenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsInactiveTitle: "Get 10% of salary in Fi-Coins every month"
      HeaderTitle: "Fi Salary Benefits"
      RegCompleteBenefitsInactiveBgColor: "#C0B7E1"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#9287BD"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins_salary.png"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      RegCompleteBenefitsInactiveTitle: "Get 10% of salary in Fi-Coins every month"
    Custom2:
      ActiveFrom: "2022-12-22T00:00:00+05:30"
      ActiveTill: "2022-12-26T23:59:59+05:30"
      ShowCard: true
      BenefitsActiveBgColor: "#C0DAE0"
      BenefitsActiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "View All Benefits"
        TextColor: "#FFFFFF"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Home_icon_bat.png"
      BenefitsActiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsActiveTitle: "Win a bat signed by King Kohli"
      BenefitsInactiveBgColor: "#C0DAE0"
      BenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Get Salary Account"
        TextColor: "#FFFFFF"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
      BenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsInactiveTitle: "You're so close to getting 5,000 Fi-Coins"
      HeaderTitle: "Fi Salary Benefits"
      RegCompleteBenefitsInactiveBgColor: "#C0DAE0"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      RegCompleteBenefitsInactiveTitle: "You're so close to getting 5,000 Fi-Coins"
    Custom3:
      ActiveFrom: "2022-12-27T00:00:00+05:30"
      ActiveTill: "2022-12-30T23:59:59+05:30"
      ShowCard: true
      BenefitsActiveBgColor: "#C5E9B2"
      BenefitsActiveCta:
        BgColor: "#87BA6B"
        IsVisible: true
        Text: "View All Benefits"
        TextColor: "#FFFFFF"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/giftbox_money_home"
      BenefitsActiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsActiveTitle: "Play & get assured cash rewards starting ₹200"
      BenefitsInactiveBgColor: "#C5E9B2"
      BenefitsInactiveCta:
        BgColor: "#87BA6B"
        IsVisible: true
        Text: "Get Salary Account"
        TextColor: "#FFFFFF"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/giftbox_money_home"
      BenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsInactiveTitle: "Rewards worth ₹1,000 up for grabs"
      HeaderTitle: "Fi Salary Benefits"
      RegCompleteBenefitsInactiveBgColor: "#C5E9B2"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#87BA6B"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/giftbox_money_home"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      RegCompleteBenefitsInactiveTitle: "Rewards worth ₹1,000 up for grabs"
  EntryPointSectionInfo:
    BenefitsActivePromoBannerTileInfo:
      IsVisible: true
      Title: "Get benefits upto ₹30,000 with Fi Salary Program"
      TitleColor: "#313234"
      BgColor: "#BBC8E9"
      ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Homebanner/Cash_cash.png"
      Cta:
        Deeplink:
          Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
      Tag:
        BgColor: "#CDC6E8"
        Tag: ""
        TextColor: "#6F62A4"
    RegCompletedBenefitsInActivePromoBannerTileInfo:
      IsVisible: true
      Title: "Get benefits upto ₹30,000 with Fi Salary Program"
      TitleColor: "#313234"
      BgColor: "#BBC8E9"
      ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Homebanner/Cash_cash.png"
      Cta:
        Deeplink:
          Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
      Tag:
        BgColor: ""
        Tag: ""
        TextColor: ""
    RegNotCompletedPromoBannerTileInfo:
      IsVisible: true
      Title: "Get benefits upto ₹30,000 with Fi Salary Program"
      TitleColor: "#313234"
      BgColor: "#BBC8E9"
      ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Homebanner/Cash_cash.png"
      Cta:
        Deeplink:
          Screen: "SALARY_PROGRAM_INTRO_SCREEN"
      Tag:
        BgColor: ""
        Tag: ""
        TextColor: ""

  EntryPointSectionInfoTimeBound:
    Custom1:
      ActiveFrom: '2023-12-06T00:00:00+05:30'
      ActiveTill: '2023-12-14T23:59:59+05:30'
      IsVisible: true
      BenefitsActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: Get ₹30k in
        HeaderTitleColor: '#929599'
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: '#FFFFFF'
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegCompletedBenefitsInActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: Get ₹30k in
        HeaderTitleColor: '#929599'
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: '#FFFFFF'
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedSummaryTileInfo:
        IsVisible: true
        HeaderTitle: Get ₹30k in
        HeaderTitleColor: '#929599'
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: Upgrade Now
          TextColor: '#4F71AB'
          BgColor: '#FFFFFF'
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      RegCompletedBenefitsInActiveStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      BenefitsActivePromoBannerTileInfo:
        IsVisible: true
        Title: Claim your FREE health cover of up to ₹20L!
        TitleColor: '#383838'
        BgColor: '#C0DAE0'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/health-insurance-benefit.png
        Tag:
          Tag: ''
          TextColor: '#4E9199'
          BgColor: '#DEEEF2'
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_BENEFIT_INFO_SCREEN
            SalaryBenefitInfoScreenOptions:
              BenefitId: 4ab17e3c-2173-47aa-b20c-2a01c0d18c99
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#EAD8A3'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/Megha.png'
        Tag:
          Tag: ''
          TextColor: '#A73F4B'
          BgColor: '#FAD0D0'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/salary-testimonials-96650'
      RegNotCompletedPromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#EAD8A3'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/Megha.png'
        Tag:
          Tag: ''
          TextColor: '#A73F4B'
          BgColor: '#FAD0D0'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/salary-testimonials-96650'
    Custom2:
      ActiveFrom: '2023-12-15T00:00:00+05:30'
      ActiveTill: '2023-12-19T23:59:59+05:30'
      IsVisible: true
      BenefitsActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: Get ₹30k in
        HeaderTitleColor: '#929599'
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: '#FFFFFF'
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegCompletedBenefitsInActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: Get ₹30k in
        HeaderTitleColor: '#929599'
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: '#FFFFFF'
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedSummaryTileInfo:
        IsVisible: true
        HeaderTitle: Get ₹30k in
        HeaderTitleColor: '#929599'
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: Upgrade Now
          TextColor: '#4F71AB'
          BgColor: '#FFFFFF'
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      RegCompletedBenefitsInActiveStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      BenefitsActivePromoBannerTileInfo:
        IsVisible: true
        Title: 'Your chance to earn big! Earn ₹1,000 per referral!'
        TitleColor: '#383838'
        BgColor: '#EAD8A3'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/salaryfest_christmas.png
        Tag:
          Tag: ''
          TextColor: '#CDA428'
          BgColor: '#F4E7BF'
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_REFERRALS_LANDING_SCREEN
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#EAD8A3'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/Megha.png'
        Tag:
          Tag: ''
          TextColor: '#A73F4B'
          BgColor: '#FAD0D0'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/salary-testimonials-96650'
      RegNotCompletedPromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#EAD8A3'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/Megha.png'
        Tag:
          Tag: ''
          TextColor: '#A73F4B'
          BgColor: '#FAD0D0'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/salary-testimonials-96650'
    Custom3:
      ActiveFrom: '2023-12-20T00:00:00+05:30'
      ActiveTill: '2023-12-23T23:59:59+05:30'
      IsVisible: true
      BenefitsActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: Get ₹30k in
        HeaderTitleColor: '#929599'
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: '#FFFFFF'
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegCompletedBenefitsInActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: Get ₹30k in
        HeaderTitleColor: '#929599'
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: '#FFFFFF'
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedSummaryTileInfo:
        IsVisible: true
        HeaderTitle: Get ₹30k in
        HeaderTitleColor: '#929599'
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: Upgrade Now
          TextColor: '#4F71AB'
          BgColor: '#FFFFFF'
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      RegCompletedBenefitsInActiveStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      BenefitsActivePromoBannerTileInfo:
        IsVisible: true
        Title: 'Last few days to earn ₹1,000 per referral!'
        TitleColor: '#383838'
        BgColor: '#EAD8A3'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/salaryfest_christmas.png
        Tag:
          Tag: ''
          TextColor: '#CDA428'
          BgColor: '#F4E7BF'
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_REFERRALS_LANDING_SCREEN
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#EAD8A3'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/Megha.png'
        Tag:
          Tag: ''
          TextColor: '#A73F4B'
          BgColor: '#FAD0D0'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/salary-testimonials-96650'
      RegNotCompletedPromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#EAD8A3'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/Megha.png'
        Tag:
          Tag: ''
          TextColor: '#A73F4B'
          BgColor: '#FAD0D0'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/salary-testimonials-96650'
    Custom4:
      ActiveFrom: '2023-12-23T00:00:00+05:30'
      ActiveTill: '2023-12-25T23:59:59+05:30'
      IsVisible: true
      BenefitsActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: Get ₹30k in
        HeaderTitleColor: '#929599'
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: '#FFFFFF'
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegCompletedBenefitsInActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: Get ₹30k in
        HeaderTitleColor: '#929599'
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: '#FFFFFF'
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedSummaryTileInfo:
        IsVisible: true
        HeaderTitle: Get ₹30k in
        HeaderTitleColor: '#929599'
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: Upgrade Now
          TextColor: '#4F71AB'
          BgColor: '#FFFFFF'
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      RegCompletedBenefitsInActiveStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      BenefitsActivePromoBannerTileInfo:
        IsVisible: true
        Title: 'You asked, we answered. Fest extended by 3 days!'
        TitleColor: '#383838'
        BgColor: '#EAD8A3'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/salaryfest_christmas.png
        Tag:
          Tag: ''
          TextColor: '#CDA428'
          BgColor: '#F4E7BF'
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_REFERRALS_LANDING_SCREEN
      RegCompletedBenefitsInActivePromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#EAD8A3'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/Megha.png'
        Tag:
          Tag: ''
          TextColor: '#A73F4B'
          BgColor: '#FAD0D0'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/salary-testimonials-96650'
      RegNotCompletedPromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#EAD8A3'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/Megha.png'
        Tag:
          Tag: ''
          TextColor: '#A73F4B'
          BgColor: '#FAD0D0'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/salary-testimonials-96650'



  EntryPointSectionInfoB2BTimeBound:
    Custom1:
      ActiveFrom: '2023-11-27T00:00:00+05:30'
      ActiveTill: '2023-11-30T06:59:59+05:30'
      IsVisible: true
      BenefitsActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: "Get ₹30k in"
        HeaderTitleColor: "#929599"
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: "#FFFFFF"
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegCompletedBenefitsInActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: "Get ₹30k in"
        HeaderTitleColor: "#929599"
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: "#FFFFFF"
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedSummaryTileInfo:
        IsVisible: true
        HeaderTitle: "Get ₹30k in"
        HeaderTitleColor: "#929599"
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: Upgrade Now
          TextColor: '#4F71AB'
          BgColor: "#FFFFFF"
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      RegCompletedBenefitsInActiveStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      BenefitsActivePromoBannerTileInfo:
        IsVisible: true
        Title: Claim your FREE health cover of up to ₹2L!
        TitleColor: '#383838'
        BgColor: '#C0B7E1'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/health-insurance-benefit.png
        Tag:
          Tag: ''
          TextColor: '#6F62A4'
          BgColor: '#CDC6E8'
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_BENEFIT_INFO_SCREEN
            SalaryBenefitInfoScreenOptions:
              BenefitId: '99e917b8-da9e-440e-9324-afbc7f59262c'

      RegCompletedBenefitsInActivePromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#EFC0C0'
        ImageUrl: 'https://epifi-icons.pointz.in/cards/insurance.png'
        Tag:
          Tag: ''
          TextColor: '#A73F4B'
          BgColor: '#FAD0D0'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/stories/addmoney-1'
      RegNotCompletedPromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#C0B7E1'
        ImageUrl: 'https://epifi-icons.pointz.in/cards/insurance.png'
        Tag:
          Tag: ''
          TextColor: '#6F62A4'
          BgColor: '#CDC6E8'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/stories/addmoney-1'

  IntroPageV3:
    ShowSalaryCalculatorCta: false
    HeroBenefitFeatureConfig:
      DisableFeature: false
      MinAndroidVersion: 293
      MinIOSVersion: 409
  SalaryBenefitsLandingPageQuickLinksSection:
    IsVisible: true
    MinAndroidVersionSupportingQuickLinksSection: 184
    MinIOSVersionSupportingQuickLinksSection: 268
    QuickLinksTiles:
      link1:
        VisibleFromAndroidVersion: 252
        VisibleFromIosVersion: 343
        Title: "Download a cancelled cheque"
        TitleColor: "#333333"
        ImageUrl: "https://epifi-icons.pointz.in/casper/catalog/cancelled_cheque_new.png"
        BgColor: "#CDC6E8"
        CTA:
          IsVisible: true
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          BgColor: "#C0B7E1"
          Deeplink:
            Screen: "DOWNLOAD_DIGITAL_CANCELLED_CHEQUE"
            DownloadDigitalCancelledChequeScreenOptions:
              CtaText:
                FontColor: "#FFFFFF"
                PlainString: "Preview & Download"
                BgColor: "#00B899"
                StandardFontStyle: "BUTTON_M"
              Title:
                FontColor: "#333333"
                PlainString: "Download a digital cancelled cheque on your device"
                StandardFontStyle: "SUBTITLE_1"
              Description:
                FontColor: "#646464"
                PlainString: "You can share a cancelled cheque with your employer as a proof of your account details."
                StandardFontStyle: "BODY_S"
        TileRank: 1
      link2:
        BgColor: "#FAD0D0"
        CTA:
          BgColor: "#EFC0C0"
          Deeplink:
            Screen: "STORY_SCREEN"
            StoryScreenOptions:
              StoryId: "e24825d2-740b-4fce-9aba-6a1ecc47d0ef"
              StoryTitle: "Get a chequebook"
              StoryUrl: "https://stories.fi.money/stories/salary-chequebook-request"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-pink.png"
          IsVisible: true
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Quick_links/chequebook.png"
        TileRank: 2
        Title: "Get a free chequebook"
        TitleColor: "#333333"
      link3:
        BgColor: "#DEEEF2"
        CTA:
          BgColor: "#C0DAE0"
          Deeplink:
            Screen: "STATEMENT_REQUEST_SCREEN"
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-dark-blue.png"
          IsVisible: true
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Quick_links/statement.png"
        TileRank: 3
        Title: "Download your bank statement"
        TitleColor: "#333333"
      link4:
        BgColor: "#D9F2CC"
        CTA:
          BgColor: "#C5E9B2"
          Deeplink:
            ExternalRedirectionScreenOptions:
              ExternalUrl: "https://form.typeform.com/to/FZ8lzU09"
            Screen: "EXTERNAL_REDIRECTION"
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          IsVisible: true
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Quick_links/survey.png"
        TileRank: 4
        Title: "Take a Quick Survey"
        TitleColor: "#333333"
  SalaryProgramSpecialRewardCampaignEndDate: "2023-04-10T23:59:59+05:30"
  SalaryReferralEmployersBlockedMap: [ ]
  B2BEmployersMap:
    # b2b employer with base health insurance
    a1ee8b4d-66a1-47a3-b712-40315e6c879e: ""
    fe9e8cb3-4061-402f-b844-c8b1eb5263b4: ""
    48f58ef2-9b78-45c6-89a7-d6507ab5ad46: ""
    5fa76472-2436-4776-b156-e71fb8a9dce8: ""
    6a824886-c253-49b4-9e50-6f470ad8680c: ""
    176511a0-eec6-4993-8d33-e3d12ba31d81: ""
    b8512de7-494d-4f4e-8248-e4045c1d5e7f: ""
    5cc116f3-736d-4e04-8726-64cc656d31be: ""
    f7a4abd9-d8fc-4636-ae77-2258ff2732a9: ""
    27a931e2-799e-44f5-86c2-c393e0d2b243: ""
    d164dbf6-f025-452b-8dea-e6179a6f4ef4: ""
    2aa2bebb-1110-403d-83db-55de087dc687: ""
    7eae0d2d-a95c-4336-8646-633d01885068: ""
    d97d9467-a35b-4128-ad4f-5ee03f1405a9: ""
    36e0e031-c018-4d32-a1a7-cc742aaa080f: ""
    dddcd4c7-a502-4407-8914-43a8593ae934: ""
    f986ea1a-4cb7-40d3-8e9a-a770db4d5a7b: ""
    f4ce60b5-946f-41b8-844d-4acfb486ac58: ""
    1969318b-532c-4002-abd8-598915fc4a3e: ""
    ********-45be-41e8-9ee3-0895d5f66a22: ""
    1c91f8d5-f30e-4e65-9872-291bb87fbeec: ""
  SalaryProgramFAQsCategoryId: "***********"
  SalaryAccountBenefitsSectionTopBannerInfo:
    Title: "Calculate the total annual value of the Fi Salary Program benefits "
    TitleFontColor: "#FFFFFF"
    BgColor: "#383838"
    LeftIconUrl: ""
    RightIconUrl: "https://epifi-icons.pointz.in/salaryprogram/right-arrow.png"
    IsVisible: false
  HelpSectionInfo:
    IsVisible: true
    MinAndroidVersionSupportingHelpSection: 246
    MinIOSVersionSupportingHelpSection: 343
  # todo (utkarsh) : update this value once the early salary rewardOffer is configured on prod
  EarlySalaryBenefitConfig:
    EarlySalaryBenefitRewardOfferId: "06b3e3c3-4379-4429-9723-eab53ead99c3"
  HealthInsuranceRewardOfferIdToPolicyConfigMap:
    4ab17e3c-2173-47aa-b20c-2a01c0d18c99:
      HealthInsurancePolicyFAQsDocS3Path: "healthinsurance/docs/healthInsuranceFAQs.pdf"
      HealthInsurancePolicyClaimProcessDocS3Path: "healthinsurance/docs/claimsProcessDoc.pdf"
      HealthInsuranceCashlessHospitalListDocS3Path: "healthinsurance/docs/cashlessHospitalsDoc.pdf"
      HealthInsuranceTncsDocS3Path: "healthinsurance/docs/healthInsuranceTncs.pdf"
      HealthInsurancePolicyType: "SUPER_TOP_UP_INSURANCE"
    99e917b8-da9e-440e-9324-afbc7f59262c:
      HealthInsurancePolicyFAQsDocS3Path: "healthinsurance/docs/healthInsuranceFAQs.pdf"
      HealthInsurancePolicyClaimProcessDocS3Path: "healthinsurance/docs/claimsProcessDoc.pdf"
      HealthInsuranceCashlessHospitalListDocS3Path: "healthinsurance/docs/cashlessHospitalsDoc.pdf"
      HealthInsuranceTncsDocS3Path: "healthinsurance/docs/healthInsuranceTncs.pdf"
      HealthInsurancePolicyType: "BASE_HEALTH_INSURANCE"
    999e2acd-caac-4113-bb32-6a828789e12d:
      HealthInsurancePolicyType: "ONSURITY_OPD_WELLNESS_2A2C"
    db30be5d-8e92-4f06-adcb-46311f09ee6d:
      HealthInsurancePolicyType: "ONSURITY_OPD_WELLNESS_2A"
    95711cae-a985-47df-9f80-c27de9a8059f:
      HealthInsurancePolicyType: "ONSURITY_OPD_WELLNESS_1A"
  SalaryProgramSurvey:
    BenefitsLandingPageSurveySegmentIdExpression: ""
  IsSalaryProgramBenefitAmazon500VoucherEnabled: false
  MinAndroidAppVersionToSupportBenefitsCalculatorPage: 228
  MinIosAppVersionToSupportBenefitsCalculatorPage: 330
  MinAndroidAppVersionHandlingWinningsSectionV2: 230
  MinIosAppVersionHandlingWinningsSectionV2: 332
  MinReqDurationSinceLastActivationForBenefitsExpiryComms: 960h # 40 days
  MinAndroidAppVersionHandlingRpcResponseErrorView: 203
  MinAndroidAppVersionSupportingLandingScreenRedirection: 249
  MinIosAppVersionSupportingLandingScreenRedirection: 343
  MinAndroidAppVersionSupportingBenefitsSectionV1: 10000
  MinIosAppVersionSupportingBenefitsSectionV1: 10000
  SalaryLiteFlowsEligibleActors:
  HealthInsuranceOnsurityPolicyFlowsConfig:
    DisableFeature: false
    MinAndroidVersion: 370
    MinIOSVersion: 524
  AaSalaryConfig:
    EnableAaSalaryAmountSetupVersionV2: false
    AaSalaryAmountSetupVersionV2:
      MinAndroidVersion: 372
      MinIOSVersion: 525
    AaSalarySourceScreenHideConnectTitle:
      MinAndroidVersion: 372
      MinIOSVersion: 525
  SalaryWinningSectionPrioritizedMonthlyIntervals:
    StartDate: 6
    EndDate: 23

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "prod/gcloud/profiling-service-account-key"
    RudderClientApiKey: "prod/rudder/android_write_key"
    AppsFlyerClientKey: "prod/appsflyer/client_key"
    OneMoneyClientSecret: "prod/onemoney/client_secret"
    DeviceIdsEnabledForSafetyNetV2: "prod/frontend-auth/v2_safetynet_enabled_device_ids"
    MoengageAppSdkKey: "prod/moengage/app-sdk-key"
    ActiveDeviceIntegrityTokenSigningKey: "prod/frontend-auth/device-integrity-token-signing-key"
    MiAmpPushSecretJson: "prod/mi_amp_push/mi_secret_json"
    AppsFlyerClientKeyV2: "prod/appsflyer/client_key_2"
    RudderIosClientApiKey: "prod/rudder/ios_write_key"

DeviceIntegrity:
  EnableWhitelistedTokens: false
  # SkipAsyncDeviceIntegrityChecks is kill switch, when set to true it would bypass device integrity checks
  SkipAsyncDeviceIntegrityChecks: false
  WhitelistedTokensList: [ "DUMMY_TOKEN" ]
  DefaultHighRiskDeviceConsentDuration: "24h"
  MaxHighRiskDeviceConsentDuration: "1080h" # 45 days
  AsyncDeviceIntegrityCheck:
    DisableFeature: false
    MinAndroidVersion: 183
    MinIosVersion: 100000
  AsyncDeviceIntegrityRolloutPercentage: 100

VKYC:
  PopupTileDuration: 12 # in hours
  PopupTileNonDismissableAfter: 4320 # 3 days in minutes
  AccountFreezePopupNonDismissibleWithinDays: 30 # in days
  AccountFreezeThreshold: "4320h" # 180 days
  SavingsBalanceLimitPercent: 70
  CreditBalanceLimitPercent: 70
  PerformEkycAfter: "72h"
  SlotDays: 5
  MorningStart: 10
  SplitMorning: 12
  SplitAfternoon: 15
  SplitLateAfternoon: 18
  EveningEnd: 22
  ScheduleToLiveCutOffMinutes: 2
  # redmit 8A, Samsumng S20+, Samsung note10+
  UnsupportedCustomerDeviceModels: [ ]
  DisableVKYCOutOfBizHoursForForceLSO: true
  NewPopupTileAccountCreationTimeLimit: 180 # in days
  NewPopupTileDuration: 84 # in hours
  HomeBannerColorHex: "#383838"
  EnableCallRetry: false
  FAQCategoryId: "***********"
  EnableEPAN:
    MinAndroidVersion: 2000
    MinIOSVersion: 2000
    FallbackToEnableFeature: true
    DisableFeature: true
  EPANRolloutPercentage: 0
  ShowAuditorAcceptedTileTime: "8h"
  ABFeatureReleaseConfig:
    FeatureConstraints:
      - VKYC_NEW_REVIEW_SCREEN:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 332
              MinIOSVersion: 465
          Buckets:
            - ONE:
                Start: 0
                End: 99

InsightsParams:
  GetInsightConfig:
    MarkNoticedAfter: 2
  SmsReadConsentPocAndroidAppVersion: 100000
  EpfConfig:
    DisableEpfPassbookOtpFlow: false
OrderReceipt:
  ChequeTransactionSuccessTimeDuration: 24h
  IsHtmlRenderingEnabledIOnPlatform:
    IsEnableOnAndroid: true
    MinAndroidVersion: 1
    IsEnableOnIos: false
    MinIosVersion: 100

Card:
  MinAndroidVersionForFMAuth: 58
  CardDynamicTileDuration:
    ViewCardDeliveryTracking: "24h"
    QRCodeAsPrimaryTile: "72h"
    ViewCardOnlineTxnEnabledTile: "12h"
    ViewQRCodeScanTime: "48h"
    ViewSecurePinActivationTime: "216h"
  MinAndroidVersionForCardOffers: 82
  MinAndroidVersionForCardTracking: 83
  MinAndroidVersionForSecurePinValidation: 111
  MinIOSVersionForSecurePinValidation: 98
  EnableSecurePinValidationAuth: true
  EnableSecurePinActivationFlow:
    IsEnableOnAndroid: true
    MinAndroidVersion: 262
    IsEnableOnIos: true
    MinIosVersion: 377
  AllowedUserGrpForSecurePinValidationAuth:
    - 1 # INTERNAL
  OffersDynamicTileExpiryTime: "31-08-2022T23:59:00"
  EnableCardOffersInformativeDynamicTile: false
  PrintingToDispatchDynamicTileDuration: "168h"
  PhysicalDebitCardRequestParams:
    TitleForChargesFlow: "Get a physical Debit Card"
    DescriptionForChargesFlow: "Buy a VISA Platinum Debit Card for a one-time fee No annual maintenance fees."
    CTANameForChargesFlow: "ORDER CARD"
  PhysicalCardChargingFlowStartTimestamp: 1615283875
  EnableDCCopyCardDetails:
    IsEnableOnAndroid: true
    MinAndroidVersion: 262
    IsEnableOnIos: true
    MinIosVersion: 361
  EnableDCCopyCardDetailsV1:
    IsFeatureRestricted: false
    AllowedUserGroups:
      - 1 # INTERNAL
  EnableDCConsolidatedCardControls:
    IsEnableOnAndroid: true
    MinAndroidVersion: 262
    IsEnableOnIos: true
    MinIosVersion: 377
  EnableDCConsolidatedCardControlsV1:
    IsFeatureRestricted: false
    AllowedUserGroups:
      - 1 # INTERNAL
  EnableDcCardRenewalChargesFlow:
    IsEnableOnAndroid: true
    MinAndroidVersion: 324
    IsEnableOnIos: true
    MinIosVersion: 465
  ShowPhysicalCardOrderEntryPointInCardSettings:
    IsEnableOnAndroid: false
    MinAndroidVersion: 1000
    IsEnableOnIos: false
    MinIosVersion: 1000
  EnablePinSetFlowRedirectionPlatformVersionCheck:
    IsEnableOnAndroid: false
    MinAndroidVersion: 3000
    IsEnableOnIos: false
    MinIosVersion: 3000
  DashboardV2Config:
    IsQuestCheckEnabledForDashboardV2: true
    IsDashboardV2EnabledByQuest: true
    SectionsConfig:
      CardSectionConfig:
        ShowTapnPaySettingOnHome:
          IsEnableOnAndroid: false
          MinAndroidVersion: 10000
          IsEnableOnIos: false
          MinIosVersion: 10000
      ShortcutsSectionConfig:
        PlanTravelBudgetUrl: "https://fi.money/travel-budget"
        EnableAtmLocatorShortcut:
          IsEnableOnAndroid: true
          MinAndroidVersion: 421
          IsEnableOnIos: true
          MinIosVersion: 578
        AtmLocatorUserGrpCheck:
          EnableUserGroupCheck: true
          AllowedUserGrp:
            - 1 # INTERNAL
  F30IssuanceFeeRefundContent:
    IsEnabled: true
    SegmentIds:
      - "b6a8b426-bc47-4852-8d32-cd35a1336e43"
    PhysicalCardSectionText: "New offer - Get Card\n Fee back on 1st spend"
  DcOrderPageFeedbackEngine:
    IsEnabled: true

Comms:
  NotificationsPageSize: 10

Screening:
  EmpVerificationCheckStatusPollIntervalInSecs: 5
  CheckCreditReportAvailabilityStatusPollIntervalInSecs: 5
  CheckCreditReportVerificationStatusPollIntervalInSecs: 5
  ShowVerifyOtpScreenOptionsErr:
    MinAndroidVersion: 10000
    MinIOSVersion: 10000
    FallbackToEnableFeature: false
    DisableFeature: true

Referrals:
  LandingPageVersion: 2 # referrals-v1
  IsReferralsViaFiniteCodeAllowed: true
  # todo(divyadeep): update them with correct values once new home referrals widget is released on prod
  MinAndroidVersionSupportingNewHomeReferralsWidget: 200
  MinIosVersionSupportingNewHomeReferralsWidget: 286
  MinIosVersionSupportingCtasFixForHomeV1EntryPoint: 327
  AppDownloadUrl: "https://go.fi.money/invite"
  AppDownloadUrlWithFiniteCode: "https://fi.onelink.me/GvZH/invite?ru=%s"
  LandingScreenPromotionalBanner:
    ShowBanner: false
    Title: "Referral Rush : Activated 🔑\n\nRefer more & unlock ₹500 per referral \nHurry! Offer ends on 6 Aug"
    TitleColor: "#FFFFFF"
    ImageUrl: "https://epifi-icons.pointz.in/referrals/1L-people-img.png"
    BgColor: "#4F71AB"
    Cta:
      BgColor: "#00B899"
      ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
      IsEnabled: false
      IsVisible: false
      Text: ""
      TextColor: "#FFFFFF"
    ExpandedStateTitle: "Offer Details"
    ExpandedStateDesc: "Earn ₹300 to ₹3000 for each referral"
    ExpandedStateTitleColor: "#FFFFFF"
    ExpandedStateDescColor: "#FFFFFF"
    ExpandedStateIconUrl: "https://epifi-icons.pointz.in/referrals/promo_banner_icon.png"
    ExpandedStateCta:
      BgColor: ""
      ImageUrl: ""
      IsEnabled: false
      IsVisible: true
      Text: ""
      TextColor: ""
    ExpandedStateHeading1: ""
    ExpandedStateInfos1: "You need to maintain an average balance of ₹1000 or more across your Savings Account and Deposits for at least 10 days to unlock referrals\nOnce you unlock referrals, share your unique referral code with your friends\nWhen a friend opens an account on Fi and adds at least ₹3000 in their savings account you get a reward\nYou will win assured cash reward of up to ₹3000 and your friend earns at least ₹100 in cash rewards. You will receive any amount between ₹300 to ₹3000 per successful referral"
    ExpandedStateHeading2: "Reward T&Cs"
    ExpandedStateInfos2: "To qualify you have to maintain an average balance of ₹1000 or more across your Savings Account and Deposits for at least 10 days\nYour friend has to use your unique referral code when opening their account\nYou will get a reward only when your friend adds at least ₹3000 to their savings account within 7 days of opening an account\nA maximum of 40 referral rewards can be earned by you in a financial year\nThis offer is valid till 31 Dec'22\nReward amount is determined based on the reward offer active when your referee creates a Fi account and not according to the offer active when you shared your referral code\nReward programs can be terminated without prior notice at the Company's discretion\nPlease also note that users of our Platform residing in the State of Tamil Nadu are not eligible to participate in specific rewards/offers as per the applicable law in Tamil Nadu, thus users residing in Tamil Nadu are requested not to participate in offers relating to cash backs etc"
  InfoDuringOnboarding:
    OfferCodes:
      - CODE_1:
          IsEnabled: true
          Icon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/FI200-offer-icon.png"
          DisplayCode: "FI200"
          UnderlyingFiniteCode: "DLFFH5HHJC"
          Offer: "Get up to ₹200 cashback"
          Desc: "When you add money to your account"
          CtaText: "Apply now"
          CodeAppliedPopupDetails:
            Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
            Title: "Yay! “FI200” applied"
            Subtitle: "You get up to ₹200 cashback"
            Desc: "Sign up & add funds to your Savings Account to claim this offer"
            CanDismiss: true
    ReferralCodeAppliedPopupDetails:
      Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
      Title: "Yay! Code applied"
      Subtitle: "You’re off to a great start 🎉"
      Desc: "Continue signing up to Fi"
      CanDismiss: false
    Fi200ReferralCodeAppliedPopupDetails:
      Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
      Title: "Yay! Code applied"
      Subtitle: "You'll get up to ₹200 cashback"
      Desc: "Sign up & add funds to your Savings Account to claim this offer"
      CanDismiss: false
    ReferralCodeClaimFailedPopupDetails:
      Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-auto-apply-1.png"
      Title: "That code didn't work. Here's an awesome offer for you anyway"
      Subtitle: "You'll get up to ₹200 cashback"
      Desc: "Sign up & add funds to your Savings Account to claim this offer"
      CanDismiss: true
    ReferralCodeClaimFailedPopup:
      MinAndroidVersion: 99999
      MinIOSVersion: 99999
    MaxReferralCodeClaimAttempts: 10000
  FeatureRestrictionParams:
    AgeRestriction:
      Enable: true
      AgeLowerBound: 21

ReferralsV1:
  MinAndroidVersionSupportingInviteContactsSection: 234
  MinIosVersionSupportingInviteContactsSection: 330
  MinAndroidVersionForImplicitCopyCodeButton: 236
  MinIosVersionForImplicitCopyCodeButton: 332
  IsReferralForD2hUsersEnabled: true
  HomeWidgetV2FeatureConfig:
    DisableFeature: true
    MinAndroidVersion: 10000
    MinIOSVersion: 10000
  LandingPageWeeklyEarningsComponentFeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 397
    MinIOSVersion: 554
  ReferralLinkGenerationInfoConf:
    LinkValidForSinceGeneration: 1320h # 55d
    OnelinkTemplateId: "GvZH"
    AfChannel: "Referral"
    Campaign: "Referral Invite WA"
    OnelinkGenerationParams:
      - deep_link_sub5: "FINITE_CODE_PLACEHOLDER"
      - pid: "Referral Invite"
      - Source: "Referral"
  AppUpdateHardNudgeConf:
    ShowNudgeTillAppVersionAndroid: 0
    ShowNudgeTillAppVersionIos: 0
    UserBucketStart: 0
    UserBucketEnd: 99
  AppUpdateSoftNudgeConf:
    ShowNudgeTillAppVersionAndroid: 0
    ShowNudgeTillAppVersionIos: 0
    UserBucketStart: 0
    UserBucketEnd: 99
  StackedRewards:
    StartDateTime: "2001-01-01T00:00:00+05:30" # Starting date and time in RFC3339 Format
    EndDateTime: "2001-01-01T00:00:00+05:30" # Ending date and time in RFC3339 Format
    ShowReferralHistoryEarningSummary: false
    RefereeSignupActionExpiryDuration: 168h
    RefereeAddMoneyActionExpiryDuration: 168h

Signup:
  ThemeBasedInfoAckScreen:
    MinAndroidVersion: 155
    MinIOSVersion: 202
  AcctClosureBalTransfer:
    MinAndroidVersion: 160
    MinIOSVersion: 225
  ShowAccountFrozenScreenV2:
    MinAndroidVersion: 176
    MinIOSVersion: 257
  EnableKnowMoreAccountClosureFlowIOS:
    MinIOSVersion: 289
    MinAndroidVersion: 1
    FallbackToEnableFeature: true
  EnableAndroidAPIVersionCheckForSignup: true
  BlockOnboardingDueToUnlinkedPANAndAadhaar: true
  EnableDeviceIntegrityScreen: true
  BabaPhoneNumberHashes:
    2af8052055013fdbdd89b0e2a967e5fa8b93542b: true # aditya bhardwaj


AttributionLinkParseConfigs:
  - "CONF-1":
      AcquisitionSource: "STUDENT_PROGRAM_VIT"
      IsEnabled: true
      SourceIdentification:
        CampaignName: "Students_VITVellore"
  - "CONF-2":
      AcquisitionSource: "SOURCE-2"
      IsEnabled: false
      SourceIdentification:
        CampaignName: "campaign-2"
  - "CONF-3":
      AcquisitionSource: "STUDENT_PROGRAM_VIT"
      IsEnabled: true
      SourceIdentification:
        DeepLinkSub1: "Students_VITVellore"
  - "CONF-4":
      AcquisitionSource: "STUDENT_PROGRAM_VIT"
      IsEnabled: true
      SourceIdentification:
        DeepLinkSub2: "Students_VITVellore"

AFU:
  AllowDeeplinkForWhitelistedActions: true
  EnableAFUForCC: true
  EnableDeviceOrSimUpdateForNRUser: true
  EnableForceUpdateSimUpdate: false
  AllowOnboardingOnRegisteredDevice: false
  AllowReOnboardingOnRegisteredDevice: true
  AllowSimUpdateAFU: true
  EnableAccountInactiveCheck: true


ConnectedAccountUserGroupParams:
  IsConnectedAccountRestricted: false
  AllowedUserGrps:
    - 1 # INTERNAL
    - 2 # FNF
    - 7 # CONNECTED_ACCOUNT

ConnectedAccount:
  DisableWealthOnboardingMinAndroidVersion: 152
  DisableWealthOnboardingMinIosVersion: 208
  FinvuDisconnectUrl: "https://revokeconsentlive.finvu.in/"
  HomeBannerCutoffDays: 300
  IsConnectedAccountEnabled: true
  HomeEntryPoint:
    Enabled: true
    Text: "Track all your bank accounts in 1 place"
    LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/home_entry_1.png"
  SearchEntryPoint:
    Enabled: true
    Text: "Connect Account"
    LogoUrl: "https://epifi-icons.pointz.in/home-v2/LinkIconV2.png"
  AnalyserEntryPoint:
    Enabled: true
    Text: "Connect\naccounts"
    LogoUrl: "https://epifi-icons.pointz.in/home-v2/LinkIconV2.png"
  ProfileEntryPoint:
    Enabled: true
    Text: "Track all your other accounts accurately & safely in one place"
    LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/home_entry_1.png"
  AccountManagerEntryPoint:
    Enabled: true
    Text: "Connect more accounts"
    LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/ca_entry_plus_circle_1.png"
  AllTransactionsEntryPoint:
    Enabled: true
    Text: "Connect Accounts"
    LogoUrl: "https://epifi-icons.pointz.in/home-v2/LinkIconV2.png"
  SearchBannerEntryPoint:
    Enabled: true
    Text: "Track all your other accounts accurately & safely in one place"
    LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/home_entry_1.png"
  V2FlowParams:
    UseV2Flow: true
    AccountDiscoveryTitleText: "Select the accounts you want to link & track on Fi"
    AccountDiscoverySubtitleText: "We found these accounts linked to %s"
    AccountDiscoverySubtitleSearchingText: "One moment! We're searching for your accounts linked to %s."
    AccountDiscoveryCtaText: "Continue"
    AccountDiscoveryLoadingText: "Searching for your accounts"
    RegisterOtherAccountsText: "Can't see your accounts?"
    MinVersionAndroid: 182
    MinVersionIos: 472
  MinimumDurationRequiredToPermitDisconnect: "72h"
  DisconnectBottomSheetMinVersionIos: 347
  DisconnectBottomSheetMinVersionAndroid: 240
  DeleteBottomSheetMinVersionIos: 338
  DeleteBottomSheetMinVersionAndroid: 240
  SegmentIds:
    ConsentRenewal: "cb067226-b73f-4ddd-9890-f26dccdaccd7"
  InvalidateConnectFiToFiFlowRetries: "24h"
  MaxRetryAllowedForFiToFiFlowFailureCase: 3
  MaxRetryAllowedForFiToFiAccountDiscoveryFailureCase: 2
  MaxAllowedConsentHandleStatusPoll: 10
  ConsentHandleStatusNextPollDuration: "500ms"
  EnableAccountManagerConsentRenewalEntryPoint: true

Tiering:
  NewTiersConstraints:
    AaSalary:
      IsEnableOnAndroid: true
      MinAndroidVersion: 361
      IsEnableOnIos: true
      MinIosVersion: 513
  TieringFeature:
    MinVersionAndroid: 229
    MinVersionIos: 331
  HeroBenefits:
    MinVersionAndroid: 281
    MinVersionIos: 381
  TierIntroduction:
    ReleaseConstraints:
      MinVersionAndroid: 100000
      MinVersionIos: 100000
    LaunchAnimationInactivitySeconds: 0
  MaxNumberOfManualUpgradeRetries: 3
  SegmentIds:
    StandardPlusAndUnAssigned: "52a1b7f4-d67b-48be-8d32-6327bb8ce489"
    SalaryHighConfidenceNotchSegment: "6593dcc0-f8a4-4d03-8453-a4c070cfb7a1"
    AaSalary: "141f9f75-9521-4f18-b8f9-e0fde31f2a78"
  EnableSalaryHighConfidenceNotch: true
  SalaryHighConfNotchEnabledTimestamp: "2024-01-30T00:00:00+05:30"
  OnbAddFundsSuccessVersionConstraints:
    IsEnableOnAndroid: true
    MinAndroidVersion: 100000
    IsEnableOnIos: true
    MinIosVersion: 100000
  IsTierAllPlansDropOffFeedbackFlowEnabled: false
  EnableHeroV3Component: false
  HeroBenefitsV3Constraints:
    MinVersionAndroid: 10000
    MinVersionIos: 10000
  TierDropOffBottomSheet:
    ShouldShowUSStocks: true
    ShouldShowFixedDeposit: false
    ShouldShowAddFunds: true
  SegmentIdsForAMB:
    - "31888a96-654c-4192-8435-35d08f6abfe2"
    - "4f5835f4-be0b-4d06-8eb1-3a169f2bd0a6"
    - "aa35b5b0-f34e-47db-81be-bf36ddc1838e"
    - "ab41c22d-ec91-438f-9a3b-e3de67cf2940"
    - "b5c234e0-21ba-43da-807c-8c46f35eeea7"
    - "a9c4fbff-d2cd-4216-aff2-3588f6491dd6"
    - "d41b7afc-3a89-4854-b0fd-9f1bcf9bf043"
    - "798311a5-97d9-476a-91ba-8fcad61927c0"
  ExcludeSegmentsFromAMBScreenEntrypoint:
    ExcludedSegmentIds:
      - "0c161ab4-0583-4496-a0c1-91ad0e4f89b1"
      - "9cd5e2fb-fe04-4352-a880-076d096c67cc"
      - "6d6180f3-2716-4f5c-83da-06998f8c1c16"
      - "86a1110f-551c-450a-be1f-7019beb1175d"
    StartTime: 1748944683
    EndTime: 1750357800

IPInterceptorParams:
  EnableIPInterceptor: true
  BlockedIPAddresses: [ "**************", "*************", "*************", "**************", "*************" ]

Fittt:
  InvestRegularlyCollectionId: "1e9ba29f-6d70-4fbc-8728-e20b30158dab"
  SaveWhenYouShopCollectionId: "85adfaac-59fd-437c-acef-74c0230016f7"

AddFundsParams:
  IsTierMovementDropOffFeedbackFlowEnabled: false
  AddFundsV3Params:
    ImageWithTextConstraints:
      IsEnableOnAndroid: true
      MinAndroidVersion: 314
      IsEnableOnIos: false
      MinIosVersion: 100000
    IntentThreshold: 25000
  DownTime:
    DateTimeFormat: "2006-01-02:15:04:05"
    EndDateTime: "2022-11-25:03:00:00"
    IsDownTimeEnabled: true
    StartDateTime: "2022-11-24:23:30:00"
    Timezone: "Asia/Kolkata"
  IntentNavigateToPayStatusAndroidVersion: 430
  CollectNavigateToPayStatusAndroidVersion: 10000

Investment:
  AmcPiIds:
    - paymentinstrument-icici-amc-business-account
    - paymentinstrument-idfc-amc-business-account
    - paymentinstrument-axis-amc-business-account
    - paymentinstrument-absl-amc-business-account
    - PI220509/WN0ey9PTeqPR0p/BlnboQ==
    - paymentinstrument-kotak-amc-business-account
    - paymentinstrument-liquiloans-1
    - PIOboMH0p9Q4iQiFJew8drpw230427==
    - paymentinstrument-hdfc-amc-business-account
    - paymentinstrument-lnt-amc-business-account
    - paymentinstrument-tata-amc-business-account
    - paymentinstrument-motilal-oswal-amc-business-account
    - paymentinstrument-canararobeco-amc-business-account
    - paymentinstrument-edelweiss-amc-business-account
    - paymentinstrument-lic-amc-business-account
    - paymentinstrument-nippon-amc-business-account
    - paymentinstrument-mahindra-amc-business-account
    - paymentinstrument-pgim-amc-business-account
    - paymentinstrument-mirae-amc-business-account
    - paymentinstrument-uti-amc-business-account
    - paymentinstrument-franklin-amc-business-account
    - paymentinstrument-ppfas-amc-business-account
    - paymentinstrument-sundaram-amc-business-account
    - paymentinstrument-navi-amc-business-account
    - paymentinstrument-reliance-amc-business-account
    - paymentinstrument-sbi-amc-business-account
    - paymentinstrument-invesco-amc-business-account
    - paymentinstrument-bnp-paribas-amc-business-account
    - paymentinstrument-idbi-amc-business-account
    - paymentinstrument-jm-financial-amc-business-account
    - paymentinstrument-boi-axa-amc-business-account
    - paymentinstrument-union-amc-business-account
    - paymentinstrument-hsbc-amc-business-account
    - paymentinstrument-quant-amc-business-account
    - paymentinstrument-baroda-amc-business-account
    - paymentinstrument-iifl-amc-business-account
    - paymentinstrument-quantum-amc-business-account
    - paymentinstrument-principal-amc-business-account
    - paymentinstrument-taurus-amc-business-account
    - paymentinstrument-indiabulls-amc-business-account
    - paymentinstrument-shriram-amc-business-account
    - paymentinstrument-iti-amc-business-account
    - paymentinstrument-trust-amc-business-account
    - paymentinstrument-nj-amc-business-account
    - paymentinstrument-samco-amc-business-account
    - paymentinstrument-sahara-amc-business-account
    - paymentinstrument-whiteoak-amc-business-account

  InvestmentSummary:
    RestrictInvestedAmountAsSaved: false
    AllowedUserGroups:
      - 1 # INTERNAL
  MinAndroidVersionToSupportOneTimeInvestment: 137
  MinIOSVersionToSupportOneTimeInvestment: 169
  AggregatorRecommendationID: "TEN_ONLY_COLLECTION"
  IsWithdrawalAllowedWithoutOtp: false
  MinAndroidVersionToSupportMINKYCCheckForPurchase: 137
  MinIOSVersionToSupportMINKYCCheckForPurchase: 169
  PreferredPaymentProtocolForOTI: "NEFT"
  MinAndroidVersionToUploadPan: 137
  MinIosVersionToUploadPan: 169
  ISKYCCheckOnPurchaseEnabledForIOS: true
  ISKYCCheckOnPurchaseEnabledForAndroid: true
  MfNavChart:
    WebUrl: "https://fi.money/fin-charts/line-chart"
    LastUpdatedAt: ********** # Wednesday, 8 February 2023 12:00:00 AM GMT+05:30
  FundActivityManualInterventionSupport:
    IsEnableOnAndroid: 1
    MinAndroidVersion: 167
    IsEnableOnIos: 1
    MinIosVersion: 235
  MinAndroidVersionForNextOnboardingStep: 228
  MinIOSVersionForNextOnboardingStep: 331
  MFLandingPageCollectionIDForCuratedFunds: "MFCOLL220616OpSMFB48RmSti0YWVhvf8A=="
  MFLandingPageCollectionNameForCuratedFunds: "High Stakes"
  EnableOTIReminderPNFlag: true
  MinAndroidVersionToSupportGraph: 10000
  MinIOSVersionToSupportGraph: 300
  PromotionalContentUsecase: "US_STOCKS_1"
  InvestmentHomeElementParams:
    InvestmentSegmentABExperimentParams:
      - 0:
          SegmentExpression: "IsMember('80064dee-4074-488e-ba8e-611a65973be6')"
          ABExperimentVariant: "DEFAULT"
          UseCase: "US_STOCKS_1"
      - 1:
          SegmentExpression: "IsMember('80064dee-4074-488e-ba8e-611a65973be6')"
          ABExperimentVariant: "INVESTMENT_HOME_COMPONENT_CONTROL_1"
          UseCase: "US_STOCKS_1"
  InvestmentLandingRecommendations:
    SegmentExpressionToRecommendationDetailsMap:
      - "IsMember('cbf93eb5-813d-4f30-b9ea-158a8333390b')":
          RecommendationID: "USSTOCK_LIVE"
          InstrumentType: 5 # represent usstocks
      - "IsMember('296b4e52-46a4-4d53-9288-bae6255e3f05')":
          RecommendationID: "USSTOCK_LIVE"
          InstrumentType: 5 # represent usstocks
    FallBackRecommendationDetails:
      RecommendationID: "USSTOCK_LIVE"
      InstrumentType: 5 # represent usstocks
    MFRecommendationIDToDetailsMap:
      - "TEN_ONLY_COLLECTION":
          CollectionID: "MFCOLL221017pxCtEqYYTNmYVMBnIBTu1w=="
          HeaderDisplayString: "₹10 Only"
          SubtitleString: "Based on what people invest"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/coin_stack.png"
      - "HUNDRED_ONLY_COLLECTION":
          CollectionID: "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA=="
          HeaderDisplayString: "₹100 Only"
          SubtitleString: "Based on what people invest"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
      - "TAX_SAVER_COLLECTION":
          CollectionID: "MFCOLL11q30wxq/uS0SO98y2Fqzr2w=="
          HeaderDisplayString: "Tax Saver"
          SubtitleString: "Save tax while building your wealth"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
      - "BETTER_THAN_FD":
          CollectionID: "MFCOLL220816X8pn3rfgRxOJoafUyUInEw=="
          HeaderDisplayString: "Better than FD"
          SubtitleString: "Based on what people invest"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Boxes-1.png"
      - "GOLD_FUNDS":
          CollectionID: "MFCOLL221031Uagf1nwFRKWrpP7sCf+gWg=="
          HeaderDisplayString: "Gold fund"
          SubtitleString: "Based on what people invest"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/collections_gold.png"
      - "HIGH_STAKES":
          CollectionID: "MFCOLL220616OpSMFB48RmSti0YWVhvf8A=="
          HeaderDisplayString: "High Stakes"
          SubtitleString: "Based on what people invest"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Graphhigh.png"
      - "INDEX_FUNDS":
          CollectionID: "MFCOLL220906o0vQpkttR/mBJopY5qP7Gw=="
          HeaderDisplayString: "Index Funds"
          SubtitleString: "Invest in the top Indian companies at low cost"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
      - "SIP_WITH_500":
          CollectionID: "MFCOLL2303105ofsxZ4XRqqYpVhLkfPDng=="
          HeaderDisplayString: "SIP with ₹500"
          SubtitleString: "Build wealth through regular investments"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/SIP_collection_icon.png"
      - "POPULAR_IN_MF":
          FilterIDs:
            - "INDEX_FUND_TYPE"
            - "UPTO_POINT5_PER_EXPENSE"
            - "1001_TO_5000CR_FUND_SIZE"
            - "5001_TO_10KCR_FUND_SIZE"
            - "MORE_THAN_10KCR_FUND_SIZE"
          HeaderDisplayString: "Popular in Mutual funds"
          SubtitleString: "Based on what people invest"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
    FallBackMFRecommendationDetails:
      CollectionID: "MFCOLL221017pxCtEqYYTNmYVMBnIBTu1w=="
      HeaderDisplayString: "₹10 Only"
      SubtitleString: "Based on what people invest"
      HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
    USStocksRecommendationIDToDetailsMap:
      - "USSTOCK_LIVE":
          CollectionID: "COLLECTION_NAME_TOP_GAINERS_ACROSS_ALL_INDEX"
          HeaderDisplayString: "Top Gainers"
          SubtitleString: "Discover top gainers of NASDAQ & S&P500"
          HeaderImageUrl: "https://epifi-icons.pointz.in/top-gainers.png"
    FallBackUSStocksRecommendationDetails:
      CollectionID: "COLLECTION_NAME_TOP_GAINERS_ACROSS_ALL_INDEX"
      HeaderDisplayString: "Top Gainers"
      SubtitleString: "Discover top gainers of NASDAQ & S&P500"
      HeaderImageUrl: "https://epifi-icons.pointz.in/top-gainers.png"
    HybridRecommendationIDToDetailsMap:
      - "HYBRID_1":
          #          TitleString: "Popular on Fi"
          #          SubtitleString: "Based on our investors' preferences"
          TitleString: "Mutual Funds collections"
          CTAForInstrumentType: 1
          SubtitleString: "Save time with expert-picked funds"
          TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
          CollectionRecommendationCards:
            "MFCOLL2303105ofsxZ4XRqqYpVhLkfPDng==":
              InstrumentType: 1
              Rank: 3
              Title: "SIP with ₹500"
              Subtitle: "Invest regularly with these top funds"
              ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
            "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
              InstrumentType: 1
              Rank: 5
              Title: "Tax saver"
              Subtitle: "Funds to save tax under section 80C"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
            "MFCOLL220906o0vQpkttR/mBJopY5qP7Gw==":
              InstrumentType: 1
              Rank: 4
              Title: "Index funds"
              Subtitle: "Funds that track Nifty & Sensex performance"
              ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
            "MFCOLL220906lmxFMeXcRCiRC1f1C0C0AA==":
              InstrumentType: 1
              Rank: 1
              Title: "Global Funds"
              Subtitle: "Diversify & invest in global companies"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Basket.png"
            "MFCOLL220616OpSMFB48RmSti0YWVhvf8A==":
              InstrumentType: 1
              Rank: 2
              Title: "High Stakes"
              Subtitle: "Explore high risk, high return funds"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Graphhigh.png"
      - "INTERNAL":
          TitleString: "Mutual Funds collections"
          SubtitleString: "Save time with expert-picked funds"
          CTAForInstrumentType: 1
          TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
          CollectionRecommendationCards:
            "MFCOLL2303105ofsxZ4XRqqYpVhLkfPDng==":
              InstrumentType: 1
              Rank: 3
              Title: "SIP with ₹500"
              Subtitle: "Invest regularly with these top funds"
              ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
            "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
              InstrumentType: 1
              Rank: 5
              Title: "Tax saver"
              Subtitle: "Funds to save tax under section 80C"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
            "MFCOLL220906o0vQpkttR/mBJopY5qP7Gw==":
              InstrumentType: 1
              Rank: 4
              Title: "Index funds"
              Subtitle: "Funds that track Nifty & Sensex performance"
              ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
            "MFCOLL220906lmxFMeXcRCiRC1f1C0C0AA==":
              InstrumentType: 1
              Rank: 1
              Title: "Global Funds"
              Subtitle: "Diversify & invest in global companies"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Basket.png"
            "MFCOLL220616OpSMFB48RmSti0YWVhvf8A==":
              InstrumentType: 1
              Rank: 2
              Title: "High Stakes"
              Subtitle: "Explore high risk, high return funds"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Graphhigh.png"
    FallBackHybridRecommendationDetails:
      #      TitleString: "Popular on Fi"
      #      SubtitleString: "Based on what people invest"
      #      TitleImageUrl: "https://epifi-icons.pointz.in/investments/landing/recommendation_star.png"
      TitleString: "Mutual Funds collections"
      SubtitleString: "Save time with expert-picked funds"
      CTAForInstrumentType: 1
      TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
      CollectionRecommendationCards:
        "MFCOLL2303105ofsxZ4XRqqYpVhLkfPDng==":
          InstrumentType: 1
          Rank: 3
          Title: "SIP with ₹500"
          Subtitle: "Invest regularly with these top funds"
          ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
        "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
          InstrumentType: 1
          Rank: 5
          Title: "Tax saver"
          Subtitle: "Funds to save tax under section 80C"
          ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
        "MFCOLL220906o0vQpkttR/mBJopY5qP7Gw==":
          InstrumentType: 1
          Rank: 4
          Title: "Index funds"
          Subtitle: "Funds that track Nifty & Sensex performance"
          ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
        "MFCOLL220906lmxFMeXcRCiRC1f1C0C0AA==":
          InstrumentType: 1
          Rank: 1
          Title: "Global Funds"
          Subtitle: "Diversify & invest in global companies"
          ImgUrl: "https://epifi-icons.pointz.in/investments/Basket.png"
        "MFCOLL220616OpSMFB48RmSti0YWVhvf8A==":
          InstrumentType: 1
          Rank: 2
          Title: "High Stakes"
          Subtitle: "Explore high risk, high return funds"
          ImgUrl: "https://epifi-icons.pointz.in/investments/Graphhigh.png"
    MinIosVersionForV2Recommendations: 344
    MinAndroidVersionForV2Recommendations: 251
    AndroidVersionForSubtitleSwapFix: 254
    EnableMultipleRecommendation: false
  SIPRenewSupportConfig:
    MinIOSVersionSIPRenewSupport: 494
    MinAndroidVersionSIPRenewSupport: 342
  EnableInvestmentDigestTileDedupe: true
  EnableHardLockinCalculator: true

ABFeatureReleaseConfig:
  FeatureConstraints:
    - ASK_FI_HOME_SEARCH_BAR:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1000
            MinIOSVersion: 1000
        Buckets:
          - CONTROL:
              Start: 70
              End: 74
          - TYPE_1:
              Start: 75
              End: 79
          - TYPE_2:
              Start: 80
              End: 84
          - TYPE_3:
              Start: 85
              End: 89
    - INVESTMENT_LANDING_COMPONENTS_ORDERING:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 100
            MinIOSVersion: 100
        Buckets:
          - CONTROL_1:
              Start: 10
              End: 13
    - INVESTMENT_LANDING_MUTUAL_FUNDS_DEEPLINK:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 100
            MinIOSVersion: 100
        Buckets:
          - INVESTMENT_LANDING_MUTUAL_FUNDS_INSTRUMENT_DEEPLINK_CONTROL_1:
              Start: 48
              End: 59
    - JUMP_DASHBOARD_FOR_NON_INVESTED_USERS:
        Buckets:
          - ZERO_STATE_DASHBOARD_VARIANT_ENABLED:
              Start: 16
              End: 26
    - FIXED_DEPOSIT_INTEREST_RATES:
        Buckets:
          - FIXED_DEPOSIT_INTEREST_RATES_EXPERIMENT_MIN_1_YEAR:
              Start: -1 # 27
              End: -1 # 42
    - FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM:
        Buckets:
          - FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM_EXPERIMENT_15_MONTHS:
              Start: -1 # 27
              End: -1 # 42
    - HOME_SEARCH_BAR_CHIPS:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 217
            MinIOSVersion: 305
        Buckets:
          - TYPE_1:
              Start: 43
              End: 69
    - REFERRAL_SCREEN_DURING_ONBOARDING_V1:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 99999
            MinIOSVersion: 99999
        Buckets:
          - ONE:
              Start: 0
              End: 49
    - INVESTMENT_HOME_COMPONENT:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 100
            MinIOSVersion: 100
        Buckets:
          - INVESTMENT_HOME_COMPONENT_CONTROL_1:
              Start: 1
              End: 50
    - UPDATED_BENEFITS_FOR_AFFLUENT_USER_ONB_ADD_FUNDS:
        ConstraintConfig:
          AppVersionConstraintConfig: # this should be the same as the app version for AFFLUENT_USER_BONUS_TRANSITION_SCREEN in user-prod.yml
            MinAndroidVersion: 99999
            MinIOSVersion: 99999
          StickyPercentageConstraintConfig:
            RolloutPercentage: 100
        Buckets: # this should be the same as the buckets for AFFLUENT_USER_BONUS_TRANSITION_SCREEN in user-prod.yml
          - ONE:
              Start: 0
              End: 0
    - APP_UPDATE_HARD_NUDGE_REFERRALS:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - APP_UPDATE_SOFT_NUDGE_REFERRALS:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - REFERRALS_V1_LANDING_PAGE_STACKED_REWARDS_COMPONENT:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 277
            MinIOSVersion: 379
        Buckets:
          - ONE:
              Start: 0
              End: 100
    - PHONE_NUMBER_AS_REFERRAL_CODE:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 99999
            MinIOSVersion: 99999
        Buckets:
          - ONE:
              Start: 0
              End: 0
    - FEATURE_SHOW_REQUIRED_BALANCE_FOR_TIER_UPGRADE:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 421
            MinIOSVersion: 584
        Buckets:
          - ONE:
              Start: 0
              End: 29

FeatureReleaseConfig:
  FeatureConstraints:
    - FEATURE_US_FUNDS_IN_INVEST_LANDING_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1000000
          MinIOSVersion: 1000000
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_WB_MAGIC_IMPORT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_UPDATE_SAVINGS_ACCOUNT_NOMINEE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 427
          MinIOSVersion: 590
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_HOME_DESIGN_ENHANCEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 444
          MinIOSVersion: 611
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 20
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_DC_ORDER_SCREEN_WA_ADDRESS_UPDATE_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 421
          MinIOSVersion: 584
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CA_BANK_SELECTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 375
          MinIOSVersion: 545
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - FEATURE_NET_WORTH_INDIAN_STOCKS_DISTRIBUTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 439
          MinIOSVersion: 601
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SALARY_LITE_PROGRAM:
        AppVersionConstraintConfig:
          MinAndroidVersion: 338
          MinIOSVersion: 496
    - TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH:
        AppVersionConstraintConfig:
          MinAndroidVersion: 346
          MinIOSVersion: 502
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - PRIME_SMS_PARSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 400
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
    - SAVINGS_ACCOUNT_CLOSURE_PROFILE_SETTINGS_ENTRY_POINT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 311
          MinIOSVersion: 460
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INAPPHELP_FEEDBACK_ENGINE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 270
          MinIOSVersion: 370
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INVESTMENT_MF_UI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 139
          MinIOSVersion: 174
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 2 # FNF
            - 9 # FIT_INVESTMENT
    - PAY_VIA_PHONE_NUMBER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 200000
          MinIOSVersion: 200000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - MF_ADVANCE_FILTER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 160
          MinIOSVersion: 225
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_TEXT_SEARCH:
        AppVersionConstraintConfig:
          MinAndroidVersion: 164
          MinIOSVersion: 238
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_SIP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 175
          MinIOSVersion: 257
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_NEW_OTI_PAYMENT_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 181
          MinIOSVersion: 257
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SHOW_SUPPORT_TICKETS_IN_APP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 230
          MinIOSVersion: 238
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MERCHANT_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 189
          MinIOSVersion: 275
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - HOME_PAGE_LAYOUT_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 217
          MinIOSVersion: 305
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - REFERRALS_V1_LANDING_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 220
          MinIOSVersion: 318
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - REFERRAL_LINK_GENERATION_AT_CLIENT_SIDE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 240
          MinIOSVersion: 335
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - TIME_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 210
          MinIOSVersion: 295
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - AA_FINVU_TOKEN_AUTHENTICATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 210
          MinIOSVersion: 361
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - JUMP_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 196
          MinIOSVersion: 283
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 12 # P2P_INVESTMENT_INTERNAL
    - CATEGORY_ANALYSER_ADD_FUNDS_BANNER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 194
          MinIOSVersion: 1000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - CREDIT_SCORE_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 210
          MinIOSVersion: 295
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ANALYSER_FEEDBACK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 230
          MinIOSVersion: 331
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FI_MINUTE_HUB_BANNER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 203
          MinIOSVersion: 287
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - US_STOCK_UI:
        # US_STOCK_UI controls the visibility of us stocks button on investment landing page
        AppVersionConstraintConfig:
          MinAndroidVersion: 209
          MinIOSVersion: 295
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - US_STOCK_LANDING_PAGE_UI:
        # US_STOCK_LANDING_PAGE_UI feature controls visibility of landing page to the user to invest in us stocks
        # Pre-launch page is shown instead of landing for users not allowed for this feature
        AppVersionConstraintConfig:
          MinAndroidVersion: 311
          MinIOSVersion: 443
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 18 # USSTOCKS_INTERNAL
            - 25 # USSTOCKS_EXTERNAL
            - 1 # INTERNAL
    - SALARY_PROGRAM_HEALTH_INSURANCE_BENEFIT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 230
          MinIOSVersion: 332
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - JUMP_RENEWAL_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 226
          MinIOSVersion: 328
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ML_KIT_QR:
        AppVersionConstraintConfig:
          MinAndroidVersion: 228
          MinIOSVersion: 1000000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 2 # FNF
    - ENABLE_GET_VKYC_NEXT_ACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 240
          MinIOSVersion: 335
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - ENABLE_2FA_MF_ONE_TIME_BUY:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ENABLE_2FA_MF_REGISTER_SIP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - CATEGORY_ANALYSER_ACCOUNT_FILTER_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 243
          MinIOSVersion: 338
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - JUMP_DASHBOARD_FOR_NON_INVESTED_USERS:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - HEALTH_ENGINE_FOR_PAYMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: **********
          MinIOSVersion: **********
        StickyPercentageConstraintConfig:
          RolloutPercentage: 15
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - TIME_ANALYSER_UPCOMING_TRANSACTIONS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 249
          MinIOSVersion: 345
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ANALYSER_ZERO_STATES:
        AppVersionConstraintConfig:
          MinAndroidVersion: 253
          MinIOSVersion: 349
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - AA_CONSENT_RENEWAL:
        AppVersionConstraintConfig:
          MinAndroidVersion: 258
          MinIOSVersion: 361
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - JUMP_MATURITY_CONSENT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 270
          MinIOSVersion: 370
    - JUMP_ALL_ACTIVITY_DEEPLINK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 355
    - VERIFY_QR_V1:
        AppVersionConstraintConfig:
          MinAndroidVersion: 250
          MinIOSVersion: 1000000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - CA_CONNECT_FI_TO_FI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 311
          MinIOSVersion: 446
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 7 # CONNECTED_ACCOUNT
    - FEATURE_JUMP_INVEST_PAGE_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 272
          MinIOSVersion: 376
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_HOLDINGS_IMPORT_PHONE_NUMBER_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 280
          MinIOSVersion: 380
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_JUMP_NEW_LANDING_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 277
          MinIOSVersion: 379
    - AA_CONSENT_RENEWAL_ALL_TXNS_PAGE_WIDGET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 277
          MinIOSVersion: 380
    - REFERRALS_V1_REFERRAL_HISTORY_EARNING_SUMMARY_INFO:
        AppVersionConstraintConfig:
          MinAndroidVersion: 99999
          MinIOSVersion: 99999
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - POST_PAYMENT_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 401
          MinIOSVersion: 562
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_BIOMETRIC_REVALIDATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 311
          MinIOSVersion: 481
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
          RolloutPercentage: 0
    - FEATURE_DC_INTERNATIONAL_ATM_WITHDRAWAL_LAYOUT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 381
          MinIOSVersion: 536
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_DC_TRAVEL_MODE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 401
          MinIOSVersion: 550
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_DC_TOGGLE_TRAVEL_MODE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1000
          MinIOSVersion: 550
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_DC_TRAVEL_MODE_LOTTIE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 412
          MinIOSVersion: 574
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - LOANS_EARLY_SALARY:
        AppVersionConstraintConfig:
          MinAndroidVersion: 284
          MinIOSVersion: 385
    - LOANS_FI_LITE:
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - LOANS_TPAP_PRE_PAYMENT:
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_LOANS_INCOME_ESTIMATE_VIA_ITR:
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_POST_LOAN_DISBURSAL_SCREENS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 343
          MinIOSVersion: 500
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - NETWORTH_DASHBOARD:
        AppVersionConstraintConfig:
          MinAndroidVersion: 283
          MinIOSVersion: 382
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INVESTMENT_RETENTION_UI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 289
          MinIOSVersion: 382
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - NETWORTH_DEPOSITS_WIDGET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 283
          MinIOSVersion: 386
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - NETWORTH_IND_SECURITIES_WIDGET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - AA_EQUITY_ACC_HOME_SUMMARY:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 7 # CONNECTED_ACCOUNT
    - FIRST_TIME_PAYMENT_PROMPT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 333
          MinIOSVersion: 487
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ANALYSER_LANDING_PAGE_DISABLED_CARD:
        AppVersionConstraintConfig:
          MinAndroidVersion: 270
          MinIOSVersion: 376
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_INVESTMENT_CALCULATOR_UI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 309
          MinIOSVersion: 440
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_NAV_GRAPH_UI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 309
          MinIOSVersion: 440
    - UPCOMING_TRANSACTIONS_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 283
          MinIOSVersion: 386
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 308
          MinIOSVersion: 434
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_HOLDINGS_IMPORT_V2_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 318
          MinIOSVersion: 446
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_SECURED_LOANS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ALLOW_AND_CONTROL_AA_TXN_FOR_TPAP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 294
          MinIOSVersion: 410
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - MANUAL_ASSET_FORM_AIF:
        AppVersionConstraintConfig:
          MinAndroidVersion: 304
          MinIOSVersion: 429
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_ASSET_FORM_ART_ARTEFACTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 304
          MinIOSVersion: 429
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_ASSET_FORM_BONDS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 304
          MinIOSVersion: 429
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_ASSET_FORM_CASH:
        AppVersionConstraintConfig:
          MinAndroidVersion: 304
          MinIOSVersion: 429
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_ASSET_FORM_DIGITAL_GOLD:
        AppVersionConstraintConfig:
          MinAndroidVersion: 304
          MinIOSVersion: 429
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_ASSET_FORM_DIGITAL_SILVER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 304
          MinIOSVersion: 429
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_ASSET_FORM_PRIVATE_EQUITY:
        AppVersionConstraintConfig:
          MinAndroidVersion: 304
          MinIOSVersion: 429
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_ASSET_FORM_REAL_ESTATE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 304
          MinIOSVersion: 429
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ACTIVATE_BENEFICIARY_VIA_LIVENESS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 315
          MinIOSVersion: 451
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SPEND_ANALYSER_INVEST_MORE_INSIGHT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 298
          MinIOSVersion: 423
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SPEND_ANALYSER_INVEST_SURPLUS_BALANCE_INSIGHT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 298
          MinIOSVersion: 423
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SPEND_ANALYSER_EARLY_SALARY_INSIGHT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ONB_ADD_FUNDS_TIERING_SUCCESS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 99999
          MinIOSVersion: 99999
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SPEND_ANALYSER_SET_REMINDER_INSIGHT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 298
          MinIOSVersion: 423
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_ASK_FI_SEARCH_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SUPPORT_FOR_BENEFICIARY_COOL_DOWN_IN_PAY_BY_PHONE_NUMBER:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CREDIT_CARD_TPAP_PAYMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 312
          MinIOSVersion: 446
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL
    - AA_PERMITTED_FIP_CONFIG_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 7 # connected_account
    - ADD_FUNDS_V3:
        AppVersionConstraintConfig:
          MinAndroidVersion: 313
          MinIOSVersion: 447
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ADD_FUNDS_V4:
        AppVersionConstraintConfig:
          MinAndroidVersion: 319
          MinIOSVersion: 457
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 312
          MinIOSVersion: 446
    - DEPOSIT_AUTO_RENEW_CTA:
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_ENABLE_SIMPLIFI_CREDIT_CARD_ATM:
        AppVersionConstraintConfig:
          MinAndroidVersion: 300
          MinIOSVersion: 425
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - ALFRED_SAVINGS_ACC_SIGN_UPDATE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INDIAN_SECURITIES_ETF:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INDIAN_SECURITIES_REIT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INDIAN_SECURITIES_INVIT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_UAN_EPF_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 329
          MinIOSVersion: 478
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - NSDL_PAN_API_V2_FOR_CA:
        AppVersionConstraintConfig:
          MinAndroidVersion: 343
          MinIOSVersion: 484
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - DEBIT_CARD_OFFER_WIDGET_HOME:
        AppVersionConstraintConfig:
          MinAndroidVersion: 336
          MinIOSVersion: 488
    - FEATURE_REWARD_DETAILS_IN_ORDER_RECEIPT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 350
          MinIOSVersion: 505
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - NETWORTH_REFRESH_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 346
          MinIOSVersion: 502
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_MS_CLARITY_SDK_ENABLED:
        AppVersionConstraintConfig:
          MinAndroidVersion: 368
          MinIOSVersion: 9999
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 20
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - FEATURE_REWARD_DETAILS_IN_ALL_TRANSACTIONS_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 373
          MinIOSVersion: 527
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_ASSET_FORM_PORTFOLIO_MANAGEMENT_SERVICE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 304
          MinIOSVersion: 429
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - LOANS_IDFC_VKYC_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 328
          MinIOSVersion: 482
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL

    - NSDL_PAN_FLOW_V2_MF_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 343
          MinIOSVersion: 484
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - LOANS_LIQUILOANS_EARLY_SALARY_V2:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
    - MANUAL_ASSET_FORM_EMPLOYEE_STOCK_OPTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 352
          MinIOSVersion: 506
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_ALFRED_USS_DOCUMENT_REQUEST:
        AppVersionConstraintConfig:
          MinAndroidVersion: 364
          MinIOSVersion: 514
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ANALYSER_HUB_FI_TO_FI_INTEGRATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 364
          MinIOSVersion: 546
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_OFF_APP_ENACH_CANCELLATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 363
          MinIOSVersion: 513
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
    - ASSET_LANDING_PAGE_FOR_MANUAL_ASSET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 364
          MinIOSVersion: 514
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CC_REWARD_DASHBOARD_UNSECURED_CARD:
        AppVersionConstraintConfig:
          MinAndroidVersion: 362
          MinIOSVersion: 513
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_CC_REWARD_DASHBOARD_MASS_UNSECURED_CARD:
        AppVersionConstraintConfig:
          MinAndroidVersion: 359
          MinIOSVersion: 508
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_CC_REWARD_DASHBOARD_SECURED_CARD:
        AppVersionConstraintConfig:
          MinAndroidVersion: 359
          MinIOSVersion: 508
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_DC_DASHBOARD_V2_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 372
          MinIOSVersion: 525
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_ADD_FUNDS_USS_DOUBLE_PIN_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 364
          MinIOSVersion: 514
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_CC_OFFERS_WIDGET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 367
          MinIOSVersion: 518
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_NET_WORTH_NPS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 390
          MinIOSVersion: 540
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_MONEY_SECRET_HOME_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 389
          MinIOSVersion: 545
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CC_DASHBOARD_BENEFITS_WIDGET_UNSECURED_CARD:
        AppVersionConstraintConfig:
          MinAndroidVersion: 20000
          MinIOSVersion: 531
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_IN_APP_ISSUE_REPORTING_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 386
          MinIOSVersion: 541
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_HOME_SINGLE_RPC:
        AppVersionConstraintConfig:
          MinAndroidVersion: 398
          MinIOSVersion: 566
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 10
            RolloutPercentageAndroid: 10
    - FEATURE_ALTERNATE_APP_ICON:
        AppVersionConstraintConfig:
          MinAndroidVersion: 406
          MinIOSVersion: 568
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_SMS_PARSER_PARTNER_SDK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 400
          MinIOSVersion: 9999
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 40 # SMS_PARSER_INTERNAL
    - FEATURE_LOANS_PRE_PAY_VIA_PG:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
    - FEATURE_CX_HELP_RECENT_ACTIVITY:
        AppVersionConstraintConfig:
          MinAndroidVersion: 9999
          MinIOSVersion: 9999
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_RECEIPT_NEW_ERROR_VIEW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 377
          MinIOSVersion: 531
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - PHYSICAL_DEBIT_CARD_CHARGES_PAYMENT_OPTIONS_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
    - FEATURE_PMS_PROVIDER_AND_AIF_SEARCH:
        AppVersionConstraintConfig:
          MinAndroidVersion: 379
          MinIOSVersion: 534
    - FEATURE_US_STOCKS_SIP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 393
          MinIOSVersion: 545
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_ENABLE_PAYMENT_OPTIONS_V1:
        AppVersionConstraintConfig:
          MinAndroidVersion: 397
          MinIOSVersion: 527
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_IN_APP_ISSUE_REPORTING_NON_FCR_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 386
          MinIOSVersion: 541
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_ISSUE_REPORTING_ASK_FI_INTEGRATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 402
          MinIOSVersion: 558
        UserGroupConstraintConfig:
          AllowedGroups:
            - 14 # CX_INTERNAL
    - FEATURE_HOME_BOTTOM_NAV_BAR_NETWORTH_SECTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 389
          MinIOSVersion: 545
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_LOAN_PRE_PAY_VIA_PG:
        AppVersionConstraintConfig:
          MinAndroidVersion: 397
          MinIOSVersion: 527
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
    - FEATURE_PAY_SEARCH_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 420
          MinIOSVersion: 583
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
          RolloutPercentageByPlatform:
            RolloutPercentageAndroid: 100
            RolloutPercentageIOS: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_EPF_GENERIC_ERROR_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 396
          MinIOSVersion: 547
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
    - FEATURE_REQUEST_APP_UPGRADE_FOR_CHATBOT_LOADING:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 14 # CX_INTERNAL
    - FEATURE_US_STOCKS_LIMIT_ORDER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 405
          MinIOSVersion: 566
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_MONEY_SECRET_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 401
          MinIOSVersion: 557
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_SHOW_WEALTH_ANALYSER_WIDGET_HOME:
        AppVersionConstraintConfig:
          MinAndroidVersion: 401
          MinIOSVersion: 557
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100 # Changing this would start showing regular users the wealth analyser widget irrespective feature status. Please be cautious.
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CONNECTED_ACCOUNTS_SKIP_PAN_DOB:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_WEALTH_BUILDER_PAN_COLLECTION_FORM:
        AppVersionConstraintConfig:
          MinAndroidVersion: 405
          MinIOSVersion: 566
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_SHARE_POST_PAYMENT_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 407
          MinIOSVersion: 566
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 418
          MinIOSVersion: 583
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_MONEY_SECRET_PEER_COMPARISON:
        AppVersionConstraintConfig:
          MinAndroidVersion: 401
          MinIOSVersion: 557
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - UPI_MAPPER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 420
          MinIOSVersion: 583
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
          RolloutPercentageByPlatform:
            RolloutPercentageAndroid: 100
            RolloutPercentageIOS: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SELF_TRANSFER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 410
          MinIOSVersion: 574
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_SALARY_REPORT_MONEY_SECRET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 405
          MinIOSVersion: 566
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_DC_MANDATES:
        AppVersionConstraintConfig:
          MinAndroidVersion: 419
          MinIOSVersion: 583
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_MONEY_SECRET_FOOTER_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
    # Feature flag to control whether to display new cheque components on order receipt for cheque transactions
    - FEATURE_ORDER_RECEIPT_GENERIC_DESCRIPTION_DETAILS_TILE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 400
          MinIOSVersion: 550
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_SHOW_STALE_COMPUTED_BALANCE_WARNING:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
    - FEATURE_HOME_NAV_BAR_WEALTH_BUILDER_SECTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 423
          MinIOSVersion: 585
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
    - FEATURE_ASSET_IMPORT_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 425
          MinIOSVersion: 589
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_ASSET_DASHBOARD_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 426
          MinIOSVersion: 589
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_BENEFICIARY_NAME_LOOKUP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 429
          MinIOSVersion: 591
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME:
        AppVersionConstraintConfig:
          MinAndroidVersion: 427
          MinIOSVersion: 590
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_WEALTH_BUILDER_NETWORTH_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 434
          MinIOSVersion: 598
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_MONEY_PLANT_EARNED_BENEFITS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 432
          MinIOSVersion: 596
    - FEATURE_LOANS_NEW_DATA_COLLECTION_SCREENS:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        AppVersionConstraintConfig:
          MinAndroidVersion: 441
          MinIOSVersion: 599
    - FEATURE_LOANS_PREQUAL_OFFER_FLOW:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        AppVersionConstraintConfig:
          MinAndroidVersion: 439
          MinIOSVersion: 602
    - FEATURE_UPI_QUICK_LINK_BANNER_PAY_LANDING_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 443
          MinIOSVersion: 609
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PORTFOLIO_TRACKER_LANDING_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 439
          MinIOSVersion: 601
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
    - FEATURE_SHOULD_SHOW_PIN_SCREEN_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 601
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - FEATURE_AMB_ENTRYPOINT_BANNER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 441
          MinIOSVersion: 605
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL
    - FEATURE_CX_NEW_LANDING_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 456
          MinIOSVersion: 619
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_WB_DASHBOARD_LIABILITIES:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1000000
          MinIOSVersion: ********
    - FEATURE_REWARDS_CATALOG_MERGED_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1000000
          MinIOSVersion: ********
    - FEATURE_WEEKLY_PORTFOLIO_TRACKER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 439
          MinIOSVersion: 601
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 7 # CONNECTED_ACCOUNT

USStocks:
  MorningStarBasedUIFlag:
    Disable: true
  IsSofAnalysisFlowActive: true
  A2FormURL: "https://fi.money/assets/pages/a2-form/v2"
  ETF:
    IsEnabled: true
  MinAndroidAppVersionToSupportInlineErrDuringBuyFlow: 241
  MinIOSAppVersionToSupportInlineErrDuringBuyFlow: 337
  VersionSupport:
    MinAndroidAppVersionToSupportETF: 254
    MinIOSAppVersionToSupportETF: 355
    MinAndroidAppVersionToSupportOnboardingPreRequisites: 229
    MinIOSAppVersionToSupportOnboardingPreRequisites: 331
    MinAndroidAppVersionToSupportVkycCheck: 229
    MinIOSAppVersionToSupportVkycCheck: 331
    MinIOSAppVersionToSupportPanAadhaarLinkCheck: 229
    MinAndroidAppVersionToSupportPanAadhaarLinkCheck: 331
    MinIOSAppVersionToSupportAnnouncementsInSymbolDetails: 10000
    MinAndroidAppVersionToSupportAnnouncementsInSymbolDetails: 10000
    MinAndroidAppVersionToSupportFiLite: 229
    MinIOSAppVersionToSupportFiLite: 331
    MinAndroidAppVersionToSupportProfileSuitabilityCheck: 311
    MinIOSAppVersionToSupportProfileSuitabilityCheck: 437
    MinAndroidAppVersionToSupportDropdownForCurrentlyInvestedInstruments: 330
    MinIOSAppVersionToSupportDropdownForCurrentlyInvestedInstruments: 478
    MinAndroidAppVersionToSupportNavBarAnimation: 329
    MinIOSAppVersionToSupportNavBarAnimation: 476
    MinAndroidAppVersionToSupportWalletHeaderPromo: 329
    MinIOSAppVersionToSupportWalletHeaderPromo: 476
    MinAndroidAppVersionToSupportSimilarStocks: 345
    MinIOSAppVersionToSupportSimilarStocks: 501
    MinAndroidAppVersionToSupportHiddenActivityTab: 1000
  BuyTimeoutInMilliseconds: 60000
  PriceGraphURL: "https://fi.money/fin-charts/line-chart"
  RatioGraphURL: "https://fi.money/fin-charts/multi-chart"
  PriceGraphUpdatedAt: 1678213800 # Wednesday, 8 March 2023 0:00:00 GMT+05:30
  RatioGraphUpdatedAt: 1678213800 # Wednesday, 8 March 2023 0:00:00 GMT+05:30
  IsBuyDisabled: false
  IsSellDisabled: false
  # Note: If changing this, change the corresponding min/max values in US stocks backend config
  AddFundsAmountConfig:
    # Default amount is kept equal to tax efficient amount to reduce user's losses in taxes
    DefaultAmount:
      CurrencyCode: "USD"
      Units: 15
    # Max amount is kept close to but less than 10L INR
    MaxAmount:
      CurrencyCode: "USD"
      Units: 4500
    MinAmount:
      CurrencyCode: "USD"
      Units: 15
  WithdrawFundsAmountConfig:
    DefaultAmount:
      CurrencyCode: "USD"
      Units: 1
    # Max amount is kept close to but less than 4L INR
    # The partner bank asks for forms if remitting more over single inward remittance (SWIFT)
    MaxAmount:
      CurrencyCode: "USD"
      Units: 4000
    MinAmount:
      CurrencyCode: "USD"
      Units: 1
  # Note: A duplicate config for Brokerage is defined at frontend and usstocks service
  # If below Brokerage config is changed, respective value changes should be done at usstocks config as well and vice versa
  Brokerage:
    Enabled: false
    BrokerageInPercentage: 0
    MinIosAppVersionToSupportOrderCardTextFields: 504
    MinAndroidAppVersionToSupportOrderCardTextFields: 349
  BrokerDowntime:
    IsEnabled: true
    StartTs: "2024-06-19T18:30:00+05:30" # Start date and time in RFC3339 Format
    EndTs: "2024-06-19T21:30:00+05:30" # End date and time in RFC3339 Format

Tracing:
  Enable: true

Deposit:
  Preclosure:
    ConfirmationNudge:
      FaqCategoryId: "***********" # currently its save category, also category id is different for prod and non prod
  MaturityAmountVisibility: # deposit's maturity amount feature flags belong here
    Global: true # if false, maturity amount will be hidden everywhere irrespective of the screen
    GlobalAllowedUserGroups: [ ] # allowed user groups
    SDCreation: false  # if false, maturity amount will be hidden in SD creation flow
    FDCreation: false # if false, maturity amount will be hidden in FD creation flow
    SDDetails: false # if false, maturity amount will be hidden in SD details screen
    FDDetails: true # if false, maturity amount will be hidden in FD details screen
    SDAddFunds: false # if false, maturity amount will be hidden in SD add funds flow
  Goals:
    GoalDetailsInDepositList:
      Enable: true
    GoalDetailsInDepositDetails:
      Enable: true
  AutoSave:
    PostCreationFlow:
      Enable: false
      GlobalAllowedUserGroups: [ ] # allowed user groups
    DetailsFlow:
      Enable: true
      GlobalAllowedUserGroups: [ ] # allowed user groups
      EnableAutoSaveSuggestions: true
      EnableAutoSaveRuleList: true
    PreCreationFlow:
      Enable: true
      AllowedUserGroups: [ ] # allowed user groups
  TaxSaving:
    Enable: true
    MinAndroidVersion: 10000
    MinIosVersion: 314
  Statement:
    Enable: true
    AllowedUserGroups: [ ] # allowed user groups
  Summary:
    MinIosVersionForAddMoneyBottomSheet: 10000
    MinAndroidVersionForAddMoneyBottomSheet: 10000

AnalyserParams:
  ShowAnalyser: true
  AnalyserConfigJson: "./mappingJson/analyserConfig.json"
  AnalyserReleaseConfig:
    - ANALYSER_NAME_SPEND_TOP_CATEGORIES:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 100
            IsFeatureRestricted: false
          IOSReleaseConfig:
            MinAppVersion: 100
            IsFeatureRestricted: false
        LensReleaseConfigs:
          - LENS_NAME_TOP_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 100
                  IsFeatureRestricted: false
                IOSReleaseConfig:
                  MinAppVersion: 100
                  IsFeatureRestricted: false
          - LENS_NAME_TOP_SPENDS_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 247
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 328
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
          - LENS_NAME_TOP_INVESTMENTS_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 247
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 328
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_SPEND_TOP_MERCHANTS:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 189
            IsFeatureRestricted: false
          IOSReleaseConfig:
            MinAppVersion: 275
            IsFeatureRestricted: false
        LensReleaseConfigs:
          - LENS_NAME_TOP_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 189
                  IsFeatureRestricted: false
                IOSReleaseConfig:
                  MinAppVersion: 275
                  IsFeatureRestricted: false
          - LENS_NAME_TOP_PEOPLE_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 247
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 328
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
          - LENS_NAME_TOP_MERCHANTS_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 247
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 328
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_SPEND_TIME:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 223
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 306
            IsFeatureRestricted: true
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_INCREMENTAL_DISTRIBUTION:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 223
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 306
                  IsFeatureRestricted: true
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_CREDIT_SCORE:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 210
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 295
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_CREDIT_SCORE_SUMMARY:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 210
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 295
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_INVESTMENTS_MF_GROWTH:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 256
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 357
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_PORTFOLIO_GROWTH:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 256
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 357
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_INVESTMENTS_MF_TOP_FUNDS:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 256
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 357
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_TOP_MUTUAL_FUNDS:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 256
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 357
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_INVESTMENTS_MF_ASSET_CLASS:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 256
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 357
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_MUTUAL_FUNDS_ASSET_CLASS:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 256
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 357
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_INVESTMENTS_MF_EQUITY:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 256
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 357
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_MUTUAL_FUNDS_EQUITY_MARKET_CAP:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 256
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 357
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
  AnalyserLandingPageReleaseConfig:
    - ANALYSER_LANDING_PAGE_NAME_MUTUAL_FUND:
        AndroidReleaseConfig:
          MinAppVersion: 256
          IsFeatureRestricted: false
          AllowedUserGroups:
            - "INTERNAL"
        IOSReleaseConfig:
          MinAppVersion: 357
          IsFeatureRestricted: false
          AllowedUserGroups:
            - "INTERNAL"
  AnalyserHubConfig:
    Experiments:
      - EXPERIMENT_NAME_CTA_BOTTOM_LEFT_WITHOUT_TEXT
  CreditReportParams:
    DownloadProcessExpiry: 30m
  CreditScoreAnalyserConfig:
    AutoRefreshCoolOffDurationInDays: 30
    AutoRefreshPollerTimeoutDuration: 5s
    ExperianV2InsightsConfig:
      IsEnabled: true
      ActiveFrom: "2024-04-12T00:00:00+05:30"
      ActiveTill: "2024-06-30T23:59:59+05:30"
  MfAnalyserRefreshBannerReleaseConfig:
    IsFeatureRestricted: false
    AllowedUserGroups:
      - "INTERNAL"
  LoanAnalyserInsightsParams:
    PlSegmentAndInsightContent:
      "0":
        ArrayElement:
          Position: 0
        SegmentExpression: "(IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # 3,4 are SG loan segments
        InsightDetails:
          Title: "You are eligible to borrow upto ₹5,00,000 "
          TitleColor: "#333333"
          Image: "https://epifi-icons.pointz.in/preapproved/pl-analyser-gift-box.png"
          CtaText: "No Pre- closure charges"
          CtaTextColor: "#333333"
          CtaColor: "#F6E1C1"
          BgColor: "#FFFCEB"
      "1":
        ArrayElement:
          Position: 1
        SegmentExpression: ""
        InsightDetails:
          Title: "You are eligible to borrow upto ₹5,00,000 "
          TitleColor: "#37522A"
          Image: "https://epifi-icons.pointz.in/preapprovedloan/pl-analyser-cash-icon.png"
          CtaText: "Claim Now"
          CtaTextColor: "#F6F9FD"
          CtaColor: "#648E4D"
          BgColor: "#EDF5EB"
      "2":
        ArrayElement:
          Position: 2
        SegmentExpression: ""
        InsightDetails:
          Title: "You are eligible to borrow upto ₹5,00,000 "
          TitleColor: "#37522A"
          Image: "https://epifi-icons.pointz.in/preapprovedloan/pl-analyser-gift-cash.png"
          CtaText: "Claim Now"
          CtaTextColor: "#F6F9FD"
          CtaColor: "#648E4D"
          BgColor: "#D5E6CE"
    LamfSegmentAndInsightContent:
      "0":
        ArrayElement:
          Position: 0
        SegmentExpression: "IsMember('********-0ca8-4165-a3f7-cf4aed91e1b9') && !(IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # 3,4 are SG loan segments
        InsightDetails:
          Title: "Get upto ₹1 cr against your mutual funds"
          Image: "https://epifi-icons.pointz.in/preapprovedloan/pl-spend-analyser.png"
          CtaText: "   Get upto ₹1 cr without paperwork   "
      "1":
        ArrayElement:
          Position: 1
        SegmentExpression: ""
        InsightDetails:
          Title: "Unlock Cash from your Mutual Fund Investments"
          Image: "https://epifi-icons.pointz.in/preapprovedloan/pl-analyser-money-bag.png"
          CtaText: " Get upto ₹1 cr without paperwork "
  IstxnCatRestricted: true
  AllowedGroups:
    - 1 # INTERNAL

P2PInvestment:
  Activity:
    AppVersionConstraintConfig:
      MinAndroidVersion: 161
      MinIOSVersion: 226
  RecentActivity:
    AppVersionConstraintConfig:
      MinAndroidVersion: 163
      MinIOSVersion: 283
  CloseJump:
    IsEnable: true
    StartDate: "2024-08-16"
    EndDate: "2025-12-31"
    Icon: "https://epifi-icons.pointz.in/p2pinvestment/blocker.png"
    Title: "Jump is unavailable for further investments"
    SubTitle: "We would be back soon, until then check our other wealth building products such as US Stocks and Mutual Funds"
  ShowForceUpgradeForAndroid: true
  ShowForceUpgradeForIOS: true
  UseUpdatedDashboard: false
  EnableDummyDataForRenewalFlow: false
  MinIosVersionForErrorDeeplinkInInvestScreen: 379
  MinAndroidVersionForErrorDeeplinkInInvestScreen: 277
  SchemeTenureBackwardCompatibilityAndroidVersion: 239
  SchemeTenureBackwardCompatibilityIOSVersion: 334
  DeeplinkV2CompatibilityAndroidVersion: 254
  MinIosVersionForDynamicSlider: 378
  MinIosVersionForPromotionLoadingScreen: 376
  MinAndroidVersionForPromotionLoadingScreen: 272
  SchemesAvailability:
    - "SCHEME_NAME_LL_BOOSTER":
        IsUnavailable: true
        StartTime: "2023-08-01 00:00:00"
        EndTime: "2023-08-05 00:00:00"
    - "SCHEME_NAME_LL_9_75_FESTIVE":
        IsUnavailable: true
        StartTime: "2024-03-31 23:59:59"
        EndTime: "2025-03-31 23:59:59"
  MinIosVersionForActivityPagination: 10000
  MinAndroidVersionForActivityPagination: 10000
  MinIosVersionForConsentCardV2: 381
  MinAndroidVersionForConsentCardV2: 281
  DisableFlexiSchemeBanners: true

Goals:
  GoalDiscovery:
    Enable: true
    AllowedGroups: [ ] # allowed user groups
  GoalDiscoveryInExistingInvestmentInstrument:
    Enable: true
    AllowedGroups: [ ] # allowed user groups

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: true
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

AppLogs:
  MaxMessageCountThreshold: 5000

QrDeeplinkParams:
  MinAndroidVersionForAmountScreenDeeplink: 431
  MinIosVersionForAmountScreenDeeplink: 261

SharedConfig:
  EnableV2SafetyNetFlow: true

Lending:
  PreApprovedLoan:
    Enabled: true
    AppVersionConstraintConfig:
      MinAndroidVersion: 200
      MinIOSVersion: 293
    FederalPlAppVersionConstraintConfig:
      MinAndroidVersion: 336
      MinIOSVersion: 488
    LiquiloansPlAppVersionConstraintConfig:
      MinAndroidVersion: 336
      MinIOSVersion: 488
    LiquiloansEsAppVersionConstraintConfig:
      MinAndroidVersion: 284
      MinIOSVersion: 385
    LiquiloansFldgAppVersionConstraintConfig:
      MinAndroidVersion: 343
      MinIOSVersion: 500
    LiquiloansStplAppVersionConstraintConfig:
      MinAndroidVersion: 336
      MinIOSVersion: 488
    LiquiloansFlPlAppVersionConstraintConfig:
      MinAndroidVersion: 336
      MinIOSVersion: 458
    LiquiloansNonFiCoreAppVersionConstraintConfig:
      MinAndroidVersion: 378
      MinIOSVersion: 532
    IdfcPlAppVersionConstraintConfig:
      MinAndroidVersion: 336
      MinIOSVersion: 458
    FiftyfinLamfAppVersionConstraintConfig:
      MinAndroidVersion: 345
      MinIOSVersion: 501
    PollScreenV2AppVersionConstraintConfig:
      MinAndroidVersion: 340
      MinIOSVersion: 490
    AbflPlAppVersionConstraintConfig:
      MinAndroidVersion: 336
      MinIOSVersion: 488
    AbflPwaAppVersionConstraintConfig:
      MinAndroidVersion: 336
      MinIOSVersion: 581
    MvNonFiCorePwaAppVersionConstraintConfig:
      MinAndroidVersion: 336
      MinIOSVersion: 581
    LendenPlAppVersionConstraintConfig:
      MinAndroidVersion: 443
      MinIOSVersion: 609
    FederalRealTimeDistNtbAppVersionConstraintConfig:
      MinAndroidVersion: 441
      MinIOSVersion: 605
    RealTimeEtbEligibilityAppVersionConstraintConfig:
      MinAndroidVersion: 336
      MinIOSVersion: 488
    MvPlAppVersionConstraintConfig:
      MinAndroidVersion: 362
      MinIOSVersion: 510
    SkipLandingScreenConstraintConfig:
      MinAndroidVersion: 5000
      MinIOSVersion: 5000
    RealTimeSubventionAppVersionConstraintConfig:
      MinAndroidVersion: 336
      MinIOSVersion: 488
    RealTimeStplAppVersionConstraintConfig:
      MinAndroidVersion: 336
      MinIOSVersion: 488
    StockGuradianRealTimeSubvAppVersionConstraintConfig:
      MinAndroidVersion: 402
      MinIOSVersion: 563
    RealTimeOfferUpdateAppVersionConstraintConfig:
      MinAndroidVersion: 372
      MinIOSVersion: 525
    PostDisbursalV2FlowAppVersionConstraintConfig:
      AppVersionConstraintConfig:
        MinAndroidVersion: 343
        MinIOSVersion: 500
      SkipVendorLoanProgramCheck: false
      VendorLoanProgramMap:
        - "LIQUILOANS:LOAN_PROGRAM_FLDG": true
        - "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
        - "LIQUILOANS:LOAN_PROGRAM_STPL": true
        - "MONEYVIEW:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
        - "LIQUILOANS:LOAN_PROGRAM_ACQ_TO_LEND": true
        - "LIQUILOANS:LOAN_PROGRAM_FI_LITE_PL": true
        - "IDFC:LOAN_PROGRAM_PRE_APPROVED_LOAN": false
        - "FEDERAL_BANK:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
        - "FEDERAL_BANK:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
        - "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
        - "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION": true
        - "LIQUILOANS:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
        - "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION": true
        - "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL": true
        - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
        - "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
        - "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
        - "FEDERAL_BANK:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB": true
        - "MONEYVIEW:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
    FederalAaAppVersionConstraintConfig:
      MinAndroidVersion: 368
      MinIOSVersion: 520
    EnablePlLiquiloans: false
    Prepay:
      Enabled: true
      V2Config:
        Enabled: true
        AppVersionConstraintConfig:
          MinAndroidVersion: 370
          MinIOSVersion: 524
    PrePayConfig:
      LLFldg:
        Enabled: true
        Tpap:
          Enabled: false
      LLEs:
        Enabled: true
        Tpap:
          Enabled: false
      LLAcqToLend:
        Enabled: true
        AppVersionConstraintConfig:
          MinAndroidVersion: 301
          MinIOSVersion: 427
        Tpap:
          Enabled: false
      IdfcPl:
        Tpap:
          Enabled: false
      FederalPl:
        Tpap:
          Enabled: false
      RTSubvention:
        Enabled: true
        AppVersionConstraintConfig:
          MinAndroidVersion: 360
          MinIOSVersion: 510
      PrePayViaPGConfig:
        LenderLoanProgramToIsPgEnabledMap:
          - "LIQUILOANS:LOAN_PROGRAM_FLDG": true
          - "LIQUILOANS:LOAN_PROGRAM_STPL": false
          - "LIQUILOANS:LOAN_PROGRAM_REALTIME_STPL": true
          - "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL": true
          - "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION": true
          - "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION": true
          - "LIQUILOANS:LOAN_PROGRAM_ACQ_TO_LEND": true
          - "LIQUILOANS:LOAN_PROGRAM_FI_LITE_PL": true
          - "LIQUILOANS:LOAN_PROGRAM_EARLY_SALARY": false
    Vendor: 1 #FEDERAL
    Downtime:
      Start: "31-03-2024T20:00:00"
      End: "02-04-2024T07:59:59"
    InstantCashSegmentId: "562e2816-dcfb-4812-b7d4-c1c739e3aa3b"
    LoanNonEligibleScreenV2:
      AppVersionConstraintConfig:
        MinAndroidVersion: 500
        MinIOSVersion: 500
    IsLoanOriginationDropOffFeedbackEnabled: true
    ITRFlow:
      Enabled: true
      AppVersionConstraintConfig:
        MinAndroidVersion: 338
        MinIOSVersion: 493
    LoanDetailsSelectionV2Flow:
      EnableLoanPrograms:
        - IDFC
        - FEDERAL_BANK
        - LOAN_PROGRAM_PRE_APPROVED_LOAN
        - LOAN_PROGRAM_FLDG
        - LOAN_PROGRAM_STPL
        - LOAN_PROGRAM_ACQ_TO_LEND
      DefaultAmountPercentage:
        - "LIQUILOANS": 0.95
        - "IDFC": 1
        - "FEDERAL": 1
    NewLoanOfferNavBarHighLight:
      # Note: Make sure to change the ID when changing highlight contents
      # so that clients reset highlight interaction counts on their side
      Id: "upto-5lakh-loan-offer-highlight"
      Disable: false
      LottieUrl: "https://epifi-icons.pointz.in/lending/upto-5lakh-loan-home-nav-bar-icon.json"
      MaxSessions: 20
      MaxClicks: 5
      Metadata:
        - "Reason": "upto 5L loan offer"
    #      SegmentExpression: "(IsMember('f00dd7cc-cbd4-4d3e-941c-b9a5a7f10cfb') || IsMember('9b91b527-3548-4a9f-b4d7-aee65a3f0bd0'))"
    MinKycUsersReleaseConfig:
      MinAndroidVersion: 343
      MinIOSVersion: 500
      SegmentExpression: "IsMember('1d8ef412-5f54-4189-9d0d-aa1fa7a4a7c2')"
    OfferDetailsV4Config:
      IsEnabled: true
      #     to be changed with prod release
      AppVersionConstraintConfig:
        MinAndroidVersion: 359
        MinIOSVersion: 506
      VendorLoanProgramMap:
        - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 455
              MinIOSVersion: 613
        - "FEDERAL_BANK:LOAN_PROGRAM_PRE_APPROVED_LOAN":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 455
              MinIOSVersion: 613
        - "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 455
              MinIOSVersion: 613
        - "FEDERAL_BANK:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 455
              MinIOSVersion: 613
        - "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
            IsEnabled: true
        - "FEDERAL_BANK:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 455
              MinIOSVersion: 613
        - "MONEYVIEW:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 455
              MinIOSVersion: 613
        - "MONEYVIEW:LOAN_PROGRAM_PRE_APPROVED_LOAN":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 455
              MinIOSVersion: 613
      SkipAmountSelectionScreen: true
      ShowInterestRate: true
      ShowZeroPreClosureTag: true
    OfferDetailsV3Config:
      IsEnabled: true
      AppVersionConstraintConfig:
        MinAndroidVersion: 393
        MinIOSVersion: 545
      VendorLoanProgramMap:
        - "LIQUILOANS:LOAN_PROGRAM_FLDG": true
        - "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
        - "LIQUILOANS:LOAN_PROGRAM_STPL": true
        - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
        - "FEDERAL_BANK:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
        - "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN": false
        - "FEDERAL_BANK:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
        - "FEDERAL_BANK:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB": true
      SkipAmountSelectionScreen: true
      ShowInterestRate: true
      ShowZeroPreClosureTag: true
    PrePayShortcutSegmentExpression: "IsMember('************************************')"
    DesiredLoanAmountAppVersionConstraintConfig:
      MinAndroidVersion: 431
      MinIOSVersion: 593
    AutoCancelCurrentLrConfig:
      MinAndroidVersion: 10000
      MinIOSVersion: 10000
  LoanProgram: 1 #PRE-APPROVED LOAN
  SecuredLoanParams:
    HomeNavBarSegmentExpression: "IsMember('********-0ca8-4165-a3f7-cf4aed91e1b9') && !(IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))"  # 3,4 are SG loan segments
    DowntimeConfig:
      # Adding downtime between 13th December 2024 00:00hrs to 15th December 2024 00:00hrs
      Start: "13-12-2024T23:59:59"
      End: "15-12-2024T23:59:59"
    ReleasedSegmentDetails:
      IsEnabled: true
      Expression: "IsMember('79b60a3b-964d-41c4-a448-1d1de5658884')"
    EnablePreCloseFlow: true
    EnableAutoPaySetupBanner: false

DepositIcons:
  FDUrl: "https://epifi-icons.pointz.in/deposit/fixed_deposit1.svg"
  SDUrl: "https://epifi-icons.pointz.in/deposit/smart_deposit1.svg"

HomeV2DepositIcons:
  FDUrl: "https://epifi-icons.pointz.in/deposit/fixed_deposit2.png"
  SDUrl: "https://epifi-icons.pointz.in/deposit/smart_deposit2.png"

UserProfile:
  EnableEditCommAddress: false
  EnableEditEmployment: true
  ProfileHeaderV2MinVersion:
    MinVersionAndroid: 323
    MinVersionIos: 466
  IsEmailEditableConfig:
    DisableFeature: true
    MinAndroidVersion: 357
    MinIOSVersion: 1
  IsContactDetailsEditable:
    DisableFeature: false
    FallbackToEnableFeature: false
    MinAndroidVersion: 402
    MinIOSVersion: 560
  IsAadhaarCommsAddressUpdateEnabled:
    DisableFeature: true
    FallbackToEnableFeature: false
    MinAndroidVersion: 99999
    MinIOSVersion: 99999
AddFundsV2Params:
  ReleaseParams:
    MinVersionAndroid: 229
    MinVersionIos: 331
  IsIosUrnFlowEnabled: true
  SkipAddFundsV2: true
  WhitelistedActorIds: [
    "AC210111uWVrMh3gRJ+iZHnVIA685Q==", # Hardik Agrawal
    "AC220103Hapts4DoRsqNUTkIYeereA==", # Sainath Singineedi
    "AC220705di1dtkIkTOmUHaGxDy1IEA==", # Surya Gangaraj
    "AC210721BAKDIpfuSV6avTCqaNqs/w==" # Sayan Chaudhuri
  ]
  UpiAppUrnPrefixMap:
    - "UPI_APP_GOOGLE_PAY":
        DisplayName: "Google Pay"
        UrnPrefix: "gpay://upi/"
        ImageUrl: "https://epifi-icons.pointz.in/tiering/add_funds/google_pay.png"
    - "UPI_APP_PHONE_PE":
        DisplayName: "Phone Pe"
        UrnPrefix: "phonepe://upi/"
        ImageUrl: "https://epifi-icons.pointz.in/tiering/add_funds/phone_pe.png"
    - "UPI_APP_PAYTM":
        DisplayName: "Paytm"
        UrnPrefix: "paytmmp://upi/"
        ImageUrl: "https://epifi-icons.pointz.in/tiering/add_funds/pay_tm.png"
  #    - "UPI_APP_BHIM_UPI":
  #        DisplayName: "BHIM UPI"
  #        UrnPrefix: "upi://"
  #        ImageUrl: "https://epifi-icons.pointz.in/tiering/add_funds/bhim_upi.png"
  OnboardingAddFundsV2ScreenDetails:
    ShowManualAccountBalanceRefreshCta: true
    # Note: This value should be in sync with AddMoneyBalanceOptions in user config.
    MinAmount: 2000
    MaxAmount: 25000
    MinKYCMaxAmount: 25000
    DefaultAmount: 2000
    SuggestedAmounts: [ 2000, 5000, 10000, 20000 ]
    SalaryB2BSignupUrl: "https://fi.money/signup"
    AffluenceClassesEligibleForUpdatedBenefits:
      - "AFFLUENCE_CLASS_CLASS_1": true
      - "AFFLUENCE_CLASS_CLASS_2": true
      - "AFFLUENCE_CLASS_CLASS_3": false
      - "AFFLUENCE_CLASS_CLASS_4": false
      - "AFFLUENCE_CLASS_CLASS_5": false
    # add a new flow along with whether it is enabled/disabled
    # add the different type of ranges - size of ranges can be adjusted [0-50k,50k-1L] or [0-10k,10k-50k-50k-1L]
    PageInfoForFlowsMap:
      - "FLOW_REFERRED_USER": # referred user
          IsEnabled: true
          NudgeForHigherAmountAdditionThreshold: 3000
          NudgeForHigherAmountAdditionBottomSheetTitle: "Don’t want your ₹100 joining bonus?"
          NudgeForHigherAmountAdditionBottomSheetTitleAffluent: "Don’t want your ₹200 joining bonus?"
          NudgeForHigherAmountAdditionBottomSheetSubtitle: "Just deposit %s or more in your Savings Account today to claim this bonus"
          BenefitInfoPopUpDetails:
            ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/info-popup.png"
            Title: "How to activate benefits?"
            Description: "You can activate exclusive benefits with a single tap of your screen. When you reach the Fi home for account plans, join the Infinite plan."
          FaqDetails:
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-bank.png"
              Title: "Why should I add money?"
              Description: "To benefit from Fi’s features, you’ll need funds in the account! Do more on Fi: Make UPI payments, invest at 0% commission, analyse your spends, unlock rewards, etc."
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-rocket.png"
              Title: "How do I activate benefits?"
              Description: "You can activate exclusive benefits with a single tap of your screen. When you reach the Fi home for account plans, join the Infinite plan."
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-shield.png"
              Title: "Is my money safe?"
              Description: "Yes. Funds deposited through Fi are secure as it lies with Federal Bank, our licensed partner bank. Plus, your money is insured for up to ₹5 lakh by the DICGC."
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-money-bag.png"
              Title: "What’s the minimum amount?"
              Description: "Add at least ₹100 to start exploring Fi. However, after this, you can withdraw your money anytime from the account."
          RangeBasedBenefits:
            - "RANGE_0":
                MinAmount: 0
                MaxAmount: 9999
                Benefits:
                  - MinAmount: 3000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹100 joining bonus"
                    TitleDisabled: "You get a ₹100 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 3k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "1x rewards on your spends"
                    TitleDisabled: "1x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "1x rewards on your spends"
                    TitleDisabledAffluentUser: "1x rewards on your spends"
            - "RANGE_1":
                MinAmount: 10000
                MaxAmount: 49999
                Benefits:
                  - MinAmount: 3000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹100 joining bonus"
                    TitleDisabled: "You get a ₹100 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 3k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "2x rewards on your spends"
                    TitleDisabled: "2x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "2x rewards on your spends"
                    TitleDisabledAffluentUser: "2x rewards on your spends"
            - "RANGE_2":
                MinAmount: 50000
                MaxAmount: -1
                Benefits:
                  - MinAmount: 3000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹100 joining bonus"
                    TitleDisabled: "You get a ₹100 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 3k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "4x rewards on your spends"
                    TitleDisabled: "4x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "4x rewards on your spends"
                    TitleDisabledAffluentUser: "4x rewards on your spends"
      - "FLOW_NON_REFERRED_USER": # non referred user
          IsEnabled: true
          NudgeForHigherAmountAdditionThreshold: 5000
          NudgeForHigherAmountAdditionBottomSheetTitle: "Don’t want your ₹200 joining bonus?"
          NudgeForHigherAmountAdditionBottomSheetTitleAffluent: "Don’t want your ₹200 joining bonus?"
          NudgeForHigherAmountAdditionBottomSheetSubtitle: "Just deposit %s or more in your Savings Account today to claim this bonus"
          BenefitInfoPopUpDetails:
            ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/info-popup.png"
            Title: "How to activate benefits?"
            Description: "You can activate exclusive benefits with a single tap of your screen. When you reach the Fi home for account plans, join the Infinite plan."
          FaqDetails:
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-bank.png"
              Title: "Why should I add money?"
              Description: "To benefit from Fi’s features, you’ll need funds in the account! Do more on Fi: Make UPI payments, invest at 0% commission, analyse your spends, unlock rewards, etc."
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-rocket.png"
              Title: "How do I activate benefits?"
              Description: "You can activate exclusive benefits with a single tap of your screen. When you reach the Fi home for account plans, join the Infinite plan."
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-shield.png"
              Title: "Is my money safe?"
              Description: "Yes. Funds deposited through Fi are secure as it lies with Federal Bank, our licensed partner bank. Plus, your money is insured for up to ₹5 lakh by the DICGC."
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-money-bag.png"
              Title: "What’s the minimum amount?"
              Description: "Add at least ₹100 to start exploring Fi. However, after this, you can withdraw your money anytime from the account."
          RangeBasedBenefits:
            - "RANGE_0":
                MinAmount: 0
                MaxAmount: 9999
                Benefits:
                  - MinAmount: 5000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹200 joining bonus"
                    TitleDisabled: "You get a ₹200 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 5k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "1x rewards on your spends"
                    TitleDisabled: "1x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "1x rewards on your spends"
                    TitleDisabledAffluentUser: "1x rewards on your spends"
            - "RANGE_1":
                MinAmount: 10000
                MaxAmount: 49999
                Benefits:
                  - MinAmount: 5000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹200 joining bonus"
                    TitleDisabled: "You get a ₹200 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 5k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "2x rewards on your spends"
                    TitleDisabled: "2x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "2x rewards on your spends"
                    TitleDisabledAffluentUser: "2x rewards on your spends"
            - "RANGE_2":
                MinAmount: 50000
                MaxAmount: -1
                Benefits:
                  - MinAmount: 5000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹200 joining bonus"
                    TitleDisabled: "You get a ₹200 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 5k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "4x rewards on your spends"
                    TitleDisabled: "4x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "4x rewards on your spends"
                    TitleDisabledAffluentUser: "4x rewards on your spends"
      - "FLOW_REFERRED_SALARY_B2B_USER": # referred salary b2b user
          IsEnabled: true
          NudgeForHigherAmountAdditionThreshold: 3000
          NudgeForHigherAmountAdditionBottomSheetTitle: "Don’t want your ₹100 joining bonus?"
          NudgeForHigherAmountAdditionBottomSheetTitleAffluent: "Don’t want your ₹200 joining bonus?"
          NudgeForHigherAmountAdditionBottomSheetSubtitle: "Just deposit %s or more in your Savings Account today to claim this bonus"
          BenefitInfoPopUpDetails:
            ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/info-popup.png"
            Title: "How to activate benefits?"
            Description: "You don’t have to do anything! As soon as your first salary credits in your account, your exclusive benefits will activate"
          FaqDetails:
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-rocket.png"
              Title: "How do I activate benefits?"
              Description: "Once your first salary gets credited into your Federal Bank Account, all benefits get instantly activated"
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-shield.png"
              Title: "Is my money safe?"
              Description: "Yes. Funds deposited through Fi are secure as it lies with Federal Bank, our licensed partner bank. Plus, your money is insured for up to ₹5 lakh by the DICGC."
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-money-bag.png"
              Title: "Can I skip this step?"
              Description: "Yes you can! But you’ll need funds to use Fi’s benefits: make UPI payments, invest at 0 commission, analyse your spends, get rewards & more."
          RangeBasedBenefits:
            - "RANGE_0":
                MinAmount: 0
                MaxAmount: 9999
                Benefits:
                  - MinAmount: 3000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹100 joining bonus"
                    TitleDisabled: "You get a ₹100 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 3k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "4x rewards on your spends"
                    TitleDisabled: "4x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "4x rewards on your spends"
                    TitleDisabledAffluentUser: "4x rewards on your spends"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-dc.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-dc-disabled.png"
                    Title: "Free debit card with 0 forex fee"
                    TitleDisabled: "Free debit card with 0 forex fee"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "Free debit card with 0 forex fee"
                    TitleDisabledAffluentUser: "Free debit card with 0 forex fee"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-cashback.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-cashback-disabled.png"
                    Title: "2% Cash-back on spends"
                    TitleDisabled: "2% Cash-back on spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "2% Cash-back on spends"
                    TitleDisabledAffluentUser: "2% Cash-back on spends"
            - "RANGE_1":
                MinAmount: 10000
                MaxAmount: 49999
                Benefits:
                  - MinAmount: 3000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹100 joining bonus"
                    TitleDisabled: "You get a ₹100 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 3k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "4x rewards on your spends"
                    TitleDisabled: "4x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "4x rewards on your spends"
                    TitleDisabledAffluentUser: "4x rewards on your spends"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-dc.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-dc-disabled.png"
                    Title: "Free debit card with 0 forex fee"
                    TitleDisabled: "Free debit card with 0 forex fee"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "Free debit card with 0 forex fee"
                    TitleDisabledAffluentUser: "Free debit card with 0 forex fee"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-cashback.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-cashback-disabled.png"
                    Title: "2% Cash-back on spends"
                    TitleDisabled: "2% Cash-back on spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "2% Cash-back on spends"
                    TitleDisabledAffluentUser: "2% Cash-back on spends"
            - "RANGE_2":
                MinAmount: 50000
                MaxAmount: -1
                Benefits:
                  - MinAmount: 3000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹100 joining bonus"
                    TitleDisabled: "You get a ₹100 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 3k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "4x rewards on your spends"
                    TitleDisabled: "4x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "4x rewards on your spends"
                    TitleDisabledAffluentUser: "4x rewards on your spends"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-dc.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-dc-disabled.png"
                    Title: "Free debit card with 0 forex fee"
                    TitleDisabled: "Free debit card with 0 forex fee"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "Free debit card with 0 forex fee"
                    TitleDisabledAffluentUser: "Free debit card with 0 forex fee"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-cashback.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-cashback-disabled.png"
                    Title: "2% Cash-back on spends"
                    TitleDisabled: "2% Cash-back on spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "2% Cash-back on spends"
                    TitleDisabledAffluentUser: "2% Cash-back on spends"
      - "FLOW_NON_REFERRED_SALARY_B2B_USER": # non-referred salary b2b user
          IsEnabled: true
          NudgeForHigherAmountAdditionThreshold: 5000
          NudgeForHigherAmountAdditionBottomSheetTitle: "Don’t want your ₹200 joining bonus?"
          NudgeForHigherAmountAdditionBottomSheetTitleAffluent: "Don’t want your ₹200 joining bonus?"
          NudgeForHigherAmountAdditionBottomSheetSubtitle: "Just deposit %s or more in your Savings Account today to claim this bonus"
          BenefitInfoPopUpDetails:
            ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/info-popup.png"
            Title: "How to activate benefits?"
            Description: "You don’t have to do anything! As soon as your first salary credits in your account, your exclusive benefits will activate"
          FaqDetails:
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-rocket.png"
              Title: "How do I activate benefits?"
              Description: "Once your first salary gets credited into your Federal Bank Account, all benefits get instantly activated"
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-shield.png"
              Title: "Is my money safe?"
              Description: "Yes. Funds deposited through Fi are secure as it lies with Federal Bank, our licensed partner bank. Plus, your money is insured for up to ₹5 lakh by the DICGC."
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-money-bag.png"
              Title: "Can I skip this step?"
              Description: "Yes you can! But you’ll need funds to use Fi’s benefits: make UPI payments, invest at 0 commission, analyse your spends, get rewards & more."
          RangeBasedBenefits:
            - "RANGE_0":
                MinAmount: 0
                MaxAmount: 9999
                Benefits:
                  - MinAmount: 5000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹200 joining bonus"
                    TitleDisabled: "You get a ₹200 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 5k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "4x rewards on your spends"
                    TitleDisabled: "4x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "4x rewards on your spends"
                    TitleDisabledAffluentUser: "4x rewards on your spends"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-dc.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-dc-disabled.png"
                    Title: "Free debit card with 0 forex fee"
                    TitleDisabled: "Free debit card with 0 forex fee"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "Free debit card with 0 forex fee"
                    TitleDisabledAffluentUser: "Free debit card with 0 forex fee"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-cashback.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-cashback-disabled.png"
                    Title: "2% Cash-back on spends"
                    TitleDisabled: "2% Cash-back on spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "2% Cash-back on spends"
                    TitleDisabledAffluentUser: "2% Cash-back on spends"
            - "RANGE_1":
                MinAmount: 10000
                MaxAmount: 49999
                Benefits:
                  - MinAmount: 5000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹200 joining bonus"
                    TitleDisabled: "You get a ₹200 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 5k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "4x rewards on your spends"
                    TitleDisabled: "4x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "4x rewards on your spends"
                    TitleDisabledAffluentUser: "4x rewards on your spends"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-dc.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-dc-disabled.png"
                    Title: "Free debit card with 0 forex fee"
                    TitleDisabled: "Free debit card with 0 forex fee"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "Free debit card with 0 forex fee"
                    TitleDisabledAffluentUser: "Free debit card with 0 forex fee"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-cashback.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-cashback-disabled.png"
                    Title: "2% Cash-back on spends"
                    TitleDisabled: "2% Cash-back on spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "2% Cash-back on spends"
                    TitleDisabledAffluentUser: "2% Cash-back on spends"
            - "RANGE_2":
                MinAmount: 50000
                MaxAmount: -1
                Benefits:
                  - MinAmount: 5000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹200 joining bonus"
                    TitleDisabled: "You get a ₹200 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 5k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "4x rewards on your spends"
                    TitleDisabled: "4x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "4x rewards on your spends"
                    TitleDisabledAffluentUser: "4x rewards on your spends"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-dc.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-dc-disabled.png"
                    Title: "Free debit card with 0 forex fee"
                    TitleDisabled: "Free debit card with 0 forex fee"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "Free debit card with 0 forex fee"
                    TitleDisabledAffluentUser: "Free debit card with 0 forex fee"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-cashback.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-cashback-disabled.png"
                    Title: "2% Cash-back on spends"
                    TitleDisabled: "2% Cash-back on spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "2% Cash-back on spends"
                    TitleDisabledAffluentUser: "2% Cash-back on spends"
      - "FLOW_DEFAULT": # default flow
          IsEnabled: true
          NudgeForHigherAmountAdditionThreshold: 5000
          NudgeForHigherAmountAdditionBottomSheetTitle: "Don’t want your ₹200 joining bonus?"
          NudgeForHigherAmountAdditionBottomSheetTitleAffluent: "Don’t want your ₹200 joining bonus?"
          NudgeForHigherAmountAdditionBottomSheetSubtitle: "Just deposit %s or more in your Savings Account today to claim this bonus"
          BenefitInfoPopUpDetails:
            ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/info-popup.png"
            Title: "How to activate benefits?"
            Description: "You can activate exclusive benefits with a single tap of your screen. When you reach the Fi home for account plans, join the Infinite plan."
          FaqDetails:
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-bank.png"
              Title: "Why should I add money?"
              Description: "To benefit from Fi’s features, you’ll need funds in the account! Do more on Fi: Make UPI payments, invest at 0% commission, analyse your spends, unlock rewards, etc."
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-rocket.png"
              Title: "How do I activate benefits?"
              Description: "You can activate exclusive benefits with a single tap of your screen. When you reach the Fi home for account plans, join the Infinite plan."
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-shield.png"
              Title: "Is my money safe?"
              Description: "Yes. Funds deposited through Fi are secure as it lies with Federal Bank, our licensed partner bank. Plus, your money is insured for up to ₹5 lakh by the DICGC."
            - ImageUrl: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/faq-money-bag.png"
              Title: "What’s the minimum amount?"
              Description: "Add at least ₹100 to start exploring Fi. However, after this, you can withdraw your money anytime from the account."
          RangeBasedBenefits:
            - "RANGE_0":
                MinAmount: 0
                MaxAmount: 9999
                Benefits:
                  - MinAmount: 5000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹200 joining bonus"
                    TitleDisabled: "You get a ₹200 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 5k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "1x rewards on your spends"
                    TitleDisabled: "1x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "1x rewards on your spends"
                    TitleDisabledAffluentUser: "1x rewards on your spends"
            - "RANGE_1":
                MinAmount: 10000
                MaxAmount: 49999
                Benefits:
                  - MinAmount: 5000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹200 joining bonus"
                    TitleDisabled: "You get a ₹200 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 5k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "2x rewards on your spends"
                    TitleDisabled: "2x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "2x rewards on your spends"
                    TitleDisabledAffluentUser: "2x rewards on your spends"
            - "RANGE_2":
                MinAmount: 50000
                MaxAmount: -1
                Benefits:
                  - MinAmount: 5000
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-box-disabled.png"
                    Title: "You get a ₹200 joining bonus"
                    TitleDisabled: "You get a ₹200 joining bonus"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: "Add 5k"
                    TitleAffluentUser: "You get a ₹200 joining bonus"
                    TitleDisabledAffluentUser: "You get a ₹200 joining bonus"
                  - MinAmount: 0
                    LeftIcon: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    LeftIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/benefits-rewards.png"
                    Title: "4x rewards on your spends"
                    TitleDisabled: "4x rewards on your spends"
                    RightIconEnabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-enabled-v2_1.png"
                    RightIconDisabled: "https://epifi-icons.pointz.in/onboarding/add_funds_v2/check-disabled-v2_1.png"
                    RightTextEnabled: ""
                    RightTextDisabled: ""
                    TitleAffluentUser: "4x rewards on your spends"
                    TitleDisabledAffluentUser: "4x rewards on your spends"

MinKycMandatoryAddFundConfig:
  IsEnabled: false
  MinimumAmount:
    CurrencyCode: "INR"
    Units: 5000
    Nanos: 0

QuestSdk:
  Disable: false

HomeRevampParams:
  NreSavingsDashboardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 377
    IsEnableOnIos: true
    MinIosVersion: 539
  NroSavingsDashboardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 10000
    IsEnableOnIos: true
    MinIosVersion: 10000
  MutualfundDashboardCardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 417
    IsEnableOnIos: true
    MinIosVersion: 577
  CreditScoreDashboardCardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 417
    IsEnableOnIos: true
    MinIosVersion: 577
  EpfDashboardCardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 417
    IsEnableOnIos: true
    MinIosVersion: 577
  DcInternationalWidgetReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 432
    IsEnableOnIos: false
    MinIosVersion: 599
  BgColorForBottomNavBarReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 0
    IsEnableOnIos: true
    MinIosVersion: 500
  FeatureWidgetsReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 309
    IsEnableOnIos: true
    MinIosVersion: 444
  RewardsWidgetSeparationReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 299
    IsEnableOnIos: true
    MinIosVersion: 424
  DashboardVersionV2ReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 294
    IsEnableOnIos: true
    MinIosVersion: 410
  NewSearchUIReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 294
    IsEnableOnIos: true
    MinIosVersion: 410
  ShortcutIconTypeToDetailsMap:
    SHORTCUT_FI_STORE_MILES_EXCHANGE:
      Title: "Travel\nMiles"
      ImageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/miles-exchange-icon.png"
      Deeplink: '{"screen":"WEB_PAGE", "webPageScreenOptions":{"webpageTitle":"Fi Store", "webpageUrl":"https://fimoney.poshvine.com/points-xchange/home", "disableHardwareBackPress": true}}'
  AllHomeIcons:
    "qr_code-69c444b6-cd1e-4b2d-8be3-7d9b2e2a25bb":
      IconWithVersionConstraints:
        - VersionConstraints:
            IsEnableOnAndroid: true
            MinAndroidVersion: 240
            IsEnableOnIos: true
            MinIosVersion: 334
          IconType: "QR_CODE"
          ImageUrl: "https://epifi-icons.pointz.in/home-v2/QRCodeWhite.png"
          OnclickImageUrl: ""
          Title: "Scan & Pay"
          FontColour: "#FFFFFF"
          FontStyle: "BUTTON_S"
        # The default icon parameters do not require a version constraint check
        - IconType: "QR_CODE"
          ImageUrl: "https://epifi-icons.pointz.in/home-v2/QRCode.png"
          OnclickImageUrl: ""
          Title: ""
  ToShowTieringInfoInSavingsDashboard: true
  HomeNudgeParams:
    MinAndroidAppVersionToSupportExploreNudge: 225
    MinIOSAppVersionToSupportExploreNudge: 328
    MinAndroidAppVersionToSupportGTMNudge: 251
    MinIOSAppVersionToSupportGTMNudge: 347
    MinAndroidAppVersionToSupportV2StandardNudge: 258
    MinIOSAppVersionToSupportV2StandardNudge: 361
  LayoutBySegFeatureRelease:
    IsEnableOnAndroid: true
    MinAndroidVersion: 250
    IsEnableOnIos: true
    MinIosVersion: 354
  HomeLayoutUIRevamp:
    IsEnableOnAndroid: true
    MinAndroidVersion: 268
    IsEnableOnIos: true
    MinIosVersion: 364
  HomeLayoutV2Params:
    HomeElementAttributes:
      # This document contains the logic for the different layouts listed below. - https://docs.google.com/document/d/1DPhkfXSf-8COI4eNfUuXLMPsy_FdfcV5Qbv0TwQ0CIg/edit
      ElementIdToSegmentExpressionScores:
        "primarysavings-1":
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "networth-1":
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "invest-1":
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "loans-1":
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "shortcuts-1":
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "promotionalbanner-1":
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "wealth-analyser":
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "refer-2":
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "tabbed-card-1":
          - Expression: "IsMember('10057ad2-69f9-4e75-aeff-59525efe876f')" # us stocks activated segment
            Score: 8
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 1
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 1
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "suggestedforyou-1":
          - Expression: "IsMember('10057ad2-69f9-4e75-aeff-59525efe876f')" # us stocks activated segment
            Score: 7
          - Expression: "IsMember('85a81891-739c-4c01-95aa-6da31ebd5e2d')" # international DC segment
            Score: 8
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 8
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 8
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "recentupcomingactivities-1":
          - Expression: "IsMember('10057ad2-69f9-4e75-aeff-59525efe876f')" # us stocks activated segment
            Score: 6
          - Expression: "IsMember('85a81891-739c-4c01-95aa-6da31ebd5e2d')" # international DC segment
            Score: 7
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 6
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 6
        "primary-feature-1":
          - Expression: "IsMember('10057ad2-69f9-4e75-aeff-59525efe876f')" # us stocks activated segment
            Score: 5
          - Expression: "IsMember('85a81891-739c-4c01-95aa-6da31ebd5e2d')" # international DC segment
            Score: 5
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 5
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 5
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "money-secrets-1":
          - Expression: "IsMember('10057ad2-69f9-4e75-aeff-59525efe876f')" # us stocks activated segment
            Score: 4
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 4
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 4
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "secondary-feature-1":
          - Expression: "IsMember('10057ad2-69f9-4e75-aeff-59525efe876f')" # us stocks activated segment
            Score: 3
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 2
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 2
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "rewards-2":
          - Expression: "IsMember('10057ad2-69f9-4e75-aeff-59525efe876f')" # us stocks activated segment
            Score: 2
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 3
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 3
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "rewards-3":
          - Expression: "IsMember('10057ad2-69f9-4e75-aeff-59525efe876f')" # us stocks activated segment
            Score: 1
          - Expression: "IsMember('85a81891-739c-4c01-95aa-6da31ebd5e2d')" # international DC segment
            Score: 6
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 7
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 7
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "pay-1":
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "invest-3":
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "networth-2":
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 1
          - Expression: "IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490')" # loan eligible
            Score: 1
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 1
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "rewards-4":
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "usstocks-1":
          - Expression: "IsMember('b4d18dc5-8594-4e9e-8a2a-638429b44b79')" # for users not invested in any invest product or just invested in usstocks
            Score: 1
        "card-1":
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 0
          - Expression: "IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490')" # loan eligible
            Score: 3
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 0
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "wealth-builder-1":
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 3
          - Expression: "IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490')" # loan eligible
            Score: 2
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 1
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "discover-1":
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 1
          - Expression: "IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490')" # loan eligible
            Score: 0
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 0
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "card-2":
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 4
          - Expression: "IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490')" # loan eligible
            Score: 0
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 4
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
        "loans-2":
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f')" # cc active
            Score: 0
          - Expression: "IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490')" # loan eligible
            Score: 1
          - Expression: "IsMember('58fb617a-bf81-4163-90a9-06d50b1b917f') && (IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active and loan eligible
            Score: 3
          - Expression: "!(IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b') || IsMember('4ddcfd6f-a9e0-4838-9bea-5310c269e803') || IsMember('9365f6cf-6909-4f62-8925-10ab228125e6') || IsMember('d8566f18-ad2a-48f9-9e22-f8b2fb744490'))" # cc active but sa account frozen
            Score: -1
          - Expression: "IsMember('de063a83-dde1-4f6f-9182-402b28e0980f')" # cc active but sa account frozen
            Score: -1
      ElementIdToFeatureLifecycleExpressionScores:
        "promotionalbanner-1":
          - Expression: "GetDaysSinceOnboardingForSA() <= 7" # for F0-7 users, no promo widgets
            Score: -1
        "secondary-feature-1":
          - Expression: "GetDaysSinceOnboardingForSA() <= 28" # for F0-28 users, no promo widgets
            Score: -1
        "tabbed-card-1":
          - Expression: "GetDaysSinceOnboardingForSA() <= 7" # for F0-7 users, no promo widgets
            Score: -1
        "suggestedforyou-1":
          - Expression: "GetDaysSinceOnboardingForSA() <= 7" # for F0-7 users
            Score: 6
          - Expression: "GetDaysSinceOnboardingForSA() >= 8 && GetDaysSinceOnboardingForSA() <= 28" # for F8-28 users
            Score: 6
        "recentupcomingactivities-1":
          - Expression: "GetDaysSinceOnboardingForSA() <= 7" # for F0-7 users
            Score: 5
          - Expression: "GetDaysSinceOnboardingForSA() >= 8 && GetDaysSinceOnboardingForSA() <= 28" # for F8-28 users
            Score: 5
        "rewards-2":
          - Expression: "GetDaysSinceOnboardingForSA() <= 7" # for F0-7 users
            Score: 4
          - Expression: "GetDaysSinceOnboardingForSA() >= 8 && GetDaysSinceOnboardingForSA() <= 28" # for F8-28 users
            Score: 3
        "rewards-3":
          - Expression: "GetDaysSinceOnboardingForSA() <= 7" # for F0-7 users
            Score: 3
          - Expression: "GetDaysSinceOnboardingForSA() >= 8 && GetDaysSinceOnboardingForSA() <= 28" # for F8-28 users
            Score: 1
        "money-secrets-1":
          - Expression: "GetDaysSinceOnboardingForSA() <= 7" # for F0-7 users
            Score: 2
          - Expression: "GetDaysSinceOnboardingForSA() >= 8 && GetDaysSinceOnboardingForSA() <= 28" # for F8-28 users
            Score: 2
          - Expression: "GetFeatureActivationStatus('FEATURE_WEALTH_ANALYSER') == 'FEATURE_STATUS_ACTIVE'" # If Wealth Analyser feature is active, don't show money secrets widget.
            Score: -1
        "primary-feature-1":
          - Expression: "GetDaysSinceOnboardingForSA() <= 7" # for F0-7 users
            Score: 1
          - Expression: "GetDaysSinceOnboardingForSA() >= 8 && GetDaysSinceOnboardingForSA() <= 28" # for F8-28 users
            Score: 4
        "wealth-analyser":
          - Expression: "GetFeatureActivationStatus('FEATURE_WEALTH_ANALYSER') != 'FEATURE_STATUS_ACTIVE'" # Show wealth analyser widget only if the Wealth Analyser feature is Active. Note: Here if we get status other than Active, we return -1. Which states that the widget won't be shown.
            Score: -1
    SlotIdToScreenElementIdsMap:
      TopNavBarSlotSection:
        LeftSlots:
          slot_1: [ "profile-1" ]
        RightSlots:
          slot_1: [ "card-1", "wealth-builder-1", "discover-1" ]
          slot_2: [ "rewards-4" ]
          slot_3: [ "notification-1" ]
      DashboardSlotSection:
        slot_1: [ "intro-1" ]
        slot_2: [ "primarysavings-1" ]
        slot_3: [ "networth-1" ]
        slot_4: [ "invest-1" ]
        slot_5: [ "creditcards-1" ]
        slot_6: [ "loans-1" ]
      VerticalSlotSection:
        slot_1: [ "shortcuts-1" ]
        slot_2: [ "dc-international-widget" ]
        slot_3: [ "promotionalbanner-1" ]
        slot_4: [ "" ]
        slot_5: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_6: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_7: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_8: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_9: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_10: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_11: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_12: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_13: [ "" ]
        slot_14: [ "" ]
        slot_15: [ "help-1" ]
        slot_16: [ "refer-2" ]
        slot_17: [ "trust-marker" ]
      BottomNavBarSlotSection:
        slot_1: [ "home-1" ]
        slot_2: [ "pay-1" ]
        slot_3: [ "invest-3", "usstocks-1" ]
        slot_4: [ "wealth-builder-1","card-2" ]
        slot_5: [ "wealth-builder-1", "discover-1", "loans-2" ]
      StickyIconSlotSection:
        slot_1: [ "qr_code-1" ]
        slot_2: [ "qr_code-2" ]
        slot_3: [ "" ]
  HomeLayoutV2D0To7Params:
    HomeElementAttributes:
      ElementIdToSegmentExpressionScores:
        "usstocks-1":
          - Expression: "IsMember('b4d18dc5-8594-4e9e-8a2a-638429b44b79')" # for users not invested in any invest product or just invested in usstocks
            Score: 1
  HomeLayoutV2D8To14Params:
    HomeElementAttributes:
      ElementIdToSegmentExpressionScores:
        "usstocks-1":
          - Expression: "IsMember('b4d18dc5-8594-4e9e-8a2a-638429b44b79')" # for users not invested in any invest product or just invested in usstocks
            Score: 1
  HomeLayoutV2D15To28Params:
    HomeElementAttributes:
      ElementIdToSegmentExpressionScores:
        "usstocks-1":
          - Expression: "IsMember('b4d18dc5-8594-4e9e-8a2a-638429b44b79')" # for users not invested in any invest product or just invested in usstocks
            Score: 1

RpcRateLimitConfig:
  Namespace: "frontend-rpc"
  # rate limit keys in map are as per GenerateKey method in pkg/epifigrpc/interceptors/ratelimit/helper.go
  ResourceMap:
    #    frontend_clientlogger_clientLogger_log:
    #      Rate: 30
    #      Period: 1s
    frontend_user_user_synccontactdetails:
      Rate: 50
      Period: 1s
    frontend_user_user_recordhashedcontacts:
      Rate: 20
      Period: 1s

CreditCard:
  AppVersionSupport:
    MinIosVersionForCreditCard: 329
    MinAndroidVersionForCreditCard: 228
    MinIosVersionForCreditCardIntroV2: 346
    MinAndroidVersionForCreditCardIntroV2: 250
  OnboardingRetryAttemptCutoff: 20
  FiLiteOnboardingHomeScreenRedirectAttemptCutoff: 20
  FiLiteOnboardingBottomTextDisplayAttemptCutoff: 5
  EnableCCAllTxnPagination: false
  PaymentSuccessBannerTimeInMinutes: 5
  ShowCreditCardTabByDefaultFromCardTab: true
  WorkflowConstraints:
    - "CARD_REQUEST_WORKFLOW_CARD_ACTIVATION":
        AppVersionConstraintConfig:
          MinIOSVersion: 356
    # to be used for secured card onboarding.
    - "CARD_REQUEST_WORKFLOW_CARD_ONBOARDING":
        AppVersionConstraintConfig:
          MinAndroidVersion: 293
          MinIosVersion: 423
  AllEligibleCcScreenConfig:
    CardComponentTemplateVersion: 1
  IsCcChoicesComponentListViewEnabled: true
  CcNetworkSelectionScreenVersionCheck:
    IsEnableOnAndroid: false
    MinAndroidVersion: 1000
    IsEnableOnIos: false
    MinIosVersion: 1000
  EnableCardTabsScreen:
    IsEnableOnAndroid: false
    MinAndroidVersion: 2000
    IsEnableOnIos: true
    MinIosVersion: 462
  FiLiteBottomCtaConfigs:
    EnableFiLiteBottomCtaVersionCheckFlag:
      IsEnableOnAndroid: true
      MinAndroidVersion: 317
      IsEnableOnIos: true
      MinIosVersion: 452
    IsCcChoicesComponentListViewEnabled: true
  EnableVLForIntroScreenByCardProgramType:
    CardProgramTypes:
      - "UNSECURED"
    FeatureConfig:
      MinIOSVersion: 20000
      MinAndroidVersion: 20000
      FallbackToEnableFeature: false
      DisableFeature: true
  EnableNewCvpForUnsecuredCreditCard: true
  EnableDashboardSegmentationCarousels: false
  SegmentIdToCarouselObjectMap:
  BillEraserOfferIDForOffersCatalogue: "4efb2f67-d403-44cb-9fcf-aa6be1dcf597"
  ShowAmplifiZeroForexBanner: true

AutoInvestStoryConfig:
  StoryId: ""

NonTpapPspHandles: [ "fbl" ]

EmailDomainCheck:
  EnableDomainCheck: true
  InvalidDomains: [ "privaterelay" ]
  InvalidDomainPopupConfig:
    MinIOSVersion: 400
    MinAndroidVersion: 10000
    FallbackToEnableFeature: false
    DisableFeature: false

Dispute:
  IsGetNextQuestionsForAppRequestChangeEnabled: true
  RaiseDisputeErrorViewConfig:
    IsBottomSheetErrorViewPropagationEnabled: true

Alfred:
  ServiceRequestHistoryPageSize: 10
  EnableCancelledCheque:
    MinAndroidVersion: 252
    MinIOSVersion: 343
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableVRH:
    MinAndroidVersion: 253
    MinIOSVersion: 354
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableRequestChoiceBottomSheet:
    MinAndroidVersion: 294
    MinIOSVersion: 429
    FallbackToEnableFeature: true
    DisableFeature: false
  EnableCopyTrackingUrl:
    MinAndroidVersion: 292
    MinIOSVersion: 2000
    FallbackToEnableFeature: true
    DisableFeature: false
  EnableAddressUpdate:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: false
    DisableFeature: true
  EnableAddressUpdateReqHistories:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: false
    DisableFeature: true
  EnableSavingsAccSignUpdate:
    MinAndroidVersion: 100000
    MinIOSVersion: 100000
    FallbackToEnableFeature: true
    DisableFeature: true

FeedbackEngineConfig:
  ResponseHeaderPopulationConfig:
    IsPopulationInGetAnalyserEnabled: false
    IsPopulationInGetP2POrderStatusEnabled: true
    IsPopulationInCollectDataFromCustomerEnabled: true
    IsPopulationInGetNextOnboardingStepEnabled: true
    IsPopulationInGetSupportTicketsForAppEnabled: false
    IsPopulationInGetSupportTicketByIdForAppEnabled: true
    IsPopulationInGetChatInitInformationForActorEnabled: false
    IsPopulationInGetProfileSettingPageSectionEnabled: false
    IsPopulationInUSSGetLandingPageForNewUserEnabled: true
    IsPopulationInUSSGetLandingPageForNewWalletUserEnabled: true
    IsPopulationInUSSGetLandingPageForExistingWalletUserEnabled: true
    IsPopulationInGetWalletAddFundsDetailsForNewWalletUserEnabled: true
    IsPopulationInGetWalletAddFundsDetailsForExistingWalletUserEnabled: true
    IsPopulationInGetWalletWithdrawFundsDetailsForUserEnabled: true
    IsPopulationInCreateWalletWithdrawFundsOrderForUserEnabled: true
    IsPopulationInEKYCForOnboardingForUserEnabled: true

NetworthConfig:
  ConfigPath: "./mappingJson/networthConfig.json"
  DebugActorIdsForDailyReport:
    - "AC2HCC4Txhz6250411": true
    - "AC220103Hapts4DoRsqNUTkIYeereA==": true # Sainath's actorid to debug indian stocks
  IsNetworthDashboardFeedbackEnabled: true
  EpfPassbookParams:
    IsEpfRedirectionEnabled: true
  NetworthD2HDashboardFeatureConfig:
    MinAndroidVersion: 369
    MinIOSVersion: 520
    DisableFeature: false
  FeatureFlags:
    AIFSearch:
      MinAndroidAppVersion: 10000
      MinIOSAppVersion: 10000
    DisableWBOnbMFStory: true

TargetGroup:
  IsEnable: true
  VersionCheck:
    MinAndroidVersion: 289
    MinIOSVersion: 393
    FallbackToEnableFeature: false
    DisableFeature: false

RecordContactDetailsVersion: 1

LiteHomeRevampParams:
  DashboardVersionV2ReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 294
    IsEnableOnIos: false
    MinIosVersion: 410
  NewSearchUIReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 294
    IsEnableOnIos: true
    MinIosVersion: 410

BKYC:
  AllowedAgentEmailDomains:
    - "federalbank.co.in"
    - "panobiz.in"
    - "staffzapp.com"
  EnableEPANForBKYC:
    MinAndroidVersion: 311
    MinIOSVersion: 446
    FallbackToEnableFeature: true
    DisableFeature: false

TpapLinkingEntryPointConfig:
  ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_CREDIT_CARD: { IsEnabledForSaUser: true, IsEnabledForNonSaUser: true }

ShowAutoRenewCta: true

PaymentOptionsConfig:
  IntentOptionsConfig:
    UpiAppsAndroidPackages:
      - PackageId: "com.phonepe.app"
        AppLogo: "https://epifi-icons.pointz.in/tiering/add_funds/phone_pe.png"
        AppName: "Phone Pe"
      - PackageId: "com.google.android.apps.nbu.paisa.user"
        AppLogo: "https://epifi-icons.pointz.in/tiering/add_funds/google_pay.png"
        AppName: "Google Pay"
      - PackageId: "net.one97.paytm"
        AppLogo: "https://epifi-icons.pointz.in/tiering/add_funds/pay_tm.png"
        AppName: "Paytm"
    AllowedUserGroups:
      - 21 # TPAP_INTERNAL

DynamicElementsConfig:
  BannerElementContentV2UiVariantV2FeatureConfig:
    MinAndroidVersion: 315
    MinIOSVersion: 452
    DisableFeature: false

TpapUnifiedFlowReleaseVersions:
  MinAndroidVersion: 321
  MinIOSVersion: 461

FiStoreConfig:
  SegmentIds: [ "4243702f-9847-4ed8-8e07-78a94826c06b", "dc77ca22-10f5-4b33-ba33-10fcd32274fe", "90d5a1cd-3bb5-4200-8632-3250fa1f05d6", "bf5ef1ca-ddfd-45e2-ab77-df8b03255e67", "cb4d3157-21cf-43ad-80c6-d840ce45d009", "57338924-f61e-459c-bce7-de73fbb8f322" ]
  IsWebPageWithCardDetailsScreenEnabled: true # this config is used for gift card store
  MinAndroidVersionForWebPageWithCardDetailsScreen: 329
  MinIosVersionForWebPageWithCardDetailsScreen: 473

OnAppEnachRedirectionDebug:
  ReturnRedirectionUrl: false
  AllowedActors:
    ALL: false # for allowing debugging for all actor-ids at once
    AC210622KqPeekvVTOuMmZaVRMZBew==: true # Rohan Chougule
    AC210810iIwOkpVCRWK83QYRMhmv9Q==: true # Jithin
    AC210205jkX9PO3lRhCSP4i4W02ElA==: true # Utkarsh
    ACfu3xtvLfQEOnSz7mkn+J0g240304==: true # Deepika
    ACkdJ8fHC9TKuZFP6mMG9O/Q240129==: true # Vamshi

EnableGetPaymentOptionsV1: false

FiStoreCollectedOffersConfig:
  IsFiStoreCollectedOffersEnabled: true
  MinAndroidVersionForFiStoreCollectedOffers: 338
  MinIosVersionForFiStoreCollectedOffers: 494
  EComCategoryCard:
    WebpageUrl: "https://app.dpanda.in/?publisher_eid=bGZYYnRUdys3UXZBVzBmdkVKMyt4Zz09&user_identifier=%s"
  GiftCardsCard:
    WebpageUrl: "fimoney.poshvine.com/order-history?pageType=GiftCardBooking"
  FlightsCard:
    WebpageUrl: "fimoney.poshvine.com/order-history?pageType=FlightBooking"
  HotelsCard:
    WebpageUrl: "fimoney.poshvine.com/order-history?pageType=MembershipBooking"
  MilesExchangeCard:
    WebpageUrl: "fimoney.poshvine.com/order-history?pageType=PointsXChange"

MsClarityConfig:
  IsEnabled: true
  AllowedScreenNames: [ ]
  AllowedActivityNames:
    - "com.epifi.paisa.home.HomeActivity"
    - "com.epifi.paisa.ui.MainActivity"
    - "com.epifi.paisa.accounttiering.TieringActivity" # Following Activity names are temporarily enabled until Android single activity flag is turned on for all of these
    - "com.epifi.paisa.analyser.AnalyserActivity"
    - "com.epifi.paisa.analyser.mutualfundimport.ImportMutualFundActivity"
    - "com.epifi.paisa.commonflows.chat.CustomerSupportCallUsActivity"
    - "com.epifi.paisa.commonflows.chat.CustomerSupportChatActivity"
    - "com.epifi.paisa.commonflows.feedback.feedbackvote.FeedbackVoteActivity"
    - "com.epifi.paisa.commonflows.feedbackengine.FeedbackEngineActivity"
    - "com.epifi.paisa.commonflows.generate_deposit_statement.GenerateDepositStatementActivity"
    - "com.epifi.paisa.commonflows.goals.GoalsActivity"
    - "com.epifi.paisa.commonflows.information_popup.InformationPopupActivity"
    - "com.epifi.paisa.commonflows.investment_retention.InvestmentRetentionActivity"
    - "com.epifi.paisa.help.HelpActivity"
    - "com.epifi.paisa.investments.InvestmentsActivity"
    - "com.epifi.paisa.nominee.NomineeActivity"
    - "com.epifi.paisa.onboarding.login.LoginActivity"
    - "com.epifi.paisa.onboarding.login.safetynet.SafetynetConsentActivity"
    - "com.epifi.paisa.pay.PayActivity"
    - "com.epifi.paisa.profile.ProfileActivity"
    - "com.epifi.paisa.ui.authorize.notification.CxUserAuthenticationActivity"
    - "com.epifi.paisa.ui.deeplink.DeeplinkActivity"
    - "com.epifi.paisa.ui.fullscreen.FullScreenNotificationActivity"
    - "com.epifi.paisa.ui.splash.SplashActivity"
    - "com.epifi.paisa.usstocks.UsStocksActivity"
    - "com.epifi.paisa.videokyc.OverlayActivity"
    - "com.epifi.paisa.videokyc.VideoKycActivity"
    - "com.epifi.paisa.wealth.WealthActivity"
    - "com.epifi.paisa.wealth.WealthOnboardingActivity"

UIEntryPointConfigForResolveQr:
  MinAndroidVersion: 344
  MinIOSVersion: 501
  DisableFeature: false

UpiNewDeviceRegFeatureConfig:
  MinAndroidVersion: 2000
  MinIOSVersion: 0
  DisableFeature: true

Cx:
  Ticket:
    MaxDurationToShowTicketUpdateOnHome: "336h" # 14 days
  CxLandingPageV2Config:
    MaxNumberOfOpenIndividualTickets: 2
    MaxNumberOfTicketsToFetch: 50
    HelpSectionConfig:
      FAQDetails:
        - FAQId: *********** # Why do I need to complete Re-KYC/Periodic KYC?
          FAQType: "ARTICLE"
          Priority: 3
        - FAQId: *********** # How do I raise a dispute on a transaction?
          FAQType: "ARTICLE"
          Priority: 2
        - FAQId: *********** # Can I get a passbook?
          FAQType: "ARTICLE"
          Priority: 1
        - FAQId: *********** # Account Info
          FAQType: "CATEGORY"
          Priority: 3
        - FAQId: *********** # Frozen Account
          FAQType: "CATEGORY"
          Priority: 2
        - FAQId: *********** # Video KYC
          FAQType: "CATEGORY"
          Priority: 1

HomeExploreConfig:
  EnableAskFiSection: true
  EnableFeedbackSection: false
  FullVisualCardSectionConfig:
    FeatureFlag:
      DisableFeature: false
      MinAndroidVersion: 430
      MinIOSVersion: 593

EnableReferralProgramForSavingsAccount: true

HelpRecentActivity:
  IsSearchBarShown: false
  IsFeatureEnabled: false

SavingsAccountClosure:
  FullGroupCriteiaItemMinAppVersions:
    MinVersionAndroid: 391
    MinVersionIos: 545


InAppContactUsFlowConfig:
  IsNonFcrFlowEnabled: true
  TicketCreationConfig:
    MaxAndroidVersionForTicketCreationOnReporting: 391
    MaxIOSVersionForTicketCreationOnReporting: 544
  MaxAndroidVersionForAppUpgradeRedirection: 395
  MaxIosVersionForAppUpgradeRedirection: 540
  ForcedChatbotFlowExperimentConfig:
    Enable: false
    UserLayerPercentage: 50
    AllowedCohorts: [ "ACCOUNT_FREEZE" ]
    AllowedUserGroups:
      - 14 # CX_INTERNAL

HomeFeatureQuestFlags:
  EnableUnderlayUpiComponent: true

MoneySecrets:
  MfStocksBreakdown:
    MaxStocks: 50

MoneySecretsConfig:
  ExplicitLockingFeatureConfig:
    FeatureConfig:
      MinAndroidVersion: 408
      MinIOSVersion: 568
      DisableFeature: false
    EnableForRolloutPercentageStart: 0
    EnableForRolloutPercentageEnd: 100
    DisableForSegmentExpression: "IsMember('71e8c74b-3eb2-4895-b5fb-e6c7ab38dbf0') || IsMember('851c5b55-8610-46de-8b98-71ed406949ec') || IsMember('127b7c75-93bc-4d13-a7c4-************')"
    UnlockNudgeId: "b7122de3-78db-4618-a305-934d02476971"


VKYCCall:
  MockInitiateVKYCCallParam:
    IsMockingEnable: false

HomeBalanceSummaryWidgetUiConfig:
  StaleBalanceWarningTickerIconTextComponent:
    IsEnabled: true

# This is used to show the new design for the pay landing screen
# [Critical] - Do not change these values, as pay landing screen might break for older clients if new components are sent from BE.
PayLandingScreenParams:
  IOSMinVersionForDesignFixit: 606
  AndroidMinVersionForDesignFixit: 442

TieringNotchConfig:
  EnableNotchConfigEnrichmentFeatureFlag:
    IsFeatureRestricted: True
    AllowedUserGroups:
      - 1 # INTERNAL
  UpgradeJourneyConf:
    SegmentExpiry: 5
  EarningPotentialConf:
    LowPotentialLimit: 3000
  AbuserConfig:
    TIER_FI_PLUS: 22000
    TIER_FI_INFINITE: 42000
    TIER_FI_AA_SALARY_BAND_3: 84000
  SegmentConfMap:
    UPGRADE_JOURNEY:
      #      SegmentDefinition: "Users just upgraded to {{Plus/Infinite/Prime/Salary Basic/Salary}}"
      SegmentTemplate: "You're on {{.CurrentTier}}"
      Priority: 6
      UseRealtime: true
    EARNING_POTENTIAL_LOW:
      #      SegmentDefinition: "Users on {{Plus/Infinite/Prime/Salary}} with projected rewards < {{3000}}"
      SegmentTemplate: "{{.CashBackPercent}} back on UPI!"
      Priority: 13
      UseRealtime: true
    EARNING_POTENTIAL_HIGH:
      #      SegmentDefinition: "Users on {{Plus/Infinite/Prime/Salary}} with projected rewards > {{3000}}"
      SegmentTemplate: "Fi-Coins: {{.ProjectedFiCoins}}"
      Priority: 14
      UseRealtime: true
    MONEY_PLANT_NOT_REDEEMED:
      #      SegmentDefinition: "Money plant not redeemed (D4 onwards)"
      SegmentTemplate: "Claim {{.Month}} rewards 🎁"
      Priority: 7
      UseRealtime: true
    DOWNGRADE_AMB_LOW:
      #      SegmentDefinition: "Users with low AMB"
      SegmentTemplate: "AMB below {{.TierMinBalance}}"
      Priority: 5
      UseRealtime: true
    DOWNGRADE_GRACE_10_6:
      #      SegmentDefinition: "Downgrade grace period (10–6 days)"
      SegmentTemplate: "{{.CashBackPercent}} back expires soon"
      Priority: 3
      UseRealtime: true
    DOWNGRADE_GRACE_5_2:
      #      SegmentDefinition: "Downgrade grace period (5–3 days)"
      SegmentTemplate: "{{.CashBackPercent}} back expires soon"
      Priority: 2
      UseRealtime: true
    DOWNGRADE_1_DAY:
      #      SegmentDefinition: "Downgrade (1 day)"
      SegmentTemplate: "{{.CashBackPercent}} back expires soon"
      Priority: 1
      UseRealtime: true
    UPGRADE_OPPORTUNITY_STANDARD_TO_PLUS:
      #      SegmentDefinition: "Standard Users Pitching for plus"
      SegmentId: "73422cc9-fc2e-4604-b011-3f7cf9956043"
      SegmentTemplate: "Unlock 2% back on spends!"
      Priority: 8
      UseRealtime: false
    UPGRADE_OPPORTUNITY_STANDARD_TO_INFINITE:
      #      SegmentDefinition: "Standard Users Pitching for Infinite"
      SegmentId: "65ff0b4e-c0d7-4608-b4c1-2cebffdb30f6"
      SegmentTemplate: "Unlock 3% back on spends!"
      Priority: 9
      UseRealtime: false
    UPGRADE_OPPORTUNITY_PLUS_TO_INFINITE:
      #      SegmentDefinition: "Plus users pitching for inifinite"
      SegmentId: "9ca74582-a648-47aa-a24a-51f71bcea6c4"
      SegmentTemplate: "Unlock 3% back on spends!"
      Priority: 10
      UseRealtime: false
    ABUSER_FLAGGED:
      #      SegmentDefinition: "Abuser Flagged User"
      SegmentTemplate: "Rewards on hold"
      Priority: 4
      UseRealtime: true
    POST_DOWNGRADE_1_5:
      #      SegmentDefinition: "1–5 days post downgrade"
      SegmentTemplate: "Get back on {{.PreviousTier}}"
      Priority: 11
      UseRealtime: true
    POST_DOWNGRADE_6_15:
      #      SegmentDefinition: "6–15 days post downgrade"
      SegmentTemplate: "Miss {{.PreviousTierCashBackPercent}} back?"
      Priority: 12
      UseRealtime: true
