package secrets

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	headerPb "github.com/epifi/gamma/api/frontend/header"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	secretFeEnums "github.com/epifi/gamma/api/insights/secrets/frontend"
	secretErrors "github.com/epifi/gamma/frontend/insights/secrets/errors"
	portfolioTrackerBuilder "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder"
)

var (
	portfolioTrackerComponentType = []secretFeEnums.PortfolioTrackerComponentType{
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_SUMMARY,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_ASSET_DISTRIBUTION,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_TOP_MOVERS,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_INSIGHTS,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_YOU_VS_MARKET,
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_NEXT_STEPS_ACTION,
	}
)

func (s *Service) GetPortfolioTrackerLandingPage(ctx context.Context, req *secretsFePb.GetPortfolioTrackerLandingPageRequest) (*secretsFePb.GetPortfolioTrackerLandingPageResponse, error) {
	portfolioTrackerRes, err := s.portfolioTrackerBuilder.BuildPortfolioTracker(ctx, &portfolioTrackerBuilder.BuildPortfolioTrackerRequest{
		ActorId:        req.GetReq().GetAuth().GetActorId(),
		ComponentTypes: portfolioTrackerComponentType,
	})
	if err != nil && !errors.Is(err, secretErrors.NoDataToBuildPortfolioTracker) {
		logger.Error(ctx, fmt.Sprintf("failed to get secret analyser %v", ""), zap.Error(err))
		return &secretsFePb.GetPortfolioTrackerLandingPageResponse{RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusInternalWithDebugMsg(err.Error())}}, nil
	}
	if errors.Is(err, secretErrors.NoDataToBuildPortfolioTracker) {
		return &secretsFePb.GetPortfolioTrackerLandingPageResponse{
			RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusRecordNotFoundWithDebugMsg(err.Error())},
		}, nil
	}
	return &secretsFePb.GetPortfolioTrackerLandingPageResponse{
		RespHeader:           &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
		FixedComponents:      portfolioTrackerRes.GetFixedComponents(),
		ScrollableComponents: portfolioTrackerRes.GetScrollableComponents(),
		FooterComponents:     portfolioTrackerRes.GetFooterComponents(),
	}, nil
}
