package portfoliotrackerbuilder

import (
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/integer"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	investmentPkg "github.com/epifi/gamma/pkg/investment"
)

type TitleComponentConfig struct {
	Title            string
	TitleColor       string
	Subtitle         string
	SubtitleColor    string
	DisplayDate      string
	Disclaimer       *uiPb.IconTextComponent
	VisualElementUrl string
}

type PortfolioTrackerStrategy interface {
	GetTitleComponentConfig(p *PortfolioTrackerBuilder) *TitleComponentConfig
}

type DailyTracker struct{}

func NewDailyTracker() *DailyTracker {
	return &DailyTracker{}
}

func (d *DailyTracker) GetTitleComponentConfig(p *PortfolioTrackerBuilder) *TitleComponentConfig {
	return &TitleComponentConfig{
		Title:            "Daily Mutual Fund Tracker",
		TitleColor:       colors.ColorForest,
		Subtitle:         "Know what changed in a day, everyday",
		SubtitleColor:    colors.ColorOnDarkHighEmphasis,
		DisplayDate:      getDisplayDateForPortfolioTracker(),
		Disclaimer:       getDisclaimer(),
		VisualElementUrl: "https://epifi-icons.pointz.in/secets/daily-report-background",
	}
}

// getDisplayDateForPortfolioTracker returns the current date in the format "1st Jan", "2nd Feb" etc.
// The function always returns yesterday's date by default.
// And if it's before 11 AM, it returns the day before yesterday's date.
// This is because before 11 AM, yesterday's data is not ready to be displayed.
func getDisplayDateForPortfolioTracker() string {
	// get the required date to display on the header
	reportTime := investmentPkg.GetLastTradingDayTime()
	// Get the day and format it with suffix
	day := reportTime.Day()
	dayWithSuffix := integer.GetOrdinalSuffix(day)
	// Format the month as short name (Jan, Feb, etc.)
	month := reportTime.Format("Jan")
	// Return formatted date string
	return fmt.Sprintf("%s %s", dayWithSuffix, month)
}

func getDisclaimer() *uiPb.IconTextComponent {
	return uiPb.NewITC().
		WithRightVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/insights/secrets/epifi-wealth-logo2", 11, 56).
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Powered by", "#929599", commontypes.FontStyle_SUBTITLE_XS))
}
