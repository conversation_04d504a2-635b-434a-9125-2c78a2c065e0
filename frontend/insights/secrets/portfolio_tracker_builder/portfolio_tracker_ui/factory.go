package portfoliotrackerui

import (
	"context"
	"fmt"

	dynConf "github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/feature/release"

	networthBePb "github.com/epifi/gamma/api/insights/networth"

	"github.com/google/wire"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	secretFeEnums "github.com/epifi/gamma/api/insights/secrets/frontend"
	assetWiseDistributionBuilder "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/portfolio_tracker_ui/asset_wise_distribution_builder"
)

var WirePortfolioTrackerFactorySet = wire.NewSet(NewPortfolioTrackerFactory, wire.Bind(new(IPortfolioTrackerFactory), new(*PortfolioTrackerFactory)))

type IPortfolioTrackerFactory interface {
	// BuildPortfolioTrackerComponent used to build details ui view for a component in request
	BuildPortfolioTrackerComponent(ctx context.Context, req *BuildPortfolioTrackerComponentRequest) (*BuildPortfolioTrackerComponentResponse, error)
}

type PortfolioTrackerFactory struct {
	portfolioSummary           *PortfolioSummary
	topMovers                  *TopMovers
	assetWiseDistribution      *AssetWiseDistribution
	comparisonAndNewsComponent *ComparisonAndNewsComponent
	netWorthClient             networthBePb.NetWorthClient
	nextStepsActionComponent   *NextStepsActionComponent
	MarketInsightComponent     *MarketInsightComponent
}

func NewPortfolioTrackerFactory(assetWiseDistributionFactory assetWiseDistributionBuilder.IAssetWiseDistributionFactory, config *dynConf.Config, releaseEvaluator release.IEvaluator) *PortfolioTrackerFactory {
	return &PortfolioTrackerFactory{
		portfolioSummary:           NewPortfolioSummary(config, releaseEvaluator),
		topMovers:                  NewTopMovers(),
		assetWiseDistribution:      NewAssetWiseDistribution(assetWiseDistributionFactory),
		comparisonAndNewsComponent: NewComparisonAndNewsComponent(),
		nextStepsActionComponent:   NewNextStepsActionComponent(config),
		MarketInsightComponent:     NewMarketInsightComponent(config),
	}
}

func (p *PortfolioTrackerFactory) BuildPortfolioTrackerComponent(ctx context.Context, req *BuildPortfolioTrackerComponentRequest) (*BuildPortfolioTrackerComponentResponse, error) {
	switch req.ComponentType {
	case secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_SUMMARY:
		return p.portfolioSummary.BuildPortfolioTrackerComponent(ctx, req)
	case secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_TOP_MOVERS:
		return p.topMovers.BuildPortfolioTrackerComponent(ctx, req)
	case secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_ASSET_DISTRIBUTION:
		return p.assetWiseDistribution.BuildPortfolioTrackerComponent(ctx, req)
	case secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_YOU_VS_MARKET:
		return p.comparisonAndNewsComponent.BuildPortfolioTrackerComponent(ctx, req)
	case secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_NEXT_STEPS_ACTION:
		return p.nextStepsActionComponent.BuildPortfolioTrackerComponent(ctx, req)
	case secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_INSIGHTS:
		return p.MarketInsightComponent.BuildPortfolioTrackerComponent(ctx, req)
	}
	return nil, fmt.Errorf("unhandled portfolio tracker component type: %s", req.ComponentType)
}

type BuildPortfolioTrackerComponentRequest struct {
	ActorId             string
	ComponentType       secretFeEnums.PortfolioTrackerComponentType
	AnalysisVariableMap map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable
}

type BuildPortfolioTrackerComponentResponse struct {
	componentType             secretFeEnums.PortfolioTrackerComponentType
	NavigationToggle          *secretsFePb.NavigationToggle
	PortfolioTrackerComponent *secretsFePb.PortfolioTrackerComponent
}
