package assetwisedistributionbuilder

import (
	"context"

	"github.com/epifi/be-common/pkg/epifierrors"
)

type NpsDistribution struct{}

func NewNpsDistribution() *NpsDistribution {
	return &NpsDistribution{}
}

func (t *NpsDistribution) BuildAssetWiseDistribution(ctx context.Context, req *BuildAssetWiseDistributionRequest) (*BuildAssetWiseDistributionResponse, error) {
	return nil, epifierrors.ErrUnimplemented
}
