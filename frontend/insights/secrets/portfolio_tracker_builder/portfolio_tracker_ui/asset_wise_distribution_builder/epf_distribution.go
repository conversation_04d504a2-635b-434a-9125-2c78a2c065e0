package assetwisedistributionbuilder

import (
	"context"

	"github.com/epifi/be-common/pkg/epifierrors"
)

type EpfDistribution struct{}

func NewEpfDistribution() *EpfDistribution {
	return &EpfDistribution{}
}

func (t *EpfDistribution) BuildAssetWiseDistribution(ctx context.Context, req *BuildAssetWiseDistributionRequest) (*BuildAssetWiseDistributionResponse, error) {
	return nil, epifierrors.ErrUnimplemented
}
