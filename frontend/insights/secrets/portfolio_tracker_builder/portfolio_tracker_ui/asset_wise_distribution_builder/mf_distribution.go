package assetwisedistributionbuilder

import (
	"context"
	"fmt"
	"strings"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/mf"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	secretsScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets"
	secretsRequestParams "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets/portfoliotracker"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config/genconf"
	mfUtils "github.com/epifi/gamma/insights/networth/utils"
	"github.com/epifi/gamma/pkg/deeplinkv3"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	feInvestmentPb "github.com/epifi/gamma/api/frontend/investment/ui"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	beEnums "github.com/epifi/gamma/api/insights/secrets/frontend"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	investmentPkg "github.com/epifi/gamma/pkg/investment"
)

type MfDistribution struct {
	gconf *genconf.Config
}

func NewMfDistribution(
	gconf *genconf.Config,
) *MfDistribution {
	return &MfDistribution{
		gconf: gconf,
	}
}

func (m *MfDistribution) BuildAssetWiseDistribution(ctx context.Context, req *BuildAssetWiseDistributionRequest) (*BuildAssetWiseDistributionResponse, error) {
	variable, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS]
	if !ok {
		logger.Error(ctx, "error when fetch analyser variable mf scheme analytics")
		return nil, fmt.Errorf("failed to find analysis variable %v", analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS)
	}

	sortedMfDailyChangeList := mfUtils.GetSchemeAnalyticsBySortedDayChange(ctx, variable.GetMfSecretsSchemeAnalytics())

	portfolioTrackerAssetDetailsCardBuilder := secretsFePb.NewPortfolioTrackerAssetDetailsCardBuilder().
		SetAssetType(networthBePb.AssetType_ASSET_TYPE_MUTUAL_FUND.String()).
		SetAssetCountText(fmt.Sprintf("%d Mutual Funds", len(sortedMfDailyChangeList))).
		SetBgColour(widget.GetBlockBackgroundColour(colors.ColorGreyV2))

	if req.LineItemCount != 0 && len(sortedMfDailyChangeList) > req.LineItemCount {
		portfolioTrackerAssetDetailsCardBuilder = portfolioTrackerAssetDetailsCardBuilder.SetViewMoreCta("View More", &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PORTFOLIO_TRACKER_ASSET_DETAILS_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&secretsScreenOptions.AssetDistributionPageScreenOptions{
				RequestParams: &secretsRequestParams.AssetDetailsPageRequestParams{
					AssetType: req.AssetType.String(),
				},
			}),
		})
	}

	_, _, aggregatedDailyChangeValue := mfUtils.CalculateAggregatedMfValues(sortedMfDailyChangeList)

	if req.LineItemCount == 0 || req.LineItemCount > len(sortedMfDailyChangeList) {
		req.LineItemCount = len(sortedMfDailyChangeList)
	}
	for _, mfDailyChange := range sortedMfDailyChangeList[:req.LineItemCount] {
		dailyChangeValue := mfUtils.CalculateDailyChange(mfDailyChange)

		portfolioTrackerLineItem := setLineItemWithDisplayValue(mfDailyChange, dailyChangeValue)

		portfolioTrackerAssetDetailsCardBuilder.AddLineItem(portfolioTrackerLineItem)
	}

	portfolioTrackerAssetDetailsCardBuilder = setToggleValues(portfolioTrackerAssetDetailsCardBuilder, variable, aggregatedDailyChangeValue)

	return &BuildAssetWiseDistributionResponse{
		AssetType:                        req.AssetType,
		PortfolioTrackerAssetDetailsCard: portfolioTrackerAssetDetailsCardBuilder.Build(),
	}, nil
}

func setLineItemWithDisplayValue(mfDailyChange *mfUtils.DailyChangeDetails, dailyChangeValue float64) *secretsFePb.PortfolioTrackerLineItem {
	defaultLineItem := buildDefaultLineItem(mfDailyChange.SchemeAnalytics.GetSchemeDetail())

	enrichedSchemeDetails := mfDailyChange.SchemeAnalytics.GetEnrichedAnalytics().GetAnalytics().GetSchemeDetails()
	returnsPercentage := mf.ConvertMoneyToFloat(enrichedSchemeDetails.GetUnrealisedReturns()) * 100 / mf.ConvertMoneyToFloat(enrichedSchemeDetails.GetInvestedValue())

	portfolioTrackerLineItemBuilder := secretsFePb.NewPortfolioTrackerLineItemBuilder().
		SetLineItem(defaultLineItem).
		AddLineItemDisplayValue(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_DAILY_CHANGE.String(),
			feInvestmentPb.NewLineItemHeadingTag().WithHeading(getRightHeadingTruncatedMoney(mf.FloatToMoney(dailyChangeValue))).
				AddTag(feInvestmentPb.NewTags().WithTag(ui.NewITC().WithTexts(getRightTagPercentage(mfDailyChange.DailyNavPercentageChange)...)))).
		AddLineItemDisplayValue(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_RETURNS.String(),
			feInvestmentPb.NewLineItemHeadingTag().WithHeading(getRightHeadingTruncatedMoney(enrichedSchemeDetails.GetUnrealisedReturns())).
				AddTag(feInvestmentPb.NewTags().WithTag(ui.NewITC().WithTexts(getRightTagPercentage(returnsPercentage)...)))).
		AddLineItemDisplayValue(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_VALUE.String(),
			feInvestmentPb.NewLineItemHeadingTag().WithHeading(getRightHeadingTruncatedMoney(enrichedSchemeDetails.GetCurrentValue())).
				AddTag(feInvestmentPb.NewTags().WithTag(ui.NewITC().WithTexts(getRightTagMoney(enrichedSchemeDetails.GetInvestedValue())...)))).
		AddLineItemDisplayValue(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_XIRR.String(),
			feInvestmentPb.NewLineItemHeadingTag().WithHeading(getXirr(enrichedSchemeDetails.GetXIRR().GetValue(), commontypes.FontStyle_NUMBER_S))).Build()
	return portfolioTrackerLineItemBuilder
}

func setToggleValues(portfolioTrackerAssetDetailsCardBuilder *secretsFePb.PortfolioTrackerAssetDetailsCardBuilder, variable *analyserVariablePb.AnalysisVariable,
	aggregatedDailyChangeValue float64) *secretsFePb.PortfolioTrackerAssetDetailsCardBuilder {
	enrichedPortfolioDetails := variable.GetMfSecretsSchemeAnalytics().GetEnrichedMfPortfolioAnalytics().GetPortfolio().GetPortfolioDetails()

	dailyChangePercentage := aggregatedDailyChangeValue * 100 / mf.ConvertMoneyToFloat(enrichedPortfolioDetails.GetPortfolioValue())
	returnsPercentage := mf.ConvertMoneyToFloat(enrichedPortfolioDetails.GetUnrealisedReturns()) * 100 / mf.ConvertMoneyToFloat(enrichedPortfolioDetails.GetInvestedValue())

	portfolioTrackerAssetDetailsCardBuilder = portfolioTrackerAssetDetailsCardBuilder.
		AddToggleValue(secretsFePb.NewToggleValueBuilder().
			SetToggleValueType(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_DAILY_CHANGE.String()).
			SetAggregatedValue(ui.NewITC().WithTexts(getToggleHeadlineForMoneyAndPercentage(mf.FloatToMoney(aggregatedDailyChangeValue), dailyChangePercentage)...)).
			SetToggleText("Day Change (%)").Build()).
		AddToggleValue(secretsFePb.NewToggleValueBuilder().
			SetToggleValueType(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_RETURNS.String()).
			SetAggregatedValue(ui.NewITC().WithTexts(getToggleHeadlineForMoneyAndPercentage(enrichedPortfolioDetails.GetUnrealisedReturns(), returnsPercentage)...)).
			SetToggleText("Returns (%)").Build()).
		AddToggleValue(secretsFePb.NewToggleValueBuilder().
			SetToggleValueType(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_VALUE.String()).
			SetAggregatedValue(ui.NewITC().WithTexts(getToggleHeadlineForCurrentAndInvestedAmounts(enrichedPortfolioDetails.GetPortfolioValue(), enrichedPortfolioDetails.GetInvestedValue())...)).
			SetToggleText("Current (Invested)").Build()).
		AddToggleValue(secretsFePb.NewToggleValueBuilder().
			SetToggleValueType(beEnums.PortfolioTrackerToggleValueType_PORTFOLIO_TRACKER_TOGGLE_VALUE_TYPE_XIRR.String()).
			SetAggregatedValue(ui.NewITC().WithTexts(getXirr(enrichedPortfolioDetails.GetXIRR().GetValue(), commontypes.FontStyle_HEADLINE_L)...)).
			SetToggleText("XIRR").Build())
	return portfolioTrackerAssetDetailsCardBuilder
}

func buildDefaultLineItem(schemeDetails *mfPb.MutualFund) *feInvestmentPb.LineItem {
	lineItemBuilder := feInvestmentPb.NewLineItem().
		WithLeftIconProperties(investmentPkg.IconsForAmc[schemeDetails.GetAmc()], 32, 32, commontypes.ImageType_PNG).
		WithLeftHeading(ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(schemeDetails.GetNameData().GetShortName(), colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_S))).
		WithLeftTags(feInvestmentPb.NewTags().
			WithTag(getTagForLineItem(schemeDetails.GetAssetClass().String())).
			WithTag(getTagForLineItem(schemeDetails.GetCategoryName().String())))

	if schemeDetails.GetPlanType() == mfPb.PlanType_DIRECT {
		lineItemBuilder = lineItemBuilder.WithDeeplink(getMutualFundDeeplink(schemeDetails.GetId()))
	}
	return lineItemBuilder
}

func getTagForLineItem(text string) *ui.IconTextComponent {
	text = strings.ReplaceAll(text, "_", " ")
	return ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(text, colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_OVERLINE_2XS_CAPS)).
		WithContainer(16, 85, 8, colors.ColorOnLightHighEmphasis).WithContainerPaddingSymmetrical(8, 2)
}

func getMutualFundDeeplink(mfId string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_MUTUAL_FUND_DETAILS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_MutualFundDetailsScreenOptions{
			MutualFundDetailsScreenOptions: &deeplinkPb.MutualFundDetailsScreenOptions{
				MutualFundId: mfId,
			},
		},
	}
}
