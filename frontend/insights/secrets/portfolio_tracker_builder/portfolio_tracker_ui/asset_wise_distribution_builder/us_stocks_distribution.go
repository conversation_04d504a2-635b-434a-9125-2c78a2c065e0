package assetwisedistributionbuilder

import (
	"context"

	"github.com/epifi/be-common/pkg/epifierrors"
)

type UsStocksDistribution struct{}

func NewUsStocksDistribution() *UsStocksDistribution {
	return &UsStocksDistribution{}
}

func (t *UsStocksDistribution) BuildAssetWiseDistribution(ctx context.Context, req *BuildAssetWiseDistributionRequest) (*BuildAssetWiseDistributionResponse, error) {
	return nil, epifierrors.ErrUnimplemented
}
