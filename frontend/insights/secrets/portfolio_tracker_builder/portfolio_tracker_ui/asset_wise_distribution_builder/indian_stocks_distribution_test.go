package assetwisedistributionbuilder

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/frontend/config/genconf"
	releaseMocks "github.com/epifi/gamma/pkg/feature/release/mocks"
)

func TestMain(m *testing.M) {
	flag.Parse()

	// Init config
	gconf, err := genconf.Load()
	if err != nil {
		log.Fatal("failed to load dynamic config", err)
	}

	// Setup logger
	logger.Init(gconf.Application().Environment)

	exitCode := m.Run()
	os.Exit(exitCode)
}

func TestDefaultMetadata(t *testing.T) {
	metadata := defaultMetadata()

	expectedMetadata := &analyserVariablePb.SecurityMetadata{
		LogoUrl:      defaultLogoUrl,
		SecurityName: "Unknown",
	}

	if !reflect.DeepEqual(metadata, expectedMetadata) {
		t.Errorf("defaultMetadata() = %v, want %v", metadata, expectedMetadata)
	}
}

func TestNewIndianStocksDistribution(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockReleaseEvaluator := releaseMocks.NewMockIEvaluator(ctrl)

	isd := NewIndianStocksDistribution(mockReleaseEvaluator)

	if isd == nil {
		t.Error("NewIndianStocksDistribution() returned nil")
		return
	}

	if isd.releaseEvaluator != mockReleaseEvaluator {
		t.Error("NewIndianStocksDistribution() did not set releaseEvaluator correctly")
	}
}

func TestIndianStocksDistribution_BuildAssetWiseDistribution(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockReleaseEvaluator := releaseMocks.NewMockIEvaluator(ctrl)
	isd := NewIndianStocksDistribution(mockReleaseEvaluator)

	// Test data setup
	actorId := "test-actor-123"
	assetType := networthBePb.AssetType_ASSET_TYPE_INDIAN_SECURITIES

	// Create test money values
	initialValue := money.ParseFloat(10000.0, "INR")
	finalValue := money.ParseFloat(12000.0, "INR")
	dailyChange := 500.0

	// Create test asset value changes
	assetValueChanges := []*networthBePb.AssetValueChange{
		{
			AssetId:          "STOCK001",
			Change:           dailyChange,
			InitialDateValue: initialValue,
			FinalDateValue:   finalValue,
		},
		{
			AssetId:          "STOCK002",
			Change:           -200.0,
			InitialDateValue: money.ParseFloat(5000.0, "INR"),
			FinalDateValue:   money.ParseFloat(4800.0, "INR"),
		},
	}

	// Create security metadata
	securityMetadataMap := map[string]*analyserVariablePb.SecurityMetadata{
		"STOCK001": {
			LogoUrl:      "https://example.com/stock1.png",
			SecurityName: "Test Stock 1",
		},
		"STOCK002": {
			LogoUrl:      "https://example.com/stock2.png",
			SecurityName: "Test Stock 2",
		},
	}

	// Create day change response
	dayChangeResponse := &networthBePb.AssetTypeDayChangeResponse{
		AssetsValueChange:     assetValueChanges,
		TotalChange:           300.0, // 500 - 200
		InitialDateTotalValue: money.ParseFloat(15000.0, "INR"),
		FinalDateTotalValue:   money.ParseFloat(16800.0, "INR"),
	}

	// Create analysis variable using the correct oneof structure
	analysisVariable := &analyserVariablePb.AnalysisVariable{
		AnalysisVariableState: analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_AVAILABLE,
		Variable: &analyserVariablePb.AnalysisVariable_IndianStocksDistribution{
			IndianStocksDistribution: &analyserVariablePb.AssetTypeDayChangeResponseWrapper{
				DayChangeResponse:   dayChangeResponse,
				SecurityMetadataMap: securityMetadataMap,
			},
		},
	}

	analysisVariableMap := map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable{
		analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION: analysisVariable,
	}

	type args struct {
		ctx context.Context
		req *BuildAssetWiseDistributionRequest
	}

	tests := []struct {
		name      string
		args      args
		setupMock func()
		want      *BuildAssetWiseDistributionResponse
		wantErr   bool
	}{
		{
			name: "success case with feature enabled",
			args: args{
				ctx: context.Background(),
				req: &BuildAssetWiseDistributionRequest{
					ActorId:             actorId,
					AssetType:           assetType,
					AnalysisVariableMap: analysisVariableMap,
					LineItemCount:       5,
				},
			},
			setupMock: func() {
				mockReleaseEvaluator.EXPECT().
					Evaluate(gomock.Any(), gomock.Any()).
					Return(true, nil)
			},
			want: &BuildAssetWiseDistributionResponse{
				AssetType: assetType,
				PortfolioTrackerAssetDetailsCard: &secretsFePb.PortfolioTrackerAssetDetailsCard{
					AssetType:      assetType.String(),
					AssetCountText: commontypes.GetTextFromStringFontColourFontStyle("2 Stocks", "#646464", commontypes.FontStyle_OVERLINE_2XS_CAPS),
					LineItems:      []*secretsFePb.PortfolioTrackerLineItem{},                     // Will be populated by builder
					ToggleList:     []*secretsFePb.PortfolioTrackerAssetDetailsCard_ToggleValue{}, // Will be populated by builder
				},
			},
			wantErr: false,
		},
		{
			name: "feature disabled",
			args: args{
				ctx: context.Background(),
				req: &BuildAssetWiseDistributionRequest{
					ActorId:             actorId,
					AssetType:           assetType,
					AnalysisVariableMap: analysisVariableMap,
					LineItemCount:       5,
				},
			},
			setupMock: func() {
				mockReleaseEvaluator.EXPECT().
					Evaluate(gomock.Any(), gomock.Any()).
					Return(false, nil)
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "release evaluator error",
			args: args{
				ctx: context.Background(),
				req: &BuildAssetWiseDistributionRequest{
					ActorId:             actorId,
					AssetType:           assetType,
					AnalysisVariableMap: analysisVariableMap,
					LineItemCount:       5,
				},
			},
			setupMock: func() {
				mockReleaseEvaluator.EXPECT().
					Evaluate(gomock.Any(), gomock.Any()).
					Return(false, fmt.Errorf("evaluation failed"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "missing analysis variable",
			args: args{
				ctx: context.Background(),
				req: &BuildAssetWiseDistributionRequest{
					ActorId:             actorId,
					AssetType:           assetType,
					AnalysisVariableMap: map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable{},
					LineItemCount:       5,
				},
			},
			setupMock: func() {
				mockReleaseEvaluator.EXPECT().
					Evaluate(gomock.Any(), gomock.Any()).
					Return(true, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "analysis variable data missing",
			args: args{
				ctx: context.Background(),
				req: &BuildAssetWiseDistributionRequest{
					ActorId:   actorId,
					AssetType: assetType,
					AnalysisVariableMap: map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable{
						analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION: {
							AnalysisVariableState: analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_DATA_MISSING,
						},
					},
					LineItemCount: 5,
				},
			},
			setupMock: func() {
				mockReleaseEvaluator.EXPECT().
					Evaluate(gomock.Any(), gomock.Any()).
					Return(true, nil)
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "zero invested value error",
			args: args{
				ctx: context.Background(),
				req: &BuildAssetWiseDistributionRequest{
					ActorId:   actorId,
					AssetType: assetType,
					AnalysisVariableMap: map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable{
						analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION: {
							AnalysisVariableState: analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_AVAILABLE,
							Variable: &analyserVariablePb.AnalysisVariable_IndianStocksDistribution{
								IndianStocksDistribution: &analyserVariablePb.AssetTypeDayChangeResponseWrapper{
									DayChangeResponse: &networthBePb.AssetTypeDayChangeResponse{
										AssetsValueChange: []*networthBePb.AssetValueChange{
											{
												AssetId:          "STOCK001",
												Change:           100.0,
												InitialDateValue: money.ZeroINR().GetPb(), // Zero invested value
												FinalDateValue:   money.ParseFloat(1000.0, "INR"),
											},
										},
									},
									SecurityMetadataMap: securityMetadataMap,
								},
							},
						},
					},
					LineItemCount: 5,
				},
			},
			setupMock: func() {
				mockReleaseEvaluator.EXPECT().
					Evaluate(gomock.Any(), gomock.Any()).
					Return(true, nil)
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "limited line items with view more CTA",
			args: args{
				ctx: context.Background(),
				req: &BuildAssetWiseDistributionRequest{
					ActorId:             actorId,
					AssetType:           assetType,
					AnalysisVariableMap: analysisVariableMap,
					LineItemCount:       1, // Limit to 1 item
				},
			},
			setupMock: func() {
				mockReleaseEvaluator.EXPECT().
					Evaluate(gomock.Any(), gomock.Any()).
					Return(true, nil)
			},
			want: &BuildAssetWiseDistributionResponse{
				AssetType: assetType,
				PortfolioTrackerAssetDetailsCard: &secretsFePb.PortfolioTrackerAssetDetailsCard{
					AssetType:      assetType.String(),
					AssetCountText: commontypes.GetTextFromStringFontColourFontStyle("2 Stocks", "#646464", commontypes.FontStyle_OVERLINE_2XS_CAPS),
					LineItems:      []*secretsFePb.PortfolioTrackerLineItem{}, // Will contain 1 item
					ToggleList:     []*secretsFePb.PortfolioTrackerAssetDetailsCard_ToggleValue{},
					// ViewMoreCta will be set by the builder
				},
			},
			wantErr: false,
		},
		{
			name: "no line item count specified",
			args: args{
				ctx: context.Background(),
				req: &BuildAssetWiseDistributionRequest{
					ActorId:             actorId,
					AssetType:           assetType,
					AnalysisVariableMap: analysisVariableMap,
					LineItemCount:       0, // No limit
				},
			},
			setupMock: func() {
				mockReleaseEvaluator.EXPECT().
					Evaluate(gomock.Any(), gomock.Any()).
					Return(true, nil)
			},
			want: &BuildAssetWiseDistributionResponse{
				AssetType: assetType,
				PortfolioTrackerAssetDetailsCard: &secretsFePb.PortfolioTrackerAssetDetailsCard{
					AssetType:      assetType.String(),
					AssetCountText: commontypes.GetTextFromStringFontColourFontStyle("2 Stocks", "#646464", commontypes.FontStyle_OVERLINE_2XS_CAPS),
					LineItems:      []*secretsFePb.PortfolioTrackerLineItem{}, // Will contain all items
					ToggleList:     []*secretsFePb.PortfolioTrackerAssetDetailsCard_ToggleValue{},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()
			got, err := isd.BuildAssetWiseDistribution(tt.args.ctx, tt.args.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("BuildAssetWiseDistribution() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.want == nil && got != nil {
				t.Errorf("BuildAssetWiseDistribution() = %v, want nil", got)
				return
			}

			if tt.want != nil && got == nil {
				t.Errorf("BuildAssetWiseDistribution() = nil, want %v", tt.want)
				return
			}

			if tt.want != nil && got != nil {
				// Check basic fields
				if got.AssetType != tt.want.AssetType {
					t.Errorf("BuildAssetWiseDistribution() AssetType = %v, want %v", got.AssetType, tt.want.AssetType)
				}

				if got.PortfolioTrackerAssetDetailsCard == nil {
					t.Error("BuildAssetWiseDistribution() PortfolioTrackerAssetDetailsCard is nil")
					return
				}

				// Check asset count text contains expected number of stocks
				if tt.args.req.AnalysisVariableMap != nil {
					if variable, ok := tt.args.req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION]; ok {
						if variable.GetAnalysisVariableState() == analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_AVAILABLE {
							expectedStockCount := len(variable.GetIndianStocksDistribution().GetDayChangeResponse().GetAssetsValueChange())
							expectedText := fmt.Sprintf("%d Stocks", expectedStockCount)
							if got.PortfolioTrackerAssetDetailsCard.AssetCountText.GetPlainString() != expectedText {
								t.Errorf("BuildAssetWiseDistribution() AssetCountText = %v, want %v",
									got.PortfolioTrackerAssetDetailsCard.AssetCountText.GetPlainString(), expectedText)
							}
						}
					}
				}
			}
		})
	}
}

func TestIndianStocksDistribution_BuildAssetWiseDistribution_WithMissingMetadata(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockReleaseEvaluator := releaseMocks.NewMockIEvaluator(ctrl)
	isd := NewIndianStocksDistribution(mockReleaseEvaluator)

	// Test case where security metadata is missing for some stocks
	assetValueChanges := []*networthBePb.AssetValueChange{
		{
			AssetId:          "STOCK_NO_METADATA",
			Change:           100.0,
			InitialDateValue: money.ParseFloat(1000.0, "INR"),
			FinalDateValue:   money.ParseFloat(1100.0, "INR"),
		},
	}

	dayChangeResponse := &networthBePb.AssetTypeDayChangeResponse{
		AssetsValueChange:     assetValueChanges,
		TotalChange:           100.0,
		InitialDateTotalValue: money.ParseFloat(1000.0, "INR"),
		FinalDateTotalValue:   money.ParseFloat(1100.0, "INR"),
	}

	// Empty metadata map - stock metadata will be missing
	securityMetadataMap := map[string]*analyserVariablePb.SecurityMetadata{}

	analysisVariable := &analyserVariablePb.AnalysisVariable{
		AnalysisVariableState: analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_AVAILABLE,
		Variable: &analyserVariablePb.AnalysisVariable_IndianStocksDistribution{
			IndianStocksDistribution: &analyserVariablePb.AssetTypeDayChangeResponseWrapper{
				DayChangeResponse:   dayChangeResponse,
				SecurityMetadataMap: securityMetadataMap,
			},
		},
	}

	analysisVariableMap := map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable{
		analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION: analysisVariable,
	}

	req := &BuildAssetWiseDistributionRequest{
		ActorId:             "test-actor",
		AssetType:           networthBePb.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
		AnalysisVariableMap: analysisVariableMap,
		LineItemCount:       5,
	}

	mockReleaseEvaluator.EXPECT().
		Evaluate(gomock.Any(), gomock.Any()).
		Return(true, nil)

	got, err := isd.BuildAssetWiseDistribution(context.Background(), req)

	if err != nil {
		t.Errorf("BuildAssetWiseDistribution() error = %v, want nil", err)
		return
	}

	if got == nil {
		t.Error("BuildAssetWiseDistribution() returned nil")
		return
	}

	// Should use default metadata for missing stock metadata
	if got.PortfolioTrackerAssetDetailsCard.AssetCountText.GetPlainString() != "1 Stocks" {
		t.Errorf("BuildAssetWiseDistribution() AssetCountText = %v, want '1 Stocks'",
			got.PortfolioTrackerAssetDetailsCard.AssetCountText.GetPlainString())
	}
}

func TestIndianStocksDistribution_BuildAssetWiseDistribution_SortingBehavior(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockReleaseEvaluator := releaseMocks.NewMockIEvaluator(ctrl)
	isd := NewIndianStocksDistribution(mockReleaseEvaluator)

	// Create stocks with different daily changes to test sorting
	assetValueChanges := []*networthBePb.AssetValueChange{
		{
			AssetId:          "STOCK_LOW",
			Change:           100.0, // Lowest change
			InitialDateValue: money.ParseFloat(1000.0, "INR"),
			FinalDateValue:   money.ParseFloat(1100.0, "INR"),
		},
		{
			AssetId:          "STOCK_HIGH",
			Change:           500.0, // Highest change
			InitialDateValue: money.ParseFloat(2000.0, "INR"),
			FinalDateValue:   money.ParseFloat(2500.0, "INR"),
		},
		{
			AssetId:          "STOCK_MID",
			Change:           300.0, // Middle change
			InitialDateValue: money.ParseFloat(1500.0, "INR"),
			FinalDateValue:   money.ParseFloat(1800.0, "INR"),
		},
	}

	dayChangeResponse := &networthBePb.AssetTypeDayChangeResponse{
		AssetsValueChange:     assetValueChanges,
		TotalChange:           900.0,
		InitialDateTotalValue: money.ParseFloat(4500.0, "INR"),
		FinalDateTotalValue:   money.ParseFloat(5400.0, "INR"),
	}

	securityMetadataMap := map[string]*analyserVariablePb.SecurityMetadata{
		"STOCK_LOW": {
			LogoUrl:      "https://example.com/low.png",
			SecurityName: "Low Stock",
		},
		"STOCK_HIGH": {
			LogoUrl:      "https://example.com/high.png",
			SecurityName: "High Stock",
		},
		"STOCK_MID": {
			LogoUrl:      "https://example.com/mid.png",
			SecurityName: "Mid Stock",
		},
	}

	analysisVariable := &analyserVariablePb.AnalysisVariable{
		AnalysisVariableState: analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_AVAILABLE,
		Variable: &analyserVariablePb.AnalysisVariable_IndianStocksDistribution{
			IndianStocksDistribution: &analyserVariablePb.AssetTypeDayChangeResponseWrapper{
				DayChangeResponse:   dayChangeResponse,
				SecurityMetadataMap: securityMetadataMap,
			},
		},
	}

	analysisVariableMap := map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable{
		analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION: analysisVariable,
	}

	req := &BuildAssetWiseDistributionRequest{
		ActorId:             "test-actor",
		AssetType:           networthBePb.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
		AnalysisVariableMap: analysisVariableMap,
		LineItemCount:       0, // No limit to see all items
	}

	mockReleaseEvaluator.EXPECT().
		Evaluate(gomock.Any(), gomock.Any()).
		Return(true, nil)

	got, err := isd.BuildAssetWiseDistribution(context.Background(), req)

	if err != nil {
		t.Errorf("BuildAssetWiseDistribution() error = %v, want nil", err)
		return
	}

	if got == nil {
		t.Error("BuildAssetWiseDistribution() returned nil")
		return
	}

	// Verify that stocks are sorted by daily change in descending order
	// Expected order: STOCK_HIGH (500), STOCK_MID (300), STOCK_LOW (100)
	if len(got.PortfolioTrackerAssetDetailsCard.LineItems) != 3 {
		t.Errorf("Expected 3 line items, got %d", len(got.PortfolioTrackerAssetDetailsCard.LineItems))
	}
}

// Test helper function to create a basic analysis variable for testing
func createTestAnalysisVariable(assetChanges []*networthBePb.AssetValueChange, metadataMap map[string]*analyserVariablePb.SecurityMetadata) *analyserVariablePb.AnalysisVariable {
	totalChange := 0.0
	var totalInitial, totalFinal *moneyPb.Money

	if len(assetChanges) > 0 {
		totalInitial = money.ZeroINR().GetPb()
		totalFinal = money.ZeroINR().GetPb()

		for _, change := range assetChanges {
			totalChange += change.Change
			if change.InitialDateValue != nil {
				totalInitial, _ = money.Sum(totalInitial, change.InitialDateValue)
			}
			if change.FinalDateValue != nil {
				totalFinal, _ = money.Sum(totalFinal, change.FinalDateValue)
			}
		}
	}

	return &analyserVariablePb.AnalysisVariable{
		AnalysisVariableState: analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_AVAILABLE,
		Variable: &analyserVariablePb.AnalysisVariable_IndianStocksDistribution{
			IndianStocksDistribution: &analyserVariablePb.AssetTypeDayChangeResponseWrapper{
				DayChangeResponse: &networthBePb.AssetTypeDayChangeResponse{
					AssetsValueChange:     assetChanges,
					TotalChange:           totalChange,
					InitialDateTotalValue: totalInitial,
					FinalDateTotalValue:   totalFinal,
				},
				SecurityMetadataMap: metadataMap,
			},
		},
	}
}

func TestIndianStocksDistribution_BuildAssetWiseDistribution_EdgeCases(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockReleaseEvaluator := releaseMocks.NewMockIEvaluator(ctrl)
	isd := NewIndianStocksDistribution(mockReleaseEvaluator)

	tests := []struct {
		name              string
		assetChanges      []*networthBePb.AssetValueChange
		metadataMap       map[string]*analyserVariablePb.SecurityMetadata
		lineItemCount     int
		expectError       bool
		expectedStockText string
	}{
		{
			name: "single stock",
			assetChanges: []*networthBePb.AssetValueChange{
				{
					AssetId:          "SINGLE_STOCK",
					Change:           150.0,
					InitialDateValue: money.ParseFloat(1000.0, "INR"),
					FinalDateValue:   money.ParseFloat(1150.0, "INR"),
				},
			},
			metadataMap: map[string]*analyserVariablePb.SecurityMetadata{
				"SINGLE_STOCK": {
					LogoUrl:      "https://example.com/single.png",
					SecurityName: "Single Stock",
				},
			},
			lineItemCount:     5,
			expectError:       false,
			expectedStockText: "1 Stocks",
		},
		{
			name: "negative daily change",
			assetChanges: []*networthBePb.AssetValueChange{
				{
					AssetId:          "NEGATIVE_STOCK",
					Change:           -250.0,
					InitialDateValue: money.ParseFloat(2000.0, "INR"),
					FinalDateValue:   money.ParseFloat(1750.0, "INR"),
				},
			},
			metadataMap: map[string]*analyserVariablePb.SecurityMetadata{
				"NEGATIVE_STOCK": {
					LogoUrl:      "https://example.com/negative.png",
					SecurityName: "Negative Stock",
				},
			},
			lineItemCount:     5,
			expectError:       false,
			expectedStockText: "1 Stocks",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			analysisVariable := createTestAnalysisVariable(tt.assetChanges, tt.metadataMap)
			analysisVariableMap := map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable{
				analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION: analysisVariable,
			}

			req := &BuildAssetWiseDistributionRequest{
				ActorId:             "test-actor",
				AssetType:           networthBePb.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
				AnalysisVariableMap: analysisVariableMap,
				LineItemCount:       tt.lineItemCount,
			}

			mockReleaseEvaluator.EXPECT().
				Evaluate(gomock.Any(), gomock.Any()).
				Return(true, nil)

			got, err := isd.BuildAssetWiseDistribution(context.Background(), req)

			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
				return
			}

			if !tt.expectError && err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if !tt.expectError {
				if got == nil {
					t.Error("Expected result but got nil")
					return
				}

				if got.PortfolioTrackerAssetDetailsCard.AssetCountText.GetPlainString() != tt.expectedStockText {
					t.Errorf("Expected AssetCountText = %v, got %v",
						tt.expectedStockText, got.PortfolioTrackerAssetDetailsCard.AssetCountText.GetPlainString())
				}
			}
		})
	}
}
