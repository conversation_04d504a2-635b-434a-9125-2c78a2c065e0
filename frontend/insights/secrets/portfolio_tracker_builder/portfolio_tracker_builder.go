package portfoliotrackerbuilder

import (
	"context"

	"github.com/epifi/gamma/api/typesv2/ui"

	"github.com/google/wire"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	secretFeEnums "github.com/epifi/gamma/api/insights/secrets/frontend"
)

var (
	WirePortfolioTrackerBuilderSet = wire.NewSet(NewPortfolioTrackerBuilder, wire.Bind(new(IPortfolioTrackerBuilder), new(*PortfolioTrackerBuilder)))
	ComponentTypeToVariableNames   = map[secretFeEnums.PortfolioTrackerComponentType][]analyserVariablePb.AnalysisVariableName{
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_SUMMARY:            {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_TOP_MOVERS:         {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_YOU_VS_MARKET:      {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_ASSET_DISTRIBUTION: {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_NEXT_STEPS_ACTION: {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_ASSET_CATEGORY_DETAILS,
			analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_INVESTMENT_ACTIVITIES,
			analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_NETWORTH_DETAILS},
		secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_INSIGHTS: {analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS},
	}
)

type IPortfolioTrackerBuilder interface {
	// BuildPortfolioTracker builds the detailed view for the portfolio tracker, returns a response containing fixed and scrollable components
	// fixed components will include title component and navigation toggles and scrollable components will be built for components in request
	BuildPortfolioTracker(ctx context.Context, req *BuildPortfolioTrackerRequest) (*BuildPortfolioTrackerResponse, error)
}

type BuildPortfolioTrackerRequest struct {
	ActorId        string
	ComponentTypes []secretFeEnums.PortfolioTrackerComponentType
}

type BuildPortfolioTrackerResponse struct {
	FixedComponents      []*secretsFePb.PortfolioTrackerComponent
	ScrollableComponents []*secretsFePb.PortfolioTrackerComponent
	FooterComponents     []*ui.IconTextComponent
}

func (b *BuildPortfolioTrackerResponse) GetFixedComponents() []*secretsFePb.PortfolioTrackerComponent {
	if b != nil {
		return b.FixedComponents
	}
	return nil
}

func (b *BuildPortfolioTrackerResponse) GetScrollableComponents() []*secretsFePb.PortfolioTrackerComponent {
	if b != nil {
		return b.ScrollableComponents
	}
	return nil
}

func (b *BuildPortfolioTrackerResponse) GetFooterComponents() []*ui.IconTextComponent {
	if b != nil {
		return b.FooterComponents
	}
	return nil
}
