package portfoliotrackerbuilder

import (
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	secretColors "github.com/epifi/gamma/frontend/insights/secrets/colors"
)

type WeeklyTracker struct{}

func NewWeeklyTracker() *WeeklyTracker {
	return &WeeklyTracker{}
}

func (w *WeeklyTracker) GetTitleComponentConfig(p *PortfolioTrackerBuilder) *TitleComponentConfig {
	return &TitleComponentConfig{
		Title:            "Weekly Mutual Fund Tracker",
		TitleColor:       secretColors.ColorSupportingBerry200,
		Subtitle:         "Know what changed this week",
		SubtitleColor:    colors.ColorOnDarkMediumEmphasis,
		DisplayDate:      getDisplayDateForWeeklyPortfolioTracker(),
		Disclaimer:       getDisclaimer(),
		VisualElementUrl: "https://epifi-icons.pointz.in/weekly_report/header_image",
	}
}

// getDisplayDateForWeeklyPortfolioTracker returns a string representing the date range for the last week.
// The function calculates today's date as the end date, and then computes the start date as 7 days before the end date.
// Both dates are formatted as "DD Mon" (e.g., "30 Mar"). The returned string is in the format "DD Mon - DD Mon",
// for example: "29 Mar - 05 Apr".
func getDisplayDateForWeeklyPortfolioTracker() string {
	// Get today's date
	end := time.Now().In(datetime.IST)
	// Get the date one week back
	start := end.AddDate(0, 0, -7)
	// Format both dates as "DD Mon"
	startStr := fmt.Sprintf("%02d %s", start.Day(), start.Format("Jan"))
	endStr := fmt.Sprintf("%02d %s", end.Day(), end.Format("Jan"))
	return fmt.Sprintf("%s - %s", startStr, endStr)
}
