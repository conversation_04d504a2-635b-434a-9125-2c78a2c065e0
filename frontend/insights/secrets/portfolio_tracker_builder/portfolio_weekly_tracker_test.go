package portfoliotrackerbuilder

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/epifi/be-common/pkg/datetime"
)

// Test for weekly tracker: normal week
func TestGetDisplayDateForWeeklyPortfolioTracker_NormalWeek(t *testing.T) {
	t.Helper()
	// Simulate April 5, 2025
	testTimeNow := func() time.Time {
		return time.Date(2025, 4, 5, 12, 0, 0, 0, datetime.IST)
	}
	getDisplayDateForWeeklyPortfolioTrackerTestable := func(now func() time.Time) string {
		end := now().In(datetime.IST)
		start := end.AddDate(0, 0, -7)
		startStr := fmt.Sprintf("%02d %s", start.Day(), start.Format("Jan"))
		endStr := fmt.Sprintf("%02d %s", end.Day(), end.Format("Jan"))
		return fmt.Sprintf("%s - %s", startStr, endStr)
	}
	got := getDisplayDateForWeeklyPortfolioTrackerTestable(testTimeNow)
	assert.Equal(t, "29 Mar - 05 Apr", got)
}

// Test for weekly tracker: week crossing month boundary
func TestGetDisplayDateForWeeklyPortfolioTracker_CrossMonth(t *testing.T) {
	t.Helper()
	// Simulate March 2, 2025
	testTimeNow := func() time.Time {
		return time.Date(2025, 3, 2, 12, 0, 0, 0, datetime.IST)
	}
	getDisplayDateForWeeklyPortfolioTrackerTestable := func(now func() time.Time) string {
		end := now().In(datetime.IST)
		start := end.AddDate(0, 0, -7)
		startStr := fmt.Sprintf("%02d %s", start.Day(), start.Format("Jan"))
		endStr := fmt.Sprintf("%02d %s", end.Day(), end.Format("Jan"))
		return fmt.Sprintf("%s - %s", startStr, endStr)
	}
	got := getDisplayDateForWeeklyPortfolioTrackerTestable(testTimeNow)
	assert.Equal(t, "23 Feb - 02 Mar", got)
}
