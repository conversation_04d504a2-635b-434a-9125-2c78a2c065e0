// nolint:dupl
package ui

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
)

func GetVendorImageAndText(vendor palPb.Vendor) (vendorIcon string, imageWidth, imageHeight int32, text string) {
	switch vendor {
	case palPb.Vendor_ABFL:
		return "https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloans/abfl/abfl-newlogo", 78, 22, "Aditya Birla Capital"
	case palPb.Vendor_MONEYVIEW:
		return "https://epifi-icons.pointz.in/preapprovedloan/pld_dashboard_moneyview_vendor_icon.png", 88, 18, "Moneyview"
	case palPb.Vendor_FEDERAL:
		return "https://epifi-icons.pointz.in/preapprovedloan/pld_dashboard_federal_vendor_icon.png", 74, 18, "Federal Bank"
	case palPb.Vendor_IDFC:
		return "https://epifi-icons.pointz.in/preapprovedloan/pld_dashboard_idfc_vendor_icon.png", 52, 18, "IDFC First Bank"
	case palPb.Vendor_LIQUILOANS:
		return "https://epifi-icons.pointz.in/preapprovedloan/pld_dashboard_liquiloans_vendor_icon_2.png", 18, 18, "Liquiloans"
	case palPb.Vendor_LENDEN:
		return "https://epifi-icons.pointz.in/loans/lenden/innofin-solutions-name-and-logo.png", 78, 22, "Lenden"
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		return "https://epifi-icons.pointz.in/preapprovedloan/sg_logo.png", 62, 24, "Fi Loans"
	}
	return "", 0, 0, ""
}

func GetVendorSquareImageAndText(vendor palPb.Vendor) (vendorIcon string, imageWidth, imageHeight int32, text string) {
	switch vendor {
	case palPb.Vendor_ABFL:
		return "https://epifi-icons.pointz.in/abfl-square-icon.png", 32, 32, "Aditya Birla Capital"
	case palPb.Vendor_MONEYVIEW:
		return "https://epifi-icons.pointz.in/mv-square-icon.png", 32, 32, "Moneyview"
	case palPb.Vendor_FEDERAL:
		return "https://epifi-icons.pointz.in/fed-square-icon.png", 32, 32, "Federal Bank"
	case palPb.Vendor_IDFC:
		return "https://epifi-icons.pointz.in/idfc-square-icon", 32, 32, "IDFC First Bank"
	case palPb.Vendor_LIQUILOANS:
		return "https://epifi-icons.pointz.in/ll-square-icon.png", 32, 32, "Liquiloans"
	case palPb.Vendor_LENDEN:
		return "https://epifi-icons.pointz.in/preapprovedloan/lenden_square_logo.png", 32, 32, "Lenden"
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		return "https://epifi-icons.pointz.in/sg-square-icon.png", 32, 32, "Fi Loans"
	}
	return "", 0, 0, ""
}

func GetVendorGlowImg(vendor palPb.Vendor) *commontypes.VisualElement {
	switch vendor {
	case palPb.Vendor_ABFL:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/abfl-glow-icon.png", 80, 220)
	case palPb.Vendor_MONEYVIEW:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/mv-glow-icon.png", 80, 220)
	case palPb.Vendor_FEDERAL:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/fed-glow-icon.png", 80, 220)
	case palPb.Vendor_IDFC:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/idfc-glow-icon.png", 80, 220)
	case palPb.Vendor_LIQUILOANS:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/ll-glow-icon.png", 80, 220)
	case palPb.Vendor_LENDEN:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/lenden-glow-icon.png", 80, 220)
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/sg-glow-icon.png", 80, 220)
	}
	return nil
}

func GetPoweredByVendorIcon(vendor palPb.Vendor) *commontypes.VisualElement {
	switch vendor {
	case palPb.Vendor_ABFL:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/powered-by-abfl", 16, 131)
	case palPb.Vendor_MONEYVIEW:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/powered-by-moneyview", 16, 139)
	case palPb.Vendor_FEDERAL:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/powered-by-fed", 32, 135)
	case palPb.Vendor_IDFC:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/powered-by-idfc", 16, 139)
	case palPb.Vendor_LIQUILOANS:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/powered-by-ll", 16, 139)
	case palPb.Vendor_LENDEN:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/loans/lenden/powered-by-innofin-solutions-logo.png", 16, 139)
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/powered-by-fi-loans", 16, 139)
	}
	return nil
}

func GetOfferedByVendorIcon(vendor palPb.Vendor) *commontypes.VisualElement {
	switch vendor {
	case palPb.Vendor_ABFL:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/offered-by-abfl", 16, 126)
	case palPb.Vendor_MONEYVIEW:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/offered-by-moneyview", 32, 130)
	case palPb.Vendor_FEDERAL:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/powered-by-fed", 32, 130)
	case palPb.Vendor_IDFC:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/offered-by-idfc", 16, 139)
	case palPb.Vendor_LIQUILOANS:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/offered-by-ll", 16, 139)
	case palPb.Vendor_LENDEN:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/loans/lenden/offered-by-innofin-solutions-logo.png", 16, 139)
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		return commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/offered-by-fi-loans", 16, 139)
	}
	return nil
}
