package baseprovider

import (
	"github.com/mohae/deepcopy"
	"github.com/pkg/errors"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	commonTypes "github.com/epifi/be-common/api/typesv2/common"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFePb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	paltypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

func GetApplyForLoanAlreadyExistScreen(lh *palFeEnumsPb.LoanHeader, resp *palBePb.ApplyForLoanResponse, dashboardDl *deeplinkPb.Deeplink, isCancelSupported bool, req *palFePb.ApplyForLoanRequest) (*deeplinkPb.Deeplink, error) {
	if isCancelSupported && resp.GetActiveLoanAccountId() == "" {
		return getApplyForLoanAlreadyExistV1Screen(resp, dashboardDl, req)
	}
	ctaText := ""
	var infoItem *deeplinkPb.InfoItem
	if resp.GetActiveLoanAccountId() != "" {
		ctaText = "See active loan"
		infoItem = &deeplinkPb.InfoItem{
			Title: "You already have an active loan on Fi!",
			Desc:  "You will only be able to apply for this loan when your current loan is closed",
		}
	} else if resp.GetLoanRequestId() != "" {
		ctaText = "See active application"
		infoItem = &deeplinkPb.InfoItem{
			Title: "You already have an active loan application!",
			Desc:  "In order to apply for this loan, please cancel any existing application and try again",
		}
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_ERROR_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanErrorScreenOptions{
			PreApprovedLoanErrorScreenOptions: &deeplinkPb.PreApprovedLoanErrorScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/vkyc-under-review.png",
				Details: []*deeplinkPb.InfoItem{
					infoItem,
				},
				Cta: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_CUSTOM,
					Text:         ctaText,
					Deeplink:     dashboardDl,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
				LoanHeader: lh,
			},
		},
	}, nil
}

func getApplyForLoanAlreadyExistV1Screen(resp *palBePb.ApplyForLoanResponse, dashboardDl *deeplinkPb.Deeplink, req *palFePb.ApplyForLoanRequest) (*deeplinkPb.Deeplink, error) {
	if resp.GetActiveLoanRequest() == nil && resp.GetActiveLse() == nil {
		return nil, errors.New("no active loan request found")
	}
	applicationState := getApplicationStateData(resp.GetActiveLoanRequest(), resp.GetActiveLse())
	if !applicationState.GetCanCancel() {
		return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_APPLICATION_CONFIRMATION_BOTTOM_SHEET, &paltypes.LoansApplicationConfirmationBottomSheetScreenOptions{
			VisualElement: commonTypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/pl-late-emi-warning-triangle.png", 140, 140),
			Title:         commonTypes.GetTextFromStringFontColourFontStyle("Your current loan application cannot be cancelled", "#313234", commonTypes.FontStyle_HEADLINE_XL),
			Subtitle:      commonTypes.GetTextFromStringFontColourFontStyle("As this loan application is already under processing, it cannot be cancelled at the moment", "#929599", commonTypes.FontStyle_BODY_S),
			PrimaryCta: &paltypes.LoansCta{
				CtaContent: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_DONE,
					Text:         "Ok, got it",
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
				},
			},
		})
	}

	reqCopy := deepcopy.Copy(req).(*palFePb.ApplyForLoanRequest)
	reqCopy.CancelCurrentLoanRequest = true
	rpcReq, err := anyPb.New(reqCopy)
	if err != nil {
		return nil, errors.Wrap(err, "cannot convert apply for loan request to any type")
	}

	icon := commonTypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/atl-pen-pad.png", 120, 120)
	title := commonTypes.GetTextFromStringFontColourFontStyle("Start a new loan application?", "#313234", commonTypes.FontStyle_HEADLINE_XL)
	subTitle := commonTypes.GetTextFromStringFontColourFontStyle("Your previous application will be cancelled to start a new one with the offer you chose", "#929599", commonTypes.FontStyle_BODY_S)
	if resp.GetActiveLoanRequest().GetDetails().GetIsHardPullDone() {
		icon = commonTypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/credit-score.png", 160, 160)
		title = commonTypes.GetTextFromStringFontColourFontStyle("Start a new loan application?", "#313234", commonTypes.FontStyle_HEADLINE_XL)
		subTitle = commonTypes.GetTextFromStringFontColourFontStyle("A hard inquiry has already been made on your credit report. Starting a new application may lead to multiple hard inquiries, which could impact your credit score", "#929599", commonTypes.FontStyle_BODY_S)
	}

	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_APPLICATION_CONFIRMATION_BOTTOM_SHEET, &paltypes.LoansApplicationConfirmationBottomSheetScreenOptions{
		VisualElement: icon,
		Title:         title,
		Subtitle:      subTitle,
		PrimaryCta: &paltypes.LoansCta{
			CtaContent: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         "Continue with previous application",
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
			CtaAction: &paltypes.LoansCtaAction{Action: &paltypes.LoansCtaAction_Deeplink{Deeplink: dashboardDl}},
		},
		SecondaryCta: &paltypes.LoansCta{
			CtaContent: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         "Start new application",
				DisplayTheme: deeplinkPb.Cta_TEXT,
			},
			CtaAction: &paltypes.LoansCtaAction{Action: &paltypes.LoansCtaAction_CallRpc_{CallRpc: &paltypes.LoansCtaAction_CallRpc{
				RpcName:    paltypes.LoansCtaAction_RPC_NAME_APPLY_FOR_LOAN,
				RpcRequest: rpcReq,
			}}},
		},
	})
}
