package provider

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/nulltypes"

	"context"
	"time"

	"google.golang.org/genproto/googleapis/type/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/errors"
	palPb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	genConf "github.com/epifi/gamma/frontend/config/genconf"
)

type IDeeplinkProvider interface {
	GetLoanLandingScreenDeepLink(_ context.Context, lh *palPbFeEnums.LoanHeader, req *LandingInfoRequest) (*deeplinkPb.Deeplink, error)
	GetLoanOfferDetailsScreenDeepLink(ctx context.Context, actorId string, lh *palPbFeEnums.LoanHeader, od *palBePb.GetOfferDetailsResponse, req *palPb.GetOfferDetailsRequest) (*deeplinkPb.Deeplink, error)
	GetPLMandateScreenDeeplink(lh *palPbFeEnums.LoanHeader, loanRequestId string) *deeplinkPb.Deeplink
	GetLoanApplicationDetailsScreenDeepLink(ctx context.Context, actorId string, lh *palPbFeEnums.LoanHeader, od *palBePb.GetOfferDetailsResponse) (*deeplinkPb.Deeplink, error)
	GetEmploymentDetailsDeeplink(lh *palPbFeEnums.LoanHeader, requestId string) *deeplinkPb.Deeplink
	GetOccupationSelectionDeeplink(lh *palPbFeEnums.LoanHeader, defaultEmploymentType *deeplinkPb.PreApprovedOccupationSelectionScreenOptions_EmploymentData) *deeplinkPb.Deeplink
	GetIncomeSelectionDeeplink(req *GetIncomeSelectionDeeplinkRequest) *deeplinkPb.Deeplink
	GetInformationDialogDeeplink(lh *palPbFeEnums.LoanHeader) *deeplinkPb.Deeplink
	GetLoanDashboardScreenDeepLinkWithScreenOptionsV2(ctx context.Context, lh *palPbFeEnums.LoanHeader, res *palBePb.GetDashboardResponse, showLoanOfferScreenV2 bool, entryPoint string) (*deeplinkPb.Deeplink, error)
	GetESignScreen(lh *palPbFeEnums.LoanHeader, loanRequestId string) *deeplinkPb.Deeplink
	GetLoanOfferDetailsV2ScreenDeepLink(ctx context.Context, lh *palPbFeEnums.LoanHeader, offerId string, od *palBePb.GetOfferDetailsResponse, screenVersion map[palPbFeEnums.ComponentName]*preapprovedloans.ComponentVersion, params *genConf.LoanOfferDetailsScreenV2) *deeplinkPb.Deeplink
	GetLoanDetailsScreenDeeplink(ctx context.Context, lh *palPbFeEnums.LoanHeader, beRes *palBePb.GetLoanDetailsResponse, getDowntimeStatusResponse *palBePb.GetDowntimeStatusResponse, conf *genConf.PrePayConfigMap) (*deeplinkPb.Deeplink, error)
	GetAuthPollScreen(lh *palPbFeEnums.LoanHeader, clientReqId string) *deeplinkPb.Deeplink
	GetLoanDashboardScreenDeepLink(ctx context.Context, lh *palPbFeEnums.LoanHeader, actorId string) (*deeplinkPb.Deeplink, error)
	GetLoanDetailsScreen(lh *palPbFeEnums.LoanHeader, loanAccountId string, isLoanDetailsV2Enabled bool) *deeplinkPb.Deeplink
	GetLoanLandingInfo(ctx context.Context, lh *palPbFeEnums.LoanHeader, req *GetLoanLandingInfoRequest) *deeplinkPb.Deeplink
	GetCustomOfferDetailsScreenDeepLink(lh *palPbFeEnums.LoanHeader, od *palBePb.GetOfferDetailsResponse) *deeplinkPb.Deeplink
	GetLoanApplicationConfirmationViaOtpScreenDeepLink(lh *palPbFeEnums.LoanHeader, loanRequest *palBePb.LoanRequest, lse *palBePb.LoanStepExecution, isOtpGenerated bool, token string) *deeplinkPb.Deeplink
	GetLoanApplicationConfirmationViaOtpIncorrectScreenDeepLink(lh *palPbFeEnums.LoanHeader, loanRequest *palBePb.LoanRequest, lse *palBePb.LoanStepExecution, token string) *deeplinkPb.Deeplink
	GetLoanApplicationConfirmationViaOtpScreen(lh *palPbFeEnums.LoanHeader, loanRequestId string, lseId string) *deeplinkPb.Deeplink
	GetApplicationStatusPollScreenDeepLink(ctx context.Context, lh *palPbFeEnums.LoanHeader, requestId string) (*deeplinkPb.Deeplink, error)
	GetGenericErrorScreen(ctx context.Context, lh *palPbFeEnums.LoanHeader) *deeplinkPb.Deeplink
	GetCancelApplicationScreenDeeplink(ctx context.Context, lh *palPbFeEnums.LoanHeader, offerId string, loanOffer *palBePb.LoanOffer) *deeplinkPb.Deeplink
	GetNoOfferLandingScreenDeepLink(ctx context.Context, lh *palPbFeEnums.LoanHeader, req *NoOfferLandingInfoRequest) *deeplinkPb.Deeplink
	GetKnowMoreScreenDeepLink(ctx context.Context, lh *palPbFeEnums.LoanHeader, faqTopic palPbFeEnums.FaqTopic) *deeplinkPb.Deeplink
	GetOfferDetailsScreenDeepLink(lh *palPbFeEnums.LoanHeader, offerID string) *deeplinkPb.Deeplink
	GetLoanApplicationDetailsV2ScreenDeepLink(lh *palPbFeEnums.LoanHeader, offerId string, od *palBePb.GetOfferDetailsResponse, screenVersion map[palPbFeEnums.ComponentName]*preapprovedloans.ComponentVersion) (*deeplinkPb.Deeplink, error)
	GetApplicationStatusPollScreenWithCustomMsgDeepLink(ctx context.Context, lh *palPbFeEnums.LoanHeader, requestId string, pollingText string, fontColor string, iconUrl string) (*deeplinkPb.Deeplink, error)
	GetVkycPendingScreen(lh *palPbFeEnums.LoanHeader, lrId string) *deeplinkPb.Deeplink
	GetLoanHeader() *palPbFeEnums.LoanHeader
	CheckLoanEligibilityScreenDeeplink(ctx context.Context, actorId string, lh *palPbFeEnums.LoanHeader) (*deeplinkPb.Deeplink, error)
	GetPrePayDetailsScreenDeeplink(
		ctx context.Context,
		lh *palPbFeEnums.LoanHeader,
		beRes *palBePb.GetLoanDetailsResponse,
		loanDefaultDetails *palBePb.LoanDefaultDetails,
		prePayAmount *types.Money,
		isAmountSelectorSkipped bool,
		isPreClose bool,
		fetchUserAccounts bool,
	) (*deeplinkPb.Deeplink, error)

	GetLoanReviewDetailsScreenDeeplink(ctx context.Context, lh *palPbFeEnums.LoanHeader, lrId string, beRes *palBePb.GetLoanReviewDetailsResponse, actorId string) (*deeplinkPb.Deeplink, error)
	GetNoOfferLandingScreenDeepLinkV2(ctx context.Context, lh *palPbFeEnums.LoanHeader, req *NoOfferLandingInfoRequest, deeplink *deeplinkPb.Deeplink) *deeplinkPb.Deeplink
	GetApplicationProgressScreen(ctx context.Context, lh *palPbFeEnums.LoanHeader, res *palBePb.GetDashboardResponse, req *GetApplicationProgressScreenRequest, applMsg *palTypesPb.SectionApplicationMessage) (*deeplinkPb.Deeplink, error)
	GetAcqToLendLandingScreen(lh *palPbFeEnums.LoanHeader) *deeplinkPb.Deeplink
	GetEligibilitySuccessScreen(ctx context.Context, lh *palPbFeEnums.LoanHeader, offerAmount *money.Money, interestRate float64, offerId string, showOfferV2 bool, loanOffer *palBePb.LoanOffer) *deeplinkPb.Deeplink
	GetNextDeeplink(ctx context.Context, lh *palPbFeEnums.LoanHeader, res *palBePb.GetDashboardResponse, showOfferV2 bool, actorId string, postLoansV2Active bool) (*deeplinkPb.Deeplink, error)
	GetDashboardMsgComponent(lh *palPbFeEnums.LoanHeader, lr *palBePb.LoanRequest, loanSteps []*palBePb.LoanStepExecution) (*palTypesPb.SectionApplicationMessage, bool, bool, bool)
	GetAllTransactionDeeplink(ctx context.Context, lh *palPbFeEnums.LoanHeader, loanId string, od *palBePb.GetAllTransactionsResponse, loanDetails *palBePb.GetLoanDetailsResponse) (*deeplinkPb.Deeplink, error)
	GetForceUpgradeLandingResponse(platform commontypes.Platform, offerId string, title string, loanHeader *palPbFeEnums.LoanHeader) *deeplinkPb.Deeplink
	GetLoansApplicationStatusPollDeeplink(ctx context.Context, lh *palPbFeEnums.LoanHeader, actorId string, loanReqId string, params *ApplicationStatusPollDeeplinkParams) (*deeplinkPb.Deeplink, error)
	GetIncomeVerificationLandingScreen(lh *palPbFeEnums.LoanHeader, lrId string, askConsent, isItrEnabled, isMaxAttemptsReached bool) *deeplinkPb.Deeplink
	GetPWARedirectionDeeplink(ctx context.Context, pwaUrl string, lh *palPbFeEnums.LoanHeader) *deeplinkPb.Deeplink
	GetPlDowntimeScreenDeepLink(
		ctx context.Context,
		loanHeader *palPbFeEnums.LoanHeader,
		nextAvailableTime time.Time,
	) (*deeplinkPb.Deeplink, error)
	GetLoanPrePayActivityStatusSuccessScreenDeepLink(
		lh *palPbFeEnums.LoanHeader,
		loanAccountId string,
		isLoanDetailsV2Enabled bool,
	) (*deeplinkPb.Deeplink, error)
	GetLoanPreCloseActivityStatusSuccessScreenDeepLink(
		lh *palPbFeEnums.LoanHeader,
		loanAccountId string,
		isLoanDetailsV2Enabled bool,
	) (*deeplinkPb.Deeplink, error)
	GetLoanActivityStatusPendingScreenDeepLink(lh *palPbFeEnums.LoanHeader, loanAccountId string, isLoanDetailsV2Enabled bool) *deeplinkPb.Deeplink
	GetEsignViewDocumentScreen(lh *palPbFeEnums.LoanHeader, loanRequestId string, documentUrl string, docType palPbFeEnums.LoanDocType) *deeplinkPb.Deeplink
	GetLoanApplicationDetailsForDashboard(ctx context.Context, lh *palPbFeEnums.LoanHeader, lr *palBePb.LoanRequest, lses []*palBePb.LoanStepExecution, lo *palBePb.LoanOffer) (*GetDashboardLoanApplicationDetailsResponse, error)
	GetLoanAccountDetailsForDashboard(ctx context.Context, lh *palPbFeEnums.LoanHeader, li *palBePb.LoanInfo) (*GetDashboardLoanAccountDetailsResponse, error)
	GetLoanDetailsScreenDeeplinkV2(ctx context.Context, lh *palPbFeEnums.LoanHeader, res *palBePb.GetLoanDetailsResponse, _ *palBePb.GetDowntimeStatusResponse, prePayConfigMap *genConf.PrePayConfigMap, req *GetLoanDetailsScreenDeeplinkV2Request) (*deeplinkPb.Deeplink, error)
	GetAllTransactionDeeplinkV2(ctx context.Context, lh *palPbFeEnums.LoanHeader, allTxnsRes *palBePb.GetAllTransactionsResponse, loanDetails *palBePb.GetLoanDetailsResponse) (*deeplinkPb.Deeplink, error)
	GetPrePayDetailsScreenV2(ctx context.Context, lh *palPbFeEnums.LoanHeader, beRes *palBePb.GetPrePayDetailsResponse, prePayAmount *types.Money) (*deeplinkPb.Deeplink, error)
	GetLoanRepaymentMethodsScreen(ctx context.Context, lh *palPbFeEnums.LoanHeader, beRes *palBePb.GetPrePayDetailsResponse, amount *types.Money, repayDetailsType palPbFeEnums.RepaymentDetailsType, shouldFetchUserAccounts, isPrePayViaPGEnabled bool) (*deeplinkPb.Deeplink, error)
	// showIncorrectInfoMsg is used when wrong OTP is entered and screen is reseted
	GetAlternateContactVerificationViaOtpScreen(ctx context.Context, lh *palPbFeEnums.LoanHeader, loanStep *palBePb.LoanStepExecution, showIncorrectOtpMsg bool) (*deeplinkPb.Deeplink, error)
	GetAlternateContactCoolOffScreen(ctx context.Context, lh *palPbFeEnums.LoanHeader) (*deeplinkPb.Deeplink, error)
	// GetLoanOfferOnClickDeeplink is used in actions to show S1 screens or apply for loan (moneyview), i.e. when offer banner is clicked or via second look, or via dashboard.
	GetLoanOfferOnClickDeeplink(ctx context.Context, lo *palBePb.LoanOffer) (*deeplinkPb.Deeplink, error)
	// GetAddressDetailsScreen this will be used to show some prefilled address fields based of reverse geocoding of location token
	GetAddressDetailsScreen(lh *palPbFeEnums.LoanHeader, lrId string, address *types.PostalAddress) *deeplinkPb.Deeplink
	GetRevisedLoanOfferDetailsScreenDeepLink(ctx context.Context, lh *palPbFeEnums.LoanHeader, od *palBePb.GetOfferDetailsResponse, req *palPb.GetOfferDetailsRequest) (*deeplinkPb.Deeplink, error)
	GetRevisedLoanOfferApplicationDetailsScreenDeepLink(ctx context.Context, lh *palPbFeEnums.LoanHeader, od *palBePb.GetOfferDetailsResponse, req *palPb.GetApplicationDetailsRequest) (*deeplinkPb.Deeplink, error)
	GetPreBreLoanConsentScreen(lh *palPbFeEnums.LoanHeader, loanRequestId string) (*deeplinkPb.Deeplink, error)
	GetPreBreLoanOfferDetailsScreen(lh *palPbFeEnums.LoanHeader, loanRequestId string) (*deeplinkPb.Deeplink, error)
	GetPreBreLoanOfferIntroScreen(lh *palPbFeEnums.LoanHeader, loanRequestId string) (*deeplinkPb.Deeplink, error)
	GetPrePayAccountSelectionScreen(lh *palPbFeEnums.LoanHeader, req *GetGetPrePayAccountSelectionScreenRequest) (*deeplinkPb.Deeplink, error)
	// to be used in flows like LAMF prepay where we hae option to redirect to either accont selection or slider screens from the given scerrn.
	GetLoanDetailsScreenDeeplinkWithAccountSelection(ctx context.Context, req *GetLoanDetailsScreenDeeplinkWithAccountSelectionRequest) (*deeplinkPb.Deeplink, error)
	GetPrePayDetailsScreenV3(ctx context.Context, req *GetPrepayDetailsScreenV3Request) (*deeplinkPb.Deeplink, error)
	GetLoanRepaymentMethodsScreenV2(ctx context.Context, req *GetLoanRepaymentMethodsScreenV2Request) (*deeplinkPb.Deeplink, error)
	GetPrePayKnowMoreBottomSheetScreen(ctx context.Context, req *GetPrePayKnowMoreBottomSheetScreen) (*deeplinkPb.Deeplink, error)
	GetForceUpgradeLandingResponseV2(ctx context.Context, req *GetForceUpgradeLandingResponseV2Request) *deeplinkPb.Deeplink
	GetPreClosureBlackoutPeriodBottomSheetErrorView() *errors.ErrorView
	GetPreQualOfferCardMultiOfferScreenComponent(loanOffer *palBePb.LoanOffer, isFirstOffer bool, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoanOfferDetailsCard, error)
	GetSoftOfferCardMultiOfferScreenComponent(ctx context.Context, loanOffer *palBePb.LoanOffer, isFirstOffer bool, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoanOfferDetailsCard, error)
	GetHardOfferCardMultiOfferScreenComponent(loanOffer *palBePb.LoanOffer, isFirstOffer bool, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoanOfferDetailsCard, error)
	GetPreQualOfferLandingScreenLoansCta(loanOffer *palBePb.LoanOffer) (*palTypesPb.LoansCta, error)

	// TODO(Brijesh): Use struct instead of too many arguments
	GetCkycVerificationViaOtpScreen(ctx context.Context, lh *palPbFeEnums.LoanHeader, invalidOtp bool, otpExpired bool, otpGenerated bool, loanStep *palBePb.LoanStepExecution, displayStringFromVendor string) (*deeplinkPb.Deeplink, error)
}

type GetIncomeSelectionDeeplinkRequest struct {
	Lh                   *palPbFeEnums.LoanHeader
	DefaultInitialAmount *types.Money
	Title                string
	BottomMessage        string
	IncomeFrequencyLabel *commontypes.Text
	MinValue             *types.Money
	MaxValue             *types.Money
}

type GetForceUpgradeLandingResponseV2Request struct {
	Platform   commontypes.Platform
	OfferId    string
	Title      string
	SubTitle   string
	LoanHeader *palPbFeEnums.LoanHeader
}

type GetPrePayKnowMoreBottomSheetScreen struct {
	Lh *palPbFeEnums.LoanHeader
}

type GetLoanRepaymentMethodsScreenV2Request struct {
	Lh                                            *palPbFeEnums.LoanHeader
	BeRes                                         *palBePb.GetPrePayDetailsResponse
	Amount                                        *types.Money
	RepayDetailsType                              palPbFeEnums.RepaymentDetailsType
	ShouldFetchUserAccounts, IsPrePayViaPGEnabled bool
	LoanPaymentAccountType                        palPbFeEnums.LoanPaymentAccountType
}

type GetGetPrePayAccountSelectionScreenRequest struct {
	LoanId             string
	BeRes              *palBePb.GetPrePayDetailsResponse
	GetLoanDetailsResp *palBePb.GetLoanDetailsResponse
}

type GetPrepayDetailsScreenV3Request struct {
	Lh                     *palPbFeEnums.LoanHeader
	BeRes                  *palBePb.GetPrePayDetailsResponse
	GetLoanDetailsResp     *palBePb.GetLoanDetailsResponse
	PrePayAmount           *types.Money
	LoanPaymentAccountType palPbFeEnums.LoanPaymentAccountType
}

type GetLoanDetailsScreenDeeplinkWithAccountSelectionRequest struct {
	Lh              *palPbFeEnums.LoanHeader
	BeRes           *palBePb.GetLoanDetailsResponse
	PrePayConfigMap *genConf.PrePayConfigMap
	// if the deeplink in cta should redirect to account selection screen, to be used in LAMF prepay flow.
	ShowAccountSelectionScreen bool
	ShowUpdateScreen           bool
	Platform                   commontypes.Platform
}

type GetLoanDetailsScreenDeeplinkV2Request struct {
	// account number which we are going to show to the user
	DisplayAccountNumber string
	// flag to hide emi details tab or not, e.g. in Federal doesn't show this since no data is present
	IsUpcomingEmiDetailsTabAbsent bool
	// flag to hide loan closure tab
	IsLoanClosureNotAllowed bool
}

type ApplicationStatusPollDeeplinkParams struct {
	Icon                nulltypes.NullString
	Title               nulltypes.NullString
	SubTitle            nulltypes.NullString
	RetryDurationInMs   int32
	RetryBackOffInMs    int32
	GetNextActionInSync bool
}

type GetDashboardLoanApplicationDetailsResponse struct {
	ActiveApplicationCards                    *palTypesPb.LoanDashboardBottomSection_SectionDivision
	ShowOfferBannerAccordingToLseDashboardMap bool
}

func (p *GetDashboardLoanApplicationDetailsResponse) GetActiveApplicationCards() *palTypesPb.LoanDashboardBottomSection_SectionDivision {
	if p != nil {
		return p.ActiveApplicationCards
	}
	return nil
}

func (p *GetDashboardLoanApplicationDetailsResponse) GetShowOfferBannerAccordingToLseDashboardMap() bool {
	if p != nil {
		return p.ShowOfferBannerAccordingToLseDashboardMap
	}
	return false
}

type GetDashboardLoanAccountDetailsResponse struct {
	ActiveLoanCard  *palTypesPb.LoanDashboardBottomSection_Component
	DisbursalBanner *palTypesPb.LoanDashboardBottomSection_SectionDivision
}

func (p *GetDashboardLoanAccountDetailsResponse) GetActiveLoanCard() *palTypesPb.LoanDashboardBottomSection_Component {
	if p != nil {
		return p.ActiveLoanCard
	}
	return nil
}

func (p *GetDashboardLoanAccountDetailsResponse) GetDisbursalBanner() *palTypesPb.LoanDashboardBottomSection_SectionDivision {
	if p != nil {
		return p.DisbursalBanner
	}
	return nil
}
