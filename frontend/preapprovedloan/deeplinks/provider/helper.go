package provider

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	palBeDeeplink "github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"
	"google.golang.org/genproto/googleapis/type/money"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	money2 "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	typesPb "github.com/epifi/gamma/api/typesv2"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	genConf "github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/feature/release"
)

const (
	ComingSoonText   = " (Coming Soon)"
	PrivacyPolicyUrl = "https://fi.money/privacy"
)

type LandingInfoRequest struct {
	LoanOffer          *palPb.LoanOffer
	EarlySalaryDetails *palPb.GetLandingInfoResponse_EarlySalaryDetails
	// Deprecated : Offer screen v2 is deprecated (V2 version of older screen)
	ShowLoanOfferV2Screen       bool
	DowntimeStatusResponse      *palPb.GetDowntimeStatusResponse
	DefaultLoanAmountPercentage float64
	LoanOffers                  []*palPb.LoanOffer
}

func (p *LandingInfoRequest) GetLoanOffers() []*palPb.LoanOffer {
	if p != nil {
		return p.LoanOffers
	}
	return nil
}

func (p *LandingInfoRequest) GetLoanOffer() *palPb.LoanOffer {
	if p != nil {
		return p.LoanOffer
	}
	return nil
}

func (p *LandingInfoRequest) GetEarlySalaryDetails() *palPb.GetLandingInfoResponse_EarlySalaryDetails {
	if p != nil {
		return p.EarlySalaryDetails
	}
	return nil
}

func (p *LandingInfoRequest) GetShowLoanOfferV2Screen() bool {
	if p != nil {
		return p.ShowLoanOfferV2Screen
	}
	return false
}

func (p *LandingInfoRequest) GetDowntimeStatusResponse() *palPb.GetDowntimeStatusResponse {
	if p != nil {
		return p.DowntimeStatusResponse
	}
	return nil
}

func (p *LandingInfoRequest) GetDefaultLoanAmountPercentage() float64 {
	if p != nil {
		return p.DefaultLoanAmountPercentage
	}
	return 0
}

type NoOfferLandingInfoRequest struct {
	LoanOffer          *palPb.LoanOffer
	EarlySalaryDetails *palPb.GetLandingInfoResponse_EarlySalaryDetails
	ActorId            string
}

func (p *NoOfferLandingInfoRequest) GetLoanOffer() *palPb.LoanOffer {
	if p != nil {
		return p.LoanOffer
	}
	return nil
}

func (p *NoOfferLandingInfoRequest) GetEarlySalaryDetails() *palPb.GetLandingInfoResponse_EarlySalaryDetails {
	if p != nil {
		return p.EarlySalaryDetails
	}
	return nil
}

func (p *NoOfferLandingInfoRequest) GetActorId() string {
	if p != nil {
		return p.ActorId
	}
	return ""
}

type GetApplicationProgressScreenRequest struct {
	CanCancel   bool
	CanRetry    bool
	ShowOfferV2 bool
	ShowCta     bool
	ActorId     string
}

func (p *GetApplicationProgressScreenRequest) GetActorId() string {
	if p != nil {
		return p.ActorId
	}
	return ""
}

type GetLoanLandingInfoRequest struct {
	ActorId string
}

func (p *GetLoanLandingInfoRequest) GetActorId() string {
	if p != nil {
		return p.ActorId
	}
	return ""
}

type GetLoanAmountSelectorScreenRequest struct {
	OfferId          string
	OfferConstraints *palPb.OfferConstraints
	DefaultAmount    *money.Money
}

func (p *GetLoanAmountSelectorScreenRequest) GetOfferConstraints() *palPb.OfferConstraints {
	if p != nil {
		return p.OfferConstraints
	}
	return nil
}

func (p *GetLoanAmountSelectorScreenRequest) GetDefaultAmount() *money.Money {
	if p != nil {
		return p.DefaultAmount
	}
	return nil
}

func GetText(text string, color string, style commontypes.FontStyle) *commontypes.Text {
	return &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: text,
		},
		FontColor: color,
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: style,
		},
	}
}

func GetVisualElementPng(url string, width, height int32) *commontypes.VisualElement {
	return &commontypes.VisualElement{
		Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{
					Url: url,
				},
				Properties: &commontypes.VisualElementProperties{
					Width:  width,
					Height: height,
				},
				ImageType: commontypes.ImageType_PNG,
			},
		},
	}
}

func GetPrePayTileCta(ctx context.Context, cta *deeplink.Cta, prePayConf *genConf.PrePayConfig, preCloseAmount *money.Money) *deeplink.Cta {
	if !prePayConf.Enabled() || money2.IsZero(preCloseAmount) {
		cta.Status = deeplink.Cta_CTA_STATUS_DISABLED
	} else {
		constraintData := release.NewAppVersionConstraintData(prePayConf.AppVersionConstraintConfig())
		appVersionConstraint := release.NewAppVersionConstraint()
		appVersionEnabled, _ := appVersionConstraint.Evaluate(ctx, constraintData, nil)
		if !appVersionEnabled {
			cta.Status = deeplink.Cta_CTA_STATUS_DISABLED
			return cta
		}

		cta.Status = deeplink.Cta_CTA_STATUS_ENABLED
	}
	return cta
}

func DisablePrePayTileCta(cta *deeplink.Cta) *deeplink.Cta {
	cta.Text += ComingSoonText
	cta.Status = deeplink.Cta_CTA_STATUS_DISABLED
	return cta
}

func GetTextWithBgColor(text string, fontColor string, bgColor string, style commontypes.FontStyle) *commontypes.Text {
	typesText := &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: text,
		},
		FontColor: fontColor,
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: style,
		},
	}
	if bgColor != "" {
		typesText.BgColor = bgColor
	}

	return typesText
}

func GetEmploymentDetailsItemRows(loanReviewDetailsData *palPb.GetLoanReviewDetailsResponse) []*palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_ItemRow {
	onbData := loanReviewDetailsData.GetOnboardingData()
	var itemBlock []*palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_ItemRow
	if onbData.GetEmploymentDetails().GetOccupation() != typesPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED {
		employmentType := cases.Title(language.Und).String(onbData.GetEmploymentDetails().GetOccupation().String()[16:])

		// Check if the employment type exists in EmploymentTypeOptions
		for _, opt := range palBeDeeplink.EmploymentTypeOptions {
			if opt.Enum == onbData.GetEmploymentDetails().GetOccupation() {
				employmentType = opt.DisplayText
				break
			}
		}

		itemBlock = append(itemBlock, &palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_ItemRow{
			Items: []*palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_Item{
				{
					Key:   GetText("Employment type", "#929599", commontypes.FontStyle_SUBTITLE_XS),
					Value: GetText(employmentType, "#313234", commontypes.FontStyle_HEADLINE_M),
				},
			},
		})
	}
	if onbData.GetEmploymentDetails().GetOrganizationName() != "" {
		itemBlock = append(itemBlock, &palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_ItemRow{
			Items: []*palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_Item{
				{
					Key:   GetText("Employer name", "#929599", commontypes.FontStyle_SUBTITLE_XS),
					Value: GetText(cases.Title(language.Und).String(onbData.GetEmploymentDetails().GetOrganizationName()), "#313234", commontypes.FontStyle_HEADLINE_M),
				},
			},
		})
	}
	if onbData.GetEmploymentDetails().GetWorkEmail() != "" {
		itemBlock = append(itemBlock, &palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_ItemRow{
			Items: []*palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_Item{
				{
					Key:   GetText("Work email", "#929599", commontypes.FontStyle_SUBTITLE_XS),
					Value: GetText(onbData.GetEmploymentDetails().GetWorkEmail(), "#313234", commontypes.FontStyle_HEADLINE_M),
				},
			},
		})
	}
	if onbData.GetEmploymentDetails().GetMonthlyIncome() != nil {
		itemBlock = append(itemBlock, &palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_ItemRow{
			Items: []*palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_Item{
				{
					Key:   GetText("Monthly income", "#929599", commontypes.FontStyle_SUBTITLE_XS),
					Value: GetText(fmt.Sprintf("%s/month", money2.ToDisplayStringInIndianFormat(onbData.GetEmploymentDetails().GetMonthlyIncome(), 0, true)), "#313234", commontypes.FontStyle_HEADLINE_M),
				},
			},
		})
	}
	if loanReviewDetailsData.GetOnboardingData().GetEmploymentDetails().GetGSTIN() != "" {
		itemBlock = append(itemBlock, &palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_ItemRow{
			Items: []*palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_Item{
				{
					Key:   GetText("GST Number", "#929599", commontypes.FontStyle_SUBTITLE_XS),
					Value: GetText(onbData.GetEmploymentDetails().GetGSTIN(), "#313234", commontypes.FontStyle_HEADLINE_M),
				},
			},
		})
	}
	if loanReviewDetailsData.GetOnboardingData().GetEmploymentDetails().GetAnnualRevenue() != nil {
		itemBlock = append(itemBlock, &palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_ItemRow{
			Items: []*palTypesPb.LoanApplicationReviewDetailsScreenOptions_DetailBlock_Item{
				{
					Key:   GetText("Annual Revenue", "#929599", commontypes.FontStyle_SUBTITLE_XS),
					Value: GetText(fmt.Sprintf("%s", money2.ToDisplayStringInIndianFormat(onbData.GetEmploymentDetails().GetAnnualRevenue(), 0, true)), "#313234", commontypes.FontStyle_HEADLINE_M),
				},
			},
		})
	}
	return itemBlock
}

func GetLoanAppDataPrivacyTermInfo() *deeplink.PreApprovedLoanApplicationDetailsScreenOptions_TermInfo {
	return &deeplink.PreApprovedLoanApplicationDetailsScreenOptions_TermInfo{
		TermText:             fmt.Sprintf("I accept Fi’s <a href=\"%s\" style=\"color:#00B899\">data privacy policy.</a>", PrivacyPolicyUrl),
		IsTermClickable:      true,
		IsTermLinkUnderlined: false,
		TermCta: &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_WEB_PAGE,
				ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
					WebPageScreenOptions: &deeplink.WebpageScreenOptions{
						WebpageTitle: "Data Privacy Policy",
						WebpageUrl:   PrivacyPolicyUrl,
					},
				},
			},
		},
	}
}

func GetLoanAppDataPrivacyTermInfoV2() *deeplink.TermInfo {
	return &deeplink.TermInfo{
		TermText: &commontypes.Text{
			DisplayValue: &commontypes.Text_Html{
				Html: fmt.Sprintf("I accept Fi’s <a href=\"%s\" style=\"color:#00B899\">data privacy policy.</a>", PrivacyPolicyUrl),
			},
			FontColor: "#333333",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_4,
			},
		},
		IsTermClickable:      true,
		IsTermLinkUnderlined: false,
		TermCta: &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_WEB_PAGE,
				ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
					WebPageScreenOptions: &deeplink.WebpageScreenOptions{
						WebpageTitle: "Data Privacy Policy",
						WebpageUrl:   PrivacyPolicyUrl,
					},
				},
			},
		},
	}
}

func ConvertLoanApplicationDetailsTermInfoToCommon(terms []*deeplink.PreApprovedLoanApplicationDetailsScreenOptions_TermInfo) []*deeplink.TermInfo {
	termsInCommonFormat := make([]*deeplink.TermInfo, 0)
	for _, term := range terms {
		termsInCommonFormat = append(termsInCommonFormat, &deeplink.TermInfo{
			TermText:             commontypes.GetTextFromHtmlStringFontColourFontStyle(term.GetTermText(), "#333333", commontypes.FontStyle_BODY_4),
			TermCta:              term.GetTermCta(),
			IsTermClickable:      term.GetIsTermClickable(),
			IsTermLinkUnderlined: term.GetIsTermLinkUnderlined(),
		})
	}
	return termsInCommonFormat
}

func GetPwaCopyUrlBottomSheetComponentForDashboard(pwaUrl string, lh *pal_enums.LoanHeader) (*palTypesPb.LoanDashboardBottomSection_Component, error) {
	bottomSheetDl, err := deeplinkv3.GetDeeplinkV3(deeplink.Screen_LOANS_PERMISSION_BOTTOM_SHEET_SCREEN, &palTypesPb.LoansPermissionBottomSheetScreenOptions{
		Header:      nil,
		LoanHeader:  lh,
		Title:       GetText("Open the link in your mobile browser to continue", "#313234", commontypes.FontStyle_HEADLINE_XL),
		Description: GetText("Enable the following permissions on your browser", "#6A6D70", commontypes.FontStyle_HEADLINE_S),
		TopIcon: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloan/chat_logo.png",
					},
					Properties: &commontypes.VisualElementProperties{
						Width:  80,
						Height: 80,
					},
					ImageType: commontypes.ImageType_PNG,
				},
			},
		},
		PermissionDialogueLists: []*palTypesPb.LoansPermissionBottomSheetScreenOptions_PermissionDialogueList{
			{
				PermissionTitle: GetText("Camera & Mic", "#929599", commontypes.FontStyle_HEADLINE_S),
				PermissionIcon: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: "https://epifi-icons.s3.ap-south-1.amazonaws.com/Pre-Approved/camera_logo.png",
							},
							Properties: &commontypes.VisualElementProperties{
								Width:  20,
								Height: 20,
							},
							ImageType: commontypes.ImageType_PNG,
						},
					},
				},
			},
			{
				PermissionTitle: GetText("Location", "#929599", commontypes.FontStyle_HEADLINE_S),
				PermissionIcon: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: "https://epifi-icons.s3.ap-south-1.amazonaws.com/Pre-Approved/small_location.png",
							},
							Properties: &commontypes.VisualElementProperties{
								Width:  20,
								Height: 20,
							},
							ImageType: commontypes.ImageType_PNG,
						},
					},
				},
			},
		},
		BgColor:          "#FFFFFF",
		IsCopyCtaEnabled: true,
		TextToBeCopied:   pwaUrl,
		Cta: &deeplink.Cta{
			Text:         "Tap to copy link",
			DisplayTheme: deeplink.Cta_PRIMARY,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "error getting deeplink for loans permission bottom sheet screen")
	}

	return &palTypesPb.LoanDashboardBottomSection_Component{
		Component: &palTypesPb.LoanDashboardBottomSection_Component_BannerWithLeftIconAndRightCta{
			BannerWithLeftIconAndRightCta: &palTypesPb.BannerWithLeftIconAndRightCta{
				Title:       &ui.IconTextComponent{Texts: []*commontypes.Text{GetText("Facing difficulties with your application?", "#313234", commontypes.FontStyle_HEADLINE_S)}},
				Description: &ui.IconTextComponent{Texts: []*commontypes.Text{GetText("Here’s how you can avoid hassles", "#6A6D70", commontypes.FontStyle_HEADLINE_XS)}},
				BgColor:     "#FFFFFF",
				RightCta: &ui.IconTextComponent{
					RightVisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.s3.ap-south-1.amazonaws.com/Pre-Approved/right_arrow_icon.png",
								},
								Properties: &commontypes.VisualElementProperties{
									Width:  32,
									Height: 32,
								},
								ImageType: commontypes.ImageType_PNG,
							},
						},
					},
					Deeplink: bottomSheetDl,
				},
				TopActionTag: true,
			},
		},
	}, nil
}
