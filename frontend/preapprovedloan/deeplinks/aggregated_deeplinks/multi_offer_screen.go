// nolint:dupl
package aggregated_deeplinks

import (
	"context"

	"github.com/pkg/errors"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/preapprovedloan"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	uiFrontend "github.com/epifi/gamma/frontend/preapprovedloan/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

func (adp *AggregatedDeeplinkProvider) GetMultiOfferScreen(ctx context.Context, loanOffers []*preapprovedloan.LoanOffer, isLoansPreQualOfferFlowEnabled bool, currentLr *preapprovedloan.LoanRequest) (*deeplinkPb.Deeplink, error) {
	userResp, userErr := adp.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: loanOffers[0].GetActorId(),
		},
	})
	if te := epifigrpc.RPCError(userResp, userErr); te != nil {
		return nil, errors.Wrap(te, "error in getting user")
	}

	screenOptionsMultiOfferScreen := &palTypesPb.LoansMultipleOfferSelectionScreenOptions{
		BgColor: "#EFF2F6",
		ToolbarHelp: ui.NewITC().WithLeftImageUrlHeightAndWidth(uiFrontend.HelpIconRound, 24, 24).WithDeeplink(&deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_HELP_MAIN,
		}),
		ToolbarTitle:              provider.GetText("We found these offers for you", "#313234", commontypes.FontStyle_HEADLINE_L),
		SelectedBannerBorderColor: "#00B899",
		IndexOfDefaultSelected:    -1,
		CtaBanner: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				provider.GetText("Fi is trusted by 35+ Lakh Users ", "#007A56", commontypes.FontStyle_BUTTON_XS),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#DCF3EE",
				LeftPadding:   20,
				RightPadding:  20,
				TopPadding:    8,
				BottomPadding: 8,
			},
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.GreenPeopleIcon, 14, 14),
		},
		ChooseOfferCta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         "Choose offer",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		},
		DefaultCta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         "Choose offer",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_DISABLED,
		},
	}

	var isNonHardLoanOfferAvailable bool
	for _, offer := range loanOffers {
		if offer.GetLoanOfferType() != preapprovedloan.LoanOfferType_LOAN_OFFER_TYPE_HARD {
			isNonHardLoanOfferAvailable = true
			break
		}
	}
	if isNonHardLoanOfferAvailable {
		screenOptionsMultiOfferScreen.FooterSecondaryLabel = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				provider.GetText("Offer may get updated based on your details", "#929599", commontypes.FontStyle_OVERLINE_2XS_CAPS),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor: "#F6F9FD",
			},
		}
	}

	for idx, offer := range loanOffers {
		isFirstOffer := idx == 0
		dlProvider, err := adp.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinks.GetDeeplinkProviderRequest{
			Vendor:      helper.GetPalFeVendorFromBe(offer.GetVendor()),
			LoanProgram: helper.GetFeLoanProgramFromBe(offer.GetLoanProgram()),
		})
		if err != nil {
			return nil, errors.Wrap(err, "error while getting deeplink provider for offer id: "+offer.GetId())
		}
		offerCard, err := adp.getLoanOfferDetailsCard(ctx, offer, isFirstOffer, dlProvider, isLoansPreQualOfferFlowEnabled)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting loan offer details card for offer id: "+offer.GetId())
		}

		// redirect to the application status screen if user selects any offer for which there is already an active application
		if currentLr != nil && currentLr.GetCompletedAt() == nil && currentLr.GetVendor() == offer.GetVendor() && currentLr.GetLoanProgram() == offer.GetLoanProgram() {
			applStatusDl, dlErr := adp.GetLoansApplicationStatusPollDeeplink(ctx, helper.GetFeLoanHeaderByBeLoanHeader(&preapprovedloan.LoanHeader{
				LoanProgram: currentLr.GetLoanProgram(),
				Vendor:      currentLr.GetVendor(),
			}), "", currentLr.GetId(), nil)
			if dlErr != nil {
				return nil, errors.Wrap(dlErr, "error while getting appl status dl")
			}
			offerCard.LoansCta = &palTypesPb.LoansCta{
				CtaContent: offerCard.GetLoansCta().GetCtaContent(),
				CtaAction: &palTypesPb.LoansCtaAction{
					Action: &palTypesPb.LoansCtaAction_Deeplink{Deeplink: applStatusDl},
				},
			}
		}

		screenOptionsMultiOfferScreen.OfferDetailsCards = append(screenOptionsMultiOfferScreen.GetOfferDetailsCards(), offerCard)
	}

	loDeepLink := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_MULTIPLE_OFFER_SELECTION_SCREEN, screenOptionsMultiOfferScreen)

	return loDeepLink, nil
}

func (adp *AggregatedDeeplinkProvider) getLoanOfferDetailsCard(ctx context.Context, loanOffer *preapprovedloan.LoanOffer, isFirstOffer bool,
	deeplinkProvider provider.IDeeplinkProvider, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoanOfferDetailsCard, error) {
	switch loanOffer.GetLoanOfferType() {
	case preapprovedloan.LoanOfferType_LOAN_OFFER_TYPE_PRE_QUALIFIED:
		return deeplinkProvider.GetPreQualOfferCardMultiOfferScreenComponent(loanOffer, isFirstOffer, isLoansPreQualOfferFlowEnabled)
	case preapprovedloan.LoanOfferType_LOAN_OFFER_TYPE_SOFT:
		return deeplinkProvider.GetSoftOfferCardMultiOfferScreenComponent(ctx, loanOffer, isFirstOffer, isLoansPreQualOfferFlowEnabled)
	case preapprovedloan.LoanOfferType_LOAN_OFFER_TYPE_HARD:
		return deeplinkProvider.GetHardOfferCardMultiOfferScreenComponent(loanOffer, isFirstOffer, isLoansPreQualOfferFlowEnabled)

	default:
		return nil, errors.New("unknown loan offer type")
	}
}
