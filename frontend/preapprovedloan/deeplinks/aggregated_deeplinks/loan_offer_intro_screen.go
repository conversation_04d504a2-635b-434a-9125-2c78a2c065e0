// nolint:dupl
package aggregated_deeplinks

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/pkg/errors"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks"
	"github.com/epifi/gamma/pkg/feature/release"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	uiFrontend "github.com/epifi/gamma/frontend/preapprovedloan/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

func (adp *AggregatedDeeplinkProvider) GetLoanOfferIntroScreen(ctx context.Context, loanOffer *palPb.LoanOffer, req *provider.LandingInfoRequest) (*deeplinkPb.Deeplink, error) {
	screenOptionsLoanOfferScreen := &palTypesPb.LoansOfferScreenOptions{
		LoanHeader: &palPbFeEnums.LoanHeader{
			EventData: &palPbFeEnums.EventData{
				// Adding this for analytics purposes on client side
				ComponentIdentifier: loanOffer.GetLoanOfferType().String(),
			},
		},
		ToolbarRightCta: ui.NewITC().WithLeftImageUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/grey_question_info_icon.png", 16, 16).WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Get help", "#6A6D70", commontypes.FontStyle_HEADLINE_M)).WithDeeplink(&deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_HELP_MAIN,
		}).WithContainerPadding(4, 17, 4, 8).WithContainerBackgroundColor("#FFFFFF").WithContainerCornerRadius(16),
		Components: []*palTypesPb.LoansScreenUiComponents{
			{
				Component: &palTypesPb.LoansScreenUiComponents_FaqsScrollableCardsViewComponent{
					FaqsScrollableCardsViewComponent: &palTypesPb.FaqsScrollableCardsViewComponent{
						TopMargin:      48,
						ComponentTitle: provider.GetText("Popular Questions", "#313234", commontypes.FontStyle_HEADLINE_S),
						Faqs: []*deeplinkPb.InfoItemV3{
							{
								BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
								Title:   provider.GetText("Will I need any paperwork?", "#313234", commontypes.FontStyle_HEADLINE_S),
								Desc:    provider.GetText("You can get your loan without any \npaperwork required.", "#929599", commontypes.FontStyle_BODY_XS),
							},
							{
								BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
								Title:   provider.GetText("Will there be any charges if \nI close my loan early?", "#313234", commontypes.FontStyle_HEADLINE_S),
								Desc:    provider.GetText("Fi has a zero preclosure charge \npolicy. You can close the loan any \ntime no matter how long the tenure \nyou choose", "#929599", commontypes.FontStyle_BODY_XS),
							},
						},
					},
				},
			},
		},
		CtaBanner: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				provider.GetText("Fi is trusted by 35+ Lakh Users ", "#007A56", commontypes.FontStyle_BUTTON_XS),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#DCF3EE",
				LeftPadding:   20,
				RightPadding:  20,
				TopPadding:    8,
				BottomPadding: 8,
			},
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.GreenPeopleIcon, 14, 14),
		},
	}

	loansPreQualOfferRelConst := release.NewCommonConstraintData(typesv2.Feature_FEATURE_LOANS_PREQUAL_OFFER_FLOW).WithActorId(loanOffer.GetActorId())
	isLoansPreQualOfferFlowEnabled, err := adp.releaseEvaluator.Evaluate(ctx, loansPreQualOfferRelConst)
	if err != nil {
		return nil, errors.Wrap(err, "failed to evaluate feature")
	}

	dpMultiOffer, numOffers, errMultiOffer := adp.GetMultiOfferScreenWithSupportedOffers(ctx, req.GetLoanOffers(), nil)
	if errMultiOffer != nil {
		return nil, errors.Wrap(errMultiOffer, "failed to get multi offer screen deeplink")
	}
	if dpMultiOffer != nil && numOffers > 1 {
		seeMoreOffersComponent := getSeeMoreOffersComponent(numOffers, dpMultiOffer)
		screenOptionsLoanOfferScreen.Components = append([]*palTypesPb.LoansScreenUiComponents{seeMoreOffersComponent}, screenOptionsLoanOfferScreen.GetComponents()...)
	}

	dlProvider, err := adp.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinks.GetDeeplinkProviderRequest{
		Vendor:      helper.GetPalFeVendorFromBe(loanOffer.GetVendor()),
		LoanProgram: helper.GetFeLoanProgramFromBe(loanOffer.GetLoanProgram()),
	})
	if err != nil {
		return nil, errors.Wrap(err, "error while getting deeplink provider in base")
	}

	var errUpdateScreen error
	switch loanOffer.GetLoanOfferType() {
	case palPb.LoanOfferType_LOAN_OFFER_TYPE_PRE_QUALIFIED:
		screenOptionsLoanOfferScreen, errUpdateScreen = adp.UpdateDeepLinkForPreQualLOScreen(ctx, dlProvider, loanOffer, screenOptionsLoanOfferScreen, isLoansPreQualOfferFlowEnabled)
	case palPb.LoanOfferType_LOAN_OFFER_TYPE_HARD:
		screenOptionsLoanOfferScreen, errUpdateScreen = adp.UpdateDeepLinkForHardLOScreen(ctx, dlProvider, loanOffer, screenOptionsLoanOfferScreen, isLoansPreQualOfferFlowEnabled)
	default:
		screenOptionsLoanOfferScreen, errUpdateScreen = adp.UpdateDeepLinkForSoftLOScreen(ctx, dlProvider, loanOffer, screenOptionsLoanOfferScreen, isLoansPreQualOfferFlowEnabled)
	}
	if errUpdateScreen != nil {
		return nil, errors.Wrap(errUpdateScreen, "failed to update screen for loan offers screen")
	}

	loDeepLink := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_OFFER_INTRO_SCREEN, screenOptionsLoanOfferScreen)

	return loDeepLink, nil
}

func (adp *AggregatedDeeplinkProvider) UpdateDeepLinkForPreQualLOScreen(ctx context.Context, dlProvider provider.IDeeplinkProvider, loanOffer *palPb.LoanOffer,
	screenOptions *palTypesPb.LoansOfferScreenOptions, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoansOfferScreenOptions, error) {
	screenOptions.PrimaryCta = adp.getPrimaryCta("Get approval", loanOffer)

	loanCta, err := dlProvider.GetPreQualOfferLandingScreenLoansCta(loanOffer)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting loan cta")
	}
	if isLoansPreQualOfferFlowEnabled {
		screenOptions.PrimaryLoansCta = loanCta
	}
	moneyInString := money.ToDisplayStringInIndianFormat(loanOffer.GetOfferConstraints().GetMaxLoanAmount(), 0, false)
	screenOptions.ScreenBgColor = widget.GetBlockBackgroundColour("#E6E9ED")
	screenOptions.Components = append([]*palTypesPb.LoansScreenUiComponents{
		{
			Component: &palTypesPb.LoansScreenUiComponents_LoanOfferCardComponent{
				LoanOfferCardComponent: &palTypesPb.LoanOfferCardComponent{
					CardBgColor: "#FFFFFF",
					OfferDetailsCard: &palTypesPb.LoanOfferCardComponent_OfferDetailsCard{
						OfferTitleV2: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								provider.GetText("Check your loan eligibility", "#313234", commontypes.FontStyle_HEADLINE_2),
							},
						},
						OfferAmountDetails: &palTypesPb.LoanOfferCardComponent_OfferAmountDetails{
							OfferSubtitle: provider.GetText("You may be qualified for up to:", "#6A6D70", commontypes.FontStyle_HEADLINE_S),
							OfferAmount: &ui.IconTextComponent{
								Texts: []*commontypes.Text{
									provider.GetText("₹", "#8D8D8D", commontypes.FontStyle_CURRENCY_XL),
									provider.GetText(moneyInString, "#313234", commontypes.FontStyle_NUMBER_3XL),
								},
							},
						},
						OfferDescriptionChips: []*ui.IconTextComponent{
							{
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.HighVoltageIcon, 20, 20),
								Texts: []*commontypes.Text{
									provider.GetText("Get approved within minutes", "#007A56", commontypes.FontStyle_SUBTITLE_S),
								},
								LeftImgTxtPadding: 4,
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									LeftPadding:   8,
									RightPadding:  8,
									TopPadding:    9,
									BottomPadding: 9,
									CornerRadius:  16,
									BgColor:       "#DCF3EE",
								},
							},
						},
					},
					OfferProgressStageCard: &palTypesPb.LoanOfferCardComponent_OfferProgressCard{
						BgColor: "#EFF2F6",
						StageProgress: &palTypesPb.SectionTypeProgress{
							ProgressComponents: []*palTypesPb.SectionTypeProgress_SectionTypeProgressComponent{
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.MoneyBagIcon, 27, 27),
									Text:               provider.GetText("Get approved\nby partner", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#BCDCE7",
								},
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.ConfirmDetailsIcon, 27, 27),
									Text:               provider.GetText("Verify\ndetails", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#BCDCE7",
								},
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.GetMoneyIcon, 27, 27),
									Text:               provider.GetText("Get\nmoney ", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#BCDCE7",
								},
							},
						},
						PartnerLogo: uiFrontend.GetOfferedByVendorIcon(loanOffer.GetVendor()),
					},
				},
			},
		},
	}, screenOptions.GetComponents()...)

	return screenOptions, nil
}

func (adp *AggregatedDeeplinkProvider) UpdateDeepLinkForSoftLOScreen(ctx context.Context, dlProvider provider.IDeeplinkProvider, loanOffer *palPb.LoanOffer, screenOptions *palTypesPb.LoansOfferScreenOptions, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoansOfferScreenOptions, error) {
	loanCard, err := dlProvider.GetSoftOfferCardMultiOfferScreenComponent(ctx, loanOffer, true, isLoansPreQualOfferFlowEnabled)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting loan card")
	}
	if isLoansPreQualOfferFlowEnabled {
		screenOptions.PrimaryLoansCta = loanCard.GetLoansCta()
	}
	screenOptions.PrimaryCta = adp.getPrimaryCta("Continue", loanOffer)

	screenOptions.ScreenBgColor = widget.GetBlockBackgroundColour("#E6E9ED")
	screenOptions.BackgroundVisualElement = commontypes.GetVisualElementLottieFromUrl(uiFrontend.MoneyFallingLottie).WithRepeatCount(-1)

	moneyInString := money.ToDisplayStringInIndianFormat(loanOffer.GetOfferConstraints().GetMaxLoanAmount(), 0, false)

	screenOptions.Components = append([]*palTypesPb.LoansScreenUiComponents{
		{
			Component: &palTypesPb.LoansScreenUiComponents_LoanOfferCardComponent{
				LoanOfferCardComponent: &palTypesPb.LoanOfferCardComponent{
					CardBgColor: "#EFF2F6",
					CardTitle:   provider.GetText("You have an offer:", "#313234", commontypes.FontStyle_HEADLINE_2),
					OfferDetailsCard: &palTypesPb.LoanOfferCardComponent_OfferDetailsCard{
						OfferAmountDetails: &palTypesPb.LoanOfferCardComponent_OfferAmountDetails{
							OfferSubtitle: provider.GetText("You can borrow upto:", "#6A6D70", commontypes.FontStyle_HEADLINE_S),
							OfferAmount: &ui.IconTextComponent{
								Texts: []*commontypes.Text{
									provider.GetText("₹", "#8D8D8D", commontypes.FontStyle_CURRENCY_XL),
									provider.GetText(moneyInString, "#313234", commontypes.FontStyle_NUMBER_3XL),
								},
							},
						},
						OfferDescriptionChips: []*ui.IconTextComponent{
							{
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.HighVoltageIcon, 20, 20),
								Texts: []*commontypes.Text{
									provider.GetText("Takes less than 2 mins", "#007A56", commontypes.FontStyle_SUBTITLE_S),
								},
								LeftImgTxtPadding: 4,
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									LeftPadding:   8,
									RightPadding:  8,
									TopPadding:    9,
									BottomPadding: 9,
									CornerRadius:  16,
									BgColor:       "#DCF3EE",
								},
							},
						},
					},
					OfferProgressStageCard: &palTypesPb.LoanOfferCardComponent_OfferProgressCard{
						BgColor: "#EFF2F6",
						StageProgress: &palTypesPb.SectionTypeProgress{
							ProgressComponents: []*palTypesPb.SectionTypeProgress_SectionTypeProgressComponent{
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.MoneyBagIcon, 27, 27),
									Text:               provider.GetText("Choose\namount", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#BCDCE7",
								},
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.ConfirmDetailsIcon, 27, 27),
									Text:               provider.GetText("Confirm\ndetails", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#BCDCE7",
								},
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.GetMoneyIcon, 27, 27),
									Text:               provider.GetText("Get\nmoney ", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#BCDCE7",
								},
							},
						},
						PartnerLogo: uiFrontend.GetPoweredByVendorIcon(loanOffer.GetVendor()),
					},
				},
			},
		},
	}, screenOptions.GetComponents()...)

	return screenOptions, nil
}

func (adp *AggregatedDeeplinkProvider) UpdateDeepLinkForHardLOScreen(_ context.Context, _ provider.IDeeplinkProvider, loanOffer *palPb.LoanOffer, screenOptions *palTypesPb.LoansOfferScreenOptions, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoansOfferScreenOptions, error) {
	screenOptions.PrimaryCta = adp.getPrimaryCta("Continue", loanOffer)
	if isLoansPreQualOfferFlowEnabled {
		screenOptions.PrimaryLoansCta = &palTypesPb.LoansCta{
			CtaContent: adp.getPrimaryCta("Continue", loanOffer),
			CtaAction: &palTypesPb.LoansCtaAction{
				Action: &palTypesPb.LoansCtaAction_CallRpc_{CallRpc: &palTypesPb.LoansCtaAction_CallRpc{
					RpcName: palTypesPb.LoansCtaAction_RPC_NAME_OFFER_DETAILS,
					CommonMetaData: &palTypesPb.LoansCtaAction_CallRpc_CommonMetaData{
						LoanHeader: &palPbFeEnums.LoanHeader{
							LoanProgram: helper.GetFeLoanProgramFromBe(loanOffer.GetLoanProgram()),
							Vendor:      helper.GetPalFeVendorFromBe(loanOffer.GetVendor()),
						},
						LoId: loanOffer.GetId(),
					},
				}},
			},
		}
	}
	vendorIcon, vendorIconWidth, vendorIconHeight, _ := uiFrontend.GetVendorImageAndText(loanOffer.GetVendor())
	screenOptions.ScreenBgColor = widget.GetBlockBackgroundColour("#E6E9ED")
	screenOptions.BackgroundVisualElement = commontypes.GetVisualElementLottieFromUrl(uiFrontend.MoneyFallingLottie).WithRepeatCount(-1)

	moneyInString := money.ToDisplayStringInIndianFormat(loanOffer.GetOfferConstraints().GetMaxLoanAmount(), 0, false)
	tenureString := strconv.Itoa(int(loanOffer.GetOfferConstraints().GetMinTenureMonths())) + "-" + strconv.Itoa(int(loanOffer.GetOfferConstraints().GetMaxTenureMonths())) + " months"
	interestRatePercentageValue := loanOffer.GetProcessingInfo().GetInterestRate()[0].GetPercentage()
	if interestRatePercentageValue == 0 {
		return nil, errors.New("interest rate percentage is zero")
	}
	if loanOffer.GetVendor() == palPb.Vendor_LENDEN {
		// LDC provides interest rate in monthly terms, but we need to show it in yearly terms for compliance reasons
		interestRatePercentageValue = math.Round(loanOffer.GetProcessingInfo().GetInterestRate()[0].GetPercentage()*12*100) / 100
	}
	screenOptions.Components = append([]*palTypesPb.LoansScreenUiComponents{
		{
			Component: &palTypesPb.LoansScreenUiComponents_LoanOfferCardComponent{
				LoanOfferCardComponent: &palTypesPb.LoanOfferCardComponent{
					OfferDetailsCard: &palTypesPb.LoanOfferCardComponent_OfferDetailsCard{
						BgColor: "#F6F9FD",
						OfferTitleV2: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								provider.GetText("Congratulations! 🎉", "#313234", commontypes.FontStyle_HEADLINE_2),
							},
						},
						OfferAmountDetails: &palTypesPb.LoanOfferCardComponent_OfferAmountDetails{
							PartnerLogo: &ui.IconTextComponent{
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(vendorIcon, vendorIconHeight, vendorIconWidth),
							},
							OfferSubtitle: provider.GetText("has approved you for", "#6A6D70", commontypes.FontStyle_HEADLINE_S),
							OfferAmount: &ui.IconTextComponent{
								Texts: []*commontypes.Text{
									provider.GetText("₹", "#8D8D8D", commontypes.FontStyle_CURRENCY_XL),
									provider.GetText(moneyInString, "#313234", commontypes.FontStyle_NUMBER_3XL),
								},
							},
						},
						OfferDescriptionChips: []*ui.IconTextComponent{
							{
								Texts: []*commontypes.Text{
									provider.GetText(fmt.Sprintf("@ %.1f%% p.a.", interestRatePercentageValue), "#98712F", commontypes.FontStyle_SUBTITLE_S),
								},
								LeftImgTxtPadding: 4,
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									LeftPadding:   8,
									RightPadding:  8,
									TopPadding:    9,
									BottomPadding: 9,
									CornerRadius:  15,
									BgColor:       "#FFFCEB",
								},
							},
							{
								Texts: []*commontypes.Text{
									provider.GetText(fmt.Sprintf("Tenure of %s", tenureString), "#98712F", commontypes.FontStyle_SUBTITLE_S),
								},
								LeftImgTxtPadding: 4,
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									LeftPadding:   8,
									RightPadding:  8,
									TopPadding:    9,
									BottomPadding: 9,
									CornerRadius:  15,
									BgColor:       "#FFFCEB",
								},
							},
						},
					},
					OfferProgressStageCard: &palTypesPb.LoanOfferCardComponent_OfferProgressCard{
						BgColor: "#FFFFFF",
						StageProgress: &palTypesPb.SectionTypeProgress{
							ProgressComponents: []*palTypesPb.SectionTypeProgress_SectionTypeProgressComponent{
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.GreenRightIcon, 27, 27),
									Text:               provider.GetText("Approved by\npartner", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#86BA6F",
								},
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.ConfirmDetailsIcon, 27, 27),
									Text:               provider.GetText("Verify\ndetails", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#BCDCE7",
								},
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.GetMoneyIcon, 27, 27),
									Text:               provider.GetText("Get\nmoney ", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#BCDCE7",
								},
							},
						},
					},
				},
			},
		},
	}, screenOptions.GetComponents()...)

	return screenOptions, nil
}

func (adp *AggregatedDeeplinkProvider) getPrimaryCta(ctaText string, loanOffer *palPb.LoanOffer) *deeplinkPb.Cta {
	var nextActionCta *deeplinkPb.Deeplink
	if loanOffer.GetLoanOfferType() != palPb.LoanOfferType_LOAN_OFFER_TYPE_PRE_QUALIFIED {
		nextActionCta = deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_OFFER_DETAILS_SCREEN, &palTypesPb.LoansOfferDetailsScreenOptions{
			LoanHeader: &palPbFeEnums.LoanHeader{
				LoanProgram: helper.GetFeLoanProgramFromBe(loanOffer.GetLoanProgram()),
				Vendor:      helper.GetPalFeVendorFromBe(loanOffer.GetVendor()),
			},
			OfferId: loanOffer.GetId(),
		})
	}

	return &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CUSTOM,
		Text:         ctaText,
		Deeplink:     nextActionCta,
		Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
	}
}

func getSeeMoreOffersComponent(loanOffers int, deeplinkMultiOfferScreen *deeplinkPb.Deeplink) *palTypesPb.LoansScreenUiComponents {
	var offerText string
	if loanOffers-1 > 1 {
		offerText = fmt.Sprintf("See %d other offers", loanOffers-1)
	} else {
		offerText = fmt.Sprintf("See %d other offer", loanOffers-1)
	}

	return &palTypesPb.LoansScreenUiComponents{
		Component: &palTypesPb.LoansScreenUiComponents_ViewMoreOfferCardComponent{
			ViewMoreOfferCardComponent: &palTypesPb.ViewMoreOfferCardComponent{
				BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
				OfferDescription: &ui.VerticalKeyValuePair{
					Title: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							provider.GetText(offerText, "#313234", commontypes.FontStyle_HEADLINE_S),
						},
						LeftImgTxtPadding: 12,
						LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.PillarBuildingMoreOffers, 52, 71),
						Deeplink:          deeplinkMultiOfferScreen,
					},
				},
				ActionIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.ChevronIcon, 32, 32),
			},
		},
	}
}

func (adp *AggregatedDeeplinkProvider) isOfferDetailsV4FlowEnabled(ctx context.Context, lh *palPbFeEnums.LoanHeader) (bool, error) {
	conf := adp.preApprovedLoanConf.OfferDetailsV4Config()
	if !conf.IsEnabled(ctx) {
		return false, nil
	}
	appVersionConstraintData := release.NewAppVersionConstraintData(adp.preApprovedLoanConf.OfferDetailsV4Config().AppVersionConstraintConfig())
	isAppVersionGreater, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
	if appVerErr != nil {
		return false, errors.Wrap(appVerErr, "failed to evaluate app version constraint for overall multi offer screen")
	}
	if !isAppVersionGreater {
		return false, nil
	}

	if lhConf := conf.VendorLoanProgramMap().Get(strings.Join([]string{
		lh.GetVendor().String(),
		lh.GetLoanProgram().String(),
	}, ":")); lhConf != nil && lhConf.IsEnabled(ctx) {
		if lhConf.AppVersionConstraintConfig() != nil {
			appVersionConstraintData = release.NewAppVersionConstraintData(lhConf.AppVersionConstraintConfig())
			isAppVersionGreater, appVerErr = release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
			if appVerErr != nil {
				return false, errors.Wrap(appVerErr, fmt.Sprintln("failed to evaluate app version constraint for multi offer screen for vendor: ", lh.GetVendor().String(), ", program: ", lh.GetLoanProgram().String()))
			}
			if !isAppVersionGreater {
				return false, nil
			}
		}

		return true, nil
	}
	return false, nil
}

func (adp *AggregatedDeeplinkProvider) GetMultiOfferScreenWithSupportedOffers(ctx context.Context, allOffers []*palPb.LoanOffer, currentLr *palPb.LoanRequest) (*deeplinkPb.Deeplink, int, error) {
	var filteredOffers []*palPb.LoanOffer
	for _, offer := range allOffers {
		isEnabled, err := adp.isOfferDetailsV4FlowEnabled(ctx, helper.GetFeLoanHeaderByBeLoanHeader(&palPb.LoanHeader{
			LoanProgram: offer.GetLoanProgram(),
			Vendor:      offer.GetVendor(),
		}))
		if err != nil {
			return nil, 0, errors.Wrap(err, fmt.Sprintf("failed to evaluate if multi offer screen is live for particular loan header: %s:%s", offer.GetVendor(), offer.GetLoanProgram()))
		}
		if isEnabled {
			filteredOffers = append(filteredOffers, offer)
		}
	}
	if len(filteredOffers) == 0 {
		return nil, 0, nil
	}

	loansPreQualOfferRelConst := release.NewCommonConstraintData(typesv2.Feature_FEATURE_LOANS_PREQUAL_OFFER_FLOW).WithActorId(filteredOffers[0].GetActorId())
	isLoansPreQualOfferFlowEnabled, err := adp.releaseEvaluator.Evaluate(ctx, loansPreQualOfferRelConst)
	if err != nil {
		return nil, 0, errors.Wrap(err, "failed to evaluate feature")
	}
	dl, err := adp.GetMultiOfferScreen(ctx, filteredOffers, isLoansPreQualOfferFlowEnabled, currentLr)
	if err != nil {
		return nil, 0, errors.Wrap(err, "failed to get multi offer screen")
	}
	return dl, len(filteredOffers), nil
}
