package aggregated_deeplinks

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	uiFrontend "github.com/epifi/gamma/frontend/preapprovedloan/ui"
	"github.com/epifi/gamma/pkg/feature/release"

	"context"
	"fmt"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	money2 "github.com/epifi/be-common/pkg/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

func GetLoanDashboardScreenV3(lh *palFeEnumsPb.LoanHeader) (*deeplinkPb.Deeplink, error) {
	dl, dlErr := deeplinkV3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_DASHBOARD_SCREEN, &palTypesPb.LoansDashboardScreenOptions{
		LoanHeader: &palFeEnumsPb.LoanHeader{
			LoanProgram: lh.GetLoanProgram(),
		},
	})
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "unable to create loans dashboard deeplinkV3")
	}
	return dl, nil
}

func (adp *AggregatedDeeplinkProvider) GetLoanDashboardScreenV3(lh *palFeEnumsPb.LoanHeader) (*deeplinkPb.Deeplink, error) {
	dl, dlErr := deeplinkV3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_DASHBOARD_SCREEN, &palTypesPb.LoansDashboardScreenOptions{
		LoanHeader: &palFeEnumsPb.LoanHeader{
			LoanProgram: lh.GetLoanProgram(),
		},
	})
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "unable to create loans dashboard deeplinkV3")
	}
	return dl, nil
}

// nolint:funlen
func (adp *AggregatedDeeplinkProvider) GetLoanDashboardDeepLinkV3(ctx context.Context, res *palBePb.GetDashboardResponse) (*deeplinkPb.Deeplink, error) {
	screenOptions := &palTypesPb.LoansDashboardScreenOptions{
		PageTitle:     provider.GetText("Your loans", "#F6F9FD", commontypes.FontStyle_HEADLINE_M),
		PageSubtitle:  nil,
		TopSection:    &palTypesPb.LoanDashboardTopSection{},
		BottomSection: &palTypesPb.LoanDashboardBottomSection{Divisions: []*palTypesPb.LoanDashboardBottomSection_SectionDivision{}},
	}

	if len(res.GetLoanInfoList()) > 0 && res.GetLoanInfoList()[0].GetLoanAccount().GetStatus() == palBePb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED {
		screenOptions = adp.getScreenOptionsForClosedLoan()
		return deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_DASHBOARD_SCREEN, screenOptions), nil
	}

	var applicationDetailsResp *provider.GetDashboardLoanApplicationDetailsResponse
	if res.GetRecentLoanRequest() != nil {
		loanApplicationDlProvider, _ := adp.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinks.GetDeeplinkProviderRequest{
			Vendor:      helper.GetPalFeVendorFromBe(res.GetRecentLoanRequest().GetVendor()),
			LoanProgram: helper.GetFeLoanProgramFromBe(res.GetRecentLoanRequest().GetLoanProgram()),
		})
		var appDetailsErr error
		applicationDetailsResp, appDetailsErr = loanApplicationDlProvider.GetLoanApplicationDetailsForDashboard(ctx, loanApplicationDlProvider.GetLoanHeader(), res.GetRecentLoanRequest(), res.GetLoanSteps(), res.GetActiveLoanOffer())
		if appDetailsErr != nil {
			return nil, errors.Wrap(appDetailsErr, "unable to get loan account details for given loan info list")
		}
	}

	accountDetailsResp, laDetailsErr := adp.getLoanAccountsDetailsForLoanDashboard(ctx, res.GetLoanInfoList())
	if laDetailsErr != nil {
		return nil, errors.Wrap(laDetailsErr, "unable to get loan account details for given loan info list")
	}

	topSectionComponent, tscErr := adp.getLoanDashboardTopSectionComponent(accountDetailsResp, res.GetLoanSteps(), accountDetailsResp.getLoanProgram())
	if tscErr != nil {
		return nil, errors.Wrap(laDetailsErr, "unable to get top section component for dashboard")
	}

	screenOptions.GetTopSection().Divisions = []*palTypesPb.LoanDashboardTopSection_SectionDivision{
		{
			Components: []*palTypesPb.LoanDashboardTopSection_Component{
				topSectionComponent,
			},
		},
	}
	if accountDetailsResp.getDisbursalBanner() != nil {
		screenOptions.GetBottomSection().Divisions = append(screenOptions.GetBottomSection().GetDivisions(), accountDetailsResp.getDisbursalBanner())
	}
	if applicationDetailsResp.GetActiveApplicationCards() != nil && !res.GetRecentLoanRequest().IsFailedTerminal() {
		screenOptions.GetBottomSection().Divisions = append(screenOptions.GetBottomSection().GetDivisions(), applicationDetailsResp.GetActiveApplicationCards())
	}
	if res.GetActiveLoanOffer() != nil {
		exploreOffersDiv, err := adp.getExploreOffersDivision(ctx, res, applicationDetailsResp)
		if err != nil {
			return nil, errors.Wrap(err, "error in getExploreOffersDivision")
		}
		if exploreOffersDiv != nil {
			screenOptions.GetBottomSection().Divisions = append(screenOptions.GetBottomSection().GetDivisions(), exploreOffersDiv)
		}
	}
	if applicationDetailsResp.GetActiveApplicationCards() != nil && res.GetRecentLoanRequest().IsFailedTerminal() {
		screenOptions.GetBottomSection().Divisions = append(screenOptions.GetBottomSection().GetDivisions(), applicationDetailsResp.GetActiveApplicationCards())
	}
	if accountDetailsResp.getActiveLoanCards() != nil {
		screenOptions.GetBottomSection().Divisions = append(screenOptions.GetBottomSection().GetDivisions(), accountDetailsResp.getActiveLoanCards())
	}
	screenOptions.GetBottomSection().Divisions = append(screenOptions.GetBottomSection().GetDivisions(), &palTypesPb.LoanDashboardBottomSection_SectionDivision{
		Title: ui.NewITC().WithTexts(provider.GetText("Support", "#6A6D70", commontypes.FontStyle_HEADLINE_S)),
		Components: []*palTypesPb.LoanDashboardBottomSection_Component{
			// TODO: previous loans row
			getLoanDashboardFaqComponent(),
		},
	})

	// todo remove this this when we have loan offer banner in dashboard screen

	dl, dlErr := deeplinkV3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_DASHBOARD_SCREEN, screenOptions)
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "unable to create loans dashboard deeplinkV3")
	}
	return dl, nil
}

func (adp *AggregatedDeeplinkProvider) getExploreOffersDivision(ctx context.Context, res *palBePb.GetDashboardResponse, applicationDetailsResp *provider.GetDashboardLoanApplicationDetailsResponse) (*palTypesPb.LoanDashboardBottomSection_SectionDivision, error) {
	appVersionConstraintData := release.NewAppVersionConstraintData(adp.preApprovedLoanConf.AutoCancelCurrentLrConfig())
	isAppVersionCompatible, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
	if appVerErr != nil {
		return nil, errors.Wrap(appVerErr, "unable to evaluate app version constraint")
	}

	if !isAppVersionCompatible {
		loBanner, loBannerErr := adp.getLoanOfferBanner(ctx, res.GetActiveLoanOffer(),
			applicationDetailsResp.GetShowOfferBannerAccordingToLseDashboardMap() && res.GetCheckUserEligibility())
		if loBannerErr != nil {
			return nil, errors.Wrap(loBannerErr, "unable to get loan offer banner")
		}
		return loBanner, nil
	}

	multiOfferDl := deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_MULTIPLE_OFFER_SELECTION_SCREEN, &palTypesPb.LoansMultipleOfferSelectionScreenOptions{
		GetDataFromRpcCall: true,
	})
	return &palTypesPb.LoanDashboardBottomSection_SectionDivision{
		Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Start a new loan application", "#6A6D70", commontypes.FontStyle_HEADLINE_S)),
		Components: []*palTypesPb.LoanDashboardBottomSection_Component{
			{
				Component: &palTypesPb.LoanDashboardBottomSection_Component_BannerWithLeftIconAndRightCta{
					BannerWithLeftIconAndRightCta: &palTypesPb.BannerWithLeftIconAndRightCta{
						LeftIcon: provider.GetVisualElementPng(uiFrontend.PillarBuildingMoreOffers, 55, 40),
						Title:    ui.NewITC().WithTexts(provider.GetText("Compare & choose from other offers", "#313234", commontypes.FontStyle_SUBTITLE_XS)),
						RightCta: ui.NewITC().WithTexts(provider.GetText("Explore", "#00B899", commontypes.FontStyle_BUTTON_S)).WithDeeplink(multiOfferDl),
						BgColor:  "#FFFFFF",
					},
				},
			},
		},
	}, nil
}

func getLoanDashboardFaqComponent() *palTypesPb.LoanDashboardBottomSection_Component {
	return &palTypesPb.LoanDashboardBottomSection_Component{
		Component: &palTypesPb.LoanDashboardBottomSection_Component_SingleColumnLineItems{SingleColumnLineItems: &palTypesPb.SingleColumnLineItems{Items: []*palTypesPb.SingleColumnLineItems_SingleColumnLineItem{
			{
				Element: &widget.VisualElementTitleSubtitleElement{
					VisualElement:   provider.GetVisualElementPng("https://epifi-icons.pointz.in/loans/loan_dashboard_faq_icon.png", 32, 32),
					TitleText:       provider.GetText("FAQs", "#313234", commontypes.FontStyle_SUBTITLE_S),
					SubtitleText:    provider.GetText("Need help? Reach out to us", "#6A6D70", commontypes.FontStyle_BODY_XS),
					BackgroundColor: "#FFFFFF",
				},
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_HELP_MAIN,
				},
			},
		}}},
	}
}

func (adp *AggregatedDeeplinkProvider) getLoanAccountsDetailsForLoanDashboard(ctx context.Context, loanInfos []*palBePb.LoanInfo) (*getLoanAccountDetailsResponse, error) {
	if len(loanInfos) < 1 {
		return nil, nil
	}
	res := &getLoanAccountDetailsResponse{
		activeLoanCards: &palTypesPb.LoanDashboardBottomSection_SectionDivision{
			Title:      ui.NewITC().WithTexts(provider.GetText("Active loan", "#6A6D70", commontypes.FontStyle_HEADLINE_S)),
			Components: []*palTypesPb.LoanDashboardBottomSection_Component{},
		},
	}
	outstandingAmount := money2.ZeroINR().GetPb()
	totalPayableAmount := money2.ZeroINR().GetPb()

	for i, li := range loanInfos {
		loanDetailsDlProvider, _ := adp.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinks.GetDeeplinkProviderRequest{
			Vendor:      helper.GetPalFeVendorFromBe(li.GetLoanAccount().GetVendor()),
			LoanProgram: helper.GetFeLoanProgramFromBe(li.GetLoanAccount().GetLoanProgram()),
		})
		res.vendor = helper.GetPalFeVendorFromBe(li.GetLoanAccount().GetVendor())
		res.loanProgram = helper.GetFeLoanProgramFromBe(li.GetLoanAccount().GetLoanProgram())
		accountDetailsResp, accDetailsErr := loanDetailsDlProvider.GetLoanAccountDetailsForDashboard(ctx, loanDetailsDlProvider.GetLoanHeader(), li)
		if accDetailsErr != nil {
			return nil, errors.Wrap(accDetailsErr, fmt.Sprintf("unable to get loan account details for given loan info list, index: %d", i))
		}
		if accountDetailsResp == nil {
			continue
		}
		if accountDetailsResp.GetActiveLoanCard().GetCardWithLineProgress().GetCtaBottomRow() != nil {
			res.overdueState = true
		}
		res.getActiveLoanCards().Components = append(res.getActiveLoanCards().GetComponents(), accountDetailsResp.GetActiveLoanCard())
		res.disbursalBanner = accountDetailsResp.GetDisbursalBanner()
		var err error
		// TODO: to check for moneyview flow, since for moneyview this info might be invalid
		outstandingAmount, err = money2.Sum(outstandingAmount, li.GetLoanAccount().GetLoanAmountInfo().GetOutstandingAmount())
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("unable to sum outstanding amount for dashboard, index: %d", i))
		}
		// TODO: to check for moneyview flow, since for moneyview this info might be invalid
		totalPayableAmount, err = money2.Sum(totalPayableAmount, li.GetLoanAccount().GetLoanAmountInfo().GetTotalPayableAmount())
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("unable to sum total payable amount for dashboard, index: %d", i))
		}
	}
	if len(res.getActiveLoanCards().GetComponents()) == 0 && res.getDisbursalBanner() == nil {
		res.activeLoanCards = nil
	}
	res.outstandingAmount = types.GetFromBeMoney(outstandingAmount)
	res.totalLoanAmount = types.GetFromBeMoney(totalPayableAmount)
	return res, nil
}

type getLoanAccountDetailsResponse struct {
	activeLoanCards   *palTypesPb.LoanDashboardBottomSection_SectionDivision
	outstandingAmount *types.Money
	totalLoanAmount   *types.Money
	disbursalBanner   *palTypesPb.LoanDashboardBottomSection_SectionDivision
	overdueState      bool
	vendor            palFeEnumsPb.Vendor
	loanProgram       palFeEnumsPb.LoanProgram
}

func (p *getLoanAccountDetailsResponse) getActiveLoanCards() *palTypesPb.LoanDashboardBottomSection_SectionDivision {
	if p != nil {
		return p.activeLoanCards
	}
	return nil
}

func (p *getLoanAccountDetailsResponse) getDisbursalBanner() *palTypesPb.LoanDashboardBottomSection_SectionDivision {
	if p != nil {
		return p.disbursalBanner
	}
	return nil
}

func (p *getLoanAccountDetailsResponse) getTotalLoanAmount() *types.Money {
	if p != nil {
		return p.totalLoanAmount
	}
	return nil
}

func (p *getLoanAccountDetailsResponse) getOutstandingAmount() *types.Money {
	if p != nil {
		return p.outstandingAmount
	}
	return nil
}

func (p *getLoanAccountDetailsResponse) getVendor() palFeEnumsPb.Vendor {
	if p != nil {
		return p.vendor
	}
	return palFeEnumsPb.Vendor_VENDOR_UNSPECIFIED
}

func (p *getLoanAccountDetailsResponse) getLoanProgram() palFeEnumsPb.LoanProgram {
	if p != nil {
		return p.loanProgram
	}
	return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

// nolint: funlen
func (adp *AggregatedDeeplinkProvider) getLoanDashboardTopSectionComponent(accountRes *getLoanAccountDetailsResponse, lses []*palBePb.LoanStepExecution, loanProgram palFeEnumsPb.LoanProgram) (*palTypesPb.LoanDashboardTopSection_Component, error) {
	// Todo(Anupam): Ticket ID(93035)
	if accountRes.getVendor() == palFeEnumsPb.Vendor_MONEYVIEW {
		return &palTypesPb.LoanDashboardTopSection_Component{
			Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
				VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/golden_clock_pld_dashboard.png", 40, 40),
				VisualisationValue: ui.NewITC().WithTexts(provider.GetText("Loan details available on\nMoneyview", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)),
			}},
		}, nil
	} else if accountRes.getVendor() == palFeEnumsPb.Vendor_ABFL && loanProgram == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION {
		progressBarColor1 := "#E3E5E3"
		progressBarColor2 := "#525252"
		return &palTypesPb.LoanDashboardTopSection_Component{
			Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
				ProgressVisualisation: &palTypesPb.ProgressVisualisation{
					CurrentProgress:  2,
					MaxProgress:      100,
					TrackWidth:       20,
					ProgressBarWidth: 20,
					TrackColor:       &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#313234"}},
					ProgressBarColor: &widget.BackgroundColour{Colour: &widget.BackgroundColour_LinearGradient{LinearGradient: &widget.LinearGradient{
						Degree: 80,
						LinearColorStops: []*widget.ColorStop{
							{
								Color:          progressBarColor1,
								StopPercentage: 60,
							},
							{
								Color:          progressBarColor2,
								StopPercentage: 100,
							},
						},
					}}},
					VisualisationType: palTypesPb.ProgressVisualisationType_PROGRESS_VISUALISATION_TYPE_SEMI_CIRCLE,
					Visualisation:     &palTypesPb.ProgressVisualisation_SemiCircleProgressVisualisation{SemiCircleProgressVisualisation: &palTypesPb.SemiCircleProgressVisualisation{}},
				},
				VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloan/orange-book-icon.png", 40, 40),
				VisualisationValue: ui.NewITC().WithTexts(provider.GetText("Loan details available on\nAditya Birla Capital app", "#FFFFFF", commontypes.FontStyle_SUBTITLE_S).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)),
			}},
		}, nil
	}

	// default component
	component := &palTypesPb.LoanDashboardTopSection_Component{
		Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
			VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/orange-clock-icon.png", 48, 48),
			VisualisationValue: ui.NewITC().WithTexts(provider.GetText("Loan application in progress", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
		}},
	}

	// check for disbursal lse, if yes changing top section accordingly
	if len(lses) > 0 {
		latestLse := lses[0]
		if latestLse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_AMOUNT_DISBURSAL ||
			latestLse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION ||
			// TODO(Brijesh): Should we add a vendor check too?
			latestLse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ROI_MODIFICATION {
			component.GetCardSemiCircularProgress().VisualisationTitle = ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/orange-clock-icon.png", 48, 48)
			component.GetCardSemiCircularProgress().VisualisationValue = ui.NewITC().WithTexts(provider.GetText("Loan disbursal in progress", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M))
			component.GetCardSemiCircularProgress().VisualisationTag = ui.NewITC().WithTexts(provider.GetText("ETA 24 hours", "#FFFFFF", commontypes.FontStyle_OVERLINE_2XS_CAPS)).
				WithContainerBackgroundColor("#D48647").WithContainerPadding(3, 10, 3, 10).WithContainerCornerRadius(10)
		} else {
			switch latestLse.GetStatus() {
			case palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:
				component = &palTypesPb.LoanDashboardTopSection_Component{
					Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
						VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/pal/dashboard/rejectionIcon.png", 48, 48),
						VisualisationValue: ui.NewITC().WithTexts(provider.GetText("Loan application failed", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
					}},
				}
			case palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED:
				component = &palTypesPb.LoanDashboardTopSection_Component{
					Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
						VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/pal/dashboard/cancelledIcon.png", 48, 48),
						VisualisationValue: ui.NewITC().WithTexts(provider.GetText("Loan application was cancelled", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
					}},
				}
			case palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:
				component = &palTypesPb.LoanDashboardTopSection_Component{
					Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
						VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/pal/dashboard/rejectionIcon.png", 48, 48),
						VisualisationValue: ui.NewITC().WithTexts(provider.GetText("Loan application expired", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
					}},
				}
			}
		}
	}

	// TODO: to check for moneyview flow, since for moneyview this info might be invalid
	if accountRes.getOutstandingAmount() != nil && accountRes.getTotalLoanAmount() != nil {
		outstandingAmountPaise, oaErr := money2.ToPaise(accountRes.getOutstandingAmount().GetBeMoney())
		if oaErr != nil {
			return nil, errors.Wrap(oaErr, "unable to convert outstanding amount to paise")
		}
		totalPayableAmountPaise, tpErr := money2.ToPaise(accountRes.getTotalLoanAmount().GetBeMoney())
		if tpErr != nil {
			return nil, errors.Wrap(tpErr, "unable to convert total payable amount to paise")
		}

		progressBarColor1 := "#37522A"
		progressBarColor2 := "#AFD2A2"
		if accountRes != nil && accountRes.overdueState {
			progressBarColor1 = "#721121"
			progressBarColor2 = "#F29CA2"
		}

		component = &palTypesPb.LoanDashboardTopSection_Component{
			Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
				ProgressVisualisation: &palTypesPb.ProgressVisualisation{
					CurrentProgress:  totalPayableAmountPaise - outstandingAmountPaise,
					MaxProgress:      totalPayableAmountPaise,
					TrackWidth:       20,
					ProgressBarWidth: 20,
					TrackColor:       &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#313234"}},
					ProgressBarColor: &widget.BackgroundColour{Colour: &widget.BackgroundColour_LinearGradient{LinearGradient: &widget.LinearGradient{
						Degree: 80,
						LinearColorStops: []*widget.ColorStop{
							{
								Color:          progressBarColor1,
								StopPercentage: 60,
							},
							{
								Color:          progressBarColor2,
								StopPercentage: 100,
							},
						},
					}}},
					VisualisationType: palTypesPb.ProgressVisualisationType_PROGRESS_VISUALISATION_TYPE_SEMI_CIRCLE,
					Visualisation:     &palTypesPb.ProgressVisualisation_SemiCircleProgressVisualisation{SemiCircleProgressVisualisation: &palTypesPb.SemiCircleProgressVisualisation{}},
				},
				VisualisationTitle: ui.NewITC().WithTexts(provider.GetText("Total outstanding", "#929599", commontypes.FontStyle_SUBTITLE_XS)),
				VisualisationValue: ui.NewITC().WithTexts(provider.GetText(money2.ToDisplayStringInIndianFormat(accountRes.outstandingAmount.GetBeMoney(), 0, true), "#FFFFFF", commontypes.FontStyle_NUMBER_2XL)),
			},
			},
		}
	}
	return component, nil
}

func (adp *AggregatedDeeplinkProvider) getScreenOptionsForClosedLoan() *palTypesPb.LoansDashboardScreenOptions {
	return &palTypesPb.LoansDashboardScreenOptions{
		PageTitle: provider.GetText("Your loans", "#F6F9FD", commontypes.FontStyle_HEADLINE_M),
		TopSection: &palTypesPb.LoanDashboardTopSection{
			Divisions: []*palTypesPb.LoanDashboardTopSection_SectionDivision{
				{
					Components: []*palTypesPb.LoanDashboardTopSection_Component{
						{
							Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
								VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/pl-circular-loan-bag.png", 48, 48),
								VisualisationValue: ui.NewITC().WithTexts(provider.GetText("No loan offer", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
							}},
						},
					}},
			},
		},
		BottomSection: &palTypesPb.LoanDashboardBottomSection{
			Divisions: []*palTypesPb.LoanDashboardBottomSection_SectionDivision{
				{
					Components: []*palTypesPb.LoanDashboardBottomSection_Component{
						{
							Component: &palTypesPb.LoanDashboardBottomSection_Component_SingleColumnLineItems{SingleColumnLineItems: &palTypesPb.SingleColumnLineItems{Items: []*palTypesPb.SingleColumnLineItems_SingleColumnLineItem{
								{
									Element: &widget.VisualElementTitleSubtitleElement{
										VisualElement:   provider.GetVisualElementPng("https://epifi-icons.pointz.in/loans/loan_dashboard_faq_icon.png", 32, 32),
										TitleText:       provider.GetText("FAQs", "#313234", commontypes.FontStyle_SUBTITLE_S),
										SubtitleText:    provider.GetText("Need help? Reach out to us", "#6A6D70", commontypes.FontStyle_BODY_XS),
										BackgroundColor: "#FFFFFF",
									},
									Deeplink: &deeplinkPb.Deeplink{
										Screen: deeplinkPb.Screen_HELP_MAIN,
									},
								},
							}}},
						},
					}},
			},
		},
	}
}

func (adp *AggregatedDeeplinkProvider) getLoanOfferBanner(ctx context.Context, lo *palBePb.LoanOffer, showOfferBanner bool) (*palTypesPb.LoanDashboardBottomSection_SectionDivision, error) {
	if lo.IsActiveNow() && showOfferBanner {
		offerDl, offerDlErr := adp.getLoanOfferDeeplinkScreen(ctx, lo)
		if offerDlErr != nil {
			return nil, errors.Wrap(offerDlErr, "unable to get loan offer deeplink screen")
		}
		return &palTypesPb.LoanDashboardBottomSection_SectionDivision{
			Components: []*palTypesPb.LoanDashboardBottomSection_Component{
				{
					Component: &palTypesPb.LoanDashboardBottomSection_Component_BannerWithRightIconAndBottomLeftCta{
						BannerWithRightIconAndBottomLeftCta: &palTypesPb.BannerWithRightIconAndBottomLeftCta{
							RightIcon: provider.GetVisualElementPng("https://epifi-icons.pointz.in/preapprovedloan/pld_dashboard_new_offer_banner_icon.png", 125, 110),
							Title:     ui.NewITC().WithTexts(provider.GetText(fmt.Sprintf("Get a new loan up to\n%s", money2.ToDisplayStringInIndianFormat(lo.GetOfferConstraints().GetMaxLoanAmount(), 0, true)), "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
							BottomLeftCta: ui.NewITC().WithTexts(provider.GetText("Get Loan", "#00B899", commontypes.FontStyle_BUTTON_S)).
								WithContainerBackgroundColor("#FFFFFF").WithDeeplink(offerDl).WithContainerPadding(8, 24, 8, 24).WithContainerCornerRadius(19),
							Deeplink: offerDl,
							BgColor:  "#6294A6",
						},
					},
				},
			},
		}, nil
	}
	return nil, nil
}

func (adp *AggregatedDeeplinkProvider) getLoanOfferDeeplinkScreen(ctx context.Context, lo *palBePb.LoanOffer) (*deeplinkPb.Deeplink, error) {
	isEnabled, err := adp.isOfferDetailsV4FlowEnabled(ctx, helper.GetFeLoanHeaderByBeLoanHeader(&palBePb.LoanHeader{
		LoanProgram: lo.GetLoanProgram(),
		Vendor:      lo.GetVendor(),
	}))
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("failed to evaluate if multi offer screen is live for particular loan header: %s:%s", lo.GetVendor(), lo.GetLoanProgram()))
	}
	if isEnabled {
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
				PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
					LandingInfoFilters: []string{palBePb.LandingInfoFilter_LANDING_INFO_SKIP_FAILED_LR_FETCH.String()},
				},
			},
		}, nil
	}

	loanDetailsDlProvider, providerErr := adp.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinks.GetDeeplinkProviderRequest{
		Vendor:      helper.GetPalFeVendorFromBe(lo.GetVendor()),
		LoanProgram: helper.GetFeLoanProgramFromBe(lo.GetLoanProgram()),
	})
	if providerErr != nil {
		return nil, errors.Wrap(providerErr, fmt.Sprintf("unable to get deeplink generator for vendor: %s, program: %s", lo.GetVendor().String(), lo.GetLoanProgram().String()))
	}
	return loanDetailsDlProvider.GetLoanOfferOnClickDeeplink(ctx, lo)
}
