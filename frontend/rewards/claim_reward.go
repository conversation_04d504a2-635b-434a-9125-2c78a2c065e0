package rewards

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	accountPb "github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/actor"
	beCasperPb "github.com/epifi/gamma/api/casper"
	beExchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	depositPb "github.com/epifi/gamma/api/deposit"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	errorsPb "github.com/epifi/gamma/api/frontend/errors"
	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	feRewardsPb "github.com/epifi/gamma/api/frontend/rewards"
	rewardsFrontendPkgPb "github.com/epifi/gamma/api/frontend/rewards/pkg"
	beKycPb "github.com/epifi/gamma/api/kyc"
	vkyc2 "github.com/epifi/gamma/api/kyc/vkyc"
	beRewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/rewards/luckydraw"
	beRewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	savingsPb "github.com/epifi/gamma/api/savings"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	types "github.com/epifi/gamma/api/typesv2"
	screenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/rewards"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	beUserPb "github.com/epifi/gamma/api/user"

	"github.com/epifi/gamma/frontend/rewards/tags"
	feErrors "github.com/epifi/gamma/pkg/frontend/errors"
	"github.com/epifi/gamma/pkg/onboarding"
	"github.com/epifi/gamma/pkg/vkyc"
)

var (
	defaultErrorView = &feRewardsPb.ErrorView{
		Title:       "Internal Server Error",
		Description: "Please try again after sometime",
	}
	maxSDCreationLimitReachedErrorView = &feRewardsPb.ErrorView{
		Title:       "Cannot create any more Smart Deposits",
		Description: "Try again after existing Smart Deposits get matured",
	}
	monthlySDCreationLimitReachedErrorView = &feRewardsPb.ErrorView{
		Title:       "Cannot create any more Smart Deposits in current month",
		Description: "Monthly Smart Deposits creation limit is reached. Please reclaim the reward next month",
	}
	depositCreationAlreadyInProgressErrorView = &feRewardsPb.ErrorView{
		Title:       "Cannot create a new Smart Deposit",
		Description: "Please try again after existing Smart Deposit request gets processed",
	}
	amountLessThanMinReqSDAmountErrorView = &feRewardsPb.ErrorView{
		Title:       "Cannot create or add into any existing Smart Deposit",
		Description: "Reclaim after creating a new Smart Deposit of min 30 days maturity",
	}
	amountGreaterThanMaxPermissibleSDAmountErrorView = &feRewardsPb.ErrorView{
		Title:       "Cannot create a new Smart Deposit",
		Description: "Reward amount exceeds maximum allowed amount for Smart Deposit creation",
	}
	notEligibleForNewSDCreationErrorView = &feRewardsPb.ErrorView{
		Title:       "Cannot create a new Smart Deposit now",
		Description: "Reclaim after sometime",
	}
	rewardNotSupportedOnAppVersionErrorView = &feRewardsPb.ErrorView{
		Title:       "Oops! This reward is not supported in this app version",
		Description: "Please update your app to latest version in order to claim this reward",
	}
	exchangerChooseOptionErrorView = feErrors.NewBottomSheetErrorView(
		"",
		"An error occurred, but your reward is safe", "",
		"Something's up at our end. We'll fix it ASAP! Don't worry, you're still getting a reward",
		&errorsPb.CTA{
			Text: "OK",
			Type: errorsPb.CTA_CUSTOM,
			Action: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
			},
			DisplayTheme: errorsPb.CTA_PRIMARY,
		},
	)
	stuckExchangerChooseOptionErrorView = feErrors.NewBottomSheetErrorView(
		"",
		"Congratulations on your winnings!", "",
		"We have recorded your option. It might take upto 24 Hours for your winnings to reflect on the Fi app",
		&errorsPb.CTA{
			Text: "OK",
			Type: errorsPb.CTA_CUSTOM,
			Action: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
			},
			DisplayTheme: errorsPb.CTA_PRIMARY,
		},
	)
	stuckSubmitUserInputForChosenOption = feErrors.NewBottomSheetErrorView(
		"",
		"Congratulations on your winnings!", "",
		"We have received your order. It might take upto 24 Hours for your order to reflect on the My Orders page.",
		&errorsPb.CTA{
			Text: "OK",
			Type: errorsPb.CTA_CUSTOM,
			Action: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
			},
			DisplayTheme: errorsPb.CTA_PRIMARY,
		},
	)
	// todo(divyadeep): discuss the Title and Info for default case with pallavi
	defaultSuccessScreen = &feRewardsPb.ChooseExchangerOrderOptionResponse_SuccessScreenDisplayDetails{
		Title: "Congratulations on your winnings!",
		Info:  "Processing your reward...",
	}
)

// nolint:funlen
func (r *RewardService) ClaimReward(ctx context.Context, req *feRewardsPb.ClaimRewardRequest) (*feRewardsPb.ClaimRewardResponse, error) {
	var (
		actorId     = req.GetReq().GetAuth().GetActorId()
		reward      *beRewardsPb.Reward
		rewardOffer *beRewardOffersPb.RewardOffer
		appPlatform = req.GetReq().GetAuth().GetDevice().GetPlatform()
		appVersion  = req.GetReq().GetAppVersionCode()
	)
	// todo: do the tasks concurrently

	// fetch the reward option that is chosen by the user
	reward, rewardOption, err := r.getRewardAndOptionById(ctx, req.GetRewardId(), req.GetRewardOptionId())
	if err != nil {
		logger.Error(ctx, "error fetching reward option by reward and option id", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String("option_id", req.GetRewardOptionId()), zap.Error(err))
		return &feRewardsPb.ClaimRewardResponse{
			Status: rpc.StatusInternalWithDebugMsg("error checking for existing reward SD : " + err.Error()),
		}, nil
	}

	// check if rewardId belongs to actor for which request is made
	if reward.GetActorId() != actorId {
		logger.Error(ctx, "reward does not belong to actor for which request is made", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.REWARD_ID, req.GetRewardId()))
		return &feRewardsPb.ClaimRewardResponse{
			Status: rpc.StatusInternalWithDebugMsg("reward does not belong to actor for which request is made"),
		}, nil
	}

	feChosenRewardOption, err := r.getFeChosenRewardOption(ctx, rewardOption, reward)
	if err != nil {
		logger.Error(ctx, "error getting FE chosen reward option", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.Error(err))
		return &feRewardsPb.ClaimRewardResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting FE chosen reward option"),
		}, nil
	}

	// return response v2 if applicable
	responseV2, err := r.getClaimRewardResponseV2(ctx, req, reward, rewardOption)
	if err != nil {
		logger.WarnWithCtx(ctx, "error fetching response v2 for claim reward. Ignoring silently", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String("option_id", req.GetRewardOptionId()), zap.Error(err))
	}
	if responseV2 != nil {
		responseV2.Status = rpc.StatusOk()
		return responseV2, nil
	}

	rewardValue, err := r.getClaimedRewardValue(rewardOption, feChosenRewardOption)
	if err != nil {
		logger.Error(ctx, "error while fetching claimed reward value", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.Error(err))
		return &feRewardsPb.ClaimRewardResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching claimed reward value"),
		}, nil
	}

	// fetch the reward-offer for response generation
	rewardOfferRes, err := r.rewardOffersClient.GetRewardOffersByIds(ctx, &beRewardOffersPb.GetRewardOffersByIdsRequest{
		Ids: []string{reward.GetOfferId()},
	})
	if rpcErr := epifigrpc.RPCError(rewardOfferRes, err); rpcErr != nil {
		logger.WarnWithCtx(ctx, "error fetching reward offer by offer-id during ClaimReward. Ignoring silently", zap.String(logger.REWARD_ID, req.GetRewardId()),
			zap.Error(err), zap.Any(logger.RPC_STATUS, rewardOfferRes.GetStatus()), zap.String(logger.REWARD_OFFER_ID, reward.GetOfferId()),
		)
	}
	if len(rewardOfferRes.GetRewardOffers()) > 0 {
		rewardOffer = rewardOfferRes.GetRewardOffers()[0]
	}

	// populate the next-screen-cta or redirection details
	nextScreenCta, err := r.getRewardClaimFlowNextScreenCta(ctx, actorId, rewardOption.GetRewardType(), rewardOffer.GetRewardMeta().GetRewardDisplayMeta().GetPostClaimNudgeAttributes(), appPlatform, appVersion)
	if err != nil {
		logger.WarnWithCtx(ctx, "error fetching next-screen-cta for reward claim flow. Ignoring silently", zap.Error(err),
			zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String("option_id", req.GetRewardOptionId()),
		)
	}

	// var additionalInfoCarousel = r.getRewardAdditionalInfoCarousel(ctx, actorId, rewardOption.GetRewardUnitsCalculationInfo(), rewardOption.GetDisplay(), appPlatform, appVersion, nil)

	var additionalInfoSection *fePb.AdditionalInfoSection
	// Currently not in use
	// if additionalInfoCarousel == nil {
	// 	additionalInfoSection = &fePb.AdditionalInfoSection{
	// 		KeyValueInfos: []*fePb.KeyValueInfo{
	// 			{
	// 				Key:             commontypes.GetTextFromHtmlStringFontColourFontStyle("CODE", "#B9B9B9", commontypes.FontStyle_OVERLINE_2),
	// 				IsValueCopyable: true,
	// 				ValueV1:         commontypes.GetTextFromHtmlStringFontColourFontStyle("QSZ8-DHSC3-KWO7", colorFiSnow, commontypes.FontStyle_NUMBERS_6),
	// 			},
	// 			{
	// 				Key:             commontypes.GetTextFromHtmlStringFontColourFontStyle("CODE", "#B9B9B9", commontypes.FontStyle_OVERLINE_2),
	// 				IsValueCopyable: false,
	// 				ValueV1:         commontypes.GetTextFromHtmlStringFontColourFontStyle("QSZ8-DHSC3-KWO7", colorFiSnow, commontypes.FontStyle_NUMBERS_6),
	// 			},
	// 		},
	// 		BgColour: widget.GetBlockBackgroundColour("#262626"),
	// 	}
	// }

	var shouldFetchCatalogOffers = false
	if rewardOption.GetRewardType() == beRewardsPb.RewardType_FI_COINS {
		if rewardOption.GetRewardUnitsCalculationInfo() != nil && len(rewardOption.GetRewardUnitsCalculationInfo().GetRewardUnitsCalculationEntries()) <= 2 {
			shouldFetchCatalogOffers = true
			nextScreenCta = nil
		}
	}

	var redirectionDetails *feRewardsPb.ClaimRewardResponse_RedirectionDetails
	// send redirection details if there is nothing to show on bottom of the page
	// not sending redirection details for non-prod env to support running automation tests
	if nextScreenCta == nil && !shouldFetchCatalogOffers && !cfg.IsNonProdEnv(r.dyconf.Application().Environment) {
		// redirect to MyRewards screen if no next-screen CTA is available
		redirectionDetails = &feRewardsPb.ClaimRewardResponse_RedirectionDetails{
			RedirectionScreen: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_MY_REWARDS_SCREEN,
			},
		}
	}

	var bottomBanner *fePb.ClaimRewardResponse_Banner
	// Currently not in use
	// bottomBanner = &fePb.ClaimRewardResponse_Banner{
	// 	BannerIcon:     commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/fi-inf-icon.png", 32, 32),
	// 	BannerText:     commontypes.GetTextFromStringFontColourFontStyle("Order received! It will reach your portfolio soon", colorGrayNight, commontypes.FontStyle_SUBTITLE_3),
	// 	BgColor:        widget.GetBlockBackgroundColour("#ECEEF0"),
	// 	BannerDeeplink: nil,
	// 	RightIcon:      commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/chevron-right-light.png", 24, 24),
	// }

	res := &feRewardsPb.ClaimRewardResponse{
		Title:                    "Congratulations on your winnings",
		RedirectionDetails:       redirectionDetails,
		NextScreenCta:            nextScreenCta,
		TitleV2:                  commontypes.GetTextFromStringFontColourFontStyle("Congratulations on your winnings", "#FFFFFF", commontypes.FontStyle_HEADLINE_1),
		RewardIcon:               commontypes.GetVisualElementFromUrlHeightAndWidth(rewardOption.GetDisplay().GetIcon(), 60, 60),
		ClaimedRewardTitle:       commontypes.GetTextFromStringFontColourFontStyle(rewardValue, "#FFFFFF", commontypes.FontStyle_NUMBERS_3),
		ClaimedRewardDescription: commontypes.GetTextFromStringFontColourFontStyle(rewardOption.GetDisplay().GetAfterClaimTitle(), "", commontypes.FontStyle_BODY_3),
		BottomBanner:             bottomBanner,
		ShouldFetchCatalogOffers: shouldFetchCatalogOffers,
		AdditionalInfoSection:    additionalInfoSection,
		RespHeader:               nil,
	}

	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, r.dyconf.RewardsFrontendMeta().BeDrivenRewardPostClaimScreenFeatureConfig()) {
		res.TitleV2 = commontypes.GetTextFromStringFontColourFontStyle("Congratulations on your winnings", "#FFFFFF", commontypes.FontStyle_HEADLINE_1)
		res.RewardIcon = commontypes.GetVisualElementFromUrlHeightAndWidth(rewardOption.GetDisplay().GetIcon(), 60, 60)
		res.ClaimedRewardTitle = commontypes.GetTextFromStringFontColourFontStyle(rewardValue, "#FFFFFF", commontypes.FontStyle_NUMBERS_3)
		res.ClaimedRewardDescription = commontypes.GetTextFromStringFontColourFontStyle(rewardOption.GetDisplay().GetAfterClaimTitle(), "", commontypes.FontStyle_BODY_3)
		// res.AdditionalInfoCarousel = additionalInfoCarousel
	}

	// if reward option is not of type lucky draw then call reward api otherwise call lucky draw claim winning api
	if rewardOption.GetLuckyDraw() == nil {
		chooseRewardRes, err := r.rewardsGeneratorClient.ChooseReward(ctx, &beRewardsPb.ChooseRewardRequest{
			RewardId:       req.GetRewardId(),
			RewardOptionId: req.GetRewardOptionId(),
			ClaimMetadata:  r.getBeRewardClaimMetadata(req.GetClaimMetadata()),
		})
		if rpcErr := epifigrpc.RPCError(chooseRewardRes, err); rpcErr != nil {
			logger.Error(ctx, "error in choose reward rpc call", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String("option_id", req.GetRewardOptionId()), zap.Any("response", chooseRewardRes), zap.Error(rpcErr))
			return &feRewardsPb.ClaimRewardResponse{
				Status: rpc.StatusInternalWithDebugMsg("error in choose reward rpc call"),
			}, nil
		}

		res.Status = rpc.StatusOk()

		return res, nil
	}

	// todo (utkarsh) : handle case when lucky draw could be given as a choice, then we need to first call choose lucky draw option
	// and next claim call, claim the lucky draw winning.

	// for now assuming that user was already registered for lucky draw, so directly claiming the winning
	// get lucky draw regID for actor
	luckyDrawRegId, err := r.getLuckyDrawRegIdByLuckyDrawAndActorId(ctx, rewardOption.GetLuckyDraw().GetLuckyDrawId(), actorId)
	if err != nil {
		logger.Error(ctx, "error fetching lucky draw registration id for claiming winning", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String("option_id", req.GetRewardOptionId()), zap.Error(err))
		return &feRewardsPb.ClaimRewardResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching lucky draw registration id for claiming winning : " + err.Error()),
		}, nil
	}

	// claim winning using lucky draw regId
	claimWinningRes, err := r.luckyDrawClient.ClaimWinningByRegistrationId(ctx, &luckydraw.ClaimWinningByRegIdRequest{
		LuckyDrawRegistrationId: luckyDrawRegId,
		ClaimMetadata:           r.getBeRewardClaimMetadata(req.GetClaimMetadata()),
	})
	if err != nil || !claimWinningRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error in claiming lucky draw winning rpc call", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String("option_id", req.GetRewardOptionId()), zap.Any(logger.RPC_STATUS, claimWinningRes.GetStatus()), zap.Error(err))
		return &feRewardsPb.ClaimRewardResponse{
			Status: rpc.StatusInternalWithDebugMsg("error claiming lucky draw winning"),
		}, nil
	}

	res.Status = rpc.StatusOk()

	return res, nil
}

// nolint:dupl
func (r *RewardService) getRewardAdditionalInfoCarousel(ctx context.Context, actorId string,
	beRewardUnitsCalcInfo *beRewardsPb.RewardUnitsCalculationInfo,
	beRewardOptionDisplayDetails *beRewardsPb.RewardOptionDisplay,
	appPlatform commontypes.Platform, appVersion uint32, userAndAppAttributes *UserAndAppAttributes) *feRewardsPb.AdditionalInfoCarousel {

	feRewardUnitsCalculationInfo := r.getFeRewardUnitsCalculationInfo(ctx, actorId, beRewardUnitsCalcInfo, beRewardOptionDisplayDetails, appPlatform, appVersion, userAndAppAttributes)
	if feRewardUnitsCalculationInfo == nil {
		return nil
	}

	infoCarousel := &feRewardsPb.AdditionalInfoCarousel{
		Title: feRewardUnitsCalculationInfo.GetTitle(),
	}

	for idx, entry := range feRewardUnitsCalculationInfo.GetRewardUnitsCalculationEntries() {
		if idx == 0 {
			continue
		}

		infoCarousel.Banners = append(infoCarousel.GetBanners(), &feRewardsPb.AdditionalInfoCarousel_Banner{
			Value: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("+%v", entry.GetDelta()), entry.GetDisplayDetails().GetDeltaColor(), commontypes.FontStyle_NUMBER_L),
				},
				LeftImgTxtPadding: 4,
				LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(entry.GetDisplayDetails().GetIconUrl(), 20, 20),
			},
			InfoText:     entry.GetDisplayDetails().GetTitle(),
			BgColor:      widget.GetBlockBackgroundColour(entry.GetDisplayDetails().GetBgColor()),
			DividerColor: widget.GetBlockBackgroundColour("#646464"),
		})
	}

	return infoCarousel
}

// getRewardClaimFlowNextScreenCta generates the nudge CTA to be shown after the claim flow of reward.
// It takes into account the reward-type, post-claim-nudge attributes from the reward-offer and the dynamic config flag to decide which nudge to show.
// Note: The priority order of checking for attributes matter:
// 1. Tier upgrade CTA
// 2. Salary account reg CTA
// 3. Spend-Fi-Coins if applicable CTA
// nolint:funlen
func (r *RewardService) getRewardClaimFlowNextScreenCta(ctx context.Context, actorId string, chosenOptionRewardType beRewardsPb.RewardType, postClaimNudgeAttributes []string, appPlatform commontypes.Platform, appVersion uint32) (*feRewardsPb.CTA, error) {
	var nextScreenCta *feRewardsPb.CTA

	tieringConfParamsResp, tieringConfParamsErr := r.tieringClient.GetConfigParams(ctx, &beTieringPb.GetConfigParamsRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(tieringConfParamsResp, tieringConfParamsErr); rpcErr != nil {
		logger.Error(ctx, "error while fetching config params for actor", zap.Error(rpcErr))
	}
	isTieringAllPlansV2Enabled := tieringConfParamsResp.GetIsMultipleWaysToEnterTieringEnabledForActor()

	// if account tier upgrade nudge is to be shown
	if r.checkIfClientSupportsBoosterFields(ctx, actorId, true, false, beTieringExtPb.Tier_TIER_UNSPECIFIED, appPlatform, appVersion) &&
		r.dyconf.RewardsFrontendMeta().RewardClaimFlowAllowedNextScreenCTAs().Get("TIER") && lo.Contains(postClaimNudgeAttributes, "TIER") {
		tieringPitch, err := r.tieringClient.GetTieringPitchV2(ctx, &beTieringPb.GetTieringPitchV2Request{
			ActorId: actorId,
		})
		// not checking for `DISABLED` status explicitly as that should get handled when we'd perform the tiering-release-sync checks in `checkIfClientSupportsBoosterFields`
		if rpcErr := epifigrpc.RPCError(tieringPitch, err); rpcErr != nil {
			logger.Error(ctx, "error fetching tiering pitch for the actor for claim-flow-next-screen-cta", zap.Error(err),
				zap.Any(logger.RPC_STATUS, tieringPitch.GetStatus()),
			)
			return nil, fmt.Errorf("error fetching tiering pitch for the actor")
		}

		var (
			nextTier = beTieringExtPb.Tier_TIER_UNSPECIFIED
		)

		for _, tieringMovement := range tieringPitch.GetMovementDetailsList() {
			// we want to nudge the user for upgrading to the next tier only if its allowed
			if tieringMovement.GetTierName() != tieringPitch.GetCurrentTier() && tieringMovement.GetIsMovementAllowed() {
				nextTier = tieringMovement.GetTierName()
				break
			}
		}

		if nextTier != beTieringExtPb.Tier_TIER_UNSPECIFIED /* && nextTier != beTieringExtPb.Tier_TIER_FI_SALARY */ { // salary tier was excluded earlier to show a different nudge
			ctaConfig, ok := r.rewardsFrontendMeta.RewardPostClaimCtaToConfigMap[nextTier.String()]
			if ok {
				nextScreenCta = &feRewardsPb.CTA{
					ImageUrl:          ctaConfig.IconUrl,
					SecondaryImageUrl: ctaConfig.SecondaryIconUrl,
					Text: &commontypes.Text{
						FontColor: ctaConfig.FontColor,
						BgColor:   ctaConfig.BgColor,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: ctaConfig.Title,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
						},
					},
					DeeplinkAction: tiering.AllPlansDeeplink(beTieringExtPb.Tier_TIER_UNSPECIFIED, isTieringAllPlansV2Enabled),
				}
			}
		}
	}
	if nextScreenCta != nil {
		return nextScreenCta, nil
	}

	// if salary program registration nudge is to be shown.
	// commenting the code as we'd send the user to tiering all-plans page in case of upgrade possibility to salary-account,
	// but keeping the code as we'd want to try this out in the future
	/*
			if r.dyconf.RewardsFrontendMeta().RewardClaimFlowAllowedNextScreenCTAs().Get("SALARY_PROGRAM") && lo.Contains(postClaimNudgeAttributes, "SALARY_PROGRAM") {
				salaryProgramReg, err := r.salaryProgramClient.GetCurrentRegStatusAndNextRegStage(ctx, &beSalaryProgramPb.CurrentRegStatusAndNextRegStageRequest{
					ActorId: actorId,
		FlowType: beSalaryProgramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
				})
				if rpcErr := epifigrpc.RPCError(salaryProgramReg, err); rpcErr != nil {
					logger.Error(ctx, "error fetching salary program reg status for claim-flow-next-screen-cta", zap.Error(err),
						zap.Any(logger.RPC_STATUS, salaryProgramReg.GetStatus()),
					)
					return nil, fmt.Errorf("error fetching salary program reg status for the actor")
				}

				if salaryProgramReg.GetRegistrationStatus() != beSalaryProgramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
					ctaConfig, ok := r.rewardsFrontendMeta.RewardPostClaimCtaToConfigMap["SALARY_PROGRAM"]
					if ok {
						nextScreenCta = &feRewardsPb.CTA{
							ImageUrl: ctaConfig.IconUrl,
							SecondaryImageUrl: ctaConfig.SecondaryIconUrl,
							Text: &commontypes.Text{
								FontColor: ctaConfig.FontColor,
								BgColor:   ctaConfig.BgColor,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: ctaConfig.Title,
								},
								FontStyle: &commontypes.Text_StandardFontStyle{
									StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
								},
							},
							DeeplinkAction: &deeplinkPb.Deeplink{
								Screen: deeplinkPb.Screen_SALARY_PROGRAM_INTRO_SCREEN,
							},
						}
					}
				}
			}
			if nextScreenCta != nil {
				return nextScreenCta, nil
			}
	*/

	// if no special nudges are to be shown, we fall back to the default nudge of spending-fi-coins if applicable
	switch chosenOptionRewardType {
	case beRewardsPb.RewardType_FI_COINS:
		ctaConfig, ok := r.rewardsFrontendMeta.RewardPostClaimCtaToConfigMap["SPEND_FI_COINS"]
		if ok {
			nextScreenCta = &feRewardsPb.CTA{
				ImageUrl:          ctaConfig.IconUrl,
				SecondaryImageUrl: ctaConfig.SecondaryIconUrl,
				Text: &commontypes.Text{
					FontColor: ctaConfig.FontColor,
					BgColor:   ctaConfig.BgColor,
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: ctaConfig.Title,
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
				DeeplinkAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
				},
			}
		}
	default:
		// do nothing if the reward type doesn't match
	}

	return nextScreenCta, nil
}

// getClaimRewardResponseV2 returns the response v2 for ClaimReward
func (r *RewardService) getClaimRewardResponseV2(ctx context.Context, req *feRewardsPb.ClaimRewardRequest, reward *beRewardsPb.Reward, chosenRewardOption *beRewardsPb.RewardOption) (*feRewardsPb.ClaimRewardResponse, error) {
	var (
		actorId                         = req.GetReq().GetAuth().GetActorId()
		appPlatform                     = req.GetReq().GetAuth().GetDevice().GetPlatform()
		appVersion                      = req.GetReq().GetAppVersionCode()
		rewardClaimSuccessScreenOptions = &screenOptionsPb.RewardClaimSuccessScreenOptions{}

		err error
	)

	// 1. check if claim reward response v2 is applicable
	if !isClaimRewardResponseV2Applicable(chosenRewardOption.GetRewardType(), appPlatform, appVersion) {
		return nil, nil
	}

	// 2. get reward claim success screen options
	// 2a. Get page ui details
	rewardClaimSuccessScreenOptions.PageUiDetails = &screenOptionsPb.RewardClaimSuccessScreenOptions_PageUiDetails{
		BgColor:    widget.GetBlockBackgroundColour(colorCharcoalBlack),
		TopBgImage: commontypes.GetVisualElementFromUrlHeightAndWidth(rewardClaimSuccessScreenTopBgImageUrl, 410, 440),
	}

	// 2b. Get reward details
	rewardClaimSuccessScreenOptions.RewardDetails, err = r.getRewardDetailsForRewardClaimSuccessScreen(ctx, actorId, reward, chosenRewardOption)
	if err != nil {
		return nil, fmt.Errorf("error getting reward details for claim reward success screen v2: %v", err)
	}

	// 2c. calling at the end so that the updated fi coins balance is used while
	//     fetching promo section as it contains the catalog offers
	//     which uses the fi coins balance for showing some info like redemption bottom sheet (inoperable info)

	// 2d. Get bottom section details
	rewardClaimSuccessScreenOptions.BottomSection = r.getBottomSectionForRewardClaimSuccessScreen(ctx, actorId)

	// 3. Choose the respective reward option for the actor
	chooseRewardRes, err := r.rewardsGeneratorClient.ChooseReward(ctx, &beRewardsPb.ChooseRewardRequest{
		RewardId:       reward.GetId(),
		RewardOptionId: req.GetRewardOptionId(),
		ClaimMetadata:  r.getBeRewardClaimMetadata(req.GetClaimMetadata()),
	})
	if rpcErr := epifigrpc.RPCError(chooseRewardRes, err); rpcErr != nil {
		logger.Error(ctx, "error in choose reward rpc call", zap.String(logger.REWARD_ID, reward.GetId()), zap.String(logger.CHOSEN_OPTION_ID, req.GetRewardOptionId()), zap.Any(logger.RESPONSE, chooseRewardRes), zap.Error(rpcErr))
		return nil, fmt.Errorf("error in choose reward rpc call, err: %v", rpcErr)
	}

	// Poll for reward processed status before fetching promo section (best effort)
	if _, pollErr := r.pollForRewardProcessedStatus(ctx, reward.GetId()); pollErr != nil {
		// Log the polling error as a warning but continue, as this is best-effort.
		logger.WarnWithCtx(ctx, "Polling for reward processed status failed, proceeding anyway (best effort)",
			zap.String(logger.REWARD_ID, reward.GetId()),
			zap.Error(pollErr),
		)
	}

	// 2c. Get promo section details
	rewardClaimSuccessScreenOptions.PromoSection, err = r.getPromoSectionForRewardClaimSuccessScreen(ctx, req)
	if err != nil {
		logger.WarnWithCtx(ctx, "error getting promo section for claim reward success screen v2", zap.String(logger.REWARD_ID, reward.GetId()), zap.Error(err))
		// if promo section is not available, we can still show the screen as reward has been claimed successfully
		rewardClaimSuccessScreenOptions.PromoSection = nil
	}

	return &fePb.ClaimRewardResponse{
		ResponseV2: &fePb.ClaimRewardResponse_ClaimRewardResponseV2{
			RedirectionDeeplink: &deeplinkPb.Deeplink{
				Screen:          deeplinkPb.Screen_REWARD_CLAIM_SUCCESS_SCREEN,
				ScreenOptionsV2: GetAnyWithoutError(rewardClaimSuccessScreenOptions),
			},
		},
	}, nil
}

// pollForRewardProcessedStatus polls the GetRewardsByRewardId a few times to check if the reward status is PROCESSED.
// It incorporates the changes from the user's diff (retry delay, error handling within loop, logging).
// Have kept a retry delay of 75ms and a maximum of 3 retries.
// The idea is the P90 is around 120ms(This data may be a bit skewed due to some rewards which are being processed infinitely),
// but Fi-Coins rewards will generally be processed quickly, so this should be sufficient for most cases.
// todo: add uts for this case
func (r *RewardService) pollForRewardProcessedStatus(ctx context.Context, rewardId string) (*beRewardsPb.Reward, error) {
	var (
		fetchedReward *beRewardsPb.Reward
		processed     = false
		maxRetries    = 3
		retryDelay    = 75 * time.Millisecond
	)

	for i := 0; i < maxRetries; i++ {
		// sleeping for retry delay
		time.Sleep(retryDelay)

		getRewardRes, getErr := r.rewardsGeneratorClient.GetRewardsByRewardId(ctx, &beRewardsPb.RewardsByRewardIdRequest{
			RewardId: rewardId,
		})

		if rpcErr := epifigrpc.RPCError(getRewardRes, getErr); rpcErr != nil {
			logger.Error(ctx, "error fetching reward status during polling",
				zap.String(logger.REWARD_ID, rewardId),
				zap.Error(rpcErr),
				zap.Int("attempt", i+1),
			)
			continue
		}

		fetchedReward = getRewardRes.GetReward()
		if fetchedReward.GetStatus() == beRewardsPb.RewardStatus_PROCESSED {
			processed = true
			break
		}
	}

	if !processed {
		return nil, fmt.Errorf("reward %s did not reach PROCESSED status after %d retries. Final status: %s", rewardId, maxRetries, fetchedReward.GetStatus().String())
	}

	return fetchedReward, nil
}

// isClaimRewardResponseV2Applicable returns true if :
// 1. reward type is FI_COINS and
// 2. app platform and version is correct
func isClaimRewardResponseV2Applicable(claimRewardOptionRewardType beRewardsPb.RewardType, appPlatform commontypes.Platform, appVersion uint32) bool {
	switch {
	case claimRewardOptionRewardType != beRewardsPb.RewardType_FI_COINS:
		return false
	case appPlatform == commontypes.Platform_ANDROID && appVersion >= 434:
		return true
	case appPlatform == commontypes.Platform_IOS && appVersion >= 598:
		return true
	default:
		return false
	}
}

// getRewardDetailsForRewardClaimSuccessScreen returns the reward details for the reward claim success screen
func (r *RewardService) getRewardDetailsForRewardClaimSuccessScreen(ctx context.Context, actorId string, reward *beRewardsPb.Reward, chosenRewardOption *beRewardsPb.RewardOption) (*screenOptionsPb.RewardClaimSuccessScreenOptions_RewardDetails, error) {
	var (
		title, rewardIconUrl, rewardValueText, rewardSubtitle string
	)
	switch chosenRewardOption.GetRewardType() {
	case beRewardsPb.RewardType_FI_COINS:
		fiCoinsBalance, err := r.getFiCoinsBalanceForActor(ctx, actorId)
		if err != nil {
			return nil, fmt.Errorf("getFiCoinsBalanceForActor call failed, error: %v", err)
		}

		fiCoinsRewardValue := chosenRewardOption.GetFiCoins().GetUnits()
		title = rewardClaimSuccessScreenFiCoinRewardTitle
		rewardIconUrl = chosenRewardOption.GetDisplay().GetIcon()
		rewardValueText = moneyPkg.ToDisplayStringInIndianFormatFromFloatValue(float64(fiCoinsBalance+fiCoinsRewardValue), 0)
		rewardSubtitle = fmt.Sprintf("+%s %s • %s", moneyPkg.ToDisplayStringInIndianFormatFromFloatValue(float64(fiCoinsRewardValue), 0), chosenRewardOption.GetDisplay().GetAfterClaimTitle(), reward.GetRewardOptions().GetActionDetails())
	default:
		return nil, fmt.Errorf("reward type not supported for claim reward success screen v2")
	}

	return &screenOptionsPb.RewardClaimSuccessScreenOptions_RewardDetails{
		Title:           commontypes.GetTextFromStringFontColourFontStyle(title, colorContentOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_M),
		RewardIcon:      commontypes.GetVisualElementFromUrlHeightAndWidth(rewardIconUrl, 32, 32),
		RewardValueText: commontypes.GetTextFromStringFontColourFontStyle(rewardValueText, colorFiSnow, commontypes.FontStyle_HEADLINE_2XL),
		RewardSubtitle:  commontypes.GetTextFromStringFontColourFontStyle(rewardSubtitle, colorJadeLightGreen, commontypes.FontStyle_BODY_XS),
	}, nil
}

func (r *RewardService) getPromoSectionForRewardClaimSuccessScreen(ctx context.Context, req *fePb.ClaimRewardRequest) (*sections.Section, error) {
	// fetch the catalog offers of category voucher
	catalogOffers, catalogOffersAdditionalDetails, err := r.getOrderedOffersAndExchangerOffers(
		ctx,
		req.GetReq(),
		nil,
		&tags.Config{
			// putting this for new offers catalog page catalog card because we want the same cards
			TagRenderLocation: tags.RenderLocationOffersCatalogPageCatalogCard,
		},
		// offers will be filtered based on the tags and category tags applied by the user
		&beCasperPb.CatalogFilters{
			CategoryTags: []beCasperPb.CategoryTag{beCasperPb.CategoryTag_CATEGORY_TAG_VOUCHERS},
		},
		false,
		fePb.SortBy_UNSPECIFIED_SORT_BY,
		deeplinkPb.Screen_REWARD_CLAIM_SUCCESS_SCREEN,
	)
	if err != nil {
		return nil, fmt.Errorf("error fetching ordered offers and exchanger offers: %v", err)
	}

	// fetch a vertical grid list section for the catalog offers
	catalogOfferGridListSection := r.fetchOffersForCatalogOffersSection(
		ctx,
		catalogOffers,
		sections.GridListSection_ORIENTATION_VERTICAL,
		catalogOffersAdditionalDetails,
		new(uint32))

	return &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: &sections.VerticalListSection{
				HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
				Components: []*components.Component{
					{
						Content: GetAnyWithoutError(ui.NewITC().WithContainerPadding(0, 0, 24, 0).
							WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(rewardClaimSuccessScreenPromoSectionTitle, colorContentOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_M, commontypes.Text_ALIGNMENT_CENTER).WithMaxLines(1))),
					},
					{
						Content: GetAnyWithoutError(catalogOfferGridListSection),
					},
				},
			},
		},
	}, nil
}

// getBottomSectionForRewardClaimSuccessScreen returns the bottom section for the reward claim success screen
func (r *RewardService) getBottomSectionForRewardClaimSuccessScreen(ctx context.Context, actorId string) *screenOptionsPb.RewardClaimSuccessScreenOptions_BottomSection {
	return &screenOptionsPb.RewardClaimSuccessScreenOptions_BottomSection{
		Cta: &rewardsFrontendPkgPb.Cta{
			Itc: ui.NewITC().
				WithContainer(36, 380, 40, colorFiGreen).
				WithContainerPaddingSymmetrical(16, 16).
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle(rewardClaimSuccessScreenBottomSectionCtaText, colorFiSnow, commontypes.FontStyle_BUTTON_M).WithMaxLines(1)),
			Action: &rewardsFrontendPkgPb.Cta_DeeplinkAction{
				DeeplinkAction: r.getPoshvineWebpageDeeplink(ctx, actorId),
			},
		},
	}
}

// GetClaimRewardInputScreen checks if any user input is required for claiming the reward.
// If required, it returns deeplink to the screen where input should be taken.
func (r *RewardService) GetClaimRewardInputScreen(ctx context.Context, req *feRewardsPb.ClaimRewardInputScreenRequest) (*feRewardsPb.ClaimRewardInputScreenResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	actorResp, errResp := r.actorServiceClient.GetActorById(ctx, &actor.GetActorByIdRequest{Id: actorId})
	if err := epifigrpc.RPCError(actorResp, errResp); err != nil {
		logger.Error(ctx, "Error in finding actor", zap.Error(err))
		return &feRewardsPb.ClaimRewardInputScreenResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	actor := actorResp.GetActor()

	// fetch the reward option that is chosen by the user
	_, rewardOption, err := r.getRewardAndOptionById(ctx, req.GetRewardId(), req.GetRewardOptionId())
	if err != nil {
		logger.Error(ctx, "error fetching reward option by reward and option id", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String("option_id", req.GetRewardOptionId()), zap.Error(err))
		return &feRewardsPb.ClaimRewardInputScreenResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching reward option by reward and option id : " + err.Error()),
		}, nil
	}

	logger.Info(ctx, "fetching claim reward input screen")
	switch rewardOption.GetRewardType() {

	// nolint: dupl
	case beRewardsPb.RewardType_SMART_DEPOSIT:
		inputScreenDeeplink, errorView := r.getClaimRewardInputScreenDeeplink(ctx, req.GetReq(), actor, rewardOption.GetRewardType(), rewardOption.GetSmartDeposit())
		if errorView != nil {
			logger.Error(ctx, "error getting input screen deeplink", zap.Any(logger.ACTOR_ID_V2, actorId), zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String("option_id", req.GetRewardOptionId()), zap.Any("error_view", errorView))
			return &feRewardsPb.ClaimRewardInputScreenResponse{
				Status:    rpc.StatusInternal(),
				ErrorView: errorView,
			}, nil
		}
		return &feRewardsPb.ClaimRewardInputScreenResponse{
			Status:      rpc.StatusOk(),
			InputScreen: inputScreenDeeplink,
		}, nil

	// nolint: dupl
	case beRewardsPb.RewardType_LUCKY_DRAW:
		inputScreenDeeplink, errorView := r.getClaimRewardInputScreenDeeplink(ctx, req.GetReq(), actor, rewardOption.GetRewardType(), rewardOption.GetLuckyDraw())
		if errorView != nil {
			logger.Error(ctx, "error getting input screen deeplink", zap.Any(logger.ACTOR_ID_V2, actorId), zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String("option_id", req.GetRewardOptionId()), zap.Any("error_view", errorView))
			return &feRewardsPb.ClaimRewardInputScreenResponse{
				Status:    rpc.StatusInternal(),
				ErrorView: errorView,
			}, nil
		}
		return &feRewardsPb.ClaimRewardInputScreenResponse{
			Status:      rpc.StatusOk(),
			InputScreen: inputScreenDeeplink,
		}, nil

	// nolint: dupl
	case beRewardsPb.RewardType_FI_COINS:
		inputScreenDeeplink, errorView := r.getClaimRewardInputScreenDeeplink(ctx, req.GetReq(), actor, rewardOption.GetRewardType(), rewardOption.GetFiCoins())
		if errorView != nil {
			logger.Error(ctx, "error getting input screen deeplink", zap.Any(logger.ACTOR_ID_V2, actorId), zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String("option_id", req.GetRewardOptionId()), zap.Any("error_view", errorView))
			return &feRewardsPb.ClaimRewardInputScreenResponse{
				Status:    rpc.StatusInternal(),
				ErrorView: errorView,
			}, nil
		}
		return &feRewardsPb.ClaimRewardInputScreenResponse{
			Status:      rpc.StatusOk(),
			InputScreen: inputScreenDeeplink,
		}, nil

	// nolint: dupl
	case beRewardsPb.RewardType_CASH:
		inputScreenDeeplink, errorView := r.getClaimRewardInputScreenDeeplink(ctx, req.GetReq(), actor, rewardOption.GetRewardType(), rewardOption.GetCash())
		if errorView != nil {
			logger.Error(ctx, "error getting input screen deeplink", zap.Any(logger.ACTOR_ID_V2, actorId), zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String("option_id", req.GetRewardOptionId()), zap.Any("error_view", errorView))
			return &feRewardsPb.ClaimRewardInputScreenResponse{
				Status:    rpc.StatusInternal(),
				ErrorView: errorView,
			}, nil
		}
		return &feRewardsPb.ClaimRewardInputScreenResponse{
			Status:      rpc.StatusOk(),
			InputScreen: inputScreenDeeplink,
		}, nil

	// nolint: dupl
	case beRewardsPb.RewardType_GIFT_HAMPER:
		inputScreenDeeplink, errorView := r.getClaimRewardInputScreenDeeplink(ctx, req.GetReq(), actor, rewardOption.GetRewardType(), rewardOption.GetGiftHamper())
		if errorView != nil {
			logger.Error(ctx, "error getting input screen deeplink", zap.Any(logger.ACTOR_ID_V2, actorId), zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String("option_id", req.GetRewardOptionId()), zap.Any("error_view", errorView))
			return &feRewardsPb.ClaimRewardInputScreenResponse{
				Status:    rpc.StatusInternal(),
				ErrorView: errorView,
			}, nil
		}
		return &feRewardsPb.ClaimRewardInputScreenResponse{
			Status:      rpc.StatusOk(),
			InputScreen: inputScreenDeeplink,
		}, nil
	// nolint: dupl
	case beRewardsPb.RewardType_US_STOCK:
		inputScreenDeeplink, errorView := r.getClaimRewardInputScreenDeeplink(ctx, req.GetReq(), actor, rewardOption.GetRewardType(), rewardOption.GetUsstockReward())
		if errorView != nil {
			logger.Error(ctx, "error getting input deeplink", zap.Any(logger.ACTOR_ID_V2, actorId), zap.String(logger.REWARD_ID, req.GetRewardId()),
				zap.String("option_id", req.GetRewardOptionId()), zap.Any("error_view", errorView))
			return &feRewardsPb.ClaimRewardInputScreenResponse{
				Status:    rpc.StatusInternal(),
				ErrorView: errorView,
			}, nil
		}
		return &feRewardsPb.ClaimRewardInputScreenResponse{
			Status:      rpc.StatusOk(),
			InputScreen: inputScreenDeeplink,
		}, nil
	case beRewardsPb.RewardType_NO_REWARD:
		// no input is required for no reward case
		return &feRewardsPb.ClaimRewardInputScreenResponse{
			Status: rpc.StatusOk(),
		}, nil

	default:
		logger.Error(ctx, "error getting input screen deeplink, unsupported reward type", zap.Any(logger.ACTOR_ID_V2, actorId), zap.String(logger.REWARD_TYPE, rewardOption.GetRewardType().String()), zap.String(logger.REWARD_ID, req.GetRewardId()), zap.String("option_id", req.GetRewardOptionId()))
		return &feRewardsPb.ClaimRewardInputScreenResponse{
			Status:    rpc.StatusInternal(),
			ErrorView: defaultErrorView,
		}, nil
	}
}

// getClaimRewardInputScreenDeeplink returns the deeplink for the claim reward input screen.
// Input screen depends on the type of reward being claimed.
// nolint: funlen
func (r *RewardService) getClaimRewardInputScreenDeeplink(ctx context.Context, feRequestHeader *feHeaderPb.RequestHeader, actor *types.Actor, rewardType beRewardsPb.RewardType, reward interface{}) (*deeplinkPb.Deeplink, *feRewardsPb.ErrorView) {
	if !r.checkIfRewardTypeIsSupportedOnAppVersion(rewardType, feRequestHeader) {
		logger.Info(ctx, "current app version does not supports claim this reward type", zap.String(logger.ACTOR_ID_V2, actor.GetId()),
			zap.String(logger.APP_PLATFORM, feRequestHeader.GetPlatform().String()), zap.Uint32(logger.APP_VERSION_CODE, feRequestHeader.GetAppVersionCode()), zap.String("reward_type", rewardType.String()))
		return nil, rewardNotSupportedOnAppVersionErrorView
	}

	switch rewardType {
	// for sd reward, first we check if sd amount can be credited in an existing sd or not.
	// If yes, then no input is required from user, otherwise we redirect the user to sd creation screen
	case beRewardsPb.RewardType_SMART_DEPOSIT:
		sdReward, ok := reward.(*beRewardsPb.SmartDeposit)
		if !ok {
			logger.Error(ctx, "invalid sd reward details")
			return nil, defaultErrorView
		}

		// check if reward amount can be credited to an existing SD.
		existingSDAccount, err := r.getExistingSDForCreditingReward(ctx, actor.GetId(), sdReward.GetAmount())
		switch {
		case err != nil:
			logger.Error(ctx, "error fetching existing SD for crediting reward", zap.Error(err))
			return nil, defaultErrorView
		// if existing sd account exists, we need not take any input, so return empty deeplink
		case existingSDAccount != nil:
			logger.Info(ctx, "sd reward amount will be added to existing SD", zap.String(logger.DEPOSIT_ACCOUNT_ID, existingSDAccount.GetId()))
			// todo (utkarsh) : changes in response to inform user that reward would be credited to this SD account.
			return nil, nil
		}

		// no existing sd found for crediting reward amount
		// check if new SD creation is possible
		err = r.IsNewRewardSDCreationPossible(ctx, actor, sdReward.GetAmount())
		switch {
		case errors.Is(err, depositCreationAlreadyInProgress):
			return nil, depositCreationAlreadyInProgressErrorView
		case errors.Is(err, monthlySDCreationLimitReached):
			return nil, monthlySDCreationLimitReachedErrorView
		case errors.Is(err, maxSDCreationLimitReached):
			return nil, maxSDCreationLimitReachedErrorView
		case errors.Is(err, amountLessThanMinRequiredAmountForSDCreation):
			return nil, amountLessThanMinReqSDAmountErrorView
		case errors.Is(err, amountGreaterThanMaxPermissibleAmountForSDCreation):
			return nil, amountGreaterThanMaxPermissibleSDAmountErrorView
		case errors.Is(err, notEligibleForNewSDCreation):
			return nil, notEligibleForNewSDCreationErrorView
		case err != nil:
			return nil, defaultErrorView
		}
		// no error implies sd creation is possible, return the deeplink to sd creation screen
		deeplink, err := r.getCreateNewRewardSDDeeplink(ctx, actor, sdReward)
		if err != nil {
			logger.Error(ctx, "error creating deeplink for new sd creation", zap.Error(err))
			return nil, defaultErrorView
		}
		return deeplink, nil

	// for gift hamper reward, we require shipping address as input from user.
	// on input screen, we also display the existing shipping addresses present in user profile.
	case beRewardsPb.RewardType_GIFT_HAMPER:
		_, ok := reward.(*beRewardsPb.GiftHamper)
		if !ok {
			logger.Error(ctx, "invalid gift hamper reward details")
			return nil, defaultErrorView
		}

		// fetch existing shipping and permanent address from user profile
		userAddressRes, err := r.usersClient.GetAllAddresses(ctx, &beUserPb.GetAllAddressesRequest{UserId: actor.GetEntityId()})
		if err != nil || !userAddressRes.GetStatus().IsSuccess() {
			logger.Error(ctx, "GetAllAddresses rpc call failed", zap.String(logger.USER_ID, actor.GetEntityId()), zap.Any(logger.RPC_STATUS, userAddressRes.GetStatus()), zap.Error(err))

			// if client doesn't support graceful handling of the empty addresses, then return error
			if !r.checkIfClientSupportsEmptyAddressesGracefully(feRequestHeader.GetAuth().GetDevice().GetPlatform(), feRequestHeader.GetAppVersionCode()) {
				return nil, defaultErrorView
			}
		}

		// convert google postal address to types.postalAddress
		var addresses []*types.PostalAddress
		for addressType, address := range userAddressRes.GetAddresses() {
			addressList := address.GetAddresses()
			if (addressType == types.AddressType_SHIPPING.String() || addressType == types.AddressType_PERMANENT.String()) && len(addressList) > 0 {
				addresses = append(addresses, convertToClientPostalAddressType(addressList[0]))
			}
		}
		// return deeplink with user's existing shipping address details
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_REWARD_SHIPPING_ADDRESS_INPUT_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_RewardShippingAddressInputScreenOptions{
				RewardShippingAddressInputScreenOptions: &deeplinkPb.RewardShippingAddressInputScreenOptions{
					// existing shipping addresses present in user profile
					Addresses: addresses,
				},
			},
		}, nil

	case beRewardsPb.RewardType_LUCKY_DRAW:
		ldReward, ok := reward.(*beRewardsPb.LuckyDraw)
		if !ok {
			logger.Error(ctx, "invalid lucky draw reward details")
			return nil, defaultErrorView
		}
		winning, err := r.getWinningByLuckyDrawAndActorId(ctx, ldReward.GetLuckyDrawId(), actor.GetId())
		if err != nil {
			logger.Error(ctx, "error fetching lucky draw winning by lucky draw and actor id", zap.String(logger.ACTOR_ID_V2, actor.GetId()), zap.String("lukcy_draw_id", ldReward.GetLuckyDrawId()), zap.Error(err))
			return nil, defaultErrorView
		}
		// called method for returning the input screen deeplink for claiming the winning reward
		return r.getClaimWinningRewardInputScreenDeeplink(ctx, feRequestHeader, actor, winning)
	case beRewardsPb.RewardType_FI_COINS, beRewardsPb.RewardType_CASH, beRewardsPb.RewardType_NO_REWARD, beRewardsPb.RewardType_US_STOCK:
		// no input required for above reward type
		return nil, nil

	// added default case to avoid lint error
	default:

	}

	logger.Error(ctx, "error fetching claim reward input screen, invalid reward type", zap.String(logger.REWARD_TYPE, rewardType.String()))
	return nil, defaultErrorView
}

// getClaimWinningRewardInputScreenDeeplink returns the deeplink to the input screen for claiming the lucky draw winning reward.
// Input screen depends on the type of reward present in winning.
func (r *RewardService) getClaimWinningRewardInputScreenDeeplink(ctx context.Context, feRequestHeader *feHeaderPb.RequestHeader, actor *types.Actor, winning *luckydraw.LuckyDrawWinning) (*deeplinkPb.Deeplink, *feRewardsPb.ErrorView) {
	switch winning.GetRewardType() {
	// if winning reward is of type FI_COINS, return deeplink to claim the fiCoins reward.
	case beRewardsPb.RewardType_FI_COINS:
		return r.getClaimRewardInputScreenDeeplink(ctx, feRequestHeader, actor, winning.GetRewardType(), winning.GetReward().GetFiCoins())
	// if winning reward is of type SMART_DEPOSIT, return deeplink to claim the smart deposit reward.
	case beRewardsPb.RewardType_SMART_DEPOSIT:
		return r.getClaimRewardInputScreenDeeplink(ctx, feRequestHeader, actor, winning.GetRewardType(), winning.GetReward().GetSmartDeposit())
	// if winning reward is of type GIFT_HAMPER, return deeplink to claim the gift hamper reward.
	case beRewardsPb.RewardType_GIFT_HAMPER:
		return r.getClaimRewardInputScreenDeeplink(ctx, feRequestHeader, actor, winning.GetRewardType(), winning.GetReward().GetGiftHamper())
	// if winning reward is of type CASH, return deeplink to claim the cash reward.
	case beRewardsPb.RewardType_CASH:
		return r.getClaimRewardInputScreenDeeplink(ctx, feRequestHeader, actor, winning.GetRewardType(), winning.GetReward().GetCash())
	// if winning reward is of type NO_REWARD, return deeplink to claim no reward.
	case beRewardsPb.RewardType_NO_REWARD:
		return r.getClaimRewardInputScreenDeeplink(ctx, feRequestHeader, actor, winning.GetRewardType(), winning.GetReward().GetNoReward())
	// return error is reward type is not explicitly handled
	default:
		logger.Error(ctx, "error fetching claim winning input screen deeplink, unsupported winning reward type", zap.String(logger.WINNING_ID, winning.GetId()), zap.String(logger.REWARD_TYPE, winning.GetRewardType().String()))
		return nil, defaultErrorView
	}
}

// getExistingSDForCreditingReward returns an existing SD account of the actor to which sd reward amount can be credited.
// If no such account  exists it returns nil response without error. The logic for fetching for existing SD for depositing the reward amount
// depends on deposit side checks for Add funds eligibility on an existing deposit account and rewards business logic specific checks
// on the deposit account (for eg if remaining maturity date is above some threshold value etc.)
func (r *RewardService) getExistingSDForCreditingReward(ctx context.Context, actorId string, sdAmount *money.Money) (*depositPb.DepositAccount, error) {
	logger.Info(ctx, "checking for existing SD for crediting reward amount", zap.String(logger.ACTOR_ID, actorId), zap.Int64("amount", sdAmount.GetUnits()))
	// get list of active SD accounts for the actor
	accountListRes, err := r.depositClient.ListDepositAccounts(ctx, &depositPb.ListDepositAccountsRequest{
		ActorId:     actorId,
		States:      []depositPb.DepositState{depositPb.DepositState_CREATED},
		Types:       []accountPb.Type{accountPb.Type_SMART_DEPOSIT},
		Provenances: []depositPb.DepositAccountProvenance{depositPb.DepositAccountProvenance_REWARDS_APP, depositPb.DepositAccountProvenance_USER_APP},
	})
	if err != nil || !accountListRes.GetStatus().IsSuccess() {
		return nil, errors.New("error fetching SD account list for actor")
	}
	// todo (utkarsh) : need some ordering of the deposit account list before doing the eligibility check
	//  as more than one account can pass eligibility criteria and currently the first eligible one is returned.

	// For each sd account check if its eligible for adding reward amount.
	// Currently following checks are done for eligibility :
	// 1. Checks at deposit side for add funds eligibility for given sd
	// 2. Reward business logic specific checks on that SD
	//    a.  If at least one month is remaining for the SD to mature
	for _, sdAccount := range accountListRes.GetAccounts() {
		// 1. Check at deposit side for add funds eligibility for given sd
		isEligibleRes, err := r.depositClient.IsEligibleForAddFunds(ctx, &depositPb.IsEligibleForAddFundsRequest{
			DepositAccountId: sdAccount.GetId(),
			Amount:           sdAmount,
		})
		if err != nil || isEligibleRes.GetStatus().IsInternal() {
			logger.Info(ctx, "depositClient.IsEligibleForAddFunds rpc call failed", zap.String(logger.DEPOSIT_ACCOUNT_ID, sdAccount.GetId()), zap.Any(logger.RPC_STATUS, isEligibleRes.GetStatus()), zap.Error(err))
			return nil, errors.New("depositClient.IsEligibleForAddFunds rpc call failed")
		}
		// handling separately all the non OK (apart from internal server case) cases as there can be some validation errors
		// that could led to ineligibility and we should not halt checking eligibility of other sd accounts because of those
		// non success cases. For now only INTERNAL SERVER error means some problem at deposit server end and processing
		// should only be halted for that case.
		if !isEligibleRes.GetStatus().IsSuccess() {
			logger.Info(ctx, "non success response in depositClient.IsEligibleForAddFunds call", zap.String(logger.DEPOSIT_ACCOUNT_ID, sdAccount.GetId()), zap.Any(logger.RPC_STATUS, isEligibleRes.GetStatus()))
			continue
		}
		if !isEligibleRes.IsEligible {
			logger.Info(ctx, "sd account not eligible for adding reward amount", zap.String(logger.DEPOSIT_ACCOUNT_ID, sdAccount.GetId()))
			continue
		}
		// 2. Reward business logic specific checks
		// 2a. If at least one month is remaining for the SD to mature
		if !time.Now().Add(oneMonthDuration).Before(sdAccount.GetMaturityDate().AsTime()) {
			logger.Info(ctx, "sd account not eligible for adding reward amount due to remaining maturity duration check", zap.String(logger.DEPOSIT_ACCOUNT_ID, sdAccount.GetId()))
			continue
		}

		logger.Info(ctx, "sd account eligible for adding reward amount", zap.String(logger.DEPOSIT_ACCOUNT_ID, sdAccount.GetId()))
		return sdAccount, nil
	}
	// no SD eligible for crediting reward amount.
	logger.Info(ctx, "no eligible sd account found for crediting reward amount", zap.String(logger.ACTOR_ID_V2, actorId))
	return nil, nil
}

// IsNewRewardSDCreationPossible checks that for the given actor if it's possible to create a new SD of given amount.
// Returns specific errors for validation failures.
// If returned error is nil then it implies that SD creation is possible.
func (r *RewardService) IsNewRewardSDCreationPossible(ctx context.Context, actor *types.Actor, amount *money.Money) error {
	logger.Info(ctx, "checking if new SD creation possible for actor", zap.String(logger.ACTOR_ID_V2, actor.GetId()))
	// check if actor is eligible for creating a new SD account.
	createSDEligibilityRes, err := r.depositClient.IsEligibleForCreateDeposit(ctx, &depositPb.IsEligibleForCreateDepositRequest{
		ActorId:                  actor.GetId(),
		DepositType:              accountPb.Type_SMART_DEPOSIT,
		DepositAccountProvenance: depositPb.DepositAccountProvenance_REWARDS_APP,
	})
	if err != nil {
		logger.Error(ctx, "depositClient.IsEligibleForCreateDeposit call failed", zap.String(logger.ACTOR_ID_V2, actor.GetId()), zap.Any(logger.RPC_STATUS, createSDEligibilityRes.GetStatus()), zap.Error(err))
		return errors.Wrap(err, "depositClient.IsEligibleForCreateDeposit call failed")
	}
	if !createSDEligibilityRes.GetStatus().IsSuccess() {
		switch createSDEligibilityRes.GetStatus().GetCode() {
		case uint32(depositPb.IsEligibleForCreateDepositResponse_DEPOSIT_CREATION_ALREADY_IN_PROGRESS):
			logger.Error(ctx, "cannot create new SD now, some deposit creation is already in progress", zap.Any(logger.RPC_STATUS, createSDEligibilityRes.GetStatus()))
			return depositCreationAlreadyInProgress
		case uint32(depositPb.IsEligibleForCreateDepositResponse_ACTIVE_SD_MAX_LIMIT_REACHED):
			logger.Error(ctx, "cannot create new SD, max SD limit reached", zap.Any(logger.RPC_STATUS, createSDEligibilityRes.GetStatus()))
			return maxSDCreationLimitReached
		case uint32(depositPb.IsEligibleForCreateDepositResponse_TOTAL_DEPOSIT_MAX_LIMIT_MONTHLY_EXHAUSTED):
			logger.Error(ctx, "cannot create new SD, monthly SD creation limit reached", zap.Any(logger.RPC_STATUS, createSDEligibilityRes.GetStatus()))
			return monthlySDCreationLimitReached
		default:
			logger.Error(ctx, "error checking for deposit creation eligibility", zap.Any(logger.RPC_STATUS, createSDEligibilityRes.GetStatus()))
			return errors.New("depositClient.IsEligibleForCreateDeposit call failed")
		}
	}
	// if we get notEligible in response , return error
	if !createSDEligibilityRes.GetIsEligible() {
		logger.Error(ctx, "cannot create new SD, deposit eligibility check returned false", zap.Any("eligibility_response", createSDEligibilityRes))
		return notEligibleForNewSDCreation
	}

	// get actor age for GetDepositCreationAttributes request
	actorAge, err := GetActorAge(ctx, r.usersClient, actor)
	if err != nil {
		logger.Error(ctx, "error fetching actor age", zap.String(logger.ACTOR_ID_V2, actor.GetId()))
		return errors.New("error fetching actor age")
	}

	// get deposit creation attributes required for creating a new SD.
	depositCreationAttributesRes, err := r.depositClient.GetDepositCreationAttributes(ctx, &depositPb.GetDepositCreationAttributesRequest{
		Age:  actorAge,
		Type: accountPb.Type_SMART_DEPOSIT,
		// todo (utkarsh) : add vendor to sd reward config
		Vendor:                   commonvgpb.Vendor_FEDERAL_BANK,
		ActorId:                  actor.GetId(),
		DepositAccountProvenance: depositPb.DepositAccountProvenance_REWARDS_APP,
	})
	if err != nil || !depositCreationAttributesRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "depositClient.GetDepositCreationAttributes call failed", zap.String(logger.ACTOR_ID_V2, actor.GetId()), zap.Any(logger.RPC_STATUS, depositCreationAttributesRes.GetStatus()), zap.Error(err))
		return errors.New("depositClient.GetDepositCreationAttributes call failed")
	}

	// check if amount to be added for new SD >= min required amount
	if amount.GetUnits() < depositCreationAttributesRes.GetMinAmount().GetUnits() {
		logger.Error(ctx, "sd reward amount is less than minimum amount required for new SD creation", zap.String(logger.ACTOR_ID_V2, actor.GetId()),
			zap.Int64("min_required_amount", depositCreationAttributesRes.GetMinAmount().GetUnits()), zap.Int64("sd_reward_amount", amount.GetUnits()))
		return amountLessThanMinRequiredAmountForSDCreation
	}

	// check if amount to be added for new SD  <= max required amount
	if amount.GetUnits() > depositCreationAttributesRes.GetMaxAmount().GetUnits() {
		logger.Error(ctx, "sd reward amount is greater than maximum amount permissible for new SD creation", zap.String(logger.ACTOR_ID_V2, actor.GetId()),
			zap.Int64("max_permissible_amount", depositCreationAttributesRes.GetMaxAmount().GetUnits()), zap.Int64("sd_reward_amount", amount.GetUnits()))
		// this case shouldn't occur as the reward amount would be configured according but anyways handling it.
		return amountGreaterThanMaxPermissibleAmountForSDCreation
	}
	// all above checks passed, so return nil error
	logger.Info(ctx, "new rewards SD creation possible for actor", zap.String(logger.ACTOR_ID_V2, actor.GetId()))
	return nil
}

// getCreateNewRewardSDDeeplink returns deeplink to CREATE_NEW_REWARD_SD_SCREEN screen with proper screen params.
func (r *RewardService) getCreateNewRewardSDDeeplink(ctx context.Context, actor *types.Actor, sdReward *beRewardsPb.SmartDeposit) (*deeplinkPb.Deeplink, error) {
	// get exact maturity date from sd reward config
	maturityDate, err := beRewardsPb.GetRewardConfigDate(sdReward.GetMaturityDateConfig(), time.Now())
	if err != nil {
		return nil, errors.Wrap(err, "error calculating sd maturity date from config")
	}
	// calculate deposit term given maturity date
	depositTerm, err := ConvertToValidDepositTerm(maturityDate.AsTime())
	if err != nil {
		return nil, errors.Wrap(err, "error converting maturity date to deposit term")
	}
	// get actor age for calculating sd interest rate
	actorAge, err := GetActorAge(ctx, r.usersClient, actor)
	if err != nil {
		return nil, errors.Wrap(err, "error fetching actor age")
	}
	// calculate the interest rate applicable on SD
	depositAttrRes, err := r.depositClient.GetDepositCreationAttributes(ctx, &depositPb.GetDepositCreationAttributesRequest{
		Age:  actorAge,
		Type: accountPb.Type_SMART_DEPOSIT,
		// todo (utkarsh) : add vendor to sd reward config
		Vendor:                   commonvgpb.Vendor_FEDERAL_BANK,
		ActorId:                  actor.GetId(),
		DepositAccountProvenance: depositPb.DepositAccountProvenance_REWARDS_APP,
	})
	if err != nil || !depositAttrRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "depositClient.GetDepositCreationAttributes call failed", zap.Any(logger.RPC_STATUS, depositAttrRes.GetStatus()), zap.Error(err))
		return nil, errors.New("depositClient.GetDepositCreationAttributes call failed")
	}
	newSDInterestRate, err := GetInterestRateForDepositTerm(ctx, depositAttrRes.GetInterestDetails(), depositTerm)
	if err != nil {
		logger.Error(ctx, "error calculating interest for smart deposit", zap.Any("deposit_term", depositTerm), zap.Any("interest_details", depositAttrRes.GetInterestDetails()), zap.Error(err))
		return nil, errors.New("depositClient.GetDepositCreationAttributes call failed")
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CREATE_NEW_REWARD_SD_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_CreateRewardSd_ScreenOptions{
			CreateRewardSd_ScreenOptions: &deeplinkPb.CreateRewardSDScreenOptions{
				SdName:        rewardsSDName, // todo (utkarsh) : decide on a naming convention
				DepositAmount: types.GetFromBeMoney(sdReward.GetAmount()),
				InterestRate:  newSDInterestRate,
				MaturityDate:  maturityDate,
			},
		},
	}, nil
}

func (r *RewardService) checkIfRewardTypeIsSupportedOnAppVersion(rewardType beRewardsPb.RewardType, feRequestHeader *feHeaderPb.RequestHeader) bool {
	appPlatform, appVersion := feRequestHeader.GetPlatform(), int(feRequestHeader.GetAppVersionCode())
	// Supported cases are more than not supported cases so only checking for not supported
	// cases in switch block and returning false if found, otherwise by default true is returned.
	// nolint:gocritic
	switch rewardType {
	case beRewardsPb.RewardType_GIFT_HAMPER:
		if appPlatform == feHeaderPb.Platform_ANDROID && appVersion < r.rewardsFrontendMeta.MinAndroidAppVersionSupportingGiftHamperReward ||
			appPlatform == feHeaderPb.Platform_IOS && appVersion < r.rewardsFrontendMeta.MinIosAppVersionSupportingGiftHamperReward {
			return false
		}
	default:
	}
	return true
}

// ChooseExchangerOrderOption chooses/claims one of the reward option which are shown post redeeming the exchanger-offer
// nolint
func (r *RewardService) ChooseExchangerOrderOption(ctx context.Context, req *feRewardsPb.ChooseExchangerOrderOptionRequest) (*feRewardsPb.ChooseExchangerOrderOptionResponse, error) {
	var (
		actorId          = req.GetReq().GetAuth().GetActorId()
		exchangerOrderId = req.GetExchangerOrderId()
		optionId         = req.GetOptionId()
	)
	beChooseOptionRes, err := r.exchangerOfferClient.ChooseExchangerOrderOption(ctx, &beExchangerPb.ChooseExchangerOrderOptionRequest{
		ActorId:          actorId,
		ExchangerOrderId: exchangerOrderId,
		OptionId:         optionId,
	})
	if err != nil || !beChooseOptionRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error while choosing exchanger-order-option",
			zap.String(logger.ORDER_ID, exchangerOrderId), zap.String("optionId", optionId), zap.Error(err),
		)

		return &feRewardsPb.ChooseExchangerOrderOptionResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while choosing exchanger-order-option"),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: exchangerChooseOptionErrorView,
			},
		}, nil
	}
	// return error view if order is still in OPTIONS_GENERATED state
	if r.checkIfBottomSheetErrorIsSupportedOnAppVersion(req.GetReq()) && beChooseOptionRes.GetExchangerOfferOrder().GetState() == beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATED {
		logger.Error(ctx, "exchanger offer order stuck in options generated state",
			zap.String(logger.ORDER_ID, exchangerOrderId), zap.String("optionId", optionId))

		return &feRewardsPb.ChooseExchangerOrderOptionResponse{
			Status: rpc.StatusOkWithDebugMsg("exchanger offer order stuck in options generated state"),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: stuckExchangerChooseOptionErrorView,
			},
		}, nil
	}

	updatedFeExchangerOrder, err := r.convertToFeExchangerOrder(ctx, beChooseOptionRes.GetExchangerOfferOrder(), nil, tags.RenderLocationUnspecified)

	if err != nil || updatedFeExchangerOrder == nil {
		logger.Error(ctx, "error converting the updated be-exchanger-offer-order to fe",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ORDER_ID, exchangerOrderId), zap.Error(err),
		)

		return &feRewardsPb.ChooseExchangerOrderOptionResponse{
			Status: rpc.StatusInternalWithDebugMsg("error converting be-order to fe-order"),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: exchangerChooseOptionErrorView,
			},
		}, nil
	}

	// logic to decide the next screen, i.e. redeem-exchanger-offer or offer-catalog screen

	successScreenDisplayDetails, err := r.getChosenExchangerOptionSuccessScreenDetails(beChooseOptionRes.GetExchangerOfferOrder().GetChosenOption().GetRewardType())
	if err != nil {
		logger.Error(ctx, "error in getting SuccessScreenDisplayDetails", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, beChooseOptionRes.GetExchangerOfferOrder().GetId()), zap.String("exchangerOrderChosenRewardType", beChooseOptionRes.GetExchangerOfferOrder().GetChosenOption().GetRewardType().String()), zap.Error(err))
		successScreenDisplayDetails = defaultSuccessScreen
	}

	// if user's input is needed the normal flow of CBR will be broken.
	// In this flow we will not redirect the user to Catalogue screen or the exchanger offer screen but the SubmitExchangerOrderUserInput RPC will decide where the user will be redirected.
	if beChooseOptionRes.GetExchangerOfferOrder().GetState() == beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT {
		// fetch the actor as it will be needed for retrieving user's info (like saved addresses)
		getActorByIdResponse, err := r.actorServiceClient.GetActorById(ctx, &actor.GetActorByIdRequest{Id: actorId})
		if err != nil || !getActorByIdResponse.GetStatus().IsSuccess() {
			logger.Error(ctx, "failed to fetch actor by actorId", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err), zap.String(logger.RPC_STATUS, getActorByIdResponse.GetStatus().String()))
			return &feRewardsPb.ChooseExchangerOrderOptionResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status:    rpc.StatusInternalWithDebugMsg("failed to fetch actor by actorId"),
					ErrorView: nil,
				},
			}, nil
		}
		actor := getActorByIdResponse.GetActor()

		inputScreenDeeplink, errView := r.getExchangerOrderInputScreenDeeplink(ctx, actor, beChooseOptionRes.GetExchangerOfferOrder().GetChosenOption().GetRewardType(),
			req.GetReq().GetAuth().GetDevice().GetPlatform(), req.GetReq().GetAppVersionCode(),
		)
		if errView != nil {
			logger.Error(ctx, "error in fetching input screen's deep link required for fulfilling the ExchangerOrder", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, exchangerOrderId))
			return &feRewardsPb.ChooseExchangerOrderOptionResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status:    rpc.StatusInternalWithDebugMsg("error in fetching input screen's deep link required for fulfilling the ExchangerOrder"),
					ErrorView: errView,
				},
			}, nil
		}

		// in case of success, return the deepLink to the required user-input screen
		return &feRewardsPb.ChooseExchangerOrderOptionResponse{
			Status:                      rpc.StatusOk(),
			ExchangerOrder:              updatedFeExchangerOrder,
			NextScreen:                  inputScreenDeeplink,
			SuccessScreenDisplayDetails: successScreenDisplayDetails,
		}, nil
	}

	// todo(rohanchougule): can be broken down into logical parts and extracted in a separate method to resuse it elsewhere
	exchangerOfferId := beChooseOptionRes.GetExchangerOfferOrder().GetExchangerOfferId()
	beExchangerOfferRes, err := r.exchangerOfferClient.GetExchangerOffersByIds(ctx, &beExchangerPb.GetExchangerOffersByIdsRequest{
		Ids: []string{exchangerOfferId},
	})
	if err != nil || !beExchangerOfferRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching exchanger offer", zap.String(logger.OFFER_ID, exchangerOfferId), zap.Error(err),
			zap.Any(logger.RPC_STATUS, beChooseOptionRes.GetStatus()),
		)
		return &feRewardsPb.ChooseExchangerOrderOptionResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching exchanger offer"),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: exchangerChooseOptionErrorView,
			},
		}, nil
	}

	// fetch attempts count
	currentDayStartTime := datetime.GetTimeAtStartOfTheDay(time.Now().In(datetime.IST))
	attemptsCountForOfferRes, err := r.exchangerOfferClient.GetExchangerOffersActorAttemptsCount(ctx, &beExchangerPb.GetExchangerOffersActorAttemptsCountRequest{
		ActorId:           actorId,
		ExchangerOfferIds: []string{exchangerOfferId},
		// start time of today's date in IST
		FromAttemptedTime: timestamppb.New(currentDayStartTime),
		// start time of tomorrow's date in IST
		ToAttemptedTime: timestamppb.New(currentDayStartTime.AddDate(0, 0, 1)),
	})
	if err != nil || !attemptsCountForOfferRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching attempts count of offer for actor", zap.Error(err), zap.String(logger.OFFER_ID, exchangerOfferId),
			zap.Any(logger.RPC_STATUS, attemptsCountForOfferRes.GetStatus()),
		)
		return &feRewardsPb.ChooseExchangerOrderOptionResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching exchanger offer attempts count"),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: exchangerChooseOptionErrorView,
			},
		}, nil
	}

	// retrieve the current fi-coins balance
	fiCoinsBalance, err := r.getFiCoinsBalanceForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error fetching fi-coins balance in ChooseExchangerOrderOption", zap.Error(err))
		return &feRewardsPb.ChooseExchangerOrderOptionResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching fi-coins balance for the actor"),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: exchangerChooseOptionErrorView,
			},
		}, nil
	}

	maxAllowedAttempts := beExchangerOfferRes.GetExchangerOffers()[0].GetOfferAggregatesConfig().GetDailyAllowedAttemptsPerUser()
	attemptsCount := attemptsCountForOfferRes.GetOfferIdToAttemptsCountMap()[exchangerOfferId]
	offerRedemptionPrice := beExchangerOfferRes.GetExchangerOffers()[0].GetRedemptionPrice()
	// setting the default next screen to redemption screen
	nextScreen := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_REDEEM_EXCHANGER_OFFER,
		ScreenOptions: &deeplinkPb.Deeplink_RedeemExchangerOfferScreenOptions{
			RedeemExchangerOfferScreenOptions: &deeplinkPb.RedeemExchangerOfferScreenOptions{
				OfferId: exchangerOfferId,
			},
		},
	}

	// set the screen to offer-catalog screen in case the offer is not redeemable
	isMonthlyCapHit, err := r.isMonthlyRedemptionCapHitForExchangerOffer(ctx, actorId, exchangerOfferId)
	if err != nil {
		logger.Error(ctx, "error checking if monthly redemption max cap is hit for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.EXCHANGER_OFFER_ORDER_ID, exchangerOrderId), zap.Error(err))
		return &feRewardsPb.ChooseExchangerOrderOptionResponse{
			Status: rpc.StatusInternalWithDebugMsg("error checking if monthly redemption max cap is hit for actor"),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: exchangerChooseOptionErrorView,
			},
		}, nil
	}
	if fiCoinsBalance < uint32(offerRedemptionPrice) || attemptsCount >= int32(maxAllowedAttempts) || isMonthlyCapHit {
		nextScreen = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN}
	}

	// check if max-cap for reward-units is hit for the offer or not, i.e. reward generation is possible or not
	offerIdToMaxCapHitMap, err := r.isEORewardUnitsMaxCapReached(ctx, actorId, beExchangerOfferRes.GetExchangerOffers())
	if err != nil {
		logger.Error(ctx, "error checking if max-cap for reward-units is hit by an actor for exchanger-offer",
			zap.String(logger.OFFER_ID, exchangerOfferId), zap.Error(err),
		)
		return &feRewardsPb.ChooseExchangerOrderOptionResponse{
			Status: rpc.StatusInternalWithDebugMsg("error checking if max-cap for reward-units is hit by an actor for exchanger-offer"),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: exchangerChooseOptionErrorView,
			},
		}, nil
	}

	// take the user to offers landing screen if the offer can't be redeemed anymore
	if offerIdToMaxCapHitMap[exchangerOfferId] {
		nextScreen = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN}
	}

	// redirect to 'MyOrders' screen if reward-type is EGV and if app version is >
	// min supported version for redirection flow for android, or platform is
	// IOS
	if beChooseOptionRes.GetExchangerOfferOrder().GetRewardType() == beExchangerPb.RewardType_REWARD_TYPE_EGV &&
		(req.GetReq().GetAuth().GetDevice().GetPlatform() != commontypes.Platform_ANDROID ||
			(req.GetReq().GetAuth().GetDevice().GetPlatform() == commontypes.Platform_ANDROID &&
				req.GetReq().GetAuth().GetDevice().GetAppVersion() >= uint32(r.dyconf.RewardsFrontendMeta().MinAndroidVersionSupportingCbrEgvRedirectionFlow()))) {
		nextScreen = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN}
	}

	return &feRewardsPb.ChooseExchangerOrderOptionResponse{
		Status:                      rpc.StatusOk(),
		ExchangerOrder:              updatedFeExchangerOrder,
		NextScreen:                  nextScreen,
		SuccessScreenDisplayDetails: successScreenDisplayDetails,
	}, nil
}

func (r *RewardService) getChosenExchangerOptionSuccessScreenDetails(rewardType beExchangerPb.RewardType) (*feRewardsPb.ChooseExchangerOrderOptionResponse_SuccessScreenDisplayDetails, error) {
	switch rewardType {
	case beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE:
		return &feRewardsPb.ChooseExchangerOrderOptionResponse_SuccessScreenDisplayDetails{
			Title: "Drum Roll! This has been added to your orders",
			Info:  "Taking you to address confirmation",
		}, nil
	case beExchangerPb.RewardType_REWARD_TYPE_CASH:
		return &feRewardsPb.ChooseExchangerOrderOptionResponse_SuccessScreenDisplayDetails{
			Title: "Congratulations on your winnings",
			Info:  "The money will be added to your account in 2-3 days.",
		}, nil
	case beExchangerPb.RewardType_REWARD_TYPE_FI_COINS:
		return &feRewardsPb.ChooseExchangerOrderOptionResponse_SuccessScreenDisplayDetails{
			Title: "Way to go!",
			Info:  "This will be credited to your Fi-Coin balance.",
		}, nil
	case beExchangerPb.RewardType_REWARD_TYPE_EGV:
		return &feRewardsPb.ChooseExchangerOrderOptionResponse_SuccessScreenDisplayDetails{
			Title: "Congratulations on your winnings",
			Info:  "You can find the voucher on 'My Orders' page",
		}, nil
	case beExchangerPb.RewardType_REWARD_TYPE_CREDIT_CARD_BILL_ERASER:
		return &feRewardsPb.ChooseExchangerOrderOptionResponse_SuccessScreenDisplayDetails{
			Title: "Congratulations on your winnings",
			Info:  "This will be added to your credit card limit on your unbilled amount in 24-48 hours",
		}, nil
	default:
		return nil, fmt.Errorf("unsupported reward type encountered while adding SuccessScreenDisplayDetails")
	}
}

func (r *RewardService) isMonthlyRedemptionCapHitForExchangerOffer(ctx context.Context, actorId, offerId string) (bool, error) {
	// fetch exchanger offer
	exchangerOffersByIdsRes, err := r.exchangerOfferClient.GetExchangerOffersByIds(ctx, &beExchangerPb.GetExchangerOffersByIdsRequest{
		Ids: []string{offerId},
	})
	if rpcErr := epifigrpc.RPCError(exchangerOffersByIdsRes, err); rpcErr != nil {
		return false, fmt.Errorf("error while calling exchangerOfferClient.GetExchangerOffersByIds, err: %w", rpcErr)
	}
	if len(exchangerOffersByIdsRes.GetExchangerOffers()) != 1 {
		return false, fmt.Errorf("no exchanger offer returned for given ID, offerId: %s", offerId)
	}
	if exchangerOffersByIdsRes.GetExchangerOffers()[0].GetOfferAggregatesConfig().GetUserLevelMonthlyRedemptionCap() != 0 {
		redemptionCountRes, redemptionCountErr := r.exchangerOfferClient.GetRedemptionCountsForActorOfferIdsInMonth(ctx, &beExchangerPb.GetRedemptionCountsForActorOfferIdsInMonthRequest{
			ActorId:        actorId,
			OfferIds:       []string{offerId},
			MonthTimestamp: timestamppb.Now(),
		})
		if rpcErr := epifigrpc.RPCError(redemptionCountRes, redemptionCountErr); rpcErr != nil {
			return false, fmt.Errorf("error while calling exchangerOfferClient.GetRedemptionCountsForActorOfferIdsInMonth, err: %w", rpcErr)
		}

		return redemptionCountRes.GetOfferIdToRedemptionsCountInMonthMap()[offerId] < exchangerOffersByIdsRes.GetExchangerOffers()[0].GetOfferAggregatesConfig().GetUserLevelMonthlyRedemptionCap(), nil
	}

	return false, nil
}

// BulkClaimRewards RPC can be used for claiming multiple rewards at once for an actor
func (r *RewardService) BulkClaimRewards(ctx context.Context, req *feRewardsPb.BulkClaimRewardsRequest) (*feRewardsPb.BulkClaimRewardsResponse, error) {
	res, err := r.rewardsGeneratorClient.BulkClaimRewardsWithDefaultOptionV2(ctx, &beRewardsPb.BulkClaimRewardsWithDefaultOptionV2Request{
		ActorId: req.GetReq().GetAuth().GetActorId(),
	})

	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil && !res.GetStatus().IsAlreadyExists() {
		logger.Error(ctx, "failed to claim rewards in bulk", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err), zap.String(logger.RPC_STATUS, res.GetStatus().String()))
		return &feRewardsPb.BulkClaimRewardsResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("failed to claim rewards in bulk")},
		}, nil
	}

	return &feRewardsPb.BulkClaimRewardsResponse{
		RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
	}, nil

}

// GetClaimedRewardDetails fetched reward details for passed rewardId/exchangerOrderId
// Currently shows:
//  1. Reward Details if reward is claimed/processed
//  2. If Reward is LOCKED, then shows bottom sheet with unlock details
//
// https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?node-id=12244%3A79466&t=buqkfpbtIexoCbix-4
// nolint:dupl,funlen
func (r *RewardService) GetClaimedRewardDetails(ctx context.Context, req *feRewardsPb.GetClaimedRewardDetailsRequest) (*feRewardsPb.GetClaimedRewardDetailsResponse, error) {
	const (
		rewardIdPrefix = "RW"
	)
	var (
		actorId       = req.GetReq().GetAuth().GetActorId()
		claimedReward *feRewardsPb.ClaimedRewardDetails
	)

	// Since req.RewardId can contain both rewardId and exchangerOrderId,
	// fetch reward/exchangerOfferOrder in the following order until reward is found:
	//  1. Fetch reward if rewardIdPrefix (RW) is present in the id (best-effort)
	//  2. Fetch exchangerOfferOrder
	//  3. Fetch reward in case rewardIdPrefix is not present in the id
	if strings.HasPrefix(req.GetRewardId(), rewardIdPrefix) {
		var err error
		claimedReward, err = r.getClaimedReward(ctx, req)
		switch {
		case errors.Is(err, epifierrors.ErrFailedPrecondition):
			logger.Error(ctx, "pre condition failed for fetching claimed reward", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.Error(err))
			return &feRewardsPb.GetClaimedRewardDetailsResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusFailedPrecondition(),
				},
			}, nil

		case err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Error(ctx, "error while fetching claimed reward", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.Error(err))
			return &feRewardsPb.GetClaimedRewardDetailsResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("error while fetching claimed reward"),
				},
			}, nil
		}
	} else {
		var err error
		claimedReward, err = r.getClaimedExchangerReward(ctx, req.GetRewardId(), actorId)
		switch {
		case errors.Is(err, epifierrors.ErrFailedPrecondition):
			logger.Error(ctx, "pre condition failed for fetching claimed exchanger reward", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, req.GetRewardId()), zap.Error(err))
			return &feRewardsPb.GetClaimedRewardDetailsResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusFailedPrecondition(),
				},
			}, nil

		case err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Error(ctx, "error while fetching claimed exchanger reward", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, req.GetRewardId()), zap.Error(err))
			return &feRewardsPb.GetClaimedRewardDetailsResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("error while fetching claimed exchanger reward"),
				},
			}, nil
		}
	}

	// Last Attempt: Case when passed id is rewardId but doesn't have rewardIdPrefix
	if claimedReward == nil {
		var err error
		claimedReward, err = r.getClaimedReward(ctx, req)
		switch {
		case errors.Is(err, epifierrors.ErrFailedPrecondition):
			logger.Error(ctx, "pre condition failed for fetching claimed reward", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.Error(err))
			return &feRewardsPb.GetClaimedRewardDetailsResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusFailedPrecondition(),
				},
			}, nil

		// return RecordNotFound if reward is not found in last attempt as well
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Error(ctx, "reward not found", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.Error(err))
			return &feRewardsPb.GetClaimedRewardDetailsResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusRecordNotFound(),
				},
			}, nil

		case err != nil:
			logger.Error(ctx, "error while fetching claimed reward", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.Error(err))
			return &feRewardsPb.GetClaimedRewardDetailsResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("error while fetching claimed reward"),
				},
			}, nil
		}
	}

	logger.Debug(ctx, "claimed reward details", zap.Any("reward", claimedReward), zap.String(logger.REWARD_ID, req.GetRewardId()))

	return &feRewardsPb.GetClaimedRewardDetailsResponse{
		RespHeader: &feHeaderPb.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		RewardDetails: claimedReward,
	}, nil
}

// nolint:funlen
func (r *RewardService) getClaimedReward(ctx context.Context, req *feRewardsPb.GetClaimedRewardDetailsRequest) (*feRewardsPb.ClaimedRewardDetails, error) {
	var (
		appVersion  = req.GetReq().GetAuth().GetDevice().GetAppVersion()
		appPlatform = req.GetReq().GetAuth().GetDevice().GetPlatform()
		actorId     = req.GetReq().GetAuth().GetActorId()
	)

	getRewardRes, err := r.rewardsGeneratorClient.GetRewardsByRewardId(ctx, &beRewardsPb.RewardsByRewardIdRequest{
		RewardId: req.GetRewardId(),
	})
	if rpcErr := epifigrpc.RPCError(getRewardRes, err); rpcErr != nil {
		if getRewardRes.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("rewardsGeneratorClient.GetRewardsByRewardId call failed, err: %w", rpcErr)
	}
	reward := getRewardRes.GetReward()

	// check if reward belongs to the actor for which request is made
	if reward.GetActorId() != actorId {
		return nil, fmt.Errorf("actorId mismatch, expected: %s, got: %s", reward.GetActorId(), actorId)
	}

	if !(reward.GetChosenReward().GetOption() != nil || reward.GetStatus() == beRewardsPb.RewardStatus_LOCKED) {
		return nil, epifierrors.ErrFailedPrecondition
	}

	userAndAppAttributes, err := r.getUserAndAppAttributes(ctx, req.GetReq().GetAuth(), appVersion)
	if err != nil {
		return nil, fmt.Errorf("error while fetching user and app attributes, err: %w", err)
	}

	status, err := r.getRewardStatus(ctx, reward, userAndAppAttributes, appVersion, appPlatform)
	if err != nil {
		return nil, fmt.Errorf("unable to compute status of reward, err: %w", err)
	}

	statusDesc, err := r.getRewardStatusDescText(status, reward)
	if err != nil {
		return nil, errors.Wrap(err, "unable to compute reward status desc")
	}

	blockedActionDialog, err := r.getClaimedRewardBlockedActionDialog(ctx, status, reward, userAndAppAttributes, actorId)
	if err != nil {
		return nil, fmt.Errorf("error fetching blocked action dialog, err: %w", err)
	}
	// if chosenOption is:
	//  1. Present, then show the reward details in BG of blockedActionDialog, figma: https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=3504%3A7260&t=Wroz1r9k5aONaZDA-1
	//  2. Not present, the just show title and status desc in BG of blockedActionDialog
	if blockedActionDialog != nil && reward.GetChosenReward().GetOption() == nil {
		return &feRewardsPb.ClaimedRewardDetails{
			Title:                commontypes.GetTextFromHtmlStringFontColourFontStyle(reward.GetRewardOptions().GetActionDetails(), "#FFFFFF", commontypes.FontStyle_HEADLINE_L),
			StatusDescription:    commontypes.GetTextFromStringFontColourFontStyle(statusDesc, "#A4A4A4", commontypes.FontStyle_SUBTITLE_S),
			BlockingActionDialog: blockedActionDialog,
		}, nil
	}

	feChosenRewardOption, err := r.getFeChosenRewardOption(ctx, reward.GetChosenReward(), reward)
	if err != nil {
		return nil, fmt.Errorf("unable to compute chosen reward option, err: %w", err)
	}

	logger.Debug(ctx, "fe chosen reward option", zap.Any("option", feChosenRewardOption))

	rewardValueStr, err := r.getClaimedRewardValue(reward.GetChosenReward(), feChosenRewardOption)
	if err != nil {
		return nil, fmt.Errorf("error fetch claimed reward value, err: %w", err)
	}
	var rewardValue *ui.IconTextComponent
	if rewardValueStr != "" {
		rewardValue = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(rewardValueStr, colorFiSnow, commontypes.FontStyle_HEADLINE_XL),
			},
		}
	}

	logger.Debug(ctx, "fe chosen reward option", zap.String("rewardVal", rewardValueStr), zap.Any("option", feChosenRewardOption))

	tag, _ := r.getRewardTileBottomTagAndBorderColor(ctx, reward.GetActorId(), reward, appPlatform, appVersion, nil)
	var bottomTag *ui.IconTextComponent
	if tag != nil {
		bottomTag = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				tag,
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       tag.GetBgColor(),
				CornerRadius:  8,
				LeftPadding:   8,
				RightPadding:  8,
				TopPadding:    2,
				BottomPadding: 2,
			},
		}
	}

	return &feRewardsPb.ClaimedRewardDetails{
		RewardLogo:             commontypes.GetVisualElementFromUrlHeightAndWidth(r.getClaimedRewardLogo(reward.GetChosenReward()), 60, 60),
		RewardValue:            rewardValue,
		RewardDescription:      r.getClaimedRewardDescription(feChosenRewardOption),
		RewardCardBg:           widget.GetBlockBackgroundColour("#383838"),
		BottomTag:              bottomTag,
		Title:                  commontypes.GetTextFromHtmlStringFontColourFontStyle(reward.GetRewardOptions().GetActionDetails(), "#FFFFFF", commontypes.FontStyle_HEADLINE_L),
		StatusDescription:      commontypes.GetTextFromStringFontColourFontStyle(statusDesc, "#A4A4A4", commontypes.FontStyle_SUBTITLE_S),
		BottomTexts:            r.getClaimedRewardBottomTexts(reward),
		BlockingActionDialog:   blockedActionDialog,
		BottomDescriptionText:  r.getClaimedRewardBottomDescText(status, reward.GetChosenReward().GetRewardType()),
		AdditionalInfoCarousel: r.getClaimedRewardAdditionalInfoCarousel(ctx, actorId, reward.GetChosenReward().GetRewardUnitsCalculationInfo(), reward.GetChosenReward().GetDisplay(), appPlatform, appVersion, userAndAppAttributes),
		DescriptionsList:       r.getClaimedRewardDescriptionList(feChosenRewardOption),
	}, nil
}

// nolint:funlen
func (r *RewardService) getClaimedRewardBlockedActionDialog(ctx context.Context, feStatus feRewardsPb.RewardStatus, beReward *beRewardsPb.Reward, userAndAppAttributes *UserAndAppAttributes, actorId string) (*feRewardsPb.ClaimedRewardDetails_BlockingActionDialog, error) {
	var (
		deeplinksMap = map[deeplinkName]*deeplinkPb.Deeplink{}
	)

	if beReward.GetStatus() != beRewardsPb.RewardStatus_LOCKED {
		return nil, nil
	}

	if userAndAppAttributes.KycLevel != beKycPb.KYCLevel_FULL_KYC {
		var err error
		deeplinksMap[minKycToFullKycConversionDeeplink], err = vkyc.BuildVKYCStatusDeeplink(&vkyc.StatusScreenOptions{
			EntryPoint: vkyc2.EntryPoint_ENTRY_POINT_REWARDS,
		})
		if err != nil {
			logger.Error(ctx, "error while fetching KYC completion deeplink for user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		}
	}

	if userAndAppAttributes.SavingsAccountStatus != savingsPb.State_CREATED {
		var err error
		deeplinksMap[openSavingsAccountDeeplink], err = onboarding.GetSABenefitsScreen(ctx)
		if err != nil {
			logger.Error(ctx, "error while fetching deeplink to open SA for user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		}
	}

	unlockDetails, err := r.getRewardUnlockDisplayDetails(ctx, userAndAppAttributes, feStatus, beReward, deeplinksMap)
	if err != nil {
		return nil, fmt.Errorf("error fetching unlock display details, err: %w", err)
	}
	if unlockDetails != nil {
		return &feRewardsPb.ClaimedRewardDetails_BlockingActionDialog{
			Icon:    unlockDetails.GetIcon(),
			Heading: unlockDetails.GetHeading(),
			Desc:    unlockDetails.GetDesc(),
			Ctas:    unlockDetails.GetCtas(),
		}, nil
	}

	if beReward.GetRewardOptions().GetUnlockDate() != nil {
		unlockTime := datetime.TimestampToTime(beReward.GetRewardOptions().GetUnlockDate())
		unlockDateString := unlockTime.In(datetime.IST).Format(r.rewardsFrontendMeta.AppDisplayDateTimeFormat)

		return &feRewardsPb.ClaimedRewardDetails_BlockingActionDialog{
			Icon: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/rewards/green-lock.png",
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  86,
							Height: 86,
						},
					},
				},
			},
			Heading: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Your reward is currently locked"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
				},
				FontColor: colorGrayNight,
			},
			Desc: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("It will automatically unlock on %s.", unlockDateString)},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_BODY_3_PARA,
				},
				FontColor: colorGrayLead,
			},
			Ctas: []*fePb.CtaV1{
				{
					CtaTitle: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Go back"},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BUTTON_M,
						},
						FontColor: colorFiGreen,
					},
					BgColor:   colorIvory,
					BgColorV2: widget.GetBlockBackgroundColour(colorIvory),
					Action: &fePb.CtaV1_CustomAction{
						CustomAction: &fePb.CustomAction{
							ActionType: fePb.CustomAction_GO_BACK_TO_PREVIOUS_SCREEN,
						},
					},
				},
			},
		}, nil
	}

	return nil, fmt.Errorf("got unhandled unlock state")
}

func (r *RewardService) getClaimedExchangerReward(ctx context.Context, exchangerOfferOrderId, actorId string) (*feRewardsPb.ClaimedRewardDetails, error) {

	exchangerOrderRes, err := r.exchangerOfferClient.GetExchangerOrderById(ctx, &beExchangerPb.GetExchangerOrderByIdRequest{
		ExchangerOrderId: exchangerOfferOrderId,
	})
	if rpcErr := epifigrpc.RPCError(exchangerOrderRes, err); rpcErr != nil {
		if exchangerOrderRes.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("exchangerOfferClient.GetExchangerOrderById call failed, err: %w", rpcErr)
	}
	exchangerOrder := exchangerOrderRes.GetExchangerOrder()

	// check if exchanger order belongs to the actor for which request is made
	if exchangerOrder.GetActorId() != actorId {
		return nil, fmt.Errorf("actorId mismatch, expected: %s, got: %s", exchangerOrder.GetActorId(), actorId)
	}

	if exchangerOrder.GetChosenOption() == nil {
		return nil, fmt.Errorf("chosen option cannot be nil, err: %w", epifierrors.ErrFailedPrecondition)
	}

	if !lo.Contains(getOpenExchangerRewardTileExchangerOrderStates(), exchangerOrder.GetState()) {
		return nil, fmt.Errorf("unsupported exchanger order state, state: %s, err: %w", exchangerOrder.GetState().String(), epifierrors.ErrFailedPrecondition)
	}

	if !lo.Contains(getOpenExchangerRewardTileRewardTypes(), exchangerOrder.GetRewardType()) {
		return nil, fmt.Errorf("unsupported exchanger reward type, type: %s, err: %w", exchangerOrder.GetRewardType().String(), epifierrors.ErrFailedPrecondition)
	}

	feExchangerOrderStatus, err := r.getFeExchangerOrderStatus(exchangerOrder.GetState())
	if err != nil {
		return nil, errors.Wrap(err, "error converting BE exchanger-order status to FE")
	}

	statusDesc := r.getStatusDescTextForExchangerOrder(feExchangerOrderStatus, exchangerOrder.GetRewardType())

	return &feRewardsPb.ClaimedRewardDetails{
		RewardLogo: commontypes.GetVisualElementFromUrlHeightAndWidth(exchangerOrder.GetChosenOption().GetDisplayDetails().GetAfterClaimIconUrl(), 60, 60),
		RewardValue: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(exchangerOrder.GetChosenOption().GetDisplayDetails().GetAfterClaimTitle(), "#FFFFFF", commontypes.FontStyle_HEADLINE_XL),
			},
		},
		RewardDescription: commontypes.GetTextFromStringFontColourFontStyle(exchangerOrder.GetChosenOption().GetDisplayDetails().GetAfterClaimSubtitle(), "#8D8D8D", commontypes.FontStyle_BODY_S),
		RewardCardBg:      widget.GetBlockBackgroundColour("#383838"),
		Title:             commontypes.GetTextFromHtmlStringFontColourFontStyle(exchangerOrder.GetChosenOption().GetDisplayDetails().GetDesc(), "#FFFFFF", commontypes.FontStyle_HEADLINE_L),
		StatusDescription: commontypes.GetTextFromStringFontColourFontStyle(statusDesc, "#A4A4A4", commontypes.FontStyle_SUBTITLE_S),
		BottomTexts:       r.getClaimedExchangerRewardBottomTexts(exchangerOrder.GetCreatedAt(), exchangerOrder.GetExternalId()),
	}, nil
}

func (r *RewardService) getClaimedRewardLogo(chosenOption *beRewardsPb.RewardOption) string {
	return chosenOption.GetDisplay().GetIcon()
}

func (r *RewardService) getClaimedRewardValue(beChosenOption *beRewardsPb.RewardOption, feChosenOption *feRewardsPb.RewardOption) (string, error) {
	var (
		rewardValueStr string
	)

	if beChosenOption.GetRewardType() == beRewardsPb.RewardType_CREDIT_CARD_BILL_ERASER {
		rewardValueStr = moneyPkg.ToDisplayString(beChosenOption.GetCreditCardBillEraser().GetAmount())
	} else {
		switch val := feChosenOption.GetOption().(type) {
		case *feRewardsPb.RewardOption_Cash:
			moneyStr, err := moneyPkg.ToString(feChosenOption.GetCash().GetAmount().GetBeMoney(), 0)
			if err != nil {
				return "", err
			}
			rewardValueStr = "₹" + moneyStr
		case *feRewardsPb.RewardOption_FiCoins:
			rewardValueStr = strconv.FormatInt(int64(feChosenOption.GetFiCoins().GetPoints()), 10)
		case *feRewardsPb.RewardOption_Sd:
			moneyStr, err := moneyPkg.ToString(feChosenOption.GetSd().GetAmount().GetBeMoney(), 0)
			if err != nil {
				return "", err
			}
			rewardValueStr = "₹" + moneyStr
		case *feRewardsPb.RewardOption_GiftHamper:
			rewardValueStr = feChosenOption.GetGiftHamper().GetProductName()
		case *feRewardsPb.RewardOption_NoReward:
			rewardValueStr = ""
		case *feRewardsPb.RewardOption_Luckydraw:
			rewardValueStr = feChosenOption.GetLuckydraw().GetLuckyDrawId()
		default:
			return "", fmt.Errorf("unhandled fe rewardType received, type: %T", val)
		}
	}

	logger.DebugNoCtx("got the reward value", zap.String("val", rewardValueStr), zap.Uint32("fi", feChosenOption.GetFiCoins().GetPoints()))

	return rewardValueStr, nil
}

func (r *RewardService) getClaimedRewardDescription(feChosenOption *feRewardsPb.RewardOption) *commontypes.Text {
	// if feChosenOption.GetOptionText() != nil {
	// 	return feChosenOption.GetOptionText().GetRewardDescription()
	// }

	return commontypes.GetTextFromStringFontColourFontStyle(feChosenOption.GetDisplayDetails().GetDesc(), "#8D8D8D", commontypes.FontStyle_BODY_S)
}

func (r *RewardService) getClaimedExchangerRewardBottomTexts(createdAt *timestamppb.Timestamp, rewardId string) []*feRewardsPb.ClaimedRewardDetails_BottomTextInfo {
	const (
		titleColor = "#A4A4A4"
		dateLayout = "02 Jan 2006"
	)

	var bottomTexts []*feRewardsPb.ClaimedRewardDetails_BottomTextInfo

	bottomTexts = append(bottomTexts,
		&feRewardsPb.ClaimedRewardDetails_BottomTextInfo{
			BottomTextTitle:       commontypes.GetTextFromStringFontColourFontStyle("ADDED ON", titleColor, commontypes.FontStyle_OVERLINE_XS_CAPS),
			BottomTextDescription: commontypes.GetTextFromStringFontColourFontStyle(createdAt.AsTime().In(datetime.IST).Format(dateLayout), colors.ColorSnow, commontypes.FontStyle_BODY_S),
		},
		&feRewardsPb.ClaimedRewardDetails_BottomTextInfo{
			BottomTextTitle:       commontypes.GetTextFromStringFontColourFontStyle("REWARD ID", titleColor, commontypes.FontStyle_OVERLINE_XS_CAPS),
			BottomTextDescription: commontypes.GetTextFromStringFontColourFontStyle(rewardId, colors.ColorSnow, commontypes.FontStyle_BODY_S),
		})

	return bottomTexts
}

func (r *RewardService) getClaimedRewardBottomTexts(beReward *beRewardsPb.Reward) []*feRewardsPb.ClaimedRewardDetails_BottomTextInfo {
	const (
		titleColor = "#A4A4A4"
		dateLayout = "02 Jan 2006"
	)

	var bottomTexts []*feRewardsPb.ClaimedRewardDetails_BottomTextInfo

	addedOnDate := beReward.GetChosenReward().GetProcessingDate()
	if beReward.GetChosenReward().GetProcessingDate() == nil {
		addedOnDate = beReward.GetCreatedAt()
	}

	bottomTexts = append(bottomTexts, &feRewardsPb.ClaimedRewardDetails_BottomTextInfo{
		BottomTextTitle:       commontypes.GetTextFromStringFontColourFontStyle("ADDED ON", titleColor, commontypes.FontStyle_OVERLINE_XS_CAPS),
		BottomTextDescription: commontypes.GetTextFromStringFontColourFontStyle(addedOnDate.AsTime().In(datetime.IST).Format(dateLayout), colors.ColorSnow, commontypes.FontStyle_BODY_S),
	})

	if beReward.GetChosenReward().GetRewardType() == beRewardsPb.RewardType_FI_COINS {
		bottomTexts = append(bottomTexts, &feRewardsPb.ClaimedRewardDetails_BottomTextInfo{
			BottomTextTitle:       commontypes.GetTextFromStringFontColourFontStyle("EXPIRES ON", titleColor, commontypes.FontStyle_OVERLINE_XS_CAPS),
			BottomTextDescription: commontypes.GetTextFromStringFontColourFontStyle(beReward.GetChosenReward().GetFiCoins().GetExpiresAt().AsTime().In(datetime.IST).Format(dateLayout), colors.ColorSnow, commontypes.FontStyle_BODY_S),
		})
	}

	bottomTexts = append(bottomTexts,
		&feRewardsPb.ClaimedRewardDetails_BottomTextInfo{
			BottomTextTitle:       commontypes.GetTextFromStringFontColourFontStyle("REFERENCE NUMBER", titleColor, commontypes.FontStyle_OVERLINE_XS_CAPS),
			BottomTextDescription: commontypes.GetTextFromStringFontColourFontStyle(beReward.GetRefId(), colors.ColorSnow, commontypes.FontStyle_BODY_S),
		},
		&feRewardsPb.ClaimedRewardDetails_BottomTextInfo{
			BottomTextTitle:       commontypes.GetTextFromStringFontColourFontStyle("REWARD ID", titleColor, commontypes.FontStyle_OVERLINE_XS_CAPS),
			BottomTextDescription: commontypes.GetTextFromStringFontColourFontStyle(beReward.GetExternalId(), colors.ColorSnow, commontypes.FontStyle_BODY_S),
		})

	return bottomTexts
}

func (r *RewardService) getClaimedRewardBottomDescText(feStatus feRewardsPb.RewardStatus, rewardType beRewardsPb.RewardType) *commontypes.Text {
	if !lo.Contains([]feRewardsPb.RewardStatus{feRewardsPb.RewardStatus_ARRIVING, feRewardsPb.RewardStatus_DELAYED_UNLOCK_WITH_AUTOCLAIM}, feStatus) {
		return nil
	}

	switch rewardType {
	case beRewardsPb.RewardType_CASH, beRewardsPb.RewardType_SMART_DEPOSIT:
		return commontypes.GetTextFromStringFontColourFontStyle("The amount will be added to your rewards within 24hrs on the mentioned date", "#A4A4A4", commontypes.FontStyle_BODY_4)
	case beRewardsPb.RewardType_FI_COINS:
		return commontypes.GetTextFromStringFontColourFontStyle("The Fi-Coins will be added to your rewards within 24hrs on the mentioned date", "#A4A4A4", commontypes.FontStyle_BODY_4)
	default:
		return nil
	}
}

// nolint:dupl
func (r *RewardService) getClaimedRewardAdditionalInfoCarousel(ctx context.Context, actorId string,
	beRewardUnitsCalcInfo *beRewardsPb.RewardUnitsCalculationInfo,
	beRewardOptionDisplayDetails *beRewardsPb.RewardOptionDisplay,
	appPlatform commontypes.Platform, appVersion uint32, userAndAppAttributes *UserAndAppAttributes) *feRewardsPb.ClaimedRewardDetails_AdditionalInfoCarousel {

	feRewardUnitsCalculationInfo := r.getFeRewardUnitsCalculationInfo(ctx, actorId, beRewardUnitsCalcInfo, beRewardOptionDisplayDetails, appPlatform, appVersion, userAndAppAttributes)
	if feRewardUnitsCalculationInfo == nil {
		return nil
	}

	infoCarousel := &feRewardsPb.ClaimedRewardDetails_AdditionalInfoCarousel{
		Title:     feRewardUnitsCalculationInfo.GetTitle(),
		InfoCards: nil,
	}

	for idx, entry := range feRewardUnitsCalculationInfo.GetRewardUnitsCalculationEntries() {
		if idx == 0 {
			continue
		}

		infoCarousel.InfoCards = append(infoCarousel.GetInfoCards(), &feRewardsPb.ClaimedRewardDetails_AdditionalInfoCarousel_AdditionalInfoCard{
			Value: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("+%v", entry.GetDelta()), entry.GetDisplayDetails().GetDeltaColor(), commontypes.FontStyle_NUMBER_L),
				},
				LeftImgTxtPadding: 4,
				LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(entry.GetDisplayDetails().GetIconUrl(), 20, 20),
			},
			InfoText:     entry.GetDisplayDetails().GetTitle(),
			BgColor:      widget.GetBlockBackgroundColour(entry.GetDisplayDetails().GetBgColor()),
			DividerColor: widget.GetBlockBackgroundColour("#646464"),
		})
	}

	return infoCarousel
}

func (r *RewardService) getClaimedRewardDescriptionList(chosenOption *feRewardsPb.RewardOption) *feRewardsPb.ClaimedRewardDetails_DescriptionsList {
	var descriptionListParagraphs []*feRewardsPb.ClaimedRewardDetails_DescriptionsList_DescriptionParagraph
	for _, htmlFormattedDetail := range chosenOption.GetDisplayDetails().GetHtmlFormattedDetails() {
		descriptionListParagraphs = append(descriptionListParagraphs, &feRewardsPb.ClaimedRewardDetails_DescriptionsList_DescriptionParagraph{
			Title:         commontypes.GetTextFromHtmlStringFontColourFontStyle(htmlFormattedDetail.GetHeader(), "#B9B9B9", commontypes.FontStyle_OVERLINE_2),
			ParagraphText: commontypes.GetTextFromHtmlStringFontColourFontStyle(htmlFormattedDetail.GetBody(), colors.ColorSnow, commontypes.FontStyle_BODY_4_PARA),
		})
	}

	if len(descriptionListParagraphs) > 0 {
		return &feRewardsPb.ClaimedRewardDetails_DescriptionsList{
			Paragraphs:   descriptionListParagraphs,
			DividerColor: widget.GetBlockBackgroundColour("#555555"),
		}
	}

	return nil
}
