package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/gamma/api/frontend/pay/transaction"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/be-common/pkg/events"
)

const (
	EventConfiguredAddFundsOptions        = "ConfiguredAddFundsOptions"
	EventTxnAmountChecksTriggered         = "TxnAmountChecksTriggered"
	EventSkippedAddFunds                  = "SkippedAddFunds"
	SUCCESS                               = "success"
	FAILURE                               = "failure"
	Payer                                 = "payer"
	Payee                                 = "payee"
	EventInitiatedCollectRequest          = "InitiatedCollectRequest"
	EventCollectRequestExpired            = "CollectRequestExpired"
	EventLoadedNewAddFundsScreenOnbServer = "LoadedNewAddFundsScreenOnbServer"
)

type LoadedNewAddFundsScreenOnbServer struct {
	UserId     string
	ProspectId string
	EventName  string
	SessionId  string
	AttemptId  string
	EventId    string
	DeviceId   string
	Timestamp  time.Time
	// Is user referred
	IsReferred bool
	// Amount entered by default for the user
	DefaultAmount int64
	// Has user entered via B2B flow
	IsSalaryB2B bool
	// Salary / non-salary flow loaded - salary has skip option
	FlowLoaded string
	// Version of the page load - v2.1, v2.2, v2.3
	Version string
}

func NewLoadedNewAddFundsScreenOnbServer(userId string, minorVersion string, defaultAmount int64, isReferred bool,
	isSalaryB2B bool, flowLoaded string) *LoadedNewAddFundsScreenOnbServer {
	return &LoadedNewAddFundsScreenOnbServer{
		UserId:        userId,
		ProspectId:    "",
		SessionId:     "",
		AttemptId:     "",
		EventId:       uuid.New().String(),
		EventName:     EventLoadedNewAddFundsScreenOnbServer,
		Timestamp:     time.Now(),
		IsReferred:    isReferred,
		DefaultAmount: defaultAmount,
		IsSalaryB2B:   isSalaryB2B,
		FlowLoaded:    flowLoaded,
		Version:       getOnbAddFundsPageVersion(minorVersion),
	}
}

// method to get page version of onboarding add funds from the minor version
// since this event is fired only in the GetAddFundsScreenDetailsV2 RPC, we have assumed the major version to be 2
func getOnbAddFundsPageVersion(minorVersion string) string {
	switch minorVersion {
	case "ONE":
		return "2.1"
	case "TWO":
		return "2.2"
	default:
		return "2.0"
	}
}

func (s *LoadedNewAddFundsScreenOnbServer) GetEventType() string {
	return events.EventTrack
}

func (s *LoadedNewAddFundsScreenOnbServer) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *LoadedNewAddFundsScreenOnbServer) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *LoadedNewAddFundsScreenOnbServer) GetEventId() string {
	return s.EventId
}

func (s *LoadedNewAddFundsScreenOnbServer) GetUserId() string {
	return s.UserId
}

func (s *LoadedNewAddFundsScreenOnbServer) GetProspectId() string {
	return s.ProspectId
}

func (s *LoadedNewAddFundsScreenOnbServer) GetDeviceId() string {
	return s.DeviceId
}

func (s *LoadedNewAddFundsScreenOnbServer) GetEventName() string {
	return EventLoadedNewAddFundsScreenOnbServer
}

type ConfiguredAddFundsOptions struct {
	UserId     string
	ProspectId string
	EventName  string
	SessionId  string
	AttemptId  string
	EventId    string
	DeviceId   string
	Timestamp  time.Time
	EventType  string
	Status     string
	EntryPoint string
	Option     int
}

func NewConfiguredAddFundsOptions(timestamp time.Time, userId, status string, entryPoint transaction.UIEntryPoint, option int) *ConfiguredAddFundsOptions {
	return &ConfiguredAddFundsOptions{
		UserId:     userId,
		ProspectId: "",
		SessionId:  "",
		AttemptId:  "",
		EventId:    uuid.New().String(),
		Timestamp:  timestamp,
		EventType:  events.EventTrack,
		Status:     status,
		EntryPoint: entryPoint.String(),
		Option:     option,
	}
}

func getSuggestedAmount(suggestedAmount *config.SuggestedAmount) interface{} {
	suggestAmountResult := map[string]interface{}{}
	resultTags := map[int]interface{}{}
	for tagNo, tag := range suggestedAmount.Tags {
		resultTags[tagNo+1] = tag.Title
	}
	suggestAmountResult["Amount"] = suggestedAmount.Amount.Units
	suggestAmountResult["Tags"] = resultTags
	return suggestAmountResult
}

func (s *ConfiguredAddFundsOptions) GetEventType() string {
	return events.EventTrack
}

func (s *ConfiguredAddFundsOptions) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *ConfiguredAddFundsOptions) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *ConfiguredAddFundsOptions) GetEventId() string {
	return s.EventId
}

func (s *ConfiguredAddFundsOptions) GetUserId() string {
	return s.UserId
}

func (s *ConfiguredAddFundsOptions) GetProspectId() string {
	return s.ProspectId
}

func (s *ConfiguredAddFundsOptions) GetDeviceId() string {
	return s.DeviceId
}

func (s *ConfiguredAddFundsOptions) GetEventName() string {
	return EventConfiguredAddFundsOptions
}

type TxnAmountChecksTriggered struct {
	UserId        string
	ActorIdOf     string
	ProspectId    string
	EventName     string
	SessionId     string
	AttemptId     string
	DeviceId      string
	EventId       string
	Timestamp     time.Time
	EventType     string
	Status        string
	FailureReason string
}

func NewTxnAmountChecksTriggered(timestamp time.Time, userId, actorIdOf, status, failureReason string) *TxnAmountChecksTriggered {
	return &TxnAmountChecksTriggered{
		UserId:        userId,
		ActorIdOf:     actorIdOf,
		ProspectId:    "",
		SessionId:     "",
		AttemptId:     "",
		EventId:       uuid.New().String(),
		Timestamp:     timestamp,
		EventType:     events.EventTrack,
		Status:        status,
		FailureReason: failureReason,
	}
}

func (s *TxnAmountChecksTriggered) GetEventType() string {
	return events.EventTrack
}

func (s *TxnAmountChecksTriggered) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *TxnAmountChecksTriggered) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *TxnAmountChecksTriggered) GetEventId() string {
	return s.EventId
}

func (s *TxnAmountChecksTriggered) GetUserId() string {
	return s.UserId
}

func (s *TxnAmountChecksTriggered) GetProspectId() string {
	return s.ProspectId
}

func (s *TxnAmountChecksTriggered) GetDeviceId() string {
	return s.DeviceId
}

func (s *TxnAmountChecksTriggered) GetEventName() string {
	return EventTxnAmountChecksTriggered
}

type SkippedAddFunds struct {
	UserId     string
	ProspectId string
	EventName  string
	SessionId  string
	AttemptId  string
	DeviceId   string
	EventId    string
	Timestamp  time.Time
	EventType  string
	Stage      string
}

func NewSkippedAddFunds(timestamp time.Time, userId, stage string) *SkippedAddFunds {
	return &SkippedAddFunds{
		UserId:     userId,
		ProspectId: "",
		SessionId:  "",
		AttemptId:  "",
		EventId:    uuid.New().String(),
		Timestamp:  timestamp,
		EventType:  events.EventTrack,
		Stage:      stage,
	}
}

func (s *SkippedAddFunds) GetEventType() string {
	return events.EventTrack
}

func (s *SkippedAddFunds) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *SkippedAddFunds) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *SkippedAddFunds) GetEventId() string {
	return s.EventId
}

func (s *SkippedAddFunds) GetUserId() string {
	return s.UserId
}

func (s *SkippedAddFunds) GetProspectId() string {
	return s.ProspectId
}

func (s *SkippedAddFunds) GetDeviceId() string {
	return s.DeviceId
}

func (s *SkippedAddFunds) GetEventName() string {
	return EventSkippedAddFunds
}

type InitiatedCollectRequest struct {
	UserId         string
	ProspectId     string
	EventName      string
	SessionId      string
	AttemptId      string
	DeviceId       string
	EventId        string
	Timestamp      time.Time
	EventType      string
	AmountCategory string
	CollectType    string
	OrderId        string
	Workflow       string
	EntryPoint     string
	Status         string
}

func NewInitiateCollectRequest(timestamp time.Time, status, userId, amountCategory, collectType, orderId, workflow, entryPoint string) *InitiatedCollectRequest {
	return &InitiatedCollectRequest{
		UserId:         userId,
		ProspectId:     "",
		SessionId:      "",
		AttemptId:      "",
		EventId:        uuid.New().String(),
		Timestamp:      timestamp,
		EventType:      events.EventTrack,
		AmountCategory: "",
		CollectType:    collectType,
		OrderId:        orderId,
		Workflow:       workflow,
		EntryPoint:     entryPoint,
		Status:         status,
	}
}

func (s *InitiatedCollectRequest) GetEventType() string {
	return events.EventTrack
}

func (s *InitiatedCollectRequest) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *InitiatedCollectRequest) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *InitiatedCollectRequest) GetEventId() string {
	return s.EventId
}

func (s *InitiatedCollectRequest) GetUserId() string {
	return s.UserId
}

func (s *InitiatedCollectRequest) GetProspectId() string {
	return s.ProspectId
}

func (s *InitiatedCollectRequest) GetDeviceId() string {
	return s.DeviceId
}

func (s *InitiatedCollectRequest) GetEventName() string {
	return EventInitiatedCollectRequest
}

type CollectRequestExpired struct {
	UserId          string
	ProspectId      string
	EventName       string
	SessionId       string
	AttemptId       string
	DeviceId        string
	EventId         string
	Timestamp       time.Time
	EventType       string
	OrderId         string
	NumberOfNudges  int32
	CollectRaisedOn time.Time
	PayerType       string
}

func NewCollectRequestExpired(timestamp time.Time, userId, orderId, payerType string, numberOfNudges int32, collectRaisedOn time.Time) *CollectRequestExpired {
	return &CollectRequestExpired{
		UserId:          userId,
		ProspectId:      "",
		SessionId:       "",
		AttemptId:       "",
		EventId:         uuid.New().String(),
		Timestamp:       timestamp,
		EventType:       events.EventTrack,
		PayerType:       payerType,
		OrderId:         orderId,
		NumberOfNudges:  numberOfNudges,
		CollectRaisedOn: collectRaisedOn,
	}
}

func (s *CollectRequestExpired) GetEventType() string {
	return events.EventTrack
}

func (s *CollectRequestExpired) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *CollectRequestExpired) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *CollectRequestExpired) GetEventId() string {
	return s.EventId
}

func (s *CollectRequestExpired) GetUserId() string {
	return s.UserId
}

func (s *CollectRequestExpired) GetProspectId() string {
	return s.ProspectId
}

func (s *CollectRequestExpired) GetDeviceId() string {
	return s.DeviceId
}

func (s *CollectRequestExpired) GetEventName() string {
	return EventCollectRequestExpired
}
