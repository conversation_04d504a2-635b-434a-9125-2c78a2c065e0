//nolint:dupl
package transaction

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"strings"

	"github.com/samber/lo"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/cfg"

	"github.com/epifi/gamma/api/frontend/deeplink"
	orderServicePb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	typesUi "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config/genconf"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	errors2 "github.com/epifi/gamma/pkg/frontend/errors"

	"github.com/epifi/gamma/api/frontend/errors"

	"github.com/golang/protobuf/proto"

	"github.com/epifi/gamma/api/frontend/pay/transaction"
	"github.com/epifi/gamma/api/frontend/timeline"
)

const (
	title_delimiter = " \u2022 "

	minKycCreditCheckFailedTitle                  = "Alert! Approaching maximum transaction limit"
	minKycBalanceCheckFailedTitle                 = "Your account is almost at its deposit limit!"
	minKycAccountDurationCheckFailedTitle         = "This Fi Account Is No Longer Valid!"
	minKycCreditCheckFailedDescription            = "Your minimum-KYC account has a transaction limit of ₹2 lakh as per regulations. More transactions may breach this limit! To unlock an unlimited account, complete a video KYC verification call. Timings: 09:00 am - 10:00 pm."
	minKycCreditCheckFailedDescriptionForNewUser  = "Your minimum-KYC account has a transaction limit of ₹50,000 as per bank policies. More transactions may breach this limit! To unlock an unlimited account, complete a video KYC verification call. Timings: 09:00 am - 10:00 pm."
	minKycBalanceCheckFailedDescription           = "As a minimum KYC user, your account has a ₹1 lakh deposit limit as per regulations. Any future transaction may breach this limit. To unlock an unlimited account, complete a video verification call. Timings: 09:00 am - 10:00 pm."
	minKycBalanceCheckFailedDescriptionForNewUser = "As a minimum KYC user, your account has a ₹50,000 deposit limit as bank policies. Any future transaction may breach this limit. To unlock an unlimited account, complete a video verification call. Timings: 09:00 am - 10:00 pm."
	minKycAccountDurationCheckFailedDescription   = "As a minimum KYC user, your account is only valid for %v days. To restore access, unlock a full KYC account. It’s free — just complete a quick video verification call. Timings: 09:00 am - 10:00 pm."

	minKycSoftNudgeBalanceLimitTitle       = "Your account is almost at its deposit limit!"
	minKycSoftNudgeBalanceLimitDescription = "As a min KYC user, your account has a ₹%v deposit limit as per bank policies. Any future transaction may breach the limit. Complete KYC to upgrade to an unlimited account."
	// nolint: gosec
	minKycSoftNudgeCreditLimitDescription          = "Your min KYC account has a transaction limit of ₹%v as per regulations. More transactions may breach this limit. Complete KYC to upgrade to a full account"
	micKycSoftNudgeAccountDurationLimitDescription = "Complete KYC to avoid account freeze. This means you will not be able to use your Fi Federal savings account, because yours is a minimum KYC account."

	accountBlockedForAddFundTitle       = "Your Account Is Blocked!"
	accountBlockedForAddFundDescription = "Your Account Is Blocked! Adding Funds Is Not Allowed For This Account."
)

type PayErrorViewMapping struct {
	StatusCode                 string
	StatusCodeDescriptionPayer string
	StatusCodeDescriptionPayee string
}

type PayErrorViewJsonMappings struct {
	PayErrorViewJsonMappings []PayErrorViewJsonMapping `json:"StatusCode"`
}

type PayErrorViewJsonMapping struct {
	StatusCode  string `json:"StatusCode"`
	Title       string `json:"Title"`
	SubTitle    string `json:"SubTitle"`
	Description string `json:"Description"`
	// CanShowToPayerIfItsPayeeError is a boolean value which tells if the error view can be shown to payer if its payee error
	CanShowToPayerIfItsPayeeError bool   `json:"CanShowToPayerIfItsPayeeError"`
	Action1                       string `json:"Action1"`
	Action2                       string `json:"Action2"`
	Cta1                          string `json:"Cta1"`
	Cta2                          string `json:"Cta2"`
}

var (
	ErrorViewIconUrl                        string = "https://epifi-icons.pointz.in/notifications/red-icon.png"
	statusCodeToErrorViewMap                map[string]*transaction.ErrorView
	statusCodeToBottomSheetCardErrorViewMap map[string]*errors.BottomSheetCardErrorView
	statusCodeToReceiptErrorInfoMap         map[string]*transaction.GetOrderReceiptResponse_ReceiptErrorInfo
	// map which helps in whitelisting error-codes which can be shown to the Payer even though they are of the Payee.
	// for eg, when a transaction fails because of Payee side issue and that error code is also present in this map(whitelisted) then we can show that error to Payer
	statusCodeToCanShowToPayerIfItsPayeeErrorMap map[string]bool
	actionToTimelineActionTypeMap                = map[string]timeline.TimelineAction_ActionType{
		"Done":          timeline.TimelineAction_DONE,
		"Retry":         timeline.TimelineAction_RETRY,
		"Forgot PIN":    timeline.TimelineAction_FORGOT_PIN,
		"Reset PIN":     timeline.TimelineAction_RESET_PIN,
		"Get Help":      timeline.TimelineAction_GET_HELP,
		"Add Funds":     timeline.TimelineAction_TRANSFER_IN,
		"Retry later":   timeline.TimelineAction_CANCEL,
		"Retry Payment": timeline.TimelineAction_RETRY,
		"Ok, got it":    timeline.TimelineAction_DONE,
		"Try later":     timeline.TimelineAction_CANCEL,
	}
	actionToCTATypeMap = map[string]errors.CTA_Type{
		"Done":          errors.CTA_DONE,
		"Retry":         errors.CTA_RETRY,
		"Get Help":      errors.CTA_GET_HELP,
		"Enable Vpa":    errors.CTA_ENABLE_VPA,
		"Forgot PIN":    errors.CTA_FORGOT_PIN,
		"Reset PIN":     errors.CTA_RESET_PIN,
		"Retry later":   errors.CTA_CANCEL,
		"Retry Payment": errors.CTA_RETRY,
		"Ok, got it":    errors.CTA_DONE,
		"Try later":     errors.CTA_CANCEL,
	}

	minKYCAddFundsFundsTimelineActions = []*timeline.TimelineAction{
		{
			DisplayValue:     "Learn More",
			Action:           timeline.TimelineAction_ADD_FUNDS_MIN_KYC_CHECK_FAIL,
			ActionPrecedence: timeline.TimelineAction_PRIMARY,
		},
	}

	minKYCAddFundsCTAs = []*errors.CTA{
		{
			Type:         errors.CTA_DONE,
			Text:         "Done",
			DisplayTheme: errors.CTA_PRIMARY,
		},
		{
			Type:   errors.CTA_CUSTOM,
			Text:   "Learn More",
			Action: vkycDeeplink,
		}}

	DefaultBottomSheetCardErrorView = errors2.NewBottomSheetCardErrorView("UPI123", "Request Failed", "",
		"Something went wrong")

	// default error view to be used in case we dont find the mapping between status code to error view
	// or if we face internal errors
	DefaultErrorView = &transaction.ErrorView{
		Title:       "Transaction Failed",
		Description: "Something went wrong",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}

	defaultReceiptErrorInfo = &transaction.GetOrderReceiptResponse_ReceiptErrorInfo{
		Title: &typesUi.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    "#E31B22",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Transaction failed"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
				},
			},
			LeftVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/tiering/add_funds/red_cross.png",
					},
						Properties: &commontypes.VisualElementProperties{
							Width:  16,
							Height: 16,
						},
					},
				},
			},
			LeftImgTxtPadding: 4,
		},
		Description: &commontypes.Text{
			FontColor:    "#444444",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Something went wrong."},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
		},
		BgColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{
				BlockColour: "#EFF2F6",
			}},
	}

	DefaultErrorViewOrderStatusCheck = &transaction.ErrorView{
		Title:       "Payment In Progress!",
		Description: "Please wait while we confirm the status.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}

	DefaultErrorViewOrderCreation = &transaction.ErrorView{
		Title:       "Transaction initiation failure!",
		Description: "Sorry, server maintenance in progress! Please retry in some time.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}

	AmountMisMatchErrorView = &transaction.ErrorView{
		Title:       "Transaction initiation failure!",
		Description: "Sorry Something doesn't seem right!",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}

	AddFundsNameMatchFailed = &transaction.ErrorView{
		Title:       "We spotted a name mismatch!",
		Description: "To make this payment, please retry by entering the UPI ID linked to your bank account",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}

	AddFundsMinKYCCreditCheckFailed = &transaction.ErrorView{
		Title:       minKycCreditCheckFailedTitle,
		Description: minKycCreditCheckFailedDescription,
		IconUrl:     ErrorViewIconUrl,
		Actions:     minKYCAddFundsFundsTimelineActions,
	}

	AddFundsMinKYCDurationCheckFailed = &transaction.ErrorView{
		Title:       minKycAccountDurationCheckFailedTitle,
		Description: fmt.Sprintf(minKycAccountDurationCheckFailedDescription, vkycPkg.AccountClosureDaysLimit),
		IconUrl:     ErrorViewIconUrl,
		Actions:     minKYCAddFundsFundsTimelineActions,
	}

	AddFundsMinKYCBalanceCheckFailed = &transaction.ErrorView{
		Title:       minKycBalanceCheckFailedTitle,
		Description: minKycBalanceCheckFailedDescription,
		IconUrl:     ErrorViewIconUrl,
		Actions:     minKYCAddFundsFundsTimelineActions,
	}

	ProtocolUnavailable = &transaction.ErrorView{
		Title:       "Transaction Initiation Failed",
		Description: "We are currently facing technical issues with our partner bank. Please try again later.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}

	PayerVPADisabled = &transaction.ErrorView{
		Title:       "UPI ID is disabled",
		Description: "The UPI ID entered has been disabled by the user. Please find an active UPI ID associated with the beneficiary.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}
	PayeeVPADisabled = &transaction.ErrorView{
		Title:       "Transaction Failed",
		Description: "Payee VPA is disabled",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}

	PaymentBlocked = &transaction.ErrorView{
		Title:       "Payment blocked",
		Description: "Can't pay to this user",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}

	VpaDisabledErrorView = &transaction.ErrorView{
		Title:       "Transaction failed",
		Description: "Your UPI ID is disabled. Please enable UPI ID and try again.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Enable Vpa",
				Action:           timeline.TimelineAction_ENABLE_VPA,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	UpiTransactionCountExceededInLimitWindowErrorView = &transaction.ErrorView{
		Title:       "You've exceeded UPI's daily transaction limit 🚨",
		Description: "UPI only allows 10 transactions in 24 hours. Please retry after sometime or use bank account & IFSC details for transfer.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	UpiTotalTransactedAmountExceededInNewUserCoolDownPeriodErrorView = &transaction.ErrorView{
		Title:       "Cooldown Phase activated ⛄",
		Description: "After a device is registered, your account enters a 24-hour cooldown phase! During this period, you can only transact upto ₹5000. Please transfer a smaller amount or try again in 24 hours.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	UpiTxnAmountExceededErrorView = &transaction.ErrorView{
		Title: "You've exceeded UPI's per transaction limit 🚨",
		Description: "UPI has a ₹1,00,000 limit on a single transactions. " +
			"Please change the amount or use bank account & IFSC details for transfer.",
		IconUrl: ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	UpiTotalTransactedAmountExceededInLimitWindowErrorView = &transaction.ErrorView{
		Title: "You've exceeded UPI's amount transfer limit 🚨",
		Description: "UPI has a ₹1,00,000 limit on transactions for 24 hour period. " +
			"Please retry after sometime or use bank account & IFSC details for transfer.",
		IconUrl: ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	UpiTotalTransactedAmountExceededAfterPinResetErrorView = &transaction.ErrorView{
		Title: "Your account's in a cooldown phase 🚨",
		Description: "Each time you change a Secure (UPI) PIN, there's a 12-hour cooldown phase! " +
			"During this period, your account can only transact upto ₹5000. " +
			"Please transfer a smaller amount or try again in 12 hours.",
		IconUrl: ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	TotalTransactedAmountExceededInCoolDownPeriodErrorView = &transaction.ErrorView{
		Title:       "Cooldown Phase activated ⛄",
		Description: "Your account is in cooldown phase. During this period, you can only transact upto ₹10000. Please transfer a smaller amount or try again in 24 hours.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	TotalTransactedAmountExceededInNewUserCoolDownPeriodErrorView = &transaction.ErrorView{
		Title:       "Cooldown Phase activated ⛄",
		Description: "After a device is registered, your account enters a 24-hour cooldown phase! During this period, you can only transact upto ₹10000. Please transfer a smaller amount or try again in 24 hours.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	TotalTransactedAmountExceededInDeviceCoolDownPeriodErrorView = &transaction.ErrorView{
		Title:       "Cooldown Phase activated ⛄",
		Description: "After your registered device is updated, your account enters a 24-hour cooldown phase! During this period, you can only transact upto ₹10000. Please transfer a smaller amount or try again in 24 hours.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	TotalTransactedAmountExceededInEmailCoolDownPeriodErrorView = &transaction.ErrorView{
		Title:       "Cooldown Phase activated ⛄",
		Description: "After your registered email-id is updated, your account enters a 24-hour cooldown phase! During this period, you can only transact upto ₹10000. Please transfer a smaller amount or try again in 24 hours.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	TotalTransactedAmountExceededInMobileCoolDownPeriodErrorView = &transaction.ErrorView{
		Title:       "Cooldown Phase activated ⛄",
		Description: "After your registered mobile number is updated, your account enters a 24-hour cooldown phase! During this period, you can only transact upto ₹10000. Please transfer a smaller amount or try again in 24 hours.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	BlockedActorErrorView = &transaction.ErrorView{
		Title:       "Transaction Declined",
		Description: "Transaction with this user is not allowed",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{

			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	CollectAmountExceededErrorView = &transaction.ErrorView{
		Title:    "Amount Limit Exceeded",
		SubTitle: "Cannot raise collect request for greater than 5,000",
		IconUrl:  ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}

	CollectVelocityLimitExceededErrorView = &transaction.ErrorView{
		Title:       "Collect requests limit exceeded",
		SubTitle:    "Five collect request can be raised within a time window of 24hrs",
		Description: "",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}

	CoolOffValidationFailedErrorView = &transaction.ErrorView{
		Title:       "Device in cool off period",
		SubTitle:    "You can make payment of maximum 5000 within 24 hrs",
		Description: "",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}

	URNAmountLimitExceededErrorView = &transaction.ErrorView{
		Title:       "Payment Failed",
		SubTitle:    "Payment limit exceeded to this QR/Intent",
		Description: "",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}

	ZeroAmountErrorView = &transaction.ErrorView{
		Title:       "Transaction not allowed",
		Description: "Looks like it is a zero value transaction. Please check the amount and try again.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
		},
	}

	RetryActionButton = &timeline.TimelineAction{
		Action:           timeline.TimelineAction_RETRY,
		ActionPrecedence: timeline.TimelineAction_PRIMARY,
		DisplayValue:     "TRY AGAIN",
	}

	ChangeMethodActionButton = &timeline.TimelineAction{
		Action:           timeline.TimelineAction_CHANGE_PAYMENT_METHOD,
		ActionPrecedence: timeline.TimelineAction_SECONDARY,
		DisplayValue:     "CHANGE METHOD",
	}

	csisErrorView = func(desc string) *errors.ErrorView {
		return &errors.ErrorView{
			Type: errors.ErrorViewType_BOTTOM_SHEET,
			Options: &errors.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errors.BottomSheetErrorView{
					ErrorCode:   "",
					Title:       "Transaction failed",
					Subtitle:    "",
					Description: desc,
					Ctas:        nil,
				},
			},
		}
	}

	addFundsMinKYCCreditLimitCheckFailedErrorView = func() *errors.ErrorView {
		return &errors.ErrorView{
			Type: errors.ErrorViewType_BOTTOM_SHEET,
			Options: &errors.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errors.BottomSheetErrorView{
					ErrorCode:   "",
					Title:       minKycCreditCheckFailedTitle,
					Subtitle:    "",
					Description: minKycCreditCheckFailedDescription,
					Ctas:        minKYCAddFundsCTAs,
				},
			},
		}
	}

	addFundsMinKYCBalanceCheckFailedErrorView = func() *errors.ErrorView {
		return &errors.ErrorView{
			Type: errors.ErrorViewType_BOTTOM_SHEET,
			Options: &errors.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errors.BottomSheetErrorView{
					ErrorCode:   "",
					Title:       minKycBalanceCheckFailedTitle,
					Subtitle:    "",
					Description: minKycBalanceCheckFailedDescription,
					Ctas:        minKYCAddFundsCTAs,
				},
			},
		}
	}

	addFundsMinKYCAccountDurationCheckFailedErrorView = func() *errors.ErrorView {
		return &errors.ErrorView{
			Type: errors.ErrorViewType_BOTTOM_SHEET,
			Options: &errors.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errors.BottomSheetErrorView{
					ErrorCode:   "",
					Title:       minKycAccountDurationCheckFailedTitle,
					Subtitle:    "",
					Description: fmt.Sprintf(minKycAccountDurationCheckFailedDescription, vkycPkg.AccountClosureDaysLimit),
					Ctas:        minKYCAddFundsCTAs,
				},
			},
		}
	}

	addFundsDownTimeErrorView = func(desc string) *errors.ErrorView {
		return &errors.ErrorView{
			Type: errors.ErrorViewType_BOTTOM_SHEET,
			Options: &errors.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errors.BottomSheetErrorView{
					ErrorCode:   "",
					Title:       "We are under maintenance!",
					Subtitle:    "",
					Description: desc,
					Ctas:        nil,
				},
			},
		}
	}

	addFundsPermissionDeniedErrorView = func() *errors.ErrorView {
		return &errors.ErrorView{
			Type: errors.ErrorViewType_BOTTOM_SHEET,
			Options: &errors.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errors.BottomSheetErrorView{
					ErrorCode:   "",
					Title:       "We are under maintenance!",
					Subtitle:    "",
					Description: "Our team is working very hard to fix this issue. Please try again after some time.",
					Ctas:        nil,
				},
			},
		}
	}

	AddFundsPermissionDenied = &transaction.ErrorView{
		Title:       "We are under maintenance!",
		Description: "Our team is working very hard to fix this issue. Please try again after some time.",
	}

	AddFundsDownTime = func(desc string) *transaction.ErrorView {
		return &transaction.ErrorView{
			Title:       "We are under maintenance!",
			Description: desc,
		}
	}

	internalErrorView = func() *errors.ErrorView {
		return &errors.ErrorView{
			Type: errors.ErrorViewType_BOTTOM_SHEET,
			Options: &errors.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errors.BottomSheetErrorView{
					ErrorCode:   "",
					Title:       "Transaction failed.",
					Subtitle:    "",
					Description: "",
					Ctas:        nil,
				},
			},
		}
	}

	totalTransactionAmountExceededForUserInGivenTimeDuration = &transaction.ErrorView{
		Title: "You've exceeded total amount transfer limit 🚨",
		Description: "Total transaction amount has a ₹10,00,000 limit for a 24 hour period. " +
			"Please retry after sometime.",
		IconUrl: ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}
	upiPaymentsInUnhealthyState = &transaction.ErrorView{
		Title:       "Server issue at bank side 🚨",
		Description: "This payment may fail due to high UPI failure rate at remitter bank. Please try again later.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	neftPaymentsInUnhealthyState = &transaction.ErrorView{
		Title:       "Server issue at bank side 🚨",
		Description: "This payment may fail due to high NEFT failure rate at remitter bank. Please try again later.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	impsPaymentsInUnhealthyState = &transaction.ErrorView{
		Title:       "Server issue at bank side 🚨",
		Description: "This payment may fail due to high IMPS failure rate at remitter bank. Please try again later.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	rtgsPaymentsInUnhealthyState = &transaction.ErrorView{
		Title:       "Server issue at bank side 🚨",
		Description: "This payment may fail due to high RTGS failure rate at remitter bank. Please try again later.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	intraPaymentsInUnhealthyState = &transaction.ErrorView{
		Title:       "Server issue at bank side 🚨",
		Description: "This payment may fail due to high INTRA failure rate at remitter bank. Please try again later.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	beneficiaryInCooldownPhase = &transaction.ErrorView{
		Title:       "Added a new beneficiary?",
		Description: "You can only transact up to ₹1,00,000 with them. Try transferring a smaller amount or retry tomorrow. This limitation only applies for a 24-hour cooldown period.",
		IconUrl:     ErrorViewIconUrl,
		Actions: []*timeline.TimelineAction{
			{
				DisplayValue:     "Get Help",
				Action:           timeline.TimelineAction_GET_HELP,
				ActionPrecedence: timeline.TimelineAction_PRIMARY,
			},
			{
				DisplayValue:     "Done",
				Action:           timeline.TimelineAction_DONE,
				ActionPrecedence: timeline.TimelineAction_SECONDARY,
			},
		},
	}

	chequeFailureGenericReceiptErrorView = &transaction.GetOrderReceiptResponse_ReceiptErrorInfo{
		Title: &typesUi.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    "#E31B22",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Cheque bounced, amount not credited"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
				},
			},
			LeftVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/tiering/add_funds/red_cross.png",
					},
						Properties: &commontypes.VisualElementProperties{
							Width:  16,
							Height: 16,
						},
					},
				},
			},
			LeftImgTxtPadding: 4,
		},
		Description: &commontypes.Text{
			FontColor:    "#444444",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "The cheque could not be processed. Please check with the payer for further details."},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
		},
		BgColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{
				BlockColour: "#EFF2F6",
			},
		},
	}

	enachFailureGenericReceiptErrorView = &transaction.GetOrderReceiptResponse_ReceiptErrorInfo{
		Title: &typesUi.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    "#E31B22",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "eNACH mandate failed"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
				},
			},
			LeftVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/tiering/add_funds/red_cross.png",
					},
						Properties: &commontypes.VisualElementProperties{
							Width:  16,
							Height: 16,
						},
					},
				},
			},
			LeftImgTxtPadding: 4,
		},
		Description: &commontypes.Text{
			FontColor:    "#444444",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Your mandate request couldn’t be processed. Contact your provider for further assistance."},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
		},
		BgColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{
				BlockColour: "#EFF2F6",
			},
		},
	}
)

// loads the error view from the specified json file path
// looks for the vendor and call the vendor specific methods to map the status codes
func LoadPayErrorView(errorViewFilePath string) error {
	initErrorViewMap()
	cfgDir, err := cfg.GetConfigDir()
	if err != nil {
		return fmt.Errorf("could not get config directory: %w", err)
	}
	file, err := ioutil.ReadFile(filepath.Join(cfgDir, errorViewFilePath))
	if err != nil {
		return fmt.Errorf("failed to open error view mapping file, %v", err)
	}
	data := PayErrorViewJsonMappings{}
	err = json.Unmarshal(file, &data)
	if err != nil {
		return fmt.Errorf("failed to unmarshal error view json to error view mappings, %v", err)
	}
	for i := range data.PayErrorViewJsonMappings {
		parseToMap(&data.PayErrorViewJsonMappings[i])
		parseToBottomSheetCardMap(&data.PayErrorViewJsonMappings[i])
		parseToReceiptInfoMap(&data.PayErrorViewJsonMappings[i])
		parseToCanShowToPayerIfItsPayeeErrorMap(&data.PayErrorViewJsonMappings[i])
	}
	return nil
}

// returns error view for the given status code
// return default error view in case the status code to error view mapping doesn”t exist in the map
// if we want to show payee side error to payer (in case of failed transaction because of payee side issue)
// use getPrioritisedErrorCode method to get the prioritised error code
func GetErrorView(statusCode string) *transaction.ErrorView {
	errorView := &transaction.ErrorView{}
	if fetchedErrorView, ok := statusCodeToErrorViewMap[statusCode]; ok {
		copyErrorView(errorView, fetchedErrorView)
		errorView.Title = errorView.GetTitle() + title_delimiter + statusCode
		errorView.IconUrl = ErrorViewIconUrl
		return errorView
	}
	errorView = proto.Clone(DefaultErrorView).(*transaction.ErrorView)
	if statusCode != "" {
		errorView.Title = errorView.GetTitle() + title_delimiter + statusCode
	}
	return errorView
}

// initialises the error view map
func initErrorViewMap() {
	statusCodeToErrorViewMap = make(map[string]*transaction.ErrorView)
	statusCodeToBottomSheetCardErrorViewMap = make(map[string]*errors.BottomSheetCardErrorView)
	// also adding statusCodeToReceiptErrorInfoMap loading here
	statusCodeToReceiptErrorInfoMap = make(map[string]*transaction.GetOrderReceiptResponse_ReceiptErrorInfo)
	statusCodeToCanShowToPayerIfItsPayeeErrorMap = make(map[string]bool)
}

// creates the mapping between status codes and error view
func parseToMap(errorViewJsonMapping *PayErrorViewJsonMapping) {
	var actions []*timeline.TimelineAction

	errorView := &transaction.ErrorView{
		Title:       errorViewJsonMapping.Title,
		SubTitle:    errorViewJsonMapping.SubTitle,
		Description: errorViewJsonMapping.Description,
	}

	if errorViewJsonMapping.Action1 != "" {
		actions = append(actions, &timeline.TimelineAction{
			Action:           actionToTimelineActionTypeMap[errorViewJsonMapping.Action1],
			ActionPrecedence: timeline.TimelineAction_PRIMARY,
			DisplayValue:     errorViewJsonMapping.Action1,
		})
	}

	if errorViewJsonMapping.Action2 != "" {
		actions = append(actions, &timeline.TimelineAction{
			Action:           actionToTimelineActionTypeMap[errorViewJsonMapping.Action2],
			ActionPrecedence: timeline.TimelineAction_SECONDARY,
			DisplayValue:     errorViewJsonMapping.Action2,
		})
	}

	// add the default action if no action is present
	if len(actions) == 0 {
		actions = append(actions, &timeline.TimelineAction{
			Action:           timeline.TimelineAction_DONE,
			ActionPrecedence: timeline.TimelineAction_PRIMARY,
			DisplayValue:     "Done",
		})
	}
	errorView.Actions = actions

	statusCodeToErrorViewMap[errorViewJsonMapping.StatusCode] = errorView
}

func copyErrorView(to, from *transaction.ErrorView) {
	to.Title = from.Title
	to.SubTitle = from.SubTitle
	to.Description = from.Description
	to.IconUrl = from.IconUrl
	to.Actions = from.Actions
}

// creates the mapping between status codes and bottom sheet card error view
func parseToBottomSheetCardMap(errorViewJsonMapping *PayErrorViewJsonMapping) {

	var ctas []*errors.CTA

	if errorViewJsonMapping.Action1 != "" {
		ctas = append(ctas, &errors.CTA{
			Type:         actionToCTATypeMap[errorViewJsonMapping.Action1],
			Text:         errorViewJsonMapping.Action1,
			DisplayTheme: errors.CTA_PRIMARY,
		})
	}

	if errorViewJsonMapping.Action2 != "" {
		ctas = append(ctas, &errors.CTA{
			Type:         actionToCTATypeMap[errorViewJsonMapping.Action2],
			Text:         errorViewJsonMapping.Action2,
			DisplayTheme: errors.CTA_SECONDARY,
		})
	}

	// add the default cta if no cta is present
	if len(ctas) == 0 {
		ctas = append(ctas, &errors.CTA{
			Type:         errors.CTA_DONE,
			Text:         "Done",
			DisplayTheme: errors.CTA_PRIMARY,
		})
	}

	bottomSheetCardErrorView := &errors.BottomSheetCardErrorView{
		ErrorCode:   errorViewJsonMapping.StatusCode,
		Title:       errorViewJsonMapping.Title,
		Subtitle:    errorViewJsonMapping.SubTitle,
		Description: errorViewJsonMapping.Description,
		Ctas:        ctas,
	}

	statusCodeToBottomSheetCardErrorViewMap[errorViewJsonMapping.StatusCode] = bottomSheetCardErrorView
}

func GetBottomSheetCardErrorView(statusCode string) *errors.ErrorView {

	bottomSheetCardErrorView := &errors.BottomSheetCardErrorView{}
	if fetchedErrorView, ok := statusCodeToBottomSheetCardErrorViewMap[statusCode]; ok {
		logger.DebugNoCtx("Error view fetched successfully for given status code", zap.String(logger.STATUS_CODE, statusCode))
		copyBottomSheetCardErrorView(bottomSheetCardErrorView, fetchedErrorView)
		bottomSheetCardErrorView.Title = bottomSheetCardErrorView.Title + title_delimiter + statusCode
		return &errors.ErrorView{
			Type: errors.ErrorViewType_BOTTOM_SHEET_CARD,
			Options: &errors.ErrorView_BottomSheetCardErrorView{
				BottomSheetCardErrorView: bottomSheetCardErrorView,
			},
		}
	}
	logger.ErrorNoCtx("Error view cannot be fetched for given status code", zap.String(logger.STATUS_CODE, statusCode))
	return DefaultBottomSheetCardErrorView
}

func copyBottomSheetCardErrorView(to, from *errors.BottomSheetCardErrorView) {
	to.Title = from.GetTitle()
	to.Subtitle = from.GetSubtitle()
	to.ErrorCode = from.GetErrorCode()
	to.Description = from.GetDescription()
	to.Ctas = from.GetCtas()
}

// get button CTA struct from cta string value in map
// We pass card ID as a blank string. We inject the actual value later
// ctaNumber is used to determine the order of the CTAs. It matters as the UI colour changes depending on that
func getDeeplinkCtaFromCtaString(cta string, cardId string) *deeplink.Deeplink {
	var deepLink *deeplink.Deeplink
	switch cta {
	case "Card Settings", "Enable online", "Enable International", "Enable Contactless", "Enable Usage", "Card Usage":
		deepLink = &deeplink.Deeplink{
			Screen: deeplink.Screen_CARD_USAGE_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CardUsageScreenOptions{
				CardUsageScreenOptions: &deeplink.CardUsageScreenOptions{
					CardId: cardId,
				},
			},
		}
	case "Reset ATM pin", "Request Card":
		deepLink = &deeplink.Deeplink{
			Screen: deeplink.Screen_CARD_SETTINGS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CardSettingsScreenOptions{
				CardSettingsScreenOptions: &deeplink.CardSettingsScreenOptions{
					CardId: cardId,
				},
			},
		}
	case "Add funds":
		deepLink = &deeplink.Deeplink{
			Screen: deeplink.Screen_TRANSFER_IN,
		}
	case "Modify limit", "Card Limits", "ATM Limits":
		deepLink = &deeplink.Deeplink{
			Screen: deeplink.Screen_CARD_LIMITS_HOME_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CardLimitHomeScreenOptions{
				CardLimitHomeScreenOptions: &deeplink.CardLimitHomeScreenOptions{
					CardId: cardId,
				},
			},
		}
	case "View Card Details":
		deepLink = &deeplink.Deeplink{
			Screen: deeplink.Screen_CARD_HOME_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CardHomeScreenOptions{
				CardHomeScreenOptions: &deeplink.CardHomeScreenOptions{
					CardId: cardId,
				},
			},
		}
	}

	return deepLink
}

// ctaNumber is used to determine the order of the CTAs. It matters as the UI colour changes depending on that
func getCtaFromCtaString(cta string, ctaNumber int) *typesUi.IconTextComponent {
	bgColour := "#FFFFFF"
	fontColour := "#00B899"
	if ctaNumber == 2 {
		bgColour = "#00B899"
		fontColour = "#FFFFFF"
	}

	return &typesUi.IconTextComponent{
		Texts: []*commontypes.Text{
			{
				FontColor:    fontColour,
				DisplayValue: &commontypes.Text_PlainString{PlainString: cta},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
			},
		},
		ContainerProperties: &typesUi.IconTextComponent_ContainerProperties{
			BgColor:       bgColour,
			LeftPadding:   16,
			RightPadding:  16,
			TopPadding:    10,
			BottomPadding: 10,
			CornerRadius:  20,
		},
	}
}

// creates the mapping between status codes & receipt error info
// we  do not add deeplink to CTA here as the object will vary depending on the type
func parseToReceiptInfoMap(errorViewJsonMapping *PayErrorViewJsonMapping) {
	// fetch CTAs
	var ctas []*typesUi.IconTextComponent
	if errorViewJsonMapping.Cta1 != "" {
		if errorViewJsonMapping.Cta2 != "" {
			ctas = append(ctas, getCtaFromCtaString(errorViewJsonMapping.Cta1, 1))
		} else {
			ctas = append(ctas, getCtaFromCtaString(errorViewJsonMapping.Cta1, 2))
		}
	}
	if errorViewJsonMapping.Cta2 != "" {
		ctas = append(ctas, getCtaFromCtaString(errorViewJsonMapping.Cta2, 2))
	}
	statusCodeToReceiptErrorInfoMap[errorViewJsonMapping.StatusCode] = &transaction.GetOrderReceiptResponse_ReceiptErrorInfo{
		Title: &typesUi.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    "#E31B22",
					DisplayValue: &commontypes.Text_PlainString{PlainString: errorViewJsonMapping.Title},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
				},
			},
			LeftVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/tiering/add_funds/red_cross.png",
					},
						Properties: &commontypes.VisualElementProperties{
							Width:  16,
							Height: 16,
						}},
				},
			},
			LeftImgTxtPadding: 4,
		},
		Description: &commontypes.Text{
			FontColor:    "#444444",
			DisplayValue: &commontypes.Text_PlainString{PlainString: errorViewJsonMapping.Description},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
		},
		Ctas: ctas,
		BgColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{
				BlockColour: "#EFF2F6",
			}},
	}
}

func GetReceiptErrorInfo(fiErrorCode string, cardId string, paymentProtocol payment.PaymentProtocol) *transaction.GetOrderReceiptResponse_ReceiptErrorInfo {
	var receiptErrorInfo = &transaction.GetOrderReceiptResponse_ReceiptErrorInfo{}
	if fetchedReceiptErrorInfo, ok := statusCodeToReceiptErrorInfoMap[fiErrorCode]; ok {
		copyReceiptErrorInfo(receiptErrorInfo, fetchedReceiptErrorInfo)
		// add deeplink to CTA
		if paymentProtocol == payment.PaymentProtocol_CARD {
			for _, cta := range fetchedReceiptErrorInfo.GetCtas() {
				if len(cta.GetTexts()) > 0 {
					cta.Deeplink = getDeeplinkCtaFromCtaString(cta.GetTexts()[0].GetDisplayValue().(*commontypes.Text_PlainString).PlainString, cardId)
				}
			}
		}
		return receiptErrorInfo
	}
	// in case an error code is not found, we return defaultReceiptErrorInfo
	receiptErrorInfo = proto.Clone(defaultReceiptErrorInfo).(*transaction.GetOrderReceiptResponse_ReceiptErrorInfo)
	return receiptErrorInfo
}

func copyReceiptErrorInfo(to, from *transaction.GetOrderReceiptResponse_ReceiptErrorInfo) {
	to.Title = from.GetTitle()
	to.Description = from.GetDescription()
	to.Ctas = from.GetCtas()
	to.BgColor = from.GetBgColor()
}

func parseToCanShowToPayerIfItsPayeeErrorMap(errorViewJsonMapping *PayErrorViewJsonMapping) {
	statusCodeToCanShowToPayerIfItsPayeeErrorMap[errorViewJsonMapping.StatusCode] = errorViewJsonMapping.CanShowToPayerIfItsPayeeError
}

// GetPrioritisedErrorCode returns the prioritised error code to be shown to the user
// for eg, a txn is failed because of payee side issue, we will get a payeeStatusCode
// if we are whitelisting the payeeStatusCode to be shown to payer, we will return payeeStatusCode
// otherwise we will fall back to payerStatusCode
func GetPrioritisedErrorCode(payerStatusCode string, payeeStatusCode string) string {
	if statusCodeToCanShowToPayerIfItsPayeeErrorMap[payeeStatusCode] {
		return payeeStatusCode
	}
	return payerStatusCode
}

func GetReceiptErrorInfoForChequeTxn(gconf *genconf.Config, orderReceipt *orderServicePb.GetReceiptDetailsResponse) *transaction.GetOrderReceiptResponse_ReceiptErrorInfo {
	if orderReceipt.GetPaymentProtocol() != payment.PaymentProtocol_CTS {
		return nil
	}
	if !lo.Contains(orderReceipt.GetOrderTags(), orderServicePb.OrderTag_CHEQUE_REVERSAL) {
		return nil
	}
	var receiptErrorInfo = proto.Clone(chequeFailureGenericReceiptErrorView).(*transaction.GetOrderReceiptResponse_ReceiptErrorInfo)

	gconf.OrderReceipt().ChequeBounceReasonToFailureTileMap().Range(func(errorReason string, value *genconf.TileTxnDescription) (continueRange bool) {
		if strings.Contains(strings.ToLower(orderReceipt.GetPrimaryTransaction().GetRemarks()), errorReason) {
			receiptErrorInfo.GetTitle().GetTexts()[0].DisplayValue = &commontypes.Text_PlainString{PlainString: value.TitleText()}
			receiptErrorInfo.GetDescription().DisplayValue = &commontypes.Text_PlainString{PlainString: value.DescriptionText()}
			return false
		}
		return true
	})

	return receiptErrorInfo
}

// GetReceiptErrorInfoForEnachTxn returns an error component to be
// displayed for failed eNACH transactions receipts based on the enach failure status.
// Ref figma: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=35498-27682&t=5B0SykTpH44C0YFV-0
func GetReceiptErrorInfoForEnachTxn(gconf *genconf.Config, orderReceipt *orderServicePb.GetReceiptDetailsResponse) *transaction.GetOrderReceiptResponse_ReceiptErrorInfo {
	if orderReceipt.GetPrimaryTransaction().GetPaymentProtocol() != payment.PaymentProtocol_ENACH ||
		orderReceipt.GetPrimaryTransaction().GetStatus() != payment.TransactionStatus_FAILED {
		return nil
	}

	var receiptErrorInfo = proto.Clone(enachFailureGenericReceiptErrorView).(*transaction.GetOrderReceiptResponse_ReceiptErrorInfo)

	if len(orderReceipt.GetPrimaryTransaction().GetDetailedStatus().GetDetailedStatusList()) > 0 {
		errorInfo := gconf.OrderReceipt().EnachFailedTxnStatusCodeToFailureTileMap().Get(orderReceipt.GetPrimaryTransaction().GetDetailedStatus().GetDetailedStatusList()[0].GetStatusCodePayer())
		if errorInfo != nil {
			receiptErrorInfo.GetTitle().GetTexts()[0].DisplayValue = &commontypes.Text_PlainString{PlainString: errorInfo.TitleText()}
			receiptErrorInfo.GetDescription().DisplayValue = &commontypes.Text_PlainString{PlainString: errorInfo.DescriptionText()}
		}
	}

	return receiptErrorInfo
}
