Application:
  Environment: "staging"
  Name: "mf_folio_reconciliation"

EpifiWealthDb:
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "./crdb/staging/"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"
