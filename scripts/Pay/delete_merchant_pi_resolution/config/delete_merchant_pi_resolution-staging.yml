Application:
  Environment: "staging"
  Name: "delete_merchant_pi_resolution"

MerchantDb:
  DbType: "PGDB"
  Name: "merchant"
  EnableDebug: false
  SSLMode: "disable"
  DbServerAlias: "PLUTUS_RDS"
  SecretName: "staging/rds/epifiplutus/merchant_dev_user"
  MaxOpenConn: 5
  MaxIdleConn: 3
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Aws:
  Region: "ap-south-1"


ConsulData:
  Key: "staging/script/pay/delete-merchant-pi-resolution"
  Address: "staging-consul.pointz.in"
  Scheme: "https"

MerchantRedis:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 14

MerchantPiIdPrefix: "merchant_pi_id_"
