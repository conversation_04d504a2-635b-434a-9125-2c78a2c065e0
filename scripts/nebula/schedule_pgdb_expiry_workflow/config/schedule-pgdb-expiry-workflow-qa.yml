Application:
  Environment: "qa"
  Name: "schedule-pgdb-expiry-workflow"
  Namespace : "qa-nebula"
  TaskQueue: "qa-nebula-task-queue"

Dbs:
  - Host: "postgres.cwlhyhiuk8ce.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "qa/rds/postgres"
    Name: "auth"
    SslMode: "disable"
  - Host: "postgres.cwlhyhiuk8ce.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "qa/rds/postgres/comms"
    Name: "comms"
    SslMode: "disable"
  - Host: "postgres.cwlhyhiuk8ce.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "qa/rds/postgres/inapphelp"
    Name: "inapphelp"
    SslMode: "disable"

  - Host: "postgres.cwlhyhiuk8ce.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "qa/rds/postgres/sherlock"
    Name: "sherlock"
    SslMode: "disable"

  - Host: "postgres.cwlhyhiuk8ce.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "qa/rds/epifimetis/fittt_admin_user"
    Name: "fittt"
    SslMode: "disable"

  - Host: "postgres.cwlhyhiuk8ce.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "qa/rds/postgres/insights"
    Name: "insights"
    SslMode: "disable"

  - Host: "postgres.cwlhyhiuk8ce.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "qa/rds/postgres/actor-insights"
    Name: "actor_insights"
    SslMode: "disable"

  - Host: "postgres.cwlhyhiuk8ce.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "qa/rds/postgres/budgeting_dev_user"
    Name: "budgeting"
    SslMode: "disable"

  - Host: "postgres.cwlhyhiuk8ce.ap-south-1.rds.amazonaws.com"
    Port: 5432
    Username: ""
    Password: "qa/rds/postgres/connected_account_dev_user"
    Name: "connected_account"
    SslMode: "disable"

TemporalCodecConfig:
  TemporalCodecAesKey: "qa/temporal/codec-encryption-key"
