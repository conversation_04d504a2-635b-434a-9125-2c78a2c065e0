package job

type Type int32

const (
	Unspecified                        Type = 0
	ProcessFiEligibleBase              Type = 1
	GenerateCcQr                       Type = 2
	InitiateCardRequest                Type = 3
	PhysicalCardRecon                  Type = 4
	CcCommsTest                        Type = 5
	DcExpiryUpdate                     Type = 6
	DcTxnDataFetch                     Type = 7
	DcTxnPublish                       Type = 8
	DcForexRefund                      Type = 9
	DcForexRefundFiPlusReplay          Type = 10
	CcCardTrackingRequestBackfill      Type = 11
	CcStatement                        Type = 12
	TriggerWelcomeOffer                Type = 13
	MaskingCardNumber                  Type = 15
	DcTxnDataFetchWithCountryCode      Type = 16
	CreditCardWorkflowsReporting       Type = 17
	DisableOffers                      Type = 19
	ExtendOfferExpiry                  Type = 20
	ProcessDcDeclineData               Type = 21
	UpdateCardRequestsOfferId          Type = 23
	CreditCardClosureIntimation        Type = 24
	UploadFilesToS3Bucket              Type = 25
	GetPanAadhaarLinkStatus            Type = 26
	DcVendorIdToAccNumber              Type = 27
	ForexRefundRecon                   Type = 28
	CbsIdPopulationForTxnId            Type = 29
	DcCardCreationRetry                Type = 30
	StatementGenerationAlerting        Type = 31
	DcReconUnsentRefunds               Type = 32
	DcPhysicalCardDispatchRecon        Type = 33
	DcContactlessSwitchOff             Type = 34
	ReconProcessedRefunds              Type = 35
	ForexRefundAlerting                Type = 36
	UpdatePaymentInstrument            Type = 37
	CcCardRequestReward                Type = 39
	DcPhysicalCardDispatchFailureAlert Type = 40
	AddFiRejectedUsers                 Type = 41
	TriggerStatementGeneration         Type = 42
	AddCCTransaction                   Type = 43
	TriggerReconcileWorkflows          Type = 44
	StatementDueDateMismatch           Type = 45
	UpdateCardReqAndStageStatus        Type = 46
	ManualStatementGeneration          Type = 47
	DecryptVgFiles                     Type = 48
	WelcomeOfferRewardIdJob            Type = 49
	OfferInvalidation                  Type = 50
	BillEraserRecon                    Type = 51
	CreditCardSecuredDepositIdJob      Type = 52
	CcUpdateAtmLimitJob                Type = 53
	CcUpdateRewardInfoInBill           Type = 54
	FiliteOnbResetJob                  Type = 55
	DedupeCheckingJob                  Type = 56
	TerminateAuthWfs                   Type = 57
	CreditCcOfferAddJob                Type = 58
	InvalidOnboardingDetection         Type = 59
	UserOnboardingDataPopulation       Type = 60
	CcUpdateCustomerDetailsAtM2pJob    Type = 61
	BlockAndReissueNewCC               Type = 62
	RegisterCustomerForCC              Type = 63
	UpdateCardDetailsAtBank            Type = 64
	DcRenewCard                        Type = 65
	BlockDebitCard                     Type = 66
	ImitateReissueCard                 Type = 67
	ProcessCardTransactionJob          Type = 68
	DeleteDcDynamoData                 Type = 69
	ChangeDcPinValidationParams        Type = 70
	GetActorCcShippingAddress          Type = 71
	TriggerWelcomeRewards              Type = 72
	DcPhysicalCardChargesReversal      Type = 73
	CCRecon                            Type = 74
	RotateTokenizerKeys                Type = 75
	ActivateCreditCard                 Type = 76
	GenerateStatement                  Type = 77
	TriggerFeeWaiver                   Type = 78
	BypassCcOnboarding                 Type = 79
	DeleteCardRequest                  Type = 80
	TriggerProcessCcTxnWf              Type = 81
	SendInAppNotification              Type = 82
	SendDpdEvent                       Type = 83
	InitiateAmcReportGeneration        Type = 84
	DCCorrectForexRefundDbJob          Type = 86
	DCDeleteAmcCardRequestJob          Type = 87
	VisaApisTest                       Type = 88
	DCInvestigateAwbMismatchJob        Type = 89
	DCOrderPhysicalCardWithChargesJob  Type = 90
	FetchDueData                       Type = 91
	CreateVendorRepaymentRecord        Type = 92
	CreditCardFedFibSmsScriptJob       Type = 93
	DcCorrectForexTcsClashJob          Type = 94
	CollectDCIssuanceApiTest           Type = 95
	CcProcessKycExpiryUpdate           Type = 96
	DcEnquireChargesCollectionStatus   Type = 97
)
