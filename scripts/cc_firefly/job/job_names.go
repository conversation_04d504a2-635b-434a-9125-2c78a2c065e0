package job

var (
	JobNames = map[string]Type{
		"PROCESS_FI_ELIGIBLE_BASE":                ProcessFiEligibleBase,
		"GENERATE_CC_QR":                          GenerateCcQr,
		"INITIATE_CARD_REQUEST":                   InitiateCardRequest,
		"PHYSICAL_CARD_RECON":                     <PERSON><PERSON>ardRecon,
		"CC_COMMS_TEST":                           <PERSON>cCommsTest,
		"DC_EXPIRY_UPDATE":                        D<PERSON><PERSON><PERSON><PERSON><PERSON>Update,
		"DC_TXN_DATA_FETCH_JOB":                   DcTxnDataFetch,
		"DC_TXN_PUBLISH_JOB":                      DcTxnPublish,
		"DC_FOREX_REFUND_JOB":                     D<PERSON>ForexRef<PERSON>,
		"DC_FOREX_REFUND_FI_PLUS_REPLAY_JOB":      DcForexRefundFiPlusReplay,
		"CC_CARD_TRACKING_REQUEST_BACKFILL_JOB":   CcCardTrackingRequestBack<PERSON>,
		"CC_STATEMENT_JOB":                        C<PERSON><PERSON>tatement,
		"TRIGGER_WELCOME_OFFER_JOB":               <PERSON><PERSON><PERSON><PERSON>comeOffer,
		"MASKING_CARD_NUMBER":                     MaskingCardNumber,
		"DC_TXN_DATA_FETCH_WITH_COUNTRY_CODE":     DcTxnDataFetchWithCountryCode,
		"CREDIT_CARD_WORKFLOWS_DAILY_REPORTING":   CreditCardWorkflowsReporting,
		"DISABLE_OFFERS":                          DisableOffers,
		"ADD_FI_REJECTED_USERS":                   AddFiRejectedUsers,
		"EXTEND_OFFER_EXPIRY":                     ExtendOfferExpiry,
		"DC_DECLINE_DATA_DUMP":                    ProcessDcDeclineData,
		"UPDATE_CARD_REQUESTS_OFFER_ID":           UpdateCardRequestsOfferId,
		"CARD_CLOSURE_INTIMATION":                 CreditCardClosureIntimation,
		"UPLOAD_FILE_TO_S3_BUCKET":                UploadFilesToS3Bucket,
		"GET_PAN_AADHAAR_LINK_STATUS":             GetPanAadhaarLinkStatus,
		"DC_VENDOR_ID_TO_CARD_NUMBER_MAPPING":     DcVendorIdToAccNumber,
		"FOREX_REFUND_RECON":                      ForexRefundRecon,
		"CBS_ID_POPULATION_FOR_TXN_ID":            CbsIdPopulationForTxnId,
		"DC_CARD_CREATION_RETRY":                  DcCardCreationRetry,
		"STATEMENT_GENERATION_ALERTING":           StatementGenerationAlerting,
		"DC_RECON_UNSENT_REFUNDS":                 DcReconUnsentRefunds,
		"DC_PHYSICAL_CARD_DISPATCH_RECON":         DcPhysicalCardDispatchRecon,
		"DC_CONTACTLESS_SWITCH_OFF":               DcContactlessSwitchOff,
		"PROCESSED_REFUNDS_RECON":                 ReconProcessedRefunds,
		"FOREX_REFUND_ALERTING":                   ForexRefundAlerting,
		"UPDATE_PAYMENT_INSTRUMENT_JOB":           UpdatePaymentInstrument,
		"CC_CARD_REQUEST_REWARD_JOB":              CcCardRequestReward,
		"DC_PHYSICAL_CARD_DISPATCH_FAILURE_ALERT": DcPhysicalCardDispatchFailureAlert,
		"TRIGGER_STATEMENT_WORKFLOW":              TriggerStatementGeneration,
		"ADD_CC_TRANSACTION":                      AddCCTransaction,
		"TRIGGER_RECON_WORKFLOW":                  TriggerReconcileWorkflows,
		"STATEMENT_DUE_DATE_MISMATCH_RECON":       StatementDueDateMismatch,
		"UPDATE_CARD_REQUEST_AND_STAGE_STATUS":    UpdateCardReqAndStageStatus,
		"MANUAL_STATEMENT_GENERATION":             ManualStatementGeneration,
		"DECRYPT_VG_FILES":                        DecryptVgFiles,
		"WELCOME_OFFER_REWARD_ID_JOB":             WelcomeOfferRewardIdJob,
		"INVALIDATE_CC_OFFERS":                    OfferInvalidation,
		"BILL_ERASER_RECON":                       BillEraserRecon,
		"CC_SECURED_DEPOSIT_ID_JOB":               CreditCardSecuredDepositIdJob,
		"CC_UPDATE_ATM_LIMIT_JOB":                 CcUpdateAtmLimitJob,
		"CC_UPDATE_REWARD_INFO_IN_BILL":           CcUpdateRewardInfoInBill,
		"RESET_FI_LITE_ONBOARDING":                FiliteOnbResetJob,
		"DEDUPE_CHECK":                            DedupeCheckingJob,
		"TERMINATE_AUTH_WORKFLOWS":                TerminateAuthWfs,
		"CC_OFFER_ADD_JOB":                        CreditCcOfferAddJob,
		"INVALID_ONBOARDING_DETECTION":            InvalidOnboardingDetection,
		"USER_ONBOARDING_DATA_POPULATION":         UserOnboardingDataPopulation,
		"CC_UPDATE_CUSTOMER_DETAILS_AT_M2P_JOB":   CcUpdateCustomerDetailsAtM2pJob,
		"BLOCK_AND_REISSUE_NEW_CC":                BlockAndReissueNewCC,
		"REGISTER_CUSTOMER_FOR_CC":                RegisterCustomerForCC,
		"UPDATE_CARD_DETAILS_AT_BANK":             UpdateCardDetailsAtBank,
		"DC_RENEW_CARD":                           DcRenewCard,
		"DC_BLOCK_CARD_JOB":                       BlockDebitCard,
		"IMITATE_REISSUE_CARD":                    ImitateReissueCard,
		"PROCESS_CARD_TRANSACTION":                ProcessCardTransactionJob,
		"DELETE_DC_DYNAMO_DATA":                   DeleteDcDynamoData,
		"CHANGE_DC_PIN_VALIDATION_PARAMS":         ChangeDcPinValidationParams,
		"GET_ACTOR_CC_SHIPPING_ADDRESS":           GetActorCcShippingAddress,
		"TRIGGER_WELCOME_REWARDS":                 TriggerWelcomeRewards,
		"DC_PHYSICAL_CARD_CHARGES_REVERSAL_JOB":   DcPhysicalCardChargesReversal,
		"CC_RECON":                                CCRecon,
		"ROTATE_TOKENIZER_KEYS":                   RotateTokenizerKeys,
		"ACTIVATE_CREDIT_CARD":                    ActivateCreditCard,
		"NP_STATEMENT_GENERATION":                 GenerateStatement,
		"TRIGGER_FEE_WAIVER":                      TriggerFeeWaiver,
		"BYPASS_CC_ONBOARDING":                    BypassCcOnboarding,
		"DELETE_CARD_REQUEST":                     DeleteCardRequest,
		"TRIGGER_PROCESS_CC_TXN_WF":               TriggerProcessCcTxnWf,
		"SEND_IN_APP_NOTIFICATION":                SendInAppNotification,
		"SEND_DPD_EVENTS":                         SendDpdEvent,
		"INVESTIGATE_AWB_MISMATCH":                DCInvestigateAwbMismatchJob,
		"INITIATE_AMC_REPORT_GENERATION":          InitiateAmcReportGeneration,
		"CORRECT_FOREX_REFUND_DB_JOB":             DCCorrectForexRefundDbJob,
		"DELETE_AMC_CARD_REQUEST":                 DCDeleteAmcCardRequestJob,
		"VISA_API_TEST_JOB":                       VisaApisTest,
		"DC_ORDER_PHYSICAL_CARD_WITH_CHARGES":     DCOrderPhysicalCardWithChargesJob,
		"FETCH_DUE_DATA_JOB":                      FetchDueData,
		"CREATE_VENDOR_REPAYMENT_RECORD":          CreateVendorRepaymentRecord,
		"CC_SMS_SCRIPT_JOB":                       CreditCardFedFibSmsScriptJob,
		"CORRECT_FOREX_TCS_CLASH_JOB":             DcCorrectForexTcsClashJob,
		"CC_PROCESS_KYC_EXPIRY_UPDATE":            CcProcessKycExpiryUpdate,
		"DC_ENQUIRY_CHARGES_COLLECTION_STATUS":    DcEnquireChargesCollectionStatus,
	}
)
