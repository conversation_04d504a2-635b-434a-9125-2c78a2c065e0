Application:
  Environment: "uat"
  Name: "purge_external_mf_holdings"

EpifiWealthDb:
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "./crdb/uat/"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "./crdb/uat/"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

EpifiWealthAnalyticsDb:
  AppName: "investment_analyser"
  StatementTimeout: 5m
  Name: "epifi_wealth_analytics"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms

Secrets:
  Ids:
    - EpifiWealthAnalyticsDbUsernamePassword: "uat/rds/postgres"

AWS:
  Region: "ap-south-1"

AnalyserRedisStore:
  IsSecure: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
