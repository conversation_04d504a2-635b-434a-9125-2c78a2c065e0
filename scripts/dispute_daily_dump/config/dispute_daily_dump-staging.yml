Application:
  Environment: "staging"
  Name: "dispute_daily_dump"

EpifiDb:
  AppName: "cx"
  StatementTimeout: 5s
  Name: "sherlock"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbUsernamePassword: "staging/rds/postgres14"

DisputeCreateTicketPublisher:
  QueueName: "staging-cx-dispute-create-ticket-queue"
