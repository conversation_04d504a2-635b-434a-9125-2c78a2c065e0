Application:
  Environment: "prod"
  Name: "s3_uploader"

AWS:
  Region: "ap-south-1"

Secrets:
  Ids:
    PrestoSecretsKey: "prod/onboarding/presto"
    DbUsernamePassword: "prod/rds/epifimetis/vendormapping"
    SlackOauthToken: "prod/onboarding/slack-oauth-token"
    RudderWriteKey: "prod/rudder/internal-writekey"


S3UploaderPrestoCelestialConfig:
  EPIFI_TECH:
    Username: "prod_presto_onb_system"
    Host: "presto-master-dp-ext.data-prod.epifi.in"
    Port: 8889
    Catalog: "hive"
    Schema: "dl_tech_epifi"
  RMS_TECH:
    Username: "prod_presto_onb_system"
    Host: "presto-master-dp-ext.data-prod.epifi.in"
    Port: 8889
    Catalog: "hive"
    Schema: "dl_rms"
  US_STOCKS_ALPACA:
    Username: "prod_presto_onb_system"
    Host: "presto-master-dp-ext.data-prod.epifi.in"
    Port: 8889
    Catalog: "hive"
    Schema: "dl_usstocks_alpaca"

S3UploaderPrestoConfig:
  Username: "prod_presto_onb_system"
  Host: "presto-master-dp-ext.data-prod.epifi.in"
  Port: 8889
  Catalog: "hive"
  Schema: "tech_epifi"

S3UploaderPrestoFederalConfig:
  Username: "prod_presto_onb_system"
  Host: "presto-master-dp-ext.data-prod.epifi.in"
  Port: 8889
  Catalog: "hive"
  Schema: "federal_epifi"

S3UploaderPrestoNRFederalConfig:
  Username: "prod_presto_onb_system"
  Host: "presto-master-dp-ext.data-prod.epifi.in"
  Port: 8889
  Catalog: "hive"
  Schema: "kyc_non_resident"

S3UploaderPrestoBankCustomerConfig:
  Username: "prod_presto_onb_system"
  Host: "presto-master-dp-ext.data-prod.epifi.in"
  Port: 8889
  Catalog: "hive"
  Schema: "bank_customer"

S3Conf:
  Bucket: "epifi-prod-snowflake-result-upload"

EpifiDb:
  DbType: "CRDB"
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 1
  MaxIdleConn: 1
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

VendormappingPgdb:
  DbType: "PGDB"
  Name: "vendormapping"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  SecretName: "prod/rds/epifimetis/vendormapping"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

UserStuckInVkycReview:
  fromEmailId: "<EMAIL>"
  fromEmailName: "Fi money"
  toEmailId: "<EMAIL>"

DobDiscrepancyEmailInfo:
  fromEmailId: "<EMAIL>"
  fromEmailName: "Fi money"
  toEmailId: "<EMAIL>"

FederalVkycUpdatePublisher:
  QueueName: "prod-vn-federal-vkyc-update-queue"

SlackOAuthToken: "********************************************************"

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false


AlertRoutingMap:
  onboarding:
    SlackChannelId: "C01NTC58Z35"
    SlackOnCallGroupId: "S05TE0KH5QQ"
  growth-experience:
    SlackChannelId: "C02UVU82RRA"
    SlackOnCallGroupId: "S08LF8GUTQC"
  cx:
    SlackChannelId: "C02UVU82RRA"
    SlackOnCallGroupId: "S08LF8GUTQC"
  lending:
    SlackChannelId: "C04GXEBG8F6"
    SlackOnCallGroupId: "S05S77W3V2R"
  pay:
    SlackChannelId: "C020HNXMSGH"
    SlackOnCallGroupId: "S05UWQU2HGA"
  central-growth:
    SlackChannelId: "C020HNXMSGH"
    SlackOnCallGroupId: "S05UWQU2HGA"
  wealth:
    SlackChannelId: "C0366ECLLHE"
    SlackOnCallGroupId: "S05713C487K"
