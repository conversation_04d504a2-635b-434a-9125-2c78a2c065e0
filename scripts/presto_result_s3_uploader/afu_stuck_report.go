// nolint: dupl
package main

import (
	"bytes"
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/jszwec/csvutil"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/slack-go/slack"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
)

type AFUStuckReport struct {
	slack *slack.Client
}

var (
	stuckDateFromFetch   = "2025-02-01"
	afuStuckState        = "OVERALL_STATUS_STUCK"
	afuStuckS3OutputPath = "PRESTO/AFU_ID/AFU/AFU_STUCK_STATE/%v.csv"
)

func (a *AFUStuckReport) GetQueryStorageInfo() []*QueryStorageInfo {
	args, query := afuStuckGetSnowflakeDetails()
	file := afuStuckGetS3File()
	var queryStorageInfos []*QueryStorageInfo
	queryStorageInfos = append(queryStorageInfos, &QueryStorageInfo{
		queryArgs:   args,
		prestoQuery: query,
		s3File:      file,
	})
	return queryStorageInfos
}

func afuStuckGetSnowflakeDetails() ([]interface{}, string) {
	args := []interface{}{afuStuckState, stuckDateFromFetch}
	query := "SELECT ID FROM AUTH_FACTOR_UPDATES WHERE overall_status = ? AND CAST(updated_at AS DATE) >= CAST(? AS DATE)"
	return args, query
}

func afuStuckGetS3File() string {
	s3OutputPathWithDate := fmt.Sprintf(afuStuckS3OutputPath, time.Now().Format("01-02-2006"))
	return s3OutputPathWithDate
}

type afuStuckDetail struct {
	AFUId string `csv:"AFU Id"`
}

func (a *AFUStuckReport) DoJob(afuIds []string, index int) error {
	var afuStuckDetails []*afuStuckDetail
	var actionableAFUInStuck int64
	ctx := context.Background()
	for _, afuId := range afuIds {
		actionableAFUInStuck++
		afuStuckDetails = append(afuStuckDetails, &afuStuckDetail{AFUId: afuId})
		time.Sleep(500 * time.Millisecond)
	}
	err := a.sendSlackAlert(afuStuckDetails, actionableAFUInStuck)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Error in sending slack alert"))
	}
	logger.Info(ctx, fmt.Sprintf("Sucessfully send slack alert for %v cases", actionableAFUInStuck))
	return nil
}

func (a *AFUStuckReport) sendSlackAlert(afuStuckDetails []*afuStuckDetail, actionableAFUInStuck int64) error {
	var (
		channelID = "C01NTC58Z35"
	)
	dataBytes, err := csvutil.Marshal(afuStuckDetails)
	if err != nil {
		return errors.Wrap(err, "error in csvUtil.Marshal")
	}
	dtString := datetime.TimestampToString(timestamppb.Now(), datetime.DATE_LAYOUT_YYYYMMDD, datetime.IST)
	title := fmt.Sprintf("Actionable AFU in stuck cases: %v", actionableAFUInStuck)
	fileName := fmt.Sprintf("AFU-in-stuck-%v.csv", dtString)
	csvReader := bytes.NewReader(dataBytes)
	params := slack.UploadFileV2Parameters{
		Reader:         csvReader,
		Filename:       fileName,
		FileSize:       csvReader.Len(),
		Title:          title,
		Channel:        channelID,
		InitialComment: "<@U032YMJQ3E0>",
	}
	_, err = a.slack.UploadFileV2(params)
	if err != nil {
		logger.ErrorNoCtx("error sending slack alert", zap.Error(err))
		return errors.Wrap(err, "error in slackClient.UploadFile")
	}
	return nil
}

func (a *AFUStuckReport) GetS3FileName() []string {
	var fileNames []string
	s3InputPathWithDate := fmt.Sprintf(afuStuckS3OutputPath, time.Now().Format("01-02-2006"))
	fileNames = append(fileNames, s3InputPathWithDate)
	return fileNames
}
