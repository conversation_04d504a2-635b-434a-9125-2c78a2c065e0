Application:
  Environment: "development"
  Name: "credit_report_purge"

EpifiDb:
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Tracing:
  Enable: true

FeatureEngineeringDb:
  DbType: "PGDB"
  AppName: "user"
  StatementTimeout: 5s
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

# This is the number of hours in 173 days (1 week less than 6 months) 173 * 24
PurgeIfConsentBeforeHrs: 4152
