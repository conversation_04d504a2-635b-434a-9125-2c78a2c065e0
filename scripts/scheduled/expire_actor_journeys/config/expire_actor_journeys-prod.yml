Application:
  Environment: "prod"
  Name: "expire_actor_journeys"

NudgeDb:
  Name: "nudge"
  StatementTimeout: 10s
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 1
  MaxIdleConn: 1
  MaxConnTtl: "30m"
  DbType: "PGDB"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbCredentials: "prod/rds/epifimetis/nudge_dev_user"
    RudderWriteKey: "prod/rudder/internal-writekey"

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false
