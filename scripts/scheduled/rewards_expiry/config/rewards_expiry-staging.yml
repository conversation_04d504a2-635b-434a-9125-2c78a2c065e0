Application:
  Environment: "staging"
  Name: "rewards_expiry"

RewardsDb:
  Name: "rewards"
  StatementTimeout: 5s
  EnableDebug: false
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  DbType: "PGDB"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbCredentials: "staging/rds/postgres14"
