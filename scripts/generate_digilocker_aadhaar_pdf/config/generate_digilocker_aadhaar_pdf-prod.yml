Application:
  Environment: "prod"

Aws:
  Region: "ap-south-1"

EpifiWealthDb:
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: false
  StatementTimeout: 2m
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

S3Conf:
  Bucket: "epifi-prod-wealth-onboarding"
