Application:
  Environment: "uat"
  Name: "onboarding_god_script"

EpifiDb:
  DbType: "CRDB"
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "./crdb/uat/"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "user_properties_pgdb"
        DbDsn:
          DbType: "PGDB"
          DbServerAlias: "PRIMARY_RDS"
          Name: "user_properties"
          EnableDebug: true
          SSLMode: "disable"
          AppName: "user"
          SecretName: "uat/rds/epifimetis/user_properties_dev_user"

ActorPgdb:
  DbType: "PGDB"
  DbServerAlias: "PLUTUS_RDS"
  Name: "actor"
  EnableDebug: false
  StatementTimeout: 1s
  SSLMode: "disable"
  SecretName: "uat/rds/epifiplutus/actor_dev_user"
  MaxOpenConn: 50
  MaxIdleConn: 14
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Email:
  PostAccountClosureEmailId: "<EMAIL>"
  PostAccountClosureEmailName: "Fi Money"

FederalIfscCode: "FDRL0005555"

Secrets:
  Ids:
    DbUsernamePassword: "uat/rds/postgres"
    FederalPoolAccountNo: "uat/cx/federal-pool-acc-no"
    BankCustomerPgdbUsernamePassword: "uat/rds/epifimetis/bank_customer_dev_user"
    SlackOauthToken: "uat/onboarding/slack-oauth-token"

EpifiPgdb:
  DbType: "PGDB"
  Name: "vendormapping"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

BankCustomerPgdb:
  DbType: "PGDB"
  AppName: "bank_customer"
  StatementTimeout: 5s
  Name: "bank_customer"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

KycPgdb:
  AppName: "kyc"
  DbType: "PGDB"
  StatementTimeout: 5s
  Name: "kyc"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "uat/rds/postgres/kyc_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false


VerifiPGDB:
  AppName: "verifi"
  StatementTimeout: 5m
  DbType: "PGDB"
  Name: "kyc_non_resident"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  SecretName: "uat/rds/epifiminerva/kyc_non_resident_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 500ms
    UseInsecureLog: true

FederalVkycUpdatePublisher:
  QueueName: "uat-vn-federal-vkyc-update-queue"

AmlFileGenerationPublisher:
  QueueName: "uat-aml-file-generation-queue"

SavingsAccountPIPublisher:
  QueueName: "uat-savings-account-pi-creation-queue"

AuthDevRegRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12
  ClientName: auth
  HystrixCommand:
    CommandName: "auth_tokens_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 2500
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

Tracing:
  Enable: true

UserRedis:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12
  HystrixCommand:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80


S3Conf:
  LivenessBucket: "epifi-uat-liveness"
  BucketNames:
    OnboardingBucket: "epifi-onboarding-dev"
    PanBucket: "epifi-uat-pan"
    ItrIntimationBucket: "epifi-uat-itr-intimation"
    NrBucket: "epifi-uat-nrusers"
    UsersBucket: "epifi-uat-users"
    VkycCallBucket: "epifi-uat-vkyc-call"

AccessS3File:
  fromEmailId: "<EMAIL>"
  fromEmailName: "Fi money"
  toEmailId: "<EMAIL>"

DebitCardDocsBucketName: "epifi-uat-debit-card-docs"

ClientDeeplinkUrl: "https://fi.onelink.me"


VkycCallRedisOption:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 16

