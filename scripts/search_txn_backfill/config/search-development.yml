Application:
  Environment: "staging"

AWS:
  Region: "ap-south-1"

ESHost: "https://vpc-deploy-search-access-j3gbgxj77i5tyqkoopmi5pklzq.ap-south-1.es.amazonaws.com/"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "/home/<USER>/epifi/gamma/config/crdb/staging/"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"

Tracing:
  Enable: true
