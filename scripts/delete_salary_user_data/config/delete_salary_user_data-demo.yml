Application:
  Environment: "demo"
  Name: "delete_salary_user_data"

SalaryProgramDb:
  AppName: "salaryprogram"
  StatementTimeout: 5s
  Name: "salaryprogram"
  EnableDebug: false
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Secrets:
  Ids:
    DbCredentials: "demo/rds/postgres"

Aws:
  Region: "ap-south-1"
