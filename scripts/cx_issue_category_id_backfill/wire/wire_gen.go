// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/gamma/cx/issue_category/dao"
	"github.com/epifi/gamma/cx/issue_category/manager"
	dao2 "github.com/epifi/gamma/cx/ticket/dao"
	"github.com/epifi/gamma/scripts/cx_issue_category_id_backfill/processor"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializeBackfillProcess(db types.SherlockPGDB, gormDb *gorm.DB) *processor.BackFillProcessor {
	issueCategoryDao := dao.NewIssueCategoryDao(db)
	issueCategoryManagerImpl := manager.NewIssueCategoryManagerImpl(issueCategoryDao)
	supportTicketDao := dao2.NewSupportTicketDao(db)
	backFillProcessor := processor.NewBackFillProcessor(issueCategoryManagerImpl, supportTicketDao, gormDb)
	return backFillProcessor
}
