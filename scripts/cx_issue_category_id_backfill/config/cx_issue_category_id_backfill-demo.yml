Application:
  Environment: "demo"
  Name: "cx_issue_category_id_backfill"

EpifiDb:
  AppName: "cx"
  StatementTimeout: 5s
  Name: "sherlock"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbUsernamePassword: "demo/rds/postgres"

