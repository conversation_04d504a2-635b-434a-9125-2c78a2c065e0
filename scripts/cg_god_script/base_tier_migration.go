package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/gocarina/gocsv"
	"google.golang.org/grpc/metadata"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/tiering/tiermappings"

	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/tiering/dao"
)

type actorBaseTierMigrationJob struct {
	actorTierInfoDao dao.ActorTierInfoDao
	tieringClient    tiering.TieringClient
	usersClient      userPb.UsersClient
	actorIds         []string // Parsed actor IDs
	externalTier     tieringExtPb.Tier
	internalTier     enums.Tier
}

// ParseAndStoreArgs parses input arguments for actor IDs
// Tries to parse the actorIds first from file attached to the job if any
// If no file attached, tries to parse actorIds from Args2 (JSON)
func (j *actorBaseTierMigrationJob) ParseAndStoreArgs(ctx context.Context, request *JobRequest) error {
	var err error
	if request.Args1 == "" {
		logger.Error(ctx, "toBaseTier (Args1) is required")
		return fmt.Errorf("toBaseTier (Args1) is required")
	}

	switch {
	case request.FilePath != "":
		file, err := os.Open(request.FilePath)
		if err != nil {
			return fmt.Errorf("failed to open CSV file: %w", err)
		}
		defer func() {
			if closeErr := file.Close(); closeErr != nil {
				logger.Error(ctx, "failed to close file", zap.Error(closeErr))
			}
		}()
		var rows []struct {
			ActorId string `csv:"Actor_id"`
		}
		if err := gocsv.UnmarshalFile(file, &rows); err != nil {
			return fmt.Errorf("failed to parse CSV file: %w", err)
		}
		if len(rows) == 0 {
			return fmt.Errorf("CSV file is empty")
		}
		var actorIds []string
		for _, row := range rows {
			actorId := strings.TrimSpace(row.ActorId)
			if actorId != "" {
				actorIds = append(actorIds, actorId)
			}
		}
		if len(actorIds) == 0 {
			return fmt.Errorf("no valid actorIds found in CSV")
		}
		j.actorIds = actorIds
	case request.Args2 != "":
		var idsJSON struct {
			ActorIds []string `json:"actorIds"`
		}
		if err := json.Unmarshal([]byte(request.Args2), &idsJSON); err != nil {
			return fmt.Errorf("failed to parse actorIds from JSON: %w", err)
		}
		if len(idsJSON.ActorIds) == 0 {
			return fmt.Errorf("no actorIds provided in JSON input")
		}
		j.actorIds = idsJSON.ActorIds
	default:
		return fmt.Errorf("input args cannot be empty (provide either CSV file with -FileInput1 or JSON with -Args2)")
	}

	tierVal, ok := enums.Tier_value[request.Args1]
	if !ok {
		logger.Error(ctx, "failed to find tier value for arg1")
		return fmt.Errorf("invalid base tier: %s", request.Args1)
	}
	j.internalTier = enums.Tier(tierVal)

	// Convert to external tier and check if it's a base tier
	j.externalTier, err = tiermappings.GetExternalTierFromInternalTier(j.internalTier)
	if err != nil {
		logger.Error(ctx, "Failed to convert internal tier to external tier", zap.String("internal_tier", j.internalTier.String()), zap.Error(err))
		return fmt.Errorf("failed to convert internal tier to external tier: %w", err)
	}
	if !j.externalTier.IsBaseTier() {
		err := fmt.Errorf("target tier is not a base tier: %s", j.externalTier.String())
		logger.Error(ctx, err.Error())
		return err
	}
	return nil
}

// DoJob
// Args1 ( mandatory ): base tier string : enums.Tier , e.g. Tier_TIER_FIVE
// Args2 ( Optional -> either attach file or provide JSON as Args2 ): JSON containing actorIds, e.g. '{"actorIds":["actor1","actor2"]}'
// FilePath ( Optional -> either attach file or provide JSON as Args2 ) : attach a csv file, having a column with name 'Actor_id' containing record of actor ids
// This job updates the actor_base_tier field for the given actors and triggers a tier evaluation via tieringClient.Upgrade with PROVENANCE_MANUAL_OVERRIDE provenance.
func (j *actorBaseTierMigrationJob) DoJob(ctx context.Context, request *JobRequest) error {

	ctx = epificontext.WithTraceId(ctx, metadata.MD{})

	if err := j.ParseAndStoreArgs(ctx, request); err != nil {
		return err
	}

	failedB2BSalaryStatus := make([]string, 0)
	failedDaoBaseTierUpdate := make([]string, 0)
	failedUpgradeProvenance := make([]string, 0)

	for i, actorId := range j.actorIds {
		// Per-actor context with trace_id and actor_id
		actorCtx := epificontext.CtxWithActorId(ctx, actorId)

		// If to tier is REGULAR, check B2B salary program verification status
		if j.externalTier == tieringExtPb.Tier_TIER_FI_REGULAR {
			resp, err := j.usersClient.GetB2BSalaryProgramVerificationStatus(actorCtx, &userPb.GetB2BSalaryProgramVerificationStatusRequest{
				Identifier: &userPb.GetB2BSalaryProgramVerificationStatusRequest_ActorId{
					ActorId: actorId,
				},
			})
			if err != nil {
				logger.Error(actorCtx, "Failed to get B2B salary program verification status", zap.String("actor_id", actorId), zap.Error(err))
				failedB2BSalaryStatus = append(failedB2BSalaryStatus, actorId)
				continue
			}
			if resp.GetIsVerified() {
				logger.Error(actorCtx, "Actor is B2B salary program verified, skipping", zap.String("actor_id", actorId))
				failedB2BSalaryStatus = append(failedB2BSalaryStatus, actorId)
				continue
			}
		}
		ati := &tiering.ActorTierInfo{
			ActorId:       actorId,
			ActorBaseTier: j.internalTier,
		}
		_, err := j.actorTierInfoDao.Update(actorCtx, ati, []tiering.ActorTierInfoFieldMask{tiering.ActorTierInfoFieldMask_ACTOR_TIER_INFO_FIELD_MASK_ACTOR_BASE_TIER})
		if err != nil {
			logger.Error(actorCtx, "Failed to update actor base tier info", zap.String("actor_id", actorId), zap.Error(err))
			failedDaoBaseTierUpdate = append(failedDaoBaseTierUpdate, actorId)
			continue
		}
		upgradeResp, upgradeErr := j.tieringClient.Upgrade(actorCtx, &tiering.UpgradeRequest{
			ActorId:    actorId,
			Provenance: enums.Provenance_PROVENANCE_MANUAL_OVERRIDE,
		})
		// nolint:gosec
		if rpcErr := epifigrpc.RPCError(upgradeResp, upgradeErr); rpcErr != nil && upgradeResp.GetStatus().GetCode() != uint32(tiering.UpgradeResponse_DOWNGRADED.Number()) {
			logger.Error(actorCtx, "Failed to trigger tier evaluation via Upgrade", zap.String("actor_id", actorId), zap.Error(rpcErr))
			failedUpgradeProvenance = append(failedUpgradeProvenance, actorId)
			continue
		}
		logger.Info(actorCtx, "Successfully updated base tier and triggered evaluation", zap.String("actor_id", actorId), zap.String("base_tier", j.internalTier.String()))
		if (i+1)%100 == 0 {
			time.Sleep(200 * time.Millisecond)
		}
	}

	if len(failedB2BSalaryStatus) > 0 || len(failedDaoBaseTierUpdate) > 0 || len(failedUpgradeProvenance) > 0 {
		return fmt.Errorf(
			"failed actorIds: \n"+
				"  B2BSalaryStatus    (count=%d): \n%v\n"+
				"  DaoBaseTierUpdate  (count=%d): \n%v\n"+
				"  UpgradeProvenance  (count=%d): \n%v",
			len(failedB2BSalaryStatus), failedB2BSalaryStatus,
			len(failedDaoBaseTierUpdate), failedDaoBaseTierUpdate,
			len(failedUpgradeProvenance), failedUpgradeProvenance,
		)
	}

	logger.Info(ctx, "All actor base tiers updated and evaluated successfully.")
	return nil
}
