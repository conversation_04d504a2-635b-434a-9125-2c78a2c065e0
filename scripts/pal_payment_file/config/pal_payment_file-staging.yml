Application:
  Environment: "staging"
  Name: "pal_payment_file"

PgDbConfigMap:
  FEDERAL_BANK:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_federal"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "staging/rds/epifimetis/loans_federal_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_liquiloans"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "staging/rds/epifimetis/loans_liquiloans_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  IDFC_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_idfc"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "staging/rds/epifimetis/loans_idfc_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

AWS:
  Region: "ap-south-1"
  S3:
    PreapprovedLoanBucketName: "epifi-staging-preapprovedloan"

Email:
  FromEmail: "<EMAIL>"
  ToEmail: "<EMAIL>"
  CcEmails: [ "<EMAIL>", "<EMAIL>", "<EMAIL>"]

RedisStore:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: ""
    DB: 12
    PoolSize: 20 # this is set to twice the size of default connection pool size
    MinIdleConns: 10 # this allows min. number of conn to be readily available
