// nolint:dupl,funlen
package job

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"
	"google.golang.org/grpc/metadata"

	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/gamma/scripts/pal/job/credit_report_purge"
	"github.com/epifi/gamma/scripts/pal/job/lamf"
	lmsDataDifference "github.com/epifi/gamma/scripts/pal/job/lms_data_difference"
)

type Registry struct {
	liquiLoansPrepaymentJob                       *LiquiLoansPrepaymentJob
	processFiEligibleBaseJob                      *ProcessFiEligibleBaseJob
	processEligibleBaseFromVendorJob              *ProcessEligibleBaseFromVendorJob
	deleteRiskyUsersJob                           *DeleteRiskyUsersJob
	retryFetchDetailsV2Job                        *RetryFetchDetailsV2Job
	getStatementJob                               *GetStatementJob
	populateLecFromVendorFileJob                  *PopulateLecFromVendorFileJob
	populateLecFromVendorJob                      *PopulateLecFromVendorJob
	sendEmiCommsJob                               *SendEmiCommsJob
	createNewOfferJob                             *CreateNewOfferJob
	extendExpiryFedOffersJob                      *ExtendExpiryFedOffersJob
	processLlEligibleBaseJob                      *ProcessLlEligibleBaseJob
	creditReportFlattenAdhocJob                   *CreditReportFlattenAdhocJob
	creditReportFlattenJob                        *CreditReportFlattenJob
	CreditReportDataRecoveryJob                   *CreditReportDataRecoveryJob
	updateInstallmentInfoJob                      *UpdateInstallmentInfoJob
	fetchLlRejectedBaseJob                        *FetchLlRejectedBaseJob
	triggerLlActivityJob                          *TriggerLlActivityJob
	setLoanProgramInLoanOffersJob                 *SetLoanProgramInLoanOffersJob
	backfillCkycVideoPQJob                        *BackfillCkycVideoPQJob
	loanAccountReconJob                           *LoanAccountReconJob
	cryptS3DataJob                                *CryptS3DataJob
	RepaymentCaptureJob                           *RepaymentCaptureJob
	LoanActivityBackFillJob                       *LoanActivityBackFillJob
	generateVendorBureauFileJob                   *GenerateVendorBureauFileJob
	disableOffersJob                              *DisableOffersJob
	loanSchemeBackfillJob                         *LoanSchemeBackFillJob
	syncLoanAccountJob                            *SyncLoanAccountJob
	DeactivateAllOffersJob                        *DeactivateAllOffersJob
	DeactivateOfferIfLoanTakenJob                 *DeactivateOfferIfLoanTakenJob
	CleanUpPayoutsJob                             *CleanUpPayoutsJob
	ResolveLOECJob                                *ResolveLOECJob
	EsSiCollectionJob                             *SiCollectionJob
	BackfillLoanAccountIdJob                      *BackfillLoanAccountIdJob
	ResolveEMIDefaultsJob                         *ResolveEMIDefaultsJob
	EsEsignTriggerJob                             *EsEsignTriggerJob
	CreateIdfcCugOffers                           *CreateIdfcCugOffersJob
	DeleteLoanAccounts                            *DeleteLoanAccountsJob
	GetAddress                                    *GetAddressJob
	FixLoanApplicant                              *FixLoanApplicantJob
	ResetCharges                                  *ResetChargesForSubventionUsers
	BackfillUTRJob                                *BackfillUTRJob
	BureauReportingSubventionJob                  *BureauReportingSubventionJob
	updateVendorReqIdIdfc                         *UpdateVendorReqIdIdfcJob
	BrePipingJob                                  *BrePipingJob
	syncLoanAccountLLJob                          *SyncLoanAccountLLJob
	deleteDuplicateLoanLiiJob                     *DeleteDuplicateLoanLiiJob
	deactivateFederalOffersJob                    *DeactivateFederalOffersJob
	processScrubDataJob                           *ProcessScrubDataJob
	processEncryptedScrubDataJob                  *ProcessEncryptedScrubDataJob
	mvPanDedupeJob                                *MvPanDedupeJob
	processPrepaymentAtVendorJob                  *ProcessPrepaymentAtVendorJob
	FetchRepaymentScheduleJob                     *FetchRepaymentScheduleJob
	FetchLoanStatusJob                            *FetchLoanStatusJob
	LamfResetFailedOfferGenLseJob                 *LamfResetFailedOfferGenLseJob
	BackfillLoanActivitiesUTRJob                  *BackfillLoanActivitiesUTRJob
	DeleteUserLoanDataJob                         *DeleteUserLoanDataJob
	NormalizeCibilJob                             *NormalizeCibilJob
	CollectionsSyncLeadJob                        *CollectionsSyncLeadJob
	ProcessBillzyPaymentsJob                      *ProcessBillzyPaymentsJob
	BackfillCibilCustIdMismatch                   *BackfillCibilCustIdMismatch
	BackfillCibilReportsJob                       *BackfillCibilReportsJob
	ReconLoanPreClosureJob                        *ReconLoanPreClosureJob
	LamfManualResolution                          *lamf.LamfStuckUserManualResolution
	CollectionsAllocationSyncJob                  *CollectionsAllocationSyncJob
	LmsDataDifferenceJob                          *lmsDataDifference.Job
	FillFederalDPDUserDataJob                     *FillFederalDPDUserDataJob
	UploadITRFileToSFTPJob                        *UploadITRFileToSFTPJob
	LamfCreateInterestAccountPaymentInstrumentJob *lamf.CreateInterestAccountPaymentInstrumentJob
	autoPayJob                                    *AutoPayJob
	postNachPaymentsToCredgenics                  *PostNachPaymentsToCredgenicsJob
	softDeleteCreditReportDataJob                 *credit_report_purge.SoftDeleteCreditReportDataJob
	softDeleteOldCreditReportDataJob              *credit_report_purge.SoftDeleteOldCreditReportDataJob
	hardDeleteFlattenedCreditReportDataJob        *credit_report_purge.HardDeleteFlattenedCreditReportDataJob
	updateCreditReportDownloadDataJob             *credit_report_purge.UpdateCreditReportDownloadDataJob
	oneTimeJob                                    *OneTimeJob
	applyOfferDiscountAfterUserDropoffJob         *ApplyOfferDiscountAfterUserDropoffJob
	softDeleteOldCibilReportDataJob               *credit_report_purge.SoftDeleteCibilReportDataJob
	hardDeleteFlattenedCibilReportDataJob         *credit_report_purge.HardDeleteFlattenedCibilReportDataJob
	deactivateAllLoanApplicationJob               *DeactivateAllLoanApplicationJob
	purgeCkycDataJob                              *PurgeCkycDataJob
	abflRegulatoryDataJob                         *AbflRegulatoryDataJob
	loanAccountCreationLAMFJob                    *lamf.LoanAccountCreationLAMFJob
	StoreExperianConsentsJob                      *StoreExperianConsentsJob
	updateLrLseStatusJob                          *UpdateLrLseStatusJob
	syncAllActiveLeadsCollectionsJob              *SyncAllActiveLeadsCollectionsJob
	updateLoanOfferconstraintsJob                 *UpdateLoanOffersJob
	initiateAnalysisJob                           *InitiateAnalysisJob
	UpdateAbflLoanStatus                          *UpdateAbflLoanStatus
	BackfillRejectedBreDataJob                    *BackfillRejectedBreDataJob
	epfoDataFetchJob                              *EpfoDataFetchJob
	fedLentraCugTesting                           *FedLentraCugTestingJob
	retryDedupeJob                                *RetryDedupeJob
	loanFunnelTrackingJob                         *LoanFunnelTrackingJob
	updateFederalLoanRequestVendorIdJob           *UpdateFederalLoanRequestVendorIdJob
	loansOnboardingJob                            *LoansOnboardingJob
}

func NewRegistry(
	liquiLoansPrepaymentJob *LiquiLoansPrepaymentJob,
	processFiEligibleBaseJob *ProcessFiEligibleBaseJob,
	processEligibleBaseFromVendorJob *ProcessEligibleBaseFromVendorJob,
	deleteRiskyUsersJob *DeleteRiskyUsersJob,
	retryFetchDetailsV2Job *RetryFetchDetailsV2Job,
	getStatementJob *GetStatementJob,
	populateLecFromVendorFileJob *PopulateLecFromVendorFileJob,
	populateLecFromVendorJob *PopulateLecFromVendorJob,
	sendEmiCommsJob *SendEmiCommsJob,
	createNewOfferJob *CreateNewOfferJob,
	extendExpiryFedOffersJob *ExtendExpiryFedOffersJob,
	processLlEligibleBaseJob *ProcessLlEligibleBaseJob,
	creditReportFlattenAdhocJob *CreditReportFlattenAdhocJob,
	creditReportFlattenJob *CreditReportFlattenJob,
	updateInstallmentInfoJob *UpdateInstallmentInfoJob,
	fetchLlRejectedBaseJob *FetchLlRejectedBaseJob,
	triggerLlActivityJob *TriggerLlActivityJob,
	setLoanProgramInLoanOffersJob *SetLoanProgramInLoanOffersJob,
	backfillCkycVideoPQJob *BackfillCkycVideoPQJob,
	loanAccountReconJob *LoanAccountReconJob,
	cryptS3DataJob *CryptS3DataJob,
	repaymentCaptureJob *RepaymentCaptureJob,
	loanActivityBackFillJob *LoanActivityBackFillJob,
	generateVendorBureauFileJob *GenerateVendorBureauFileJob,
	disableOffersJob *DisableOffersJob,
	loanSchemeBackfillJob *LoanSchemeBackFillJob,
	syncLoanAccountJob *SyncLoanAccountJob,
	deactivateAllOffersJob *DeactivateAllOffersJob,
	deactivateOfferIfLoanTakenJob *DeactivateOfferIfLoanTakenJob,
	cleanUpPayoutsJob *CleanUpPayoutsJob,
	resolveLOECJob *ResolveLOECJob,
	esSiCollectionJob *SiCollectionJob,
	backfillLoanAccountIdJob *BackfillLoanAccountIdJob,
	resolveEMIDefaultsJob *ResolveEMIDefaultsJob,
	esEsignTriggerJob *EsEsignTriggerJob,
	createIdfcCugOffers *CreateIdfcCugOffersJob,
	deleteLoanAccounts *DeleteLoanAccountsJob,
	getAddressJob *GetAddressJob,
	fixLoanApplicant *FixLoanApplicantJob,
	resetCharges *ResetChargesForSubventionUsers,
	backfillUTRJob *BackfillUTRJob,
	bureauReportingSubventionJob *BureauReportingSubventionJob,
	updateVendorReqIdIdfc *UpdateVendorReqIdIdfcJob,
	brePipingJob *BrePipingJob,
	syncLoanAccountLLJob *SyncLoanAccountLLJob,
	deleteDuplicateLoanLiiJob *DeleteDuplicateLoanLiiJob,
	deactivateFederalOffersJob *DeactivateFederalOffersJob,
	processScrubDataJob *ProcessScrubDataJob,
	processEncryptedScrubDataJob *ProcessEncryptedScrubDataJob,
	processPrepaymentAtVendorJob *ProcessPrepaymentAtVendorJob,
	mvPanDedupeJob *MvPanDedupeJob,
	fetchRepaymentScheduleJob *FetchRepaymentScheduleJob,
	fetchLoanStatusJob *FetchLoanStatusJob,
	lamfResetFailedOfferGenLseJob *LamfResetFailedOfferGenLseJob,
	backfillLoanActivitiesUTRJob *BackfillLoanActivitiesUTRJob,
	deleteUserLoanDataJob *DeleteUserLoanDataJob,
	collectionsSyncLeadJob *CollectionsSyncLeadJob,
	normalizeCibilJob *NormalizeCibilJob,
	backfillCibilCustIdMismatch *BackfillCibilCustIdMismatch,
	backfillCibilReportsJob *BackfillCibilReportsJob,
	reconLoanPreClosureJob *ReconLoanPreClosureJob,
	lamfManualResolution *lamf.LamfStuckUserManualResolution,
	collectionsAllocationSyncJob *CollectionsAllocationSyncJob,
	processBillzyPaymentsJob *ProcessBillzyPaymentsJob,
	lmsDataDifferenceJob *lmsDataDifference.Job,
	fillFederalDPDUserDataJob *FillFederalDPDUserDataJob,
	uploadITRFileToSFTPJob *UploadITRFileToSFTPJob,
	lamfCreateInterestAccountPaymentInstrumentJob *lamf.CreateInterestAccountPaymentInstrumentJob,
	creditReportDataRecoveryJob *CreditReportDataRecoveryJob,
	autoPayJob *AutoPayJob,
	postNachPaymentsToCredgenics *PostNachPaymentsToCredgenicsJob,
	softDeleteCreditReportDataJob *credit_report_purge.SoftDeleteCreditReportDataJob,
	softDeleteOldCreditReportDataJob *credit_report_purge.SoftDeleteOldCreditReportDataJob,
	hardDeleteFlattenedCreditReportDataJob *credit_report_purge.HardDeleteFlattenedCreditReportDataJob,
	oneTimeJob *OneTimeJob,
	updateCreditReportDownloadDataJob *credit_report_purge.UpdateCreditReportDownloadDataJob,
	applyOfferDiscountAfterUserDropoffJob *ApplyOfferDiscountAfterUserDropoffJob,
	softDeleteOldCibilReportDataJob *credit_report_purge.SoftDeleteCibilReportDataJob,
	hardDeleteFlattenedCibilReportDataJob *credit_report_purge.HardDeleteFlattenedCibilReportDataJob,
	deactivateAllLoanApplicationJob *DeactivateAllLoanApplicationJob,
	purgeCkycDataJob *PurgeCkycDataJob,
	abflRegulatoryDataJob *AbflRegulatoryDataJob,
	loanAccountCreationLAMFJob *lamf.LoanAccountCreationLAMFJob,
	storeExperianConsentsJob *StoreExperianConsentsJob,
	updateLrLseStatusJob *UpdateLrLseStatusJob,
	syncAllActiveLeadsCollectionsJob *SyncAllActiveLeadsCollectionsJob,
	updateLoanOfferconstraintsJob *UpdateLoanOffersJob,
	initiateAnalysisJob *InitiateAnalysisJob,
	updateAbflLoanStatus *UpdateAbflLoanStatus,
	backfillRejectedBreDataJob *BackfillRejectedBreDataJob,
	epfoDataFetchJob *EpfoDataFetchJob,
	fedLentraCugTesting *FedLentraCugTestingJob,
	retryDedupeJob *RetryDedupeJob,
	loanFunnelTrackingJob *LoanFunnelTrackingJob,
	updateFederalLoanRequestVendorIdJob *UpdateFederalLoanRequestVendorIdJob,
	loansOnboardingJob *LoansOnboardingJob,
) *Registry {
	return &Registry{
		liquiLoansPrepaymentJob:                       liquiLoansPrepaymentJob,
		processFiEligibleBaseJob:                      processFiEligibleBaseJob,
		processEligibleBaseFromVendorJob:              processEligibleBaseFromVendorJob,
		deleteRiskyUsersJob:                           deleteRiskyUsersJob,
		retryFetchDetailsV2Job:                        retryFetchDetailsV2Job,
		getStatementJob:                               getStatementJob,
		populateLecFromVendorFileJob:                  populateLecFromVendorFileJob,
		populateLecFromVendorJob:                      populateLecFromVendorJob,
		sendEmiCommsJob:                               sendEmiCommsJob,
		createNewOfferJob:                             createNewOfferJob,
		extendExpiryFedOffersJob:                      extendExpiryFedOffersJob,
		processLlEligibleBaseJob:                      processLlEligibleBaseJob,
		creditReportFlattenAdhocJob:                   creditReportFlattenAdhocJob,
		creditReportFlattenJob:                        creditReportFlattenJob,
		updateInstallmentInfoJob:                      updateInstallmentInfoJob,
		fetchLlRejectedBaseJob:                        fetchLlRejectedBaseJob,
		triggerLlActivityJob:                          triggerLlActivityJob,
		setLoanProgramInLoanOffersJob:                 setLoanProgramInLoanOffersJob,
		backfillCkycVideoPQJob:                        backfillCkycVideoPQJob,
		loanAccountReconJob:                           loanAccountReconJob,
		cryptS3DataJob:                                cryptS3DataJob,
		RepaymentCaptureJob:                           repaymentCaptureJob,
		LoanActivityBackFillJob:                       loanActivityBackFillJob,
		generateVendorBureauFileJob:                   generateVendorBureauFileJob,
		disableOffersJob:                              disableOffersJob,
		loanSchemeBackfillJob:                         loanSchemeBackfillJob,
		syncLoanAccountJob:                            syncLoanAccountJob,
		DeactivateAllOffersJob:                        deactivateAllOffersJob,
		DeactivateOfferIfLoanTakenJob:                 deactivateOfferIfLoanTakenJob,
		CleanUpPayoutsJob:                             cleanUpPayoutsJob,
		ResolveLOECJob:                                resolveLOECJob,
		EsSiCollectionJob:                             esSiCollectionJob,
		BackfillLoanAccountIdJob:                      backfillLoanAccountIdJob,
		ResolveEMIDefaultsJob:                         resolveEMIDefaultsJob,
		EsEsignTriggerJob:                             esEsignTriggerJob,
		CreateIdfcCugOffers:                           createIdfcCugOffers,
		DeleteLoanAccounts:                            deleteLoanAccounts,
		GetAddress:                                    getAddressJob,
		FixLoanApplicant:                              fixLoanApplicant,
		ResetCharges:                                  resetCharges,
		BackfillUTRJob:                                backfillUTRJob,
		BureauReportingSubventionJob:                  bureauReportingSubventionJob,
		updateVendorReqIdIdfc:                         updateVendorReqIdIdfc,
		BrePipingJob:                                  brePipingJob,
		syncLoanAccountLLJob:                          syncLoanAccountLLJob,
		deleteDuplicateLoanLiiJob:                     deleteDuplicateLoanLiiJob,
		deactivateFederalOffersJob:                    deactivateFederalOffersJob,
		processScrubDataJob:                           processScrubDataJob,
		processEncryptedScrubDataJob:                  processEncryptedScrubDataJob,
		processPrepaymentAtVendorJob:                  processPrepaymentAtVendorJob,
		mvPanDedupeJob:                                mvPanDedupeJob,
		FetchRepaymentScheduleJob:                     fetchRepaymentScheduleJob,
		FetchLoanStatusJob:                            fetchLoanStatusJob,
		LamfResetFailedOfferGenLseJob:                 lamfResetFailedOfferGenLseJob,
		BackfillLoanActivitiesUTRJob:                  backfillLoanActivitiesUTRJob,
		DeleteUserLoanDataJob:                         deleteUserLoanDataJob,
		NormalizeCibilJob:                             normalizeCibilJob,
		CollectionsSyncLeadJob:                        collectionsSyncLeadJob,
		BackfillCibilCustIdMismatch:                   backfillCibilCustIdMismatch,
		BackfillCibilReportsJob:                       backfillCibilReportsJob,
		ReconLoanPreClosureJob:                        reconLoanPreClosureJob,
		LamfManualResolution:                          lamfManualResolution,
		CollectionsAllocationSyncJob:                  collectionsAllocationSyncJob,
		ProcessBillzyPaymentsJob:                      processBillzyPaymentsJob,
		LmsDataDifferenceJob:                          lmsDataDifferenceJob,
		FillFederalDPDUserDataJob:                     fillFederalDPDUserDataJob,
		UploadITRFileToSFTPJob:                        uploadITRFileToSFTPJob,
		LamfCreateInterestAccountPaymentInstrumentJob: lamfCreateInterestAccountPaymentInstrumentJob,
		CreditReportDataRecoveryJob:                   creditReportDataRecoveryJob,
		autoPayJob:                                    autoPayJob,
		postNachPaymentsToCredgenics:                  postNachPaymentsToCredgenics,
		softDeleteCreditReportDataJob:                 softDeleteCreditReportDataJob,
		softDeleteOldCreditReportDataJob:              softDeleteOldCreditReportDataJob,
		hardDeleteFlattenedCreditReportDataJob:        hardDeleteFlattenedCreditReportDataJob,
		oneTimeJob:                                    oneTimeJob,
		updateCreditReportDownloadDataJob:             updateCreditReportDownloadDataJob,
		applyOfferDiscountAfterUserDropoffJob:         applyOfferDiscountAfterUserDropoffJob,
		hardDeleteFlattenedCibilReportDataJob:         hardDeleteFlattenedCibilReportDataJob,
		softDeleteOldCibilReportDataJob:               softDeleteOldCibilReportDataJob,
		deactivateAllLoanApplicationJob:               deactivateAllLoanApplicationJob,
		purgeCkycDataJob:                              purgeCkycDataJob,
		abflRegulatoryDataJob:                         abflRegulatoryDataJob,
		loanAccountCreationLAMFJob:                    loanAccountCreationLAMFJob,
		StoreExperianConsentsJob:                      storeExperianConsentsJob,
		updateLrLseStatusJob:                          updateLrLseStatusJob,
		syncAllActiveLeadsCollectionsJob:              syncAllActiveLeadsCollectionsJob,
		updateLoanOfferconstraintsJob:                 updateLoanOfferconstraintsJob,
		initiateAnalysisJob:                           initiateAnalysisJob,
		UpdateAbflLoanStatus:                          updateAbflLoanStatus,
		BackfillRejectedBreDataJob:                    backfillRejectedBreDataJob,
		epfoDataFetchJob:                              epfoDataFetchJob,
		fedLentraCugTesting:                           fedLentraCugTesting,
		retryDedupeJob:                                retryDedupeJob,
		loanFunnelTrackingJob:                         loanFunnelTrackingJob,
		updateFederalLoanRequestVendorIdJob:           updateFederalLoanRequestVendorIdJob,
		loansOnboardingJob:                            loansOnboardingJob,
	}
}

func (r *Registry) getJob(t Type) (Job, error) {
	switch t {
	case ProcessFiEligibleBase:
		return r.processFiEligibleBaseJob, nil
	case ProcessEligibleBaseFromVendor:
		return r.processEligibleBaseFromVendorJob, nil
	case DeleteRiskyUsers:
		return r.deleteRiskyUsersJob, nil
	case RetryFetchDetailsV2:
		return r.retryFetchDetailsV2Job, nil
	case GetStatement:
		return r.getStatementJob, nil
	case PopulateLecFromVendorFile:
		return r.populateLecFromVendorFileJob, nil
	case PopulateLecFromVendor:
		return r.populateLecFromVendorJob, nil
	case SendEmiComms:
		return r.sendEmiCommsJob, nil
	case CreateNewOffer:
		return r.createNewOfferJob, nil
	case ExtendExpiryFedOffers:
		return r.extendExpiryFedOffersJob, nil
	case ProcessLlEligibleBase:
		return r.processLlEligibleBaseJob, nil
	case CreditReportFlattenAdhoc:
		return r.creditReportFlattenAdhocJob, nil
	case CreditReportFlatten:
		return r.creditReportFlattenJob, nil
	case UpdateInstallmentInfo:
		return r.updateInstallmentInfoJob, nil
	case FetchLlRejectedBase:
		return r.fetchLlRejectedBaseJob, nil
	case TriggerLlActivity:
		return r.triggerLlActivityJob, nil
	case SetLoanProgramInLoanOffers:
		return r.setLoanProgramInLoanOffersJob, nil
	case BackfillCkycVideoPQ:
		return r.backfillCkycVideoPQJob, nil
	case LoanAccountRecon:
		return r.loanAccountReconJob, nil
	case CryptS3Data:
		return r.cryptS3DataJob, nil
	case RepaymentCapture:
		return r.RepaymentCaptureJob, nil
	case LoanActivityBackFill:
		return r.LoanActivityBackFillJob, nil
	case GenerateVendorBureauFile:
		return r.generateVendorBureauFileJob, nil
	case DisableOffers:
		return r.disableOffersJob, nil
	case LoanSchemeBackfill:
		return r.loanSchemeBackfillJob, nil
	case SyncLoanAccount:
		return r.syncLoanAccountJob, nil
	case DeactivateAllOffers:
		return r.DeactivateAllOffersJob, nil
	case DeactivateOfferIfLoanTaken:
		return r.DeactivateOfferIfLoanTakenJob, nil
	case CleanUpPayouts:
		return r.CleanUpPayoutsJob, nil
	case ResolveLOEC:
		return r.ResolveLOECJob, nil
	case SiCollection:
		return r.EsSiCollectionJob, nil
	case BackfillLoanAccountId:
		return r.BackfillLoanAccountIdJob, nil
	case ResolveEMIDefaults:
		return r.ResolveEMIDefaultsJob, nil
	case EsEsignTrigger:
		return r.EsEsignTriggerJob, nil
	case CreateIdfcCugOffers:
		return r.CreateIdfcCugOffers, nil
	case DeleteLoanAccount:
		return r.DeleteLoanAccounts, nil
	case GetAddress:
		return r.GetAddress, nil
	case FixLoanApplicant:
		return r.FixLoanApplicant, nil
	case ResetChargesAmount:
		return r.ResetCharges, nil
	case BackfillUTR:
		return r.BackfillUTRJob, nil
	case BureauReportingSubvention:
		return r.BureauReportingSubventionJob, nil
	case UpdateVendorReqIdIdfc:
		return r.updateVendorReqIdIdfc, nil
	case BrePiping:
		return r.BrePipingJob, nil
	case SyncLoanAccountLL:
		return r.syncLoanAccountLLJob, nil
	case DeleteDuplicateLoanLii:
		return r.deleteDuplicateLoanLiiJob, nil
	case DeactivateFederalOffer:
		return r.deactivateFederalOffersJob, nil
	case ProcessScrubData:
		return r.processScrubDataJob, nil
	case ProcessEncryptedScrubData:
		return r.processEncryptedScrubDataJob, nil
	case MvPanDedupe:
		return r.mvPanDedupeJob, nil
	case ProcessPrepaymentAtVendor:
		return r.processPrepaymentAtVendorJob, nil
	case FetchRepaymentSchedule:
		return r.FetchRepaymentScheduleJob, nil
	case FetchLoanStatus:
		return r.FetchLoanStatusJob, nil
	case LamfResetFailedOfferGenLse:
		return r.LamfResetFailedOfferGenLseJob, nil
	case BackfillLoanActivitiesUTR:
		return r.BackfillLoanActivitiesUTRJob, nil
	case DeleteUserLoanData:
		return r.DeleteUserLoanDataJob, nil
	case NormalizeCibil:
		return r.NormalizeCibilJob, nil
	case CollectionsSyncLead:
		return r.CollectionsSyncLeadJob, nil
	case BfCibilCustIdMismatch:
		return r.BackfillCibilCustIdMismatch, nil
	case BackFillCibilReports:
		return r.BackfillCibilReportsJob, nil
	case ReconLoanPreClosure:
		return r.ReconLoanPreClosureJob, nil
	case LamfManualResolution:
		return r.LamfManualResolution, nil
	case CollectionsAllocationSync:
		return r.CollectionsAllocationSyncJob, nil
	case ProcessBillzyPayments:
		return r.ProcessBillzyPaymentsJob, nil
	case LmsDataDifference:
		return r.LmsDataDifferenceJob, nil
	case FillFederalDpdUserData:
		return r.FillFederalDPDUserDataJob, nil
	case UploadITRFileToSFTP:
		return r.UploadITRFileToSFTPJob, nil
	case LamfCreateInterestAccountPi:
		return r.LamfCreateInterestAccountPaymentInstrumentJob, nil
	case CreditReportDataRecovery:
		return r.CreditReportDataRecoveryJob, nil
	case AutoPay:
		return r.autoPayJob, nil
	case PostNachPaymentsToCredgenics:
		return r.postNachPaymentsToCredgenics, nil
	case SoftDeleteOldCreditReportData:
		return r.softDeleteOldCreditReportDataJob, nil
	case SoftDeleteCreditReportData:
		return r.softDeleteCreditReportDataJob, nil
	case HardDeleteFlattenedCreditReportData:
		return r.hardDeleteFlattenedCreditReportDataJob, nil
	case OneTime:
		return r.oneTimeJob, nil
	case UpdateCreditReportDownloadData:
		return r.updateCreditReportDownloadDataJob, nil
	case ApplyOfferDiscountAfterUserDropoff:
		return r.applyOfferDiscountAfterUserDropoffJob, nil
	case HardDeleteFlattenedCibilReportData:
		return r.hardDeleteFlattenedCibilReportDataJob, nil
	case SoftDeleteOldCibilReportData:
		return r.softDeleteOldCibilReportDataJob, nil
	case DeactivateAllLoanApplication:
		return r.deactivateAllLoanApplicationJob, nil
	case PurgeCkycData:
		return r.purgeCkycDataJob, nil
	case AbflRegulatoryData:
		return r.abflRegulatoryDataJob, nil
	case LoanAccountCreationLAMF:
		return r.loanAccountCreationLAMFJob, nil
	case StoreExperianConsents:
		return r.StoreExperianConsentsJob, nil
	case UpdateLrLseStatus:
		return r.updateLrLseStatusJob, nil
	case LiquiloansRepostHoldPayments:
		return r.liquiLoansPrepaymentJob, nil
	case SyncAllActiveLeadsCollections:
		return r.syncAllActiveLeadsCollectionsJob, nil
	case UpdateLoanOfferConstraints:
		return r.updateLoanOfferconstraintsJob, nil
	case InitiateAnalysis:
		return r.initiateAnalysisJob, nil
	case UploadAbflPwaLoanApplicationStatus:
		return r.UpdateAbflLoanStatus, nil
	case BackfillRejectedBreData:
		return r.BackfillRejectedBreDataJob, nil
	case EpfoDataFetch:
		return r.epfoDataFetchJob, nil
	case FedLentraCugTesting:
		return r.fedLentraCugTesting, nil
	case RetryDedupe:
		return r.retryDedupeJob, nil
	case LoanFunnelTracking:
		return r.loanFunnelTrackingJob, nil
	case UpdateFederalLoanRequestVendorId:
		return r.updateFederalLoanRequestVendorIdJob, nil
	case LoansOnboarding:
		return r.loansOnboardingJob, nil
	default:
		return nil, fmt.Errorf("unable to find job for job type: %v", t)
	}
}

func (r *Registry) RunJob(t Type, jobArguments string, filePaths ...string) error {
	job, err := r.getJob(t)
	if err != nil {
		return errors.Wrap(err, "failure in getJob")
	}

	// unmarshalling the string
	jobArgs := job.GetArgs()
	unmarshalErr := json.Unmarshal([]byte(jobArguments), jobArgs)
	if unmarshalErr != nil {
		panic(unmarshalErr)
	}
	ctx := epificontext.WithTraceId(context.Background(), metadata.MD{})
	err = job.Run(ctx, jobArgs, filePaths)

	fmt.Printf("successfully ran job with traceID: %v\n", epificontext.TraceIdFromContext(ctx))
	return err
}
