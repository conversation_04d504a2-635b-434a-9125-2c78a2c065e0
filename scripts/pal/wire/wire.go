//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.
package wire

import (
	"net/http"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/google/wire"
	temporalClient "go.temporal.io/sdk/client"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"

	locationPb "github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/connected_account/analytics"
	ldcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	events2 "github.com/epifi/gamma/preapprovedloan/events"
	loandataprovider "github.com/epifi/gamma/preapprovedloan/loan_data_provider"
	vgConf "github.com/epifi/gamma/vendorgateway/config"

	"github.com/epifi/be-common/pkg/aws/v2/s3"

	"github.com/epifi/be-common/pkg/cmd/types"
	onceV2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	dsig "github.com/epifi/be-common/pkg/goxmldsig"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/queue"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	vendorapiPkg "github.com/epifi/be-common/pkg/vendorapi"
	vendorapiGenConf "github.com/epifi/be-common/pkg/vendorapi/config/genconf"

	celestialPb "github.com/epifi/be-common/api/celestial"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/developer"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	livenessDevPb "github.com/epifi/gamma/api/auth/liveness/developer"
	orchPb "github.com/epifi/gamma/api/auth/orchestrator"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	brePb "github.com/epifi/gamma/api/bre"
	"github.com/epifi/gamma/api/collection"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/consent"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	docsPb "github.com/epifi/gamma/api/docs"
	esignPb "github.com/epifi/gamma/api/docs/esign"
	beEmploymentPb "github.com/epifi/gamma/api/employment"
	creditreportpb "github.com/epifi/gamma/api/frontend/credit_report"
	fePalPb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/nudge"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/preapprovedloan"
	palCxPb "github.com/epifi/gamma/api/preapprovedloan/cx"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	sgEsignApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/esign"
	sgLmsApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/lms"
	userPb "github.com/epifi/gamma/api/user"
	creditReportPb "github.com/epifi/gamma/api/user/credit_report"
	userDevPb "github.com/epifi/gamma/api/user/developer"
	userLocationPb "github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/obfuscator"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	ekycPb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	"github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics"
	digitapVgPb "github.com/epifi/gamma/api/vendorgateway/lending/digitap"
	"github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	palVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	abflVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	idfcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	llVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	moneyviewVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	"github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	accountVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgCustomerPb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	savingsVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	panVgPb "github.com/epifi/gamma/api/vendorgateway/pan"
	vendorClient "github.com/epifi/gamma/api/vendors/http"
	collectionDaoImpl "github.com/epifi/gamma/collection/dao/impl"
	daoUtils "github.com/epifi/gamma/collection/dao/utils"
	crDaoV2 "github.com/epifi/gamma/creditreportv2/dao"
	"github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/preapprovedloan/config/common"
	commonGenConf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	palWorkerGConf "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	daoImpl "github.com/epifi/gamma/preapprovedloan/dao/impl"
	palHelper "github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/helper/agreement"
	"github.com/epifi/gamma/preapprovedloan/userdata"
	fiftyfin3 "github.com/epifi/gamma/preapprovedloan/vendor_data_provider/fiftyfin"
	types2 "github.com/epifi/gamma/preapprovedloan/wire/types"
	"github.com/epifi/gamma/scripts/pal/config"
	"github.com/epifi/gamma/scripts/pal/helper"
	"github.com/epifi/gamma/scripts/pal/job"
	"github.com/epifi/gamma/scripts/pal/job/credit_report_purge"
	"github.com/epifi/gamma/scripts/pal/job/lamf"
	lmsDataDifference "github.com/epifi/gamma/scripts/pal/job/lms_data_difference"
	"github.com/epifi/gamma/scripts/pal/job/vendors"
	"github.com/epifi/gamma/scripts/pal/job/vendors/idfc"
	types3 "github.com/epifi/gamma/scripts/pal/wire/type"
	vendorMappingDao "github.com/epifi/gamma/vendormapping/dao"
)

func newGormTxnExecutorProvider(dbConn *gorm.DB) storageV2.TxnExecutor {
	return storageV2.NewGormTxnExecutor(dbConn)
}

func notificationConfigProvider(conf *config.Config) *common.Notification {
	return conf.Notification
}

func InitialiseJobRegistry(
	crdb types.LoansFederalPGDB,
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	epifiDb types.EpifiCRDB,
	pgdb types.PostgresPGDB,
	pgdbTxnExecutor types.PGDBTxnExecutor,
	palVgClient palVgPb.PreApprovedLoanClient,
	accountVgClient accountVgPb.AccountsClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	bankCustClient bankCustPb.BankCustomerServiceClient,
	authClient authPb.AuthClient,
	commsClient commsPb.CommsClient,
	savingsClient savingsPb.SavingsClient,
	s3Client s3.S3Client,
	broker events.Broker,
	conf *config.Config,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	llPalVgClient llVgPb.LiquiloansClient,
	profileClient profilePb.ProfileClient,
	authOrchClient orchPb.OrchestratorClient,
	persistentQueue persistentqueue.PersistentQueue,
	salaryClient salaryPb.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	fePalClient fePalPb.PreApprovedLoanClient,
	creditReportClient creditreportpb.CreditReportClient,
	authDb types.AuthPGDB,
	awsConf aws.Config,
	idfcVgClient idfcVgPb.IdfcClient,
	palClient preapprovedloan.PreApprovedLoanClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	collectionClient collection.CollectionClient,
	docsClient docsPb.DocsClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient fiftyfin.FiftyFinClient,
	segmentationServiceClient segment.SegmentationServiceClient,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	abflClient abflVgPb.AbflClient,
	provider types3.CrdbConnProvider,
	commonGenConf *commonGenConf.CreditReportConfig,
	dataDevS3Client types3.DataDevS3Client,
	cryptorStore *cryptormap.InMemoryCryptorStore,
	mfExternalOrderClient mfExternalPb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	eKycVgClient ekycPb.EKYCClient,
	moneyviewVgPbClient moneyviewVgPb.MoneyviewClient,
	nudgeClient nudge.NudgeServiceClient,
	vendorMappingDao vendorMappingDao.IVendorMappingDAO,
	cibilReportPublisher queue.Publisher,
	temporalClient temporalClient.Client,
	credgenicsClient credgenics.CredgenicsClient,
	redisClient types2.FireflyRedisStore,
	palS3client types3.PalS3Client,
	palCxClient palCxPb.CxClient,
	consentClient consent.ConsentClient,
	devAuthClient developer.DevAuthClient,
	devLivenessClient livenessDevPb.DevLivenessClient,
	devUserClient userDevPb.DevUserClient,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	finFluxVgClient finflux.FinfluxClient,
	sgLmsApiGateway sgLmsApiGatewayPb.LmsClient,
	lendenClient ldcVgPb.LendenClient,
	analyticsClient analytics.AnalyticsClient,
	breClient brePb.BreClient,
	workerGenConf *palWorkerGConf.Config,
	caPgdb types.ConnectedAccountPGDB,
	locationClient locationPb.LocationClient,
	digitapVgClient digitapVgPb.DigitapServiceClient,
	vendorApiGenConf *vendorapiGenConf.Config,
) *job.Registry {
	wire.Build(
		types.LoansFederalPGDBGormDBProvider,
		newGormTxnExecutorProvider,
		notificationConfigProvider,
		idgen.NewClock,
		idgen.WireSet,
		datetimePkg.WireDefaultTimeSet,
		daoImpl.LoanOffersDaoMultiDBWireSet,
		daoImpl.LoanOfferEligibilityCriteriaDaoMultiDBWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanStepExecutionsDaoMultiDBWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		crDaoV2.CreditReportFlattenPgDaoWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		daoImpl.LoanPaymentRequestsDaoWireSet,
		daoImpl.LoanRequestsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentPayoutDaoWireSet,
		collectionDaoImpl.LeadWireSet,
		collectionDaoImpl.AllocationWireSet,
		events2.AcqEventPublisherWireSet,
		daoUtils.NewDBProvider,
		// we use DAO because raw report is not exposed in RPC
		crDaoV2.CreditReportWireSet,
		helper.NewHelper,

		agreement.NewAgreementProvider,
		job.NewLiquiLoansPrepaymentJob,
		job.NewProcessFiEligibleBaseJob,
		job.NewProcessEligibleBaseFromVendorJob,
		job.NewDeleteRiskyUsersJob,
		job.NewRetryFetchDetailsV2Job,
		job.NewGetStatementJob,
		job.NewPopulateLecFromVendorFileJob,
		job.NewDeactivateAllLoanApplicationJob,
		job.NewPurgeCkycDataJob,
		job.NewPopulateLecFromVendorJob,
		job.NewSendEmiCommsJob,
		job.NewCreateNewOfferJob,
		job.NewExtendExpiryFedOffersJob,
		job.NewCreditReportFlattenAdhocJob,
		job.NewCreditReportFlattenJob,
		job.NewProcessLlEligibleBaseJob,
		job.NewUpdateInstallmentInfoJob,
		job.NewFetchLlRejectedBaseJob,
		job.NewTriggerLlActivityJob,
		job.NewSetLoanProgramInLoanOffersJob,
		job.NewBackfillCkycVideoPQJob,
		job.NewLoanAccountReconJob,
		job.NewCryptS3DataJob,
		job.NewRepaymentCaptureJob,
		job.NewLoanActivityBackFillJob,
		job.NewGenerateVendorBureauFileJob,
		job.NewDisableOffersJob,
		job.NewLoanSchemeBackFillJob,
		job.NewSyncLoanAccountJob,
		job.NewDeactivateAllOffersJob,
		job.NewDeactivateOfferIfLoanTakenJob,
		job.NewCleanUpPayoutsJob,
		job.NewResolveLOECJob,
		job.NewProcessPrepaymentAtVendorJob,
		job.NewVendors,
		vendors.NewFederal,
		vendors.NewLiquiloans,
		idfc.NewProcessor,
		job.NewRegistry,
		job.NewInitiateAnalysisJob,
		palHelper.NewRpcHelper,
		job.NewSiCollectionJob,
		job.NewBackfillLoanAccountIdJob,
		job.NewResolveEMIDefaultsJob,
		job.NewEsEsignTriggerJob,
		job.NewCreateIdfcCugOffersJob,
		job.NewDeleteLoanAccountsJob,
		job.NewGetAddressJob,
		job.NewFixLoanApplicantJob,
		job.NewResetChargesForSubventionUsers,
		job.NewBackfillUTRJob,
		job.NewBureauReportingSubventionJob,
		preApprovedLoanS3ClientProvider,
		job.NewUpdateVendorReqIdIdfcJob,
		job.NewBrePipingJob,
		job.NewSyncLoanAccountLLJob,
		job.NewDeleteDuplicateLoanLiiJob,
		job.NewDeactivateFederalOffersJob,
		job.NewProcessScrubDataJob,
		job.NewProcessEncryptedScrubDataJob,
		job.NewMvPanDedupeJob,
		job.NewFetchRepaymentScheduleJob,
		job.NewFetchLoanStatusJob,
		job.NewLamfResetFailedOfferGenLseJob,
		job.NewBackfillLoanActivitiesUTRJob,
		newMultiDbDoOnce,
		palHelper.NewCommsHelper,
		job.NewDeleteUserLoanDataJob,
		job.NewNormalizeCibilJob,
		job.NewCollectionsSyncLeadJob,
		job.NewBackfillCibilCustIdMismatch,
		job.NewBackfillCibilReportsJob,
		job.NewReconLoanPreClosureJob,
		job.NewPostNachPaymentsToCredgenicsJob,
		lamf.NewLamfStuckUserManualResolution,
		job.NewCollectionsAllocationSyncJob,
		job.NewProcessBillzyPaymentsJob,
		lmsDataDifference.NewJob,
		job.NewFederalDPDUserDataJob,
		job.NewUploadITRFileToSFTPJob,
		types2.CacheStorageProvider,
		lamf.NewCreateInterestAccountPaymentInstrumentJob,
		job.NewCreditReportDataRecoveryJob,
		job.NewUpdateLrLseStatusJob,
		job.NewAutoPayJob,
		job.NewAbflRegulatoryDataJob,
		job.NewUpdateAbflLoanStatus,
		job.NewStoreExperianConsentsJob,
		credit_report_purge.NewSoftDeleteCreditReportDataJob,
		credit_report_purge.NewSoftDeleteOldCreditReportDataJob,
		credit_report_purge.NewHardDeleteFlattenedCreditReportDataJob,
		credit_report_purge.NewUpdateCreditReportDownloadDataJob,
		job.NewOneTimeJob,
		job.NewSyncAllActiveLeadsCollectionsJob,
		job.NewApplyOfferDiscountAfterUserDropoffJob,
		credit_report_purge.NewSoftDeleteCibilReportDataJob,
		credit_report_purge.NewHardDeleteFlattenedCibilReportDataJob,
		loandataprovider.WireSet,
		fiftyfin3.WireVendorDataProviderSet,
		wire.NewSet(cache.NewInMemoryCacheService, wire.Bind(new(cache.CacheStorage), new(*cache.InMemoryCacheService))),
		lamf.NewLoanAccountCreationLAMFJob,
		job.NewUpdateLoanOffersJob,
		userdata.CommonUserDataProviderWireSet,
		job.NewBackfillRejectedBreDataJob,
		job.NewEpfoDataFetchJob,
		SecureHttpClientNilSignCtxWireSet,
		vendorapiPkg.New,
		envProvider,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		job.NewFedLentraCugTestingJob,
		job.NewRetryDedupeJob,
		job.NewLoanFunnelTrackingJob,
		job.NewUpdateFederalLoanRequestVendorIdJob,
		job.NewLoansOnboardingJob,
	)
	return &job.Registry{}
}

func envProvider(conf *config.Config) string {
	return conf.Application.Environment
}

func nilSigningContextProvider() *dsig.SigningContext {
	return nil
}

func preApprovedLoanS3ClientProvider(awsConf aws.Config, conf *config.Config) types2.PreApprovedLoanS3Client {
	return s3.NewClient(awsConf, conf.Aws.S3.PreapprovedloanBucketName)
}

func newMultiDbDoOnce(dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB]) onceV2.MultiDbDoOnce {
	return onceV2.NewMultiDbDoOnce(dbResourceProvider)
}

var SecureHttpClientNilSignCtxWireSet = wire.NewSet(SecureHttpClientProviderNew, NilSigningContextProvider)

func NilSigningContextProvider() *dsig.SigningContext {
	return nil
}

func getHttpClient(gconf *vendorapiGenConf.Config, env string, conf *config.Config, insecure ...bool) *http.Client {
	insecureSkipVerifyTLS := false
	if len(insecure) == 1 && insecure[0] {
		insecureSkipVerifyTLS = true
	}
	if cfg.IsSimulatedEnv(env) || insecureSkipVerifyTLS {
		return vendorClient.NewHttpClient(gconf.HttpClientConfig())
	}

	cert, key := cfg.GetCertAndKeysFromSecret("",
		conf.Secrets.Ids[vgConf.EpiFiFederalClientSslCert], conf.Secrets.Ids[vgConf.EpiFiFederalClientSslKey])

	return vendorClient.NewHttpClientWithSSLCert(cert, key, gconf.HttpClientConfig())
}

func SecureHttpClientProviderNew(gconf *vendorapiGenConf.Config, env string, conf *config.Config) *http.Client {
	return getHttpClient(gconf, env, conf, false)
}
