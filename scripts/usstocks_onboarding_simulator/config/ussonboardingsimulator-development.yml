Application:
  Environment: "development"


Aws:
  Region: "ap-south-1"

DbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    AppName: "celestial"
    StatementTimeout: 5s
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  EPIFI_WEALTH:
    DbType: "CRDB"
    AppName: "celestial"
    StatementTimeout: 5s
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi_wealth"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  US_STOCKS_ALPACA:
    DbType: "CRDB"
    AppName: "celestial"
    StatementTimeout: 5s
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "usstocks_alpaca"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
      EnableMultiDBSupport: true
      DBResolverList:
        - Alias: "usstocks_alpaca_pgdb"
          DbDsn:
            DbType: "PGDB"
            Host: "localhost"
            Port: 5432
            Username: "root"
            Name: "usstocks_alpaca"
            SSLMode: "disable"
            SecretName: "{\"username\": \"root\", \"password\": \"\"}"

ActorDb:
  Host: "localhost"
  Port: 5432
  Name: "actor"
  DbType: "PGDB"
  SSLMode: "disable"
  AppName: "actor"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

SimulatorDb:
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "simulator"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

UserSavingsRedis:
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 13
