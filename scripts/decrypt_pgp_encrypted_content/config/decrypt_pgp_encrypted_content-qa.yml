Application:
  Environment: "qa"
  Name: "decrypt_pgp_encrypted_content"

AWS:
  Region: "ap-south-1"

SftpStatementAwsBucket:
  AwsBucket: "epifi-federal-monthly-statements"
  SrcFolder: "InProcess"
  DstFolder: "Processed"
  AclString: "private"

Flags:
  IsDecryptionEnable: true

Secrets:
  Ids:
    #Federal
    EpifiFederalPgpPrivateKey: "vg_pgp_epifi-key-fed-api_qa_20201231.privkey"
    FederalPgpPublicKey: "vg_pgp_federal-dummy-key-for-sim_qa_20201231.pubkey"
    EpifiFederalPgpPassphrase: "vg_pgp_epifi-key-passphrase-fed-api_qa_20201231.str"
