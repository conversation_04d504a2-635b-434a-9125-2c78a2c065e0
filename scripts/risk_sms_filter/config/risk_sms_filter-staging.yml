Application:
  Environment: "staging"
  Name: "risk_sms_filter"

AWS:
  Region: "ap-south-1"

S3Configs:
  account:
    SourceBucketName: "sms-solutioning-bucket-archived"
    DestBucketName: "sms-qa-risk-features"
    CheckpointFileKey: "processed_output/checkpoint.json"
    SlackSecretName: " "
    SummaryFileKey: "summary/account_summary_v7.json"
  lea:
    SourceBucketName: "sms-solutioning-bucket-archived"
    DestBucketName: "sms-qa-risk-features"
    CheckpointFileKey: "processed_output/checkpoint.json"
    SlackSecretName: " "
    SummaryFileKey: "summary/lea_summary_v7.json"
  other:
    SourceBucketName: " "
    DestBucketName: " "
    CheckpointFileKey: " "
    SlackSecretName: " "
    SummaryFileKey: "summary/other_summary_v7.json"
