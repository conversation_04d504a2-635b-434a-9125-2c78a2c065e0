package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"os"
	"strings"
	"time"

	awss3 "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	confpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	mfNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/mutualfund"
	"github.com/epifi/be-common/pkg/logger"
	woWorkflowPb "github.com/epifi/gamma/api/wealthonboarding/workflow"
	"github.com/epifi/gamma/scripts/mf_aum_reconciliation/config"
)

var (
	rtaFlag = flag.String("rta", "", "rta")
)

const (
	aumFilePath     = "./aumFilePath.csv"
	cams            = "cams"
	karvy           = "karvy"
	aumFileS3Folder = "capture_mf_aum_reconciliation"
	aumFile         = "aum_file"
)

func main() {
	flag.Parse()
	if rtaFlag == nil || *rtaFlag == "" {
		logger.ErrorNoCtx("rta should not be empty")
		os.Exit(1)
	}

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
		os.Exit(1)
	}

	rta := *rtaFlag
	rtaVendor, err := getVendor(strings.ToLower(rta))
	if err != nil {
		logger.ErrorNoCtx("error in getVendor", zap.Error(err))
		os.Exit(1)
	}

	aumFileContent, err := os.ReadFile(aumFilePath)
	if err != nil {
		logger.Panic("failed to load aum file", zap.Error(err))
		os.Exit(1)
	}

	celestialConn := epifigrpc.NewConnByService(cfg.CELESTIAL_SERVICE)
	celestialClient := celestialPb.NewCelestialClient(celestialConn)

	ctx := context.Background()
	awsConf, awsErr := confpkg.NewAWSConfig(ctx, conf.Aws.Region, false)
	if awsErr != nil {
		logger.ErrorNoCtx("failed to load aws config", zap.Error(awsErr))
		os.Exit(1)
	}
	s3Client := s3.NewClient(awsConf, conf.Aws.S3.BucketName)

	path := getS3PathForAumFile(rtaVendor, time.Now())

	logger.InfoNoCtx("aum file path", zap.String("path", path))
	err1 := s3Client.Write(ctx, path, aumFileContent, string(awss3.ObjectCannedACLBucketOwnerFullControl))
	if err1 != nil {
		logger.ErrorNoCtx("failed to write to url", zap.Error(err1))
		os.Exit(1)
	}

	payloadBytes, _ := protojson.Marshal(&woWorkflowPb.CaptureMfAumFileRequest{
		RtaType:    rtaVendor,
		AumFileUrl: path,
	})

	wfRes, wfErr := celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{Params: &celestialPb.WorkflowCreationRequestParams{
		Version: workflowPb.Version_V0,
		Type:    celestial.GetTypeEnumFromWorkflowType(mfNs.CaptureMfAumFile),
		Payload: payloadBytes,
		ClientReqId: &workflowPb.ClientReqId{
			Id:     uuid.NewString(),
			Client: workflowPb.Client_WEALTH_ONBOARDING,
		},
		Ownership:        commontypes.Ownership_EPIFI_WEALTH,
		QualityOfService: celestialPb.QoS_GUARANTEED,
	}})
	if te := epifigrpc.RPCError(wfRes, wfErr); te != nil {
		logger.Error(ctx, "error in initiating capture mf aum file workflow", zap.Error(te))
	}
	logger.Info(ctx, "workflow initiated capture mf aum file workflow", zap.String("workflowId", wfRes.GetParams().GetWorkflowRequestId()))
	os.Exit(1)
}

func getVendor(rta string) (commonvgpb.Vendor, error) {
	switch rta {
	case cams:
		return commonvgpb.Vendor_CAMS, nil
	case karvy:
		return commonvgpb.Vendor_KARVY, nil

	default:
		return commonvgpb.Vendor_VENDOR_UNSPECIFIED, errors.New("vendor is invalid")
	}
}

func getS3PathForAumFile(vendor commonvgpb.Vendor, curTime time.Time) string {
	return fmt.Sprintf("%s/%s/%s/%s",
		aumFileS3Folder,
		strings.ToLower(vendor.String()),
		aumFile,
		fmt.Sprintf("aumFilePath_%s.csv", curTime.Format("20060102_150405")),
	)
}
