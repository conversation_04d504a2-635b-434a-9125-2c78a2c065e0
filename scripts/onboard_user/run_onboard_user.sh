#!/bin/bash
set -e

GOMODCACHE=$(go env GOMODCACHE)
BE_COMMON_VERSION=$(go list -m -f '{{.Version}}' github.com/epifi/be-common)
BE_COMMON_PATH=$GOMODCACHE/github.com/epifi/be-common@$BE_COMMON_VERSION
# ensure the be-common is fetched before accessing any config files from BE_COMMON_PATH
go get github.com/epifi/be-common@$(go list -f '{{.Version}}' -m github.com/epifi/be-common | awk -F "-" '{print $3}')

assume_role() {
	echo "Smoke test script: assume_role()"
	date
	aws_credentials_json=$(aws sts assume-role --role-arn "${ROLE_ARN}" --role-session-name uatSession --region ap-south-1)
	export AWS_ACCESS_KEY_ID=$(echo "$aws_credentials_json" | jq --exit-status --raw-output .Credentials.AccessKeyId)
	export AWS_SECRET_ACCESS_KEY=$(echo "$aws_credentials_json" | jq --exit-status --raw-output .Credentials.SecretAccessKey)
	export AWS_SESSION_TOKEN=$(echo "$aws_credentials_json" | jq --exit-status --raw-output .Credentials.SessionToken)
	echo "Smoke test script: assume_role() - end"
	date
}

fetch_secrets () {
	echo "Smoke test script: fetch_secrets()"
	date
	mkdir -p ~/.pki
	aws --region ap-south-1 secretsmanager get-secret-value --cli-connect-timeout 1 --profile=$AWS_PROFILE --secret-id epifi/pki/letsencrypt | jq -r '.SecretString| fromjson | .key' > ~/.pki/letsencrypt.key
	aws --region ap-south-1 secretsmanager get-secret-value --cli-connect-timeout 1 --profile=$AWS_PROFILE --secret-id epifi/pki/letsencrypt | jq -r '.SecretString| fromjson | .cert' > ~/.pki/letsencrypt.crt
	aws --region ap-south-1 secretsmanager get-secret-value --cli-connect-timeout 1 --profile=$AWS_PROFILE --secret-id epifi/pki/letsencrypt | jq -r '.SecretString| fromjson | .cacert' > ~/.pki/ca.crt
	echo "Smoke test script: fetch_secrets() - end"
	date
}

copy_configs() {
	echo "Onboard user script: copy_configs()"
	date
    mkdir -p ./output/onboard_user/config
    cp $BE_COMMON_PATH/pkg/cfg/config/* ./output/onboard_user/config/ 2>/dev/null || :
    cp ./cmd/auth/config/* ./output/onboard_user/config/ 2>/dev/null || :
    cp -r ./scripts/onboard_user/config/* ./output/onboard_user/config/ 2>/dev/null || :
	echo "Onboard user script: copy_configs() - end"
	date
}

build_onboard_user() {
	echo "Onboard user script: build_onboard_user()"
	set -x
	echo "building onboard_user binary"
	time go test -c -o ./output/onboard_user/onboard_user_bin -v ./scripts/onboard_user
	echo "Onboard user script: build_onboard_user() - end"
}

run_onboard_user() {
	echo "Onboard user script: run_onboard_user()"
	set -x
	phone=$2
	email=$3
	pan=$4
	money=${5:-"10000"}
	KycLevel=${6:-"DEFAULT"}
	FiniteCode=${7:-"NOFINITECODE"}
	DeviceId=${8:-"RANDOMDEVICEID"}
    echo "running onboard_user"
	./output/onboard_user/onboard_user_bin -test.v -test.timeout 15m -test.run=TestOnboardUser -phoneno "$phone" -emailid "$email" -pan "$pan" -addmoney "$money" -kyclevel "$KycLevel" -finitecode "$FiniteCode" -deviceid "$DeviceId"
	echo "Onboard user script: run_onboard_user() - end"
}

# Command to run it locally
export CONFIG_DIR=$PWD/output/onboard_user/config
export ENVIRONMENT=$ENVIRONMENT
export AWS_PROFILE=epifi-$ENVIRONMENT
export AWS_SDK_LOAD_CONFIG=true
export REMOTE_DEBUG=disable

fetch_secrets
case $1 in
build)
    copy_configs
    build_onboard_user
    ;;
run)
    # shellcheck disable=SC2068
    run_onboard_user $@
    ;;
build_and_run)
	copy_configs
	build_onboard_user
	# shellcheck disable=SC2068
	run_onboard_user $@
	;;
*)
	echo "input one of build, run or build_and_run"
	;;
esac
