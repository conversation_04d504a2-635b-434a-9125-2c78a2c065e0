// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	"github.com/epifi/gamma/scripts/ift/ift_recon/reconcilation"
	"github.com/epifi/gamma/scripts/ift/ift_recon/reconcilation/processor"
)

// Injectors from wire.go:

func InitializeReconFactory(db types.EpifiCRDB, vgAccountsClient accounts.AccountsClient, numDays int, orderClient order.OrderServiceClient) *reconcilation.ReconProcessorFactory {
	dailyOutwardsPoolAccountReconProcessor := processor.NewDailyOutwardsPoolAccountReconProcessor(db, vgAccountsClient, numDays)
	inwardSwiftTxnReconProcessor := processor.NewInwardSwiftTxnReconProcessor(vgAccountsClient, orderClient)
	reconProcessorFactory := reconcilation.NewReconProcessorFactory(dailyOutwardsPoolAccountReconProcessor, inwardSwiftTxnReconProcessor)
	return reconProcessorFactory
}
