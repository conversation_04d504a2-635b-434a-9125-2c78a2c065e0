Application:
  Environment: "qa"
  Name: "es"

Aws:
  Region: "ap-south-1"

ESHost: "https://vpc-deploy-logs-tnhfidfvohz3zxmrfwndbitwxa.ap-south-1.es.amazonaws.com/"
ESHostSecure: "https://logs-secure.pointz.in/"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "/home/<USER>/epifi/gamma/config/crdb/qa/"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"


Secrets:
  Ids:
    SecureLogs: "qa/elasticsearch/deploy-logs-secure"
