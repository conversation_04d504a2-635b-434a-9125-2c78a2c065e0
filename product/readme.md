## PRODUCT SERVICE ##

Service to check the onboarding status of user in various (core/non-core) products. 
This service will also be used to check different things across different products (e.g. CX Priority). 

This is a sync service which makes call to relevant rpcs of the products to get an accumulated status. 

[Products](https://github.com/epiFi/protos/blob/master/api/product/enums.proto#L9)

[Product Statuses](https://github.com/epiFi/protos/blob/master/api/product/enums.proto#L18)

How to add a new product :-
1. Add the product enum to the [products list](https://github.com/epiFi/protos/blob/master/api/product/enums.proto#L9)
2. Create a separate [product folder](https://github.com/epiFi/gamma/tree/master/product/productproc) in the product service
3. Implement the interface for the newly added product
4. By default, use product status as onboarding in progress to avoid breaking other flows which requires a valid product status from all products
