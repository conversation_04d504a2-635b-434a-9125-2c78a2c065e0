//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"
	"gorm.io/gorm"

	"github.com/epifi/gamma/securities/catalog/dao"
	"github.com/epifi/gamma/securities/catalog/historicalpricesfetcher"
	"github.com/epifi/gamma/securities/catalog/ingester"

	"github.com/redis/go-redis/v9"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/lock"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	ussCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	"github.com/epifi/gamma/securities/catalog"
	catalogConsumer "github.com/epifi/gamma/securities/catalog/consumer"
	catalogDao "github.com/epifi/gamma/securities/catalog/dao"
	genConf "github.com/epifi/gamma/securities/config/genconf"
	wtypes "github.com/epifi/gamma/securities/wire/types"
)

func stocksGormDbProvider(db types.StocksPGDB) *gorm.DB {
	return db
}

func SecuritiesRedisStoreProvider(cl wtypes.SecuritiesRedisStore) *redis.Client {
	return cl
}

func InitializeCatalogService(
	db types.StocksPGDB,
	genConf *genConf.Config,
	vgCatalogClient vgCatalogPb.CatalogClient,
	stockCatalogRefreshPublisher wtypes.StockCatalogRefreshPublisher,
	redisClient wtypes.SecuritiesRedisStore,
) *catalog.Service {
	wire.Build(
		SecuritiesRedisStoreProvider,
		stocksGormDbProvider,
		catalog.NewService,
		storagev2.IdempotentTxnExecutorWireSet,
		dao.SecurityListingDaoWireSet,
		dao.SecuritiesDaoWireSet,
		dao.HistoricalPriceDaoWireSet,
		historicalpricesfetcher.PGDBHistoricalPricesDAOWireSet,
		historicalpricesfetcher.RedisHistoricalPricesFetcherWireSet,
	)
	return &catalog.Service{}
}

func InitializeCatalogConsumerService(
	db types.StocksPGDB,
	genConf *genConf.Config,
	vgCatalogClient vgCatalogPb.CatalogClient,
	ussCatalogClient ussCatalogPb.CatalogManagerClient,
	publisher wtypes.AddNewSecuritiesPublisher,
	redisClient types.InvestmentRedisStore) *catalogConsumer.Service {
	wire.Build(
		stocksGormDbProvider,
		catalogConsumer.NewService,
		catalogDao.SecuritiesDaoWireSet,
		catalogDao.SecurityListingDaoWireSet,
		catalogDao.HistoricalPriceDaoWireSet,
		types.InvestmentRedisStoreRedisClientProvider,
		lock.RedisV9LockManagerWireSet,
		ingester.CatalogIngesterSet,
	)
	return &catalogConsumer.Service{}
}
