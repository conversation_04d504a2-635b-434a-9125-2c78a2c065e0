package ingester

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/monitoring"
	securitiesPb "github.com/epifi/gamma/api/securities/catalog"
	ussCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vendorPb "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
	"github.com/epifi/gamma/securities/catalog/dao"
	genConf "github.com/epifi/gamma/securities/config/genconf"
)

var (
	// list of exchanges to fetch securities from
	bridgewiseExchanges             = []string{"NSEI", "BSE", "NYSE", "NasdaqGS", "NYSEAM", "OTCPK"}
	bridgewiseIndianExchanges       = []string{"NSEI", "BSE"}
	bridgewiseUsExchanges           = []string{"NYSE", "NasdaqGS", "NYSEAM", "OTCPK"}
	bridgewiseToInternalExchangeMap = map[string]securitiesPb.Exchange{
		"NSEI":     securitiesPb.Exchange_EXCHANGE_INDIA_NSE,
		"BSE":      securitiesPb.Exchange_EXCHANGE_INDIA_BSE,
		"NYSE":     securitiesPb.Exchange_EXCHANGE_USA_NYSE,
		"NASDAQGS": securitiesPb.Exchange_EXCHANGE_USA_NASDAQ,
		"NYSEAM":   securitiesPb.Exchange_EXCHANGE_USA_ASE,
		"OTCPK":    securitiesPb.Exchange_EXCHANGE_USA_OTC,
	}

	ErrMaxPagesExceeded = errors.New("Max pages exceeded")
)

//go:generate mockgen -source=catalog_data_ingester.go -destination=./mocks/mock_ingester.go -package=mocks

type CatalogDataIngester interface {
	IngestByPage(ctx context.Context, pageNum int32) error
	IngestByISINs(ctx context.Context, isins []string) error
}

// Compile-time assertion to ensure CatalogDataIngesterImpl implements CatalogDataIngester
var _ CatalogDataIngester = (*CatalogDataIngesterImpl)(nil)

var CatalogIngesterSet = wire.NewSet(
	NewCatalogIngester,
	wire.Bind(new(CatalogDataIngester), new(*CatalogDataIngesterImpl)),
)

type CatalogDataIngesterImpl struct {
	securityDao         dao.SecuritiesDao
	securityListingsDao dao.SecurityListingsDao
	vgCatalogClient     vgCatalogPb.CatalogClient
	ussCatalogClient    ussCatalogPb.CatalogManagerClient
	conf                *genConf.Config
}

func NewCatalogIngester(
	conf *genConf.Config,
	securityDao dao.SecuritiesDao,
	securityListingsDao dao.SecurityListingsDao,
	vgCatalogClient vgCatalogPb.CatalogClient,
	ussCatalogClient ussCatalogPb.CatalogManagerClient,
) *CatalogDataIngesterImpl {
	return &CatalogDataIngesterImpl{
		conf:                conf,
		securityDao:         securityDao,
		securityListingsDao: securityListingsDao,
		vgCatalogClient:     vgCatalogClient,
		ussCatalogClient:    ussCatalogClient,
	}
}

func (s *CatalogDataIngesterImpl) IngestByPage(ctx context.Context, pageNum int32) error {
	if int(pageNum) > s.conf.AddNewSecuritiesConfig().MaximumPageNum() {
		return ErrMaxPagesExceeded
	}

	ingestionErrorCount := 0
	logger.Info(ctx, "processing add new securities", zap.Any("pageNum", pageNum), zap.Strings("exchanges", s.getExchanges()))

	//nolint: gosec
	resp, err := s.vgCatalogClient.GetCompanies(ctx, &vgCatalogPb.GetCompaniesRequest{
		Header:    &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
		Exchanges: s.getExchanges(),
		Page:      pageNum,
		PageSize:  int32(s.conf.AddNewSecuritiesConfig().PageSize()),
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		if resp.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "Page number is out of bounds", zap.Any("page number", pageNum))
			return epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error while calling GetCompanies vg", zap.Error(rpcErr))
		return rpcErr
	}

	for _, companyData := range resp.GetCompanies().GetData() {
		err = s.ingestCompanyAndListings(ctx, companyData)
		if err != nil {
			logger.Error(ctx, "error while ingesting company and listings", zap.Error(err), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
			ingestionErrorCount++
		}
	}

	if ingestionErrorCount > 0 {
		monitoring.KibanaInfoServiceMonitor(ctx, cfg.SECURITIES_SERVICE, "count of security ingestion failures", zap.Int("ingestionErrorCount", ingestionErrorCount))
	}

	return nil
}

func (s *CatalogDataIngesterImpl) IngestByISINs(ctx context.Context, isins []string) error {
	return epifierrors.ErrUnimplemented
}

func (s *CatalogDataIngesterImpl) ingestCompanyAndListings(ctx context.Context, companyData *vendorPb.CompanyDetails) error {
	security, err := s.upsertCompany(ctx, companyData)
	if err != nil {
		logger.Error(ctx, "error while get or create company data", zap.Error(err), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
		return err
	}

	securityListingsVg, vgErr := s.getSecurityListingsFromVg(ctx, security.GetVendorSecurityId())
	if vgErr != nil {
		logger.Error(ctx, "error while fetching security listings from vg", zap.Error(vgErr), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
		return vgErr
	}

	var securityListings []*securitiesPb.SecurityListing

	for _, securityListingVg := range securityListingsVg.GetAssetDetails() {
		if _, ok := bridgewiseToInternalExchangeMap[strings.ToUpper(securityListingVg.GetExchangeSymbol())]; !ok {
			// if exchange is not handled internally skip parsing, currently only Indian and US stocks are ingested
			continue
		}
		externalId, generateErr := s.generateSecurityListingExternalId(ctx, securityListingVg.GetTickerSymbol(), securityListingVg.GetExchangeSymbol())
		if generateErr != nil {
			logger.Error(ctx, "error while calling createOrGetSecurityListingExternalId", zap.Error(generateErr),
				zap.Any("symbol", securityListingVg.GetTickerSymbol()), zap.Any("exchange", securityListingVg.GetExchangeSymbol()),
				zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()), zap.Any("tradingItemId", securityListingVg.GetTradingItemId()))
			return generateErr
		}
		securityListing := convertTradingItemToSecurityListing(securityListingVg, security.GetId(), externalId)
		securityListings = append(securityListings, securityListing)
	}

	upsertErr := s.securityListingsDao.BatchUpsert(ctx, securityListings, nil)
	if upsertErr != nil {
		var vendorListingIds []int64
		for _, securityListing := range securityListingsVg.GetAssetDetails() {
			vendorListingIds = append(vendorListingIds, securityListing.GetTradingItemId())
		}
		logger.Error(ctx, "error while upserting security listings", zap.Error(upsertErr),
			zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()), zap.Any("vendorListingIds", vendorListingIds))
		return upsertErr
	}
	return nil
}

func convertAssetDetailsToSecurityPb(ctx context.Context, securityData *vendorPb.CompanyDetails) *securitiesPb.Security {
	// This parsing logic will be specific to bridgewise
	// Ex: If Bridgewise provides us Trading Companies and Distributors, replace all converts it to Trading Companies & Distributors
	gicsSectorType, ok := MapGICSSectorTypeToEnum[strings.ReplaceAll(securityData.GetGicsSectorName(), "and", "&")]
	if !ok {
		logger.Info(ctx, "GICS Sector Type unavailable", zap.String("GICS Sector Type", securityData.GetGicsSectorName()))
		gicsSectorType = securitiesPb.GICSSectorType_GICS_SECTOR_TYPE_UNSPECIFIED
	}
	gicsIndustryGroupType, ok := MapGICSIndustryGroupTypeToEnum[strings.ReplaceAll(securityData.GetGicsIndustryGroupName(), "and", "&")]
	if !ok {
		logger.Info(ctx, "GICS Industry Group Type unavailable", zap.String("GICS Industry Group Type", securityData.GetGicsIndustryGroupName()))
		gicsIndustryGroupType = securitiesPb.GICSIndustryGroupType_GICS_INDUSTRY_GROUP_TYPE_UNSPECIFIED
	}
	gicsIndustryType, ok := MapGICSIndustryTypeToEnum[strings.ReplaceAll(securityData.GetGicsIndustryName(), "and", "&")]
	if !ok {
		logger.Info(ctx, "GICS Industry Type unavailable", zap.String("GICS Industry Type", securityData.GetGicsIndustryName()))
		gicsIndustryType = securitiesPb.GICSIndustryType_GICS_INDUSTRY_TYPE_UNSPECIFIED
	}
	return &securitiesPb.Security{
		SecurityType:     securitiesPb.SecurityType_SECURITY_TYPE_STOCK,
		SecurityName:     securityData.GetCompanyName(),
		Vendor:           vendorgateway.Vendor_BRIDGEWISE,
		VendorSecurityId: strconv.Itoa(int(securityData.GetCompanyId())),
		SecurityDetails: &securitiesPb.SecurityDetails{
			SecurityType: &securitiesPb.SecurityDetails_StockDetails{
				StockDetails: &securitiesPb.StockDetails{
					StockName:                securityData.GetCompanyName(),
					StockShortName:           securityData.GetCompanyNameShort(),
					WebsiteUrl:               securityData.GetWebsite(),
					RegionName:               securityData.GetRegionName(),
					IncorporationCountryName: securityData.GetIncorporationCountryName(),
					GicsSectorType:           gicsSectorType,
					GicsIndustryType:         gicsIndustryType,
					GicsIndustryGroupType:    gicsIndustryGroupType,
				},
			},
		},
	}
}

func convertTradingItemToSecurityListing(tradingItem *vendorPb.AssetDetails, securityId, externalId string) *securitiesPb.SecurityListing {
	return &securitiesPb.SecurityListing{
		ExternalId:       externalId,
		SecurityId:       securityId,
		Exchange:         bridgewiseToInternalExchangeMap[strings.ToUpper(tradingItem.GetExchangeSymbol())],
		Symbol:           tradingItem.GetTickerSymbol(),
		IsPrimaryListing: tradingItem.GetPrimaryFlag(),
		Status:           securitiesPb.ListingStatus_LISTING_STATUS_ACTIVE,
		Vendor:           vendorgateway.Vendor_BRIDGEWISE,
		VendorListingId:  strconv.Itoa(int(tradingItem.GetTradingItemId())),
		FinancialInfo:    &securitiesPb.FinancialInfo{},
	}
}

// Currently supporting US and Indian security listings only
// If US listing -> if id is present in uss catalog service, use that id as the external id, else generate a new external id with USS prefix
// If IN listing -> create external id with INS prefix
func (s *CatalogDataIngesterImpl) generateSecurityListingExternalId(ctx context.Context, symbol string, exchange string) (string, error) {
	var externalId string
	switch bridgewiseToInternalExchangeMap[strings.ToUpper(exchange)] {
	case securitiesPb.Exchange_EXCHANGE_INDIA_NSE, securitiesPb.Exchange_EXCHANGE_INDIA_BSE:
		logger.Info(ctx, "generating external id for indian securities", zap.String("symbol", symbol), zap.String("exchange", exchange))
		externalId = "INS" + idgen.RandAlphaNumericString(5)
	case securitiesPb.Exchange_EXCHANGE_USA_NYSE, securitiesPb.Exchange_EXCHANGE_USA_NASDAQ, securitiesPb.Exchange_EXCHANGE_USA_ASE, securitiesPb.Exchange_EXCHANGE_USA_OTC:
		resp, err := s.ussCatalogClient.GetStocks(ctx, &ussCatalogPb.GetStocksRequest{
			Identifiers: &ussCatalogPb.GetStocksRequest_Symbols{
				Symbols: &ussCatalogPb.RepeatedStrings{
					Ids: []string{symbol},
				},
			},
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
			if !resp.GetStatus().IsRecordNotFound() {
				return "", fmt.Errorf("error getting us stock id, skipping id generation: %s", symbol)
			}
			logger.Info(ctx, "symbol not found in us catalog service, generating new external id", zap.Error(err), zap.String("symbol", symbol), zap.String("exchange", exchange))
			externalId = "USS" + idgen.RandAlphaNumericString(5)
		} else {
			if _, ok := resp.GetStocks()[symbol]; !ok {
				logger.Info(ctx, "symbol not found in us catalog service, generating new external id", zap.Error(err), zap.String("symbol", symbol), zap.String("exchange", exchange))
				externalId = "USS" + idgen.RandAlphaNumericString(5)
				return externalId, nil
			}
			logger.Info(ctx, "symbol found in us catalog service", zap.String("symbol", symbol), zap.String("id", resp.GetStocks()[symbol].GetId()))
			externalId = resp.GetStocks()[symbol].GetId()
		}
	default:
		return "", fmt.Errorf("unknown exchange type %s", exchange)
	}
	return externalId, nil
}

func (s *CatalogDataIngesterImpl) upsertCompany(ctx context.Context, companyData *vendorPb.CompanyDetails) (*securitiesPb.Security, error) {
	security, getErr := s.securityDao.GetByVendorSecurityId(ctx, vendorgateway.Vendor_BRIDGEWISE, strconv.Itoa(int(companyData.GetCompanyId())),
		[]securitiesPb.SecurityFieldMask{
			securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID,
			securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR_SECURITY_ID,
		})
	if getErr != nil && errors.Is(getErr, epifierrors.ErrRecordNotFound) {
		securityObj := convertAssetDetailsToSecurityPb(ctx, companyData)
		logger.Info(ctx, "creating new security", zap.Any("securityObj", securityObj))

		var createErr error
		security, createErr = s.securityDao.Create(ctx, securityObj)
		if createErr != nil {
			logger.Error(ctx, "error while calling Create", zap.Error(createErr), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
			return nil, createErr
		}
	} else if getErr != nil {
		logger.Error(ctx, "error while calling GetByVendorSecurityId", zap.Error(getErr), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
		return nil, getErr
	}
	return security, nil
}

func (s *CatalogDataIngesterImpl) getSecurityListingsFromVg(ctx context.Context, companyId string) (*vgCatalogPb.GetCompanyTradingItemsResponse, error) {
	// Get security listings for a security
	tradingItems, tradingItemsErr := s.vgCatalogClient.GetCompanyTradingItems(ctx, &vgCatalogPb.GetCompanyTradingItemsRequest{
		Header:    &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
		CompanyId: companyId,
	})
	if rpcErr := epifigrpc.RPCError(tradingItems, tradingItemsErr); rpcErr != nil {
		logger.Error(ctx, "error while calling GetCompanyTradingItems vg", zap.Error(rpcErr), zap.String(logger.REQUEST_ID, companyId))
		return nil, rpcErr
	}
	return tradingItems, nil
}

func (s *CatalogDataIngesterImpl) getExchanges() []string {
	switch s.conf.AddNewSecuritiesConfig().ExchangeType() {
	case "INDIA":
		return bridgewiseIndianExchanges
	case "US":
		return bridgewiseUsExchanges
	default:
		return bridgewiseExchanges
	}
}
