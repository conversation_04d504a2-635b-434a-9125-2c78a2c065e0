Application:
  Environment: "qa"
  Name: "stocks"

Server:
  Ports:
    GrpcPort: 8096
    GrpcSecurePort: 9511
    HttpPort: 9886

StocksDb:
  DbType: "PGDB"
  AppName: "stocks"
  StatementTimeout: 10s
  Name: "stocks"
  EnableDebug: true
  SSLMode: "disable"
  SecretName: "qa/rds/epifimetis/stocks_dev_user"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"

StockCatalogRefreshPublisher:
  QueueName: "qa-stocks-catalog-refresh-queue"

StockCatalogRefreshSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "qa-stocks-catalog-refresh-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 600
        Period: 1m
    Namespace: "stocks"

HistoricalPricesRedisCache:
  TTL: 5m


SecuritiesCatalogAdditionPublisher:
  QueueName: "qa-securities-catalog-addition-queue"

SecuritiesCatalogAdditionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "qa-securities-catalog-addition-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 600
        Period: 1m
    Namespace: "stocks"

AddNewSecuritiesPublisher:
  QueueName: "qa-securities-catalog-addition-queue"

AddNewSecuritiesConfig:
  MaximumPageNum: 200
  PageSize: 100

SecuritiesHistoricalPricePublisher:
  QueueName: "qa-securities-historical-price-queue"

SecuritiesHistoricalPriceSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "qa-securities-historical-price-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 600
        Period: 1m
    Namespace: "stocks"
