Application:
  Environment: "qa"
  Name: "analyser"

Server:
  Ports:
    GrpcPort: 8096
    GrpcSecurePort: 9511
    HttpPort: 9999

EpifiWealthAnalyticsDb:
  AppName: "investment_analyser"
  StatementTimeout: 5m
  Name: "epifi_wealth_analytics"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms

AnalyserOrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-analyser-order-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "analyser"

AnalyserCategoryUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-analyser-category-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "analyser"

AnalyserAaTxnUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-analyser-aa-txn-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "analyser"

AnalyserInvestmentEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-analyser-investment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1s
    Namespace: "analyser"

AnalyserInvestmentAnalysisTaskSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-analyser-investment-analysis-task-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "analyser"

AnalyserHighPriorityInvestmentAnalysisTaskSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-analyser-high-priority-investment-analysis-task-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "analyser"

RefreshMFPortfolioAnalyticsSubscriber:
  StartOnServerStart: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-analyser-refresh-mf-portfolio-analytics-queue"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 4
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1
        Period: 1s
    Namespace: "analyser"

TransactionsProducer:
  StreamName: "qa-analyser-pinot-transactions-stream"

AATransactionsProducer:
  StreamName: "qa-analyser-pinot-aa-transactions-stream"

TransactionsUpdateProducer:
  StreamName: "qa-analyser-pinot-transactions-update-stream"

AATransactionsUpdateProducer:
  StreamName: "qa-analyser-pinot-aa-transactions-update-stream"

InvestmentAnalysisTaskPublisher:
  QueueName: "qa-analyser-investment-analysis-task-queue"

HighPriorityInvestmentAnalysisTaskPublisher:
  QueueName: "qa-analyser-high-priority-investment-analysis-task-queue"

AWS:
  Region: "ap-south-1"

Tracing:
  Enable: true

Secrets:
  Ids:
    - EpifiWealthAnalyticsDbUsernamePassword: "qa/rds/postgres/epifi-wealth-analytics"
    - StartreePinotAuthToken: "qa/pinot/startree"

GetTransactionsAggregatesDowntimeConf:
  IsDowntime: false

PinotConfig:
  AppName: "analyser-txnaggregates"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

PinotPollingConfig:
  EnableGoCronScheduler: true
