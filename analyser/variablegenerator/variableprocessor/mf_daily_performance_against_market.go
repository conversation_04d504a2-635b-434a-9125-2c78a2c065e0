package variableprocessor

import (
	"context"
	"fmt"

	"github.com/shopspring/decimal"

	"github.com/epifi/be-common/pkg/money"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	"github.com/epifi/gamma/api/analyser/variables/mutualfund"
	mfUtils "github.com/epifi/gamma/investment/utils"
)

const (
	percentageMultiplier = 100
)

type MfPortfolioPerformanceVariableProcessor struct{}

func NewMfPortfolioPerformanceVariableProcessor() *MfPortfolioPerformanceVariableProcessor {
	return &MfPortfolioPerformanceVariableProcessor{}
}

// GenerateAnalysisVariable generates analysis variables for mutual fund portfolio performance
// by comparing user's daily returns with Nifty 50 returns,
// can extend to other indices in the future
func (u *MfPortfolioPerformanceVariableProcessor) GenerateAnalysisVariable(ctx context.Context, req *GenerateAnalysisVariableRequest) (*analyserVariablePb.AnalysisVariable, error) {
	userDailyChangePercentage, err := u.calculateUserDailyChangePercentage(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate user daily change percentage: %w", err)
	}

	dailyNavChangePercentageValue, err := u.calculateNifty50Returns(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate nifty 50 returns: %w", err)
	}

	return &analyserVariablePb.AnalysisVariable{
		AnalysisVariableName: analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO,
		Variable: &analyserVariablePb.AnalysisVariable_MfPortfolioPerformanceDetails{
			MfPortfolioPerformanceDetails: &mutualfund.MfPortfolioPerformanceDetails{
				UserPercentageReturns:    userDailyChangePercentage,
				Nifty50PercentageReturns: dailyNavChangePercentageValue,
			},
		},
		AnalysisVariableState: analyserVariablePb.AnalysisVariableState_ANALYSIS_VARIABLE_STATE_AVAILABLE,
	}, nil
}

// calculateUserDailyChangePercentage calculates the daily percentage change in user's mutual fund portfolio
// It aggregates the changes across all schemes weighted by their current values
func (u *MfPortfolioPerformanceVariableProcessor) calculateUserDailyChangePercentage(ctx context.Context, req *GenerateAnalysisVariableRequest) (float64, error) {
	mfPortfolioAnalytics, ok := req.RawDetailsMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_PORTFOLIO_ANALYTICS]
	if !ok {
		return 0, fmt.Errorf("unable to find mutual fund portfolio analytics data for raw varible %v", analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_PORTFOLIO_ANALYTICS)
	}

	analytics := mfPortfolioAnalytics.GetMfPortfolioAnalytics()
	aggregatedDailyChange := decimal.Zero

	for _, scheme := range analytics.GetMfSchemeAnalytics() {
		percentageChange := mfUtils.CalculateNavChangePercentage(ctx, scheme.GetMutualFundCatalogDetails())
		if percentageChange == 0 {
			continue
		}
		currentValue := money.ToDecimal(scheme.GetEnrichedMfSchemeAnalytics().GetAnalytics().GetSchemeDetails().GetCurrentValue())
		percentageChangeDecimal := decimal.NewFromFloat(percentageChange)
		denominator := decimal.NewFromInt(percentageMultiplier).Add(percentageChangeDecimal)
		aggregatedDailyChange = aggregatedDailyChange.Add(currentValue.Mul(percentageChangeDecimal).Div(denominator))
	}

	portfolioValue := money.ToDecimal(analytics.GetEnrichedPortfolioAnalytics().GetPortfolio().GetPortfolioDetails().GetPortfolioValue())
	if portfolioValue.IsZero() {
		return 0, nil
	}

	return aggregatedDailyChange.Mul(decimal.NewFromInt(percentageMultiplier)).Div(portfolioValue).InexactFloat64(), nil
}

// calculateNifty50Returns calculates the daily percentage change in Nifty 50 index
// using a low tracking error index fund as a proxy
func (u *MfPortfolioPerformanceVariableProcessor) calculateNifty50Returns(ctx context.Context, req *GenerateAnalysisVariableRequest) (float64, error) {
	indexFundAnalytics, ok := req.RawDetailsMap[analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_LOW_TRACKING_ERROR_INDEX_FUND]
	if !ok {
		return 0, fmt.Errorf("unable to find index fund analytics data for raw variable %v", analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_MF_LOW_TRACKING_ERROR_INDEX_FUND)
	}

	indexFund := indexFundAnalytics.GetMfLowTrackingErrorFundDetails()
	return mfUtils.CalculateNavChangePercentage(ctx, indexFund.GetLowTrackingErrorIndexFund()), nil
}
