---
description: To create or add a dao method
globs: 
alwaysApply: false
---
This rule outlines the common patterns for implementing DAO (Data Access Object) methods in our codebase. DAOs are responsible for handling database operations and converting between database models and protocol buffer objects.

## Directory Structure
```
service/
  └── dao/
      ├── dao.go             # Interface definitions
      ├── impl/              # Implementation directory (Sometimes developers instead put this in the parent folder)
      │   └── entity.go      # Implementation of DAO interfaces
      │   └── entity_test.go # Unit test of DAO implementations
      ├── mocks/             # Mocks directory
      │   └── entity.go      # Auto generated mocks of DAO interfaces
      └── model/             # Model definitions
          └── entity.go      # Database model structs
```

## Model Implementation
### Model Structure
- Model structures will at minimum have all the fields present in the corresponding database table. 
- Schema can be found by grepping `public.entity` in `db/` directory in `latest.sql` file.
- Find the appropriate tables and create model with all the fields *only* if the model is not already present. 
**Important** Always ask the user to confirm if you're not sure.

```go
type Entity struct {
    Id        string `gorm:"type:uuid;default:gen_random_uuid();primary_key"`
    Field1    string
    Field2    int32
    CreatedAt time.Time
    UpdatedAt time.Time
    DeletedAt gorm.DeletedAt
}
```

Models should implement:
1. Database field mappings
2. `TableName()` method
3. Conversion methods to/from proto objects
**Important** Example Model: [actor_biller_account.go](mdc:billpay/dao/model/actor_biller_account.go)
**Important** Example Schema: [latest.sql](mdc:db/billpay/latest.sql)

## DAO Structure
### Interface Definition
DAO interfaces are typically defined in `dao.go` and follow this pattern:

```go
//go:generate mockgen -source=dao.go -destination=./mocks/dao.go -package=mocks
//go:generate dao_metrics_gen .
type EntityDao interface {
    Create(ctx context.Context, entity *pb.Entity) (*pb.Entity, error)
    GetById(ctx context.Context, id string) (*pb.Entity, error)
    GetByX(ctx context.Context, x string) ([]*pb.Entity, error)
    Update(ctx context.Context, entity *pb.Entity, updateMask []pb.EntityFieldMask) error
    Delete(ctx context.Context, id string) error
}
```
A dao.go file can have multiple interface definitions corresponding to each entity.
**Important** Example DAO Interface: [dao.go](mdc:billpay/dao/dao.go)

### DAO Implementation
 - Use Gorm ORM library to implement all the DAO methods.
 - DAO implementations for interfaces are kept in separate files. 
 - Search for existing implementation file first and add a new one *only* if not present.
 -**Important** Example Implementation: [actor_biller_account.go](mdc:billpay/dao/actor_biller_account.go)

### Error Handling
Return epifierrors objects appropriately when required:
1. Record Not found: Return `epifierrors.ErrRecordNotFound` if gorm error is `gormv2.ErrRecordNotFound`
2. Duplicate entry: `epifierrors.ErrDuplicateEntry` if gorm error is `gormv2.ErrDuplicatedKey`

### Metrics and Logging
1. Always use `defer metric_util.TrackDuration()` at the beginning of DAO methods
2. Use structured logging with appropriate context:
   ```go
   logger.Error(ctx, "error message", zap.Error(err), zap.String("id", id))
   ```

### Field Masks
- Use `"google.golang.org/protobuf/types/known/fieldmaskpb"` type for field masks
- Add fieldmasks.go in dao/impl/. This contains methods that returns fieldmasks by choosing the appropriate column names of the entity from latest.sql. 
- **Important** Example: [fieldmasks.go](mdc:billpay/dao/fieldmasks.go)

1. For Update Operation -
 - Use field masks to select only the columns that should be updated
 - **Important** Example: `Update` in [actor_biller_account.go](mdc:billpay/dao/actor_biller_account.go)
 
2. For Select Operation -
 - Use field masks to select only the columns that should be fetched
 - **Important** Example: `GetByActorId` in [actor_biller_account.go](mdc:billpay/dao/actor_biller_account.go)

### Pagination
For methods that return multiple records:
1. Accept pagination parameters: `pageToken *pagination.PageToken` and `pageSize uint32`
    - Set proper offset using `pageToken.Offset`
    - Handle forward and reverse pagination using `pageToken.IsReverse`
    - Set limit using `pageSize`
2. Return pagination response: `*rpc.PageContextResponse`
    - Create a new type `type Entities []*pb.Entity` which implements `Rows` interface -
        ```go
        type Rows interface {
            Slice(start, end int) Rows
            GetTimestamp(index int) time.Time
            Size() int
        }
        ```
    - Create the pagination response using `pagination.NewPageCtxResp`
3. Use the generic DAO pagination utilities always.

**Important** Example Paginated Method: `GetByActorId` in [actor_biller_account.go](mdc:billpay/dao/actor_biller_account.go)

## Wire Setup
1. Wire sets are used to bind interfaces to implementations:
    ```go
        var EntityDaoWireSet = wire.NewSet(NewEntityDaoImpl, wire.Bind(new(EntityDao), new(*EntityDaoImpl)))
    ```
    
## Testing
1. Ensure no obvious are present by running linters.
