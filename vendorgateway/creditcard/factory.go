package creditcard

import (
	"fmt"
	"net/http"

	"google.golang.org/protobuf/proto"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	ccVgPb "github.com/epifi/gamma/api/vendorgateway/creditcard"
	"github.com/epifi/gamma/vendorgateway/creditcard/saven"
)

func (s *Service) getReqFactoryMap() map[commonvgpb.Vendor]vendorapi.SyncRequestFactory {
	return map[commonvgpb.Vendor]vendorapi.SyncRequestFactory{
		commonvgpb.Vendor_SAVEN: s.NewSavenRequest,
	}
}

func (s *Service) NewSavenRequest(req proto.Message) vendorapi.SyncRequest {
	switch v := req.(type) {
	case *ccVgPb.GenerateCreditCardSdkAuthTokenRequest:
		return &saven.GenerateCreditCardSdkAuthTokenRequest{
			Req:     v,
			Method:  http.MethodPost,
			ApiKey:  s.conf.Application.Saven.SavenSecrets.ApiKey,
			BaseUrl: s.conf.Application.Saven.CreditCardBaseUrl,
			Signer:  s.savenRequestSigner,
		}
	default:
		logger.InfoNoCtx(fmt.Sprintf("Unsupported request type %v", v))
		return nil
	}
}
