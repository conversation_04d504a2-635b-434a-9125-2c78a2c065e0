Application:
  Environment: "uat"
  Name: "vendorgateway"
  IsSecureRedis: true
  SyncWrapperTimeout: 60
  VGAuthSvcSyncWrapperTimeout: 20
  IsStatementAPIEnabled: false
  CreateDisputeURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/DMP/v1.0.0/createDispute"
  DisputeStatusCheckUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/DMP/v1.0.0/disputeStatusCheck"
  BulkDisputeStatusCheckUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/DMP/v1.0.0/bulkDisputeStatus"
  SendCorrespondenceUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/DMP/v1.0.0/disputeCorrespondence"
  ChannelQuestionnaireUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/DMP/v1.0.0/channelQuestionnaire"
  AccountTransactionsUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/DMP/v1.0.0/transactions"
  UploadDocumentUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/DMP/v1.0.0/disputeDocument"
  #  CreateCustomerURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/customer/creation"
  CreateCustomerURL: "https://uatgateway.federalbank.co.in:553/fedbnk/uat/neobanking/customer/creation"
  CreateLoanCustomerURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v3.0.0/cifCustIdCreateAOF"
  LoanCustomerCreationStatusURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/cif/enquiry"
  #  CheckCustomerStatusURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/enquiry/service"
  CheckCustomerStatusURL: "https://uatgateway.federalbank.co.in:553/fedbnk/uat/neobanking/enquiry/service"

  CreateAccountURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/savings/account/opening"
  CheckAccountStatusURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/enquiry/service"
  #  DedupeCheckURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/ddupe/check"
  # version 2 of dedupe url
  #  DedupeCheckURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/ddupe/v2.0.0/check"
  # version 3 of dedupe url
  DedupeCheckURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/ddupe/v3.0.0/check"
  FetchCustomerDetailsUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/customer-details/enquire"
  EnquireVKYCStatusUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/vkycenquiry/enquiry"
  EnquireBalanceURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/balanceEnq"
  CkycSearchURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/ckyc/enquiry"
  GetKycDataURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/ckyc/download"
  CreateVirtualIdURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi-psp/CreateVirtualID"
  GetTokenURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ListKeys"
  DeviceRegistrationURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/user-device/registration"
  SetPINURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/SetCred"
  UPIBalanceEnquiryURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/BalEnq"
  ValidateAddressURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ValAdd"
  GenerateUpiOtpURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ReqOtp"
  RespAuthDetailsURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/RespAuthDetails"
  ReqPayURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ReqPay"
  RegisterMobileURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/RegMob"
  ListUpiKeyUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ListKeys"
  ListAccountURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ListAccount"
  ListAccountProviderURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ListAccPvd"
  ListPspURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ListPSP"
  GetMapperInfoURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/v1.0.0/reqGetAdd"

  RespTxnConfirmationURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/RespTxnConfirmation"
  RespValidateAddressURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi-psp/RespValAdd"
  ReqCheckTxnStatusURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ChkTxnStatus"
  ListVaeURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ListVerAddEntries"
  ReqMandateURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ReqMandate"
  RespAuthMandateURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/RespAuthMandate"
  RespMandateConfirmationURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/RespMandateConfirmation"
  ReqComplaintURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ReqComplaint"
  RespAuthValCustURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/RespAuthValCust"
  ReqCheckComplaintStatusUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ChkTxnStatus"
  RegMapperURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/v1.0.0/reqRegMapper"
  # TODO(Yatin to add url: https://monorail.pointz.in/p/fi-app/issues/detail?id=28501)
  ReqActivationUrl: ""
  ReqValQRUrl: ""
  GetUpiLiteURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ListKeys"
  SyncUpiLiteInfoURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/ChkTxnStatus"
  RespMapperConfirmationURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/upi_psp/v1.0.0/respMapperConfirmation"

  PanProfileURL: "https://testapi.karza.in/v3/pan-profile"

  BureauIdUrl: "https://api.bureau.id/transactions"

  # send vkyc data to federal for inhouse vkyc service
  SendAgentDataURL: "https://14.142.52.59/vKYCPostBack/AgentStatus"
  SendAuditorDataURL: "https://14.142.52.59/vKYCPostBack/AgentStatus"

  # EPAN
  GetEPANKarzaStatusURL: "https://app.karza.in/test/videokyc/api/v2/epan-staging"
  InhouseGetAndValidateEPANURL: "https://delta-server.uat.pointz.in/verify/epan"

  # ITR
  InhouseVerifyAndGetITRIntimationDetailsURL: "https://delta-server.qa.pointz.in:9091/verify/itr-intimation"

  #Call back urls for account setup
  CreateCustomerCallBackUrl: "https://vnotificationgw.uat.pointz.in/openbanking/customer/create"
  CreateAccountCallBackUrl: "https://vnotificationgw.uat.pointz.in/openbanking/account/create"

  # Liveness Service
  Veri5CheckLivenessRequestURL: "https://simulator.uat.pointz.in:9091/video-id-kyc/api/1.0/liveness"
  Veri5MatchFaceRequestURL: "https://sandbox.veri5digital.com/video-id-kyc/api/1.0/faceCompare"
  KarzaCheckLivenessRequestURL: "https://simulator.uat.pointz.in:9091/v3/uat/video-liveness"
  KarzaLivenessCallbackURL: "https://vnotificationgw.uat.pointz.in/liveness/karza"
  KarzaMatchFaceRequestURL: "https://simulator.uat.pointz.in:9091/v3/facesimilarity"
  KarzaCheckPassiveLivenessRequestURL: "https://simulator.uat.pointz.in:9091/v3/image-liveness"
  KarzaCheckLivenessStatusURL: "https://simulator.uat.pointz.in:9091/v3/video-liveness-status"
  InhouseCheckLivenessRequestURL: "https://liveness.data-dev.pointz.in/"
  InhouseMatchFaceRequestURL: "https://facematch.data-dev.pointz.in/v1/facematch"
  InhouseMatchFaceRequestURLV2: "https://facematch.data-dev.pointz.in/v1/facematch"
  UseFormMarshalForKarza: false
  UseFormMarshalForKarzaFM: false

  # Employment Service
  Employment:
    KarzaPFOTPURL: "https://testapi.karza.in/v2/epf-get-otp"
    KarzaPFPassbookURL: "https://testapi.karza.in/v3/epf-get-passbook"
    KarzaEmploymentVerificationURL: "https://testapi.karza.in/v2/employment-verification-advanced"
    KarzaSearchCompanyNameURL: "https://testapi.kscan.in/v3/employer-search-lite"
    KarzaUANLookupURL: "https://testapi.karza.in/v2/uan-lookup"
    KarzaEPFAuthURL: "https://testapi.karza.in/v2/epf-auth"
    KarzaEmployeeNameSearchURL: "https://testapi.karza.in/v2/employee-search"
    KarzaCompanyMasterLLPDataURL: "https://testapi.karza.in/v2/mca"
    KarzaSearchGSTINBasisPAN: "https://gst.karza.in/uat/v1/search"
    KarzaGetForm16QuarterlyURL: "https://testapi.karza.in/v3/tdsq"
    KarzaGetEmployerDetailsByGstinURL: "https://api.karza.in/gst/uat/v2/gst-verification"
    KarzaGetUANFromPan: "https://testapi.karza.in/v2/employment-verification-advanced"
    SignzyLoginURL: "https://preproduction.signzy.tech/api/v2/patrons/login"
    SignzyDomainNameVerificationURL: "https://preproduction.signzy.tech/api/v2/patrons/userid/domainverifications"
    KarzaFindUanByPan: "https://testapi.karza.in/v3/pan-uan"

  # CRM Service
  FreshdeskURI:
    Agent: "/agents"
    Ticket: "/tickets"
    FilterTicket: "/search/tickets"
    Solutions: "/solutions"
    Contacts: "/contacts"
    TicketField: "/admin/ticket_fields"
  FreshDeskAccountConfig:
    EPIFI_TECH:
      USE_CASE_RISK_CASE_MANAGEMENT:
        ApiTokenSecretName: "EpifiTechRiskFreshdeskApiKey"
        BaseURL: "https://epifitechnologies.freshdesk.com/api/v2"
    FEDERAL_BANK:
      USE_CASE_RISK_CASE_MANAGEMENT:
        ApiTokenSecretName: "EpifiTechRiskFreshdeskApiKey"
        BaseURL: "https://epifitechnologies.freshdesk.com/api/v2"

  # Freshdesk service
  FreshdeskAgentURL: "https://ficaretesting.freshdesk.com/api/v2/agents"
  FreshdeskTicketURL: "https://ficaretesting.freshdesk.com/api/v2/tickets"
  FreshdeskFilterTicketURL: "https://ficaretesting.freshdesk.com/api/v2/search/tickets"
  FreshdeskSolutionsURL: "https://ficaretesting.freshdesk.com/api/v2/solutions"
  FreshdeskContactsURL: "https://ficaretesting.freshdesk.com/api/v2/contacts"
  FreshdeskTicketFieldURL: "https://ficaretesting.freshdesk.com/api/v2/admin/ticket_fields"
  CxFreshdeskTicketAttachmentsBucketName: "epifi-uat-cx-ticket-attachments"

  #Freshchat service
  FreshchatConversationURL: "https://epifi6.freshchat.com/v2/conversations"
  FreshchatUserURL: "https://epifi6.freshchat.com/v2/users"
  FreshchatAgentURL: "https://epifi6.freshchat.com/v2/agents"

  #Ozonetel service
  OzonetelManualDialUrl: "https://api1.cloudagent.in/CAServices/AgentManualDial.php"
  OzonetelUserName: "epifi"
  OzonetelCampaignName: "Progressive_9***********"

  InhouseNameCheckUrl: "https://entity-matcher.data-dev.pointz.in/api/v1/namepairmatch"
  InhouseEmployerNameMatchUrl: "https://entity-matcher.data-dev.pointz.in/api/v1/companymatch"
  InhouseEmployerNameCategoriserUrl: "http://text-semantics.data-dev.pointz.in/v1/name_categoriser"
  InhouseBreForCCUrl: "https://iris.uat.pointz.in/evaluator/flow/v1/flow-ccpolicybre"

  DrivingLicenseValidationUrl: "https://testapi.karza.in/v3/dl"

  VoterIdValidationUrl: "https://testapi.karza.in/v2/voter"

  BankAccountVerificationUrl: "https://testapi.karza.in/v2/bankacc"

  CAMS:
    OrderFeedFileURL: "https://eiscuat1.camsonline.com/ecrms/stp/fnSystematicBatchSubmitRTA_Exg.aspx"
    FATCAFileURL: "https://eiscuat1.camsonline.com/ecrms/stp/fnStpSystematic_Fatca.aspx"
    ElogFileURL: "https://eiscuat1.camsonline.com/ecrms/STP/StpSystematic_ELogUpload.aspx"
    # OrderFeedFileStatusURL is pointer to simulator as the respective vendor endpoint doesn't work for non-prod environments.
    OrderFeedFileStatusURL: "https://simulator.uat.pointz.in:8080/cams/GetOrderFeedFileStatus"
    OrderFeedFileSyncURL: "https://eiscuat1.camsonline.com/ecrms/stp/fnSystematicExchangeUpload_RTA.aspx"
    S3Bucket: "epifi-uat-mutualfund"
    NFTFileURL: "https://eiscuat1.camsonline.com/ecrms/stp/fnStpSystematic_NF.aspx"
    GetFolioDetailsURL: "https://eiscuat2.camsonline.com/CAMSWS_R/Services_CAMS/Contact_Details"
    NomineeUpdateURL: "https://eiscuat2.camsonline.com/CAMSWS_R/Services_Nominee/Nominee_Registration"
    UserCode: "EPIFIRIA"

  SmallCase:
    CreateTransactionURL: "https://simulator.uat.pointz.in:8080/smallcase/CreateTransaction"
    InitiateHoldingsImportURL: "https://simulator.uat.pointz.in:8080/smallcase/InitiateHoldingsImportURL"
    TriggerHoldingsImportFetchURL: "https://simulator.uat.pointz.in:8080/smallcase/TriggerHoldingsImportFetchURL"
    MFAnalyticsURL: "https://mf-api.smallcase.com/gateway/mf/analytics"
    SmallCaseGateway: "fimoney-stag"

  Tiering:
    AddSchemeChangeURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/schemeChangeAddEnq"
    EnquireSchemeChangeURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/schemeChangeAddEnq"

  MFCentral:
    GenerateTokenURL: "https://uatservices.mfcentral.com/oauth/token"
    EncryptAndSignURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/encryptAndSign"
    VerifyAndDecryptURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/verifyAndDecrypt"
    UpdateFolioEmailURL: "https://simulator.uat.pointz.in:8080/mfcentral/api/client/v1/updateEmail"
    UpdateFolioMobileURL: "https://simulator.uat.pointz.in:8080/mfcentral/api/client/v1/updateMobile"
    InvestorConsentUrl: "https://simulator.uat.pointz.in:8080/mfcentral/api/client/v1/investorconsent"
    SubmitCasSummaryUrl: "https://simulator.uat.pointz.in:8080/mfcentral/api/client/v1/submitcassummaryrequest"
    GetCasDocumentUrl: "https://simulator.uat.pointz.in:8080/mfcentral/api/client/v1/getcasdocument"
    GetTransactionStatusUrl: "https://simulator.uat.pointz.in:8080/mfcentral/api/client/v1/getTransactionStatus"

  ## aa smart parser
  InHouseAAParserURL: "https://smart-parser.data-dev.pointz.in/parse"
  InHouseAABulkParserURL: "https://smart-parser.data-dev.pointz.in/bulk_parse"

  #SMS Service
  Exotel:
    URL: "https://%s:%<EMAIL>/v1/Accounts/%s/SMS"
    AccountSid: "epifi2"
    SenderId: "***********"
  Twilio:
    URL: "https://api.twilio.com/2010-04-01/Accounts/%s/Messages"
    SenderId: "***********"
  AclEpifi:
    FallbackURL: "https://dmz.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    URL: "https://push3.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    AppId: "epiualt"
    SenderId: "FiMony"
  AclFederal:
    FallbackURL: "https://dmz.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    URL: "https://push3.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    AppId: "fbefiuat"
    SenderId: "FedFib"
  AclEpifiOtp:
    FallbackURL: "https://dmzotp.aclgateway.com/OTP_ACL_Web/OtpRequestListener"
    URL: "https://otp2.aclgateway.com/OTP_ACL_Web/OtpRequestListener"
    AppId: "epifiotp"
    SenderId: "FiMony"
  KaleyraFederal:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FedFiB"
  KaleyraEpifi:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FiMony"
  KaleyraEpifiNR:
    URL: "https://api.in.kaleyra.io/v1/HXIN1778099997IN/messages"
    SenderId: "FIMONY"
    CallbackProfileId: "IN_dfd2cc95-d65c-445b-88a9-b5c9d16e32bd"
  KaleyraSmsCallbackURL: "https://vnotificationgw.uat.pointz.in/sms/callback/kaleyra/UrlListner/requestListener"
  AclWhatsapp:
    URL: "http://simulator.uat.pointz.in:8080/pull-platform-receiver/wa/messages"
    OptInURL: "http://115.113.127.155:8085/api/v1/addoptinpost"
  KaleyraFederalCreditCard:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FedFiB"
  #Loylty Service
  Loylty:
    AuthTokenURL: "https://authuat.loylty.com/mtoken"
    GiftCardBookingURL: "https://uategvb9.loylty.com/V2/GiftCard/Request"
    CharityBookingURL: "https://uatcbkb9.loylty.com/V2/InitiateV2"
    GiftCardProductListURL: "https://uategvb9.loylty.com/V2/GiftCard/Products"
    GiftCardProductDetailURL: "https://uategvb9.loylty.com/V2/GiftCard/Products/%s"
    CreateOrderURL: "https://uatordb9.loylty.com/V2/Order"
    ConfirmOrderURL: "https://uatordb9.loylty.com/V2/Order/%s/Confirm"
    GetOrderDetailsURL: "https://uatordb9.loylty.com/V2/Order/%s"

  Qwikcilver:
    GetAuthorizationCodeBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/authorization-code"
    GetAccessTokenBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/access-token"
    CreateOrderBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/create-order"
    GetActivatedCardDetailsBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/activated-card-details/%s"
    GetCategoryDetailsBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/category-details"
    GetOrderStatusBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/order-status/%s"
    GetProductDetailsBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/product-details/%s"
    GetProductListBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/product-list/%s"

  Thriwe:
    BaseUrl: "https://staging-india-api-gateway.thriwe.com"

  Riskcovry:
    BaseUrl: "https://api.uat-riskcovry.com/api/partner"

  Onsurity:
    BaseUrl: "https://partner.preprod.onsurity.com"

  Karvy:
    KarviAppId: "RIA"
    AgentCode: "INA200015185"
    BranchCode: "R999"
    BucketName: "epifi-uat-mutualfund-karvy"
    FATCAFileURL: "https://cpuat.kfintech.com/stp/stpservice.svc/FATCAUpload"
    OrderFeedFileSyncURL: "https://cpuat.kfintech.com/STP/STPService.svc/TransactionUpload250"
    OrderFeedFileV2SyncURL: "https://cpuat.kfintech.com/STP/STPService.svc/TransactionUpload250"
    NFTFileUploadURL: "https://cpuat.kfintech.com/stp/stpservice.svc/NCTUpload"
    GetFolioDetailsURL: "https://cpuat.kfintech.com/STP/STPService.svc/GetMobileAndEmailBasedOnFolio"
    NomineeUpdateURL: " https://cpuat.kfintech.com/NCTCF/NCTCFService.svc/NCTCFUpload"
    NCTCFTokenGenerateURL: "https://cpuat.kfintech.com/NCTCF/NCTCFService.svc/GenerateToken"
    UserCode: "EPFIRIA"

  #json file path
  PayFundTransferStatusCodeJson: "./mappingJson/fundTransferStatusCodes.json"
  PayUpiStatusCodeJson: "./mappingJson/upiStatusCodes.json"
  PayAckStatusCodeJson: "./mappingJson/ackStatusCode.json"

  DepositAckStatusCodeFilePath: "./mappingJson/depositAckStatusCode.json"
  DepositResponseStatusCodeFilePath: "./mappingJson/depositResponseStatusCodes.json"

  CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"
  SIResponseStatusCodeFilePath: "./mappingJson/siResponseStatusCodes.json"

  Federal:
    CheckCustomerStatusForNonResidentURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/accountcreation/enquiry"
    CreateCustomerForNonResidentURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/WebAccOpen/v1.0.0/custDataInsert"
    CustomerDetailsInsertURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/WebAccOpen/v1.0.0/custDataInsert"
    PanValidationV2Url: "https://uatgateway.federalbank.co.in/fedbnk/uat/pan/v2.0.0/validate"
    PayIntraBankURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/intra/fundtransfer"
    PayNEFTURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/neft/fundtransfer"
    PayIMPSURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/imps/fundtransfer"
    PayRTGSURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/rtgs/fundtransfer"
    PayStatusURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/enquiry/service"
    PayIntraBankDepositURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/OwnDepositAccFT"
    RemitterDetailsFetchUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/neobanking/v1.0.0/fetchRemittanceDetails"
    RemitterDetailsV1FetchUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/fundTransfer/v1.0.0/remitterFetch"
    BeneficiaryNameLookupUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/account_utility/v1.0.0/BenefiAcctNameLookup"
    GetCsisStatusUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/neobanking/v1.0.0/CSISStatusCheck"

    PayIntraBankCallbackURL: "https://vnotificationgw.uat.pointz.in/openbanking/payment/federal"
    PayNEFTCallbackURL: "https://vnotificationgw.uat.pointz.in/openbanking/payment/federal"
    PayIMPSCallbackURL: "https://vnotificationgw.uat.pointz.in/openbanking/payment/federal"
    PayRTGSCallbackURL: "https://vnotificationgw.uat.pointz.in/openbanking/payment/federal"

    # B2C Payments
    PayB2CIntraBankURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/intrabank/fundtransfer"
    PayB2CIntraBankCallbackURL: "https://vnotificationgw.uat.pointz.in/openbanking/payment/b2c/federal"
    PayB2CStatusURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/transaction/enquiry"
    PayB2CBalanceURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/balance/enquiry"
    PayB2CImpsURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/imps/fundtransfer"
    PayB2CImpsCallbackURL: "https://vnotificationgw.uat.pointz.in/openbanking/payment/b2c/federal"

    # Shipping Preference Service
    ShippingAddressUpdateURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/ShipAddrModification"
    ShippingAddressUpdateCallbackURL: "https://vnotificationgw.uat.pointz.in/openbanking/shipping_preference/federal"

    # Card Service
    DebitCardCreateURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/CardCreation"
    DebitCardCreateCallbackURL: "https://vnotificationgw.uat.pointz.in/openbanking/card/federal"
    DebitCardActivateURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/CardActivation"
    DebitCardEnquiryUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/CardEnqService"
    DebitCardPinSetUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinChangeUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinResetUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinValidationUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardBlockUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/CardControl"
    DebitCardSuspendOnOffUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/CardControl"
    DebitCardLocationOnOffUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/CardControl"
    DebitCardECommerceOnOffUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/CardControl"
    DebitCardCVVEnquiryUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/CardCVVEnq"
    DebitCardLimitEnquiry: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/CardLimitEnq"
    DebitCardUpdateLimit: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/CardLimitUpdate"
    DebitCardDeliveryTracking: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking-card/DeliveryTracking"
    DebitCardConsolidatedCardControlUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/neobanking-card/v1.0.0/FHMCardONOFF"
    DebitCardPhysicalDispatchUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/neobanking-card/cardDispatch"
    DebitCardPhysicalDispatchCallbackUrl: "https://vnotificationgw.uat.pointz.in/openbanking/cardPhysicalDispatch/federal"
    CheckDebitCardIssuanceFeeStatusUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/GSTbifurcationStatus"
    DebitCardCollectIssuanceFeeUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/GSTbifurcation"

    # PAN Service
    PANValidationURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/pan/validation"
    #PANValidationURL: "https://simulator.qa.pointz.in:8080/panValidation"
    PANAadhaarValidationURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/PANAadhaarValidate"

    # EKYC Service
    EkycNameDobValidationURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/ekyc/namedob/validation"

    #AadharMobileValidationURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/account_utility/v1.0.0/aadharMob"
    AadharMobileValidationURL: "https://simulator.uat.pointz.in:8080/aadharmobilevalidate"
    ShareDocWithVendorURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/documentSharing"

    # UN Name Check Service
    UNNameCheckURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/digitalCredit/v1.0.1/unNameCheck"

    # Device Re-registration / Profile Update API
    DeviceReRegURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/device/re-registration"
    DeviceReRegCallbackUrl: "https://vnotificationgw.uat.pointz.in/openbanking/auth/federal/user-device/re-register"

    # Device De-registration / Deactivate User
    DeviceDeRegURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/user-device/deactivation"

    # Generate OTP
    GenerateOTPURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/OTPGen"

    DeviceReactivationURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/user-device/reactivate"
    # Deposit service
    CreateFDURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/CreateFD"
    CreateSDURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/CreateSD"
    CreateRDURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/CreateRD"
    AutoRenewFdURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/neobanking/v1.0.0/fdRenewal"
    CloseDepositAccountURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/ClosingDepositAcc"
    GetDepositAccountDetailURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/GetAccDetails"
    GetPreClosureDetailURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/DepositAcctPreclosEnq"
    CheckDepositAccountStatusURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/DepositEnq"
    DepositListAccountURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/GetAccList"
    InterestRateInfoURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/v1.0.0/interestRateInfo"
    CalculateInterestDetailsURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/neobanking/v1.0.0/depositInterestCalculator"
    CreateDepositCallbackUrl: "https://vnotificationgw.uat.pointz.in/openbanking/deposit/federal/create"
    PreCloseDepositCallbackUrl: "https://vnotificationgw.uat.pointz.in/openbanking/deposit/federal/preclose"
    AutoRenewFdCallbackUrl: "https://vnotificationgw.uat.pointz.in/openbanking/deposit/federal/AutoRenewFd"

    # Standing Instruction service
    CreateSIUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/recurringtransfer/standinginstruction"
    ExecuteSIUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/recurringpayment/execution"
    SICallbackUrl: "https://vnotificationgw.uat.pointz.in/openbanking/payment/federal"
    ModifySIUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/recurringpayment/modification"
    RevokeSIUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/recurringpayment/revoke"

    # csv file path
    CityCodesCsv: "./mappingCsv/cityCodes.csv"
    StateCodesCsv: "./mappingCsv/stateCodes.csv"
    CountryCodesCsv: "./mappingCsv/countryCodes.csv"

    #Account
    OpeningBalanceURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/GetAccStatement"
    ClosingBalanceURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/v1.0.0/eodbalanceInquiry"
    AccountStatementURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/GetAccStatement"
    AccountStatementByDRApiUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/neobanking/v1.0.1/getAccountStatement"
    EnquireBalanceV1URL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/account_utility/v1.0.0/getGAMNotification"
    MiniStatementURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/v1.0.0/ministatement"
    AccountStatusURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/account_utility/v1.0.0/accountStatusEnquiry"
    ThirdPartyAccountCollectionURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/account_utility/v1.0.0/tpAccountCollection"
    UpdateNomineeURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/account_utility/v1.0.0/nominationUpdate"
    UpdateNomineeChannel: "TESTC"

    # Partner SDK
    GetSessionParamsUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/DeviceKeyReg"

    # Enquiry Service Urls
    CustomerCreationEnquiryStatusURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/enquiry/service"
    AccountCreationEnquiryStatusURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/enquiry/service"
    CardCreationEnquiryStatusURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/enquiry/service"
    DeviceReRegistrationDetailsEnquiryStatusURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/enquiry/service"
    MailingAddressModifyDetailsEnquiryStatusURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/enquiry/service"
    ShippingAddressUpdateEnquiryStatusURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/enquiry/service"
    DeviceRegistrationDetailsEnquiryStatusURL: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/enquiry/service"
    PhysicalCardDispatchDetailsEnquiryStatus: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/enquiry/service"

    # Cheque Books Url
    OrderChequebookUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/v1.0.0/chequeBookRequest"
    TrackChequebookUrl: "https://uatgateway.federalbank.co.in:443/fedbnk/uat/neobanking/v1.0.0/chequeBookTrack"
    IssueDigitalCancelledChequeUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/account_utility/v1.0.0/digitalChequeLeafIssuance"

    # Profile Update and enquiry URL
    ProfileUpdateUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/profileUpdation"
    ProfileUpdateEnquiryUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/profileUpdation"

    # lien service url
    LienUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/account_utility/v1.0.0/accountLienMarking"

    TcsCalculationURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0"
    TcsCalculationChannelId: "EPI"

    # e-nach service url
    ListEnachUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/NACH/EMANDATE/v1.0.0/dataSharing"

    FetchEnachTransactionsUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/enachdata"

  LeadSquared:
    CreateOrUpdateLeadUrl: "https://simulator.uat.pointz.in:8080/salaryprogram/leadsquared/CreateOrUpdateLead%s%s"

  Karza:
    GenerateSessionTokenUrl: "https://testapi.karza.in/v3/get-jwt"
    AddNewCustomerUrl: "https://app.karza.in/test/videokyc/api/v2/customers"
    UpdateCustomerV3Url: "https://app.karza.in/test/videokyc/api/v3/customers"
    AddNewCustomerV3Url: "https://app.karza.in/test/videokyc/api/v3/customers"
    GenerateCustomerTokenUrl: "https://app.karza.in/test/videokyc/api/v2/generate-usertoken"
    GetSlotUrl: "https://app.karza.in/test/videokyc/api/v2/get-slot"
    BookSlotUrl: "https://app.karza.in/test/videokyc/api/v2/book-slot"
    GenerateWebLinkUrl: "https://app.karza.in/test/videokyc/api/v2/link"
    SlotAgentsUrl: "https://app.karza.in/test/videokyc/api/v2/slot-agents"
    TransactionStatusEnquiryUrl: "https://app.karza.in/test/videokyc/api/v2/transaction-events"
    ReScheduleSlotUrl: "https://app.karza.in/test/videokyc/api/v2/reschedule-customer"
    TriggerCallback: "https://app.karza.in/test/videokyc/api/v2/trigger-callback"
    AgentDashboardUrl: "https://app.karza.in/prod/videokyc/api/v2/stats/agents"
    AgentDashboardAuthUrl: "https://app.karza.in/prod/videokyc/api/v3/login"
    EmploymentVerificationAdvancedUrl: "https://testapi.karza.in/v2/employment-verification-advanced"
    KycOcrUrl: "https://testapi.karza.in/v3/ocr-plus/kyc"
    PassportVerificationURL: "https://testapi.karza.in/v3/passport-verification"

  Roanuz:
    GenerateCricketAccessTokenUrl: "https://simulator.uat.pointz.in:8080/test/rounaz/cricket"
    CricketURL: "https://simulator.uat.pointz.in:8080/test/rounaz/cricket"
    GenerateFootballAccessTokenUrl: "https://simulator.uat.pointz.in:8080/test/rounaz/football/auth/"
    FootballUrl: "https://simulator.uat.pointz.in:8080/test/rounaz/football"
    FootballAppId: "com.epififootball.www"
    FootballDeviceId: "developer"

  IPStack:
    GetLocationDetailFromIpUrl: "https://api.ipstack.com"

  CvlKra:
    SoapHost: "https://krapancheck.cvlindia.com"
    PanEnquiryURL: "https://simulator.uat.pointz.in:8080/CVLPanInquiry.svc"
    InsertUpdateKycURL: "https://simulator.uat.pointz.in:8080/CVLPanInquiry.svc"
    # CvlKra sftp config
    Host: "sftp.deploy.pointz.in"
    Port: 22

  NsdlKra:
    PanInquiryURL: "https://simulator.uat.pointz.in:8080/TIN/PanInquiryBackEnd"
    GenerateSignatureURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/nsdl/v1/generateSignature"
    DisableSigning: false
    PerformPanInquiryV4URL: "https://simulator.uat.pointz.in:8080/TIN/PanInquiryBackEnd"

  Manch:
    TransactionsURL: "https://uat.manchtech.com/app/api/transactions"
    DocumentsURL: "https://uat.manchtech.com/app/api/documents"
    OrgId: "TST00180"
    ReturnUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

  Digio:
    TransactionsURL: "https://ext.digio.in:444/v2/client/document"
    ExpiryInDays: 10

  WealthKarza:
    OcrURL: "https://testapi.karza.in/v3/kycocr"
    MaskAadhaar: true
    HideAadhaar: true
    Confidence: true
    CheckBlur: true
    CheckBlackAndWhite: true
    CheckCutCard: true
    CheckBrightness: true

  Experian:
    CheckCreditReportPresenceURL: "https://ecvuat.experian.in/ECV-P2/content/enhancedMatch.action"
    FetchCreditReportURL: "https://ecvuat.experian.in/ECV-P2/content/enhancedMatch.action"
    FetchCreditReportForExistingUserURL: "https://ecvuat.experian.in/ECV-P2/content/onDemandRefresh.action"
    FetchExtendSubscriptionURL: "https://ecvuat.experian.in/ECV-P2/content/consumerConsentReRegistration.action"
    FetchCreditReportURLV1: "https://uat-in-api.experian.com/ecs/ecv-api/v1/enhanced-match"
    FetchCreditReportForExistingUserURLV1: "https://uat-in-api.experian.com/ecs/ecv-api/v1/ondemand-refresh"
    FetchExtendSubscriptionURLV1: "https://uat-in-api.experian.com/ecs/ecv-api/v1/consumer-consent-reregistration"
    FetchAccessTokenUrl: "https://uat-in-api.experian.com/oauth2/v1/token"
    V1VersionFlag: true

  Cibil:
    PingUrl: "https://apiuat.cibilhawk.com/consumer/dtc/v4/ping"
    FulfillOfferUrl: "https://apiuat.cibilhawk.com/consumer/dtc/v4/fulfilloffer"
    GetAuthQuestionsUrl: "https://apiuat.cibilhawk.com/consumer/dtc/v4/GetAuthenticationQuestions"
    VerifyAuthAnswersUrl: "https://apiuat.cibilhawk.com/consumer/dtc/v4/VerifyAuthenticationAnswers"
    GetCustomerAssetsUrl: "https://apiuat.cibilhawk.com/consumer/dtc/v4/GetCustomerAssets"
    GetProductTokenUrl: "https://apiuat.cibilhawk.com/consumer/dtc/v4/GetProductWebToken"
    ProductUrlPrefix: "https://atlasls-in-live.sd.demo.truelink.com/CreditView"

  Shipway:
    BulkUploadShipmentDataUrl: "https://simulator.uat.pointz.in:8080/shipway/BulkPushOrderData"
    GetShipmentDetailsUrl: "https://simulator.uat.pointz.in:9091/shipway/GetOrderShipmentDetails"
    AddOrUpdateWebhookUrl: "https://simulator.uat.pointz.in:9091/shipway/AddOrUpdateWebhook"
    UploadShipmentDataUrl: "https://simulator.uat.pointz.in:9091/shipway/PushOrderData"

  # AA service vendor URLs
  AA:
    BaseURL: "" #Fetched from central registry
    PostConsentURL: "/Consent"
    ConsentStatusURL: "/Consent/handle"
    ConsentArtefactURL: "/Consent"
    RequestDataURL: "/FI/request"
    FetchDataURL: "/FI/fetch"
    GetAccountLinkStatusURL: "/Account/link/status"
    GenerateAccessTokenURL: "https://uattokens.sahamati.org.in/auth/realms/sahamati/protocol/openid-connect/token"
    FetchCrEntityDetailURL: "https://uatcr.sahamati.org.in/entityInfo/AA"
    FetchCrEntityDetailURLV2: "https://uatcr.sahamati.org.in/v2/entityInfo/AA"
    ConsentUpdateURL: "/consent/update"
    AccountDeLinkURL: "/account/delink"
    UseSahamatiCrAndToken: true
    OneMoneyCrId: "onemoney-aa"
    FinvuCrId: "<EMAIL>"
    GetAccountLinkStatusBulkURL: "/Account/link/Status"
    GetHeartbeatStatusURL: "/Heartbeat"
    EpifiAaKid: "654024c8-29c8-11e8-8868-0289437bf331"
    AAClientApiKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************.A-VX3lgu6T_r2FWIp2bsDAQK9vll6p4uQC_D5LwXmdo"
    SahamatiClientId: "EPIFIUAT"
    GetBulkConsentRequestURL: "/Consent/status/bulk"
    GenerateFinvuJwtTokenURL: "/web/token"
    AaSecretsVersionToUse: "V1"
    FinvuFipMetricsURL: "/fip/latest-metrics-all"
    IsOnemoneyV2Enabled: true
    IsFinvuV2Enabled: true
    Ignosis:
      Url: "https://fiuuat.ignosis.ai:8083/api/v1/fimoney"

  # Bouncy castle library URLs
  BouncyCastle:
    GenerateKeyPairURL: "https://bouncycastle.deploy.pointz.in/ecc/v1/generateKey"
    GetSharedSecretURL: "https://bouncycastle.deploy.pointz.in/ecc/v1/getSharedKey"
    DecryptDataURL: "https://bouncycastle.deploy.pointz.in/ecc/v1/decrypt"
    CkycEncryptAndSignURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/ckyc/v1/encryptAndSign"
    CkycVerifyAndDecryptURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/ckyc/v1/verifyAndDecrypt"

  # Fennel Vendor URLs
  FennelFeatureStore:
    ExtractFeatureSetsURL: "https://main.epifi-staging.aws.fennel.ai/api/v1/extract_features"
    ExtractFeatureSetsURLV2: "https://epifi-staging.aws.fennel.ai/api/v1/query"
    ExtractFeatureSetsURLV3: "https://babel.data-dev.pointz.in/api/v1/query"
    LogDatasetsURL: "https://main.epifi-staging.aws.fennel.ai/api/v1/log"
    LogDatasetsURLV2: "https://epifi-staging.aws.fennel.ai/api/v1/log"
    LogDatasetsURLV3: "https://babel.data-dev.pointz.in/api/v1/log"
    PdScoreURL: "https://loan-default.data-dev.pointz.in/v2/loan_default"
    Simulator:
      Enable: false

  Ckyc:
    SearchURL: "https://testbed.ckycindia.in/Search/ckycverificationservice/verify"
    ApiVersion: "1.2"
    DownloadURL: "https://testbed.ckycindia.in/Search/ckycverificationservice/download"
    EnableCryptor: true

  InhouseOCR:
    MaskDocURL: "https://ocular.data-dev.pointz.in/v1/mask_doc"
    ExtractFieldsURL: "https://ocular.data-dev.pointz.in/v1/extract_fields"
    DetectDocumentURL: "https://ocular.data-dev.pointz.in/v1/detect_doc"
    ExtractFieldsURLV2: "https://ocular.data-dev.pointz.in/v1/extract_fields"

  InhousePopularFAQUrl: "http://popular-faqs.data-dev.pointz.in"

  Digilocker:
    GetAuthorizationCodeUrl: "https://simulator.uat.pointz.in:8080/public/oauth2/1/authorize"
    GetAccessTokenUrl: "https://simulator.uat.pointz.in:8080/public/oauth2/1/token"
    ClientId: "EBB0DE86"
    RedirectUri: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"
    GetListOfIssuedDocumentsUrl: "https://simulator.uat.pointz.in:8080/public/oauth2/2/files/issued"
    GetRefreshTokenUrl: "https://simulator.uat.pointz.in:8080/public/oauth2/1/token"
    GetFileFromUriUrl: "https://simulator.uat.pointz.in:8080/public/oauth2/1/file/"
    GetAadhaarInXmlUrl: "https://simulator.uat.pointz.in:8080/public/oauth2/3/xml/eaadhaar"
    TokenCacheTtl: 10m

  Liquiloans:
    Host: "https://staging-backend-v2.liquiloan.in/api/apiintegration/v3"
    SupplyIntegrationHost: "https://sup-integration-stage.liquiloan.in/api/"
    SftpHost: "sftp.deploy.pointz.in"
    SftpPort: 22
    S3BucketP2PInvestmentLedger: "epifi-uat-p2p-investment-ledger"

  Lending:
    PreApprovedLoan:
      Federal:
        UrlLentra: "" #todo: update federal lentra url in uat
        Url: "https://uatgateway.federalbank.co.in/fedbnk/uat"
        HttpUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat"
        FetchDetailsUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/account_utility/v1.0.0/fetchLoanDetails"
        RespUrl: ""
        # TODO(@kantikumar): update sftp host once available
        SftpHost: ""
        SftpPort: 22
        PlAcntCrnNtbHttpURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/loan/account/creation/api"
        PlAcntCrnEnqNtbHttpURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/loan/enquiry"
      Liquiloans:
        Url: "https://demo-testing-back.liquiloan.in"
        # This URL points to HTTP port of simulator as few Liquiloans API needs request body  in HTTP GET request, those are
        # mocked using Router and not by gRPC method.
        HttpUrl: "https://demo-testing-back.liquiloan.in"
      Idfc:
        # Old endpoint
        # Url: "https://apiext.uat.idfcfirstbank.com/"
        # New endpoint after IDFC's internal migration
        Url: "https://ent-nonprod.api.idfcfirstbank.com"
        # URL to fetch the access token for IDFC APIs
        GetAccessTokenUrl: "https://app.uat-opt.idfcfirstbank.com/platform/oauth/oauth2/token"
        MandatePageUrl: "https://uat.fmreporting.idfcfirstbank.com/IDFCEMandate/EMandateB2BPaynimmo.aspx"
        EnableEncryption: true
        Source: "FIMONEY"
        SimulatorHttpURL: "https://apiext.uat.idfcfirstbank.com/"
      Abfl:
        Url: "https://b2bpartner-api-uat.abfldirect.com"
        BreUrl: "https://b2bpartner-api-uat.abfldirect.com/v2/decisionEngineConfig"
        TxnDetailsUrl: "https://b2bpartner-uat-groww.abfldirect.com"
        PwaJourneyUrl: "https://lendinguat6.finbox.in"
      Moneyview:
        BaseUrl: "https://growth-01.stg.whizdm.com/atlas/v1"
        # This URL points to HTTP port of simulator as few MV API needs request body  in HTTP GET request, those are
        # mocked using Router and not by gRPC method.
        HttpUrl: "https://growth-01.stg.whizdm.com/atlas/v1"
      Setu:
        BaseUrl: "https://3rftmfjjkg.execute-api.ap-south-1.amazonaws.com/default"
      Digitap:
        UanAdvancedUrl: "https://svcdemo.digitap.work/cv/v3/uan_advanced/sync"
      Lenden:
        BaseUrl: "https://dev-tsp-gateway.lendenclub.com/v1/EPF/"
        ProductId: "EcoX-Loan-102"
        EnableCryptor: true

    CreditCard:
      M2P:
        RegisterCustomerHost: "https://ssltest.yappay.in/"
        M2PHost: "https://ssltest.yappay.in/"
        CreditCardRepaymentHost: "https://ssltest.yappay.in/"
        M2PFallbackHost: "https://ssltest.yappay.in/"
        M2PLMSHost: "https://ssltest.yappay.in/"
        M2PPartnerSdkUrl: "https://ssltest.yappay.in/gateway/"
        M2PSetPinUrl: "https://ssltest.yappay.in/gateway/"
        EnableEncryption: true
        RotateKey: true
        M2PFederalHost: "https://ssltest.yappay.in/"
      Federal:
        Url: "https://uatgateway.federalbank.co.in/fedbnk/uat/CreditCard/v1.0.0/"
        UpdateCreditCardLimitDetailsUnsecuredChannel: "M2P-FDEPIFICR"
        UpdateCreditCardLimitDetailsMassUnsecuredChannel: "M2P-FDEPIFIMASSCR"
    CreditLine:
      Federal:
        Url: "https://uatgateway.federalbank.co.in/fedbnk/uat/CreditCard/v1.0.0/limitFetch"
      M2P:
        Url: "https://uat-federal-onboarding.m2pfintech.com/api/v1/customer"
    Collateral:
      LendingMFCentralConfig:
        HttpUrl:
          AuthToken: "https://uatservices.mfcentral.com/oauth/token"
          EncryptAndSign: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/encryptAndSign"
          DecryptAndVerify: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/verifyAndDecrypt"
          SubmitCasSummary: "https://uatservices.mfcentral.com/api/client/V1/submitcassummaryrequest"
          InvestorConsent: "https://uatservices.mfcentral.com/api/client/V1/investorconsent"
          GetCasDocument: "https://uatservices.mfcentral.com/api/client/V1/getcasdocument"
          ValidateLien: "https://uatservices.mfcentral.com/api/client/V1/validatelien"
          SubmitLien: "https://uatservices.mfcentral.com/api/client/V1/submitlien"
          ValidateLienInvokeRevoke: "https://uatservices.mfcentral.com/api/client/V1/validateLienInvokeRevoke"
          LienCheckStatus: "https://uatservices.mfcentral.com/api/client/V1/lienCheckStatus"
          GetTransactionStatus: "https://uatservices.mfcentral.com/api/client/V1/getTransactionStatus"
    SecuredLoans:
      Url: "https://simulator.uat.pointz.in:9091/fiftyfin"

  Alpaca:
    BrokerApiHost: "https://broker-api.sandbox.alpaca.markets"
    BrokerApiVersion: "v1"
    MarketApiHost: "https://data.sandbox.alpaca.markets"
    MarketApiVersion: "v2"
    OrderEventsApiPath: "/v1/events/trades"
    AccountEventsApiPath: "/v1/events/accounts/status"
    FundTransferEventsPath: "/v1/events/transfers/status"
    JournalEventsPath: "/v1/events/journals/status"
    BrokerEventsApiHost: "broker-api.sandbox.alpaca.markets"
    BrokerEventsApiScheme: "https"
    ShouldUseSimulatedEnvForEvents: false
    MarketDataBetaAPIPrefix: "v1beta1"

  MorningStar:
    TokenCacheTtl: 12h
    EquityAPIURL: "https://equityapi.morningstar.com"
    ApiUrl: "https://api.morningstar.com"
    ApiAccessTokenExpiryInDays: 90
    MorningStarObsoleteFundAPIUrl: "https://intools.morningstar.com/identifier/api/data"

  FederalInternationalFundTransfer:
    URL: "https://uatgateway.federalbank.co.in/fedbnk/uat/LRS/v1.0.0"
    CheckLRSEligibilityPrecision: 9

  Esign:
    Leegality:
      Url: "https://sandbox.leegality.com/api/v3.0/"

  ProfileValidation:
    Federal:
      Url: "https://uatgateway.federalbank.co.in/fedbnk/uat"

  #GPlace Vendor URLs
  GPlace:
    FindPlaceReqURL: "https://maps.googleapis.com/maps/api/place/findplacefromtext/json"
    GetPlaceDetailsReqURL: "https://maps.googleapis.com/maps/api/place/details/json"

  GoogleReverseGeocodingUrl: "https://maps.googleapis.com/maps/api/geocode/json"
  GoogleGeocodingUrl: "https://maps.googleapis.com/maps/api/geocode/json"
  InhouseLocationServiceUrl: "https://geo.data-dev.pointz.in"
  MaxmindIp2CityUrlPrefix: "https://geoip.maxmind.com/geoip/v2.1/city/"

  BureauPhoneNumberDetailsUrl: "https://api.overwatch.stg.bureau.id/v1/suppliers/phone-network"

  #DronaPay
  DronapayHostURL: "https://riskuat.dronapay.pointz.in/springapi"

  "InhouseMerchantResolutionServiceUrl": "https://merchant.data-dev.pointz.in/resolution"

  Aml:
    Tss:
      Epifi:
        ScreeningUrl: "https://win-app.uat.pointz.in:54322/crmapi/a44twcustomerscreeningalertcreationcontroller/GenerateScreeningAlert"
        SystemName: "Epifi_tech"
        ParentCompany: "Epifi"
      StockGuardian:
        ScreeningUrl: "https://win-app.uat.pointz.in:54322/crmapi/a44twcustomerscreeningalertcreationcontroller/GenerateScreeningAlert"
        SystemName: "LMS"
        ParentCompany: "SGIPL"

  IncomeEstimatorConf:
    InhouseIncomeEstimatorURL: "https://fortuna-ds.data-dev.pointz.in/income_fetch"

  LocationModel:
    InHouseUrl: "http://onboarding-risk-detection.data-dev.pointz.in/v1/geo_score"
    RiskSeverityValToEnumMapping:
      "LOW": "RISK_SEVERITY_LOW"
      "MEDIUM": "RISK_SEVERITY_MEDIUM"
      "HIGH": "RISK_SEVERITY_HIGH"
      "CRITICAL": "RISK_SEVERITY_CRITICAL"

  EpanConfig:
    FederalConfig:
      SftpConn:
        Host: "************"
        Port: 10022

  EnachConfig:
    FederalConfig:
      SftpConn:
        Host: "************"
        Port: 10022
      FederalSFTPUploadPath: "/TRANSACTIONS/IN/"
      FederalSFTPDownloadPath: "/TRANSACTIONS/OUT/"
      FileTypeToSFTPPathMap:
        FILE_TYPE_PRESENTATION_FILE: "/TRANSACTIONS/IN/"
        FILE_TYPE_PRESENTATION_ACK_FILE: "/TRANSACTIONS/OUT/"
        FILE_TYPE_PRESENTATION_RESPONSE_FILE: "/TRANSACTIONS/OUT/"

  Dreamfolks:
    BaseUrl: "https://simulator.uat.pointz.in:9091/dreamfolks"
    ProgramId: "1000001632"
    ServiceId: "11"

  Razorpay:
    BaseUrl: "https://api.razorpay.com"

  Visa:
    FindNearbyAtmTotalsUrl: "https://sandbox.api.visa.com/globalatmlocator/v3/localatms/totalsinquiry"
    FindNearbyAtmsUrl: "https://sandbox.api.visa.com/globalatmlocator/v3/localatms/atmsinquiry"
    FindGeocodesUrl: "https://sandbox.api.visa.com/globalatmlocator/v3/localatms/geocodesinquiry"
    GetEnhancedForeignExchangeRatesUrl: "https://sandbox.api.visa.com/fx/rates"
    GetEnhancedMarkupForeignExchangeRatesUrl: "https://sandbox.api.visa.com/fx/rates/markup"

  Saven:
    CreditCardBaseUrl: "https://federal-fi.m2pfintech.dev"
    JwtExpiry: "3s"

  PerfiosDigilocker:
    ApiHost: "https://api-in-uat.perfios.com/kyc/api/v1/digilocker"

  Bridgewise:
    ApiHost: "https://rest.bridgewise.com"
    TokenCacheTtl: 12h


  FederalEscalation:
    BaseURL: "https://uatgateway.federalbank.co.in"
    CreateEscalationURL: "fedbnk/uat/CRM/v1.0.0/cxiosrcreation"
    BulkFetchURL: "fedbnk/uat/CRM/v1.0.0/cxsrbulkretrieval"
    S3BucketName: "epifi-uat-cx-ticket-attachments"

Server:
  Ports:
    GrpcPort: 8081
    GrpcSecurePort: 9522
    HttpPort: 9999

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    PanValidationSecretskey: "uat/vendorgateway/panvalidationsecrets"
    # Payment Gateway secrets
    RazorpaySecrets: "uat/vendorgateway/razorpay-federal-secured-cards-api-key"

    #M2P
    M2PSecrets: "uat/vendorgateway/m2p"
    M2PSecuredCardSecrets: "uat/vendorgateway/m2p/secured"
    # TODO(priyansh) : Get this secret added
    M2PMassUnsecuredCardSecrets: "uat/vendorgateway/m2p/mass-unsecured"
    EpifiM2pRsaPrivateKey: "uat/vendorgateway/epifi-m2p-private-key"
    EpifiM2pRsaPrivateKeyV2: "uat/vg-vgpci/epifi-m2p-private-key-2023"
    EpifiM2pRsaPublicKey: "uat/vendorgateway/m2p-public-key"

    # In-house BRE
    InHouseBreBearer: "uat/vendorgateway/inhouse-bre-bearer"

    #Federal
    EpifiFederalPgpPrivateKey: "uat/pgp/pgp-epifi-key-fed-api"
    FederalPgpPublicKey: "uat/pgp/federal-pgp-pub-key"
    EpifiFederalPgpPassphrase: "uat/pgp/pgp-epifi-key-passphrase-fed-api"
    EpifiFederalUPIPrivateKey: "uat/vendorgateway/upi-xml-signature"
    EpifiFederalUPIFallbackPrivateKey: "uat/vendorgateway/upi-xml-signature"
    SenderCode: "uat/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "uat/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "uat/vg-vn-vgpci/federal-auth-service-access-code"
    ClientId: "uat/vg-vgpci/federal-auth-client-id"
    ClientSecretKey: "uat/vg-vgpci/federal-auth-client-secret-key"
    EpifiFederalCardDataPrivateKeyFallBack: "uat/vg-vngw-vgpci/rsa-federal-card-data"
    EpifiFederalCardDataPrivateKey: "uat/vg-vngw-vgpci/rsa-federal-card-data"

    #Closing Balance params
    ClosingBalanceCredentials: "uat/vendorgateway/federal-closing-balance-secrets"

    GetBalanceCredentialsV1: "uat/vendorgateway/federal-get-balance-v1-secrets"

    GetRemitterDetailsCredentials: "uat/vendorgateway/federal-get-remitter-details-secrets"
    GetRemitterDetailsV1Credentials: "uat/vendorgateway/federal-get-remitter-details-secrets-v1"
    GetBeneficiaryNameDetailsCredentials: "uat/vendorgateway/federal-get-beneficiary-name-details-secrets"
    GetCsisStatusCredentials: "uat/vendorgateway/federal-get-csis-status-secrets"

    #FCM
    FCMServiceAccountCredJson: "uat/vendorgateway/fcm-account-credentials"
    #Sendgrid
    SendGridAPIKey: "uat/vendorgateway/sendgrid-api-key"
    #TLS certs
    SimulatorCert: "uat/vg-vgpci/tls-client-cert-for-sim"
    EpiFiFederalClientSslCert: "uat/vg-vgpci/tls-client-ssl-for-federal"
    EpiFiFederalClientSslKey: "uat/vg-vgpci/tls-client-ssl-for-federal"
    EpiFiFederalClientSslSecret: "uat/vg-vgpci/tls-client-ssl-for-federal"
    #Freshdesk
    FreshdeskApiKey: "uat/vendorgateway/freshdesk-api-key"
    EpifiTechRiskFreshdeskApiKey: "uat/vendorgateway/epifitech-risk-freshdesk-api-key"
    #Ozonetel
    OzonetelApiKey: "uat/vendorgateway/ozonetel-api-key"
    #Freshchat
    FreshchatApiKey: "uat/vendorgateway/freshchat-api-key"
    # Pan Validation
    PanValidationAccessId: "uat/vendorgateway/federal-auth-pan-validation-access-id"
    PanValidationAccessCode: "uat/vendorgateway/federal-auth-pan-validation-access-code"
    #Loylty
    LoyltyClientId: "uat/vendorgateway/loylty-auth-client-id"
    LoyltyClientKey: "uat/vendorgateway/loylty-auth-client-key"
    LoyltyClientSecret: "uat/vendorgateway/loylty-auth-client-secret"
    LoyltyClientEncryptionKey: "uat/vendorgateway/loylty-auth-client-encryption-key"
    LoyltyEGVModuleId: "uat/vendorgateway/loylty-auth-egv-module-id"
    LoyltyCharityModuleId: "uat/vendorgateway/loylty-auth-charity-module-id"
    LoyltyApplicationId: "uat/vendorgateway/loylty-auth-application-id"
    LoyltyProgramId: "uat/vendorgateway/loylty-auth-program-id"

    #Qwikcilver
    QwikcilverSecrets: "uat/vendorgateway/qwikcilver-secrets"

    #Thriwe
    ThriweSecrets: "uat/vendorgateway/thriwe-secrets"

    #Riskcovry
    RiskcovrySecrets: "uat/vendorgateway/riskcovry-secrets"

    #Onsurity Secrets
    OnsuritySecrets: "uat/vendorgateway/onsurity-secrets"

    # UPI API
    UPISenderUserId: "uat/vendorgateway/federal-upi-sender-user-id"
    UPISenderPassword: "uat/vendorgateway/federal-upi-sender-password"
    UPISenderCode: "uat/vendorgateway/federal-upi-sender-code"
    UPISenderUserIdfifederal: "uat/vendorgateway/federal-upi-ffi-sender-user-id"
    UPISenderPasswordfifederal: "uat/vendorgateway/federal-upi-ffi-sender-password"
    UPISenderCodefifederal: "uat/vendorgateway/federal-upi-ffi-sender-code"
    UPISenderUserIdfedepsp: "uat/vendorgateway/federal-upi-sender-user-id"
    UPISenderPasswordfedepsp: "uat/vendorgateway/federal-upi-sender-password"
    UPISenderCodefedepsp: "uat/vendorgateway/federal-upi-sender-code"
    UPISenderUserIdIosAddFunds: "uat/vendorgateway/federal-upi-sender-user-id-ios-add-funds"
    UPISenderPasswordIosAddFunds: "uat/vendorgateway/federal-upi-sender-password-ios-add-funds"
    UPISenderCodeIosAddFunds: "uat/vendorgateway/federal-upi-sender-code-ios-add-funds"
    UPISenderUserIdfede: "uat/vendorgateway/federal-upi-sender-user-id"
    UPISenderPasswordfede: "uat/vendorgateway/federal-upi-sender-password"
    UPISenderCodefede: "uat/vendorgateway/federal-upi-sender-code"

    FederalDepositSecrets: "uat/vendorgateway/federal-deposit-secrets"

    # SMS API keys
    TwilioAccountSid: "uat/vendorgateway/twilio-account-sid"
    TwilioApiKey: "uat/vendorgateway/twilio-api-key"
    ExotelApiKey: "uat/vendorgateway/exotel-api-key"
    ExotelApiToken: "uat/vendorgateway/exotel-api-token"
    AclEpifiUserId: "uat/vendorgateway/acl-epifi-user-id"
    AclEpifiPassword: "uat/vendorgateway/acl-epifi-password"
    AclFederalUserId: "uat/vendorgateway/acl-federal-user-id"
    AclFederalPassword: "uat/vendorgateway/acl-federal-password"
    AclEpifiOtpUserId: "uat/vendorgateway/acl-epifi-otp-user-id"
    AclEpifiOtpPassword: "uat/vendorgateway/acl-epifi-otp-password"
    KaleyraFederalApiKey: "uat/vendorgateway/kaleyra-federal-api-key"
    KaleyraFederalCreditCardApiKey: "uat/vendorgateway/kaleyra-federal-cc-api-key"
    KaleyraEpifiApiKey: "uat/vendorgateway/kaleyra-epifi-api-key"
    KaleyraEpifiNRApiKey: "uat/vendorgateway/kaleyra-epifi-nr-api-key"
    AclWhatsappUserId: "uat/vendorgateway/acl-whatsapp-user-id"
    AclWhatsappPassword: "uat/vendorgateway/acl-whatsapp-password"
    WhatsappEnterpriseId: "uat/vendorgateway/whatsapp-enterprise-id"
    WhatsappEnterpriseToken: "uat/vendorgateway/whatsapp-enterprise-token"
    # Video kyc KARZA api key
    KarzaVkycApiKey: "uat/vendorgateway/karza-vkyc-apikey"
    KarzaVkycPriorityApiKey: "uat/vendorgateway/karza-vkyc-priority-apikey"
    KarzaReVkycPriorityApiKey: "uat/vendorgateway/karza-re-vkyc-priority-apikey"
    KarzaSecrets: "uat/vendorgateway/karza"

    # Rounza cricket api's project and api key
    RounazCricketProjectKey: "uat/vendorgateway/roanuz-project-key"
    RounazCricketApiKey: "uat/vendorgateway/roanuz-api-key"

    # Rounaz football api's access and secret key
    RounazFootballAccessKey: "uat/vendorgateway/roanuz-football-access-key"
    RounazFootballSecretKey: "uat/vendorgateway/roanuz-football-secret-key"

    # B2C payments keys
    B2cUserId: "uat/vendorgateway/federal-auth-b2c-payment-user-id"
    B2cPassword: "uat/vendorgateway/federal-auth-b2c-payment-password"
    B2cSenderCodeKey: "uat/vendorgateway/federal-auth-b2c-payment-sender-code"

    # ipstack access key
    IpstackAccessKey: "uat/vendorgateway/ipstack-access-key"

    # Shipway username and password
    ShipwayUsername: "uat/vendorgateway/shipway-username"
    ShipwayPassword: "uat/vendorgateway/shipway-password"

    # client api key for aa
    AaVgSecretsV1: "uat/vendorgateway/aa-sahamati-secrets-v1"
    AaVgVnSecretsV1: "uat/vg-vn/aa-secrets-v1"

    # Experian Credit Report
    ExperianCreditReportPresenceClientName: "uat/vendorgateway/experian-credit-report-presence-client-name"
    ExperianCreditReportFetchClientName: "uat/vendorgateway/experian-credit-report-fetch-client-name"
    ExperianCreditReportForExistingUserClientName: "uat/vendorgateway/experian-credit-report-for-existing-user-client-name"
    ExperianExtendSubscriptionClientName: "uat/vendorgateway/experian-extend-subscription-client-name"
    ExperianVoucherCode: "uat/vendorgateway/experian-credit-report-voucher-code"

    # Experian Secrets
    ExperianSecrets: "uat/vendorgateway/experian-secrets"

    # cvl secrets
    CvlSftpUser: "uat/vendorgateway/cvl-sftp-user"
    CvlSftpPass: "uat/vendorgateway/cvl-sftp-pass"
    CvlSftpUploadUser: "uat/vendorgateway/cvl-sftp-upload-user"
    CvlSftpUploadPass: "uat/vendorgateway/cvl-sftp-upload-pass"
    CvlKraPassKey: "uat/vendorgateway/cvl-kra-pass-key"
    CvlKraPosCode: "uat/vendorgateway/cvl-kra-pos-code"
    CvlKraUserName: "uat/vendorgateway/cvl-kra-user-name"
    CvlKraPassword: "uat/vendorgateway/cvl-kra-password"
    CvlSftpSshKey: "uat/vendorgateway/cvl-sftp-ssh-key"
    CvlSecrets: "uat/vendorgateway/cvl-secrets"

    # nsdl secrets
    NsdlUserId: "uat/vendorgateway/nsdl-user-id"

    # digio secrets
    DigioClientId: "uat/vendorgateway/digio-client-id"
    DigioSecretKey: "uat/vendorgateway/digio-secret-key"

    # Manch secrets
    ManchSecureKey: "uat/vendorgateway/manch-secure-key"
    ManchTemplateKey: "uat/vendorgateway/manch-template-key"

    SeonClientApiKey: "uat/vendorgateway/seon-api-key"

    CAMSKey: "uat/investment-vendorgateway/cams-key"

    KarvyKey: "uat/investment-vendorgateway/karvy-key"

    SmallCaseKey: "uat/vendorgateway/smallcase-key"

    MFCentralKey: "uat/vendorgateway/mfcentral-key"

    #ckyc
    CkycFiCode: "uat/vendorgateway/ckyc-fi-code"

    #digilocker
    DigilockerClientSecret: "uat/vendorgateway/digilocker-client-secret"

    # Lending keys
    PreApprovedLoanFederalSecrets: "uat/vendorgateway/lending-preapprovedloans-secrets"
    FederalSftpSshKey: "uat/vendorgateway/federal-sftp-ssh-key"
    PreApprovedLoanSecrets: "uat/vendorgateway/lending-preapprovedloans-secrets"

    # Leegality
    LeegalitySecret: "uat/vendorgateway/esign-leegality-secrets"

    #Liquiloans
    LiquiloansMid: "uat/vendorgateway/liquiloans-mid"
    LiquiloansKey: "uat/vendorgateway/liquiloans-key"
    LiquiloansSecrets: "uat/vendorgateway/liquiloans-secrets"

    #GPlace api key
    GPlaceApiKey: "uat/vendorgateway/gplace-api-key"

    # karza api keys
    KarzaKey: "uat/vendorgateway/karza-key"
    TartanKey: "uat/vendorgateway/tartan-key"
    VKYCAgentDashboardSecrets: "uat/vendorgateway/vkyc-agent-dash"

    # DronaPay
    DronaPayKey: "uat/vendorgateway/dronapay-key"

    GeolocationKey: "uat/vendorgateway/geolocation-key"

    # Secrets of payu
    PayuToken: "uat/vendorgateway/payu-token"
    PayuApiKey: "uat/vendorgateway/payu-key"

    MaxmindSecrets: "uat/vendorgateway/maxmind-secrets"

    BureauSecrets: "uat/vendorgateway/bureau-secrets"

    SignzySecrets: 'uat/vendorgateway/signzy-secrets'

    AlpacaSecrets: "uat/vendorgateway/alpaca-secrets"

    FederalInternationalFundTransferSecrets: "uat/vendorgateway/federal-internationalfundtransfer-secrets"

    FederalProfileValidationSecrets: 'uat/vendorgateway/hunter-secrets'

    # p2p Investment secrets
    p2pInvestmentLiquiloansSftpUser: "uat/vendorgateway/p2p-investment-liquiloans-sftp-user"
    p2pInvestmentLiquiloansSftpPassword: "uat/vendorgateway/p2p-investment-liquiloans-sftp-pass"

    MorningStarSecrets: "uat/vendorgateway/morningstar-secrets"
    MorningStarAccountSecrets: "uat/vendorgateway/morningstar-account-secrets"

    DepositInterestRateInfoSecrets: "uat/vendorgateway/deposit-interest-rate-info-secrets"

    TssApiToken: "uat/vendorgateway/tss-api-token"
    TSSAPITokenForSG: "uat/vendorgateway/tss-api-token-sg"

    VistaraSecrets: "uat/vendorgateway/vistara-secrets"

    # Fennel Secrets
    FennelFeatureStoreSecrets: "uat/vendorgateway/fennel-secrets"

    DreamfolksSecrets: "uat/vendorgateway/dreamfolks-secrets"

    CommonSftpUser: "uat/vendorgateway-simulator/sftp-upload-user"
    CommonSftpPass: "uat/vendorgateway-simulator/sftp-upload-pass"

    # Lentra secrets
    LentraSecrets: "uat/vendorgateway/lentra-secrets"

    EpifiFederalEpanSftpSecrets: "uat/vendorgateway/epifi-federal-enach-sftp-secrets"

    EpifiFederalEnachSftpSecrets: "uat/vendorgateway/epifi-federal-enach-sftp-secrets"

    CredgenicsAuthToken: "uat/vendorgateway/credgenics"

    CredgenicsAuthenticationKeyV2: "uat/vendorgateway/credgenics-v2"

    LendingMFCentralSecrets: 'uat/vendorgateway/lamf-secrets'

    LeadSquaredSecrets: "uat/vendorgateway/leadsquared-keys"

    LendingFiftyFinLamfSecrets: "uat/vendorgateway/fiftyfin-lamf-secrets"

    KarzaPanProfileKey: "uat/vendorgateway/pan-profile-karza-key"

    PoshvineSecrets: "uat/vendorgateway/poshvine-secrets"

    CibilSecrets: "uat/vendorgateway/cibil"

    BureauIdSecrets: "uat/vendorgateway/bureauid-secrets"

    NetCoreEpifiSecrets: "uat/vendorgateway/netcore-epifi-secrets"

    VisaSecrets: "uat/vendorgateway/visa-secrets"

    PerfiosDigilockerSecrets: "uat/vendorgateway/perfios-digilocker-secrets"

    AirtelFedSMSSecrets: "uat/vendorgateway/airtel-fed-sms-secrets"

    BridgewiseSecrets: "uat/vendorgateway/bridgewise-secrets"

    FederalEscalationSecrets: "uat/vendorgateway/federal-io-secrets"
    FederalEscalationClientSslCert: "uat/vendorgateway/federal-io-cert"
    FederalEscalationClientSslKey: "uat/vendorgateway/federal-io-key"

    SavenSecrets: "uat/vendorgateway/saven-cc-secrets"

SyncWrapperSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-vn-sync-wrapper-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 2
      MaxAttempts: 6
      TimeUnit: "Second"

DisputeSFTP:
  user: ""
  password: ""
  host: ""
  port: 80
  S3BucketName: "epifi-federal-disputes"

Flags:
  TrimDebugMessageFromStatus: false
  TokenValidation: true
  SkipCertVerifyPANValidation: false
  AllowSpecialCharactersInAddress: true
  UseNewSolID: true
  EnableTransactionEnquiryNewApi: true
  EnableFennelClusterV3: true
  EnableInstrumentBillingInterceptor: true
  EnableCibilV2Secrets: false
  UseNewFieldsInCifCreation: true

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/vendorgateway/secure.log"
  MaxSizeInMBs: 5
  MaxBackups: 20

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 15

FcmAnalyticsLabel: "uat-push-notification"

AwsSes:
  FiMoneyArn: "arn:aws:ses:ap-south-1:632884248997:identity/fi.money"
  FiCareArn: "arn:aws:ses:ap-south-1:632884248997:identity/fi.care"
  StockGuardianArn: "arn:aws:ses:ap-south-1:632884248997:identity/stockguardian.in"

# generic downtime handler for vg
# the vendor name is the enum value with config values like IsEnabled, StartTime, EndTime
DowntimeConfig:
  NSDL:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2022-03-15 15:04:05"
        EndTimestamp: "2022-03-16 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  CVLKRA:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2021-11-01 15:04:05"
        EndTimestamp: "2021-11-02 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  CKYC:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2021-11-01 15:04:05"
        EndTimestamp: "2021-11-02 15:04:05"
        Msg: "This is due to a downtime at our vendor partner. This should be up by %s. We will notify you once its up."
  DIGILOCKER:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2021-11-01 15:04:05"
        EndTimestamp: "2021-11-02 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  MANCH:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2022-08-26 23:00:00"
        EndTimestamp: "2022-08-27 09:00:00"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

DisputeConfig:
  FiRequesterId: "F768484C-9C9A-4023-B298-3BA45DA1F352"

PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: "uat/pgp/v1/pgp-epifi-fed-api"
  ExternalEntity:
    - Secret: "uat/pgp/v1/federal-pgp-pub-key-for-epifi"

VideoSdk:
  Secrets:
    Path: "uat/vendorgateway/videosdk"

FederalAPICreds:
  REQUEST_SOURCE_UNSPECIFIED:
    Path: "uat/vg-vgpci/vendor-api-secrets-federal-default"
  REQUEST_SOURCE_LOANS:
    Path: "uat/vg-vgpci/vendor-api-secrets-federal-b2c-loans"

Uqudo:
  AccessTokenURL: "https://auth.uqudo.io/api/oauth/token"
  PublicKeyURL: "https://id.uqudo.io/api/.well-known/jwks.json"
  FetchImageURL: "https://id.uqudo.io/api/v1/info/img"
  EmiratesIdOcrUrl: "https://id.uqudo.io/api/v1/scan"
  Secrets:
    Path: "uat/vendorgateway/uqudo"
