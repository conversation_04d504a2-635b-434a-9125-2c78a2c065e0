package moneyview

//nolint:goimports
import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	moneyviewVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	"github.com/epifi/gamma/vendorgateway/config"
)

const (
	moneyViewAccessTokenKey = "money_view_access_token" //nolint:gosec
	tokenExpiryDuration     = 24 * time.Hour
)

type Service struct {
	Handler     *vendorapi.HTTPRequestHandler
	conf        *config.Moneyview
	cacheClient cache.CacheStorage
}

func NewService(handler *vendorapi.HTTPRequestHandler, conf *config.Moneyview, cacheClient cache.CacheStorage) *Service {
	return &Service{Handler: handler, conf: conf, cacheClient: cacheClient}
}

// compile check to ensure Service implements moneyviewVgPb.MoneyviewServer
var _ moneyviewVgPb.MoneyviewServer = &Service{}

// CreateLead rpc is useful to create a lead for a loan offer.
// nolint: dupl
func (s *Service) CreateLead(ctx context.Context, req *moneyviewVgPb.CreateLeadRequest) (*moneyviewVgPb.CreateLeadResponse, error) {
	accessToken, err := s.getAuthToken(ctx, req.GetUserType())
	if err != nil {
		logger.Error(ctx, "error fetching access token from vendor", zap.Error(err))
		return &moneyviewVgPb.CreateLeadResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	partnerCode := s.conf.PartnerCode
	// In order to move all the leads from OM to PA, we will be sending all the leads to PA channel only
	// if req.GetUserType() == moneyviewVgPb.MvUserType_MV_USER_TYPE_OPEN_MARKET {
	//	partnerCode = s.conf.OpenMarketCredentials.PartnerCode
	// }

	vendorReq := &CreateLeadRequest{
		BaseUrl:     s.conf.BaseUrl,
		PartnerCode: partnerCode,
		AccessToken: accessToken,
		Req:         req,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error handling createLead request", zap.Error(err))
		return &moneyviewVgPb.CreateLeadResponse{Status: vendorapi.GetStatusFromError(err)}, nil
	}
	return res.(*moneyviewVgPb.CreateLeadResponse), nil
}

// GetLeadStatus rpc is useful to fetch the status of an already created lead.
// nolint: dupl
func (s *Service) GetLeadStatus(ctx context.Context, req *moneyviewVgPb.GetLeadStatusRequest) (*moneyviewVgPb.GetLeadStatusResponse, error) {
	accessToken, err := s.getAuthToken(ctx, req.GetUserType())
	if err != nil {
		logger.Error(ctx, "error fetching access token from vendor", zap.Error(err))
		return &moneyviewVgPb.GetLeadStatusResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	vendorReq := &GetLeadStatusRequest{
		BaseUrl:     s.conf.BaseUrl,
		AccessToken: accessToken,
		LeadId:      req.GetLeadId(),
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error handling get lead status request", zap.Error(err))
		return &moneyviewVgPb.GetLeadStatusResponse{Status: vendorapi.GetStatusFromError(err)}, nil
	}
	return res.(*moneyviewVgPb.GetLeadStatusResponse), nil
}

// GetOffers rpc is useful to fetch the offers from for a given lead.
// nolint: dupl
func (s *Service) GetOffers(ctx context.Context, req *moneyviewVgPb.GetOffersRequest) (*moneyviewVgPb.GetOffersResponse, error) {
	accessToken, err := s.getAuthToken(ctx, req.GetUserType())
	if err != nil {
		logger.Error(ctx, "error fetching access token from vendor", zap.Error(err))
		return &moneyviewVgPb.GetOffersResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	vendorReq := &GetOffersRequest{
		BaseUrl:     s.conf.BaseUrl,
		AccessToken: accessToken,
		LeadId:      req.GetLeadId(),
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error handling getOffers request", zap.Error(err))
		return &moneyviewVgPb.GetOffersResponse{Status: vendorapi.GetStatusFromError(err)}, nil
	}
	return res.(*moneyviewVgPb.GetOffersResponse), nil
}

// GetPWAJourneyUrl rpc is useful to fetch the moneyview PWA journey url for a loan application/account created for a given lead.
// nolint: dupl
func (s *Service) GetPWAJourneyUrl(ctx context.Context, req *moneyviewVgPb.GetPWAJourneyUrlRequest) (*moneyviewVgPb.GetPWAJourneyUrlResponse, error) {
	accessToken, err := s.getAuthToken(ctx, req.GetUserType())
	if err != nil {
		logger.Error(ctx, "error fetching access token from vendor", zap.Error(err))
		return &moneyviewVgPb.GetPWAJourneyUrlResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	vendorReq := &GetPWAJourneyUrlRequest{
		BaseUrl:     s.conf.BaseUrl,
		AccessToken: accessToken,
		LeadId:      req.GetLeadId(),
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error handling get pwa url request", zap.Error(err))
		return &moneyviewVgPb.GetPWAJourneyUrlResponse{Status: vendorapi.GetStatusFromError(err)}, nil
	}
	return res.(*moneyviewVgPb.GetPWAJourneyUrlResponse), nil
}

func (s *Service) getAuthTokenFromVendor(ctx context.Context, userType moneyviewVgPb.MvUserType) (string, error) {
	vendorReq := &GenerateAuthTokenRequest{
		BaseUrl: s.conf.BaseUrl,
	}
	vendorReq.UserName = s.conf.Username
	vendorReq.Password = s.conf.Password
	vendorReq.PartnerCode = s.conf.PartnerCode
	// In order to move all the leads from OM to PA, we will be sending all the leads to PA channel only
	// switch userType {
	// case moneyviewVgPb.MvUserType_MV_USER_TYPE_WHITELISTED_OFFER:
	//	vendorReq.UserName = s.conf.Username
	//	vendorReq.Password = s.conf.Password
	//	vendorReq.PartnerCode = s.conf.PartnerCode
	// case moneyviewVgPb.MvUserType_MV_USER_TYPE_OPEN_MARKET:
	//	vendorReq.UserName = s.conf.OpenMarketCredentials.Username
	//	vendorReq.Password = s.conf.OpenMarketCredentials.Password
	//	vendorReq.PartnerCode = s.conf.OpenMarketCredentials.PartnerCode
	// default:
	//	return "", errors.New("user type is not configured")
	// }

	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		return "", fmt.Errorf("error handling get auth token request, err : %w", err)
	}

	authTokenRes := res.(*moneyviewVgPb.GenerateAuthTokenResponse)
	if !authTokenRes.GetStatus().IsSuccess() || authTokenRes.GetAuthToken() == "" {
		logger.Error(ctx, "error generation access token", zap.Any(logger.RPC_STATUS, authTokenRes.GetStatus()))
		return "", fmt.Errorf("error generating access token")
	}
	return authTokenRes.GetAuthToken(), nil
}

func (s *Service) GetDeDupeStatus(ctx context.Context, req *moneyviewVgPb.GetDeDupeStatusRequest) (*moneyviewVgPb.GetDeDupeStatusResponse, error) {
	accessToken, err := s.getAuthToken(ctx, req.GetUserType())
	if err != nil {
		logger.Error(ctx, "error fetching access token from vendor", zap.Error(err))
		return nil, err
	}
	vendorReq := &GetDeDupeStatusRequest{
		BaseUrl:     s.conf.HttpUrl,
		AccessToken: accessToken,
		Req:         req,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error handling get dedupe status request", zap.Error(err))
		return nil, err
	}
	return res.(*moneyviewVgPb.GetDeDupeStatusResponse), nil
}

// nolint:dupl
func (s *Service) GetSingleDeDupeStatus(ctx context.Context, req *moneyviewVgPb.GetSingleDeDupeStatusRequest) (*moneyviewVgPb.GetSingleDeDupeStatusResponse, error) {
	accessToken, err := s.getAuthToken(ctx, req.GetUserType())
	if err != nil {
		logger.Error(ctx, "error fetching access token from vendor", zap.Error(err))
		return nil, err
	}
	vendorReq := &GetSingleDeDupeStatusRequest{
		BaseUrl:     s.conf.HttpUrl,
		AccessToken: accessToken,
		Req:         req,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error handling get single dedupe status request", zap.Error(err))
		return nil, err
	}
	return res.(*moneyviewVgPb.GetSingleDeDupeStatusResponse), nil
}

func (s *Service) getAuthToken(ctx context.Context, userType moneyviewVgPb.MvUserType) (string, error) {
	token, err := s.cacheClient.Get(ctx, moneyViewAccessTokenKey)
	if err == nil {
		return token, nil
	}

	// getting new token from MV
	authToken, err := s.getAuthTokenFromVendor(ctx, userType)
	if err != nil {
		return "", errors.Wrap(err, "error while fetching access token from vendor")
	}

	// store the access token to in-memory cache with expiry 1min less than expiry
	err = s.cacheClient.Set(ctx, moneyViewAccessTokenKey, authToken, tokenExpiryDuration-time.Minute)
	if err != nil {
		return "", errors.Wrap(err, "error while storing the access code in cache")
	}
	return authToken, nil
}
