package com.epifi.dataplatform.epiview.scripts
import com.typesafe.config.Config
import org.apache.spark.sql.SparkSession
import com.epifi.dataplatform.commons.readwrite.HudiReadWrite
import org.apache.spark.sql.types.StringType
import org.apache.spark.sql.functions._
import scala.collection.JavaConverters._

class PayEvents extends Scripts {

  override def execute(platformConfig: Config,
                       jobConfig: Config,
                       pipelineConfig: Config,
                       sparkSession: SparkSession): Unit = {
    val configName = jobConfig.getString("configName")
    if(configName == "pay_events_v2"){
      val spark = sparkSession
      val lastSuccessfulRun = jobConfig.getString("lastSuccessfulRun")
      val dpPath = platformConfig.getString(s"datalake.base-dp")
      val colDropList = pipelineConfig.getStringList("drop_column_list").asScala.toList
      val updatedReadPath = dpPath + "event/pay_events/"
      val readPayDF = HudiReadWrite(sparkSession, jobConfig)
        .hudiRead(updatedReadPath, Some(lastSuccessfulRun)).drop(colDropList:_*)
      readPayDF.createOrReplaceTempView("pay")

      val finalDf = spark
        .sql(
          """
                                  select *,
              case when get_json_object( properties, '$.protocol') = 0 then 'PAYMENT_PROTOCOL_UNSPECIFIED' when get_json_object( properties, '$.protocol') = 1 then 'INTRA_BANK' when get_json_object( properties, '$.protocol') = 2 then 'NEFT'
              when get_json_object( properties, '$.protocol') = 3 then 'IMPS' when get_json_object( properties, '$.protocol') = 4 then 'RTGS' when get_json_object( properties, '$.protocol') = 5 then 'UPI'  when get_json_object( properties, '$.protocol') = 6 then 'CARD' else null end as protocol_value
            from pay""")
        .withColumnRenamed("protocol_value", "protocol")
        .withColumnRenamed("user_id", "actor_id")
        .withColumn("partition_value", to_date(col("timestamp")).cast(StringType))
      writeToDNA(finalDf, pipelineConfig, platformConfig, jobConfig)
    }
    else {
      val spark = sparkSession
      val lastSuccessfulRun = jobConfig.getString("lastSuccessfulRun")
      val readPayDF = readFromBaseDP("event",
        "",
        "pay",
        Some(lastSuccessfulRun),
        jobConfig,
        platformConfig,
        sparkSession)

      readPayDF.createOrReplaceTempView("pay")
      val finalDf = spark
        .sql(
          """
                              select *,
          case when protocol = 0 then 'PAYMENT_PROTOCOL_UNSPECIFIED' when protocol = 1 then 'INTRA_BANK' when protocol = 2 then 'NEFT'
          when protocol = 3 then 'IMPS' when protocol = 4 then 'RTGS' when protocol = 5 then 'UPI'  when protocol = 6 then 'CARD' else null end as protocol_value
        from pay""")
        .drop("protocol")
        .withColumnRenamed("protocol_value", "protocol")
        .withColumnRenamed("work_flow", "order_workflow")
        .withColumnRenamed("user_id", "actor_id")
      writeToDNA(finalDf, pipelineConfig, platformConfig, jobConfig)
    }
  }

}