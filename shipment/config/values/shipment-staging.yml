Application:
  Environment: "staging"
  Name: "shipment"

Server:
  Ports:
    GrpcPort: 8097
    GrpcSecurePort: 9518
    HttpPort: 9999

VendorDataDb:
  AppName: "shipment"
  StatementTimeout: 1s
  Name: "vendordata"
  EnableDebug: false
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"

AWS:
  Region: "ap-south-1"

Secrets:
  Ids:
    VendorDataDbUsernamePassword: "staging/rds/postgres14"


Flags:
  TrimDebugMessageFromStatus: false
  MaxGoRoutines: 30

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true
