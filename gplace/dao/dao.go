//go:generate dao_metrics_gen .
package dao

import (
	"context"

	"github.com/google/wire"

	modelPb "github.com/epifi/gamma/api/gplace/model"
	"github.com/epifi/gamma/api/vendorgateway/gplace"
)

var WireGPlaceDataDaoSet = wire.NewSet(NewGPlaceDataDaoPgdb, wire.Bind(new(GPlaceDataDao), new(*GPlaceDataDaoPgdb)))

type GPlaceDataDao interface {
	Create(ctx context.Context, GPlaceData *modelPb.GPlaceData) (*modelPb.GPlaceData, error)
	GetById(ctx context.Context, id string) (*modelPb.GPlaceData, error)
	GetByPlaceNameAndLocationBias(ctx context.Context, placeName string, bias *gplace.LocationBias) (*modelPb.GPlaceData, error)
}
