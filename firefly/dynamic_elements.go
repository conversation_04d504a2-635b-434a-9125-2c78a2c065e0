// nolint
package firefly

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	homeFePb "github.com/epifi/gamma/api/frontend/home"

	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	tcPb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	dePb "github.com/epifi/gamma/api/dynamic_elements"
	ffPb "github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/billing"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/segment"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

const (
	card_program_attribute_key     = "CARD_PROGRAM"
	dpd_30_element_id              = "c5e969b5-5e78-4b07-884a-e734827a707f"
	three_days_overdue_element_id  = "51472dcf-9283-49b0-99c4-16457e5db69a"
	thirty_days_overdue_element_id = "735be8b0-ad58-4f74-a3a6-eead04633350"
	two_days_overdue_element_id    = "3a628694-dc47-4d22-a85d-ada14437727e"
	tomorrow_overdue_element_id    = "363955a1-5e32-45e6-a5e5-90b408d19c50"
	due_today_element_id           = "********-d60a-4bcf-92c6-2312c99a96a4"
	bill_gen_day_element_id        = "85eca563-d0b3-4319-8039-ae9e5f90997d"
)

var (

	// default value is 24 hours
	repaymentElementFrequencyMap = map[string]time.Duration{
		dpd_30_element_id:              time.Minute * 150,
		thirty_days_overdue_element_id: time.Hour * 8,
	}

	white         = "#FFFFFF"
	axolotl       = "#5D7D4C"
	teaGreen      = "#C5E9B2"
	jetStream     = "#BFE3C1"
	amazon        = "#3E8355"
	grey          = "#B2B5B9"
	graphiteBlack = "#28292B"
	algaeGreen    = "#305C55"
	IDBBBlack     = "#0A0A0A"
	mediumGrey    = "#B2B5B9"
	black         = "#313234"
	skyBlue       = "#4BBFA4"
	lightBlue     = "#D0EDF7"
	oceanRipple   = "#DCF3EE"
	seaGreen      = "#2E8172"
	lochinvar     = "#479588"
	hazel         = "#7FCEC1"
)

func (s *Service) isFrequencyCapExceeded(ctx context.Context, mappingResp *tcPb.GetTargetedCommsMappingsResponse, elementId string, actorId string) (bool, error) {
	freq, ok := repaymentElementFrequencyMap[elementId]
	if !ok {
		freq = 24 * time.Hour
	}

	for _, mapping := range mappingResp.GetTargetedCommsMappingList() {
		if mapping.GetElementId() == elementId {
			lastCallBackTime := mapping.GetLastCallbackTime()
			if dateTimePkg.GetAbsoluteTimeDifference(lastCallBackTime.AsTime(), time.Now()) < freq {
				logger.Debug(ctx, fmt.Sprintf("found absolute time difference less than freq cap for freq: %v", freq))
				return true, nil
			}
		}
	}

	return false, nil
}

func (s *Service) addDynamicElementMapping(ctx context.Context, mappingResp *tcPb.GetTargetedCommsMappingsResponse, actorId, elementId string) error {

	mapping := getCommsElementByElementId(mappingResp.GetTargetedCommsMappingList(), elementId)
	// add mapping if not present
	if mapping == nil {
		addMappingResp, addMappingErr := s.inAppTargetedCommsClient.AddTargetedCommsMapping(ctx, &tcPb.AddTargetedCommsMappingRequest{
			TargetedCommsElementId: elementId,
			MappingDetailsList: []*tcPb.MappingDetails{
				{
					MappingType: tcPb.MappingType_MAPPING_TYPE_USER,
					MappedValue: actorId,
				},
			},
			AppDetailsMappingList: nil,
		})
		if rpcErr := epifigrpc.RPCError(addMappingResp, addMappingErr); rpcErr != nil {
			logger.Error(ctx, "error while adding nudge in in_app_targeted_comms", zap.Error(rpcErr))
			return rpcErr
		}
	}

	return nil
}

func (s *Service) FetchDynamicElements(ctx context.Context, req *dePb.FetchDynamicElementsRequest) (*dePb.FetchDynamicElementsResponse, error) {
	screenAdditionalInfo, ok := req.GetClientContext().GetScreenAdditionalInfo().(*dePb.ClientContext_HomeInfo)
	if !ok {
		logger.Error(ctx, "failed to fetch home info from context")
		return &dePb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	eligibilityRes, err := s.FetchCreditCardEligibility(ctx, &ffPb.FetchCreditCardEligibilityRequest{ActorId: req.GetActorId(), Vendor: ffEnumsPb.Vendor_FEDERAL})
	rpcErr := epifigrpc.RPCError(eligibilityRes, err)
	switch {
	// skip showing home banner if credit card already exist or if not eligible
	case !eligibilityRes.GetIsUserCcEligible():
		return &dePb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	case eligibilityRes.GetStatus().IsAlreadyExists():
		if screenAdditionalInfo.HomeInfo.GetSection() == dePb.HomeScreenAdditionalInfo_SECTION_GTM_POPUP {
			content, elementId, contentErr := s.getPaymentReminderDynamicElementContent(ctx, req.GetActorId())
			if contentErr != nil {
				logger.Error(ctx, "error in getPaymentReminderDynamicElementContent", zap.Error(contentErr))
				return &dePb.FetchDynamicElementsResponse{
					Status: rpcPb.StatusInternalWithDebugMsg(contentErr.Error()),
				}, nil
			}

			mappingResp, mappingErr := s.inAppTargetedCommsClient.GetTargetedCommsMappings(ctx, &tcPb.GetTargetedCommsMappingsRequest{
				MappingDetails: &tcPb.MappingDetails{
					MappingType: tcPb.MappingType_MAPPING_TYPE_USER,
					MappedValue: req.GetActorId(),
				},
			})

			grpcErr := epifigrpc.RPCError(mappingResp, mappingErr)
			if grpcErr != nil && !mappingResp.GetStatus().IsRecordNotFound() {
				logger.Error(ctx, "error in GetTargetedCommsMappings", zap.Error(grpcErr))
				return &dePb.FetchDynamicElementsResponse{
					Status: rpcPb.StatusInternal(),
				}, nil
			}

			if elementId != "" {
				addErr := s.addDynamicElementMapping(ctx, mappingResp, req.GetActorId(), elementId)
				if addErr != nil {
					return &dePb.FetchDynamicElementsResponse{
						Status: rpcPb.StatusInternalWithDebugMsg(addErr.Error()),
					}, nil
				}
			}

			capExceeded, capErr := s.isFrequencyCapExceeded(ctx, mappingResp, elementId, req.GetActorId())
			if capErr != nil {
				return &dePb.FetchDynamicElementsResponse{
					Status: rpcPb.StatusInternalWithDebugMsg(capErr.Error()),
				}, nil
			}

			if capExceeded {
				logger.Info(ctx, fmt.Sprintf("frequency cap exceeded for elementId: %v", elementId))
				return &dePb.FetchDynamicElementsResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil
			}

			return &dePb.FetchDynamicElementsResponse{
				Status: rpc.StatusOk(),
				ElementsList: []*dePb.DynamicElement{
					{
						Id:            elementId,
						OwnerService:  typesPb.ServiceName_FIREFLY_SERVICE,
						UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
						StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_GTM_POP_UP,
						Content:       content,
					},
				},
			}, nil
		}

		return &dePb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	case rpcErr != nil:
		logger.Error(ctx, "error fetching credit card limit", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &dePb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	if s.genConf.DisableCreditCardOnboarding() {
		return &dePb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	switch {
	case screenAdditionalInfo.HomeInfo.GetSection() == dePb.HomeScreenAdditionalInfo_SECTION_BODY2 &&
		screenAdditionalInfo.HomeInfo.GetVersion() == dePb.HomeScreenAdditionalInfo_VERSION_V2:
		content, contentErr := getDynamicElementContent(eligibilityRes.GetCardProgramType(),
			eligibilityRes.GetCardProgram())
		if contentErr != nil {
			logger.Error(ctx, "error getting dynamic element content", zap.Error(contentErr))
			return &dePb.FetchDynamicElementsResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}

		return &dePb.FetchDynamicElementsResponse{
			Status: rpc.StatusOk(),
			ElementsList: []*dePb.DynamicElement{
				{
					OwnerService:  typesPb.ServiceName_FIREFLY_SERVICE,
					UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
					StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_SCROLLABLE,
					Content:       content,
				},
			},
		}, nil
	case screenAdditionalInfo.HomeInfo.GetSection() == dePb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY ||
		screenAdditionalInfo.HomeInfo.GetSection() == dePb.HomeScreenAdditionalInfo_SECTION_FEATURE_SECONDARY:
		dynamicElements, eleErr := getDynamicElements(ctx, screenAdditionalInfo.HomeInfo.GetSection(),
			eligibilityRes.GetCardProgramType(), eligibilityRes.GetCardProgram())
		if eleErr != nil {
			logger.Error(ctx, "error getting dynamic element content", zap.Error(eleErr))
			return &dePb.FetchDynamicElementsResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		return &dePb.FetchDynamicElementsResponse{
			Status:       rpcPb.StatusOk(),
			ElementsList: dynamicElements,
		}, nil
	default:
		return &dePb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
}

func (s *Service) isUserDpd(ctx context.Context, actorId string) (bool, error) {
	segmentRes, err := s.segmentationServiceClient.IsMember(ctx, &segment.IsMemberRequest{
		ActorId:    actorId,
		SegmentIds: []string{s.conf.DpdThirtySegmentId},
	})
	if grpcErr := epifigrpc.RPCError(segmentRes, err); grpcErr != nil {
		logger.Error(ctx, "error in IsMember", zap.Error(grpcErr))
		return false, err
	}

	segmentResVal := segmentRes.GetSegmentMembershipMap()["65ea3cc4-87b1-4c45-9d7e-0901c5d4c530"]

	return segmentResVal.GetSegmentStatus() == segment.SegmentStatus_SEGMENT_INSTANCE_FOUND &&
		segmentResVal.GetIsActorMember(), nil
}

func (s *Service) getMinDueDetails(ctx context.Context, actorId string) (*moneyPb.Money, *moneyPb.Money, *typesPb.Date, error) {
	dueRes, err := s.ffAccountingClient.GetCreditAccountDueInformation(ctx, &accounting.GetCreditAccountDueInformationRequest{
		GetBy: &accounting.GetCreditAccountDueInformationRequest_ActorId{
			ActorId: actorId,
		},
	})
	if grpcErr := epifigrpc.RPCError(dueRes, err); grpcErr != nil {
		logger.Error(ctx, "error in GetCreditAccountDueInformation", zap.Error(grpcErr))
		return nil, nil, nil, grpcErr
	}

	return dueRes.GetUnpaidTotalDue(), dueRes.GetUnpaidMinDue(), dueRes.GetDueDate(), nil
}

func (s *Service) getLatestBillingDate(ctx context.Context, actorId string) (*timestamp.Timestamp, *billing.BillWindow, error) {
	billRes, err := s.billingClient.GetCreditCardBill(ctx, &billing.GetCreditCardBillRequest{
		GetBy: &billing.GetCreditCardBillRequest_ActorId{
			ActorId: actorId,
		},
	})

	if grpcErr := epifigrpc.RPCError(billRes, err); grpcErr != nil {
		logger.Error(ctx, "error in GetCreditCardBill", zap.Error(grpcErr))
		return nil, nil, grpcErr
	}

	return billRes.GetCreditCardBill().GetCreatedAt(), billRes.GetBillWindow(), nil
}

func (s *Service) DynamicElementCallback(ctx context.Context, req *dePb.DynamicElementCallbackRequest) (*dePb.DynamicElementCallbackResponse, error) {
	mappingResp, mappingErr := s.inAppTargetedCommsClient.GetTargetedCommsMappings(ctx, &tcPb.GetTargetedCommsMappingsRequest{
		MappingDetails: &tcPb.MappingDetails{
			MappingType: tcPb.MappingType_MAPPING_TYPE_USER,
			MappedValue: req.GetActorId(),
		},
	})
	rpcErr := epifigrpc.RPCError(mappingResp, mappingErr)
	if rpcErr != nil && !rpcPb.StatusFromError(rpcErr).IsRecordNotFound() {
		logger.Error(ctx, "received unexpected response from the for GetTargetedCommsMappings rpc", zap.Error(rpcErr))
		return &dePb.DynamicElementCallbackResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	mapping := getCommsElementByElementId(mappingResp.GetTargetedCommsMappingList(), req.GetElementId())
	// If RNF or mapping is missing then return
	if rpcPb.StatusFromError(rpcErr).IsRecordNotFound() || mapping == nil {
		logger.Debug(ctx, fmt.Sprintf("mapping not present for element id: %v", req.GetElementId()))
		return &dePb.DynamicElementCallbackResponse{
			Status: rpcPb.StatusOk(),
		}, nil
	}

	// if mapping is found then update it
	var updateMask []tcPb.InAppTargetedCommsMappingFieldMask
	updateMask = append(updateMask, tcPb.InAppTargetedCommsMappingFieldMask_InAppTargetedCommsMappingFieldMask_LAST_CALLBACK_TIME)
	resp, err := s.inAppTargetedCommsClient.UpdateTargetedCommsMapping(ctx, &tcPb.UpdateTargetedCommsMappingRequest{
		TargetedCommsMapping: &tcPb.InAppTargetedCommsMapping{
			Id:        mapping.GetId(),
			ElementId: req.GetElementId(),
			MappingDetails: &tcPb.MappingDetails{
				MappingType: tcPb.MappingType_MAPPING_TYPE_USER,
				MappedValue: req.GetActorId(),
			},
			LastCallbackTime: timestamp.New(time.Now()),
		},
		TargetedCommsFieldMask: updateMask,
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		logger.Error(ctx, "got unexpected response from UpdateTargetedCommsMapping rpc", zap.Error(grpcErr))
		return &dePb.DynamicElementCallbackResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	logger.Debug(ctx, fmt.Sprintf("successfully updated the target comms mapping %v", req.GetElementId()))
	return &dePb.DynamicElementCallbackResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func getCommsElementByElementId(mappingList []*tcPb.InAppTargetedCommsMapping, elementId string) *tcPb.InAppTargetedCommsMapping {
	for _, mapping := range mappingList {
		if mapping.GetElementId() == elementId {
			return mapping
		}
	}

	return nil
}

func getDynamicElements(ctx context.Context, screenSection dePb.HomeScreenAdditionalInfo_Section, cardProgramType typesPb.CardProgramType,
	cardProgram *typesPb.CardProgram) ([]*dePb.DynamicElement, error) {
	switch screenSection {
	case dePb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY:
		switch cardProgramType {
		case typesPb.CardProgramType_CARD_PROGRAM_TYPE_SECURED:
			return getPrimaryDynamicElementsForSecured(cardProgram), nil
		case typesPb.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:
			return getPrimaryDynamicElementsForUnSecured(cardProgram), nil
		case typesPb.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
			return getPrimaryDynamicElementsForMassUnsecured(ctx, cardProgram), nil
		default:
			return nil, fmt.Errorf("invalid card program type to fetch dynamic elememts content: %s", cardProgramType.String())
		}
	case dePb.HomeScreenAdditionalInfo_SECTION_FEATURE_SECONDARY:
		switch cardProgramType {
		case typesPb.CardProgramType_CARD_PROGRAM_TYPE_SECURED:
			return getSecondaryDynamicElementsForSecured(cardProgram), nil
		case typesPb.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:
			return getSecondaryDynamicElementsForUnSecured(cardProgram), nil
		case typesPb.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
			return getSecondaryDynamicElementsForMassUnsecured(ctx, cardProgram), nil
		default:
			return nil, fmt.Errorf("error in fetching dynamic elements content for card program type: %s", cardProgramType.String())
		}
	default:
		return nil, fmt.Errorf("error in fetching dynamic elements content for screen section %s", screenSection.String())
	}
}

func getPrimaryDynamicElementsForSecured(cardProgram *typesPb.CardProgram) []*dePb.DynamicElement {
	return []*dePb.DynamicElement{
		{
			OwnerService:  typesPb.ServiceName_FIREFLY_SERVICE,
			UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
			StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS,
			Content: &dePb.ElementContent{
				Content: &dePb.ElementContent_FeatureWidgetWithThreePoints{
					FeatureWidgetWithThreePoints: &dePb.FeatureWidgetWithThreePoints{
						Title:       commontypes.GetTextFromStringFontColourFontStyle("Credit Card", "#313234", commontypes.FontStyle_HEADLINE_M),
						BorderColor: homeFePb.GetHomeWidgetBorderColor(),
						LeftVerticalFlyer: &dePb.FeatureWidgetWithThreePoints_LeftVerticalFlyer{
							VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/simplifi-3points-widget-icon.png"),
							Cta: &ui.IconTextComponent{
								Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Get this credit card >", "#00B899", commontypes.FontStyle_BUTTON_S)},
								Deeplink:            getLandingDeeplink(cardProgram),
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: "#E6F7F3"},
							},
						},
						RightHorizontalFlyers: []*dePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer{
							{
								PreText:   commontypes.GetTextFromStringFontColourFontStyle("Every weekend", "#929599", commontypes.FontStyle_HEADLINE_S),
								Text:      commontypes.GetTextFromStringFontColourFontStyle("Get 20% off", "#313234", commontypes.FontStyle_NUMBER_M),
								RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/3point-widget-offer-icon.png"),
								BgColour:  ui.GetBlockColor("#FFFFFF"),
								Deeplink:  getLandingDeeplink(cardProgram),
							},
							{
								PreText:   commontypes.GetTextFromStringFontColourFontStyle("Fee", "#929599", commontypes.FontStyle_HEADLINE_S),
								Text:      commontypes.GetTextFromStringFontColourFontStyle("Lifetime free", "#313234", commontypes.FontStyle_NUMBER_M),
								RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/3points-widget-interest-icon.png"),
								BgColour:  ui.GetBlockColor("#FFFFFF"),
								Deeplink:  getLandingDeeplink(cardProgram),
							},
							{
								PreText:   commontypes.GetTextFromStringFontColourFontStyle("Credit check", "#929599", commontypes.FontStyle_HEADLINE_S),
								Text:      commontypes.GetTextFromStringFontColourFontStyle("Not required", "#313234", commontypes.FontStyle_NUMBER_M),
								RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/3points-widget-timer-icon.png"),
								BgColour:  ui.GetBlockColor("#FFFFFF"),
								Deeplink:  getLandingDeeplink(cardProgram),
							},
						},
					},
				},
			},
			BizAnalyticsData: map[string]string{
				dePb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "Simplifi Credit Card",
			},
		},
	}
}

func getPrimaryDynamicElementsForUnSecured(cardProgram *typesPb.CardProgram) []*dePb.DynamicElement {
	return []*dePb.DynamicElement{
		{
			OwnerService:  typesPb.ServiceName_FIREFLY_SERVICE,
			UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
			StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS,
			Content: &dePb.ElementContent{
				Content: &dePb.ElementContent_FeatureWidgetWithThreePoints{
					FeatureWidgetWithThreePoints: &dePb.FeatureWidgetWithThreePoints{
						Title:       commontypes.GetTextFromStringFontColourFontStyle("Premiere credit card", "#313234", commontypes.FontStyle_HEADLINE_M),
						BorderColor: homeFePb.GetHomeWidgetBorderColor(),
						LeftVerticalFlyer: &dePb.FeatureWidgetWithThreePoints_LeftVerticalFlyer{
							VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/new-apmlifi-3points-widget-img.png"),
							Cta: &ui.IconTextComponent{
								Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Get the card >", "#00B899", commontypes.FontStyle_BUTTON_S)},
								Deeplink:            getLandingDeeplink(cardProgram),
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: "#E6F7F3"},
							},
						},
						RightHorizontalFlyers: []*dePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer{
							{
								PreText:   commontypes.GetTextFromStringFontColourFontStyle("Get", "#929599", commontypes.FontStyle_HEADLINE_S),
								Text:      commontypes.GetTextFromStringFontColourFontStyle("3% back", "#313234", commontypes.FontStyle_NUMBER_M),
								RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/star-icon.png"),
								BgColour:  ui.GetBlockColor("#FFFFFF"),
								Deeplink:  getLandingDeeplink(cardProgram),
							},
							{
								PreText:   commontypes.GetTextFromStringFontColourFontStyle("Enjoy", "#929599", commontypes.FontStyle_HEADLINE_S),
								Text:      commontypes.GetTextFromStringFontColourFontStyle("0 Forex", "#313234", commontypes.FontStyle_NUMBER_M),
								RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/globe-icon.png"),
								BgColour:  ui.GetBlockColor("#FFFFFF"),
								Deeplink:  getLandingDeeplink(cardProgram),
							},
							{
								PreText:   commontypes.GetTextFromStringFontColourFontStyle("Gift cards", "#929599", commontypes.FontStyle_HEADLINE_S),
								Text:      commontypes.GetTextFromStringFontColourFontStyle("₹4,250", "#313234", commontypes.FontStyle_NUMBER_M),
								RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/gift-icon.png"),
								BgColour:  ui.GetBlockColor("#FFFFFF"),
								Deeplink:  getLandingDeeplink(cardProgram),
							},
						},
					},
				},
			},
			BizAnalyticsData: map[string]string{
				dePb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "Amplifi Credit Card",
			},
		},
	}
}

func getPrimaryDynamicElementsForMassUnsecured(ctx context.Context, cardProgram *typesPb.CardProgram) []*dePb.DynamicElement {
	leftVerticalFlyerVisualElement := getLeftVerticalFlyerVisualElement(ctx)
	return []*dePb.DynamicElement{
		{
			OwnerService:  typesPb.ServiceName_FIREFLY_SERVICE,
			UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
			StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS,
			Content: &dePb.ElementContent{
				Content: &dePb.ElementContent_FeatureWidgetWithThreePoints{
					FeatureWidgetWithThreePoints: &dePb.FeatureWidgetWithThreePoints{
						Title:       commontypes.GetTextFromStringFontColourFontStyle("Credit Card", "#313234", commontypes.FontStyle_HEADLINE_M),
						BorderColor: homeFePb.GetHomeWidgetBorderColor(),
						LeftVerticalFlyer: &dePb.FeatureWidgetWithThreePoints_LeftVerticalFlyer{
							VisualElement: leftVerticalFlyerVisualElement,
							Cta: &ui.IconTextComponent{
								Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Get this credit card >", "#F0DCB6", commontypes.FontStyle_BUTTON_S)},
								Deeplink:            getLandingDeeplink(cardProgram),
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: "#6A3D22"},
							},
							BgColour: ui.GetRadialGradient([]string{"#22100A", "#3D2501"}),
						},
						RightHorizontalFlyers: []*dePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer{
							{
								PreText:   commontypes.GetTextFromStringFontColourFontStyle("Every weekend", "#929599", commontypes.FontStyle_HEADLINE_S),
								Text:      commontypes.GetTextFromStringFontColourFontStyle("Get 20% off", "#313234", commontypes.FontStyle_NUMBER_M),
								RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/magnifi-widget-offer-benefit-icon.png"),
								BgColour:  ui.GetBlockColor("#FFFFFF"),
								Deeplink:  getLandingDeeplink(cardProgram),
							},
							{
								PreText:   commontypes.GetTextFromStringFontColourFontStyle("Fee", "#929599", commontypes.FontStyle_HEADLINE_S),
								Text:      commontypes.GetTextFromStringFontColourFontStyle("Lifetime free", "#313234", commontypes.FontStyle_NUMBER_M),
								RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/magnifi-widget-interest-benefits-icon.png"),
								BgColour:  ui.GetBlockColor("#FFFFFF"),
								Deeplink:  getLandingDeeplink(cardProgram),
							},
							{
								PreText:   commontypes.GetTextFromStringFontColourFontStyle("Save", "#929599", commontypes.FontStyle_HEADLINE_S),
								Text:      commontypes.GetTextFromStringFontColourFontStyle("₹1,000/month", "#313234", commontypes.FontStyle_NUMBER_M),
								RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/magnifi-widget-timer-benefits-icon.png"),
								BgColour:  ui.GetBlockColor("#FFFFFF"),
								Deeplink:  getLandingDeeplink(cardProgram),
							},
						},
					},
				},
			},
			BizAnalyticsData: map[string]string{
				dePb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "Magnifi Credit Card",
			},
		},
	}
}

func getLeftVerticalFlyerVisualElement(ctx context.Context) *commontypes.VisualElement {
	platform, _ := epificontext.AppPlatformAndVersion(ctx)
	if platform == commontypes.Platform_IOS {
		return commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/magnifleftflyerVE_4x.png")
	} else {
		return commontypes.GetVisualElementLottieFromUrl("https://epifi-icons.pointz.in/credit_card/meet-magnifi.json")
	}
}

func getSecondaryDynamicElementsForSecured(cardProgram *typesPb.CardProgram) []*dePb.DynamicElement {
	return []*dePb.DynamicElement{
		getPrimaryDynamicElementsForSecured(cardProgram)[0],
		{
			OwnerService:  typesPb.ServiceName_FIREFLY_SERVICE,
			UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
			StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
			Content: &dePb.ElementContent{
				Content: &dePb.ElementContent_FeatureWidgetWithFourPoints{
					FeatureWidgetWithFourPoints: &dePb.FeatureWidgetWithFourPoints{
						Title:       commontypes.GetTextFromStringFontColourFontStyle("Featured", "#313234", commontypes.FontStyle_HEADLINE_M),
						BorderColor: homeFePb.GetHomeWidgetBorderColor(),
						Card: &dePb.FeatureWidgetWithFourPoints_TextVisualElementCard_{
							TextVisualElementCard: &dePb.FeatureWidgetWithFourPoints_TextVisualElementCard{
								TopSection: &dePb.FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection{
									VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/simplifi-4points-widget-icon.png"),
								},
								MiddleSection: &dePb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection{
									HighlightedPoints: []*dePb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint{
										{
											LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/small_offer_green.png"),
											PreText:  commontypes.GetTextFromStringFontColourFontStyle("On weekends", "#929599", commontypes.FontStyle_HEADLINE_S),
											Text:     commontypes.GetTextFromStringFontColourFontStyle("20% off", "#313234", commontypes.FontStyle_NUMBER_M),
										},
										{
											LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/small_heart_green.png"),
											PreText:  commontypes.GetTextFromStringFontColourFontStyle("Save", "#929599", commontypes.FontStyle_HEADLINE_S),
											Text:     commontypes.GetTextFromStringFontColourFontStyle("₹450/month", "#313234", commontypes.FontStyle_NUMBER_M),
										},
										{
											LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/small_check_green.png"),
											PreText:  commontypes.GetTextFromStringFontColourFontStyle("Fee", "#929599", commontypes.FontStyle_HEADLINE_S),
											Text:     commontypes.GetTextFromStringFontColourFontStyle("Lifetime free", "#313234", commontypes.FontStyle_NUMBER_M),
										},
										{
											LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/small_legal_doc_green.png"),
											PreText:  commontypes.GetTextFromStringFontColourFontStyle("Credit check", "#929599", commontypes.FontStyle_HEADLINE_S),
											Text:     commontypes.GetTextFromStringFontColourFontStyle("Not required", "#313234", commontypes.FontStyle_NUMBER_M),
										},
									},
								},
								Cta: &ui.IconTextComponent{
									Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Get the card >", "#00B899", commontypes.FontStyle_BUTTON_S)},
									Deeplink:            getLandingDeeplink(cardProgram),
									ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: "#E6F7F3"},
								},
							},
						},
						IsCarouselVariant: true,
					},
				},
			},
			BizAnalyticsData: map[string]string{
				dePb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "Simplifi Credit Card",
			},
		},
	}
}

func getSecondaryDynamicElementsForUnSecured(cardProgram *typesPb.CardProgram) []*dePb.DynamicElement {
	return []*dePb.DynamicElement{
		// in case of secondary section feature we'll send one primary and one carousel widget with IsCarouselVariant as true
		getPrimaryDynamicElementsForUnSecured(cardProgram)[0],
		{
			OwnerService:  typesPb.ServiceName_FIREFLY_SERVICE,
			UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
			StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
			Content: &dePb.ElementContent{
				Content: &dePb.ElementContent_FeatureWidgetWithFourPoints{
					FeatureWidgetWithFourPoints: &dePb.FeatureWidgetWithFourPoints{
						Title:       commontypes.GetTextFromStringFontColourFontStyle("Featured", "#313234", commontypes.FontStyle_HEADLINE_M),
						BorderColor: homeFePb.GetHomeWidgetBorderColor(),
						Card: &dePb.FeatureWidgetWithFourPoints_TextVisualElementCard_{
							TextVisualElementCard: &dePb.FeatureWidgetWithFourPoints_TextVisualElementCard{
								TopSection: &dePb.FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection{
									VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/aplifi-4points-widget-icon.png"),
								},
								MiddleSection: &dePb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection{
									HighlightedPoints: []*dePb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint{
										{
											LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/star-featured-icon.png"),
											PreText:  commontypes.GetTextFromStringFontColourFontStyle("Get", "#929599", commontypes.FontStyle_HEADLINE_S),
											Text:     commontypes.GetTextFromStringFontColourFontStyle("3% back", "#313234", commontypes.FontStyle_NUMBER_M),
										},
										{
											LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/globe-featured.png"),
											PreText:  commontypes.GetTextFromStringFontColourFontStyle("Enjoy", "#929599", commontypes.FontStyle_HEADLINE_S),
											Text:     commontypes.GetTextFromStringFontColourFontStyle("0 forex fees", "#313234", commontypes.FontStyle_NUMBER_M),
										},
										{
											LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/gift-featured.png"),
											PreText:  commontypes.GetTextFromStringFontColourFontStyle("Gift cards", "#929599", commontypes.FontStyle_HEADLINE_S),
											Text:     commontypes.GetTextFromStringFontColourFontStyle("₹4,250", "#313234", commontypes.FontStyle_NUMBER_M),
										},
										{
											LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/lounge-featured.png"),
											PreText:  commontypes.GetTextFromStringFontColourFontStyle("Lounge access", "#929599", commontypes.FontStyle_HEADLINE_S),
											Text:     commontypes.GetTextFromStringFontColourFontStyle("4 times/year", "#313234", commontypes.FontStyle_NUMBER_M),
										},
									},
								},
								Cta: &ui.IconTextComponent{
									Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Get the card >", "#00B899", commontypes.FontStyle_BUTTON_S)},
									Deeplink:            getLandingDeeplink(cardProgram),
									ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: "#E6F7F3"},
								},
							},
						},
						IsCarouselVariant: true,
					},
				},
			},
			BizAnalyticsData: map[string]string{
				dePb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "Amplifi Credit Card",
			},
		},
	}
}

func getSecondaryDynamicElementsForMassUnsecured(ctx context.Context, cardProgram *typesPb.CardProgram) []*dePb.DynamicElement {
	return []*dePb.DynamicElement{
		getPrimaryDynamicElementsForMassUnsecured(ctx, cardProgram)[0],
		{
			OwnerService:  typesPb.ServiceName_FIREFLY_SERVICE,
			UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
			StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
			Content: &dePb.ElementContent{
				Content: &dePb.ElementContent_FeatureWidgetWithFourPoints{
					FeatureWidgetWithFourPoints: &dePb.FeatureWidgetWithFourPoints{
						Title:       commontypes.GetTextFromStringFontColourFontStyle("Featured", "#313234", commontypes.FontStyle_HEADLINE_M),
						BorderColor: homeFePb.GetHomeWidgetBorderColor(),
						Card: &dePb.FeatureWidgetWithFourPoints_TextVisualElementCard_{
							TextVisualElementCard: &dePb.FeatureWidgetWithFourPoints_TextVisualElementCard{
								TopSection: &dePb.FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection{
									VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/4item_magnifi_sec.png"),
								},
								MiddleSection: &dePb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection{
									HighlightedPoints: []*dePb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint{
										{
											LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/small_offer_gloden_brown.png"),
											PreText:  commontypes.GetTextFromStringFontColourFontStyle("On weekends", "#929599", commontypes.FontStyle_HEADLINE_S),
											Text:     commontypes.GetTextFromStringFontColourFontStyle("20% off", "#313234", commontypes.FontStyle_NUMBER_M),
										},
										{
											LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/small_heart_gloden_brown.png"),
											PreText:  commontypes.GetTextFromStringFontColourFontStyle("Save", "#929599", commontypes.FontStyle_HEADLINE_S),
											Text:     commontypes.GetTextFromStringFontColourFontStyle("₹1,000/month", "#313234", commontypes.FontStyle_NUMBER_M),
										},
										{
											LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/small_check_gloden_brown.png"),
											PreText:  commontypes.GetTextFromStringFontColourFontStyle("Fee", "#929599", commontypes.FontStyle_HEADLINE_S),
											Text:     commontypes.GetTextFromStringFontColourFontStyle("Lifetime free", "#313234", commontypes.FontStyle_NUMBER_M),
										},
										{
											LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card/small_Fi_coins_curr_gloden_brown.png"),
											PreText:  commontypes.GetTextFromStringFontColourFontStyle("Rewards", "#929599", commontypes.FontStyle_HEADLINE_S),
											Text:     commontypes.GetTextFromStringFontColourFontStyle("4x Fi-Coins", "#313234", commontypes.FontStyle_NUMBER_M),
										},
									},
								},
								Cta: &ui.IconTextComponent{
									Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Get the card >", "#98712F", commontypes.FontStyle_BUTTON_S)},
									Deeplink:            getLandingDeeplink(cardProgram),
									ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: "#FAF0E0B2"},
								},
							},
						},
						IsCarouselVariant: true,
					},
				},
			},
			BizAnalyticsData: map[string]string{
				dePb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "Magnifi Credit Card",
			},
		},
	}
}

func getDynamicElementContent(cardProgramType typesPb.CardProgramType, cardProgram *typesPb.CardProgram) (*dePb.ElementContent, error) {
	switch cardProgramType {
	case typesPb.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:
		return getUnsecuredDynamicElementContent(cardProgram), nil
	case typesPb.CardProgramType_CARD_PROGRAM_TYPE_SECURED:
		return getSecuredDynamicElementContent(), nil
	case typesPb.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
		return getMassUnsecuredDynamicElementContent(cardProgram), nil
	default:
		return nil, fmt.Errorf("error in fetching dynamic elements content for card program type %s", cardProgramType.String())
	}
}

func getMassUnsecuredDynamicElementContent(cardProgram *typesPb.CardProgram) *dePb.ElementContent {
	return &dePb.ElementContent{
		Content: &dePb.ElementContent_ScrollableBanner{
			ScrollableBanner: &dePb.ScrollableBannerElementContent{
				Header: &dePb.BannerHeader{
					Title: []*commontypes.Text{
						{
							FontColor: white,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "Get the",
							},
							FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							FontColorOpacity: 60,
						},
						{
							FontColor: white,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "MagniFi\nCredit Card",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_L},
						},
					},
					Cta: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							{
								FontColor: white,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Know more >",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
							},
						},
						Deeplink: getLandingDeeplink(cardProgram),
					},
				},
				ScrollingElements: []*dePb.BannerSingleShapeElement{
					{
						Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_1,
						Image: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/credit_card/zomato-icon.png",
						},
						Title:    commontypes.GetTextFromStringFontColourFontStyle("20% off on\nweekends", "", commontypes.FontStyle_SUBTITLE_S),
						BgColour: widget.GetBlockBackgroundColour("#2D170E"),
						Shadow: []*widget.Shadow{
							{
								Height: 4,
								Colour: widget.GetBlockBackgroundColour("#201207"),
							},
						},
						Deeplink: getLandingDeeplink(cardProgram),
					},
					{
						Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_3,
						Image: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/credit_card/check-icon.png",
						},
						Title:    commontypes.GetTextFromStringFontColourFontStyle("Lifetime\nfree", "", commontypes.FontStyle_SUBTITLE_S),
						BgColour: widget.GetBlockBackgroundColour("#2D170E"),
						Shadow: []*widget.Shadow{
							{
								Height: 4,
								Colour: widget.GetBlockBackgroundColour("#201207"),
							},
						},
						Deeplink: getLandingDeeplink(cardProgram),
					},
					{
						Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_2,
						Image: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/credit_card/rupee-icon.png",
						},
						Title:    commontypes.GetTextFromStringFontColourFontStyle("Save ₹1,000\nmonthly", "", commontypes.FontStyle_SUBTITLE_S),
						BgColour: widget.GetBlockBackgroundColour("#2D170E"),
						Shadow: []*widget.Shadow{
							{
								Height: 4,
								Colour: widget.GetBlockBackgroundColour("#201207"),
							},
						},
						Deeplink: getLandingDeeplink(cardProgram),
					},
				},
				BgColour: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_RadialGradient{
						RadialGradient: &widget.RadialGradient{
							Colours: []string{"#5B361F", "#422516", "#23110B"},
						},
					},
				},
			},
		},
	}
}

func getUnsecuredDynamicElementContent(cardProgram *typesPb.CardProgram) *dePb.ElementContent {
	return &dePb.ElementContent{
		Content: &dePb.ElementContent_ScrollableBanner{
			ScrollableBanner: &dePb.ScrollableBannerElementContent{
				Header: &dePb.BannerHeader{
					Title: []*commontypes.Text{
						{
							FontColor: white,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "Get the",
							},
							FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							FontColorOpacity: 60,
						},
						{
							FontColor: white,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "AmpliFi\nCredit Card",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_L},
						},
					},
					Cta: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							{
								FontColor: white,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Know more >",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
							},
						},
						Deeplink: getLandingDeeplink(cardProgram),
					},
				},
				ScrollingElements: []*dePb.BannerSingleShapeElement{
					{
						Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_1,
						Image: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/credit_card_images/3_percent_Dynamic_Element_Home_Image.png",
						},
						Title: &commontypes.Text{

							FontColor: mediumGrey,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "returns on\n20+ brands",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
						BgColour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: graphiteBlack,
							},
						},
						Deeplink: getLandingDeeplink(cardProgram),
					},
					{
						Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_2,
						Image: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/credit_card_images/0_percent_dynamic_element_home.png",
						},
						Title: &commontypes.Text{

							FontColor: mediumGrey,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "forex\ncharges",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
						BgColour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: graphiteBlack,
							},
						},
						Deeplink: getLandingDeeplink(cardProgram),
					},
					{
						Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_3,
						Image: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/credit_card_images/Launge_access_dynamic_element_home.png",
						},
						Title: &commontypes.Text{

							FontColor: mediumGrey,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "lounge access\n& more",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
						BgColour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: graphiteBlack,
							},
						},
						Deeplink: getLandingDeeplink(cardProgram),
					},
				},
				BgColour: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_RadialGradient{
						RadialGradient: &widget.RadialGradient{
							Colours: []string{algaeGreen, IDBBBlack},
						},
					},
				},
			},
		},
	}
}

func getSecuredDynamicElementContent() *dePb.ElementContent {
	return &dePb.ElementContent{
		Content: &dePb.ElementContent_ScrollableBanner{
			ScrollableBanner: &dePb.ScrollableBannerElementContent{
				Header: &dePb.BannerHeader{
					Title: []*commontypes.Text{
						{
							FontColor: oceanRipple,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "Get the",
							},
							FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							FontColorOpacity: 60,
						},
						{
							FontColor: white,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "SimpliFi\nCredit Card",
							},
							FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_L},
							FontColorOpacity: 100,
						},
					},
					Cta: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							{
								FontColor: white,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Know more >",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
							},
						},
						Deeplink: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_CREDIT_CARD_LANDING_SCREEN,
						},
					},
				},
				ScrollingElements: []*dePb.BannerSingleShapeElement{
					{
						Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_1,
						Image: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/credit_card_images/home_widget_swiggy.png",
						},
						Title: &commontypes.Text{

							FontColor: white,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "20% off on \nweekends",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
						BgColour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: seaGreen,
							},
						},
						Deeplink: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_CREDIT_CARD_LANDING_SCREEN,
						},
					},
					{
						Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_3,
						Image: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/credit_card_images/home_widget_tick.png",
						},
						Title: &commontypes.Text{

							FontColor: white,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "LifeTime \nfree",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
						BgColour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: seaGreen,
							},
						},
						Deeplink: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_CREDIT_CARD_LANDING_SCREEN,
						},
					},
					{
						Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_2,
						Image: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/credit_card_images/home_widget_no_check.png",
						},
						Title: &commontypes.Text{

							FontColor: white,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "No credit \ncheck",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
						BgColour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: seaGreen,
							},
						},
						Deeplink: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_CREDIT_CARD_LANDING_SCREEN,
						},
					},
				},
				BgColour: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_RadialGradient{
						RadialGradient: &widget.RadialGradient{
							Colours: []string{hazel, lochinvar},
						},
					},
				},
			},
		},
	}
}

func getLandingDeeplink(cardProgram *typesPb.CardProgram) *deeplinkPb.Deeplink {
	cardProgramString := ffPkg.GetCardProgramStringFromCardProgram(cardProgram)
	attributesMap := make(map[string]string)
	attributesMap[card_program_attribute_key] = cardProgramString
	landingInfoDeeplink := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CREDIT_CARD_LANDING_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_CreditCardLandingScreenOptions_{
			CreditCardLandingScreenOptions: &deeplinkPb.Deeplink_CreditCardLandingScreenOptions{
				CreditCardRequestHeader: &typesPb.CreditCardRequestHeader{
					Attributes: attributesMap,
				},
			},
		},
	}
	return landingInfoDeeplink
}
