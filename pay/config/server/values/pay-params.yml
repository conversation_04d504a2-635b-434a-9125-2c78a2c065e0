AWS:
  Region: "ap-south-1"

RazorPayResponseCodesJson: "pkg/pay/pgerrorcodes/razorpayErrorResponseCodes.json"

FundTransferParams:
  HardPreferredPaymentProtocol: 3 # IMPS
  DefaultFundTransferExpiryDuration: "10m"
  SMSTypeToOptionVersionMap:
    GenericPiDebit:
      Default: "V1"
    NeftDebit:
      Default: "V1"
    RtgsDebit:
      Default: "V1"
    TransactionReversed:
      Default: "V1"
  PaymentNotificationParams:
    HistoricPaymentSupportedTill: "2h"
    Debit:
      Title: "Money sent"
      Body: "Your transaction for %s has gone through! Tap to view."
      IconAttr:
        IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
      NotificationExpiry: "1h"
      NotificationType: "SystemTray"
      TriggerAfter: "30s"
    TransactionFailed:
      Title: "Transaction failed"
      Body: "Sorry, we couldn't send %s to %s. Tap to retry."
      IconAttr:
        IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
      NotificationExpiry: "1h"
      NotificationType: "SystemTray"
      TriggerAfter: "30s"
    TransactionReversed:
      Title: "Payment reversed"
      Body: "Heads up! The %s transferred to %s is now back in your account."
      IconAttr:
        IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
      NotificationExpiry: "1h"
      NotificationType: "SystemTray"

PaymentEnquiryParams:
  NotFoundMaxRetryDurationVendorMap:
    "FEDERAL_BANK":
      IntraBank: "3m"
      UPI: "5m"
      NEFT: "3m"
      RTGS: "3m"
      IMPS: "5m"
  InProgressToSuccessMap: # time duration and error codes for in progress response status will be move to success
    "FEDERAL_BANK":
      FiErrorCodes:
        - "FI304"
      PaymentProtocolToDurationMap:
        "NEFT": "120m"
        "RTGS": "120m"

ExecutionReportGenerationParams:
  ReportStalenessDuration: "0.1s"

InternationalFundTransfer:
  EnableFederalSherlock: true
  EnableLRSCheckFromVendor: true
  EnableUpdateLrsLimitsWorkflowFlag: true
  EnableSofStateColumnInSwiftFileFlag: true
  GenerateLrsCheckFileUsingCutOffTime: true
  IsSofBasedRemittanceLimitCheckEnabled: true
  ForexRateVendorAPIMaximumConsumptionAmountInUSD: ******** # 2cr USD or 20 million USD
  SpecialInstructionForUSStocks: "FFC EPFY-9064696IN"
  SkipPreCheckForSwiftFileGen: false
  DurationWaitForSwiftFileGen: "5m"
  TransactionAmountConstraint:
    IsEnabled: true
    MaxTransactionAmountToCreditAmountPercentage: 90
    MaxTransactionAmountToCurrentBalancePercentage: 80
    MaxPermissibleAmount:
      CurrencyCode: "INR"
      # Should be more than the USD limit US stocks side
      Units: 10_00_000
  PoolInwardAccountPI: "paymentinstrument-us-stock-inward-account"
  UsStocksVendorPI: "paymentinstrument-alpaca-international-account"
  SherlockPath: "/api/v1/us-stocks/download-file"
  MaxLRSPageSize: 100
  ForexRate:
    AllowVaryingRatesFromForexAPI: false
    USD:
      CurrencyCode: "INR"
      Units: 81
      Nanos: *********
    # Note: Modify with caution. A high refresh rate can result in a lot of events
    # in a workflow leading to its termination.
    ForexRateRefreshInterval: "30m" # 30 minutes
    ForexRateAlertThresholdsInPercent:
      - 70
      - 80
      - 90
      - 100
  IgstAccountNumber: "**************"
  IgstAccountSolId: "5555"
  CgstAccountNumber: "**************"
  CgstAccountSolId: "5555"
  SgstAccountNumber: "**************"
  SgstAccountSolId: "5555"
  TCSAccountNumber: "**************"
  TCSAccountSolId: "5555"
  OutwardPoolAccountSolId: "5555"
  OutwardPoolAccount: "**************"
  ForexRateIdPrecision: 1000000
  NostroBankSwiftCode: "CITIUS33"
  GstReportingInfo:
    FilingOrganisation: "32AABCT0020H1Z5"
    SectionName: "B2C"
    InvoiceType: "R"
    InvoiceNumber: "FEDFPL001"
    Hsn: "997157"
    ReverseCharge: "N"
    IsSez: "N"
    ServiceProviderStateCode: "32" #it is represent in kerala state
    ServiceProviderSolId: "5555" # solid of epifi pool account
    CgstPercentage: 9.0
    MinTaxableGstAmount:
      CurrencyCode: "INR"
      Units: 250
    IgstPercentage: 18.0
    SgstPercentage: 9.0
    StateCode:
      "jammu & kashmir": "1"
      "jammu and kashmir": "1"
      "himachal pradesh": "2"
      "punjab": "3"
      "chandigarh": "4"
      "uttarakhand": "5" # vendor has Uttaranchal (Uttarakhand)
      "haryana": "6"
      "delhi": "7"
      "rajasthan": "8"
      "uttar pradesh": "9"
      "bihar": "10"
      "sikkim": "11"
      "arunachal pradesh": "12"
      "nagaland": "13"
      "manipur": "14"
      "mizoram": "15"
      "tripura": "16"
      "meghalaya": "17"
      "assam": "18"
      "west bengal": "19"
      "jharkhand": "20"
      "orissa": "21"
      "odisha": "21"
      "chattisgarh": "22"
      "chhattisgarh": "22"
      "madhya pradesh": "23"
      "gujarat": "24"
      "dadra & nagar haveli and daman & diu": "25" # in our system it is combine of state but while gst it is different mapping to "26"
      "the dadra and nagar haveli and daman and diu": "25"
      "maharashtra": "27"
      "andhra pradesh": "37"
      "karnataka": "29"
      "goa": "30"
      "lakshadweep": "31"
      "kerala": "32"
      "tamil nadu": "33"
      "pondicherry": "34"  # vendor has Puducherry (Pondicherry)
      "puducherry": "34"
      "andaman & nicobar": "35"
      "andaman and nicobar islands": "35"
      "telangana": "36"
      "ladakh": "38"
  AuthFactorUpdateCoolOffPeriod: "24h"

PinotIngestionDelay:
  DelayDuration: 24h

FundTransferCelestialParams:
  IsFundTransferViaCelestialRestricted: false
  Version: "V1"

VelocityRuleThresholdsMap:
  "USER_AMOUNT_LIMIT_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 1000000
  "USER_LEVEL_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "UPI_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "UPI_PIN_RESET_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 5000
  "UPI_DEVICE_REGISTRATION_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 5000
  "UPI_LIMIT_WINDOW_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 100000
    ThresholdCount: 20
  "NEFT_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "RTGS_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "INTRA_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "IMPS_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "IMPS_LIMIT_WINDOW_RULE":
    ThresholdCount: 5


VelocityRuleAmountRangeMap:
  "UPI_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 1
    MaxAmount: 100000
  "NEFT_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 1
    MaxAmount: 1000000
  "RTGS_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 200000
    MaxAmount: 1000000
  "IMPS_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 1
    MaxAmount: 500000
  "INTRA_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 1
    MaxAmount: 1000000



MinDurationRequiredForUserVintageCheck: "8544h" #365 days in hours
EnableSOFLogForDebugging: false

RulesToRuleGroupMap:
  "VELOCITY_RULE_GROUP_FOR_USER": [
    "USER_AMOUNT_LIMIT_RULE",
  ]
  "VELOCITY_RULE_GROUP_UPI_AFU_COOL_DOWN": [
    "UPI_AFU_COOL_DOWN_RULE",
  ]
  "VELOCITY_RULE_GROUP_NEFT_AFU_COOL_DOWN": [
    "NEFT_AFU_COOL_DOWN_RULE",
  ]
  "VELOCITY_RULE_GROUP_RTGS_AFU_COOL_DOWN": [
    "RTGS_AFU_COOL_DOWN_RULE",
  ]
  "VELOCITY_RULE_GROUP_INTRA_AFU_COOL_DOWN": [
    "INTRA_AFU_COOL_DOWN_RULE",
  ]
  "VELOCITY_RULE_GROUP_IMPS_AFU_COOL_DOWN": [
    "IMPS_AFU_COOL_DOWN_RULE",
  ]
  "VELOCITY_RULE_GROUP_NEFT_AMOUNT_STANDARD": [
    "NEFT_CHECK_AMOUNT_WITHIN_RANGE_RULE",
  ]
  "VELOCITY_RULE_GROUP_RTGS_AMOUNT_STANDARD": [
    "RTGS_CHECK_AMOUNT_WITHIN_RANGE_RULE",
  ]
  "VELOCITY_RULE_GROUP_INTRA_STANDARD": [
    "INTRA_CHECK_AMOUNT_WITHIN_RANGE_RULE",
  ]
  "VELOCITY_RULE_GROUP_IMPS_STANDARD_RULES": [
    "IMPS_CHECK_AMOUNT_WITHIN_RANGE_RULE",
    "IMPS_LIMIT_WINDOW_RULE",
  ]
  "VELOCITY_RULE_GROUP_USER_LEVEL_AFU_COOL_DOWN": [
    "USER_LEVEL_AFU_COOL_DOWN_RULE",
  ]
  "VELOCITY_RULE_GROUP_UPI_PIN_RESET_COOl_DOWN": [
    "UPI_PIN_RESET_COOL_DOWN_RULE",
  ]
  "VELOCITY_RULE_GROUP_UPI_DEVICE_REGISTRATION_COOL_DOWN": [
    "UPI_DEVICE_REGISTRATION_COOL_DOWN_RULE",
  ]
  "VELOCITY_RULE_GROUP_UPI_STANDARD_RULES": [
    "UPI_CHECK_AMOUNT_WITHIN_RANGE_RULE",
    "UPI_LIMIT_WINDOW_RULE",
  ]

VendorToNameMap:
  FEDERAL_BANK: "Federal Savings A/c"

FeatureReleaseConfig:
  FeatureConstraints:
    - "PAY_INCIDENT_MANAGER_UPI_PIN_FLOW_ERROR":
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_INCIDENT_MANAGER_P2P_DEEMED_TRANSACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 10
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_INCIDENT_MANAGER_P2M_DEEMED_TRANSACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 10
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FUND_TRANSFER_V1:
        AppVersionConstraintConfig:
          MinAndroidVersion: 250
          MinIOSVersion: 345
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_FAILED_TRANSACTION_CATCH_ALL_ON_APP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP:
        # most of the Transactions which comes under this either go into DEEMED or go into DEBITED_AND_FAILED_CATCH_ALL within 5 mins (even sooner though).
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_INCIDENT_MANAGER_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP:
        # Disabling because we are rarely having any transaction which stays in IN_PROGRESS state for more than threshold(5 mins)
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_ENRICH_ORDER_AND_TXN_FROM_DC_SWITCH:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_INCIDENT_MANAGER_CHEQUE_CREDIT_TRANSACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_INCIDENT_MANAGER_ONBOARD_ADD_FUNDS_SECOND_LEG_FAILURE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_QR_SCAN_ENHANCEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 9999
          MinIOSVersion: 9999
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL

PayIncidentManager:
  UpiPinFlowError:
    ErrorCodeIncidentMap:
      "Z6": "PIN_RETRIES_EXCEEDED"
    IncidentProductCategoryMap:
      "PIN_RETRIES_EXCEEDED":
        ProductCategory: "PRODUCT_CATEGORY_TRANSACTIONS"
        ProductCategoryDetails: "PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT"
        SubCategory: "SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UPI_PIN_TRIES_EXCEEDED"
        # In-App Transactions | Unable to transact | UPI PIN tries exceeded
        PaymentProtocolToIssueCategoryIdMap:
          "UPI": "8a2d37b2-c4ab-5449-967a-87a4dd698404"
    IncidentNotificationTemplateMap:
      "PIN_RETRIES_EXCEEDED":
        IncidentCreationTemplate:
          Title: "⚠️ UPI PIN reset required"
          Body: "We've identified an issue (Ticket No. %s). Please reset your UPI PIN to continue with Transactions on Fi App"
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
        IncidentResolutionTemplate:
          Title: " ✅ UPI PIN issue resolved"
          Body: "Yay, Looks like your UPI PIN issue (Ticket No. %s) has been resolved. Have more questions? Feel free to reach out to us."
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
    IncidentTicketConfigMap:
      "PIN_RETRIES_EXCEEDED":
        TicketDescription: "https://sherlock.epifi.in/v2/scripts/transactions-upi-unable-to-transact-upi-pin-tries-exceeded-upi"
  DefaultTransactionHandler:
    ErrorCodeIncidentMap:
      # TODO(Shubham): Confirm the error code incident type mapping and update
      "U37": "REVERSED_FAILED_TRANSACTION"
    IncidentProductCategoryMap:
      "P2P_DEEMED_TRANSACTION":
        ProductCategory: "PRODUCT_CATEGORY_TRANSACTIONS"
        ProductCategoryDetails: "PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP"
        SubCategory: "SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY"
        # In-App Transactions | Amount debited but not credited to beneficiary | UPI - Pending
        PaymentProtocolToIssueCategoryIdMap:
          "UPI": "0388c59c-1417-5f90-98dc-40965ef73ebd"
      "P2M_DEEMED_TRANSACTION":
        ProductCategory: "PRODUCT_CATEGORY_TRANSACTIONS"
        ProductCategoryDetails: "PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP"
        SubCategory: "SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT"
        # In-App Transactions | Amount debited but not credited to merchant | UPI - Pending
        PaymentProtocolToIssueCategoryIdMap:
          "UPI": "0d1212df-29ae-5cb0-9910-b890648cf587"
      "P2P_DEEMED_TRANSACTION_OFF_APP":
        ProductCategory: "PRODUCT_CATEGORY_TRANSACTIONS"
        ProductCategoryDetails: "PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_OFF_APP"
        SubCategory: "SUB_CATEGORY_TRANSACTIONS_DEBITED_OFF_APP_BUT_NOT_CREDITED_TO_BENEFICIARY"
        # Off-App Transactions | Amount debited but not credited to beneficiary | UPI - Pending
        PaymentProtocolToIssueCategoryIdMap:
          "UPI": "c88334d7-30a4-5123-8702-4811df940189"
      "P2M_DEEMED_TRANSACTION_OFF_APP":
        ProductCategory: "PRODUCT_CATEGORY_TRANSACTIONS"
        ProductCategoryDetails: "PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_OFF_APP"
        SubCategory: "SUB_CATEGORY_TRANSACTIONS_DEBITED_OFF_APP_BUT_NOT_CREDITED_TO_MERCHANT"
        # Off-App Transactions | Amount debited but not credited to merchant | UPI - Pending
        PaymentProtocolToIssueCategoryIdMap:
          "UPI": "c1054e1b-9d7e-50de-837c-58d55a295174"
      "REVERSED_FAILED_TRANSACTION":
        ProductCategory: "PRODUCT_CATEGORY_TRANSACTIONS"
        ProductCategoryDetails: "PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP"
        SubCategory: "SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT"
        # In-App Transactions | Transaction Failed | Reversal Pending
        # TODO (Shubham): Confirm IssueCategory from product
        PaymentProtocolToIssueCategoryIdMap:
          "UPI": "8476ab49-bad2-5281-8b5b-168e5821b09f"
      "DEBITED_AND_FAILED_TRANSACTION":
        ProductCategory: "PRODUCT_CATEGORY_TRANSACTIONS"
        ProductCategoryDetails: "PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP"
        SubCategory: "SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY"
        # In-App Transactions | Amount debited but not credited to the beneficiary | UPI - Failed
        PaymentProtocolToIssueCategoryIdMap:
          "UPI": "0388c59c-1417-5f90-98dc-40965ef73ebd"

      # TODO (Ashutosh): Add IssueCategoryId for the below incident types, and also Notification Templates , and Freshdesk content
      "DEBITED_AND_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP":
        ProductCategory: "PRODUCT_CATEGORY_TRANSACTIONS"
        ProductCategoryDetails: "PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP"
        SubCategory: "SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED"
        # In-App Transactions | Amount debited but not credited(Auto ID) | UPI - Pending
        PaymentProtocolToIssueCategoryIdMap:
          "UPI": "699f0f29-f579-57e0-9258-7aa21ccad527"
      "DEBITED_AND_FAILED_TRANSACTION_CATCH_ALL_ON_APP":
        ProductCategory: "PRODUCT_CATEGORY_TRANSACTIONS"
        ProductCategoryDetails: "PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP"
        SubCategory: "SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED"
        # In-App Transactions | Amount debited but not credited(Auto ID) | UPI - Failed
        PaymentProtocolToIssueCategoryIdMap:
          "UPI": "af86096d-7f38-5cf4-bec2-4a664364610e"
      "IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP":
        ProductCategory: "PRODUCT_CATEGORY_TRANSACTIONS"
        ProductCategoryDetails: "PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP"
        SubCategory: "SUB_CATEGORY_TRANSACTIONS_VIA_FI_APP_BUT_NOT_DEBITED"
        # In-App Transactions | Amount not debited(Auto ID) | UPI - Pending
        PaymentProtocolToIssueCategoryIdMap:
          "UPI": "160c8d63-c112-5632-b059-e29fce5186a8"
      "CHEQUE_CREDIT_TRANSACTION":
        ProductCategory: "PRODUCT_CATEGORY_TRANSACTIONS"
        ProductCategoryDetails: "PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP"
        SubCategory: "SUB_CATEGORY_TRANSACTIONS_VIA_FI_APP_BUT_NOT_DEBITED"
        # In-App Transactions | Amount not debited(Auto ID) | UPI - Pending
        PaymentProtocolToIssueCategoryIdMap:
          "CTS": "afe5ea0e-e79f-5401-b2d5-68aa840450e5"
      "ONBOARD_ADD_FUNDS_SECOND_LEG_FAILURE":
        ProductCategory: "PRODUCT_CATEGORY_TRANSACTIONS"
        ProductCategoryDetails: "PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP"
        # TODO(Ashutosh): Add the correct subcategory before merging PR
        SubCategory: "SUB_CATEGORY_TRANSACTIONS_VIA_FI_APP_BUT_NOT_DEBITED"
        # In-App Transactions | Other transaction issues | Unable to add funds during onboarding
        PaymentProtocolToIssueCategoryIdMap:
          "INTRA_BANK": "57af8607-548a-5df9-9638-890acb596170"
    IncidentNotificationTemplateMap:
      "P2P_DEEMED_TRANSACTION":
        IncidentCreationTemplate:
          Title: "⚠️ UPI Transaction issue identified"
          Body: "Your recent UPI payment of %s has reached the receiver’s bank and will be transferred to their account within 5 days. If this fails for any reason, you will receive a refund. Ticket No: %s"
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
        IncidentResolutionTemplate:
          Title: " ✅ UPI Transaction issue resolved"
          Body: "Yay, looks like your UPI Transaction issue (Ticket No: %s) has been resolved. Have more questions? Feel free to reach out to us."
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
      "P2M_DEEMED_TRANSACTION":
        IncidentCreationTemplate:
          Title: "⚠️ UPI Transaction issue identified"
          Body: "Your recent UPI payment of %s has reached the receiver’s bank and will be transferred to their account within 5 days. If this fails for any reason, you will receive a refund. Ticket No: %s"
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
        IncidentResolutionTemplate:
          Title: " ✅ UPI Transaction issue resolved"
          Body: "Yay, looks like your UPI Transaction issue (Ticket No: %s) has been resolved. Have more questions? Feel free to reach out to us."
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
      "P2P_DEEMED_TRANSACTION_OFF_APP":
        IncidentCreationTemplate:
          Title: "⚠️ UPI Transaction issue identified"
          Body: "Your recent UPI payment of %s has reached the receiver’s bank and will be transferred to their account within 6 days. If this fails for any reason, you will receive a refund. Ticket No: %s"
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
        IncidentResolutionTemplate:
          Title: " ✅ UPI Transaction issue resolved"
          Body: "Yay, looks like your UPI Transaction issue (Ticket No: %s) has been resolved. Have more questions? Feel free to reach out to us."
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
      "P2M_DEEMED_TRANSACTION_OFF_APP":
        IncidentCreationTemplate:
          Title: "⚠️ UPI Transaction issue identified"
          Body: "Your recent UPI payment of %s has reached the receiver’s bank and will be transferred to their account within 6 days. If this fails for any reason, you will receive a refund. Ticket No: %s"
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
        IncidentResolutionTemplate:
          Title: " ✅ UPI Transaction issue resolved"
          Body: "Yay, looks like your UPI Transaction issue (Ticket No: %s) has been resolved. Have more questions? Feel free to reach out to us."
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
      "DEBITED_AND_FAILED_TRANSACTION":
        IncidentCreationTemplate:
          Title: "⚠️ Transaction refund pending"
          Body: "Your recent payment of %s has failed and has not reached the receiver's bank. You will receive a refund within 48 hours. Ticket No: %s"
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
        IncidentResolutionTemplate:
          Title: " ✅ Transaction issue resolved"
          Body: "Yay, looks like your Transaction issue (Ticket No: %s) has been resolved. Have more questions? Feel free to reach out to us."
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
      "REVERSED_FAILED_TRANSACTION":
        #TODO(Shubham): Take this content from @saurabhk
        IncidentCreationTemplate:
          Title: "⚠️ Transaction issue identified"
          Body: "Your recent payment of %s has not reached the receiver’s account yet, It will be transferred to their account within 48 hours. If this fails for any reason, you will receive a refund. Ticket No: %s"
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
        IncidentResolutionTemplate:
          Title: " ✅ Transaction issue resolved"
          Body: "Yay, looks like your Transaction issue (Ticket No: %s) has been resolved. Have more questions? Feel free to reach out to us."
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
      "DEBITED_AND_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP":
        IncidentCreationTemplate:
          Title: "⚠️ Transaction  issue identified"
          Body: "Your recent payment of %s is in progress and has not reached the receiver's bank. You will receive a refund within 48 hours. Ticket No: %s"
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
        IncidentResolutionTemplate:
          Title: " ✅ Transaction issue resolved"
          Body: "Yay, looks like your Transaction issue (Ticket No: %s) has been resolved. Have more questions? Feel free to reach out to us."
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
      "DEBITED_AND_FAILED_TRANSACTION_CATCH_ALL_ON_APP":
        IncidentCreationTemplate:
          Title: "⚠️ Transaction issue identified"
          Body: "Your recent payment of %s has failed and has not reached the receiver's bank. You will receive a refund within 48 hours. Ticket No: %s"
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
        IncidentResolutionTemplate:
          Title: " ✅ Transaction issue resolved"
          Body: "Yay, looks like your Transaction issue (Ticket No: %s) has been resolved. Have more questions? Feel free to reach out to us."
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
      "IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP":
        IncidentCreationTemplate:
          Title: "⚠️ Transaction issue identified"
          Body: "Your payment of %s is in progress. If the transaction fails and the amount gets debited, it will be refunded within 48 hours. Ticket No: %s"
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
        IncidentResolutionTemplate:
          Title: " ✅ Transaction issue resolved"
          Body: "Yay, looks like your Transaction issue (Ticket No: %s) has been resolved. Have more questions? Feel free to reach out to us."
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
      "ONBOARD_ADD_FUNDS_SECOND_LEG_FAILURE":
        IncidentCreationTemplate:
          Title: "⚠️ Unable to add funds"
          Body: "We are unable to add %s to your account. If the amount has been debited, it will be refunded within 2-3 working days. Ticket No: %s"
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"
        IncidentResolutionTemplate:
          Title: "✅ Add Funds Transaction issue update"
          Body: "Your transaction issue (Ticket No: %s) has been reviewed. Refunds typically reflect within 2–3 working days. If you haven't received yours yet, feel free to reach out."
          IconAttr:
            IconURL: "https://epifi-icons.pointz.in/pay/payincidentmanager/Alert.png"

    IncidentTicketConfigMap:
      "P2P_DEEMED_TRANSACTION":
        TicketDescription: "<strong>UTR for Transaction:</strong> [UTR] , <br> https://sherlock.epifi.in/v2/scripts/g5D61-y1oMV-O9TMC-L8hHj"
      "P2M_DEEMED_TRANSACTION":
        TicketDescription: "<strong>UTR for Transaction:</strong> [UTR] , <br> https://sherlock.epifi.in/v2/scripts/g5D61-y1oMV-O9TMC-L8hHj"
      "REVERSED_FAILED_TRANSACTION":
        # TODO(Shubham): Take this url from @Gokul
        TicketDescription: ""
      "DEBITED_AND_FAILED_TRANSACTION":
        TicketDescription: "<strong>UTR for Transaction:</strong> [UTR] , <br> https://sherlock.epifi.in/v2/scripts/transaction-failed-in-appoff-app-transactions-other-transaction-issues"
      "P2P_DEEMED_TRANSACTION_OFF_APP":
        TicketDescription: "<strong>UTR for Transaction:</strong> [UTR] , <br> https://sherlock.epifi.in/v2/scripts/g5D61-y1oMV-O9TMC-L8hHj"
      "P2M_DEEMED_TRANSACTION_OFF_APP":
        TicketDescription: "<strong>UTR for Transaction:</strong> [UTR] , <br> https://sherlock.epifi.in/v2/scripts/g5D61-y1oMV-O9TMC-L8hHj"
      "DEBITED_AND_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP":
        # TODO (Ashutosh) - Get sherlock V2 links from @Roshnee
        TicketDescription: "<strong>UTR for Transaction:</strong> [UTR]"
      "DEBITED_AND_FAILED_TRANSACTION_CATCH_ALL_ON_APP":
        TicketDescription: "<strong>UTR for Transaction:</strong> [UTR]"
      "IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP":
        TicketDescription: "<strong>UTR for Transaction:</strong> [UTR]"
      "CHEQUE_CREDIT_TRANSACTION":
        TicketDescription: "<strong>UTR for Transaction:</strong> [UTR]"
      "ONBOARD_ADD_FUNDS_SECOND_LEG_FAILURE":
        TicketDescription: "<strong>UTR for Transaction:</strong> [UTR]"
PayIncidentMgrOrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "demo-order-update-payincidentmanager-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

UniqueATMActorId: "ACHKZ9wtGvSu++VNRdu/fRlA230401=="
PageSizeToFetchTxnForATMActor: 5
PageSizeForChargeRelatedOrderAndTxn: 15
PostPaymentBanners:
  - Title:
      Color: "#FFFFFF"
      Text: "Link & pay with any bank on Fi"
      Style: "SUBTITLE_S"
    Image:
      Url: "https://epifi-icons.pointz.in/upi/icons/tpap_banner_right_icon.png"
      Height: "84"
      Width: "84"
    BgColor: "#4F71AB"
    Deeplink: "LIST_ACCOUNT_PROVIDER_SCREEN"
  - Title:
      Color: "#FFFFFF"
      Text: "Flat 15% Discounts on your favourite brands. Explore offers"
      Style: "SUBTITLE_S"
    Image:
      Url: "https://epifi-icons.pointz.in/post-payment-banner/discount-icon"
      Height: "84"
      Width: "84"
    BgColor: "#6F62A4"
    Deeplink: "DEBIT_CARD_OFFERS_HOME_SCREEN"
QrScreenElements:
  Banners:
    - Title:
        Color: "#B2B5B9"
        Text: "WOW!"
        Style: "SUBTITLE_S"
      Body:
        Color: "#EACE4C"
        Text: "{fi_coins_monthly_earned} Fi-coins earned last month 🥳"
        Style: "HEADLINE_XS"
      Image:
        Url: "https://epifi-icons.pointz.in/tiering/tier_all_plans_v2_rupee_gold_coins.png"
        Height: "52"
        Width: "52"
      BgColor: "#F6E1C1"
      DataRequirements:
        RequiresTieringData: true
        RewardsConfig:
          RequiresRewardsData: true
          TimePeriod: "LAST_MONTH"
    - Title:
        Color: "#929599"
        Text: "PAYING A BUSINESS?"
        Style: "OVERLINE_2XS_CAPS"
      Body:
        Color: "#BCDCE7"
        Text: "Get {rewards_percentage} back as Fi-coins on this payment. Yayy!"
        Style: "HEADLINE_XS"
      Image:
        Url: "{tier_icon_url}"
        Height: "52"
        Width: "52"
      BgColor: "#6EBDEF"
      DataRequirements:
        RequiresTieringData: true
  ProgressBars:
    - Title:
        Color: "#6A6D70"
        Text: "{rewards_percentage} back on all transactions"
        Style: "HEADLINE_XS"
      DataRequirements:
        RequiresTieringData: true
        RewardsConfig:
          RequiresProjectedRewardsData: true
          TimePeriod: "CURRENT_MONTH"


IncidentManagerParams:
  DebitedTransactionThresholdBreachDuration: 5m

Tracing:
  Enable: false

MaximumActiveDaysForAutoIdTickets: 20

TpapEntryPointSwitchConfigForSaUser:
  FeatureConstraints:
    FEATURE_CREDIT_CARD_TPAP_PAYMENTS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 312
        MinIOSVersion: 446
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 37 # PAY_EXPERIMENTAL

TpapEntryPointSwitchConfigForNonSaUser:
  FeatureConstraints:
    FEATURE_CREDIT_CARD_TPAP_PAYMENTS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 312
        MinIOSVersion: 446
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 37 # PAY_EXPERIMENTAL

WorkflowToVendorAccountDetailsMap:
  SECURED_CC_FEDERAL_FD_FUND_TRANSFER:
    ActorId: "actor-creditcard-federal-pool-account"
    PiId: "paymentinstrument-creditcard-federal-pool-account-1"
    BufferDuration: "30s"

# This configuration outlines different combination of transaction status, statusCodePayer and statusCodePayee
# Comparing transaction attributes with these combinations to decide whether incident type should be of DEBITED_AND_FAILED_TRANSACTION
TransactionStatusDetailsCombinationsForDebitAndFailedIncident:
  - IsEnabled: true
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI1204"
    StatusCodePayee: "UPI825"
  - IsEnabled: true
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI171"
    StatusCodePayee: "UPI784"
  - IsEnabled: true
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI372"
    StatusCodePayee: "UPI372"
  - IsEnabled: true
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI784"
    StatusCodePayee: "UPI784"
  - IsEnabled: true
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI_SUCCESS"
    StatusCodePayee: "UPI_SUCCESS"

EnableEntitySegregation: true


# Config for paymentgateway flows. This config is also duplicated in the pay worker's config. Do update there as well when updating here
PgParams:
  # TODO(Sundeep): Increase the values once we determine the reliability of webhooks.
  # For one time payments, we expect the payments to be complete within 12m, hence setting the signal wait timeout to
  # this. If the webhook event is not received from paymentgateway within this timeout, then the workflow polls the
  # payment gateway for a while to check if payment is completed.
  OneTimeFundTransferStatusWaitSignalTimeout: "1m"
  # For recurring payment executions (using mandates), for the payments to be authorised it takes T + 1 days. Hence,
  # for recurring payment executions we set the signal wait timeout to 3 days, as we expect the payments to be
  # completed within that duration. However in non-prod the mandate execution is immediately captured, hence setting it
  # to a small value
  # https://razorpay.com/docs/payments/recurring-payments/emandate/faqs/#4-for-emandates-how-long-does-it-take
  RpExecutionFundTransferStatusWaitSignalTimeout: "1m"

  OneTimePaymentOrderExpirationDuration:
    # Fail the internal order corresponding to razorpay order if no payment is attempted within 20 min.
    "RAZORPAY": "20m"

  # Config values for creating order and transaction for adjustment payments from payment gateway during recon.
  PaymentGatewayAdjustmentActorId: "pg-adjustment-generic-vendor-actor-id"
  PaymentGatewayAdjustmentActorToName: "PG Pool Account"

  PGAdjustmentOwnershipToPiMapping:
    "STOCK_GUARDIAN_TSP": "pg-adjustment-generic-vendor-pi-id_MbZRPgHhafW"
    "EPIFI_TECH": "pg-adjustment-generic-vendor-pi-id"
    "LIQUILOANS_PL": "pg-adjustment-generic-vendor-pi-id_5feFb8ziEp"

  PaymentGatewayRefundGenericActorId: "pg-refund-no-forward-payment-actor-id"
  PaymentGatewayRefundGenericActorFromName: "PG Pool account"
  # Make 2 concurrent vendor calls in every 1 second, so for page size of 100, it will take 50s to process.
  # Razorpay has a ratelimit of 10 QPS configured per account, hence starting with 2 requests per second, so that
  # we have enough headroom for normal flows as well.
  VendorDetailsEnrichmentNumWorkers: 2
  VendorDetailsEnrichmentApiRatelimit: 2

  # Disable updating of PG mandate status via consumer flow till the flow the revoke recurring payment is setup.
  EnableMandateStatusUpdateViaConsumer: false

FeedbackEngineCustomEvaluatorRules:
  - FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS:
      DurationForTxnEvaluation: "72h"
      MinSuccessCountThreshold: 2
      MinDebitAmount: 500

BaseRuleEngineConfig:
  WorkerPool: 10


EnrichOrderConfig:
  EnrichmentFromDcSwitchConfig:
    AllowedTxnTimeDeviationBuffer: "3h"

# Configuration for tier-related mappings
TierConfig:
  TierToIconMap:
    "TIER_FI_AA_SALARY": "https://epifi-icons.pointz.in/tiering/prime_badge_3d_fill.png"
    "TIER_FI_INFINITE": "https://epifi-icons.pointz.in/tiering/infinite_badge_3d_fill.png"
    "TIER_FI_SALARY": "https://epifi-icons.pointz.in/tiering/salary_badge_3d_fill.png"
    "TIER_FI_PLUS": "https://epifi-icons.pointz.in/tiering/plus_badge_3d_fill.png"
