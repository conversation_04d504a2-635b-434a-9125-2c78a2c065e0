package stageproc

import (
	"context"
	"strings"

	"github.com/epifi/gamma/api/employment"

	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/form"
	formPkg "github.com/epifi/gamma/api/typesv2/form"
	userPb "github.com/epifi/gamma/api/user"
	deeplinkV3Pb "github.com/epifi/gamma/pkg/deeplinkv3"
)

var (
	employmentTypeToOccupationTypesMap = map[typesv2.EmploymentType][]employment.OccupationType{
		typesv2.EmploymentType_EMPLOYMENT_TYPE_HOMEMAKER: {
			employment.OccupationType_OCCUPATION_TYPE_HOMEMAKER,
		},
		typesv2.EmploymentType_EMPLOYMENT_TYPE_RETIRED: {
			employment.OccupationType_OCCUPATION_TYPE_RETIRED,
		},
		typesv2.EmploymentType_EMPLOYMENT_TYPE_SALARIED: {
			employment.OccupationType_OCCUPATION_TYPE_ACADEMIA,
			employment.OccupationType_OCCUPATION_TYPE_BUREAUCRAT,
			employment.OccupationType_OCCUPATION_TYPE_FINANCIAL_SECTOR,
			employment.OccupationType_OCCUPATION_TYPE_GOVERNMENT,
			employment.OccupationType_OCCUPATION_TYPE_JUDGE,
			employment.OccupationType_OCCUPATION_TYPE_MEDIA,
			employment.OccupationType_OCCUPATION_TYPE_NGO,
			employment.OccupationType_OCCUPATION_TYPE_PRIVATE_SECTOR,
			employment.OccupationType_OCCUPATION_TYPE_PUBLIC_SECTOR,
			employment.OccupationType_OCCUPATION_TYPE_LLP,
			employment.OccupationType_OCCUPATION_TYPE_PARTNERSHIP,
			employment.OccupationType_OCCUPATION_TYPE_PROPRIETORSHIP,
			employment.OccupationType_OCCUPATION_TYPE_PRIVATE_LIMITED,
			employment.OccupationType_OCCUPATION_TYPE_PUBLIC_LIMITED,
			employment.OccupationType_OCCUPATION_TYPE_TRUST,
			employment.OccupationType_OCCUPATION_TYPE_SOCIETY,
			employment.OccupationType_OCCUPATION_TYPE_MULTINATIONAL,
		},
		typesv2.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED: {
			employment.OccupationType_OCCUPATION_TYPE_AGRICULTURE,
			employment.OccupationType_OCCUPATION_TYPE_ART_ANTIQUES_DEALER,
			employment.OccupationType_OCCUPATION_TYPE_ARM_ARMAMENTS_DEALER,
			employment.OccupationType_OCCUPATION_TYPE_ENTERTAINMENT,
			employment.OccupationType_OCCUPATION_TYPE_GOLD_PRECIOUS_STONE_DEALER,
			employment.OccupationType_OCCUPATION_TYPE_MANUFACTURING,
			employment.OccupationType_OCCUPATION_TYPE_PAWN_BROKER,
			employment.OccupationType_OCCUPATION_TYPE_PROFESSIONAL_INTERMEDIARIES,
			employment.OccupationType_OCCUPATION_TYPE_REAL_ESTATE_BUSINESS,
			employment.OccupationType_OCCUPATION_TYPE_SCRAP_DEALER,
			employment.OccupationType_OCCUPATION_TYPE_STOCK_BROKER,
			employment.OccupationType_OCCUPATION_TYPE_MONEY_LENDER_OR_PRIVATE_FINANCIERS,
			employment.OccupationType_OCCUPATION_TYPE_SERVICE_PROVIDER,
			employment.OccupationType_OCCUPATION_TYPE_TRADER,
			employment.OccupationType_OCCUPATION_TYPE_VIRTUAL_CURRENCY_DEALER,
		},
		typesv2.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED_PROFESSIONAL: {
			employment.OccupationType_OCCUPATION_TYPE_ARCHITECT,
			employment.OccupationType_OCCUPATION_TYPE_CA_CS,
			employment.OccupationType_OCCUPATION_TYPE_DOCTOR,
			employment.OccupationType_OCCUPATION_TYPE_IT_CONSULTANT,
			employment.OccupationType_OCCUPATION_TYPE_LAWYER,
			employment.OccupationType_OCCUPATION_TYPE_FREELANCER,
			employment.OccupationType_OCCUPATION_TYPE_CAR_DEALER,
		},
		typesv2.EmploymentType_EMPLOYMENT_TYPE_STUDENT: {
			employment.OccupationType_OCCUPATION_TYPE_STUDENT,
		},
		typesv2.EmploymentType_EMPLOYMENT_TYPE_UNEMPLOYED: {
			employment.OccupationType_OCCUPATION_TYPE_UNEMPLOYED,
		},
		typesv2.EmploymentType_EMPLOYMENT_TYPE_POLITICIAN_OR_STATESMAN: {
			employment.OccupationType_OCCUPATION_TYPE_POLITICIAN_OR_STATESMAN,
		},
		typesv2.EmploymentType_EMPLOYMENT_TYPE_OTHERS: {
			employment.OccupationType_OCCUPATION_TYPE_OTHERS,
		},
		typesv2.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED: {
			employment.OccupationType_OCCUPATION_TYPE_UNSPECIFIED,
		},
	}
)

type CollectAdditionalProfileDetailsForFederalLoansStage struct {
	userClient userPb.UsersClient
}

func NewCollectAdditionalProfileDetailsForFederalLoansStage(userClient userPb.UsersClient) *CollectAdditionalProfileDetailsForFederalLoansStage {
	return &CollectAdditionalProfileDetailsForFederalLoansStage{userClient: userClient}
}

func (u *CollectAdditionalProfileDetailsForFederalLoansStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	user, err := u.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: req.GetOnb().GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(user, err); rpcErr != nil {
		logger.Error(ctx, "error in fetching user from actor id", zap.String(logger.ACTOR_ID_V2, req.GetOnb().GetActorId()), zap.Error(rpcErr))
		return nil, rpcErr
	}

	var employmentDetails *userPb.DataVerificationDetail_EmploymentDetail
	verificationDetailsList := user.GetUser().GetDataVerificationDetails().GetDataVerificationDetails()
	for i := len(verificationDetailsList) - 1; i >= 0; i-- {
		verificationDetails := verificationDetailsList[i]
		if verificationDetails.GetDataType() == userPb.DataType_DATA_TYPE_EMPLOYMENT_DETAIL && verificationDetails.GetVerificationMethod() == userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA {
			employmentDetails = verificationDetails.GetEmploymentDetail()
			break // Break as soon as we find the latest matching employment details.
		}
	}

	// TODO: once we start showing religion in the form, uncomment the below religion condition.
	if user.GetUser().GetProfile().GetQualification() != typesv2.Qualification_QUALIFICATION_UNSPECIFIED &&
		user.GetUser().GetProfile().GetDesignation() != typesv2.Designation_DESIGNATION_UNSPECIFIED &&
		user.GetUser().GetProfile().GetCommunity() != typesv2.Community_COMMUNITY_UNSPECIFIED &&
		user.GetUser().GetProfile().GetCategory() != typesv2.Category_CATEGORY_UNSPECIFIED &&
		user.GetUser().GetProfile().GetDisabilityType() != typesv2.DisabilityType_DISABILITY_TYPE_UNSPECIFIED &&
		employmentDetails.GetOccupationType() != employment.OccupationType_OCCUPATION_TYPE_UNSPECIFIED {
		return nil, NoActionError
	}

	return &StageProcessorResponse{
		NextAction: &deeplinkPb.Deeplink{
			Screen:          deeplinkPb.Screen_USER_DETAILS_FORM,
			ScreenOptionsV2: deeplinkV3Pb.GetScreenOptionV2WithoutError(u.getUpdateFormDetailsScreenOptions(user.GetUser().GetProfile(), employmentDetails)),
		},
	}, nil
}

func (u *CollectAdditionalProfileDetailsForFederalLoansStage) getUpdateFormDetailsScreenOptions(profileDetails *userPb.Profile, employmentDetails *userPb.DataVerificationDetail_EmploymentDetail) *form.UserDetailsFormScreenOptions {
	var (
		qualificationDropDownOptions []*commontypes.Text
		designationDropDownOptions   []*commontypes.Text
		// religionDropDownOptions        []*commontypes.Text
		communityDropDownOptions       []*commontypes.Text
		occupationDropDownOptions      []*commontypes.Text
		categoryDropDownOptions        []*commontypes.Text
		disabilityTypeDropDownOptions  []*commontypes.Text
		qualificationDefaultValueIndex = -1
		designationDefaultValueIndex   = -1
		// religionDefaultValueIndex      = -1
		communityDefaultValueIndex      = -1
		occupationDefaultValueIndex     = -1
		categoryDefaultValueIndex       = -1
		disabilityTypeDefaultValueIndex = -1
	)

	for _, qualification := range typesv2.Qualification_name {
		if qualification == typesv2.Qualification_QUALIFICATION_UNSPECIFIED.String() {
			continue
		}
		// Convert underscores to spaces
		processedQualification := strings.ReplaceAll(qualification, "_", " ")

		// Split into words
		words := strings.Fields(processedQualification)

		// Insert line breaks after every 3 words (if there are more than 3 words)
		if len(words) > 3 {
			processedQualification = strings.Join(words[:3], " ") + "\n" + strings.Join(words[3:], " ")
		}
		qualificationDropDownOptions = append(qualificationDropDownOptions, commontypes.GetTextFromStringFontColourFontStyle(processedQualification, "#646464", commontypes.FontStyle_SUBTITLE_3))
	}

	for _, designation := range typesv2.Designation_name {
		if designation == typesv2.Designation_DESIGNATION_UNSPECIFIED.String() {
			continue
		}
		designationDropDownOptions = append(designationDropDownOptions, commontypes.GetTextFromStringFontColourFontStyle(strings.ReplaceAll(designation, "_", " "), "#646464", commontypes.FontStyle_SUBTITLE_3))
	}

	// for _, religion := range typesv2.Religion_name {
	//	if religion == typesv2.Religion_RELIGION_UNSPECIFIED.String() {
	//		continue
	//	}
	//	religionDropDownOptions = append(religionDropDownOptions, commontypes.GetTextFromStringFontColourFontStyle(strings.ReplaceAll(religion, "_", " "), "#646464", commontypes.FontStyle_SUBTITLE_3))
	// }

	for _, community := range typesv2.Community_name {
		if community == typesv2.Community_COMMUNITY_UNSPECIFIED.String() {
			continue
		}
		communityDropDownOptions = append(communityDropDownOptions, commontypes.GetTextFromStringFontColourFontStyle(strings.ReplaceAll(community, "_", " "), "#646464", commontypes.FontStyle_SUBTITLE_3))
	}

	occupationType, ok := employmentTypeToOccupationTypesMap[employmentDetails.GetEmploymentType()]
	if !ok {
		// If in employment type to occupation type map, the employment type is not present, then we will show all the occupation types.
		for _, occupation := range employment.OccupationType_name {
			if occupation == employment.OccupationType_OCCUPATION_TYPE_UNSPECIFIED.String() {
				continue
			}
			occupationDropDownOptions = append(occupationDropDownOptions, commontypes.GetTextFromStringFontColourFontStyle(strings.ReplaceAll(strings.TrimPrefix(occupation, "OCCUPATION_TYPE_"), "_", " "), "#646464", commontypes.FontStyle_SUBTITLE_3))
		}
	} else {
		for _, occupation := range occupationType {
			if occupation == employment.OccupationType_OCCUPATION_TYPE_UNSPECIFIED {
				continue
			}
			occupationDropDownOptions = append(occupationDropDownOptions, commontypes.GetTextFromStringFontColourFontStyle(strings.ReplaceAll(strings.TrimPrefix(occupation.String(), "OCCUPATION_TYPE_"), "_", " "), "#646464", commontypes.FontStyle_SUBTITLE_3))
		}
	}

	for _, category := range typesv2.Category_name {
		if category == typesv2.Category_CATEGORY_UNSPECIFIED.String() {
			continue
		}
		categoryDropDownOptions = append(categoryDropDownOptions, commontypes.GetTextFromStringFontColourFontStyle(strings.ReplaceAll(strings.TrimPrefix(category, "CATEGORY_"), "_", " "), "#646464", commontypes.FontStyle_SUBTITLE_3))
	}

	for _, disabilityType := range typesv2.DisabilityType_name {
		if disabilityType == typesv2.DisabilityType_DISABILITY_TYPE_UNSPECIFIED.String() {
			continue
		}
		disabilityTypeDropDownOptions = append(disabilityTypeDropDownOptions, commontypes.GetTextFromStringFontColourFontStyle(strings.ReplaceAll(strings.TrimPrefix(disabilityType, "DISABILITY_TYPE_"), "_", " "), "#646464", commontypes.FontStyle_SUBTITLE_3))
	}

	if profileDetails.GetQualification() != typesv2.Qualification_QUALIFICATION_UNSPECIFIED {
		qualificationDefaultValueIndex = int(profileDetails.GetQualification())
	}
	if profileDetails.GetDesignation() != typesv2.Designation_DESIGNATION_UNSPECIFIED {
		designationDefaultValueIndex = int(profileDetails.GetDesignation())
	}
	// if profileDetails.GetReligion() != typesv2.Religion_RELIGION_UNSPECIFIED {
	//	religionDefaultValueIndex = int(profileDetails.GetReligion())
	// }
	if profileDetails.GetCommunity() != typesv2.Community_COMMUNITY_UNSPECIFIED {
		communityDefaultValueIndex = int(profileDetails.GetCommunity())
	}
	if employmentDetails.GetOccupationType() != employment.OccupationType_OCCUPATION_TYPE_UNSPECIFIED {
		occupationDefaultValueIndex = int(employmentDetails.GetOccupationType())
	}
	if profileDetails.GetCategory() != typesv2.Category_CATEGORY_UNSPECIFIED {
		categoryDefaultValueIndex = int(profileDetails.GetCategory())
	}
	if profileDetails.GetDisabilityType() != typesv2.DisabilityType_DISABILITY_TYPE_UNSPECIFIED {
		disabilityTypeDefaultValueIndex = int(profileDetails.GetDisabilityType())
	}

	return &form.UserDetailsFormScreenOptions{
		HeaderBar: &deeplink.HeaderBar{
			CenterLogo: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/onboarding/federal_wide.png", 23, 92),
			BackAction: &deeplinkPb.BackAction{
				ShowButton: commontypes.BooleanEnum_TRUE,
			},
		},
		Title:    commontypes.GetTextFromStringFontColourFontStyle("Few more details", "#333333", commontypes.FontStyle_HEADLINE_XL),
		Subtitle: commontypes.GetTextFromStringFontColourFontStyle("Federal Bank required these to proceed with your application", "#8D8D8D", commontypes.FontStyle_BODY_S),
		InputFields: []*formPkg.InputField{
			{
				Label:      commontypes.GetTextFromStringFontColourFontStyle("Qualification", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
				IsEditable: true,
				FieldId:    formPkg.FieldIdentifier_FIELD_IDENTIFIER_QUALIFICATION.String(),
				FieldType:  formPkg.InputFieldType_INPUT_FIELD_TYPE_TEXT_DROPDOWN,
				InputFieldOptions: &formPkg.InputField_TextDropdownInputFieldOptions{
					TextDropdownInputFieldOptions: &formPkg.TextDropdownInputFieldOptions{
						DropdownBottomSheetTitle: commontypes.GetTextFromStringFontColourFontStyle("Select Qualification", "#313234", commontypes.FontStyle_HEADLINE_L),
						DropdownSelections:       qualificationDropDownOptions,
						DefaultValueIndex:        int32(qualificationDefaultValueIndex),
					},
				},
			},
			{
				Label:      commontypes.GetTextFromStringFontColourFontStyle("Occupation", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
				IsEditable: true,
				FieldId:    formPkg.FieldIdentifier_FIELD_IDENTIFIER_OCCUPATION.String(),
				FieldType:  formPkg.InputFieldType_INPUT_FIELD_TYPE_TEXT_DROPDOWN,
				InputFieldOptions: &formPkg.InputField_TextDropdownInputFieldOptions{
					TextDropdownInputFieldOptions: &formPkg.TextDropdownInputFieldOptions{
						DropdownBottomSheetTitle: commontypes.GetTextFromStringFontColourFontStyle("Select Occupation", "#313234", commontypes.FontStyle_HEADLINE_L),
						DropdownSelections:       occupationDropDownOptions,
						DefaultValueIndex:        int32(occupationDefaultValueIndex),
					},
				},
			},
			{
				Label:      commontypes.GetTextFromStringFontColourFontStyle("Designation", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
				IsEditable: true,
				FieldId:    formPkg.FieldIdentifier_FIELD_IDENTIFIER_DESIGNATION.String(),
				FieldType:  formPkg.InputFieldType_INPUT_FIELD_TYPE_TEXT_DROPDOWN,
				InputFieldOptions: &formPkg.InputField_TextDropdownInputFieldOptions{
					TextDropdownInputFieldOptions: &formPkg.TextDropdownInputFieldOptions{
						DropdownBottomSheetTitle: commontypes.GetTextFromStringFontColourFontStyle("Select Designation", "#313234", commontypes.FontStyle_HEADLINE_L),
						DropdownSelections:       designationDropDownOptions,
						DefaultValueIndex:        int32(designationDefaultValueIndex),
					},
				},
			},
			// TODO: Uncomment once we want to start showing religion in the form.
			// {
			//	Label:      commontypes.GetTextFromStringFontColourFontStyle("Religion", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			//	IsEditable: true,
			//	FieldId:    formPkg.FieldIdentifier_FIELD_IDENTIFIER_RELIGION.String(),
			//	FieldType:  formPkg.InputFieldType_INPUT_FIELD_TYPE_TEXT_DROPDOWN,
			//	InputFieldOptions: &formPkg.InputField_TextDropdownInputFieldOptions{
			//		TextDropdownInputFieldOptions: &formPkg.TextDropdownInputFieldOptions{
			//			DropdownBottomSheetTitle: commontypes.GetTextFromStringFontColourFontStyle("Select Religion", "#313234", commontypes.FontStyle_HEADLINE_L),
			//			DropdownSelections:       religionDropDownOptions,
			//			DefaultValueIndex:        int32(religionDefaultValueIndex),
			//		},
			//	},
			// },
			{
				Label:      commontypes.GetTextFromStringFontColourFontStyle("Community", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
				IsEditable: true,
				FieldId:    formPkg.FieldIdentifier_FIELD_IDENTIFIER_COMMUNITY.String(),
				FieldType:  formPkg.InputFieldType_INPUT_FIELD_TYPE_TEXT_DROPDOWN,
				InputFieldOptions: &formPkg.InputField_TextDropdownInputFieldOptions{
					TextDropdownInputFieldOptions: &formPkg.TextDropdownInputFieldOptions{
						DropdownBottomSheetTitle: commontypes.GetTextFromStringFontColourFontStyle("Select Community", "#313234", commontypes.FontStyle_HEADLINE_L),
						DropdownSelections:       communityDropDownOptions,
						DefaultValueIndex:        int32(communityDefaultValueIndex),
					},
				},
			},
			{
				Label:      commontypes.GetTextFromStringFontColourFontStyle("Category", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
				IsEditable: true,
				FieldId:    formPkg.FieldIdentifier_FIELD_IDENTIFIER_CATEGORY.String(),
				FieldType:  formPkg.InputFieldType_INPUT_FIELD_TYPE_TEXT_DROPDOWN,
				InputFieldOptions: &formPkg.InputField_TextDropdownInputFieldOptions{
					TextDropdownInputFieldOptions: &formPkg.TextDropdownInputFieldOptions{
						DropdownBottomSheetTitle: commontypes.GetTextFromStringFontColourFontStyle("Select Category", "#313234", commontypes.FontStyle_HEADLINE_L),
						DropdownSelections:       categoryDropDownOptions,
						DefaultValueIndex:        int32(categoryDefaultValueIndex),
					},
				},
			},
			{
				Label:      commontypes.GetTextFromStringFontColourFontStyle("Status", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
				IsEditable: true,
				FieldId:    formPkg.FieldIdentifier_FIELD_IDENTIFIER_DISABILITY_TYPE.String(),
				FieldType:  formPkg.InputFieldType_INPUT_FIELD_TYPE_TEXT_DROPDOWN,
				InputFieldOptions: &formPkg.InputField_TextDropdownInputFieldOptions{
					TextDropdownInputFieldOptions: &formPkg.TextDropdownInputFieldOptions{
						DropdownBottomSheetTitle: commontypes.GetTextFromStringFontColourFontStyle("Select Status", "#313234", commontypes.FontStyle_HEADLINE_L),
						DropdownSelections:       disabilityTypeDropDownOptions,
						DefaultValueIndex:        int32(disabilityTypeDefaultValueIndex),
					},
				},
			},
		},
		Flow: form.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_COLLECT_ADDITIONAL_PROFILE_DETAILS_FOR_FEDERAL_LOANS.String(),
		NextAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
		},
		Ctas: []*deeplink.Cta{
			{
				Type:         deeplink.Cta_DONE,
				Text:         "Continue",
				DisplayTheme: deeplink.Cta_PRIMARY,
			},
		},
	}
}
