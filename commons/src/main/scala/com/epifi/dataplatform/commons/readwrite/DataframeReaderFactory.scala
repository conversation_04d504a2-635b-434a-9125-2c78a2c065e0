package com.epifi.dataplatform.commons.readwrite

import com.epifi.dataplatform.commons.configurations.{PlatformConfig, TableConfig}
import com.epifi.dataplatform.commons.constants.SourceConfigParameters
import com.epifi.dataplatform.commons.utils.PipelineLogger
import com.typesafe.config.Config
import com.epifi.dataplatform.commons.utils.Utility.getSecretManagerSecretsString
import org.apache.spark.sql.SparkSession

object DataframeReaderFactory extends PipelineLogger {
  def getReader(sourceConfig: Config, sparkSession: SparkSession): DataframeReader = {
    val sourceType = sourceConfig.getString(SourceConfigParameters.SOURCE_TYPE)
    sourceType.toLowerCase match {
      case "bigquery" => {
        val project =  PlatformConfig.getBQProject(TableConfig.getOrg)
        val creds = getSecretManagerSecretsString(PlatformConfig.getBQSecretName())
        val dataset = sourceConfig.getString(SourceConfigParameters.DATASET)
        logInfo(Map("message" -> s"sourceType: $sourceType, sourceProject: $project, sourceDataset:$dataset"))
        val context: BigQueryIOContext = BigQueryIOContext(project = project, dataset = dataset, creds = creds, sparkSession = sparkSession)
        BigQueryReader(context)
      }
      case _ => throw new IllegalArgumentException(s"Unsupported destination type: $sourceType")
    }
  }
}
