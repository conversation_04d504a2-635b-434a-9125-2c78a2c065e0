package test

import (
	"context"
	"log"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"
	"go.uber.org/zap"

	awspkg "github.com/epifi/be-common/pkg/aws/v2/config"

	cmdgencfg "github.com/epifi/be-common/pkg/cmd/config/genconf"

	testingInit "github.com/epifi/gamma/testing/init"

	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/logger"
	pkgTestv2 "github.com/epifi/be-common/pkg/test/v2"

	"github.com/epifi/gamma/insights/config"
	genConf "github.com/epifi/gamma/insights/config/genconf"
)

var InsightsTables = []string{
	"user_mail_access_infos",
	"waitlist_onboarded_actor_mappings",
	"message_processing_states",
	"gmail_query_exec_results",
	"merchant_queries",
	"mail_sync_logs",
	"merchants",
	"user_spendings",
	"user_mail_details",
	"epf_passbook_requests",
	"uan_accounts",
	"investment_declarations",
	"networth_refresh_session",
	"epf_import_session",
	"user_declarations",
	"employer_pf_history",
	"epf_passbook_employee_details",
	"epf_passbook_overall_pf_balance",
	"epf_passbook_est_details",
	"epf_passbook_transactions",
}

// InitTestServer initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
// TODO(anand): can't this be moved to pkg? Lot of code duplication.
func InitTestServer(withKms bool) (*config.Config, *gormv2.DB, *gormv2.DB, *usecase.DBResourceProvider[*gormv2.DB], func()) {
	// Init config
	conf, err := config.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)

	serverCfg, err := cmdgencfg.Load(cfg.WEALTH_DMF_SERVER)
	if err != nil {
		log.Fatal("failed to load server config", err)
	}

	// Init db connection
	insightsDb, _, teardown, err := pkgTestv2.PrepareRandomScopedRdsTestDb(conf.InsightsDb, false)
	if err != nil {
		log.Fatal("failed to connect to insights db", err)
	}

	actorInsightsDb, _, actorInsightsTeardown, err := pkgTestv2.PrepareRandomScopedRdsTestDb(conf.ActorInsightsDb, false)
	if err != nil {
		log.Fatal("failed to connect to actor insights db", err)
	}

	useCaseDBProvider, _, _, useCaseDbTeardownFn, dbErr := pkgTestv2.PrepareRandomScopedUsecaseResourceProvider(serverCfg.UseCaseDBConfigMap(), false)
	if dbErr != nil {
		log.Fatal("failed to connect to db", dbErr)
	}

	if withKms {
		awsConfig, err := awspkg.NewAWSConfig(context.Background(), "ap-south-1", false)
		if err != nil {
			logger.Fatal("Failed to build session for aws", zap.Error(err))
		}

		err = testingInit.CreateKmsKeys(context.Background(), awsConfig)
		if err != nil {
			logger.Fatal("unable to create kms keys", zap.Error(err))
		}

		err = config.LoadKmsKeysForLocalStackEnv(conf)
		if err != nil {
			logger.Fatal("unable to load kms keys", zap.Error(err))
		}
	}

	return conf, insightsDb, actorInsightsDb, useCaseDBProvider,
		func() {
			teardown()
			actorInsightsTeardown()
			useCaseDbTeardownFn()
		}
}

func InitTestServerWithoutDBConn() (*genConf.Config, func()) {
	// Init config
	staticConf, err := config.Load()
	if err != nil {
		log.Fatal("failed to load static config", err)
	}
	// Setup logger
	logger.Init(staticConf.Application.Environment)

	// Init dynamic config
	conf, err := genConf.Load()
	if err != nil {
		log.Fatal("failed to load safe config", err)
	}

	return conf, func() {
		_ = logger.Log.Sync()
	}
}
