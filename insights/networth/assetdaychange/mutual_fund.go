package assetdaychange

import (
	"context"
	"fmt"

	"github.com/google/wire"

	networthPb "github.com/epifi/gamma/api/insights/networth"
)

var MutualFundCalculatorWireSet = wire.NewSet(
	NewMutualFundCalculator,
)

// MutualFundCalculator implements DayChangeCalculator for mutual funds
type MutualFundCalculator struct{}

// NewMutualFundCalculator creates a new instance of MutualFundCalculator
func NewMutualFundCalculator() *MutualFundCalculator {
	return &MutualFundCalculator{}
}

func (m *MutualFundCalculator) CalculateAssetDayChangeValue(ctx context.Context, req *CalculateAssetDayChangeValueRequest) (*networthPb.AssetTypeDayChangeResponse, error) {
	return nil, fmt.Errorf("not implemented")
}
