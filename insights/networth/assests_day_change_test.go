package networth

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	enumsPb "github.com/epifi/gamma/api/insights/networth/enums"
	modelPb "github.com/epifi/gamma/api/insights/networth/model"
	"github.com/epifi/gamma/insights/networth/assetdaychange"
	mockAssetDayChange "github.com/epifi/gamma/insights/networth/assetdaychange/mocks"
	mockDao "github.com/epifi/gamma/insights/networth/dao/mocks"
)

func TestService_GetAssetsDayChange(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Test data
	actorId := "test-actor-id"
	initialDate := &timestamppb.Timestamp{Seconds: **********} // 2021-01-01
	finalDate := &timestamppb.Timestamp{Seconds: **********}   // 2021-01-02
	assetTypes := []enumsPb.AssetType{
		enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND,
		enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
	}

	// Sample asset data
	epifiTechAssetData := &modelPb.AssetData{
		CurrentValue: &money.Money{
			CurrencyCode: "INR",
			Units:        1000,
		},
	}
	epifiWealthAssetData := &modelPb.AssetData{
		CurrentValue: &money.Money{
			CurrencyCode: "INR",
			Units:        2000,
		},
	}

	// Sample day change response
	mfDayChangeResponse := &networthPb.AssetTypeDayChangeResponse{
		InitialDateTotalValue: &money.Money{
			CurrencyCode: "INR",
			Units:        1000,
		},
		FinalDateTotalValue: &money.Money{
			CurrencyCode: "INR",
			Units:        1100,
		},
		TotalChange: 100,
	}

	stocksDayChangeResponse := &networthPb.AssetTypeDayChangeResponse{
		InitialDateTotalValue: &money.Money{
			CurrencyCode: "INR",
			Units:        2000,
		},
		FinalDateTotalValue: &money.Money{
			CurrencyCode: "INR",
			Units:        2200,
		},
		TotalChange: 200,
	}

	tests := []struct {
		name           string
		request        *networthPb.GetAssetsDayChangeRequest
		setupMocks     func(*mockDao.MockAssetHistoryDao, *mockAssetDayChange.MockFactory, *mockAssetDayChange.MockDayChangeCalculator, *mockAssetDayChange.MockDayChangeCalculator)
		expectedResult *networthPb.GetAssetsDayChangeResponse
		expectedError  bool
	}{
		{
			name: "successful day change calculation for multiple asset types",
			request: &networthPb.GetAssetsDayChangeRequest{
				ActorId:     actorId,
				AssetTypes:  assetTypes,
				InitialDate: initialDate,
				FinalDate:   finalDate,
			},
			setupMocks: func(assetHistoryDao *mockDao.MockAssetHistoryDao, factory *mockAssetDayChange.MockFactory, mfCalculator *mockAssetDayChange.MockDayChangeCalculator, stocksCalculator *mockAssetDayChange.MockDayChangeCalculator) {
				// Mock GetMultipleHistoriesByDate for initial date - called for each ownership
				assetHistoryDao.EXPECT().
					GetMultipleHistoriesByDate(gomock.Any(), actorId, assetTypes, initialDate).
					Return(map[enumsPb.AssetType]*modelPb.AssetHistory{
						enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND: {
							Data: epifiTechAssetData,
						},
						enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES: {
							Data: epifiWealthAssetData,
						},
					}, nil).Times(3) // Called for 3 ownerships

				// Mock GetMultipleHistoriesByDate for final date - called for each ownership
				assetHistoryDao.EXPECT().
					GetMultipleHistoriesByDate(gomock.Any(), actorId, assetTypes, finalDate).
					Return(map[enumsPb.AssetType]*modelPb.AssetHistory{
						enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND: {
							Data: epifiTechAssetData,
						},
						enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES: {
							Data: epifiWealthAssetData,
						},
					}, nil).Times(3) // Called for 3 ownerships

				// Mock factory returning calculators
				factory.EXPECT().
					GetCalculator(gomock.Any(), enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND).
					Return(mfCalculator, nil)

				factory.EXPECT().
					GetCalculator(gomock.Any(), enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES).
					Return(stocksCalculator, nil)

				// Mock calculations
				mfCalculator.EXPECT().
					CalculateAssetDayChangeValue(gomock.Any(), gomock.Any()).
					Return(mfDayChangeResponse, nil)

				stocksCalculator.EXPECT().
					CalculateAssetDayChangeValue(gomock.Any(), gomock.Any()).
					Return(stocksDayChangeResponse, nil)
			},
			expectedResult: &networthPb.GetAssetsDayChangeResponse{
				Status: rpcPb.StatusOk(),
				AssetTypeToDayChangeResponseMap: map[string]*networthPb.AssetTypeDayChangeResponse{
					enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND.String():       mfDayChangeResponse,
					enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES.String(): stocksDayChangeResponse,
				},
			},
			expectedError: false,
		},
		{
			name: "error getting asset data request map",
			request: &networthPb.GetAssetsDayChangeRequest{
				ActorId:     actorId,
				AssetTypes:  assetTypes,
				InitialDate: initialDate,
				FinalDate:   finalDate,
			},
			setupMocks: func(assetHistoryDao *mockDao.MockAssetHistoryDao, factory *mockAssetDayChange.MockFactory, mfCalculator *mockAssetDayChange.MockDayChangeCalculator, stocksCalculator *mockAssetDayChange.MockDayChangeCalculator) {
				// Mock database error for any call
				assetHistoryDao.EXPECT().
					GetMultipleHistoriesByDate(gomock.Any(), actorId, assetTypes, gomock.Any()).
					Return(nil, errors.New("database error")).AnyTimes()
			},
			expectedResult: &networthPb.GetAssetsDayChangeResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error getting asset data request map"),
			},
			expectedError: true,
		},
		{
			name: "empty asset types list",
			request: &networthPb.GetAssetsDayChangeRequest{
				ActorId:     actorId,
				AssetTypes:  []enumsPb.AssetType{},
				InitialDate: initialDate,
				FinalDate:   finalDate,
			},
			setupMocks: func(assetHistoryDao *mockDao.MockAssetHistoryDao, factory *mockAssetDayChange.MockFactory, mfCalculator *mockAssetDayChange.MockDayChangeCalculator, stocksCalculator *mockAssetDayChange.MockDayChangeCalculator) {
				// Even with empty asset types, the function still makes database calls for each ownership
				// but with empty asset types slice
				assetHistoryDao.EXPECT().
					GetMultipleHistoriesByDate(gomock.Any(), actorId, []enumsPb.AssetType{}, initialDate).
					Return(map[enumsPb.AssetType]*modelPb.AssetHistory{}, nil).Times(3)

				assetHistoryDao.EXPECT().
					GetMultipleHistoriesByDate(gomock.Any(), actorId, []enumsPb.AssetType{}, finalDate).
					Return(map[enumsPb.AssetType]*modelPb.AssetHistory{}, nil).Times(3)
			},
			expectedResult: &networthPb.GetAssetsDayChangeResponse{
				Status:                          rpcPb.StatusOk(),
				AssetTypeToDayChangeResponseMap: map[string]*networthPb.AssetTypeDayChangeResponse{},
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			assetHistoryDao := mockDao.NewMockAssetHistoryDao(ctrl)
			dayChangeCalculatorFactory := mockAssetDayChange.NewMockFactory(ctrl)
			mfCalculator := mockAssetDayChange.NewMockDayChangeCalculator(ctrl)
			stocksCalculator := mockAssetDayChange.NewMockDayChangeCalculator(ctrl)

			// Setup service with mocked dependencies
			service := &Service{
				assetHistoryDao:            assetHistoryDao,
				dayChangeCalculatorFactory: dayChangeCalculatorFactory,
			}

			// Setup mock expectations
			tt.setupMocks(assetHistoryDao, dayChangeCalculatorFactory, mfCalculator, stocksCalculator)

			// Execute test
			result, err := service.GetAssetsDayChange(context.Background(), tt.request)

			// Verify results
			if tt.expectedError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}

			assert.Equal(t, tt.expectedResult.Status.GetCode(), result.Status.GetCode())
			if tt.expectedResult.AssetTypeToDayChangeResponseMap != nil {
				assert.Len(t, result.AssetTypeToDayChangeResponseMap, len(tt.expectedResult.AssetTypeToDayChangeResponseMap))
				for key, expectedResp := range tt.expectedResult.AssetTypeToDayChangeResponseMap {
					actualResp, exists := result.AssetTypeToDayChangeResponseMap[key]
					assert.True(t, exists, "Expected asset type %s in response", key)
					if exists {
						assert.InDelta(t, expectedResp.TotalChange, actualResp.TotalChange, 0.001)
						if expectedResp.InitialDateTotalValue != nil && actualResp.InitialDateTotalValue != nil {
							assert.Equal(t, expectedResp.InitialDateTotalValue.Units, actualResp.InitialDateTotalValue.Units)
						}
						if expectedResp.FinalDateTotalValue != nil && actualResp.FinalDateTotalValue != nil {
							assert.Equal(t, expectedResp.FinalDateTotalValue.Units, actualResp.FinalDateTotalValue.Units)
						}
					}
				}
			}
		})
	}
}

func TestService_getAssetDataRequestMap(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Test data
	actorId := "test-actor-id"
	initialDate := &timestamppb.Timestamp{Seconds: **********} // 2021-01-01
	finalDate := &timestamppb.Timestamp{Seconds: **********}   // 2021-01-02
	assetTypes := []enumsPb.AssetType{
		enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND,
		enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
	}

	epifiTechAssetData := &modelPb.AssetData{
		CurrentValue: &money.Money{
			CurrencyCode: "INR",
			Units:        1000,
		},
	}
	epifiWealthAssetData := &modelPb.AssetData{
		CurrentValue: &money.Money{
			CurrencyCode: "INR",
			Units:        2000,
		},
	}
	federalBankAssetData := &modelPb.AssetData{
		CurrentValue: &money.Money{
			CurrencyCode: "INR",
			Units:        3000,
		},
	}

	tests := []struct {
		name        string
		request     *networthPb.GetAssetsDayChangeRequest
		setupMocks  func(*mockDao.MockAssetHistoryDao)
		expectError bool
		validate    func(t *testing.T, result map[enumsPb.AssetType]*assetdaychange.CalculateAssetDayChangeValueRequest)
	}{
		{
			name: "successful retrieval of asset data for all ownerships",
			request: &networthPb.GetAssetsDayChangeRequest{
				ActorId:     actorId,
				AssetTypes:  assetTypes,
				InitialDate: initialDate,
				FinalDate:   finalDate,
			},
			setupMocks: func(assetHistoryDao *mockDao.MockAssetHistoryDao) {
				// The function creates 6 goroutines (3 ownerships * 2 dates)
				// Each goroutine calls GetMultipleHistoriesByDate once

				// Mock initial date calls for all ownerships
				assetHistoryDao.EXPECT().
					GetMultipleHistoriesByDate(gomock.Any(), actorId, assetTypes, initialDate).
					Return(map[enumsPb.AssetType]*modelPb.AssetHistory{
						enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND: {
							Data: epifiTechAssetData,
						},
						enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES: {
							Data: epifiWealthAssetData,
						},
					}, nil).Times(3) // Called once per ownership

				// Mock final date calls for all ownerships
				assetHistoryDao.EXPECT().
					GetMultipleHistoriesByDate(gomock.Any(), actorId, assetTypes, finalDate).
					Return(map[enumsPb.AssetType]*modelPb.AssetHistory{
						enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND: {
							Data: federalBankAssetData,
						},
						enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES: {
							Data: federalBankAssetData,
						},
					}, nil).Times(3) // Called once per ownership
			},
			expectError: false,
			validate: func(t *testing.T, result map[enumsPb.AssetType]*assetdaychange.CalculateAssetDayChangeValueRequest) {
				// Validate that all asset types are present
				assert.Len(t, result, 2)

				for _, assetType := range assetTypes {
					req, exists := result[assetType]
					assert.True(t, exists, "Expected asset type %s in result", assetType.String())
					if exists {
						assert.Equal(t, initialDate, req.InitialDate)
						assert.Equal(t, finalDate, req.FinalDate)
						assert.Len(t, req.OwnershipToInitialDateDataMap, 3)
						assert.Len(t, req.OwnershipToFinalDateDataMap, 3)

						// Verify all ownerships are present
						ownerships := []commontypes.Ownership{
							commontypes.Ownership_EPIFI_TECH,
							commontypes.Ownership_EPIFI_WEALTH,
							commontypes.Ownership_FEDERAL_BANK,
						}
						for _, ownership := range ownerships {
							_, initialExists := req.OwnershipToInitialDateDataMap[ownership]
							_, finalExists := req.OwnershipToFinalDateDataMap[ownership]
							assert.True(t, initialExists, "Expected ownership %s in initial data", ownership.String())
							assert.True(t, finalExists, "Expected ownership %s in final data", ownership.String())
						}
					}
				}
			},
		},
		{
			name: "handle record not found error gracefully",
			request: &networthPb.GetAssetsDayChangeRequest{
				ActorId:     actorId,
				AssetTypes:  assetTypes,
				InitialDate: initialDate,
				FinalDate:   finalDate,
			},
			setupMocks: func(assetHistoryDao *mockDao.MockAssetHistoryDao) {
				// Mock record not found for all calls (should be handled gracefully)
				assetHistoryDao.EXPECT().
					GetMultipleHistoriesByDate(gomock.Any(), actorId, assetTypes, gomock.Any()).
					Return(nil, epifierrors.ErrRecordNotFound).AnyTimes()
			},
			expectError: false,
			validate: func(t *testing.T, result map[enumsPb.AssetType]*assetdaychange.CalculateAssetDayChangeValueRequest) {
				// Should still return results but with empty ownership maps
				assert.Len(t, result, 2)
				for _, assetType := range assetTypes {
					req, exists := result[assetType]
					assert.True(t, exists)
					if exists {
						assert.Empty(t, req.OwnershipToInitialDateDataMap)
						assert.Empty(t, req.OwnershipToFinalDateDataMap)
					}
				}
			},
		},
		{
			name: "empty asset types list",
			request: &networthPb.GetAssetsDayChangeRequest{
				ActorId:     actorId,
				AssetTypes:  []enumsPb.AssetType{},
				InitialDate: initialDate,
				FinalDate:   finalDate,
			},
			setupMocks: func(assetHistoryDao *mockDao.MockAssetHistoryDao) {
				// Even with empty asset types, the function still makes database calls for each ownership
				// but with empty asset types slice
				assetHistoryDao.EXPECT().
					GetMultipleHistoriesByDate(gomock.Any(), actorId, []enumsPb.AssetType{}, initialDate).
					Return(map[enumsPb.AssetType]*modelPb.AssetHistory{}, nil).Times(3)

				assetHistoryDao.EXPECT().
					GetMultipleHistoriesByDate(gomock.Any(), actorId, []enumsPb.AssetType{}, finalDate).
					Return(map[enumsPb.AssetType]*modelPb.AssetHistory{}, nil).Times(3)
			},
			expectError: false,
			validate: func(t *testing.T, result map[enumsPb.AssetType]*assetdaychange.CalculateAssetDayChangeValueRequest) {
				// Should return empty map for empty asset types
				assert.Empty(t, result)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			assetHistoryDao := mockDao.NewMockAssetHistoryDao(ctrl)

			// Setup service with mocked dependencies
			service := &Service{
				assetHistoryDao: assetHistoryDao,
			}

			// Setup mock expectations
			tt.setupMocks(assetHistoryDao)

			// Execute test
			result, err := service.getAssetDataRequestMap(context.Background(), tt.request)

			// Verify results
			if tt.expectError {
				require.Error(t, err)
				assert.Nil(t, result)
			} else {
				require.NoError(t, err)
				require.NotNil(t, result)
				if tt.validate != nil {
					tt.validate(t, result)
				}
			}
		})
	}
}
