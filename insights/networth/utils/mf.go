package utils

import (
	"context"
	"sort"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/money"
	mfAnalyserVariablePb "github.com/epifi/gamma/api/analyser/variables/mutualfund"
	mfUtils "github.com/epifi/gamma/investment/utils"
)

type DailyChangeDetails struct {
	DailyNavPercentageChange float64
	CurrentValue             float64
	SchemeAnalytics          *mfAnalyserVariablePb.SchemeAnalytics
}

func CalculateDailyChange(details *DailyChangeDetails) float64 {
	return (details.CurrentValue * details.DailyNavPercentageChange) / (100 + details.DailyNavPercentageChange)
}

// CalculateAggregatedMfValues returns aggregated currentValue, aggregated previous value and aggregated daily change
func CalculateAggregatedMfValues(dailyChangeList []*DailyChangeDetails) (float64, float64, float64) {
	aggregatedDailyChange := 0.0
	aggregatedCurrentValue := 0.0
	aggregatedPreviousValue := 0.0
	for _, dailyChange := range dailyChangeList {
		currentSchemeChangeDiff := CalculateDailyChange(dailyChange)
		aggregatedDailyChange += currentSchemeChangeDiff
		aggregatedCurrentValue += dailyChange.CurrentValue
		aggregatedPreviousValue += dailyChange.CurrentValue - currentSchemeChangeDiff
	}
	return aggregatedCurrentValue, aggregatedPreviousValue, aggregatedDailyChange
}

// GetMfAggregatedValues aggregates values from mf scheme analytics response
// and returns totalCurrentValue, totalPreviousValue, totalDailyChange and percentageChange(0 if previous value is not present)
func GetMfAggregatedValues(ctx context.Context, mfSchemeAnalytics *mfAnalyserVariablePb.MfSchemeAnalytics) (float64, float64, float64, float64) {
	sortedMfDailyChangeList := GetSchemeAnalyticsBySortedDayChange(ctx, mfSchemeAnalytics)
	mfAggCurVal, mfAggPrevVal, mfAggDailyChange := CalculateAggregatedMfValues(sortedMfDailyChangeList)
	if mfAggPrevVal == 0 {
		return 0, 0, 0, 0
	}
	percentageChange := (mfAggDailyChange / mfAggPrevVal) * 100

	return mfAggCurVal, mfAggPrevVal, mfAggDailyChange, percentageChange
}

func GetSchemeAnalyticsBySortedDayChange(ctx context.Context, schemeDetails *mfAnalyserVariablePb.MfSchemeAnalytics) []*DailyChangeDetails {
	var mfDailyChangeList []*DailyChangeDetails
	for _, scheme := range schemeDetails.GetSchemeAnalytics() {
		if !validateCurrentScheme(scheme) {
			continue
		}
		percentageChange := mfUtils.CalculateNavChangePercentage(ctx, scheme.GetSchemeDetail())
		if percentageChange == 0 {
			continue
		}
		mfDailyChangeList = append(mfDailyChangeList, &DailyChangeDetails{
			DailyNavPercentageChange: percentageChange,
			CurrentValue:             ConvertMoneyToFloat(scheme.GetEnrichedAnalytics().GetAnalytics().GetSchemeDetails().GetCurrentValue()),
			SchemeAnalytics:          scheme,
		})
	}
	sort.Slice(mfDailyChangeList, func(i, j int) bool {
		valI := mfDailyChangeList[i].CurrentValue
		valJ := mfDailyChangeList[j].CurrentValue
		return valI > valJ
	})
	return mfDailyChangeList
}

func validateCurrentScheme(scheme *mfAnalyserVariablePb.SchemeAnalytics) bool {
	enrichedSchemeDetails := scheme.GetEnrichedAnalytics().GetAnalytics().GetSchemeDetails()
	if money.IsZero(enrichedSchemeDetails.GetInvestedValue()) || money.IsZero(enrichedSchemeDetails.GetCurrentValue()) {
		return false
	}
	return true
}

// TODO(sainath): Using these may not work for multiple currency types

func ConvertMoneyToFloat(value *moneyPb.Money) float64 {
	return float64(value.GetUnits()) + (float64(value.GetNanos()) / 1000000000.0)
}
