Application:
  Environment: "prod"
  Name: "insights"
  GmailDataEncrKeyKMSId: "42425d5e-75e7-481b-a8ed-42ed43cf8176"
  EmailIdRegex: "[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*"
  MailFetchDateRangeInMonths: 3

Server:
  Ports:
    GrpcPort: 8096
    GrpcSecurePort: 9511
    HttpPort: 9999
    HttpPProfPort: 9990

InsightsDb:
  AppName: "insights"
  StatementTimeout: 10s
  Name: "insights"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  SecretName: "prod/rds/epifimetis/actor-insights"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

ActorInsightsDb:
  AppName: "insights"
  StatementTimeout: 10s
  Name: "actor_insights"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  SecretName: "prod/rds/epifimetis/actor-insights"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

UserEmailAccessPublisher:
  QueueName: "prod-new-user-email-access-queue"

UserEmailAccessSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-new-user-email-access-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~20 min post that regular interval is followed for next 200 mins
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 1
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 10
          MaxAttempts: 20
          TimeUnit: "Minute"
      MaxAttempts: 30
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "insights"

PeriodicEmaiSyncPublisher:
  QueueName: "prod-periodic-mail-sync-queue"
  QueueOwnerAccountId: "************"

PeriodicEmailSyncSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-periodic-mail-sync-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 600
        Period: 1m
    Namespace: "insights"

MailDataParserPublisher:
  QueueName: "prod-mail-data-parser-queue"

MailDataParserSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-mail-data-parser-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~20 min post that regular interval is followed for next 200 mins
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 1
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 10
          MaxAttempts: 20
          TimeUnit: "Minute"
      MaxAttempts: 30
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "insights"

OnboardingStageUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-insights-onb-stage-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 5
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 600
        Period: 1m
    Namespace: "insights"

GmailUserSpendsPublisher:
  TopicName: "prod-gmail-user-spends-topic"

EpfPassbookImportEventPublisher:
  TopicName: "prod-epf-passbook-import-topic"

CreateOrUpdateGenerationStatusSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-insight-generation-script-run-status"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "insights"

StoreGeneratedActorInsightsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-generated-actor-insights"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "insights"

GenerateInsightsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 2
  QueueName: "prod-generate-insights"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 2
        Period: 1s
    Namespace: "insights"

ProcessCaNewDataFetchEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 2
  QueueName: "prod-insights-ca-data-new-data-fetch-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "insights"

AWS:
  Region: "ap-south-1"

GmailBatchGetApiParams:
  ClientMaxIdleConns: 10
  ClientIdleConnTimeout: 30
  MaxIdleConnsPerHost: 10
  TimeoutInMillis: 15000
  BatchReqUrl: "https://www.googleapis.com/batch/gmail/v1"
  GetMessageApi: "GET /gmail/v1/users/me/messages/"

GmailListApiParams:
  ClientMaxIdleConns: 25
  MaxIdleConnsPerHost: 25
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 2000

EmailParserParams:
  ClientMaxIdleConns: 10
  MaxIdleConnsPerHost: 10
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 1000
  ParseReqUrl: "https://email-parser.epifi.in/v1/parse"

GoogleOAuthParams:
  ClientMaxIdleConns: 10
  MaxIdleConnsPerHost: 10
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 1500
  RevokeTokenUrl: "https://oauth2.googleapis.com/revoke"

MailFetchConcurrencyParams:
  MaxGoroutinesPerUser: 5
  MessagePerGoroutine: 10

Secrets:
  Ids:
    GoogleOAuthCredentials: "prod/insights-web/gmail-oauth-cred"
    EmailParserInsightsDbUsernamePassword: "prod/rds/epifimetis/insights"
    ActorInsightsDbUsernamePassword: "prod/rds/epifimetis/actor-insights"
    MailDataEncryptionKey: "prod/insights/mail-data-encr"

Flags:
  TrimDebugMessageFromStatus: false
  ShowTeaserInsights: false

AddGmailAccountBannerParams:
  ShowAddAccountBanner: true
  AddAccountRedirectUrl: "https://fi.money/insights"
  AddAccountWebViewTitle: "Insights"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

NetworthParams:
  DebugActorIdsForDailyReport:
    - "AC2HCC4Txhz6250411": true
    - "AC220103Hapts4DoRsqNUTkIYeereA==": true # Sainath's actorid to debug indian stocks
  UseSchemeAnalyticsApiForMfAggVal: true

NetWorthRefreshParams:
  AssetsRefreshThreshold:
    - "NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS": 168h # 1 week
    - "NET_WORTH_REFRESH_ASSET_EPF": 1440h # 2 month
    - "NET_WORTH_REFRESH_ASSET_CREDIT_SCORE": 720h # 1 month. should be same as CreditReportRefreshBannerDurationInDays in CreditScoreAnalyserConfig
  AssetsProcessingRefreshThreshold:
    - "NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS": 30m
    - "NET_WORTH_REFRESH_ASSET_EPF": 30m
    - "NET_WORTH_REFRESH_ASSET_CREDIT_SCORE": 30m
  ManualAssetsRefreshThreshold:
    - "REAL_ESTATE": 4320h # 6 month
    - "AIF": 8640h # 1 year
    - "PRIVATE_EQUITY": 8640h # 1 year
    - "CASH": 4320h # 6 month
    - "BOND": 8640h # 1 year
    - "ART_AND_ARTEFACTS": 8640h # 1 year
  InstrumentsRefreshOrder: [
    "NET_WORTH_REFRESH_ASSET_CREDIT_SCORE",
    "NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS",
    "NET_WORTH_REFRESH_ASSET_EPF",
    "NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS"]

EpfPassbookDataFlatteningSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-epf-passbook-data-flattening-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 10
      TimeUnit: "Second"

UANAccountsCacheConfig:
  CacheTTL: "10m"
  IsCachingEnabled: true

DbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "insights"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  FEDERAL_BANK:
    DbType: "PGDB"
    AppName: "insights"
    StatementTimeout: 5s
    Name: "insights_federal"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/insights_federal_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  EPIFI_WEALTH:
    DbType: "PGDB"
    AppName: "insights"
    StatementTimeout: 10s
    Name: "epifi_wealth_analytics"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/epifi-wealth-analytics"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms


GeminiConf:
  GoogleCloudCredentialsSecretPath: "prod/magic-import/gemini-secret"
