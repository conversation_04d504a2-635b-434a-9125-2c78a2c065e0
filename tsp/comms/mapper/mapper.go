// nolint:funlen,unparam
package mapper

import (
	"fmt"

	"github.com/samber/lo"

	commsPb "github.com/epifi/gamma/api/comms"
	tspCommsPb "github.com/epifi/gamma/api/tsp/comms"
)

// mapTspCommsTypeToCommsType maps tspCommsPb.Qos to commsPb.Qos
func mapTspCommsTypeToCommsType(tspType tspCommsPb.QoS) (commsPb.QoS, error) {
	switch tspType {
	case tspCommsPb.QoS_BEST_EFFORT:
		return commsPb.QoS_BEST_EFFORT, nil
	case tspCommsPb.QoS_GUARANTEED:
		return commsPb.QoS_GUARANTEED, nil
	default:
		return commsPb.QoS_QOS_UNSPECIFIED, fmt.Errorf("invalid QoS type: %s", tspType.String())
	}
}

// mapTspCommsMediumToCommsMedium maps tspCommsPb.Medium to commsPb.Medium
func mapTspCommsMediumToCommsMedium(tspMedium tspCommsPb.Medium) (commsPb.Medium, error) {
	switch tspMedium {
	case tspCommsPb.Medium_SMS:
		return commsPb.Medium_SMS, nil
	case tspCommsPb.Medium_EMAIL:
		return commsPb.Medium_EMAIL, nil
	default:
		return commsPb.Medium_MEDIUM_UNSPECIFIED, fmt.Errorf("invalid medium type: %s", tspMedium.String())
	}
}

// mapTspCommsCampaignNameToCommsCampaignName maps tspCommsPb.CampaignName to commsPb.CampaignName
func mapTspCommsCampaignNameToCommsCampaignName(tspCampaignName tspCommsPb.CampaignName) (commsPb.CampaignName, error) {
	switch tspCampaignName {
	case tspCommsPb.CampaignName_CAMPAIGN_NAME_UNSPECIFIED:
		return commsPb.CampaignName_CAMPAIGN_NAME_UNSPECIFIED, nil
	default:
		return commsPb.CampaignName_CAMPAIGN_NAME_UNSPECIFIED, fmt.Errorf("invalid campaign name: %s", tspCampaignName.String())
	}
}

// mapTspCommsTreatmentIdToCommsTreatmentId maps tspCommsPb.TreatmentId to commsPb.TreatmentId
func mapTspCommsTreatmentIdToCommsTreatmentId(tspTreatmentId tspCommsPb.TreatmentId) (commsPb.TreatmentId, error) {
	switch tspTreatmentId {
	case tspCommsPb.TreatmentId_TREATMENT_ID_UNSPECIFIED:
		return commsPb.TreatmentId_TREATMENT_ID_UNSPECIFIED, nil
	default:
		return commsPb.TreatmentId_TREATMENT_ID_UNSPECIFIED, fmt.Errorf("invalid campaign name: %s", tspTreatmentId.String())
	}
}

// mapTspEmailAddressToCommsEmailAddress maps *tspCommsPb.EmailMessage_Destination_EmailAddress to *commsPb.EmailMessage_Destination_EmailAddress
func mapTspEmailAddressToCommsEmailAddress(tspEmailAddress *tspCommsPb.EmailMessage_Destination_EmailAddress) *commsPb.EmailMessage_Destination_EmailAddress {
	return &commsPb.EmailMessage_Destination_EmailAddress{
		EmailId: tspEmailAddress.GetEmailId(),
		Name:    tspEmailAddress.GetName(),
	}
}

// mapTspEmailAddressesToCommsEmailAddresses maps []*tspCommsPb.EmailMessage_Destination_EmailAddress to []*commsPb.EmailMessage_Destination_EmailAddress
func mapTspEmailAddressesToCommsEmailAddresses(tspEmailAddress []*tspCommsPb.EmailMessage_Destination_EmailAddress) []*commsPb.EmailMessage_Destination_EmailAddress {
	return lo.Map(tspEmailAddress, func(emailAddress *tspCommsPb.EmailMessage_Destination_EmailAddress, _ int) *commsPb.EmailMessage_Destination_EmailAddress {
		return mapTspEmailAddressToCommsEmailAddress(emailAddress)
	})
}

// mapTspUnsubscribeOptionInfoToCommsUnsubscribeOptionInfo maps tspCommsPb.EmailMessage_UnsubscribeOptionInfo to commsPb.EmailMessage_UnsubscribeOptionInfo
func mapTspEmailOptionToCommsEmailOption(tspEmailOption *tspCommsPb.EmailOption) (*commsPb.EmailOption, error) {
	switch tspEmailOption.GetOption().(type) {
	case *tspCommsPb.EmailOption_LoanConfirmationEmailOption:
		return &commsPb.EmailOption{
			Option: &commsPb.EmailOption_LoanConfirmationEmailOption{
				LoanConfirmationEmailOption: &commsPb.LoanConfirmationEmailOption{
					EmailType: commsPb.EmailType_LOAN_CONFIRMATION_EMAIL, // TODO: get this from a converter helper method
					Option: &commsPb.LoanConfirmationEmailOption_LoansPaymentFileEmailV1{
						LoansPaymentFileEmailV1: &commsPb.LoanConfirmationEmailV1{
							TemplateVersion:               commsPb.TemplateVersion_VERSION_V1, // TODO: get this from a converter helper method
							Name:                          tspEmailOption.GetLoanConfirmationEmailOption().GetLoansPaymentFileEmailV1().GetName(),
							LastFourDigitsOfAccountNumber: tspEmailOption.GetLoanConfirmationEmailOption().GetLoansPaymentFileEmailV1().GetLastFourDigitsOfAccountNumber(),
							ContactNumber:                 tspEmailOption.GetLoanConfirmationEmailOption().GetLoansPaymentFileEmailV1().GetContactNumber(),
						},
					},
				},
			},
		}, nil
	default:
		return nil, fmt.Errorf("invalid email option type: %T", tspEmailOption.GetOption())
	}
}

// mapTspUnsubscribeOptionInfoToCommsUnsubscribeOptionInfo maps tspCommsPb.EmailMessage_UnsubscribeOptionInfo to commsPb.EmailMessage_UnsubscribeOptionInfo
func mapTspUnsubscribeOptionInfoToCommsUnsubscribeOptionInfo(tspUnsubscribeOptionInfo *tspCommsPb.EmailMessage_UnsubscribeOptionInfo) *commsPb.EmailMessage_UnsubscribeOptionInfo {
	return &commsPb.EmailMessage_UnsubscribeOptionInfo{
		AllowUserToUnsubscribe: tspUnsubscribeOptionInfo.GetAllowUserToUnsubscribe(),
		TopicName:              tspUnsubscribeOptionInfo.GetTopicName(),
	}
}

// mapTspCommsDestinationToCommsDestination maps tspCommsPb.EmailMessage_Destination to commsPb.EmailMessage_Destination
func mapTspCommsDestinationToCommsDestination(tspDestination *tspCommsPb.EmailMessage_Destination) *commsPb.EmailMessage_Destination {
	return &commsPb.EmailMessage_Destination{
		ToAddresses:  mapTspEmailAddressesToCommsEmailAddresses(tspDestination.GetToAddresses()),
		CcAddresses:  mapTspEmailAddressesToCommsEmailAddresses(tspDestination.GetCcAddresses()),
		BccAddresses: mapTspEmailAddressesToCommsEmailAddresses(tspDestination.GetBccAddresses()),
	}
}

// mapTspCommsSmsMessageToCommsSmsMessage maps tspCommsPb.SmsMessage to commsPb.SmsMessage
func mapTspCommsSmsMessageToCommsSmsMessage(tspSmsMessage *tspCommsPb.SMSMessage) (*commsPb.SMSMessage, error) {
	return &commsPb.SMSMessage{
		SmsOption: &commsPb.SmsOption{},
	}, nil
}

// mapTspCommsDispositionToCommsDisposition maps tspCommsPb.Disposition to commsPb.Disposition
func mapTspCommsDispositionToCommsDisposition(tspDisposition tspCommsPb.Disposition) commsPb.Disposition {
	switch tspDisposition {
	case tspCommsPb.Disposition_INLINE:
		return commsPb.Disposition_INLINE
	case tspCommsPb.Disposition_ATTACHMENT:
		return commsPb.Disposition_ATTACHMENT
	default:
		return commsPb.Disposition_DISPOSITION_UNSPECIFIED
	}
}

// mapTspCommsAttachmentToCommsAttachment maps tspCommsPb.Attachment to commsPb.Attachment
func mapTspCommsAttachmentToCommsAttachment(tspAttachment []*tspCommsPb.EmailMessage_Attachment) []*commsPb.EmailMessage_Attachment {
	return lo.Map(tspAttachment, func(tspAttachment *tspCommsPb.EmailMessage_Attachment, _ int) *commsPb.EmailMessage_Attachment {
		return &commsPb.EmailMessage_Attachment{
			FileContent:    tspAttachment.GetFileContent(),
			FileName:       tspAttachment.GetFileName(),
			Disposition:    mapTspCommsDispositionToCommsDisposition(tspAttachment.GetDisposition()),
			AttachmentType: tspAttachment.GetAttachmentType(),
		}
	})
}

// mapTspCommsEmailMessageToCommsEmailMessage maps tspCommsPb.EmailMessage to commsPb.EmailMessage
func mapTspCommsEmailMessageToCommsEmailMessage(tspEmailMessage *tspCommsPb.EmailMessage) (*commsPb.EmailMessage, error) {
	emailOption, err := mapTspEmailOptionToCommsEmailOption(tspEmailMessage.GetEmailOption())
	if err != nil {
		return nil, fmt.Errorf("error while converting email option: %w", err)
	}
	return &commsPb.EmailMessage{
		FromEmailId:           tspEmailMessage.GetFromEmailId(),
		FromEmailName:         tspEmailMessage.GetFromEmailName(),
		Attachment:            mapTspCommsAttachmentToCommsAttachment(tspEmailMessage.GetAttachment()),
		EmailOption:           emailOption,
		ReplyToName:           tspEmailMessage.GetReplyToName(),
		ReplyToEmailId:        tspEmailMessage.GetReplyToEmailId(),
		UnsubscribeOptionInfo: mapTspUnsubscribeOptionInfoToCommsUnsubscribeOptionInfo(tspEmailMessage.GetUnsubscribeOptionInfo()),
		Destination:           mapTspCommsDestinationToCommsDestination(tspEmailMessage.GetDestination()),
	}, nil
}

// MapToCommsSendMessageRequest maps tspCommsPb.SendMessageRequest to commsPb.SendMessageRequest
func MapToCommsSendMessageRequest(tspCommsReq *tspCommsPb.SendMessageRequest) (*commsPb.SendMessageRequest, error) {
	var (
		commsReq = &commsPb.SendMessageRequest{}
		err      error
	)

	// map QoS type
	if commsReq.Type, err = mapTspCommsTypeToCommsType(tspCommsReq.GetType()); err != nil {
		return nil, fmt.Errorf("error while converting QoS type: %w", err)
	}

	// map medium type
	if commsReq.Medium, err = mapTspCommsMediumToCommsMedium(tspCommsReq.GetMedium()); err != nil {
		return nil, fmt.Errorf("error while converting medium type: %w", err)
	}

	// map user identifier
	switch tspCommsReq.GetUserIdentifier().(type) {
	case *tspCommsPb.SendMessageRequest_EmailId:
		commsReq.UserIdentifier = &commsPb.SendMessageRequest_EmailId{EmailId: tspCommsReq.GetEmailId()}
	case *tspCommsPb.SendMessageRequest_PhoneNumber:
		commsReq.UserIdentifier = &commsPb.SendMessageRequest_PhoneNumber{PhoneNumber: tspCommsReq.GetPhoneNumber()}
	default:
		return nil, fmt.Errorf("invalid user identifier type: %T", tspCommsReq.GetUserIdentifier())
	}

	// map message
	switch tspCommsReq.GetMessage().(type) {
	case *tspCommsPb.SendMessageRequest_Sms:
		smsMessage, smsMessageErr := mapTspCommsSmsMessageToCommsSmsMessage(tspCommsReq.GetSms())
		if smsMessageErr != nil {
			return nil, fmt.Errorf("error while converting sms message: %w", smsMessageErr)
		}
		commsReq.Message = &commsPb.SendMessageRequest_Sms{Sms: smsMessage}
	case *tspCommsPb.SendMessageRequest_Email:
		emailMessage, emailMessageErr := mapTspCommsEmailMessageToCommsEmailMessage(tspCommsReq.GetEmail())
		if emailMessageErr != nil {
			return nil, fmt.Errorf("error while converting email message: %w", emailMessageErr)
		}
		commsReq.Message = &commsPb.SendMessageRequest_Email{Email: emailMessage}
	default:
		return nil, fmt.Errorf("invalid message type: %T", tspCommsReq.GetMessage())
	}

	// map client id
	commsReq.ClientId = tspCommsReq.GetClientId()

	// map campaign name
	if commsReq.CampaignName, err = mapTspCommsCampaignNameToCommsCampaignName(tspCommsReq.GetCampaignName()); err != nil {
		return nil, fmt.Errorf("error while converting campaign name: %w", err)
	}

	// map treatment id
	if commsReq.TreatmentId, err = mapTspCommsTreatmentIdToCommsTreatmentId(tspCommsReq.GetTreatmentId()); err != nil {
		return nil, fmt.Errorf("error while converting treatment id: %w", err)
	}

	// map external reference id
	commsReq.ExternalReferenceId = tspCommsReq.GetExternalReferenceId()

	return commsReq, nil
}

// MapToCommsSendMessageBatchRequest maps tspCommsPb.SendMessageBatchRequest to commsPb.SendMessageBatchRequest
func MapToCommsSendMessageBatchRequest(tspCommsReq *tspCommsPb.SendMessageBatchRequest) (*commsPb.SendMessageBatchRequest, error) {
	var (
		commsReq = &commsPb.SendMessageBatchRequest{}
		err      error
	)

	// map QoS type
	if commsReq.Type, err = mapTspCommsTypeToCommsType(tspCommsReq.GetType()); err != nil {
		return nil, fmt.Errorf("error while converting QoS type: %w", err)
	}

	// map user identifier
	switch tspCommsReq.GetUserIdentifier().(type) {
	case *tspCommsPb.SendMessageBatchRequest_EmailId:
		commsReq.UserIdentifier = &commsPb.SendMessageBatchRequest_EmailId{EmailId: tspCommsReq.GetEmailId()}
	case *tspCommsPb.SendMessageBatchRequest_PhoneNumber:
		commsReq.UserIdentifier = &commsPb.SendMessageBatchRequest_PhoneNumber{PhoneNumber: tspCommsReq.GetPhoneNumber()}
	default:
		return nil, fmt.Errorf("invalid user identifier type: %T", tspCommsReq.GetUserIdentifier())
	}

	// map communication list
	commsList := make([]*commsPb.Communication, 0)
	for _, tspComm := range tspCommsReq.GetCommunicationList() {
		commsComm := &commsPb.Communication{
			ExternalReferenceId: tspComm.GetExternalReferenceId(),
		}

		// map medium type
		if commsComm.Medium, err = mapTspCommsMediumToCommsMedium(tspComm.GetMedium()); err != nil {
			return nil, fmt.Errorf("error while converting medium type: %w", err)
		}

		switch tspComm.GetMessage().(type) {
		case *tspCommsPb.Communication_Sms:
			smsMessage, smsMessageErr := mapTspCommsSmsMessageToCommsSmsMessage(tspComm.GetSms())
			if smsMessageErr != nil {
				return nil, fmt.Errorf("error while converting sms message: %w", smsMessageErr)
			}
			commsComm.Message = &commsPb.Communication_Sms{Sms: smsMessage}
		case *tspCommsPb.Communication_Email:
			emailMessage, emailMessageErr := mapTspCommsEmailMessageToCommsEmailMessage(tspComm.GetEmail())
			if emailMessageErr != nil {
				return nil, fmt.Errorf("error while converting email message: %w", emailMessageErr)
			}
			commsComm.Message = &commsPb.Communication_Email{Email: emailMessage}
		default:
			return nil, fmt.Errorf("invalid message type: %T", tspComm.GetMessage())
		}

		commsList = append(commsList, commsComm)
	}
	commsReq.CommunicationList = commsList

	// map client id
	commsReq.ClientId = tspCommsReq.GetClientId()

	// map campaign name
	if commsReq.CampaignName, err = mapTspCommsCampaignNameToCommsCampaignName(tspCommsReq.GetCampaignName()); err != nil {
		return nil, fmt.Errorf("error while converting campaign name: %w", err)
	}

	// map treatment id
	if commsReq.TreatmentId, err = mapTspCommsTreatmentIdToCommsTreatmentId(tspCommsReq.GetTreatmentId()); err != nil {
		return nil, fmt.Errorf("error while converting treatment id: %w", err)
	}

	return commsReq, nil
}
