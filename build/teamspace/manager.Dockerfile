# Source for 632884248997.dkr.ecr.ap-south-1.amazonaws.com/teamspace-manager
# this will be used to run manager commands in teamspace setup to manage tech stacks
# Use an Ubuntu base image
FROM 632884248997.dkr.ecr.ap-south-1.amazonaws.com/gamma-dependencies:latest AS builder

COPY . /go/src/github.com/epifi/gamma/

WORKDIR /go/src/github.com/epifi/gamma/

RUN go install -v ./platform/teamspace/cmd/...

FROM --platform=linux/amd64 ubuntu:22.04 as BASE

RUN apt-get update && apt-get install -y --no-install-recommends \
    python3.5 \
    python3-pip \
    git \
    jq \
    unzip \
    curl \
    && \
apt-get clean && \
rm -rf /var/lib/apt/lists/*

RUN pip install awscli-local
RUN pip install awscli==1.29.80

WORKDIR /go/src/github.com/epifi/gamma

ENV PATH=${PATH}:/go/bin

COPY --from=BUILDER /go/bin/manager /go/bin/
COPY --from=BUILDER /go/bin/github_jwt /go/bin/

COPY ./db /go/src/github.com/epifi/gamma/db

COPY ./pinot /go/src/github.com/epifi/gamma/pinot

COPY ./scripts/localstack /go/src/github.com/epifi/gamma/scripts/localstack

COPY ./pkg/cfg/config /go/src/github.com/epifi/gamma/config

COPY ./platform/teamspace/scripts/  /go/src/github.com/epifi/gamma/platform/teamspace/scripts/

# scritps to load resources in localstack are hardcoded to refer files from this directory.
# Duplicate copy can be avoided once the bash scripts are fixed
COPY ./scripts/localstack/ /etc/localstack/init/ready.d/

CMD ["manager", "help"]
