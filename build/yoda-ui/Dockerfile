# Dockerfile for 632884248997.dkr.ecr.ap-south-1.amazonaws.com/yoda-ui
FROM 632884248997.dkr.ecr.ap-south-1.amazonaws.com/open-webui:latest

# Install necessary packages
RUN apt-get update && apt-get install -y git

RUN apt-get install -y python3-pip && \
    pip3 install awscli

WORKDIR /epifi

RUN git clone https://github.com/open-webui/pipelines.git --depth 1

WORKDIR /epifi/pipelines

COPY build/yoda-ui/webui_pipeline/yoda_pipeline.py ./pipelines/yoda_pipeline.py
COPY build/yoda-ui/webui_pipeline/yoda_salary_pipeline.py ./pipelines/yoda_salary_pipeline.py
COPY build/yoda-ui/webui_pipeline/yoda_risk_ops_pipeline.py ./pipelines/yoda_risk_ops_pipeline.py
COPY build/yoda-ui/webui_pipeline/yoda_without_epifi_context_pipeline.py ./pipelines/yoda_without_epifi_context_pipeline.py
COPY build/yoda-ui/webui_pipeline/yoda_cx_sop_pipeline.py ./pipelines/yoda_cx_sop_pipeline.py
COPY build/yoda-ui/webui_pipeline/yoda_loans_outcalling_sop_pipeline.py ./pipelines/yoda_loans_outcalling_sop_pipeline.py
COPY build/yoda-ui/webui_pipeline/start.sh ./start.sh

ENV ENVIRONMENT="deploy"
ENV PIPELINES_URLS="/epifi/pipelines/pipelines/yoda_pipeline.py;/epifi/pipelines/pipelines/yoda_salary_pipeline.py;/epifi/pipelines/pipelines/yoda_risk_ops_pipeline.py;/epifi/pipelines/pipelines/yoda_loans_outcalling_sop_pipeline.py;/epifi/pipelines/pipelines/yoda_without_epifi_context_pipeline.py;/epifi/pipelines/pipelines/yoda_cx_sop_pipeline.py"
ENV YODA_SERVER_URL="http://deploy-jarvis-go-backend-green.default.svc.cluster.local:80"

WORKDIR /
COPY build/yoda-ui/start.sh ./start.sh

CMD ["bash", "./start.sh"]
