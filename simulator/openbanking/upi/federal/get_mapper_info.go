// nolint: funlen
package federal

import (
	"context"
	"encoding/xml"
	"fmt"
	"io/ioutil"
	"net/http"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	mapperPb "github.com/epifi/gamma/api/simulator/openbanking/accounts/tpap"
	federalPb "github.com/epifi/gamma/api/simulator/openbanking/fundtransfer/federal"
	"github.com/epifi/gamma/api/vendors"
	vendorFedUpi "github.com/epifi/gamma/api/vendors/federal/upi"
	upiMapper "github.com/epifi/gamma/api/vendors/federal/upi/mapper"
	"github.com/epifi/gamma/simulator/dao/model"
	upi2 "github.com/epifi/gamma/simulator/dao/model/upi"
)

// GetMapperInfo API will be used for checking the availability of an ID before creating a new
// record as well as for fetching status in case of timeout of CREATE/MODIFY/DELETE
// record.
func (u *UpiService) GetMapperInfo(w http.ResponseWriter, req *http.Request) {
	ackRes := model.AckResponse{}
	ackRes.Api = "GetMapperInfo"

	if contentType := req.Header.Get("Content-type"); contentType != contentTypeXML {
		ackRes.Err = contentTypeInvalid
		w.WriteHeader(http.StatusInternalServerError)
		if err := xml.NewEncoder(w).Encode(ackRes); err != nil {
			logger.ErrorNoCtx("failed to encode GetMapperInfoAckResponse", zap.Error(err))
		}
		return
	}

	data, err := ioutil.ReadAll(req.Body)
	if err != nil {
		logger.ErrorNoCtx("Failed to get request body for GetMapperInfo", zap.Error(err))
		ackRes.Err = INVALID_XML
		w.WriteHeader(http.StatusBadRequest)
		if err = xml.NewEncoder(w).Encode(ackRes); err != nil {
			logger.ErrorNoCtx("failed to encode GetMapperInfoAckResponse", zap.Error(err))
		}
		return
	}
	var getMapperInfoReq upi2.GetMapperInfoRequest

	if err = xml.Unmarshal(data, &getMapperInfoReq); err != nil {
		logger.ErrorNoCtx("Failed to unmarshal getMapperInfo request into struct", zap.Error(err))
		ackRes.Err = INVALID_XML
		w.WriteHeader(http.StatusBadRequest)
		if err = xml.NewEncoder(w).Encode(ackRes); err != nil {
			logger.ErrorNoCtx("failed to encode getMapperInfoReqAckResponse ", zap.Error(err))
		}
		return
	}

	if getMapperInfoReq.MerchantHeader.ApiType != ApiTypeGetMapperInfo ||
		getMapperInfoReq.MerchantBody.ReqGetMapperInfo.Txn.ID == "" ||
		getMapperInfoReq.MerchantBody.ReqGetMapperInfo.Head.Msg == "" ||
		getMapperInfoReq.MerchantBody.ReqGetMapperInfo.Head.Ts == "" {
		ackRes.Err = INVALID_XML
		w.WriteHeader(http.StatusBadRequest)
		logger.ErrorNoCtx("mandatory fields are missing")
		if err = xml.NewEncoder(w).Encode(ackRes); err != nil {
			logger.ErrorNoCtx("failed to encode getMapperInfoAckResponse ", zap.Error(err))
		}
		return
	}

	mandatoryFieldMissing := false
	regIdDetails := getMapperInfoReq.MerchantBody.ReqGetMapperInfo.Customer.RegIdDetails
	customer := getMapperInfoReq.MerchantBody.ReqGetMapperInfo.Customer

	switch getMapperInfoReq.MerchantBody.ReqGetMapperInfo.Txn.Type {
	case "CHECK":
		if regIdDetails == nil || regIdDetails.RegId == nil || regIdDetails.RegId.Value == "" {
			mandatoryFieldMissing = true
		}
	case "PORT":
		if regIdDetails == nil || regIdDetails.RegId == nil || regIdDetails.RegId.Name == "" ||
			regIdDetails.RegId.Value == "" {
			mandatoryFieldMissing = true
		}
	case "FETCH":
		// For subType=”VPA”, RegIdDetails block not required
		// For subType=”ID” , Payer.addr + RegIdDetails.value is mandatory
		switch getMapperInfoReq.MerchantBody.ReqGetMapperInfo.Txn.SubType {
		case "ID":
			if customer.Addr == "" || regIdDetails == nil || regIdDetails.RegId == nil ||
				regIdDetails.RegId.Value == "" {
				mandatoryFieldMissing = true
			}
		case "VPA":
			if customer.Addr == "" {
				mandatoryFieldMissing = true
			}
		default:
			logger.ErrorNoCtx("invalid subtype passed", zap.Any("SubType", getMapperInfoReq.MerchantBody.ReqGetMapperInfo.Txn.SubType))
			ackRes.Err = ProperTxnSubTypeNotPresent
			w.WriteHeader(http.StatusBadRequest)
			if err = xml.NewEncoder(w).Encode(ackRes); err != nil {
				logger.ErrorNoCtx("failed to encode getMapperInfoAckResponse", zap.Error(err))
			}
			return
		}
	default:
		logger.ErrorNoCtx("invalid type passed", zap.Any("SubType", getMapperInfoReq.MerchantBody.ReqGetMapperInfo.Txn.SubType))
		ackRes.Err = ProperTxnTypeNotPresent
		w.WriteHeader(http.StatusBadRequest)
		if err = xml.NewEncoder(w).Encode(ackRes); err != nil {
			logger.ErrorNoCtx("failed to encode getMapperInfoAckResponse", zap.Error(err))
		}
		return
	}

	if mandatoryFieldMissing {
		logger.ErrorNoCtx("mandatory fields are missing to build response for get-mapper-info Api")
		if ackRes.Err == "" {
			ackRes.Err = INVALID_XML
		}
		w.WriteHeader(http.StatusBadRequest)
		if err = xml.NewEncoder(w).Encode(ackRes); err != nil {
			logger.ErrorNoCtx("failed to encode getMapperInfoAckResponse", zap.Error(err))
		}
		return
	}
	callBackReq, err := u.getCallBackRequestForGetMapperInfo(&getMapperInfoReq)
	if err != nil {
		logger.ErrorNoCtx("error getting call back request", zap.Error(err))
		ackRes.Err = internalServiceDownResponseCode
		w.WriteHeader(http.StatusInternalServerError)
		if err = xml.NewEncoder(w).Encode(ackRes); err != nil {
			logger.ErrorNoCtx("failed to encode getMapperInfoAckResponse ", zap.Error(err))
		}
		return
	}
	// publish to delayed callback queue
	if _, err = u.upiCallBackPublisher.PublishWithDelay(context.Background(), callBackReq, GetDelayDuration()); err != nil {
		logger.ErrorNoCtx("error publishing to callback queue", zap.Error(err))
		ackRes.Err = internalServiceDownResponseCode
		w.WriteHeader(http.StatusInternalServerError)
		if err = xml.NewEncoder(w).Encode(ackRes); err != nil {
			logger.ErrorNoCtx("failed to encode getMapperInfoAckResponse ", zap.Error(err))
		}
		return
	}

	ackRes.Ts = getMapperInfoReq.MerchantBody.ReqGetMapperInfo.Head.Ts
	ackRes.Err = SUCCESS
	ackRes.ReqMsgId = getMapperInfoReq.MerchantBody.ReqGetMapperInfo.Head.Msg

	w.WriteHeader(http.StatusOK)
	if err = xml.NewEncoder(w).Encode(ackRes); err != nil {
		logger.ErrorNoCtx("failed to encode getMapperInfoAckResponse ", zap.Error(err))
	}
}

var upiNumberStateToIdStatusMap = map[mapperPb.UpiNumberState]string{
	mapperPb.UpiNumberState_UPI_NUMBER_STATE_NEW:        "NEW",
	mapperPb.UpiNumberState_UPI_NUMBER_STATE_ACTIVE:     "ACTIVE",
	mapperPb.UpiNumberState_UPI_NUMBER_STATE_INACTIVE:   "INACTIVE",
	mapperPb.UpiNumberState_UPI_NUMBER_STATE_DEREGISTER: "DEREGISTER",
	mapperPb.UpiNumberState_UPI_NUMBER_STATE_BLOCK:      "BLOCK",
	mapperPb.UpiNumberState_UPI_NUMBER_STATE_UNBLOCK:    "UNBLOCK",
}

// return the call back request to be published to call back delay subscriber
func (u *UpiService) getCallBackRequestForGetMapperInfo(req *upi2.GetMapperInfoRequest) (*federalPb.ProcessUpiCallBackRequest, error) {
	var (
		upiNumberVpaMapping *model.UpiNumberVpaMapping
		err                 error
	)
	getMapperInfoRes := upiMapper.GetMapperInfoCallbackResponse{
		Head: req.MerchantBody.ReqGetMapperInfo.Head,
		Txn:  req.MerchantBody.ReqGetMapperInfo.Txn,
	}
	getMapperInfoRes.Resp.ReqMsgId = req.MerchantBody.ReqGetMapperInfo.Head.Msg
	getMapperInfoRes.Resp.Result = ResultSuccess
	getMapperInfoRes.Resp.ErrCode = ""
	ctx := context.Background()
	upiNumber := req.MerchantBody.ReqGetMapperInfo.Customer.RegIdDetails.RegId.Value
	vpa := req.MerchantBody.ReqGetMapperInfo.Customer.Addr

	upiNumberVpaMapping, err = u.upiNumberVpaMappingDao.GetByUpiNumber(ctx, upiNumber)
	if err != nil && !storagev2.IsRecordNotFoundError(err) {
		return nil, fmt.Errorf("failed to fetch upi-number-vpa-mapping for the upiNumber, err: %w", err)
	}

	switch req.MerchantBody.ReqGetMapperInfo.Txn.Type {
	// CHECK is used to fetch the last updated status of the UPI Number
	case "CHECK":
		if storagev2.IsRecordNotFoundError(err) {
			// This upi number is unused i.e. NEW
			regIdDetails := req.MerchantBody.ReqGetMapperInfo.Customer.RegIdDetails
			regIdDetails.Addr = ""
			regIdDetails.IdStatus = "NEW"
			getMapperInfoRes.Resp.RegIdDetailsList = []*vendorFedUpi.RegIdDetails{
				regIdDetails,
			}
			break
		}
		// return failure if it is already taken
		// add err codes once we know
		getMapperInfoRes.Resp.Result = ResultFailure
		// send MM18 if the upi number is already mapped to a different VPA
		getMapperInfoRes.Resp.ErrCode = IdMappedToDifferentVPA
	case "PORT":
		if storagev2.IsRecordNotFoundError(err) || upiNumberVpaMapping.State == mapperPb.UpiNumberState_UPI_NUMBER_STATE_DEREGISTER {
			getMapperInfoRes.Resp.Result = ResultFailure
			getMapperInfoRes.Resp.ErrCode = MappingDoesNotExists
			break
		}
		if req.MerchantBody.ReqGetMapperInfo.Customer.RegIdDetails.RegId.Name != "MOBILE" {
			getMapperInfoRes.Resp.Result = ResultFailure
			getMapperInfoRes.Resp.ErrCode = PortActionProhibited
			break
		}
		regIdDetails := req.MerchantBody.ReqGetMapperInfo.Customer.RegIdDetails
		regIdDetails.Addr = upiNumberVpaMapping.Vpa
		regIdDetails.IdStatus = upiNumberStateToIdStatusMap[upiNumberVpaMapping.State]

		regIdDetails.LastUpdatedTs = upiNumberVpaMapping.UpdatedAt.In(datetime.IST).Format(vendors.UPINPCIMapperTimestampLayout)
		getMapperInfoRes.Resp.RegIdDetailsList = []*vendorFedUpi.RegIdDetails{
			regIdDetails,
		}
	case "FETCH":
		// check if error is expected for this upi number (For testing)
		if u.isErrorExpectedForUpiNumber(upiNumber) {
			getMapperInfoRes.Resp.Result = ResultFailure
			getMapperInfoRes.Resp.ErrCode = ExpectedFailureForTesting
			break
		}

		switch req.MerchantBody.ReqGetMapperInfo.Txn.SubType {
		case "ID":
			if storagev2.IsRecordNotFoundError(err) || upiNumberVpaMapping.State == mapperPb.UpiNumberState_UPI_NUMBER_STATE_DEREGISTER || upiNumberVpaMapping.Vpa != vpa {
				getMapperInfoRes.Resp.Result = ResultFailure
				getMapperInfoRes.Resp.ErrCode = MappingDoesNotExists
				break
			}
			regIdDetails := req.MerchantBody.ReqGetMapperInfo.Customer.RegIdDetails
			regIdDetails.Addr = upiNumberVpaMapping.Vpa
			regIdDetails.IdStatus = upiNumberStateToIdStatusMap[upiNumberVpaMapping.State]
			regIdDetails.LastUpdatedTs = upiNumberVpaMapping.UpdatedAt.In(datetime.IST).Format(vendors.UPINPCIMapperTimestampLayout)
			getMapperInfoRes.Resp.RegIdDetailsList = []*vendorFedUpi.RegIdDetails{
				regIdDetails,
			}
		case "VPA":
			var upiNumberVpaMappings []*model.UpiNumberVpaMapping
			upiNumberVpaMappings, err = u.upiNumberVpaMappingDao.GetByVpa(ctx, vpa)
			if err != nil {
				getMapperInfoRes.Resp.Result = ResultFailure
				getMapperInfoRes.Resp.ErrCode = MappingDoesNotExists
				break
			}
			for _, upiNumberVpaMapping = range upiNumberVpaMappings {
				regIdDetails := req.MerchantBody.ReqGetMapperInfo.Customer.RegIdDetails
				regIdDetails.Addr = upiNumberVpaMapping.Vpa
				regIdDetails.RegId.Value = upiNumberVpaMapping.UpiNumber
				regIdDetails.LastUpdatedTs = upiNumberVpaMapping.UpdatedAt.In(datetime.IST).Format(vendors.UPINPCIMapperTimestampLayout)
				getMapperInfoRes.Resp.RegIdDetailsList = append(getMapperInfoRes.Resp.RegIdDetailsList, regIdDetails)
			}
		}
	}

	resBytes, err := xml.Marshal(getMapperInfoRes)
	if err != nil {
		logger.ErrorNoCtx("error converting getMapperInfo callback response to bytes")
		return nil, err
	}
	return &federalPb.ProcessUpiCallBackRequest{
		CallbackUrl: string(u.upiCallbackUrl),
		RawData:     resBytes,
	}, nil
}
