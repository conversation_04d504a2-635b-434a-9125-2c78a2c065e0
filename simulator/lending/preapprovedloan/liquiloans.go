// nolint:gosec,gocritic,funlen,goconst,errcheck,dupl
package preapprovedloan

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	encJson "encoding/json"
	"io/ioutil"
	"math"
	"math/rand"
	"net/http"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	json "google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	preapprovedloanPb "github.com/epifi/gamma/api/simulator/lending/preapprovedloan"
	simPreapprovedPb "github.com/epifi/gamma/api/simulator/lending/preapprovedloan"
	liquiloansPb "github.com/epifi/gamma/api/vendors/liquiloans/lending"
	"github.com/epifi/gamma/simulator"
	"github.com/epifi/gamma/simulator/lending/preapprovedloan/dummay_data"
)

const (
	layout                    = "2006-01-02 15:04:05"
	bankingDetailsRequest     = "bankingDetailsRequest"
	addressDetailsRequest     = "addressDetailsRequest"
	employmentDetailsRequest  = "employmentDetailsRequest"
	applicationStatusField    = "ApplicationStatusApproved"
	mandateStatusField        = "MandateStatusApproved"
	borrowerAgreementOtpField = "BorrowerAgreementOtpApproved"
)

func marshalProtoAndAddHeader[U proto.Message](pb U, w http.ResponseWriter, header int) {
	dataJson, err := encJson.Marshal(pb)
	if err != nil {
		logger.ErrorNoCtx("error in protojson.Marshal", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(err.Error()))
		return
	}
	w.WriteHeader(header)
	_, _ = w.Write(dataJson)
}

func (s *Service) getPanForApplicantIdLl(applicantId string) (string, error) {
	daoResponse, err := s.vendorResponseDao.GetByApplication(context.Background(), applicantId, preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_APPLICANT_LOOKUP, commonvgpb.Vendor_LIQUILOANS)
	if err != nil {
		return "", err
	}
	return daoResponse.GetResponseData().GetApplicantLookupResponse().GetData().GetPan(), nil
}

func (s *Service) GetCreditLineDetails(w http.ResponseWriter, req *http.Request) {
	data, err := ioutil.ReadAll(req.Body)
	if err != nil {
		logger.ErrorNoCtx("failed to read request body", zap.Error(err))
		return
	}
	var reqBody liquiloansPb.GetCreditLineDetailsRequest
	if err = json.Unmarshal(data, &reqBody); err != nil {
		logger.ErrorNoCtx("failed to unmarshal request into struct", zap.Error(err))
		return
	}
	failedResp := &liquiloansPb.GetCreditLineDetailsResponse{
		Status:  false,
		Message: "Service Unavailable",
		Code:    401,
	}

	pan, err := s.getPanForApplicantIdLl(reqBody.GetApplicantId())
	if err != nil {
		marshalProtoAndAddHeader(failedResp, w, int(failedResp.GetCode()))
		return
	}
	stubResp, err := getApiResponse[*liquiloansPb.GetCreditLineDetailsResponse](&s.responseDataMap.llDataMap, pan, "")
	if err != nil {
		marshalProtoAndAddHeader(failedResp, w, int(failedResp.GetCode()))
		return
	}

	if stubResp.GetCode() != 200 {
		marshalProtoAndAddHeader(stubResp, w, int(stubResp.GetCode()))
		return
	}

	daoResponse, daoErr := s.vendorResponseDao.GetById(context.Background(), reqBody.GetApplicantId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_INSTANT_LOAN_OFFER, commonvgpb.Vendor_LIQUILOANS)
	var resData *liquiloansPb.GetCreditLineDetailsResponse
	if daoErr != nil && errors.Is(daoErr, epifierrors.ErrRecordNotFound) {
		resData = &liquiloansPb.GetCreditLineDetailsResponse{
			Status:  false,
			Message: "Applicant Credit Limit Does Not Exist",
			Code:    400,
		}
		w.WriteHeader(400)
	} else if daoErr != nil {
		w.WriteHeader(500)
		return
	} else {
		resData = daoResponse.GetResponseData().GetCreditLineDetailsResponse()
	}
	res, err := json.Marshal(resData)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal GetCreditLineDetailsResponse", zap.Error(err))
		return
	}
	w.Write(res)
	return
}

func (s *Service) AddPersonalDetailsLl(ctx context.Context, req *liquiloansPb.AddPersonalDetailsRequest) (*liquiloansPb.AddPersonalDetailsResponse, error) {
	stubResp, err := getApiResponse[*liquiloansPb.AddPersonalDetailsResponse](&s.responseDataMap.llDataMap, req.GetPan(), "")
	if err != nil {
		simulator.SetHttpCode(ctx, 401)
		return &liquiloansPb.AddPersonalDetailsResponse{
			Status:  false,
			Message: "Service Unavailable",
			Code:    401,
		}, nil
	}

	if stubResp.GetCode() != 200 {
		return stubResp, nil
	}

	_, err = s.vendorResponseDao.GetById(context.Background(), req.GetPan(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_APPLICANT_LOOKUP, commonvgpb.Vendor_LIQUILOANS)
	// If record not found, create a new user in DB with a random applicant ID and return success response
	// we will create entry for both Applicant lookup (with PAN as ID) and GetCreditLimits (with Applicant id as ID) here.
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		applicantID := rand.Intn(999999-100000) + 100000
		roi, minAmount, maxAmount, maxEmiAllowed := 18.0, 10000.0, 90000.0, 10000.0
		minTenure, maxTenure := int32(3), int32(36)
		if req.GetSid() == "EsSimSID" {
			roi = 0
			minAmount = 1000
			maxAmount = 50000
			maxEmiAllowed = maxAmount
			minTenure = 1
			maxTenure = 1
		}
		txnErr := storageV2.RunCRDBTxn(ctx, func(ctx context.Context) error {
			_, err1 := s.vendorResponseDao.Create(ctx, &preapprovedloanPb.VendorResponse{
				Id:                 req.GetPan(),
				ApplicationNumber:  strconv.Itoa(applicantID),
				Vendor:             commonvgpb.Vendor_LIQUILOANS,
				VendorResponseType: preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_APPLICANT_LOOKUP,
				ResponseData: &preapprovedloanPb.VendorResponseData{
					Data: &preapprovedloanPb.VendorResponseData_ApplicantLookupResponse{
						ApplicantLookupResponse: &liquiloansPb.ApplicantLookupResponse{
							Data: &liquiloansPb.ApplicantLookupResponse_Data{
								ApplicantId:   int64(applicantID),
								Name:          req.GetName(),
								Email:         mask.GetMaskedString(mask.DontMaskFirstTwoAndLastTwoChars, req.GetEmail()),
								ContactNumber: mask.GetMaskedString(mask.DontMaskLastFourChars, req.GetContactNumber()),
								Pan:           req.GetPan(),
								Details: &liquiloansPb.ApplicantLookupResponse_Data_Details{
									Banking:    false,
									Address:    false,
									Employment: false,
								},
								Activation: &liquiloansPb.ApplicantLookupResponse_Data_Activation{
									Mandate:   false,
									Agreement: false,
								},
							},
							Message:  "Applicant credit limit exist",
							Code:     200,
							Status:   true,
							Checksum: "",
						},
					},
				},
			})
			if err1 != nil {
				logger.Error(ctx, "internal error, unable to create Applicant Lookup entry in DB", zap.Error(err))
				return err1
			}
			_, err2 := s.vendorResponseDao.Create(ctx, &preapprovedloanPb.VendorResponse{
				Id:                 strconv.Itoa(applicantID),
				Vendor:             commonvgpb.Vendor_LIQUILOANS,
				VendorResponseType: preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_INSTANT_LOAN_OFFER,
				ResponseData: &preapprovedloanPb.VendorResponseData{
					Data: &preapprovedloanPb.VendorResponseData_CreditLineDetailsResponse{
						CreditLineDetailsResponse: &liquiloansPb.GetCreditLineDetailsResponse{
							Data: &liquiloansPb.GetCreditLineDetailsResponse_Data{
								CreditLineDetails: &liquiloansPb.GetCreditLineDetailsResponse_Data_CreditLineDetails{
									ApplicantId:    strconv.Itoa(applicantID),
									UpperLimit:     1000000,
									AvailableLimit: 1000000,
									BlockLimit:     10000.4502,
								},
								CreditLineSchemes: []*liquiloansPb.GetCreditLineDetailsResponse_Data_CreditLineSchemes{
									{
										Roi:               roi,
										RoiType:           "Flat",
										MaxEmiAllowed:     maxEmiAllowed,
										MinTenure:         minTenure,
										MaxTenure:         maxTenure,
										MinDrawdownAmount: minAmount,
										MaxDrawdownAmount: 50000,
										EmiDueDate:        "2023-04-05",
										PfFees:            2,
										PfType:            "Percentage",
										TenureFrequency:   "Monthly",
									},
								},
							},
							Message: "Details found. Application is in progress",
							Code:    200,
							Status:  true,
						},
					},
				},
			})
			if err2 != nil {
				logger.Error(ctx, "internal error, unable to create Credit Limit entry in DB", zap.Error(err))
				return err1
			}
			return nil
		})
		if txnErr != nil {
			return &liquiloansPb.AddPersonalDetailsResponse{
				Code:     400,
				Checksum: "",
				Message:  "Applicant creation failed",
				Status:   false,
			}, nil
		}
		stubResp.GetData().Name = req.GetName()
		stubResp.GetData().Email = req.GetEmail()
		stubResp.GetData().ContactNumber = req.GetContactNumber()
		stubResp.GetData().ApplicantId = int64(applicantID)
		return stubResp, nil
	} else if err != nil {
		simulator.SetHttpCode(ctx, 401)
		return &liquiloansPb.AddPersonalDetailsResponse{
			Status:  false,
			Message: "Service Unavailable",
			Code:    401,
		}, nil
	} else {
		// if record is found, return the "duplicate PAN" response from Db
		simulator.SetHttpCode(ctx, 400)
		return &liquiloansPb.AddPersonalDetailsResponse{
			Status:  true,
			Message: "Applicant already exist with provided PAN details",
			Code:    400,
		}, nil
	}
}

func (s *Service) AddBankingDetailsLl(ctx context.Context, req *liquiloansPb.AddBankingDetailsRequest) (*liquiloansPb.AddDetailsResponse, error) {
	pan, err := s.getPanForApplicantIdLl(req.GetApplicantId())
	if err != nil {
		return &liquiloansPb.AddDetailsResponse{
			Status:  false,
			Message: "Service Unavailable",
			Code:    401,
		}, nil
	}
	stubResp, err := getApiResponse[*liquiloansPb.AddDetailsResponse](&s.responseDataMap.llDataMap, pan, bankingDetailsRequest)
	if err != nil {
		return &liquiloansPb.AddDetailsResponse{
			Status:  false,
			Message: "Service Unavailable",
			Code:    401,
		}, nil
	}
	return stubResp, nil
}

func (s *Service) AddAddressDetailsLl(ctx context.Context, req *liquiloansPb.AddAddressDetailsRequest) (*liquiloansPb.AddDetailsResponse, error) {
	pan, err := s.getPanForApplicantIdLl(req.GetApplicantId())
	if err != nil {
		return &liquiloansPb.AddDetailsResponse{
			Status:  false,
			Message: "Service Unavailable",
			Code:    401,
		}, nil
	}
	stubResp, err := getApiResponse[*liquiloansPb.AddDetailsResponse](&s.responseDataMap.llDataMap, pan, addressDetailsRequest)
	if err != nil {
		return &liquiloansPb.AddDetailsResponse{
			Status:  false,
			Message: "Service Unavailable",
			Code:    401,
		}, nil
	}

	// create applicant_status response with "created" status after all the details are added
	_, err = s.vendorResponseDao.Create(ctx, &preapprovedloanPb.VendorResponse{
		Id:                 req.GetApplicantId(),
		Vendor:             commonvgpb.Vendor_LIQUILOANS,
		VendorResponseType: preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_APPLICANT_STATUS_RESPONSE,
		ResponseData: &preapprovedloanPb.VendorResponseData{
			Data: &preapprovedloanPb.VendorResponseData_ApplicantStatusResponse{
				ApplicantStatusResponse: &liquiloansPb.GetLimitResponse{
					Status:  true,
					Message: "Applicant credit limit exist",
					Data: &liquiloansPb.GetLimitResponse_Data{
						Status: "Created",
					},
					Code:     200,
					Checksum: "",
				},
			},
		},
	})
	if err != nil && err != epifierrors.ErrDuplicateEntry {
		logger.Error(context.Background(), "internal error, unable to create Applicant status entry in DB", zap.Error(err))
	}
	if err == epifierrors.ErrDuplicateEntry {
		appStatusRes, resErr := s.vendorResponseDao.GetById(context.Background(), req.GetApplicantId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_APPLICANT_STATUS_RESPONSE, commonvgpb.Vendor_LIQUILOANS)
		if resErr != nil {
			logger.Error(context.Background(), "internal error, unable to fetch applicant status response vendor response", zap.Error(resErr))
		}
		appStatusRes.GetResponseData().GetApplicantStatusResponse().GetData().Status = "Created"
		resErr = s.vendorResponseDao.Update(context.Background(), appStatusRes)
		if resErr != nil {
			logger.Error(context.Background(), "internal error, unable to update applicant status", zap.Error(resErr))
		}
	}
	simulator.SetHttpCode(ctx, int(stubResp.GetCode()))
	return stubResp, nil
}

func (s *Service) AddEmploymentDetailsLl(ctx context.Context, req *liquiloansPb.AddEmploymentDetailsRequest) (*liquiloansPb.AddDetailsResponse, error) {
	pan, err := s.getPanForApplicantIdLl(req.GetApplicantId())
	if err != nil {
		return &liquiloansPb.AddDetailsResponse{
			Status:  false,
			Message: "Service Unavailable",
			Code:    401,
		}, nil
	}
	stubResp, err := getApiResponse[*liquiloansPb.AddDetailsResponse](&s.responseDataMap.llDataMap, pan, employmentDetailsRequest)
	if err != nil {
		return &liquiloansPb.AddDetailsResponse{
			Status:  false,
			Message: "Service Unavailable",
			Code:    401,
		}, nil
	}

	if stubResp.GetCode() != 200 {
		return stubResp, nil
	}

	daoResponse, err := s.vendorResponseDao.GetById(context.Background(), req.GetApplicantId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_INSTANT_LOAN_OFFER, commonvgpb.Vendor_LIQUILOANS)
	if err != nil {
		logger.Error(context.Background(), "unable to fetch loan offer details for the applicant", zap.Error(err))
	}
	// if early salary, we don't need this state
	if req.GetSid() != "EsSimSID" {
		daoResponse.GetResponseData().GetCreditLineDetailsResponse().GetData().GetCreditLineSchemes()[0].Roi = 18.0
		// if employment type is "BUSINESS OWNER", increase the Interest rate by 2%
		if req.GetOccupation() == "SelfEmployed" {
			daoResponse.GetResponseData().GetCreditLineDetailsResponse().GetData().GetCreditLineSchemes()[0].Roi += 2
		}
		err = s.vendorResponseDao.Update(ctx, daoResponse)
		if err != nil {
			logger.Error(ctx, "internal error, unable to update entry in DB", zap.Error(err))
		}
	}
	if !s.conf.ManuallyPassLiquiloansStages() {
		goroutine.Run(ctx, 60*time.Second, func(ctx context.Context) {
			time.Sleep(time.Second * 20)
			daoRes, daoErr := s.vendorResponseDao.GetById(context.Background(), req.GetApplicantId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_APPLICANT_STATUS_RESPONSE, commonvgpb.Vendor_LIQUILOANS)
			if daoErr != nil {
				logger.Error(ctx, "failed to fetch applicant status response from applicant id in simDb", zap.Error(daoErr))
				return
			}
			status := preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_STATUS_APPROVED
			if !s.getFieldStatusFromStubs(&s.responseDataMap.llDataMap, pan, applicationStatusField) {
				status = preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_STATUS_UNAPPROVED
			}
			s.updateLlApplicant(daoRes, status)
			err = s.vendorResponseDao.Update(ctx, daoRes)
			if err != nil {
				logger.ErrorNoCtx("internal error, unable to update entry in DB", zap.Error(err))
			}
		})
	}
	return &liquiloansPb.AddDetailsResponse{
		Status:  true,
		Message: "Employment details added successfully",
		Code:    200,
	}, nil
}

func (s *Service) ApplicantLookupLl(w http.ResponseWriter, req *http.Request) {
	data, err := ioutil.ReadAll(req.Body)
	if err != nil {
		logger.ErrorNoCtx("failed to read request body", zap.Error(err))
		return
	}
	var reqBody liquiloansPb.ApplicantLookupRequest
	if err = json.Unmarshal(data, &reqBody); err != nil {
		logger.ErrorNoCtx("failed to unmarshal request into struct", zap.Error(err))
		return
	}
	var resData *liquiloansPb.ApplicantLookupResponse
	stubResp, err := getApiResponse[*liquiloansPb.ApplicantLookupResponse](&s.responseDataMap.llDataMap, reqBody.GetPan(), "")
	if err != nil {
		resData = &liquiloansPb.ApplicantLookupResponse{
			Status:  false,
			Message: "Service Unavailable.",
			Code:    400,
		}
		marshalProtoAndAddHeader(resData, w, int(resData.GetCode()))
		return
	}

	if stubResp.GetCode() != 200 {
		marshalProtoAndAddHeader(stubResp, w, int(stubResp.GetCode()))
		return
	}

	daoResponse, err := s.vendorResponseDao.GetById(context.Background(), reqBody.GetPan(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_APPLICANT_LOOKUP, commonvgpb.Vendor_LIQUILOANS)
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		resData = &liquiloansPb.ApplicantLookupResponse{
			Status:  false,
			Message: "No applicant found with the details provided",
			Code:    400,
		}
		w.WriteHeader(400)
	} else if err != nil {
		resData = &liquiloansPb.ApplicantLookupResponse{
			Status:  false,
			Message: "Service Unavailable.",
			Code:    400,
		}
		w.WriteHeader(500)
	} else {
		resData = daoResponse.GetResponseData().GetApplicantLookupResponse()
	}
	res, err := json.Marshal(resData)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal ApplicantLookupResponse", zap.Error(err))
		return
	}
	w.Write(res)
	return
}

func (s *Service) GetMandateLinkLl(w http.ResponseWriter, req *http.Request) {
	data, err := ioutil.ReadAll(req.Body)
	if err != nil {
		logger.ErrorNoCtx("failed to read request body", zap.Error(err))
		return
	}
	var reqBody liquiloansPb.GetMandateLinkRequest
	if err = json.Unmarshal(data, &reqBody); err != nil {
		logger.ErrorNoCtx("failed to unmarshal request into struct", zap.Error(err))
		return
	}

	failResp := &liquiloansPb.GetMandateLinkResponse{
		Status:  false,
		Message: "Service Unavailable",
		Code:    401,
	}
	pan, err := s.getPanForApplicantIdLl(reqBody.GetApplicantId())
	if err != nil {
		marshalProtoAndAddHeader(failResp, w, int(failResp.GetCode()))
		return
	}
	var resData *liquiloansPb.GetMandateLinkResponse
	stubResp, err := getApiResponse[*liquiloansPb.GetMandateLinkResponse](&s.responseDataMap.llDataMap, pan, "")
	if err != nil {
		marshalProtoAndAddHeader(failResp, w, int(failResp.GetCode()))
		return
	}

	if stubResp.GetCode() != 200 {
		marshalProtoAndAddHeader(stubResp, w, int(stubResp.GetCode()))
		return
	}

	daoRes, err := s.vendorResponseDao.GetById(context.Background(), reqBody.GetApplicantId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_INSTANT_LOAN_OFFER, commonvgpb.Vendor_LIQUILOANS)
	// check if applicant_id is registered or not. if not found, return "not found" response
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		resData = &liquiloansPb.GetMandateLinkResponse{
			Status:  false,
			Message: "Failed to fetch Mandate link, Please verify all required details and try again",
			Code:    400,
		}
		w.WriteHeader(401)
	} else if err != nil {
		marshalProtoAndAddHeader(failResp, w, int(failResp.GetCode()))
		return
	} else {
		// if record is found, return the success response from Db and create a new entry for mandate response as pending
		_, err = s.vendorResponseDao.Create(context.Background(), &preapprovedloanPb.VendorResponse{
			Id:                 daoRes.GetId(),
			Vendor:             commonvgpb.Vendor_LIQUILOANS,
			VendorResponseType: preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_MANDATE_RESPONSE,
			ResponseData: &preapprovedloanPb.VendorResponseData{
				Data: &preapprovedloanPb.VendorResponseData_MandateStatusResponse{
					MandateStatusResponse: &liquiloansPb.GetMandateStatusResponse{
						Data: &liquiloansPb.GetMandateStatusResponse_Data{
							Status: "PENDING",
						},
						Message: "Status Fetched Successfully",
						Code:    200,
						Status:  true,
					},
				},
			},
		})
		if err != nil && err != epifierrors.ErrDuplicateEntry {
			logger.Error(context.Background(), "internal error, unable to create Mandate status entry in DB", zap.Error(err))
			return
		}
		// if mandate response already exists, set it to "pending" state as a new loan application is in process
		if err == epifierrors.ErrDuplicateEntry {
			mandateRes, resErr := s.vendorResponseDao.GetById(context.Background(), daoRes.GetId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_MANDATE_RESPONSE, commonvgpb.Vendor_LIQUILOANS)
			if resErr != nil {
				logger.Error(context.Background(), "internal error, unable to fetch Mandate response vendor response", zap.Error(resErr))
				return
			}
			mandateRes.GetResponseData().GetMandateStatusResponse().GetData().Status = "Pending"
			resErr = s.vendorResponseDao.Update(context.Background(), mandateRes)
			if resErr != nil {
				logger.Error(context.Background(), "internal error, unable to update Mandate status", zap.Error(resErr))
				return
			}
		}
		resData = stubResp
	}
	res, err := json.Marshal(resData)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal ApplicantLookupResponse", zap.Error(err))
		return
	}
	w.Write(res)
	return
}

func (s *Service) GetMandateStatusLl(w http.ResponseWriter, req *http.Request) {
	data, err := ioutil.ReadAll(req.Body)
	if err != nil {
		logger.ErrorNoCtx("failed to read request body", zap.Error(err))
		return
	}
	var reqBody liquiloansPb.GetMandateStatusRequest
	if err = json.Unmarshal(data, &reqBody); err != nil {
		logger.ErrorNoCtx("failed to unmarshal request into struct", zap.Error(err))
		return
	}

	failResp := &liquiloansPb.GetMandateStatusResponse{
		Status:  false,
		Message: "Service Unavailable",
		Code:    500,
	}
	pan, err := s.getPanForApplicantIdLl(reqBody.GetApplicantId())
	if err != nil {
		marshalProtoAndAddHeader(failResp, w, int(failResp.GetCode()))
		return
	}
	var resData *liquiloansPb.GetMandateStatusResponse
	daoRes, daoErr := s.vendorResponseDao.GetById(context.Background(), reqBody.GetApplicantId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_MANDATE_RESPONSE, commonvgpb.Vendor_LIQUILOANS)
	if daoErr != nil && errors.Is(daoErr, epifierrors.ErrRecordNotFound) {
		// if applicant not found, return initiated
		resData = &liquiloansPb.GetMandateStatusResponse{
			Data: &liquiloansPb.GetMandateStatusResponse_Data{
				Status: "PENDING",
			},
			Message: "Status Fetched Successfully",
			Code:    200,
			Status:  true,
		}
	} else if daoErr != nil {
		marshalProtoAndAddHeader(failResp, w, int(failResp.GetCode()))
		return
	} else {
		currentTime := time.Now()
		if currentTime.Sub(daoRes.GetUpdatedAt().AsTime()) > 90*time.Second && !s.conf.ManuallyPassLiquiloansStages() {
			if !s.getFieldStatusFromStubs(&s.responseDataMap.llDataMap, pan, mandateStatusField) {
				s.updateLlMandate(daoRes, preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_STATUS_UNAPPROVED)
				err = s.vendorResponseDao.Update(context.Background(), daoRes)
				if err != nil {
					logger.ErrorNoCtx("internal error, unable to update entry in DB", zap.Error(err))
				}
			} else {
				s.updateLlMandate(daoRes, preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_STATUS_APPROVED)
				err = s.vendorResponseDao.Update(context.Background(), daoRes)
				if err != nil {
					logger.ErrorNoCtx("internal error, unable to update entry in DB", zap.Error(err))
				}
			}
		}
		// return success response if applicant id is present.
		resData = daoRes.GetResponseData().GetMandateStatusResponse()
	}
	res, err := json.Marshal(resData)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal GetMandateStatusResponse", zap.Error(err))
		return
	}
	w.Write(res)
	return
}

func (s *Service) MakeDrawdownLl(ctx context.Context, req *liquiloansPb.MakeDrawdownRequest) (*liquiloansPb.MakeDrawdownResponse, error) {
	failResp := &liquiloansPb.MakeDrawdownResponse{
		Status:  false,
		Message: "Service Unavailable",
		Code:    401,
	}
	pan, err := s.getPanForApplicantIdLl(req.GetApplicantId())
	if err != nil {
		return failResp, nil
	}
	stubResp, err := getApiResponse[*liquiloansPb.MakeDrawdownResponse](&s.responseDataMap.llDataMap, pan, "")
	if err != nil {
		return failResp, nil
	}

	if stubResp.GetCode() != 200 {
		return stubResp, nil
	}

	if req.GetTenure() == 0 {
		req.Tenure = 1
	}
	daoResponse, err := s.vendorResponseDao.GetById(context.Background(), req.GetApplicantId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_INSTANT_LOAN_OFFER, commonvgpb.Vendor_LIQUILOANS)
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		simulator.SetHttpCode(ctx, 400)
		return &liquiloansPb.MakeDrawdownResponse{
			Status:  false,
			Message: "Credit Line Applicant Details Not Found",
			Code:    400,
		}, nil
	} else if err != nil {
		simulator.SetHttpCode(ctx, 401)
		return &liquiloansPb.MakeDrawdownResponse{
			Status:  false,
			Message: "Service Unavailable",
			Code:    401,
		}, nil
	} else {
		availableLimit := daoResponse.GetResponseData().GetCreditLineDetailsResponse().GetData().GetCreditLineDetails().GetAvailableLimit()
		if availableLimit == -1 {
			simulator.SetHttpCode(ctx, 400)
			return &liquiloansPb.MakeDrawdownResponse{
				Status:  false,
				Message: "Drawdown cannot be performed for unapproved account",
				Code:    400,
			}, nil
		}
		if req.GetAmount() > availableLimit {
			simulator.SetHttpCode(ctx, 400)
			return &liquiloansPb.MakeDrawdownResponse{
				Status:  false,
				Message: "Insufficient Available Limit",
				Code:    400,
			}, nil
		}
		if req.GetAmount() > daoResponse.GetResponseData().GetCreditLineDetailsResponse().GetData().GetCreditLineSchemes()[0].GetMaxDrawdownAmount() ||
			req.GetAmount() < daoResponse.GetResponseData().GetCreditLineDetailsResponse().GetData().GetCreditLineSchemes()[0].GetMinDrawdownAmount() {
			simulator.SetHttpCode(ctx, 400)
			return &liquiloansPb.MakeDrawdownResponse{
				Status:  false,
				Message: "Drawdown amount is not within the prescribed drawdown limits of this account",
				Code:    400,
			}, nil
		}
		daoResponse.GetResponseData().GetCreditLineDetailsResponse().GetData().GetCreditLineDetails().AvailableLimit -= req.GetAmount()
		daoResponse.GetResponseData().GetCreditLineDetailsResponse().GetData().GetCreditLineDetails().BlockLimit += req.GetAmount()
		err := s.vendorResponseDao.Update(ctx, daoResponse)
		if err != nil {
			logger.Error(ctx, "internal error, unable to update entry in DB", zap.Error(err))
		}
		loanId := rand.Intn(********)
		_, err = s.vendorResponseDao.Create(ctx, &preapprovedloanPb.VendorResponse{
			ApplicationNumber:  strconv.Itoa(loanId),
			Id:                 req.GetUrn(),
			Vendor:             commonvgpb.Vendor_LIQUILOANS,
			VendorResponseType: preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_LOAN_STATUS_RESPONSE,
			ResponseData: &preapprovedloanPb.VendorResponseData{
				Data: &preapprovedloanPb.VendorResponseData_LoanStatusResponse{
					LoanStatusResponse: &liquiloansPb.GetLoanStatusResponse{
						Data: &liquiloansPb.GetLoanStatusResponse_Data{
							Status: &liquiloansPb.GetLoanStatusResponse_Data_Status{
								LoanId:              int32(loanId),
								LoanCode:            "PL00" + strconv.Itoa(loanId),
								Status:              "Created",
								OrderId:             "na",
								Amount:              req.GetAmount(),
								ProductAmount:       req.GetAmount(),
								DisbursedAmount:     0,
								Tenure:              req.GetTenure(),
								Roi:                 daoResponse.GetResponseData().GetCreditLineDetailsResponse().GetData().GetCreditLineSchemes()[0].GetRoi(),
								LastStatusTimestamp: time.Now().Format(layout),
								Emi:                 s.calculateEmi(req.GetAmount(), req.GetTenure(), daoResponse.GetResponseData().GetCreditLineDetailsResponse().GetData().GetCreditLineSchemes()[0].GetRoi()),
							},
						},
						Message:  "Data found!",
						Code:     200,
						Status:   true,
						Checksum: "",
					},
				},
			},
		})
		if err != nil {
			logger.Error(ctx, "internal error, unable to update entry in DB", zap.Error(err))
		}
		stubResp.GetData().ApplicantId = req.GetApplicantId()
		stubResp.GetData().LoanId = int64(loanId)
		stubResp.GetData().Urn = req.GetUrn()

		return stubResp, nil
	}
}

func (s *Service) GetLoanStatusLl(ctx context.Context, req *liquiloansPb.GetLoanStatusRequest) (*liquiloansPb.GetLoanStatusResponse, error) {
	var daoResponse *simPreapprovedPb.VendorResponse
	var err error
	if req.GetUrn() != "" {
		daoResponse, err = s.vendorResponseDao.GetById(ctx, req.GetUrn(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_LOAN_STATUS_RESPONSE, commonvgpb.Vendor_LIQUILOANS)
	} else {
		daoResponse, err = s.vendorResponseDao.GetByApplication(ctx, req.GetApplicationId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_LOAN_STATUS_RESPONSE, commonvgpb.Vendor_LIQUILOANS)
	}
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		simulator.SetHttpCode(ctx, 400)
		return &liquiloansPb.GetLoanStatusResponse{
			Code:    400,
			Message: "Data not found",
			Status:  false,
		}, nil
	} else if err != nil {
		simulator.SetHttpCode(ctx, 401)
		return &liquiloansPb.GetLoanStatusResponse{
			Code:    401,
			Message: "Service Unavailable",
			Status:  false,
		}, nil
	} else {
		return daoResponse.GetResponseData().GetLoanStatusResponse(), nil
	}
}

func (s *Service) UpdateEntityDevActionLl(ctx context.Context, req *preapprovedloanPb.UpdateEntityDevActionLlRequest) (*preapprovedloanPb.DevActionMockResponse, error) {
	entityToVendorResType := map[preapprovedloanPb.UpdateEntityDevActionLlRequest_Entity]preapprovedloanPb.VendorResponseType{
		preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_APPLICANT: preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_APPLICANT_STATUS_RESPONSE,
		preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_MANDATE:   preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_MANDATE_RESPONSE,
		preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_LOAN:      preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_LOAN_STATUS_RESPONSE,
	}
	switch req.GetEntity() {
	case preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_APPLICANT, preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_MANDATE:
		daoResponse, err := s.vendorResponseDao.GetById(context.Background(), req.GetIdentifier(), entityToVendorResType[req.GetEntity()], commonvgpb.Vendor_LIQUILOANS)
		if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
			// try to find with vendor as IDFC
			daoResponseIdfc, err2 := s.vendorResponseDao.GetById(context.Background(), req.GetIdentifier(), entityToVendorResType[req.GetEntity()], commonvgpb.Vendor_IDFC)
			if err2 == nil {
				s.updateIdfcMandatedaoRes(daoResponseIdfc, req.GetStatus())
				err = s.vendorResponseDao.Update(ctx, daoResponseIdfc)
				if err != nil {
					logger.Error(ctx, "internal error, unable to update entry in DB", zap.Error(err))
					return &preapprovedloanPb.DevActionMockResponse{
						Status: rpc.StatusInternal(),
					}, nil
				}
				return &preapprovedloanPb.DevActionMockResponse{
					Status: rpc.StatusOk(),
				}, nil
			}
			logger.Error(ctx, "failed, applicant ID not found", zap.Error(err))
			return &preapprovedloanPb.DevActionMockResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		} else if err != nil {
			return &preapprovedloanPb.DevActionMockResponse{
				Status: rpc.StatusInternal(),
			}, nil
		} else {
			if req.GetEntity() == preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_APPLICANT {
				s.updateLlApplicant(daoResponse, req.GetStatus())
			} else {
				s.updateLlMandate(daoResponse, req.GetStatus())
			}
			err = s.vendorResponseDao.Update(ctx, daoResponse)
			if err != nil {
				logger.Error(ctx, "internal error, unable to update entry in DB", zap.Error(err))
			}
		}
	case preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_LOAN:
		daoResponse, err := s.vendorResponseDao.GetByApplication(context.Background(), req.GetIdentifier(), entityToVendorResType[req.GetEntity()], commonvgpb.Vendor_LIQUILOANS)
		if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed, application ID not found", zap.Error(err))
			return &preapprovedloanPb.DevActionMockResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		} else if err != nil {
			return &preapprovedloanPb.DevActionMockResponse{
				Status: rpc.StatusInternal(),
			}, nil
		} else {
			s.updateLlLoan(daoResponse, req.GetStatus())
			err = s.vendorResponseDao.Update(ctx, daoResponse)
			if err != nil {
				logger.Error(ctx, "internal error, unable to update entry in DB", zap.Error(err))
			}
		}
	default:
		logger.Error(ctx, "failed, invalid entity passed")
		return &preapprovedloanPb.DevActionMockResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}
	return &preapprovedloanPb.DevActionMockResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) GetPdfAgreementLl(ctx context.Context, req *liquiloansPb.GetPdfAgreementRequest) (*liquiloansPb.GetPdfAgreementResponse, error) {
	failResp := &liquiloansPb.GetPdfAgreementResponse{
		Status:  false,
		Message: "Service Unavailable",
		Code:    401,
	}
	pan, err := s.getPanForApplicantIdLl(req.GetApplicantId())
	if err != nil {
		simulator.SetHttpCode(ctx, int(failResp.GetCode()))
		return failResp, nil
	}
	stubResp, err := getApiResponse[*liquiloansPb.GetPdfAgreementResponse](&s.responseDataMap.llDataMap, pan, "")
	if err != nil {
		simulator.SetHttpCode(ctx, int(failResp.GetCode()))
		return failResp, nil
	}

	if stubResp.GetCode() != 200 {
		simulator.SetHttpCode(ctx, int(stubResp.GetCode()))
		return stubResp, nil
	}

	_, err = s.vendorResponseDao.GetByApplication(ctx, req.GetApplicationId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_LOAN_STATUS_RESPONSE, commonvgpb.Vendor_LIQUILOANS)
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		simulator.SetHttpCode(ctx, 400)
		return &liquiloansPb.GetPdfAgreementResponse{
			Code:    400,
			Message: "Agreement Loan application details not found in the system. Please provide valid parameters.",
			Status:  false,
		}, nil
	} else if err != nil {
		return failResp, nil
	} else {
		docId := rand.Intn(9999) + 20000
		stubResp.GetData().DocId = int32(docId)
		stubResp.GetData().Pdf = dummay_data.PdfAgreementData
		return stubResp, nil
	}
}

func (s *Service) SendBorrowerAgreementOtpLl(ctx context.Context, req *liquiloansPb.SendBorrowerAgreementOtpRequest) (*liquiloansPb.SendBorrowerAgreementOtpResponse, error) {

	daoRes, err := s.vendorResponseDao.GetByApplication(ctx, req.GetApplicationId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_LOAN_STATUS_RESPONSE, commonvgpb.Vendor_LIQUILOANS)
	// if loan_id not found, return borrower details not found msg
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		simulator.SetHttpCode(ctx, 400)
		return &liquiloansPb.SendBorrowerAgreementOtpResponse{
			Code:    400,
			Message: "Borrower details not found in the system. Please try again later with valid details.",
			Status:  false,
		}, nil
	} else if err != nil {
		simulator.SetHttpCode(ctx, 401)
		return &liquiloansPb.SendBorrowerAgreementOtpResponse{
			Code:    401,
			Message: "Service Unavailable",
			Status:  false,
		}, nil
	} else {
		// if loan_id is already disbursed, return already signed msg
		if daoRes.GetResponseData().GetLoanStatusResponse().GetData().GetStatus().GetStatus() == "Disbursed" {
			simulator.SetHttpCode(ctx, 400)
			return &liquiloansPb.SendBorrowerAgreementOtpResponse{
				Code:    400,
				Message: "Agreement Already Exists in Accepted Status",
				Status:  false,
			}, nil
		}

		// return the OTP successfully sent msg
		return &liquiloansPb.SendBorrowerAgreementOtpResponse{
			Code:    200,
			Message: "OTP send Successfully",
			Status:  true,
		}, nil
	}
}

func (s *Service) VerifyBorrowerAgreementOtpLl(ctx context.Context, req *liquiloansPb.VerifyBorrowerAgreementOtpRequest) (*liquiloansPb.VerifyBorrowerAgreementOtpResponse, error) {
	failResp := &liquiloansPb.VerifyBorrowerAgreementOtpResponse{
		Status:  false,
		Message: "Service Unavailable",
		Code:    401,
	}

	pan, err := s.getPanForApplicantIdLl(req.GetApplicantId())
	if err != nil {
		return failResp, nil
	}
	daoRes, err := s.vendorResponseDao.GetByApplication(ctx, req.GetApplicationId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_LOAN_STATUS_RESPONSE, commonvgpb.Vendor_LIQUILOANS)
	// if loan_id not found, return borrower details not found msg
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		simulator.SetHttpCode(ctx, 400)
		return &liquiloansPb.VerifyBorrowerAgreementOtpResponse{
			Code:    400,
			Message: "Unable to verify OTP. Please try again.",
			Status:  false,
		}, nil
	} else if err != nil {
		simulator.SetHttpCode(ctx, 401)
		return &liquiloansPb.VerifyBorrowerAgreementOtpResponse{
			Code:    401,
			Message: "Service Unavailable",
			Status:  false,
		}, nil
	} else {
		// if wrong otp is entered, return invalid otp msg
		if req.GetOtp() != "123456" {
			simulator.SetHttpCode(ctx, 400)
			return &liquiloansPb.VerifyBorrowerAgreementOtpResponse{
				Code:    400,
				Message: "Invalid OTP. Please try again.",
				Status:  false,
			}, nil
		}
		// return the success msg and mark the loan as "Disbursed"
		daoRes.GetResponseData().GetLoanStatusResponse().GetData().GetStatus().Status = "Ready To Disbursed"
		err := s.vendorResponseDao.Update(ctx, daoRes)
		if err != nil {
			logger.Error(ctx, "internal error, unable to update entry in DB", zap.Error(err))
		}

		if !s.conf.ManuallyPassLiquiloansStages() {
			goroutine.Run(ctx, 60*time.Second, func(ctx context.Context) {
				time.Sleep(time.Second * 20)
				status := preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_STATUS_APPROVED
				if !s.getFieldStatusFromStubs(&s.responseDataMap.llDataMap, pan, borrowerAgreementOtpField) {
					status = preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_STATUS_UNAPPROVED
				}
				s.updateLlLoan(daoRes, status)
				err = s.vendorResponseDao.Update(context.Background(), daoRes)
				if err != nil {
					logger.ErrorNoCtx("internal error, unable to update entry in DB", zap.Error(err))
				}
			})
		}
		return &liquiloansPb.VerifyBorrowerAgreementOtpResponse{
			Code:    200,
			Message: "You have successfully signed your loan application agreement",
			Status:  true,
		}, nil
	}
}

func (s *Service) VerifyAndDownloadCkyc(ctx context.Context, req *liquiloansPb.VerifyAndDownloadCkycRequest) (*liquiloansPb.VerifyAndDownloadCkycResponse, error) {
	// hardcoded PAN that will return false for ckyc record
	if req.GetIdNo() == "**********" {
		simulator.SetHttpCode(ctx, 400)
		return &liquiloansPb.VerifyAndDownloadCkycResponse{
			Status:  false,
			Message: "No record found",
			Code:    400,
		}, nil
	}
	// return true for all other PAN
	return &liquiloansPb.VerifyAndDownloadCkycResponse{
		Status:  true,
		Message: "CKyc fetched successfully",
		Code:    200,
		Data: &liquiloansPb.VerifyAndDownloadCkycResponse_Data{
			PersonalDetails: &liquiloansPb.VerifyAndDownloadCkycResponse_Data_PersonalDetails{
				CkycNo:     "5647574784",
				Name:       "Mr Dummy User",
				KycDate:    "06-02-2021",
				Gender:     "M",
				Dob:        "06-02-1979",
				Fname:      "Rohan",
				Lname:      "Singh",
				FatherName: "Mr Abhay  Singh",
				MotherName: "",
				MobNum:     "9123456789",
				Email:      "<EMAIL>",
				Address:    &liquiloansPb.VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address{},
				ImageType:  "jpg",
				Photo:      dummay_data.CkycImageBase64,
			},
		},
	}, nil
}

func (s *Service) GetApplicantStatus(w http.ResponseWriter, req *http.Request) {
	data, err := ioutil.ReadAll(req.Body)
	if err != nil {
		logger.ErrorNoCtx("failed to read request body", zap.Error(err))
		return
	}
	var reqBody liquiloansPb.GetLimitRequest
	if err = json.Unmarshal(data, &reqBody); err != nil {
		logger.ErrorNoCtx("failed to unmarshal request into struct", zap.Error(err))
		return
	}
	failedResp := &liquiloansPb.GetLimitResponse{
		Status:  false,
		Message: "Service Unavailable",
		Code:    401,
	}
	pan, err := s.getPanForApplicantIdLl(reqBody.GetApplicantId())
	if err != nil {
		marshalProtoAndAddHeader(failedResp, w, int(failedResp.GetCode()))
		return
	}
	stubResp, err := getApiResponse[*liquiloansPb.GetLimitResponse](&s.responseDataMap.llDataMap, pan, "")
	if err != nil {
		marshalProtoAndAddHeader(failedResp, w, int(failedResp.GetCode()))
		return
	}
	if stubResp.GetCode() != 200 {
		res, resErr := json.Marshal(stubResp)
		if resErr != nil {
			logger.ErrorNoCtx("failed to marshal ApplicantStatusResponse", zap.Error(resErr))
			return
		}
		w.WriteHeader(int(stubResp.GetCode()))
		w.Write(res)
		return
	}

	daoRes, err := s.vendorResponseDao.GetById(context.Background(), reqBody.GetApplicantId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_APPLICANT_STATUS_RESPONSE, commonvgpb.Vendor_LIQUILOANS)
	var resData *liquiloansPb.GetLimitResponse
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		resData = &liquiloansPb.GetLimitResponse{
			Status:  false,
			Message: "Applicant id not found in system",
			Code:    400,
		}
		w.WriteHeader(400)
	} else if err != nil {
		resData = failedResp
		w.WriteHeader(401)
	} else {
		resData = daoRes.GetResponseData().GetApplicantStatusResponse()
	}
	res, err := json.Marshal(resData)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal ApplicantStatusResponse", zap.Error(err))
		return
	}
	w.Write(res)
	return
}

func (s *Service) updateLlApplicant(daoRes *preapprovedloanPb.VendorResponse, status preapprovedloanPb.UpdateEntityDevActionLlRequest_EntityStatus) {
	if status == preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_STATUS_APPROVED {
		// if status is "Approved", mark the user as approved
		daoRes.GetResponseData().GetApplicantStatusResponse().Message = "Applicant credit limit exist"
		daoRes.GetResponseData().GetApplicantStatusResponse().Data = &liquiloansPb.GetLimitResponse_Data{
			Status: "Approved",
			UpperLimit: &liquiloansPb.GetLimitResponse_Data_LimitValue{
				Value:       100000,
				LastUpdated: "",
			},
			AvailableLimit: &liquiloansPb.GetLimitResponse_Data_LimitValue{
				Value:       100000,
				LastUpdated: "",
			},
		}
	} else {
		// if status is not "Approved", mark the user as unapproved and delete the credit line details
		daoRes.GetResponseData().GetApplicantStatusResponse().Message = "Applicant credit limit exist"
		daoRes.GetResponseData().GetApplicantStatusResponse().Data = &liquiloansPb.GetLimitResponse_Data{
			Status: "Rejected",
			UpperLimit: &liquiloansPb.GetLimitResponse_Data_LimitValue{
				Value:       -1,
				LastUpdated: "",
			},
			AvailableLimit: &liquiloansPb.GetLimitResponse_Data_LimitValue{
				Value:       -1,
				LastUpdated: "",
			},
		}
	}
}

func (s *Service) updateLlMandate(daoRes *preapprovedloanPb.VendorResponse, status preapprovedloanPb.UpdateEntityDevActionLlRequest_EntityStatus) {
	var mandateStatus string
	switch status {
	case preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_STATUS_APPROVED:
		mandateStatus = "SUCCESS"
	case preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_STATUS_UNAPPROVED:
		mandateStatus = "FAILED"
	default:
		mandateStatus = "INITIATED"
	}
	daoRes.GetResponseData().GetMandateStatusResponse().GetData().Status = mandateStatus
}

func (s *Service) updateLlLoan(daoRes *preapprovedloanPb.VendorResponse, status preapprovedloanPb.UpdateEntityDevActionLlRequest_EntityStatus) {
	var loanStatus string
	switch status {
	case preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_STATUS_APPROVED:
		loanStatus = "Disbursed"
	case preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_STATUS_UNAPPROVED:
		loanStatus = "Rejected"
	default:
		loanStatus = "Created"
	}
	daoRes.GetResponseData().GetLoanStatusResponse().GetData().GetStatus().Status = loanStatus
	daoRes.GetResponseData().GetLoanStatusResponse().GetData().GetStatus().DisbursedAmount = s.calculateDisbursedAmount(daoRes.GetResponseData().GetLoanStatusResponse().GetData().GetStatus().GetAmount())
}

func (s *Service) updateIdfcMandatedaoRes(daoRes *preapprovedloanPb.VendorResponse, status preapprovedloanPb.UpdateEntityDevActionLlRequest_EntityStatus) {
	var mandateStatus, responseMsg string
	switch status {
	case preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_STATUS_APPROVED:
		mandateStatus = "Success"
		responseMsg = "0300|success|NA|62891991071636742419|10010|**********|100000.00"
	case preapprovedloanPb.UpdateEntityDevActionLlRequest_ENTITY_STATUS_UNAPPROVED:
		mandateStatus = "Success"
		responseMsg = "0399|failure|Selected Bank Not Found|72042009731664618928|None|*********|2.00|"
	default:
		mandateStatus = "Failure"
		responseMsg = "No Response Found !!!"
	}
	daoRes.GetResponseData().GetIdfcMandateStatusResponse().Status = mandateStatus
	daoRes.GetResponseData().GetIdfcMandateStatusResponse().ResponseMsg = responseMsg
}

func (s *Service) calculateEmi(loanAmount float64, tenureMonths int32, rate float64) float64 {
	if rate == 0 {
		return loanAmount / float64(tenureMonths)
	}
	r := rate / (12 * 100)
	crp := math.Pow(1+r, float64(tenureMonths))
	emi := ((loanAmount * 100) * r * crp) / (crp - 1)
	return emi / 100
}

func (s *Service) calculateDisbursedAmount(loanAmount float64) float64 {
	charges := (1.18 * 2.0 * float64(loanAmount)) / 100
	return loanAmount - charges
}

func (s *Service) UploadDocument(w http.ResponseWriter, req *http.Request) {
	w.WriteHeader(200)
	resp, _ := json.Marshal(&liquiloansPb.UploadDocumentResponse{
		Status:  true,
		Message: "Doc uploaded successfully",
		Data: &liquiloansPb.UploadDocumentResponse_Data{
			ApplicantId:         123456,
			DocumentTypeId:      125,
			NbfcId:              1,
			Id:                  timestampPb.Now().GetSeconds(),
			FileName:            "KFS-" + "1234567" + ".pdf",
			FilePath:            "CustomerDocuments/KFS-" + "1234567" + ".pdf",
			IsPasswordProtected: "No",
			UpdatedAt:           datetime.TimestampToString(timestampPb.Now(), datetime.DATE_LAYOUT_YYYYMMDD, datetime.IST),
			CreatedAt:           datetime.TimestampToString(timestampPb.Now(), datetime.DATE_LAYOUT_YYYYMMDD, datetime.IST),
		},
		Code:     200,
		Checksum: "",
	})
	_, err := w.Write(resp)
	if err != nil {
		w.WriteHeader(401)
	}
}

func (s *Service) SaveCollection(ctx context.Context, req *liquiloansPb.SaveCollectionRequest) (*liquiloansPb.SaveCollectionResponse, error) {
	failResp := &liquiloansPb.SaveCollectionResponse{
		Status:  false,
		Code:    400,
		Message: "service unavailable",
	}
	daoResponse, err := s.vendorResponseDao.GetByApplication(ctx, req.GetLoanId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_LOAN_STATUS_RESPONSE, commonvgpb.Vendor_LIQUILOANS)
	if err != nil {
		return failResp, nil
	}
	paid, err := strconv.ParseFloat(req.GetPaymentSchedule()[0].GetPaidTotalAmount(), 32)
	if err != nil {
		return failResp, nil
	}
	newAmt := max(0, daoResponse.GetResponseData().GetLoanStatusResponse().GetData().GetStatus().GetAmount()-paid)
	daoResponse.GetResponseData().GetLoanStatusResponse().GetData().GetStatus().Amount = newAmt
	err = s.vendorResponseDao.Update(ctx, daoResponse)
	if err != nil {
		return failResp, nil
	}
	return &liquiloansPb.SaveCollectionResponse{
		Status: true,
		Data: &liquiloansPb.SaveCollectionResponse_Data{
			LoanId: req.GetLoanId(),
		},
		Code: 200,
	}, nil
}

func (s *Service) UpdateLead(ctx context.Context, req *liquiloansPb.UpdateLeadRequest) (*liquiloansPb.UpdateLeadResponse, error) {
	return &liquiloansPb.UpdateLeadResponse{
		Status: true,
		Data: &liquiloansPb.UpdateLeadResponse_Data{
			LoanId: strconv.Itoa(int(req.GetApplicationId())),
		},
		Code: 200,
	}, nil
}

func (s *Service) CancelLead(ctx context.Context, req *liquiloansPb.CancelLeadRequest) (*liquiloansPb.CancelLeadResponse, error) {
	// TODO: available limit: 1L, block limit: 0
	return &liquiloansPb.CancelLeadResponse{
		Status:  true,
		Message: "Loan status is successfully updated to withdrawn!",
		Data: &liquiloansPb.CancelLeadResponse_Data{
			ApplicationId: strconv.Itoa(int(req.GetApplicationId())),
			ApplicantId:   strconv.Itoa(int(req.GetApplicantId())),
		},
		Code:     "200",
		Checksum: req.GetChecksum(),
	}, nil
}

func (s *Service) ForeClosureDetails(ctx context.Context, req *liquiloansPb.ForeClosureDetailsRequest) (*liquiloansPb.ForeClosureDetailsResponse, error) {
	daoResponse, err := s.vendorResponseDao.GetByApplication(ctx, strconv.Itoa(int(req.GetApplicationId())), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_LOAN_STATUS_RESPONSE, commonvgpb.Vendor_LIQUILOANS)
	if err != nil {
		return &liquiloansPb.ForeClosureDetailsResponse{
			Status:  false,
			Code:    400,
			Message: "error in fetching foreclosure details",
		}, nil
	}

	return &liquiloansPb.ForeClosureDetailsResponse{
		Status: true,
		Data: &liquiloansPb.ForeClosureDetailsResponse_Data{
			TotalOutstanding: daoResponse.GetResponseData().GetLoanStatusResponse().GetData().GetStatus().GetAmount(),
			ApplicationId:    strconv.Itoa(int(req.GetApplicationId())),
		},
		Code:     200,
		Checksum: req.GetChecksum(),
	}, nil
}

func (s *Service) UpdateApplicantUdf(ctx context.Context, req *liquiloansPb.UpdateApplicantUdfRequest) (*liquiloansPb.UpdateApplicantUdfResponse, error) {
	return &liquiloansPb.UpdateApplicantUdfResponse{
		Status:  true,
		Code:    200,
		Message: "Updated succesfully",
	}, nil
}

func (s *Service) CreateRepaymentScheduleLl(ctx context.Context, req *liquiloansPb.CreateRepaymentScheduleRequest) (*liquiloansPb.CreateRepaymentScheduleResponse, error) {
	return &liquiloansPb.CreateRepaymentScheduleResponse{
		Status:  true,
		Code:    200,
		Message: "Updated succesfully",
	}, nil
}

func (s *Service) SaveChargesLl(ctx context.Context, req *liquiloansPb.SaveChargesRequest) (*liquiloansPb.SaveChargesResponse, error) {
	return &liquiloansPb.SaveChargesResponse{
		Status:  true,
		Code:    200,
		Message: "Updated succesfully",
	}, nil
}

func (s *Service) GetRepaymentScheduleLL(w http.ResponseWriter, req *http.Request) {
	data, err := ioutil.ReadAll(req.Body)
	if err != nil {
		logger.ErrorNoCtx("failed to read request body", zap.Error(err))
		return
	}
	var reqBody liquiloansPb.GetRepaymentScheduleRequest
	if err = json.Unmarshal(data, &reqBody); err != nil {
		logger.ErrorNoCtx("failed to unmarshal request into struct", zap.Error(err))
		return
	}
	loanId, _ := strconv.Atoi(reqBody.GetLoanId())

	daoResponse, err := s.vendorResponseDao.GetByApplication(context.Background(), reqBody.GetLoanId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_LOAN_STATUS_RESPONSE, commonvgpb.Vendor_LIQUILOANS)
	if err != nil {
		logger.ErrorNoCtx("failed to get dao response for loan status", zap.Error(err))
		return
	}
	dueAmmount := daoResponse.GetResponseData().GetLoanStatusResponse().GetData().GetStatus().GetAmount()
	var schedule *liquiloansPb.Schedule
	if dueAmmount == 0 {
		schedule = &liquiloansPb.Schedule{
			ApplicationId:                   int64(loanId),
			InstallmentNumber:               1,
			DueDate:                         "2024-12-05",
			DueAmount:                       0,
			PrincipalAmount:                 4952.67,
			InterestAmount:                  1333.33,
			PaymentStatus:                   "Paid",
			ReceivedDate:                    time.Now().Format("2006-01-02"),
			ReceivedAmount:                  6286,
			PaidPrincipalAmount:             4952.67,
			PaidInterestAmount:              1333.33,
			Lpi:                             0,
			OtherCharges:                    0,
			BounceCharges:                   0,
			PostPaymentPrincipalOutstanding: 0,
			PostPaymentInterestOutstanding:  0,
		}
	} else {
		schedule = &liquiloansPb.Schedule{
			ApplicationId:                   int64(loanId),
			InstallmentNumber:               1,
			DueDate:                         "2024-12-05",
			DueAmount:                       daoResponse.GetResponseData().GetLoanStatusResponse().GetData().GetStatus().GetAmount(),
			PrincipalAmount:                 4952.67,
			InterestAmount:                  1333.33,
			PaymentStatus:                   "Unpaid",
			ReceivedDate:                    "",
			ReceivedAmount:                  6286,
			PaidPrincipalAmount:             0,
			PaidInterestAmount:              0,
			Lpi:                             0,
			OtherCharges:                    0,
			BounceCharges:                   0,
			PostPaymentPrincipalOutstanding: 95047.33,
			PostPaymentInterestOutstanding:  0,
		}
	}
	resData := &liquiloansPb.GetRepaymentScheduleResponse{
		Status:  true,
		Message: "Repayment schedule fetched successfully.",
		Data:    &liquiloansPb.GetRepaymentScheduleResponse_Data{Schedule: []*liquiloansPb.Schedule{schedule}},
	}
	res, err := json.Marshal(resData)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal GetRepaymentSchedule", zap.Error(err))
		return
	}
	w.Write(res)
	return
}
