Application:
  Environment: "demo"
  Namespace: "demo-credit-report"
  TaskQueue: "demo-credit-report-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "master.demo-common-cache-redis.wqltco.aps1.cache.amazonaws.com:6379"
    Password: ""
    DB: 0

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "demo/cockroach/ca.crt"
    SSLClientCert: "demo/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "demo/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "credit-report-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "demo/cockroach/ca.crt"
    SSLClientCert: "demo/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "demo/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "credit-report-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

FeatureEngineeringPdgb:
  DbType: "PGDB"
  AppName: "credit-report-worker"
  StatementTimeout: 5s
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "demo/rds/postgres/feature_engineering_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

WorkflowUpdatePublisher:
  TopicName: "demo-celestial-workflow-update-topic"

CreditReportDownloadEventPublisher:
  TopicName: "demo-credit-report-download-event-topic"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    GoogleCloudProfilingServiceAccountKey: "demo/gcloud/profiling-service-account-key"
    RudderWriteKey: "demo/rudder/internal-writekey"
    TemporalCodecAesKey: "deploy/temporal/codec-encryption-key"

CreditReportFlattenPublisher:
  QueueName: "demo-lending-credit-report-flattening-queue"
