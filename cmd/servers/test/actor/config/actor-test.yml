ServerPorts:
  HttpPort: 9892

GrpcRatelimiterParams:
  RateLimitConfig:
    Namespace: "actor-rpc"
    ResourceMap:
      actor_actor_resolveotheractorpiandtimeline:
        Rate: 2000
        Period: 5s
        PriorityDistribution:
          IsEnabled: false
    RedisOptions:
      IsSecureRedis: false
      Options:
        Addr: "localhost:6379"
        Password: ""
        DB: 0

Databases:
  EpifiCRDB:
    AppName: "savings"
    DbType: "CRDB"
    StatementTimeout: 1s
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi_test"
    EnableDebug: true
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  PayPGDB:
    AppName: "pay"
    DbType: "PGDB"
    StatementTimeout: 5m
    Name: "pay_test"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 10
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  ActorPGDB:
    Name: "actor_test"
    DbType: "PGDB"
    DbServerAlias: "PLUTUS_RDS"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "actor"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  MerchantPGDB:
    Host: "localhost"
    Port: 5432
    DbType: "PGDB"
    Name: "merchant_test"
    DbServerAlias: "PLUTUS_RDS"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "merchant"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false

  PaymentInstrumentPGDB:
    Host: "localhost"
    Port: 5432
    Name: "payment_instrument_test"
    DbType: "PGDB"
    SSLMode: "disable"
    AppName: "payment_instrument"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  TimelinePGDB:
    Name: "timeline_test"
    DbType: "PGDB"
    DbServerAlias: "PLUTUS_RDS"
    EnableDebug: false
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "timeline"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false

RedisRateLimiterName: "VendormappingActorRedisStore"
RedisClusters:
  VendormappingActorRedisStore:
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 4
    HystrixCommand:
      CommandName: "actor_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 500
        ExecutionTimeout: 100ms
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
  ActorMerchantRedisStore:
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 14
    ClientName: merchant
  PayMerchantRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 14
  VendormappingPiRedisStore:
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 3
  PayRedisStore:
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 13
    ClientName: balance
    HystrixCommand:
      CommandName: "balance_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 2500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
  UserTimelineRedisStore:
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 3
    ClientName: timeline

UseCaseDBConfigMap:
  EPIFI_TECH:
    DbType: "PGDB"
    AppName: "actor"
    StatementTimeout: 5m
    Host: "localhost"
    Port: 5432
    Username: "root"
    Password: ""
    Name: "actor_test"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  EPIFI_TECH:USE_CASE_ACTOR:
    DbType: "PGDB"
    AppName: "actor"
    StatementTimeout: 5m
    Host: "localhost"
    Port: 5432
    Username: "root"
    Password: ""
    Name: "actor_test"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  EPIFI_TECH:USE_CASE_PAYMENT_INSTRUMENT:
    DbType: "PGDB"
    AppName: "actor"
    StatementTimeout: 5m
    Host: "localhost"
    Port: 5432
    Username: "root"
    Password: ""
    Name: "payment_instrument_test"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  LIQUILOANS_PL:
    DbType: "CRDB"
    AppName: "actor"
    StatementTimeout: 5m
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "liquiloans_epifi_test"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  LIQUILOANS_PL:USE_CASE_PAYMENT_INSTRUMENT:
    DbType: "CRDB"
    AppName: "actor"
    StatementTimeout: 5m
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "liquiloans_epifi_test"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:USE_CASE_ACTOR:
    DbType: "CRDB"
    AppName: "actor"
    StatementTimeout: 5m
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "liquiloans_epifi_test"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  STOCK_GUARDIAN_TSP:USE_CASE_PAYMENT_INSTRUMENT:
    DbType: "CRDB"
    AppName: "actor"
    StatementTimeout: 5m
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "stockguardian_tsp_crdb_test"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  STOCK_GUARDIAN_TSP:USE_CASE_ACTOR:
    DbType: "CRDB"
    AppName: "actor"
    StatementTimeout: 5m
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "stockguardian_tsp_crdb_test"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

RueidisRedisClients:
  CollapserRueidisRedisStore:
    Addrs:
      - "localhost:6379"
    RedisDB: 0
    ClientName: "actor-collapser"
    Hystrix:
      CommandName: "collapser_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 2500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
    EnableSecureRedis: false
