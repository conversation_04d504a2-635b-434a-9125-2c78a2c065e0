GrpcRatelimiterParams:
  RateLimitConfig:
    RedisOptions:
      Options:
        Addr: "localhost:6379"
        Password: ""
        DB: 0
      IsSecureRedis: false

ServerPorts:
  HttpPort: 9893

RedisRateLimiterName: "QuestRedisStore"

RedisClusters:
  QuestRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 10
    ClientName: vendor-notification-quest-sdk
    HystrixCommand:
      CommandName: "quest_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 500
        ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
  USStocksRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 0

JwtInterceptor:
  RSAPublicKeyPEM: |
    -----BEGIN PUBLIC KEY-----
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmR5VY4vQgtWXupETHsVK
    J88k32UaaUyIhgvtzAGSn67JNpk0zBwaEoFvsybg+S5NGDg70LmMumLAP83M1ged
    HctdotMRoMYHbnRIeWZa9ZsQUh/Vk6ffWXQEQ/NcvdOI5OLpNeCfmhSq7xKf2y/t
    hDK1oCeXqFC6HmP2TndpJifJF34seSvFiHFuxXv4sYwNfSjpunso/HJZKpPIXvOs
    51RKuKYrsbG5ptI1pkHfBxKmI3B6HU8pvYyzT222W+bc1X5y39ApQF2bL1KBAuX+
    IeI4vYwqI2AJadpPbcty/jK6heIgViaOugj08dNu3/7bWdbxZ7k+5uOHSIeibBrQ
    PwIDAQAB
    -----END PUBLIC KEY-----
  AllowedClients: ["loans-dsa-1"]
