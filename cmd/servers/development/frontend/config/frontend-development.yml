Name: frontend

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/frontend/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

ServerPorts:
  GrpcPort: 8082
  GrpcSecurePort: 9509
  HttpPort: 9999
  HttpPProfPort: 9990

GrpcServerConfig:
  SuppressZapLogger: false

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"
    RudderClientApiKey: "rudder/client-api-key"
    AppsFlyerClientKey: "appsflyer/client-api-key"
    OneMoneyClientSecret: "onemoney/client-secret"
    DeviceIdsEnabledForSafetyNetV2: "[\"deviceId1\"]"
    MoengageAppSdkKey: "moengage/app-sdk-key"
    ActiveDeviceIntegrityTokenSigningKey: "Isignshorttokens6"
    MiAmpPushSecretJson: "mi_amp_push/mi_secret_json"
    AppsFlyerClientKeyV2: "appsflyer/client-api-key-2"
    RudderIosClientApiKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"

RedisRateLimiterName: "HomeRedisStore"
RedisClusters:
  AnalyserRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 10
    ClientName: analyser
  HomeRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 0
    ClientName: home
  QuestRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 10
    ClientName: frontend-quest-sdk
    HystrixCommand:
      CommandName: "quest_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 500
        ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80

RueidisRedisClients:
  CollapserRueidisRedisStore:
    Addrs:
      - "localhost:6379"
    RedisDB: 0
    ClientName: "frontend-collapser"
    Hystrix:
      CommandName: "collapser_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 2500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
    EnableSecureRedis: false
