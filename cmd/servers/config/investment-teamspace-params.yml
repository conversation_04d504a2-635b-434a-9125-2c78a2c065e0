DBConfigMap:
  EPIFI_TECH:
    SSLClientCert: ""
    SSLClientKey: ""
    SSLMode: disable
    <PERSON><PERSON><PERSON><PERSON><PERSON>: ""
    SecretName: ""
  EPIFI_WEALTH:
    SSLClientCert: ""
    SSLClientKey: ""
    SSLMode: disable
    <PERSON><PERSON><PERSON><PERSON>ert: ""
    SecretName: ""
  P2P_INVESTMENT_LIQUILOANS:
    SSLClientCert: ""
    SSLClientKey: ""
    SSLMode: disable
    SS<PERSON><PERSON>Cert: ""
    SecretName: ""
  US_STOCKS_ALPACA:
    Password: usstocks_alpaca_dev_user
    SSLMode: disable
    SecretName: ""
    Username: usstocks_alpaca_dev_user
Databases:
  EpifiCRDB:
    SSLClientCert: ""
    SSLClientKey: ""
    SSLMode: disable
    SSLRootCert: ""
    SecretName: ""
  EpifiWealthCRDB:
    SSLClientCert: ""
    SSLClientKey: ""
    SSLMode: disable
    SSLRootCert: ""
    SecretName: ""
  P2pInvestmentLiquiloansCRDB:
    SSLClientCert: ""
    SS<PERSON>lientK<PERSON>: ""
    SSLMode: disable
    SSLRootCert: ""
    SecretName: ""
  UsstocksAlpacaPGDB:
    Password: usstocks_alpaca_dev_user
    SSLMode: disable
    SecretName: ""
    Username: usstocks_alpaca_dev_user
Logging:
  EnableLoggingToFile: false
RedisClusters:
  InvestmentRedisStore:
    IsSecureRedis: false
    Options:
      Addr: redis:6379
  MfCatalogRedisStore:
    IsSecureRedis: false
    Options:
      Addr: redis:6379
  USStocksRedisStore:
    IsSecureRedis: false
    Options:
      Addr: redis:6379
  P2pInvestmentRedisStore:
    IsSecureRedis: false
    Options:
      Addr: redis:6379
      DB: 2
  USStocksAccountRedisStore:
    IsSecureRedis: false
    Options:
      Addr: redis:6379
      DB: 3
