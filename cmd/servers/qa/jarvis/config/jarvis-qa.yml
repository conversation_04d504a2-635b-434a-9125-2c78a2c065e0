Databases:
  JarvisPGDB:
    DbType: "PGDB"
    StatementTimeout: 5s
    Name: "jarvis"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "deploy/rds/rds-ca-root-2061"
    MaxOpenConn: 50
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "deploy/deploy-jarvis"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false

RedisRateLimiterName: "JarvisRedisStore"
RedisClusters:
  JarvisRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      # on qa we are using common redis cluster with a different db
      DB: 2
  VKYCRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 12
    HystrixCommand:
      CommandName: "user_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 200
        ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 50
