// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	"net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	gorm "gorm.io/gorm"

	celestialpb "github.com/epifi/be-common/api/celestial"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sns "github.com/epifi/be-common/pkg/aws/v2/sns"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	awswire "github.com/epifi/be-common/pkg/aws/v2/wire"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	servergenwire2 "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	temporalcl "github.com/epifi/be-common/pkg/epifitemporal/client"
	namespace "github.com/epifi/be-common/pkg/epifitemporal/namespace"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	usecase "github.com/epifi/be-common/pkg/storage/v2/usecase"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	amlconf "github.com/epifi/gamma/aml/config"
	wire "github.com/epifi/gamma/aml/wire"
	analyserconf "github.com/epifi/gamma/analyser/config"
	genconf7 "github.com/epifi/gamma/analyser/config/genconf"
	wire10 "github.com/epifi/gamma/analyser/wire"
	types3 "github.com/epifi/gamma/analyser/wire/types"
	accountbalancepb "github.com/epifi/gamma/api/accounts/balance"
	accrualpb "github.com/epifi/gamma/api/accrual"
	actor "github.com/epifi/gamma/api/actor"
	amlpb "github.com/epifi/gamma/api/aml"
	amldevpb "github.com/epifi/gamma/api/aml/developer"
	analyserconsumerpb "github.com/epifi/gamma/api/analyser/consumer"
	analyserdeveloperpb "github.com/epifi/gamma/api/analyser/developer"
	dynamicelements2 "github.com/epifi/gamma/api/analyser/dynamicelements"
	investment3 "github.com/epifi/gamma/api/analyser/investment"
	analyserinvestmentconsumerpb "github.com/epifi/gamma/api/analyser/investment/consumer"
	txnaggregatespb "github.com/epifi/gamma/api/analyser/txnaggregates"
	analyservariablespb "github.com/epifi/gamma/api/analyser/variables"
	authpb "github.com/epifi/gamma/api/auth"
	liveness "github.com/epifi/gamma/api/auth/liveness"
	location2 "github.com/epifi/gamma/api/auth/location"
	bankcust "github.com/epifi/gamma/api/bankcust"
	reminderpb "github.com/epifi/gamma/api/budgeting/reminder"
	reminderconsumerpb "github.com/epifi/gamma/api/budgeting/reminder/consumer"
	cardcontrolpb "github.com/epifi/gamma/api/card/control"
	cardpb "github.com/epifi/gamma/api/card/provisioning"
	casper "github.com/epifi/gamma/api/casper"
	beexchangerpb "github.com/epifi/gamma/api/casper/exchanger"
	categorizerpb "github.com/epifi/gamma/api/categorizer"
	categorizerconsumerpb "github.com/epifi/gamma/api/categorizer/consumer"
	categorizerdeveloper "github.com/epifi/gamma/api/categorizer/developer"
	comms "github.com/epifi/gamma/api/comms"
	connectedaccpb "github.com/epifi/gamma/api/connected_account"
	consent "github.com/epifi/gamma/api/consent"
	creditreport "github.com/epifi/gamma/api/creditreportv2"
	ticket "github.com/epifi/gamma/api/cx/ticket"
	watsonpb "github.com/epifi/gamma/api/cx/watson"
	depositpb "github.com/epifi/gamma/api/deposit"
	developer2 "github.com/epifi/gamma/api/deposit/developer"
	depositreconpb "github.com/epifi/gamma/api/deposit/reconciliation"
	depositschedulerpb "github.com/epifi/gamma/api/deposit/scheduler"
	depositwatsonpb "github.com/epifi/gamma/api/deposit/watson"
	docs "github.com/epifi/gamma/api/docs"
	employmentpb "github.com/epifi/gamma/api/employment"
	firefly "github.com/epifi/gamma/api/firefly"
	fireflyaccpb "github.com/epifi/gamma/api/firefly/accounting"
	fireflybilling "github.com/epifi/gamma/api/firefly/billing"
	ffpinotpb "github.com/epifi/gamma/api/firefly/pinot"
	fitttpb "github.com/epifi/gamma/api/fittt"
	consumer3 "github.com/epifi/gamma/api/fittt/action/aggregator/consumer"
	consumer5 "github.com/epifi/gamma/api/fittt/action/consumer"
	devconsolepb "github.com/epifi/gamma/api/fittt/devconsole"
	fitttdev "github.com/epifi/gamma/api/fittt/developer"
	consumer4 "github.com/epifi/gamma/api/fittt/event/consumer"
	consumer6 "github.com/epifi/gamma/api/fittt/orchestrator/consumer"
	schedulerpb "github.com/epifi/gamma/api/fittt/scheduler"
	sportspb "github.com/epifi/gamma/api/fittt/sports"
	goalspb "github.com/epifi/gamma/api/goals"
	gplace "github.com/epifi/gamma/api/gplace"
	healthenginepb "github.com/epifi/gamma/api/health_engine"
	inapppb "github.com/epifi/gamma/api/inapphelp/app_feedback"
	inappreferral "github.com/epifi/gamma/api/inappreferral"
	indianstockscatalogpb "github.com/epifi/gamma/api/indianstocks/catalog"
	insights "github.com/epifi/gamma/api/insights"
	accessinfopb "github.com/epifi/gamma/api/insights/accessinfo"
	accessinfoconsumerpb "github.com/epifi/gamma/api/insights/accessinfo/consumer"
	consumerpb "github.com/epifi/gamma/api/insights/consumer"
	developer14 "github.com/epifi/gamma/api/insights/developer"
	emailparserpb "github.com/epifi/gamma/api/insights/emailparser"
	epfpb "github.com/epifi/gamma/api/insights/epf"
	epfconsumer2 "github.com/epifi/gamma/api/insights/epf/consumer"
	insightkubairpb "github.com/epifi/gamma/api/insights/kubair"
	networthpb "github.com/epifi/gamma/api/insights/networth"
	storypb "github.com/epifi/gamma/api/insights/story"
	userdeclarationpb "github.com/epifi/gamma/api/insights/user_declaration"
	aggregatorpb "github.com/epifi/gamma/api/investment/aggregator"
	aggregatorpbconsumer "github.com/epifi/gamma/api/investment/aggregator/consumer"
	auth "github.com/epifi/gamma/api/investment/auth"
	dynamicuielementpb "github.com/epifi/gamma/api/investment/dynamic_ui_element"
	investmenteventprocessorpb "github.com/epifi/gamma/api/investment/event_processor"
	catalogpb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	mfcatalogconsumer2 "github.com/epifi/gamma/api/investment/mutualfund/catalog/consumer"
	catalogschpb "github.com/epifi/gamma/api/investment/mutualfund/catalog/scheduler"
	developer7 "github.com/epifi/gamma/api/investment/mutualfund/developer"
	etahandlerpb2 "github.com/epifi/gamma/api/investment/mutualfund/eta_handler"
	mfexternalorderpb "github.com/epifi/gamma/api/investment/mutualfund/external"
	mfexternalorderconsumer "github.com/epifi/gamma/api/investment/mutualfund/external/consumer"
	foliodetails "github.com/epifi/gamma/api/investment/mutualfund/foliodetails"
	notificationspb "github.com/epifi/gamma/api/investment/mutualfund/notifications"
	mforderpb "github.com/epifi/gamma/api/investment/mutualfund/order"
	orderconsumerpb "github.com/epifi/gamma/api/investment/mutualfund/order/consumer"
	fgpb "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	mfoperationspb "github.com/epifi/gamma/api/investment/mutualfund/order/operations"
	orchestrator2 "github.com/epifi/gamma/api/investment/mutualfund/order/orchestrator"
	prhpb "github.com/epifi/gamma/api/investment/mutualfund/order/prerequisite_handler"
	prhpbconsumer "github.com/epifi/gamma/api/investment/mutualfund/order/prerequisite_handler/consumer"
	rfpb "github.com/epifi/gamma/api/investment/mutualfund/order/reverse_feed"
	rfpbconsumer "github.com/epifi/gamma/api/investment/mutualfund/order/reverse_feed/consumer"
	scheduler4 "github.com/epifi/gamma/api/investment/mutualfund/order/scheduler"
	vosconsumerpb "github.com/epifi/gamma/api/investment/mutualfund/order/vendor_order_sender/consumer"
	investpaypb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	phpbconsumer "github.com/epifi/gamma/api/investment/mutualfund/payment_handler/consumer"
	payschpb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler/scheduler"
	reconciliationpb "github.com/epifi/gamma/api/investment/mutualfund/reconciliation"
	statementpb2 "github.com/epifi/gamma/api/investment/mutualfund/statement"
	statementconsumerpb "github.com/epifi/gamma/api/investment/mutualfund/statement/consumer"
	nonfinancialeventconsumer "github.com/epifi/gamma/api/investment/non_financial_events/consumer"
	profilepb "github.com/epifi/gamma/api/investment/profile"
	investmentwatson "github.com/epifi/gamma/api/investment/watson"
	kyc "github.com/epifi/gamma/api/kyc"
	vkycpb "github.com/epifi/gamma/api/kyc/vkyc"
	merchantpb "github.com/epifi/gamma/api/merchant"
	npspb "github.com/epifi/gamma/api/nps/catalog"
	nudgepb "github.com/epifi/gamma/api/nudge"
	orderpb "github.com/epifi/gamma/api/order"
	aapb "github.com/epifi/gamma/api/order/aa"
	actoractivitypb "github.com/epifi/gamma/api/order/actoractivity"
	paymentpb "github.com/epifi/gamma/api/order/payment"
	p2pinvestment "github.com/epifi/gamma/api/p2pinvestment"
	p2pconsumerpb "github.com/epifi/gamma/api/p2pinvestment/consumer"
	p2pcxpb "github.com/epifi/gamma/api/p2pinvestment/cx"
	developer9 "github.com/epifi/gamma/api/p2pinvestment/developer"
	incidentmanager "github.com/epifi/gamma/api/p2pinvestment/incidentmanager"
	panpb "github.com/epifi/gamma/api/pan"
	paypb "github.com/epifi/gamma/api/pay"
	internationalfundtransfer "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	iftfilegenrationsvc "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	pipb "github.com/epifi/gamma/api/paymentinstrument"
	accountpipb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	preapprovedloanpb "github.com/epifi/gamma/api/preapprovedloan"
	managerpb "github.com/epifi/gamma/api/quest/manager"
	recurringpaymentpb "github.com/epifi/gamma/api/recurringpayment"
	rewardspb "github.com/epifi/gamma/api/rewards"
	rewardspinotpb "github.com/epifi/gamma/api/rewards/pinot"
	riskprofilepb "github.com/epifi/gamma/api/risk/profile"
	commandprocessor2 "github.com/epifi/gamma/api/rms/command_processor"
	developer5 "github.com/epifi/gamma/api/rms/developer"
	rmsmanagerpb "github.com/epifi/gamma/api/rms/manager"
	consumer7 "github.com/epifi/gamma/api/rms/orchestrator/consumer"
	ui "github.com/epifi/gamma/api/rms/ui"
	salaryprogram "github.com/epifi/gamma/api/salaryprogram"
	savingspb "github.com/epifi/gamma/api/savings"
	search "github.com/epifi/gamma/api/search"
	searchdevpb "github.com/epifi/gamma/api/search/developer"
	indexerpb "github.com/epifi/gamma/api/search/indexer"
	catalogpb2 "github.com/epifi/gamma/api/securities/catalog"
	catalogconsumerpb "github.com/epifi/gamma/api/securities/catalog/consumer"
	segmentpb "github.com/epifi/gamma/api/segment"
	simp2pdspb "github.com/epifi/gamma/api/simulator/p2pinvestment/developer"
	tieringpb "github.com/epifi/gamma/api/tiering"
	timelinepb "github.com/epifi/gamma/api/timeline"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	upcomingtransactions2 "github.com/epifi/gamma/api/upcomingtransactions"
	upcomingtrxpb "github.com/epifi/gamma/api/upcomingtransactions/consumer"
	developer16 "github.com/epifi/gamma/api/upcomingtransactions/developer"
	upipb "github.com/epifi/gamma/api/upi"
	user "github.com/epifi/gamma/api/user"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	obfuscatorpb "github.com/epifi/gamma/api/user/obfuscator"
	onboardingpb "github.com/epifi/gamma/api/user/onboarding"
	usstocksaccountpb "github.com/epifi/gamma/api/usstocks/account"
	usstockaccountpb "github.com/epifi/gamma/api/usstocks/account/consumer"
	usstockscatalogmanagerpb "github.com/epifi/gamma/api/usstocks/catalog"
	usstockcatalogpb "github.com/epifi/gamma/api/usstocks/catalog/consumer"
	usstockconsumerpb "github.com/epifi/gamma/api/usstocks/consumer"
	usstocksdevpb "github.com/epifi/gamma/api/usstocks/developer"
	operationspb "github.com/epifi/gamma/api/usstocks/operations"
	usstocksorderpb "github.com/epifi/gamma/api/usstocks/order"
	usstockorderpb "github.com/epifi/gamma/api/usstocks/order/consumer"
	portfolio "github.com/epifi/gamma/api/usstocks/portfolio"
	ussrewardspb "github.com/epifi/gamma/api/usstocks/rewards"
	tax "github.com/epifi/gamma/api/usstocks/tax"
	usstaxconsumerpb "github.com/epifi/gamma/api/usstocks/tax/consumer"
	ussdynamicelementpb "github.com/epifi/gamma/api/usstocks/uss_dynamic_elements"
	amlvgpb "github.com/epifi/gamma/api/vendorgateway/aml"
	employment "github.com/epifi/gamma/api/vendorgateway/employment"
	fittt "github.com/epifi/gamma/api/vendorgateway/fittt"
	vggplacepb "github.com/epifi/gamma/api/vendorgateway/gplace"
	vgp2ppb "github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	vgliveness "github.com/epifi/gamma/api/vendorgateway/liveness"
	location "github.com/epifi/gamma/api/vendorgateway/location"
	ncpb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	nps "github.com/epifi/gamma/api/vendorgateway/nps"
	vgaccountpb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgcustomerpb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	vgdepositpb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	vginternationalfundtransfer "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	vgsavingspb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	stockspb "github.com/epifi/gamma/api/vendorgateway/stocks"
	vgcatalogpb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog"
	vgcatalogpb2 "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	ckycvgpb "github.com/epifi/gamma/api/vendorgateway/wealth/ckyc"
	cvlvgpb "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	digilockervgpb "github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	digiovgpb "github.com/epifi/gamma/api/vendorgateway/wealth/digio"
	inhouseocr "github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	manchvgpb "github.com/epifi/gamma/api/vendorgateway/wealth/manch"
	mf "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	mfanalyticspb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/analytics"
	holdingsimporterpb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/holdingsimporter"
	nsdlvgpb "github.com/epifi/gamma/api/vendorgateway/wealth/nsdl"
	vmpb "github.com/epifi/gamma/api/vendormapping"
	wealthpb "github.com/epifi/gamma/api/wealthonboarding"
	wockycpb "github.com/epifi/gamma/api/wealthonboarding/ckyc"
	consumer14 "github.com/epifi/gamma/api/wealthonboarding/consumer"
	wocxpb "github.com/epifi/gamma/api/wealthonboarding/cx"
	wodevpb "github.com/epifi/gamma/api/wealthonboarding/developer"
	budgetingconf "github.com/epifi/gamma/budgeting/config"
	genconf12 "github.com/epifi/gamma/budgeting/config/genconf"
	wire15 "github.com/epifi/gamma/budgeting/wire"
	categorizerconf "github.com/epifi/gamma/categorizer/config"
	genconf8 "github.com/epifi/gamma/categorizer/config/genconf"
	wire11 "github.com/epifi/gamma/categorizer/wire"
	wiretypes "github.com/epifi/gamma/categorizer/wire/types"
	hook "github.com/epifi/gamma/cmd/servers/qa/wealthdmf/hook"
	insights3 "github.com/epifi/gamma/cmd/service_groups/insights"
	investment "github.com/epifi/gamma/cmd/service_groups/investment"
	usstocks "github.com/epifi/gamma/cmd/service_groups/usstocks"
	depositconf "github.com/epifi/gamma/deposit/config"
	genconf2 "github.com/epifi/gamma/deposit/config/genconf"
	wire2 "github.com/epifi/gamma/deposit/wire"
	depositwiretypes "github.com/epifi/gamma/deposit/wire/types"
	fitttconf "github.com/epifi/gamma/fittt/config"
	genconf3 "github.com/epifi/gamma/fittt/config/genconf"
	wire3 "github.com/epifi/gamma/fittt/wire"
	fittypes "github.com/epifi/gamma/fittt/wire/types"
	goalsconf "github.com/epifi/gamma/goals/config"
	wire5 "github.com/epifi/gamma/goals/wire"
	gplaceconf "github.com/epifi/gamma/gplace/config"
	wire16 "github.com/epifi/gamma/gplace/wire"
	indexerconf "github.com/epifi/gamma/indexer/config"
	wire7 "github.com/epifi/gamma/indianstocks/wire"
	insightsconf "github.com/epifi/gamma/insights/config"
	genconf9 "github.com/epifi/gamma/insights/config/genconf"
	wire12 "github.com/epifi/gamma/insights/wire"
	types4 "github.com/epifi/gamma/insights/wire/types"
	investmentconf "github.com/epifi/gamma/investment/config"
	investmentgenconf "github.com/epifi/gamma/investment/config/genconf"
	wire6 "github.com/epifi/gamma/investment/wire"
	wtypes "github.com/epifi/gamma/investment/wire/types"
	npsconf "github.com/epifi/gamma/nps/config"
	wire19 "github.com/epifi/gamma/nps/wire"
	p2pinvestmentconf "github.com/epifi/gamma/p2pinvestment/config"
	genconf5 "github.com/epifi/gamma/p2pinvestment/config/genconf"
	wire8 "github.com/epifi/gamma/p2pinvestment/wire"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	customdelayqueue "github.com/epifi/gamma/pkg/customdelayqueue"
	customqueuewire "github.com/epifi/gamma/pkg/customdelayqueue/wire"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	zinc "github.com/epifi/gamma/pkg/zinc/search"
	questsdkinit "github.com/epifi/gamma/quest/sdk/init"
	rmsconf "github.com/epifi/gamma/rms/config"
	genconf4 "github.com/epifi/gamma/rms/config/genconf"
	wire4 "github.com/epifi/gamma/rms/wire"
	searchconf "github.com/epifi/gamma/search/config"
	genconf11 "github.com/epifi/gamma/search/config/genconf"
	wire14 "github.com/epifi/gamma/search/wire"
	types5 "github.com/epifi/gamma/search/wire/types"
	securitiesconf "github.com/epifi/gamma/securities/config"
	genconf13 "github.com/epifi/gamma/securities/config/genconf"
	wire18 "github.com/epifi/gamma/securities/wire"
	wtypes4 "github.com/epifi/gamma/securities/wire/types"
	upcomingtransactionsconf "github.com/epifi/gamma/upcomingtransactions/config"
	genconf10 "github.com/epifi/gamma/upcomingtransactions/config/genconf"
	wire13 "github.com/epifi/gamma/upcomingtransactions/wire"
	usstocksconf "github.com/epifi/gamma/usstocks/config"
	genconf6 "github.com/epifi/gamma/usstocks/config/genconf"
	wire9 "github.com/epifi/gamma/usstocks/wire"
	wtypes3 "github.com/epifi/gamma/usstocks/wire/types"
	vgtypes "github.com/epifi/gamma/vendorgateway/wire/types"
	wealthonboardingconf "github.com/epifi/gamma/wealthonboarding/config"
	wotypes "github.com/epifi/gamma/wealthonboarding/types"
	wire17 "github.com/epifi/gamma/wealthonboarding/wire"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.WEALTH_DMF_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.WEALTH_DMF_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.WEALTH_DMF_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.WEALTH_DMF_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	var dbConnTeardown func()
	crdbResourceMap, crdbTxnResourceMap, dbConnTeardown, err = storage2.NewDBResourceProviderV2(conf.DBConfigMap.GetOwnershipToDbConfigMap(), conf.Tracing.Enable, gconf.PgdbConns())
	if err != nil {
		logger.Error(ctx, "failed to get db resource provider", zap.Error(err))
		return err
	}
	defer func() {
		dbConnTeardown()
	}()

	epifiWealthCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiWealthCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiWealthCRDB"))
		return err
	}
	epifiWealthCRDBSqlDb, err := epifiWealthCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiWealthCRDB"))
		return err
	}
	defer func() { _ = epifiWealthCRDBSqlDb.Close() }()
	epifiCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	epifiCRDBSqlDb, err := epifiCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	defer func() { _ = epifiCRDBSqlDb.Close() }()
	p2pInvestmentLiquiloansCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["P2pInvestmentLiquiloansCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "P2pInvestmentLiquiloansCRDB"))
		return err
	}
	p2pInvestmentLiquiloansCRDBSqlDb, err := p2pInvestmentLiquiloansCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "P2pInvestmentLiquiloansCRDB"))
		return err
	}
	defer func() { _ = p2pInvestmentLiquiloansCRDBSqlDb.Close() }()

	fitttPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["FitttPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "FitttPGDB"))
		return err
	}
	fitttPGDBSqlDb, err := fitttPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "FitttPGDB"))
		return err
	}
	defer func() { _ = fitttPGDBSqlDb.Close() }()
	rmsPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["RmsPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "RmsPGDB"))
		return err
	}
	rmsPGDBSqlDb, err := rmsPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "RmsPGDB"))
		return err
	}
	defer func() { _ = rmsPGDBSqlDb.Close() }()
	usstocksAlpacaPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["UsstocksAlpacaPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "UsstocksAlpacaPGDB"))
		return err
	}
	usstocksAlpacaPGDBSqlDb, err := usstocksAlpacaPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "UsstocksAlpacaPGDB"))
		return err
	}
	defer func() { _ = usstocksAlpacaPGDBSqlDb.Close() }()
	epifiWealthAnalyticsPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["EpifiWealthAnalyticsPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiWealthAnalyticsPGDB"))
		return err
	}
	epifiWealthAnalyticsPGDBSqlDb, err := epifiWealthAnalyticsPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiWealthAnalyticsPGDB"))
		return err
	}
	defer func() { _ = epifiWealthAnalyticsPGDBSqlDb.Close() }()
	categorizerPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["CategorizerPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "CategorizerPGDB"))
		return err
	}
	categorizerPGDBSqlDb, err := categorizerPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "CategorizerPGDB"))
		return err
	}
	defer func() { _ = categorizerPGDBSqlDb.Close() }()
	insightsPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["InsightsPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "InsightsPGDB"))
		return err
	}
	insightsPGDBSqlDb, err := insightsPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "InsightsPGDB"))
		return err
	}
	defer func() { _ = insightsPGDBSqlDb.Close() }()
	actorInsightsPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["ActorInsightsPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "ActorInsightsPGDB"))
		return err
	}
	actorInsightsPGDBSqlDb, err := actorInsightsPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "ActorInsightsPGDB"))
		return err
	}
	defer func() { _ = actorInsightsPGDBSqlDb.Close() }()
	budgetingPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["BudgetingPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "BudgetingPGDB"))
		return err
	}
	budgetingPGDBSqlDb, err := budgetingPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "BudgetingPGDB"))
		return err
	}
	defer func() { _ = budgetingPGDBSqlDb.Close() }()
	vendordataPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["VendordataPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "VendordataPGDB"))
		return err
	}
	vendordataPGDBSqlDb, err := vendordataPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "VendordataPGDB"))
		return err
	}
	defer func() { _ = vendordataPGDBSqlDb.Close() }()
	stocksPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["StocksPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "StocksPGDB"))
		return err
	}
	stocksPGDBSqlDb, err := stocksPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "StocksPGDB"))
		return err
	}
	defer func() { _ = stocksPGDBSqlDb.Close() }()
	npsPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["NpsPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "NpsPGDB"))
		return err
	}
	npsPGDBSqlDb, err := npsPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "NpsPGDB"))
		return err
	}
	defer func() { _ = npsPGDBSqlDb.Close() }()

	collapserRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["CollapserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for CollapserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { collapserRueidisRedisStore.Close() }()
	collapserRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(collapserRueidisRedisStore), gconf.RueidisRedisClients()["CollapserRueidisRedisStore"].Hystrix)
	growthinfraConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.GROWTH_INFRA_SERVER)
	defer epifigrpc.CloseConn(growthinfraConn)
	managerClient := managerpb.NewManagerClient(growthinfraConn)
	authConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.AUTH_SERVER)
	defer epifigrpc.CloseConn(authConn)
	groupClient := usergrouppb.NewGroupClient(authConn)
	usersClient := user.NewUsersClient(authConn)
	actorConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.ACTOR_SERVER)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actor.NewActorClient(actorConn)
	segmentationServiceClient := segmentpb.NewSegmentationServiceClient(growthinfraConn)
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	amlClient := amlvgpb.NewAmlClient(vendorgatewayConn)
	vendormappingConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.VENDORMAPPING_SERVER)
	defer epifigrpc.CloseConn(vendormappingConn)
	vendorMappingServiceClient := vmpb.NewVendorMappingServiceClient(vendormappingConn)
	docsConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.DOCS_SERVER)
	defer epifigrpc.CloseConn(docsConn)
	healthEngineServiceClient := healthenginepb.NewHealthEngineServiceClient(docsConn)
	depositClient := vgdepositpb.NewDepositClient(vendorgatewayConn)
	customerClient := vgcustomerpb.NewCustomerClient(vendorgatewayConn)
	authClient := authpb.NewAuthClient(authConn)
	centralgrowthConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.CENTRAL_GROWTH_SERVER)
	defer epifigrpc.CloseConn(centralgrowthConn)
	savingsClient := savingspb.NewSavingsClient(centralgrowthConn)
	savingsClientVar2 := vgsavingspb.NewSavingsClient(vendorgatewayConn)
	piClient := pipb.NewPiClient(actorConn)
	accountPIRelationClient := accountpipb.NewAccountPIRelationClient(actorConn)
	orderConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.ORDER_SERVER)
	defer epifigrpc.CloseConn(orderConn)
	orderServiceClient := orderpb.NewOrderServiceClient(orderConn)
	paymentClient := paymentpb.NewPaymentClient(orderConn)
	growthinfraConnVar5ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar2 := servergenwire.NewSavingsRequestClientInterceptor()
	if unaryClientInterceptorVar2 != nil {
		growthinfraConnVar5ClientInterceptors = append(growthinfraConnVar5ClientInterceptors, unaryClientInterceptorVar2)
	}
	growthinfraConnVar5 := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar5ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar5)
	commsClient := comms.NewCommsClient(growthinfraConnVar5)
	onboardingConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	vKYCClient := vkycpb.NewVKYCClient(onboardingConn)
	wealthdmfConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.WEALTH_DMF_SERVER)
	defer epifigrpc.CloseConn(wealthdmfConn)
	ruleManagerClient := rmsmanagerpb.NewRuleManagerClient(wealthdmfConn)
	accountsClient := vgaccountpb.NewAccountsClient(vendorgatewayConn)
	goalsClient := goalspb.NewGoalsClient(wealthdmfConn)
	cxConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.CX_SERVER)
	defer epifigrpc.CloseConn(cxConn)
	appFeedbackClient := inapppb.NewAppFeedbackClient(cxConn)
	panClient := panpb.NewPanClient(onboardingConn)
	bankCustomerServiceClient := bankcust.NewBankCustomerServiceClient(onboardingConn)
	onboardingClient := onboardingpb.NewOnboardingClient(authConn)
	cardConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.CARD_SERVER)
	defer epifigrpc.CloseConn(cardConn)
	accountingClient := fireflyaccpb.NewAccountingClient(cardConn)
	balanceClient := accountbalancepb.NewBalanceClient(actorConn)
	nebulaConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.NEBULA_SERVER)
	defer epifigrpc.CloseConn(nebulaConn)
	celestialClient := celestialpb.NewCelestialClient(nebulaConn)
	payClient := paypb.NewPayClient(orderConn)
	actionBarClient := search.NewActionBarClient(wealthdmfConn)
	sportsManagerClient := sportspb.NewSportsManagerClient(wealthdmfConn)
	fitttClient := fittt.NewFitttClient(vendorgatewayConn)
	fitttClientVar2 := fitttpb.NewFitttClient(wealthdmfConn)
	growthinfraConnVar8ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar3 := servergenwire.NewFitttRequestClientInterceptor()
	if unaryClientInterceptorVar3 != nil {
		growthinfraConnVar8ClientInterceptors = append(growthinfraConnVar8ClientInterceptors, unaryClientInterceptorVar3)
	}
	growthinfraConnVar8 := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar8ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar8)
	commsClientVar4 := comms.NewCommsClient(growthinfraConnVar8)
	ruleUIManagerClient := ui.NewRuleUIManagerClient(wealthdmfConn)
	orderManagerClient := mforderpb.NewOrderManagerClient(wealthdmfConn)
	fitttRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["FitttRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = fitttRedisStore.Close() }()
	depositClientVar5 := depositpb.NewDepositClient(wealthdmfConn)
	paymentHandlerClient := investpaypb.NewPaymentHandlerClient(wealthdmfConn)
	recurringPaymentServiceClient := recurringpaymentpb.NewRecurringPaymentServiceClient(orderConn)
	schedulerServiceClient := schedulerpb.NewSchedulerServiceClient(wealthdmfConn)
	wealthOnboardingClient := wealthpb.NewWealthOnboardingClient(wealthdmfConn)
	catalogManagerClient := usstockscatalogmanagerpb.NewCatalogManagerClient(wealthdmfConn)
	mutualFundClient := mf.NewMutualFundClient(vendorgatewayConn)
	fileGeneratorClient := fgpb.NewFileGeneratorClient(wealthdmfConn)
	catalogManagerClientVar2 := catalogpb.NewCatalogManagerClient(wealthdmfConn)
	growthinfraConnVar14ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar5 := servergenwire.NewInvestmentRequestClientInterceptor()
	if unaryClientInterceptorVar5 != nil {
		growthinfraConnVar14ClientInterceptors = append(growthinfraConnVar14ClientInterceptors, unaryClientInterceptorVar5)
	}
	growthinfraConnVar14 := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar14ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar14)
	commsClientVar10 := comms.NewCommsClient(growthinfraConnVar14)
	kycClient := kyc.NewKycClient(onboardingConn)
	authClientVar6 := auth.NewAuthClient(wealthdmfConn)
	docsClient := docs.NewDocsClient(docsConn)
	ticketClient := ticket.NewTicketClient(cxConn)
	mfCatalogRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["MfCatalogRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = mfCatalogRedisStore.Close() }()
	dynamicUIElementCacheRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["DynamicUIElementCacheRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = dynamicUIElementCacheRedisStore.Close() }()
	dynamicUIElementServiceClient := dynamicuielementpb.NewDynamicUIElementServiceClient(wealthdmfConn)
	p2PInvestmentClient := p2pinvestment.NewP2PInvestmentClient(wealthdmfConn)
	investmentRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["InvestmentRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = investmentRedisStore.Close() }()
	portfolioManagerClient := portfolio.NewPortfolioManagerClient(wealthdmfConn)
	holdingImporterClient := holdingsimporterpb.NewHoldingImporterClient(vendorgatewayConn)
	accountManagerClient := usstocksaccountpb.NewAccountManagerClient(wealthdmfConn)
	orderManagerClientVar13 := usstocksorderpb.NewOrderManagerClient(wealthdmfConn)
	connectedAccountClient := connectedaccpb.NewConnectedAccountClient(centralgrowthConn)
	nudgeServiceClient := nudgepb.NewNudgeServiceClient(growthinfraConn)
	consentClient := consent.NewConsentClient(authConn)
	prerequisiteHandlerClient := prhpb.NewPrerequisiteHandlerClient(wealthdmfConn)
	watsonClient := watsonpb.NewWatsonClient(cxConn)
	uNNameCheckClient := ncpb.NewUNNameCheckClient(vendorgatewayConn)
	vendorgatewayConnVar31ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar6 := servergenwire.NewMaxResponseSizeClientInterceptor(gconf)
	if unaryClientInterceptorVar6 != nil {
		vendorgatewayConnVar31ClientInterceptors = append(vendorgatewayConnVar31ClientInterceptors, unaryClientInterceptorVar6)
	}
	vendorgatewayConnVar31 := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar31ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar31)
	p2PClient := vgp2ppb.NewP2PClient(vendorgatewayConnVar31)
	p2pInvestmentRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["P2pInvestmentRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = p2pInvestmentRedisStore.Close() }()
	timelineServiceClient := timelinepb.NewTimelineServiceClient(actorConn)
	commsClientVar17 := comms.NewCommsClient(growthinfraConn)
	tieringClient := tieringpb.NewTieringClient(centralgrowthConn)
	incidentManagerClient := incidentmanager.NewIncidentManagerClient(wealthdmfConn)
	simulatorConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.SIMULATOR_GRPC_SERVER)
	defer epifigrpc.CloseConn(simulatorConn)
	devSimulatorP2PInvestmentClient := simp2pdspb.NewDevSimulatorP2PInvestmentClient(simulatorConn)
	internationalFundTransferClient := internationalfundtransfer.NewInternationalFundTransferClient(orderConn)
	vendorgatewayConnVar34ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar7 := servergenwire.NewMaxResponseSizeClientInterceptor(gconf)
	if unaryClientInterceptorVar7 != nil {
		vendorgatewayConnVar34ClientInterceptors = append(vendorgatewayConnVar34ClientInterceptors, unaryClientInterceptorVar7)
	}
	vendorgatewayConnVar34 := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar34ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar34)
	stocksClient := stockspb.NewStocksClient(vendorgatewayConnVar34)
	internationalFundTransferClientVar2 := vginternationalfundtransfer.NewInternationalFundTransferClient(vendorgatewayConn)
	uSStocksRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["USStocksRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = uSStocksRedisStore.Close() }()
	fileGeneratorClientVar8 := iftfilegenrationsvc.NewFileGeneratorClient(orderConn)
	lendingConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.LENDING_SERVER)
	defer epifigrpc.CloseConn(lendingConn)
	preApprovedLoanClient := preapprovedloanpb.NewPreApprovedLoanClient(lendingConn)
	userriskConn := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.USER_RISK_SERVER)
	defer epifigrpc.CloseConn(userriskConn)
	profileClient := riskprofilepb.NewProfileClient(userriskConn)
	vendorgatewayConnVar37 := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConnVar37)
	stocksClientVar3 := stockspb.NewStocksClient(vendorgatewayConnVar37)
	catalogClient := vgcatalogpb.NewCatalogClient(vendorgatewayConn)
	amlClientVar2 := amlpb.NewAmlClient(wealthdmfConn)
	ocrClient := inhouseocr.NewOcrClient(vendorgatewayConn)
	livenessClient := vgliveness.NewLivenessClient(vendorgatewayConn)
	livenessClientVar2 := liveness.NewLivenessClient(onboardingConn)
	uSStocksAccountRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["USStocksAccountRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = uSStocksAccountRedisStore.Close() }()
	employmentClient := employmentpb.NewEmploymentClient(onboardingConn)
	locationClient := location.NewLocationClient(vendorgatewayConn)
	rewardsGeneratorClient := rewardspb.NewRewardsGeneratorClient(growthinfraConn)
	investmentAggregatorClient := aggregatorpb.NewInvestmentAggregatorClient(wealthdmfConn)
	stocksClientVar7 := stockspb.NewStocksClient(vendorgatewayConn)
	txnCategorizerClient := categorizerpb.NewTxnCategorizerClient(wealthdmfConn)
	merchantServiceClient := merchantpb.NewMerchantServiceClient(actorConn)
	analyserRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["AnalyserRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = analyserRedisStore.Close() }()
	accountAggregatorClient := aapb.NewAccountAggregatorClient(orderConn)
	mFExternalOrdersClient := mfexternalorderpb.NewMFExternalOrdersClient(wealthdmfConn)
	mFAnalyticsClient := mfanalyticspb.NewMFAnalyticsClient(vendorgatewayConn)
	investmentAnalyticsClient := investment3.NewInvestmentAnalyticsClient(wealthdmfConn)
	growthinfraConnVar32ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptor := servergenwire.NewAnalyserRequestClientInterceptor()
	if unaryClientInterceptor != nil {
		growthinfraConnVar32ClientInterceptors = append(growthinfraConnVar32ClientInterceptors, unaryClientInterceptor)
	}
	growthinfraConnVar32 := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar32ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar32)
	commsClientVar23 := comms.NewCommsClient(growthinfraConnVar32)
	serviceClient := userdeclarationpb.NewServiceClient(wealthdmfConn)
	netWorthClient := networthpb.NewNetWorthClient(wealthdmfConn)
	securitiesCatalogClient := catalogpb2.NewSecuritiesCatalogClient(wealthdmfConn)
	epfClient := epfpb.NewEpfClient(wealthdmfConn)
	actorActivityClient := actoractivitypb.NewActorActivityClient(orderConn)
	uPIClient := upipb.NewUPIClient(orderConn)
	txnAggregatesClient := txnaggregatespb.NewTxnAggregatesClient(wealthdmfConn)
	fireflyClient := firefly.NewFireflyClient(cardConn)
	gPlaceClient := gplace.NewGPlaceClient(wealthdmfConn)
	locationClientVar2 := location2.NewLocationClient(authConn)
	categorizerVMRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["CategorizerVMRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = categorizerVMRedisStore.Close() }()
	accessInfoClient := accessinfopb.NewAccessInfoClient(wealthdmfConn)
	insightsClient := insights.NewInsightsClient(wealthdmfConn)
	growthinfraConnVar34ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar4 := servergenwire.NewInsightsRequestClientInterceptor()
	if unaryClientInterceptorVar4 != nil {
		growthinfraConnVar34ClientInterceptors = append(growthinfraConnVar34ClientInterceptors, unaryClientInterceptorVar4)
	}
	growthinfraConnVar34 := epifigrpc.NewServerConn(cfg.WEALTH_DMF_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar34ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar34)
	commsClientVar25 := comms.NewCommsClient(growthinfraConnVar34)
	useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec, useCaseDbResourceProviderTeardown, err := usecase.NewDBResourceProvider(gconf.UseCaseDBConfigMap(), gconf.Tracing().Enable, gconf.PgdbConns())
	if err != nil {
		logger.Error(ctx, "failed to initialize usecase db resource provider", zap.Error(err))
		return err
	}
	defer func() {
		useCaseDbResourceProviderTeardown()
	}()
	_ = useCaseDbResourceProvider
	_ = useCaseDbResourceProviderTxnExec
	emailParserClient := emailparserpb.NewEmailParserClient(wealthdmfConn)
	creditReportManagerClient := creditreport.NewCreditReportManagerClient(lendingConn)
	txnAggregatesClientVar4 := ffpinotpb.NewTxnAggregatesClient(cardConn)
	rewardsAggregatesClient := rewardspinotpb.NewRewardsAggregatesClient(growthinfraConn)
	employmentClientVar3 := employment.NewEmploymentClient(vendorgatewayConn)
	npsCatalogClient := npspb.NewNpsCatalogClient(wealthdmfConn)
	offerListingServiceClient := casper.NewOfferListingServiceClient(growthinfraConn)
	searchRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["SearchRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = searchRedisStore.Close() }()
	accrualClient := accrualpb.NewAccrualClient(growthinfraConn)
	cardProvisioningClient := cardpb.NewCardProvisioningClient(cardConn)
	inAppReferralClient := inappreferral.NewInAppReferralClient(onboardingConn)
	cardControlClient := cardcontrolpb.NewCardControlClient(cardConn)
	salaryProgramClient := salaryprogram.NewSalaryProgramClient(centralgrowthConn)
	reminderServiceClient := reminderpb.NewReminderServiceClient(wealthdmfConn)
	exchangerOfferServiceClient := beexchangerpb.NewExchangerOfferServiceClient(growthinfraConn)
	billingClient := fireflybilling.NewBillingClient(cardConn)
	gPlaceClientVar3 := vggplacepb.NewGPlaceClient(vendorgatewayConn)
	ckycClient := ckycvgpb.NewCkycClient(vendorgatewayConn)
	nsdlClient := nsdlvgpb.NewNsdlClient(vendorgatewayConn)
	cvlClient := cvlvgpb.NewCvlClient(vendorgatewayConn)
	manchClient := manchvgpb.NewManchClient(vendorgatewayConn)
	digioClient := digiovgpb.NewDigioClient(vendorgatewayConn)
	digilockerClient := digilockervgpb.NewDigilockerClient(vendorgatewayConn)
	obfuscatorClient := obfuscatorpb.NewObfuscatorClient(authConn)
	wealthRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["WealthRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = wealthRedisStore.Close() }()
	investmentProfileServiceClient := profilepb.NewInvestmentProfileServiceClient(wealthdmfConn)
	catalogClientVar3 := vgcatalogpb2.NewCatalogClient(vendorgatewayConn)
	securitiesRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["SecuritiesRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = securitiesRedisStore.Close() }()
	nPSClient := nps.NewNPSClient(vendorgatewayConn)

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor := servergenwire.AddQuestUserContextUnaryInterceptor(gconf)
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2, err := servergenwire2.CollapserInterceptor(collapserRueidisRedisStore, collapserRueidisCacheStorage)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	zincSearchClient := zinc.NewZincClient(zinc.NewHttpClient(), gconf.ZincClientConf(), gconf.ZincClientCredentials())

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	httpMux := http.NewServeMux()
	_ = httpMux

	err = setupAml(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, amlClient, vendorMappingServiceClient)
	if err != nil {
		return err
	}
	err = setupDeposit(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, healthEngineServiceClient, depositClient, customerClient, authClient, usersClient, savingsClient, savingsClientVar2, actorClient, piClient, accountPIRelationClient, orderServiceClient, paymentClient, commsClient, vKYCClient, ruleManagerClient, accountsClient, goalsClient, appFeedbackClient, groupClient, panClient, bankCustomerServiceClient, onboardingClient, accountingClient, balanceClient, celestialClient, payClient)
	if err != nil {
		return err
	}
	err = setupFittt(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, fitttPGDB, ruleManagerClient, actionBarClient, orderServiceClient, sportsManagerClient, fitttClient, fitttClientVar2, commsClientVar4, ruleUIManagerClient, orderManagerClient, fitttRedisStore, depositClientVar5, savingsClient, rateLimiterRedisStore, paymentHandlerClient, recurringPaymentServiceClient, balanceClient)
	if err != nil {
		return err
	}
	err = setupRms(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, rmsPGDB, schedulerServiceClient, actionBarClient, fitttClientVar2, orderManagerClient, wealthOnboardingClient, savingsClient, commsClientVar4, balanceClient, catalogManagerClient, recurringPaymentServiceClient, ruleManagerClient, accountingClient, fitttRedisStore)
	if err != nil {
		return err
	}
	err = setupGoals(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB)
	if err != nil {
		return err
	}
	err = setupInvestment(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiWealthCRDB, mutualFundClient, fileGeneratorClient, paymentHandlerClient, catalogManagerClientVar2, savingsClient, actorClient, commsClientVar10, kycClient, usersClient, authClient, wealthOnboardingClient, fitttClientVar2, authClientVar6, panClient, bankCustomerServiceClient, orderManagerClient, docsClient, ruleManagerClient, balanceClient, orderServiceClient, recurringPaymentServiceClient, ticketClient, paymentClient, piClient, groupClient, zincSearchClient, segmentationServiceClient, mfCatalogRedisStore, dynamicUIElementCacheRedisStore, dynamicUIElementServiceClient, depositClientVar5, p2PInvestmentClient, investmentRedisStore, portfolioManagerClient, holdingImporterClient, accountManagerClient, catalogManagerClient, orderManagerClientVar13, connectedAccountClient, nudgeServiceClient, epifiCRDB, consentClient, prerequisiteHandlerClient, watsonClient, uNNameCheckClient, onboardingClient)
	if err != nil {
		return err
	}
	err = setupP2pinvestment(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, p2pInvestmentLiquiloansCRDB, orderServiceClient, p2PClient, usersClient, actorClient, savingsClient, p2pInvestmentRedisStore, payClient, timelineServiceClient, docsClient, consentClient, commsClientVar17, groupClient, tieringClient, bankCustomerServiceClient, celestialClient, balanceClient, incidentManagerClient, watsonClient, devSimulatorP2PInvestmentClient)
	if err != nil {
		return err
	}
	err = setupUsstocks(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, usstocksAlpacaPGDB, celestialClient, internationalFundTransferClient, stocksClient, usersClient, savingsClient, orderServiceClient, internationalFundTransferClientVar2, bankCustomerServiceClient, commsClientVar17, actorClient, portfolioManagerClient, catalogManagerClient, uSStocksRedisStore, fileGeneratorClientVar8, accountManagerClient, preApprovedLoanClient, profileClient, authClient, payClient, stocksClientVar3, zincSearchClient, rateLimiterRedisStore, catalogClient, groupClient, amlClientVar2, ocrClient, onboardingClient, livenessClient, livenessClientVar2, uSStocksAccountRedisStore, vKYCClient, employmentClient, panClient, locationClient, wealthOnboardingClient, orderManagerClientVar13, dynamicUIElementServiceClient, rewardsGeneratorClient, investmentAggregatorClient, stocksClientVar7)
	if err != nil {
		return err
	}
	err = setupAnalyser(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, txnCategorizerClient, merchantServiceClient, analyserRedisStore, epifiWealthAnalyticsPGDB, actorClient, piClient, accountPIRelationClient, orderServiceClient, paymentClient, accountAggregatorClient, tieringClient, mFExternalOrdersClient, mFAnalyticsClient, investmentAnalyticsClient, catalogManagerClientVar2, commsClientVar23, usersClient, preApprovedLoanClient, serviceClient, netWorthClient, securitiesCatalogClient, epfClient)
	if err != nil {
		return err
	}
	err = setupCategorizer(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, categorizerPGDB, orderServiceClient, actorClient, groupClient, accountAggregatorClient, piClient, paymentClient, actorActivityClient, accountingClient, payClient, uPIClient, merchantServiceClient, txnAggregatesClient, connectedAccountClient, savingsClient, fireflyClient, accountPIRelationClient, usersClient, gPlaceClient, locationClientVar2, categorizerVMRedisStore)
	if err != nil {
		return err
	}
	err = setupInsights(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, insightsPGDB, accessInfoClient, actorClient, usersClient, actorInsightsPGDB, insightsClient, commsClientVar25, connectedAccountClient, useCaseDbResourceProvider, securitiesCatalogClient, groupClient, txnAggregatesClient, savingsClient, merchantServiceClient, piClient, txnCategorizerClient, fireflyClient, authClient, onboardingClient, emailParserClient, rewardsGeneratorClient, creditReportManagerClient, balanceClient, txnAggregatesClientVar4, accountingClient, rewardsAggregatesClient, employmentClientVar3, uNNameCheckClient, analyserRedisStore, employmentClient, investmentAnalyticsClient, investmentAggregatorClient, epfClient, mFExternalOrdersClient, netWorthClient, internationalFundTransferClient, zincSearchClient, preApprovedLoanClient, npsCatalogClient)
	if err != nil {
		return err
	}
	err = setupUpcomingtransactions(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, budgetingPGDB, ruleManagerClient, recurringPaymentServiceClient, actorClient)
	if err != nil {
		return err
	}
	err = setupIndexer(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker)
	if err != nil {
		return err
	}
	err = setupSearch(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, usersClient, actorClient, piClient, depositClientVar5, accountPIRelationClient, fitttClientVar2, accessInfoClient, offerListingServiceClient, searchRedisStore, groupClient, accrualClient, rewardsGeneratorClient, cardProvisioningClient, inAppReferralClient, connectedAccountClient, ruleManagerClient, merchantServiceClient, savingsClient, catalogManagerClientVar2, cardControlClient, uPIClient, timelineServiceClient, p2PInvestmentClient, onboardingClient, salaryProgramClient, employmentClient, orderServiceClient, preApprovedLoanClient, catalogManagerClient, fireflyClient, reminderServiceClient, portfolioManagerClient, bankCustomerServiceClient, exchangerOfferServiceClient, balanceClient, txnCategorizerClient)
	if err != nil {
		return err
	}
	err = setupBudgeting(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, ruleManagerClient, fireflyClient, billingClient, txnAggregatesClient, savingsClient, actorClient, budgetingPGDB, accountPIRelationClient, accountingClient, orderServiceClient, commsClientVar17)
	if err != nil {
		return err
	}
	err = setupGplace(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, vendordataPGDB, gPlaceClientVar3)
	if err != nil {
		return err
	}
	err = setupWealthonboarding(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiWealthCRDB, ckycClient, actorClient, usersClient, groupClient, employmentClient, savingsClient, authClient, customerClient, bankCustomerServiceClient, livenessClientVar2, nsdlClient, cvlClient, docsClient, manchClient, digioClient, consentClient, ocrClient, digilockerClient, commsClientVar17, obfuscatorClient, uNNameCheckClient, wealthRedisStore, ruleManagerClient, celestialClient, investmentProfileServiceClient, wealthOnboardingClient)
	if err != nil {
		return err
	}
	err = setupSecurities(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, stocksPGDB, catalogClientVar3, securitiesRedisStore, catalogManagerClient, investmentRedisStore)
	if err != nil {
		return err
	}
	err = setupNps(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, npsPGDB, nPSClient)
	if err != nil {
		return err
	}
	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := hook.InitInvestmentAndUsstocksServiceGroups(epifiWealthCRDB) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitInvestmentAndUsstocksServiceGroups"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	cleanupFnVar2, err := hook.LoadKmsKeysForLocalStackEnv() // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadKmsKeysForLocalStackEnv"), zap.Error(err))
		return err
	}
	defer cleanupFnVar2()

	cleanupFnVar3, err := hook.InitSearchServiceGroup() // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitSearchServiceGroup"), zap.Error(err))
		return err
	}
	defer cleanupFnVar3()

	cleanupFnVar4, err := servergenwire2.LoadCollapserWithGrpcServer(gconf, s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadCollapserWithGrpcServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar4()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupAml(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	amlClient amlvgpb.AmlClient,
	vendorMappingServiceClient vmpb.VendorMappingServiceClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	amlConf, err := amlconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.AML_SERVICE))
		return err
	}
	_ = amlConf

	caseDecisionPublisher, err := sns.NewSnsPublisherWithConfig(ctx, amlConf.CaseDecisionPublisher, awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	fl1Fl43GeneratorS3Client := s3pkg.NewClient(awsConf, amlConf.AWS.S3.BucketName)

	service := wire.InitializeService(epifiCRDB, crdbResourceMap, crdbTxnResourceMap, amlClient, vendorMappingServiceClient, amlConf, caseDecisionPublisher)

	if func(conf *config.Config) bool {
		amlConf, err := amlconf.Load()
		if err != nil {
			logger.Panic("failed to load aml config", zap.Error(err))
		}
		return amlConf.EnableService
	}(conf) {
		amlpb.RegisterAmlServer(s, service)
	}

	amlDevService := wire.InitializeDevService(epifiCRDB, crdbResourceMap)

	if func(conf *config.Config) bool {
		amlConf, err := amlconf.Load()
		if err != nil {
			logger.Panic("failed to load aml config", zap.Error(err))
		}
		return amlConf.EnableService
	}(conf) {
		amldevpb.RegisterAmlDevServiceServer(s, amlDevService)
	}

	serviceVar2 := wire.InitializeConsumer(epifiCRDB, crdbResourceMap, crdbTxnResourceMap, vendorMappingServiceClient, amlConf, fl1Fl43GeneratorS3Client)

	if func(conf *config.Config) bool {
		amlConf, err := amlconf.Load()
		if err != nil {
			logger.Panic("failed to load aml config", zap.Error(err))
		}
		return amlConf.EnableService
	}(conf) {
		_, err = sqs.NewSubscriberWithConfigV1(ctx, amlConf.VnCaseDecisionSubscriber, sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			amlpb.RegisterProcessCallbackForDecisionsOnCaseMethodToSubscriber(subscriber, serviceVar2)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		amlConf, err := amlconf.Load()
		if err != nil {
			logger.Panic("failed to load aml config", zap.Error(err))
		}
		return amlConf.EnableService
	}(conf) {
		_, err = sqs.NewSubscriberWithConfigV1(ctx, amlConf.FileGenerationSubscriber, sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			amlpb.RegisterGenerateFilesMethodToSubscriber(subscriber, serviceVar2)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	configNameToConfMap[cfg.ConfigName(cfg.AML_SERVICE)] = &commonexplorer.Config{StaticConf: &amlconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupDeposit(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	healthEngineServiceClient healthenginepb.HealthEngineServiceClient,
	depositClient vgdepositpb.DepositClient,
	customerClient vgcustomerpb.CustomerClient,
	authClient authpb.AuthClient,
	usersClient user.UsersClient,
	savingsClient savingspb.SavingsClient,
	savingsClientVar2 vgsavingspb.SavingsClient,
	actorClient actor.ActorClient,
	piClient pipb.PiClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	orderServiceClient orderpb.OrderServiceClient,
	paymentClient paymentpb.PaymentClient,
	commsClient depositwiretypes.DepositCommsClientWithInterceptors,
	vKYCClient vkycpb.VKYCClient,
	ruleManagerClient rmsmanagerpb.RuleManagerClient,
	accountsClient vgaccountpb.AccountsClient,
	goalsClient goalspb.GoalsClient,
	appFeedbackClient inapppb.AppFeedbackClient,
	groupClient usergrouppb.GroupClient,
	panClient panpb.PanClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	onboardingClient onboardingpb.OnboardingClient,
	accountingClient fireflyaccpb.AccountingClient,
	balanceClient accountbalancepb.BalanceClient,
	celestialClient celestialpb.CelestialClient,
	payClient paypb.PayClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	depositConf, err := depositconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.DEPOSIT_SERVICE))
		return err
	}
	_ = depositConf

	depositGenConf, err := dynconf.LoadConfig(depositconf.Load, genconf2.NewConfig, cfg.DEPOSIT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.DEPOSIT_SERVICE))
		return err
	}

	_ = depositGenConf

	investmentInstrumentEventPublisher, err := sqs.NewPublisherWithConfig(ctx, depositGenConf.InvestmentInstrumentEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	serviceVar3 := wire2.InitializeService(epifiCRDB, healthEngineServiceClient, depositClient, customerClient, authClient, usersClient, savingsClient, savingsClientVar2, actorClient, piClient, accountPIRelationClient, orderServiceClient, paymentClient, commsClient, vKYCClient, ruleManagerClient, accountsClient, goalsClient, broker, appFeedbackClient, depositConf, depositGenConf, investmentInstrumentEventPublisher, groupClient, panClient, bankCustomerServiceClient, onboardingClient, accountingClient, balanceClient, celestialClient, payClient)

	depositpb.RegisterDepositServer(s, serviceVar3)

	depositDev := wire2.InitializeDevDepositService(epifiCRDB, piClient, paymentClient)

	developer2.RegisterDevDepositServer(s, depositDev)

	serviceVar4 := wire2.InitializeDepositSchedulerService(investmentInstrumentEventPublisher)

	depositschedulerpb.RegisterSchedulerServer(s, serviceVar4)

	serviceVar5 := wire2.InitializeDepositReconciliationService(epifiCRDB, paymentClient, piClient, savingsClient, accountsClient, authClient, actorClient, bankCustomerServiceClient, depositClient)

	depositreconpb.RegisterReconciliationServer(s, serviceVar5)

	serviceVar6 := wire2.InitializeWatsonClientService(epifiCRDB)

	depositwatsonpb.RegisterWatsonServer(s, serviceVar6)

	callbackService := wire2.InitializeCallbackConsumerService(epifiCRDB, healthEngineServiceClient, depositClient, customerClient, authClient, usersClient, savingsClient, savingsClientVar2, actorClient, piClient, accountPIRelationClient, orderServiceClient, paymentClient, commsClient, vKYCClient, ruleManagerClient, accountsClient, goalsClient, broker, appFeedbackClient, depositConf, depositGenConf, investmentInstrumentEventPublisher, groupClient, panClient, bankCustomerServiceClient, onboardingClient, accountingClient, balanceClient, celestialClient, payClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, depositGenConf.CreateDepositCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		depositpb.RegisterProcessCreateDepositCallbackMethodToSubscriber(subscriber, callbackService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, depositGenConf.PreCloseDepositCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		depositpb.RegisterProcessPreCloseDepositCallbackMethodToSubscriber(subscriber, callbackService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, depositGenConf.FdAutoRenewCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		depositpb.RegisterProcessDepositMaturityActionCallbackMethodToSubscriber(subscriber, callbackService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar7 := wire2.InitializeConsumerService(epifiCRDB, healthEngineServiceClient, savingsClientVar2, actorClient, piClient, authClient, broker, depositClient, customerClient, usersClient, savingsClient, accountPIRelationClient, orderServiceClient, paymentClient, commsClient, vKYCClient, ruleManagerClient, accountsClient, goalsClient, appFeedbackClient, depositConf, depositGenConf, investmentInstrumentEventPublisher, groupClient, panClient, bankCustomerServiceClient, onboardingClient, accountingClient, balanceClient, celestialClient, payClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, depositGenConf.OrderUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		depositpb.RegisterUpdateDepositBalanceMethodToSubscriber(subscriber, serviceVar7)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.DEPOSIT_SERVICE)] = &commonexplorer.Config{StaticConf: &depositconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupFittt(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	fitttPGDB types.FitttPGDB,
	ruleManagerClient rmsmanagerpb.RuleManagerClient,
	actionBarClient search.ActionBarClient,
	orderServiceClient orderpb.OrderServiceClient,
	sportsManagerClient sportspb.SportsManagerClient,
	fitttClient fittt.FitttClient,
	fitttClientVar2 fitttpb.FitttClient,
	commsClientVar4 fittypes.FitttCommsClientWithInterceptors,
	ruleUIManagerClient ui.RuleUIManagerClient,
	orderManagerClient mforderpb.OrderManagerClient,
	fitttRedisStore fittypes.FitttRedisStore,
	depositClientVar5 depositpb.DepositClient,
	savingsClient savingspb.SavingsClient,
	rateLimiterRedisStore types.RateLimiterRedisStore,
	paymentHandlerClient investpaypb.PaymentHandlerClient,
	recurringPaymentServiceClient recurringpaymentpb.RecurringPaymentServiceClient,
	balanceClient accountbalancepb.BalanceClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	fitttConf, err := fitttconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.FITTT_SERVICE))
		return err
	}
	_ = fitttConf

	fitttGenConf, err := dynconf.LoadConfigWithQuestConfig(fitttconf.Load, genconf3.NewConfigWithQuest, cfg.FITTT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.FITTT_SERVICE))
		return err
	}

	if !conf.QuestSdk.Disable {
		fitttGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: fitttGenConf, SdkConfig: fitttGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{fitttGenConfAppConfig}, string(cfg.WEALTH_DMF_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = fitttGenConf

	fitttEnrichedEventPublisher, err := sqs.NewPublisherWithConfig(ctx, fitttGenConf.FitttEnrichedEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	sportsRewardsEventPublisher, err := sqs.NewPublisherWithConfig(ctx, fitttGenConf.SportsRewardsEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	aggregatedExecutionTriggerPublisher, err := sqs.NewPublisherWithConfig(ctx, fitttGenConf.AggregatedExecutionTriggerPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	initCricketMatchUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, fitttGenConf.InitCricketMatchUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	aggregatedExecutionPublisher, err := sqs.NewPublisherWithConfig(ctx, fitttGenConf.AggregatedExecutionPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	actionProcessorPublisher, err := sqs.NewPublisherWithConfig(ctx, fitttGenConf.ActionProcessorPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	executionUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, fitttGenConf.ExecutionUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	aggregationEntityPublisher, err := sqs.NewPublisherWithConfig(ctx, fitttGenConf.AggregationEntityPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	rMSEventPublisher, err := sqs.NewPublisherWithConfig(ctx, fitttGenConf.RMSEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	cricketEventPublisher, err := sqs.NewPublisherWithConfig(ctx, fitttGenConf.CricketEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	fitttServer := wire3.InitialiseFitttService(fitttPGDB, fitttGenConf, ruleManagerClient, actionBarClient, fitttEnrichedEventPublisher, sportsRewardsEventPublisher, orderServiceClient)

	fitttpb.RegisterFitttServer(s, fitttServer)

	serviceVar8 := wire3.InitialiseSchedulerService(fitttGenConf, fitttPGDB, sportsManagerClient, fitttEnrichedEventPublisher, aggregatedExecutionTriggerPublisher, fitttClient, fitttClientVar2, ruleManagerClient, commsClientVar4, actorClient)

	schedulerpb.RegisterSchedulerServiceServer(s, serviceVar8)

	fITTTDbStatesService := wire3.InitializeFITTTDbStateService(fitttPGDB, fitttGenConf)

	fitttdev.RegisterFITTTDbStatesServer(s, fITTTDbStatesService)

	serviceVar9 := wire3.InitialiseSportsManager(fitttGenConf, fitttPGDB, initCricketMatchUpdatePublisher)

	sportspb.RegisterSportsManagerServer(s, serviceVar9)

	serviceVar10, err := wire3.InitialiseDevconsoleService(fitttPGDB, fitttClient, ruleManagerClient, ruleUIManagerClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	devconsolepb.RegisterDeveloperConsoleServiceServer(s, serviceVar10)

	aggregationEntityConsumer := wire3.InitialiseAggrEntityConsumer(fitttGenConf, fitttPGDB, aggregatedExecutionTriggerPublisher)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fitttGenConf.AggregationEntitySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer3.RegisterProcessAggregationEntityMethodToSubscriber(subscriber, aggregationEntityConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	executionTriggerConsumer := wire3.InitialiseAggrExecTriggerConsumer(fitttGenConf, fitttPGDB, ruleManagerClient, aggregatedExecutionPublisher, orderManagerClient, fitttRedisStore)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fitttGenConf.AggregatedExecutionTriggerSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer3.RegisterPerformAggregationMethodToSubscriber(subscriber, executionTriggerConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	aggregatedExecutionConsumer := wire3.InitialiseAggrExecutionConsumer(fitttGenConf, fitttPGDB, depositClientVar5, commsClientVar4, ruleManagerClient, actorClient, usersClient, groupClient, savingsClient, rateLimiterRedisStore, actionProcessorPublisher)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fitttGenConf.AggregatedExecutionSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer3.RegisterProcessExecutionMethodToSubscriber(subscriber, aggregatedExecutionConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	eventConsumer := wire3.InitialiseEventConsumer(fitttGenConf, fitttEnrichedEventPublisher, fitttPGDB, depositClientVar5, orderServiceClient, commsClientVar4, ruleManagerClient, actorClient, savingsClient, orderManagerClient, paymentHandlerClient, broker, executionUpdatePublisher, aggregationEntityPublisher, rateLimiterRedisStore, recurringPaymentServiceClient, usersClient, groupClient, balanceClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fitttGenConf.OrderEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessOrderUpdateEventMethodToSubscriber(subscriber, eventConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fitttGenConf.CricketEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessSportsMatchUpdateEventMethodToSubscriber(subscriber, eventConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fitttGenConf.SalaryDetectedEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessSalaryDetectedEventMethodToSubscriber(subscriber, eventConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	actionConsumer := wire3.InitialiseActionConsumer(fitttGenConf, fitttPGDB, depositClientVar5, orderServiceClient, commsClientVar4, ruleManagerClient, actorClient, savingsClient, orderManagerClient, paymentHandlerClient, broker, executionUpdatePublisher, aggregationEntityPublisher, rateLimiterRedisStore, recurringPaymentServiceClient, usersClient, groupClient, balanceClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fitttGenConf.ActionProcessorSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer5.RegisterProcessActionMethodToSubscriber(subscriber, actionConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	orchestratorConsumer := wire3.InitialiseOrchestratorConsumer(fitttGenConf, fitttPGDB, actorClient, commsClientVar4, ruleManagerClient, rMSEventPublisher, actionProcessorPublisher)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fitttGenConf.RMSActionsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer6.RegisterProcessRmsActionMethodToSubscriber(subscriber, orchestratorConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fitttGenConf.EnrichedEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer6.RegisterProcessEnrichedEventMethodToSubscriber(subscriber, orchestratorConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar11 := wire3.InitialiseFitttCricketConsumerService(fitttGenConf, fitttPGDB, cricketEventPublisher, fitttClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, fitttGenConf.InitCricketMatchUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		sportspb.RegisterProcessMatchUpdateMethodToSubscriber(subscriber, serviceVar11)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.FITTT_SERVICE)] = &commonexplorer.Config{StaticConf: &fitttconf.Config{}, QuestIntegratedConfig: fitttGenConf}

	return nil

}

// nolint: funlen
func setupRms(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	rmsPGDB types.RmsPGDB,
	schedulerServiceClient schedulerpb.SchedulerServiceClient,
	actionBarClient search.ActionBarClient,
	fitttClientVar2 fitttpb.FitttClient,
	orderManagerClient mforderpb.OrderManagerClient,
	wealthOnboardingClient wealthpb.WealthOnboardingClient,
	savingsClient savingspb.SavingsClient,
	commsClientVar4 fittypes.FitttCommsClientWithInterceptors,
	balanceClient accountbalancepb.BalanceClient,
	catalogManagerClient usstockscatalogmanagerpb.CatalogManagerClient,
	recurringPaymentServiceClient recurringpaymentpb.RecurringPaymentServiceClient,
	ruleManagerClient rmsmanagerpb.RuleManagerClient,
	accountingClient fireflyaccpb.AccountingClient,
	fitttRedisStore fittypes.FitttRedisStore) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	rmsConf, err := rmsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.RMS_SERVICE))
		return err
	}
	_ = rmsConf

	rmsGenConf, err := dynconf.LoadConfigWithQuestConfig(rmsconf.Load, genconf4.NewConfigWithQuest, cfg.RMS_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.RMS_SERVICE))
		return err
	}

	if !conf.QuestSdk.Disable {
		rmsGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: rmsGenConf, SdkConfig: rmsGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{rmsGenConfAppConfig}, string(cfg.WEALTH_DMF_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = rmsGenConf

	actionProcessingPublisher, err := sqs.NewPublisherWithConfig(ctx, rmsGenConf.ActionProcessingPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	investmentInstrumentEventPublisherVar2, err := sqs.NewPublisherWithConfig(ctx, rmsGenConf.InvestmentInstrumentEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	rmsBudgetingDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, rmsGenConf.RmsBudgetingPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	actionProcessingDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, rmsGenConf.ActionProcessingPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	fitttRewardsExecutionUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, rmsGenConf.FitttRewardsExecutionUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	commandProcessorPublisher, err := sqs.NewPublisherWithConfig(ctx, rmsGenConf.CommandProcessorPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	rmsEventPublisher, err := sqs.NewPublisherWithConfig(ctx, rmsGenConf.RmsEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	ruleManagerServer := wire4.InitializeRuleManagerService(rmsGenConf, rmsPGDB, actionProcessingPublisher, broker, schedulerServiceClient, actionBarClient, fitttClientVar2, actorClient, orderManagerClient, wealthOnboardingClient, usersClient, groupClient, investmentInstrumentEventPublisherVar2, rmsBudgetingDelayPublisher, savingsClient, commsClientVar4, balanceClient, catalogManagerClient, recurringPaymentServiceClient, ruleManagerClient)

	rmsmanagerpb.RegisterRuleManagerServer(s, ruleManagerServer)

	ruleUIManager := wire4.InitializeRuleUiManager(rmsGenConf, rmsPGDB)

	ui.RegisterRuleUIManagerServer(s, ruleUIManager)

	commandProcessorService := wire4.InitializeNewCommandProcessorService(rmsGenConf, rmsPGDB, actionProcessingDelayPublisher, actorClient, savingsClient, ruleManagerClient, rmsBudgetingDelayPublisher, accountingClient, balanceClient)

	commandprocessor2.RegisterCommandProcessorServer(s, commandProcessorService)

	rMSDbStatesService := wire4.InitializeRMSDbStateService(rmsGenConf, rmsPGDB, ruleManagerClient)

	developer5.RegisterRMSDbStatesServer(s, rMSDbStatesService)

	commandProcessorConsumer := wire4.InitializeCommandProcessorConsumer(rmsGenConf, rmsPGDB, actionProcessingDelayPublisher, actorClient, savingsClient, ruleManagerClient, rmsBudgetingDelayPublisher, accountingClient, balanceClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, rmsGenConf.CommandProcessorSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		commandprocessor2.RegisterProcessCommandsMethodToSubscriber(subscriber, commandProcessorConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	rMSOrchestratorConsumer := wire4.InitializeRMSOrchestratorService(rmsGenConf, rmsPGDB, fitttRewardsExecutionUpdatePublisher, fitttRedisStore, ruleManagerClient, commandProcessorPublisher, rmsEventPublisher)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, rmsGenConf.RMSEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer7.RegisterProcessRMSEventMethodToSubscriber(subscriber, rMSOrchestratorConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, rmsGenConf.ActionExecutionUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer7.RegisterProcessActionExecutionUpdateEventMethodToSubscriber(subscriber, rMSOrchestratorConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.RMS_SERVICE)] = &commonexplorer.Config{StaticConf: &rmsconf.Config{}, QuestIntegratedConfig: rmsGenConf}

	return nil

}

// nolint: funlen
func setupGoals(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	goalsConf, err := goalsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.GOALS_SERVICE))
		return err
	}
	_ = goalsConf

	serviceVar12 := wire5.InitializeService(epifiCRDB)

	goalspb.RegisterGoalsServer(s, serviceVar12)

	configNameToConfMap[cfg.ConfigName(cfg.GOALS_SERVICE)] = &commonexplorer.Config{StaticConf: &goalsconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupInvestment(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiWealthCRDB types.EpifiWealthCRDB,
	mutualFundClient mf.MutualFundClient,
	fileGeneratorClient fgpb.FileGeneratorClient,
	paymentHandlerClient investpaypb.PaymentHandlerClient,
	catalogManagerClientVar2 catalogpb.CatalogManagerClient,
	savingsClient savingspb.SavingsClient,
	actorClient actor.ActorClient,
	commsClientVar10 wtypes.InvestmentCommsClientWithInterceptors,
	kycClient kyc.KycClient,
	usersClient user.UsersClient,
	authClient authpb.AuthClient,
	wealthOnboardingClient wealthpb.WealthOnboardingClient,
	fitttClientVar2 fitttpb.FitttClient,
	authClientVar6 auth.AuthClient,
	panClient panpb.PanClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	orderManagerClient mforderpb.OrderManagerClient,
	docsClient docs.DocsClient,
	ruleManagerClient rmsmanagerpb.RuleManagerClient,
	balanceClient accountbalancepb.BalanceClient,
	orderServiceClient orderpb.OrderServiceClient,
	recurringPaymentServiceClient recurringpaymentpb.RecurringPaymentServiceClient,
	ticketClient ticket.TicketClient,
	paymentClient paymentpb.PaymentClient,
	piClient pipb.PiClient,
	groupClient usergrouppb.GroupClient,
	zincSearchClient zinc.SearchClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	mfCatalogRedisStore wtypes.MfCatalogRedisStore,
	dynamicUIElementCacheRedisStore wtypes.DynamicUIElementCacheRedisStore,
	dynamicUIElementServiceClient dynamicuielementpb.DynamicUIElementServiceClient,
	depositClientVar5 depositpb.DepositClient,
	p2PInvestmentClient p2pinvestment.P2PInvestmentClient,
	investmentRedisStore types.InvestmentRedisStore,
	portfolioManagerClient portfolio.PortfolioManagerClient,
	holdingImporterClient holdingsimporterpb.HoldingImporterClient,
	accountManagerClient usstocksaccountpb.AccountManagerClient,
	catalogManagerClient usstockscatalogmanagerpb.CatalogManagerClient,
	orderManagerClientVar13 usstocksorderpb.OrderManagerClient,
	connectedAccountClient connectedaccpb.ConnectedAccountClient,
	nudgeServiceClient nudgepb.NudgeServiceClient,
	epifiCRDB types.EpifiCRDB,
	consentClient consent.ConsentClient,
	prerequisiteHandlerClient prhpb.PrerequisiteHandlerClient,
	watsonClient watsonpb.WatsonClient,
	uNNameCheckClient ncpb.UNNameCheckClient,
	onboardingClient onboardingpb.OnboardingClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	investmentConf, err := investmentconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.INVESTMENT_SERVICE))
		return err
	}
	_ = investmentConf

	investmentGenConf, err := dynconf.LoadConfig(investmentconf.Load, investmentgenconf.NewConfig, cfg.INVESTMENT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.INVESTMENT_SERVICE))
		return err
	}

	_ = investmentGenConf

	vendorOrderFileStatusPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.VendorOrderFileStatusPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	deferredNotificationDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.DeferredNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	orderDelayedNotificationDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.OrderDelayedNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	settlementOrderUpdatesDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.SettlementOrderUpdatesPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	investmentInstrumentEventPublisherVar3, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.InvestmentInstrumentEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	orderETADelayPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.OrderETAPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	processMutualFundOrderPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.ProcessMutualFundOrderPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	elogPreRequisiteStatusPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.ElogPreRequisiteStatusPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	fATCAPreRequisiteStatusPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.FATCAPreRequisiteStatusPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	preRequisiteHandlerRetryPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.PreRequisiteHandlerRetryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	recentlyVisitedPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.RecentlyVisitedPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	mfStatementPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.MfStatementPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	nonFinancialEventSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.NonFinancialEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	processFileGenPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.ProcessFileGenPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	processFileGenSuccessPublisher, err := sqs.NewPublisherWithConfig(ctx, investmentGenConf.ProcessFileGenSuccessPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	investmentEventBasedNotificationsSqsCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		investmentGenConf.InvestmentEventBasedNotificationsSqsCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(investmentGenConf.InvestmentEventBasedNotificationsSqsCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return err
	}

	investmentEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, investmentGenConf.InvestmentEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	nonFinancialEventSnsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, investmentGenConf.NonFinancialEventSnsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	camsS3Client := s3pkg.NewClient(awsConf, investmentGenConf.Application().FileGenerator().CamsS3Bucket)
	karvyS3Client := s3pkg.NewClient(awsConf, investmentGenConf.Application().FileGenerator().KarvyS3Bucket)
	camsS3ClientVar2 := s3pkg.NewClient(awsConf, investmentGenConf.Application().FileGenerator().CamsS3Bucket)
	camsS3ClientVar3 := s3pkg.NewClient(awsConf, investmentGenConf.Application().FileGenerator().CamsS3Bucket)
	karvyS3ClientVar2 := s3pkg.NewClient(awsConf, investmentGenConf.Application().FileGenerator().KarvyS3Bucket)
	mFOpsS3Client := s3pkg.NewClient(awsConf, investmentGenConf.Application().Operations().S3Bucket)
	camsS3ClientVar4 := s3pkg.NewClient(awsConf, investmentGenConf.Application().FileGenerator().CamsS3Bucket)
	karvyS3ClientVar3 := s3pkg.NewClient(awsConf, investmentGenConf.Application().FileGenerator().KarvyS3Bucket)
	mFCatalogS3Client := s3pkg.NewClient(awsConf, investmentGenConf.MutualFundCatalogS3Conf().BucketName)
	camsS3ClientVar5 := s3pkg.NewClient(awsConf, investmentGenConf.Application().FileGenerator().CamsS3Bucket)
	karvyS3ClientVar4 := s3pkg.NewClient(awsConf, investmentGenConf.Application().FileGenerator().KarvyS3Bucket)
	camsS3ClientVar6 := s3pkg.NewClient(awsConf, investmentGenConf.Application().FileGenerator().CamsS3Bucket)
	karvyS3ClientVar5 := s3pkg.NewClient(awsConf, investmentGenConf.Application().FileGenerator().KarvyS3Bucket)
	mFCatalogS3ClientVar2 := s3pkg.NewClient(awsConf, investmentGenConf.MutualFundCatalogS3Conf().BucketName)
	mFCatalogS3ClientVar3 := s3pkg.NewClient(awsConf, investmentGenConf.MutualFundCatalogS3Conf().BucketName)

	pgdbConns := gconf.PgdbConns()

	serviceVar13, err := wire6.InitialiseOrderManagerSvc(ctx, epifiWealthCRDB, sqsClient, mutualFundClient, fileGeneratorClient, vendorOrderFileStatusPublisher, camsS3Client, karvyS3Client, paymentHandlerClient, catalogManagerClientVar2, investmentGenConf, savingsClient, actorClient, commsClientVar10, kycClient, usersClient, authClient, wealthOnboardingClient, fitttClientVar2, deferredNotificationDelayPublisher, orderDelayedNotificationDelayPublisher, settlementOrderUpdatesDelayPublisher, investmentInstrumentEventPublisherVar3, authClientVar6, panClient, bankCustomerServiceClient, orderETADelayPublisher)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	mforderpb.RegisterOrderManagerServer(s, serviceVar13)

	serviceVar14 := wire6.InitializeReverseFeedService(catalogManagerClientVar2, camsS3ClientVar2, wealthOnboardingClient, fileGeneratorClient, mutualFundClient)

	rfpb.RegisterReverseFeedManagerServer(s, serviceVar14)

	serviceVar15 := wire6.InitializeSettlementOrderUpdatesConsumerService(epifiWealthCRDB, orderManagerClient)

	orderconsumerpb.RegisterOrderUpdateEventProcessorServer(s, serviceVar15)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.SettlementOrderUpdatesSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderconsumerpb.RegisterUpdateSellOrderAfterSettlementPeriodMethodToSubscriber(subscriber, serviceVar15)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar16 := wire6.InitialiseFileGeneratorSvc(epifiWealthCRDB, camsS3ClientVar3, karvyS3ClientVar2, investmentGenConf, orderManagerClient, wealthOnboardingClient, catalogManagerClientVar2, paymentHandlerClient, savingsClient, authClientVar6, docsClient, awsConf, usersClient, ruleManagerClient, balanceClient)

	fgpb.RegisterFileGeneratorServer(s, serviceVar16)

	serviceVar17 := wire6.InitialiseVendorOrderStatusConsumerSvc(epifiWealthCRDB, mutualFundClient, investmentGenConf, commsClientVar10, actorClient, catalogManagerClientVar2, paymentHandlerClient, authClient, settlementOrderUpdatesDelayPublisher, investmentInstrumentEventPublisherVar3, usersClient)

	vosconsumerpb.RegisterVendorOrderStatusConsumerServer(s, serviceVar17)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.VendorOrderFileStatusSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vosconsumerpb.RegisterProcessOrderFeedFileStatusMethodToSubscriber(subscriber, serviceVar17)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar18 := wire6.InitialiseSchedulerService(epifiWealthCRDB, fileGeneratorClient, catalogManagerClientVar2, paymentHandlerClient, processMutualFundOrderPublisher, commsClientVar10, usersClient, investmentGenConf, mutualFundClient, wealthOnboardingClient, actorClient, settlementOrderUpdatesDelayPublisher, investmentInstrumentEventPublisherVar3)

	scheduler4.RegisterSchedulerServer(s, serviceVar18)

	serviceVar19 := wire6.InitialisePrerequisiteHandlerService(epifiWealthCRDB, wealthOnboardingClient, fileGeneratorClient, elogPreRequisiteStatusPublisher, fATCAPreRequisiteStatusPublisher, preRequisiteHandlerRetryPublisher)

	prhpb.RegisterPrerequisiteHandlerServer(s, serviceVar19)

	serviceVar20 := wire6.InitialisePrerequisiteConsumerSvc(epifiWealthCRDB, mutualFundClient, orderManagerClient, wealthOnboardingClient, fileGeneratorClient, elogPreRequisiteStatusPublisher, fATCAPreRequisiteStatusPublisher, preRequisiteHandlerRetryPublisher)

	prhpbconsumer.RegisterPrerequisiteConsumerServer(s, serviceVar20)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.PreRequisiteHandlerRetrySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		prhpbconsumer.RegisterGetPreRequisiteStatusRetryMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.ElogPreRequisiteStatusSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		prhpbconsumer.RegisterProcessPrerequisiteFileMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.FATCAPreRequisiteStatusSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		prhpbconsumer.RegisterProcessPrerequisiteFileMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar21 := wire6.InitializePaymentUpdateConsumerSvc(epifiWealthCRDB, orderManagerClient, orderServiceClient, recurringPaymentServiceClient, investmentGenConf)

	phpbconsumer.RegisterPaymentUpdateEventProcessorServer(s, serviceVar21)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.PaymentUpdateEventProcessorSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		phpbconsumer.RegisterUpdatePaymentStatusFromOMSMethodToSubscriber(subscriber, serviceVar21)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar22 := wire6.InitialisePaymentHandlerSvc(epifiWealthCRDB, orderManagerClient, recurringPaymentServiceClient, orderServiceClient, investmentGenConf)

	investpaypb.RegisterPaymentHandlerServer(s, serviceVar22)

	serviceVar23 := wire6.InitializePaymentHandlerSchedulerSvc(epifiWealthCRDB, orderManagerClient, recurringPaymentServiceClient, orderServiceClient, investmentGenConf)

	payschpb.RegisterSchedulerServer(s, serviceVar23)

	serviceVar24 := wire6.InitialiseMFOrderOperationsSvc(epifiWealthCRDB, mFOpsS3Client, catalogManagerClientVar2, wealthOnboardingClient, paymentHandlerClient, usersClient, actorClient, savingsClient, orderManagerClient, fileGeneratorClient, investmentGenConf, ticketClient, camsS3ClientVar4, karvyS3ClientVar3, paymentClient, piClient)

	mfoperationspb.RegisterOrderOperationsServer(s, serviceVar24)

	serviceVar25 := wire6.InitialiseCatalogManagerSvc(epifiWealthCRDB, mFCatalogS3Client, investmentGenConf, groupClient, usersClient, actorClient, zincSearchClient, recentlyVisitedPublisher, deferredNotificationDelayPublisher, segmentationServiceClient, mutualFundClient, wealthOnboardingClient, mfCatalogRedisStore)

	catalogpb.RegisterCatalogManagerServer(s, serviceVar25)

	mfFolioService := wire6.InitialiseMfFolioService(epifiWealthCRDB, investmentGenConf)

	foliodetails.RegisterMfFolioServiceServer(s, mfFolioService)

	mutualFundDbStatesService := wire6.InitializeMutualFundDBStatesSvc(epifiWealthCRDB, investmentGenConf, paymentHandlerClient, dynamicUIElementCacheRedisStore)

	developer7.RegisterMutualFundDbStatesServer(s, mutualFundDbStatesService)

	serviceVar26 := wire6.InitialiseAuthSvc(epifiWealthCRDB)

	auth.RegisterAuthServer(s, serviceVar26)

	serviceVar27 := wire6.InitializeCatalogSchedulerSvc(epifiWealthCRDB)

	catalogschpb.RegisterSchedulerServer(s, serviceVar27)

	serviceVar28 := wire6.InitializeReconciliationSvc(epifiWealthCRDB, orderManagerClient, catalogManagerClientVar2, camsS3ClientVar5, karvyS3ClientVar4, investmentGenConf)

	reconciliationpb.RegisterReconciliationServiceServer(s, serviceVar28)

	serviceVar29 := wire6.InitializeMutualFundNotificationsSvc(investmentGenConf, epifiWealthCRDB, ruleManagerClient, commsClientVar10, paymentHandlerClient, authClient, actorClient, orderDelayedNotificationDelayPublisher, deferredNotificationDelayPublisher, catalogManagerClientVar2, wealthOnboardingClient, usersClient, groupClient, segmentationServiceClient, dynamicUIElementServiceClient)

	notificationspb.RegisterNotificationsServer(s, serviceVar29)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.DeferredNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notificationspb.RegisterSendDeferredNotificationMethodToSubscriber(subscriber, serviceVar29)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.OrderDelayedNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notificationspb.RegisterSendDelayedOrderNotificationMethodToSubscriber(subscriber, serviceVar29)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.OmsEventProcessorSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notificationspb.RegisterProcessOmsOrderUpdateMethodToSubscriber(subscriber, serviceVar29)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar30 := wire6.InitializeInvestmentAggregatorSvc(catalogManagerClientVar2, depositClientVar5, p2PInvestmentClient, investmentRedisStore, portfolioManagerClient, investmentEventPublisher)

	aggregatorpb.RegisterInvestmentAggregatorServer(s, serviceVar30)

	serviceVar31 := wire6.InitialiseMfStatementConsumerService(epifiWealthCRDB, usersClient, docsClient, wealthOnboardingClient, commsClientVar10, ruleManagerClient, investmentGenConf)

	statementconsumerpb.RegisterStatementConsumerServer(s, serviceVar31)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.MfStatementSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		statementconsumerpb.RegisterGenerateStatementMethodToSubscriber(subscriber, serviceVar31)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar32 := wire6.InitialiseDynamicUIElementService(epifiWealthCRDB, investmentGenConf, segmentationServiceClient, actorClient, usersClient, groupClient, dynamicUIElementCacheRedisStore)

	dynamicuielementpb.RegisterDynamicUIElementServiceServer(s, serviceVar32)

	serviceVar33 := wire6.InitializeMFExternalOrderService(epifiWealthCRDB, investmentGenConf, holdingImporterClient, wealthOnboardingClient, mutualFundClient)

	mfexternalorderpb.RegisterMFExternalOrdersServer(s, serviceVar33)

	serviceVar34 := wire6.InitialiseInvestmentWatsonClientService(epifiWealthCRDB, investmentGenConf)

	investmentwatson.RegisterWatsonServer(s, serviceVar34)

	serviceVar35 := wire6.InitializeEventProcessorService(pgdbConns, crdbTxnResourceMap, investmentGenConf, epifiWealthCRDB, crdbResourceMap, investmentEventBasedNotificationsSqsCustomDelayPublisher, commsClientVar10, actorClient, accountManagerClient, catalogManagerClient, orderManagerClientVar13, connectedAccountClient, p2PInvestmentClient, nudgeServiceClient)

	investmenteventprocessorpb.RegisterEventProcessorServer(s, serviceVar35)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.InvestmentEventBasedNotificationsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		investmenteventprocessorpb.RegisterSendUserJourneyNotificationsMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar36 := wire6.InitialiseStatementSvc(mfStatementPublisher)

	statementpb2.RegisterStatementServiceServer(s, serviceVar36)

	serviceVar37 := wire6.InitialiseProfileService(epifiWealthCRDB, epifiCRDB, usersClient, actorClient, consentClient, investmentGenConf, nonFinancialEventSqsPublisher)

	profilepb.RegisterInvestmentProfileServiceServer(s, serviceVar37)

	serviceVar38 := wire6.InitialiseInvestmentOrchestratorConsumer(epifiWealthCRDB, fileGeneratorClient, processFileGenPublisher, processFileGenSuccessPublisher, prerequisiteHandlerClient, investmentGenConf, orderServiceClient, catalogManagerClientVar2, actorClient, commsClientVar10, usersClient, settlementOrderUpdatesDelayPublisher, investmentInstrumentEventPublisherVar3)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.ProcessMutualFundOrderSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orchestrator2.RegisterProcessOrdersMethodToSubscriber(subscriber, serviceVar38)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.ProcessFileGenSuccessSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orchestrator2.RegisterProcessOrderFileGenerationSuccessMethodToSubscriber(subscriber, serviceVar38)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.ProcessFileGenSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orchestrator2.RegisterProcessOrderFileGenerationMethodToSubscriber(subscriber, serviceVar38)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar39 := wire6.InitializeReverseFeedConsumerService(camsS3ClientVar6, orderManagerClient, karvyS3ClientVar5, epifiWealthCRDB, investmentGenConf, catalogManagerClientVar2)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.ProcessReverseFeedSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		rfpbconsumer.RegisterProcessFeedFileMethodToSubscriber(subscriber, serviceVar39)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar40 := wire6.InitializeMutualFundCatalogConsumerSvc(investmentConf, epifiWealthCRDB, mFCatalogS3ClientVar2, mutualFundClient, investmentGenConf, investmentRedisStore)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.ProcessMFCatalogUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		mfcatalogconsumer2.RegisterProcessCatalogUpdateFileMethodToSubscriber(subscriber, serviceVar40)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.RecentlyVisitedSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		mfcatalogconsumer2.RegisterProcessRecentlyVisitedFundMethodToSubscriber(subscriber, serviceVar40)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar41 := wire6.InitializeMutualFundCatalogConsumerSvc(investmentConf, epifiWealthCRDB, mFCatalogS3ClientVar3, mutualFundClient, investmentGenConf, investmentRedisStore)

	if func(conf *config.Config) bool {
		return !cfg.IsLocalEnv(conf.Environment)
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.HistoricalNavSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			mfcatalogconsumer2.RegisterProcessMfHistoricalNavsMethodToSubscriber(subscriber, serviceVar41)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}

	serviceVar42 := wire6.InitializeInvestmentAggregatorConsumerSvc(investmentEventPublisher, orderManagerClient, piClient, savingsClient, ruleManagerClient, depositClientVar5, orderManagerClientVar13)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.InvestmentEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		aggregatorpbconsumer.RegisterProcessInvestmentEventMethodToSubscriber(subscriber, serviceVar42)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar43 := wire6.InitialiseOrderETAConsumerService(epifiWealthCRDB, investmentGenConf, orderETADelayPublisher, watsonClient, paymentHandlerClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.OrderETADelaySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		etahandlerpb2.RegisterProcessOrderETAMethodToSubscriber(subscriber, serviceVar43)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	mFExternalConsumer := wire6.InitializeMFExternalConsumerService(epifiWealthCRDB, investmentGenConf, ruleManagerClient, orderManagerClient, investmentEventPublisher, holdingImporterClient, usersClient, uNNameCheckClient, broker, savingsClient, onboardingClient)

	_, err = awswire.InitializeExtendedSubscriberWithGenConf(ctx, investmentGenConf.ProcessMFHoldingsWebhookExtendedSubscriber(), awsConf, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		mfexternalorderconsumer.RegisterProcessMFHoldingsCallbackConsumerMethodToSubscriber(subscriber, mFExternalConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors())
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar44 := wire6.InitialiseNonFinancialEventsConsumerService(nonFinancialEventSnsPublisher)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, investmentGenConf.NonFinancialEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nonfinancialeventconsumer.RegisterProcessNonFinancialEventMethodToSubscriber(subscriber, serviceVar44)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar45 := wire7.InitialiseCatalogService()

	indianstockscatalogpb.RegisterIndianStocksCatalogManagerServer(s, serviceVar45)

	configNameToConfMap[cfg.ConfigName(cfg.INVESTMENT_SERVICE)] = &commonexplorer.Config{StaticConf: &investmentconf.Config{}, QuestIntegratedConfig: nil}
	err = investment.AfterServiceGroupInit(zincSearchClient, investmentGenConf, serviceVar25)
	if err != nil {
		logger.Error(ctx, "failed to run AfterServicesInitHook", zap.Error(err))
		return err
	}

	return nil

}

// nolint: funlen
func setupP2pinvestment(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	p2pInvestmentLiquiloansCRDB types.P2pInvestmentLiquiloansCRDB,
	orderServiceClient orderpb.OrderServiceClient,
	p2PClient vgtypes.VgP2pInvestmentClientWithInterceptors,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	savingsClient savingspb.SavingsClient,
	p2pInvestmentRedisStore types.P2pInvestmentRedisStore,
	payClient paypb.PayClient,
	timelineServiceClient timelinepb.TimelineServiceClient,
	docsClient docs.DocsClient,
	consentClient consent.ConsentClient,
	commsClientVar17 comms.CommsClient,
	groupClient usergrouppb.GroupClient,
	tieringClient tieringpb.TieringClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	celestialClient celestialpb.CelestialClient,
	balanceClient accountbalancepb.BalanceClient,
	incidentManagerClient incidentmanager.IncidentManagerClient,
	watsonClient watsonpb.WatsonClient,
	devSimulatorP2PInvestmentClient simp2pdspb.DevSimulatorP2PInvestmentClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	p2pinvestmentConf, err := p2pinvestmentconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.P2P_INVESTMENT_SERVICE))
		return err
	}
	_ = p2pinvestmentConf

	p2pinvestmentGenConf, err := dynconf.LoadConfig(p2pinvestmentconf.Load, genconf5.NewConfig, cfg.P2P_INVESTMENT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.P2P_INVESTMENT_SERVICE))
		return err
	}

	_ = p2pinvestmentGenConf

	investmentInstrumentEventPublisherVar4, err := sqs.NewPublisherWithConfig(ctx, p2pinvestmentGenConf.InvestmentInstrumentEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	dBOperationsPublisher, err := sqs.NewPublisherWithConfig(ctx, p2pinvestmentGenConf.DBOperationsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	p2PInvestmentS3Client := s3pkg.NewClient(awsConf, p2pinvestmentGenConf.S3Conf().Bucket)
	p2PInvestmentS3ClientVar2 := s3pkg.NewClient(awsConf, p2pinvestmentGenConf.S3Conf().Bucket)

	serviceVar46, err := wire8.InitialiseP2pInvestmentSvc(ctx, p2pInvestmentLiquiloansCRDB, orderServiceClient, p2PClient, usersClient, actorClient, savingsClient, p2pInvestmentRedisStore, payClient, timelineServiceClient, broker, docsClient, p2PInvestmentS3Client, consentClient, commsClientVar17, p2pinvestmentConf, p2pinvestmentGenConf, investmentInstrumentEventPublisherVar4, dBOperationsPublisher, groupClient, tieringClient, bankCustomerServiceClient, celestialClient, balanceClient, incidentManagerClient, awsConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	p2pinvestment.RegisterP2PInvestmentServer(s, serviceVar46)

	p2PInvestmentDevEntity, err := wire8.InitializeP2PInvestmentDevEntityService(ctx, p2pInvestmentLiquiloansCRDB, orderServiceClient, p2PClient, usersClient, actorClient, savingsClient, p2pInvestmentRedisStore, payClient, timelineServiceClient, broker, docsClient, p2PInvestmentS3ClientVar2, consentClient, commsClientVar17, p2pinvestmentConf, p2pinvestmentGenConf, investmentInstrumentEventPublisherVar4, dBOperationsPublisher, groupClient, tieringClient, bankCustomerServiceClient, celestialClient, balanceClient, incidentManagerClient, awsConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	developer9.RegisterDevP2PInvestmentServer(s, p2PInvestmentDevEntity)

	serviceVar47 := wire8.InitializeP2PCCxService(p2pInvestmentLiquiloansCRDB, p2PClient)

	p2pcxpb.RegisterCxServer(s, serviceVar47)

	serviceVar48 := wire8.InitializeP2PConsumerService(p2pInvestmentLiquiloansCRDB)

	p2pconsumerpb.RegisterConsumerServer(s, serviceVar48)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, p2pinvestmentGenConf.DBOperationsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		p2pconsumerpb.RegisterPerformP2PInvestmentDBOperationsAsyncMethodToSubscriber(subscriber, serviceVar48)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar49, err := wire8.InitializeWatsonIncidentManagerService(p2pinvestmentGenConf, watsonClient, p2pInvestmentLiquiloansCRDB, actorClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	incidentmanager.RegisterIncidentManagerServer(s, serviceVar49)

	serviceVar50 := wire8.InitializeP2pSimulationService(devSimulatorP2PInvestmentClient)

	if func(conf *config.Config) bool {
		return cfg.IsSimulatedEnv(conf.Environment)
	}(conf) {
		p2pinvestment.RegisterSimulationServer(s, serviceVar50)
	}

	configNameToConfMap[cfg.ConfigName(cfg.P2P_INVESTMENT_SERVICE)] = &commonexplorer.Config{StaticConf: &p2pinvestmentconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupUsstocks(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	usstocksAlpacaPGDB types.UsstocksAlpacaPGDB,
	celestialClient celestialpb.CelestialClient,
	internationalFundTransferClient internationalfundtransfer.InternationalFundTransferClient,
	stocksClient vgtypes.VgStocksClientWithInterceptors,
	usersClient user.UsersClient,
	savingsClient savingspb.SavingsClient,
	orderServiceClient orderpb.OrderServiceClient,
	internationalFundTransferClientVar2 vginternationalfundtransfer.InternationalFundTransferClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	commsClientVar17 comms.CommsClient,
	actorClient actor.ActorClient,
	portfolioManagerClient portfolio.PortfolioManagerClient,
	catalogManagerClient usstockscatalogmanagerpb.CatalogManagerClient,
	uSStocksRedisStore types.USStocksRedisStore,
	fileGeneratorClientVar8 iftfilegenrationsvc.FileGeneratorClient,
	accountManagerClient usstocksaccountpb.AccountManagerClient,
	preApprovedLoanClient preapprovedloanpb.PreApprovedLoanClient,
	profileClient riskprofilepb.ProfileClient,
	authClient authpb.AuthClient,
	payClient paypb.PayClient,
	stocksClientVar3 wtypes3.VgStocksStreamClient,
	zincSearchClient zinc.SearchClient,
	rateLimiterRedisStore types.RateLimiterRedisStore,
	catalogClient vgcatalogpb.CatalogClient,
	groupClient usergrouppb.GroupClient,
	amlClientVar2 amlpb.AmlClient,
	ocrClient inhouseocr.OcrClient,
	onboardingClient onboardingpb.OnboardingClient,
	livenessClient vgliveness.LivenessClient,
	livenessClientVar2 liveness.LivenessClient,
	uSStocksAccountRedisStore types.USStocksAccountRedisStore,
	vKYCClient vkycpb.VKYCClient,
	employmentClient employmentpb.EmploymentClient,
	panClient panpb.PanClient,
	locationClient location.LocationClient,
	wealthOnboardingClient wealthpb.WealthOnboardingClient,
	orderManagerClientVar13 usstocksorderpb.OrderManagerClient,
	dynamicUIElementServiceClient dynamicuielementpb.DynamicUIElementServiceClient,
	rewardsGeneratorClient rewardspb.RewardsGeneratorClient,
	investmentAggregatorClient aggregatorpb.InvestmentAggregatorClient,
	stocksClientVar7 stockspb.StocksClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	usstocksConf, err := usstocksconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.US_STOCKS_SERVICE))
		return err
	}
	_ = usstocksConf

	usstocksGenConf, err := dynconf.LoadConfig(usstocksconf.Load, genconf6.NewConfig, cfg.US_STOCKS_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.US_STOCKS_SERVICE))
		return err
	}

	_ = usstocksGenConf

	uSStockCatalogRefreshPublisher, err := sqs.NewPublisherWithConfig(ctx, usstocksGenConf.USStockCatalogRefreshPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	usStocksSendMailToUsersPublisher, err := sqs.NewPublisherWithConfig(ctx, usstocksGenConf.UsStocksSendMailToUsersPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	processAccountActivitySyncPublisher, err := sqs.NewPublisherWithConfig(ctx, usstocksGenConf.ProcessAccountActivitySyncPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	nonFinancialEventSqsPublisherVar2, err := sqs.NewPublisherWithConfig(ctx, usstocksGenConf.NonFinancialEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	orderManagerS3Client := s3pkg.NewClient(awsConf, usstocksGenConf.CatalogS3Conf().BucketName)
	vendorAccountActivitiesS3Client := s3pkg.NewClient(awsConf, usstocksGenConf.VendorAccountActivitiesBucketName())
	accountManagerS3Client := s3pkg.NewClient(awsConf, usstocksGenConf.AccountManagerConfig().KycDocumentsBucketName())
	operationsS3Client := s3pkg.NewClient(awsConf, usstocksGenConf.CatalogS3Conf().BucketName)
	taxDocumentsS3Client := s3pkg.NewClient(awsConf, usstocksGenConf.TaxDocumentsBucketName())
	catalogConsumerS3Client := s3pkg.NewClient(awsConf, usstocksGenConf.CatalogS3Conf().BucketName)
	morningStarS3Client := s3pkg.NewClient(awsConf, usstocksGenConf.MorningStarS3Bucket().BucketName())
	taxDocumentsS3ClientVar2 := s3pkg.NewClient(awsConf, usstocksGenConf.TaxDocumentsBucketName())

	uSStocksClient, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.USStocks, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return err
	}

	uSStocksClientVar2, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.USStocks, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return err
	}

	serviceVar51 := wire9.InitializeOrderManagerService(usstocksAlpacaPGDB, usstocksGenConf, celestialClient, internationalFundTransferClient, stocksClient, orderManagerS3Client, usersClient, savingsClient, orderServiceClient, internationalFundTransferClientVar2, bankCustomerServiceClient, commsClientVar17, actorClient, portfolioManagerClient, catalogManagerClient, uSStocksRedisStore, fileGeneratorClientVar8, accountManagerClient, preApprovedLoanClient, vendorAccountActivitiesS3Client, profileClient, authClient, payClient)

	usstocksorderpb.RegisterOrderManagerServer(s, serviceVar51)

	serviceVar52 := wire9.InitializeCatalogManagerService(usstocksAlpacaPGDB, usstocksGenConf, stocksClient, stocksClientVar3, zincSearchClient, rateLimiterRedisStore, catalogClient, uSStockCatalogRefreshPublisher, uSStocksRedisStore, actorClient, usersClient, groupClient)

	usstockscatalogmanagerpb.RegisterCatalogManagerServer(s, serviceVar52)

	serviceVar53 := wire9.InitializeAccountManagerService(usstocksAlpacaPGDB, usersClient, actorClient, stocksClient, celestialClient, amlClientVar2, accountManagerS3Client, usstocksGenConf, ocrClient, onboardingClient, livenessClient, livenessClientVar2, commsClientVar17, uSStocksClient, uSStocksAccountRedisStore, vKYCClient, awsConf, employmentClient, bankCustomerServiceClient, panClient, internationalFundTransferClient, locationClient, savingsClient, wealthOnboardingClient)

	usstocksaccountpb.RegisterAccountManagerServer(s, serviceVar53)

	serviceVar54 := wire9.InitializePortfolioManagerService(usstocksAlpacaPGDB, usstocksGenConf, accountManagerClient, stocksClient, catalogManagerClient, orderManagerClientVar13, internationalFundTransferClient, uSStocksRedisStore)

	portfolio.RegisterPortfolioManagerServer(s, serviceVar54)

	dBStateService := wire9.InitializeDBStateService(usstocksAlpacaPGDB, usstocksGenConf, celestialClient)

	usstocksdevpb.RegisterDBStateServer(s, dBStateService)

	serviceVar55 := wire9.InitialiseDynamicElementManagerService(usstocksGenConf, dynamicUIElementServiceClient, catalogManagerClient, portfolioManagerClient, actorClient, usersClient, groupClient)

	ussdynamicelementpb.RegisterUSSDynamicElementsManagerServer(s, serviceVar55)

	serviceVar56 := wire9.InitializeOperationsService(usstocksGenConf, operationsS3Client)

	operationspb.RegisterOperationsServer(s, serviceVar56)

	serviceVar57 := wire9.InitialiseUssRewardManagerService(usstocksGenConf, usstocksAlpacaPGDB, rewardsGeneratorClient, celestialClient)

	ussrewardspb.RegisterUssRewardManagerServer(s, serviceVar57)

	ussTaxService := wire9.InitializeUssTaxService(usstocksAlpacaPGDB, accountManagerClient, commsClientVar17, usersClient, taxDocumentsS3Client)

	tax.RegisterUssTaxServiceServer(s, ussTaxService)

	serviceVar58 := wire9.InitialiseAccountConsumer(usstocksGenConf, usstocksAlpacaPGDB, usStocksSendMailToUsersPublisher, stocksClient, actorClient, commsClientVar17, usersClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, usstocksGenConf.AmlActionEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usstockaccountpb.RegisterProcessAMLActionEventMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, usstocksGenConf.UsStocksSendMailToUsersSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usstockaccountpb.RegisterProcessSendMailToUsersEventMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	eventConsumerService := wire9.InitialiseEventConsumer(usstocksAlpacaPGDB, usstocksGenConf, orderManagerClientVar13, processAccountActivitySyncPublisher, celestialClient, internationalFundTransferClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, usstocksGenConf.ProcessOrderUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usstockorderpb.RegisterProcessOMSOrderUpdateEventMethodToSubscriber(subscriber, eventConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, usstocksGenConf.ProcessAccountActivitySyncSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usstockorderpb.RegisterProcessAccountActivitySyncMethodToSubscriber(subscriber, eventConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar59 := wire9.InitializeCatalogConsumerService(usstocksAlpacaPGDB, catalogConsumerS3Client, catalogClient, usstocksGenConf, morningStarS3Client, catalogManagerClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, usstocksGenConf.ProcessUSEtfCatalogUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usstockcatalogpb.RegisterProcessCatalogUpdateFileMethodToSubscriber(subscriber, serviceVar59)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, usstocksGenConf.USStockCatalogRefreshSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usstockcatalogpb.RegisterRefreshStockCatalogInfoMethodToSubscriber(subscriber, serviceVar59)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar60 := wire9.InitialiseConsumer(usstocksGenConf, celestialClient, usstocksAlpacaPGDB, nonFinancialEventSqsPublisherVar2, broker, investmentAggregatorClient, catalogManagerClient, employmentClient, usersClient, uSStocksClientVar2)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, usstocksGenConf.CelestialWorkflowUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usstockconsumerpb.RegisterProcessWorkUpdateEventMethodToSubscriber(subscriber, serviceVar60)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, usstocksGenConf.USStocksIFTRemittanceFileProcessingEventsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usstockconsumerpb.RegisterProcessIFTRemittanceFileEventMethodToSubscriber(subscriber, serviceVar60)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, usstocksGenConf.USStocksIncomeUpdateEventsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usstockconsumerpb.RegisterProcessIncomeUpdateEventMethodToSubscriber(subscriber, serviceVar60)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar61 := wire9.InitializeTaxConsumerService(usstocksGenConf, usstocksAlpacaPGDB, taxDocumentsS3ClientVar2, accountManagerClient, stocksClientVar7, catalogManagerClient, uSStocksRedisStore)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, usstocksGenConf.USStocksTaxDocumentGenerationRequestSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usstaxconsumerpb.RegisterGenerateTaxDocumentsMethodToSubscriber(subscriber, serviceVar61)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.US_STOCKS_SERVICE)] = &commonexplorer.Config{StaticConf: &usstocksconf.Config{}, QuestIntegratedConfig: nil}
	err = usstocks.AfterServiceGroupInit(zincSearchClient, usstocksGenConf, serviceVar52)
	if err != nil {
		logger.Error(ctx, "failed to run AfterServicesInitHook", zap.Error(err))
		return err
	}

	return nil

}

// nolint: funlen
func setupAnalyser(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	txnCategorizerClient categorizerpb.TxnCategorizerClient,
	merchantServiceClient merchantpb.MerchantServiceClient,
	analyserRedisStore types3.AnalyserRedisStore,
	epifiWealthAnalyticsPGDB types.EpifiWealthAnalyticsPGDB,
	actorClient actor.ActorClient,
	piClient pipb.PiClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	orderServiceClient orderpb.OrderServiceClient,
	paymentClient paymentpb.PaymentClient,
	accountAggregatorClient aapb.AccountAggregatorClient,
	tieringClient tieringpb.TieringClient,
	mFExternalOrdersClient mfexternalorderpb.MFExternalOrdersClient,
	mFAnalyticsClient mfanalyticspb.MFAnalyticsClient,
	investmentAnalyticsClient investment3.InvestmentAnalyticsClient,
	catalogManagerClientVar2 catalogpb.CatalogManagerClient,
	commsClientVar23 types3.AnalyserCommsClientWithInterceptors,
	usersClient user.UsersClient,
	preApprovedLoanClient preapprovedloanpb.PreApprovedLoanClient,
	serviceClient userdeclarationpb.ServiceClient,
	netWorthClient networthpb.NetWorthClient,
	securitiesCatalogClient catalogpb2.SecuritiesCatalogClient,
	epfClient epfpb.EpfClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	analyserConf, err := analyserconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ANALYSER_SERVICE))
		return err
	}
	_ = analyserConf

	analyserGenConf, err := dynconf.LoadConfig(analyserconf.Load, genconf7.NewConfig, cfg.ANALYSER_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ANALYSER_SERVICE))
		return err
	}

	_ = analyserGenConf

	investmentAnalysisTaskPublisher, err := sqs.NewPublisherWithConfig(ctx, analyserGenConf.InvestmentAnalysisTaskPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	highPriorityInvestmentAnalysisTaskPublisher, err := sqs.NewPublisherWithConfig(ctx, analyserGenConf.HighPriorityInvestmentAnalysisTaskPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	serviceVar62, err := wire10.InitialiseTxnAggregatesService(ctx, analyserConf, txnCategorizerClient, merchantServiceClient, analyserGenConf, analyserRedisStore)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	txnaggregatespb.RegisterTxnAggregatesServer(s, serviceVar62)

	analyserDevService, err := wire10.InitialiseAnalyserDevService(ctx, analyserGenConf, analyserConf, epifiWealthAnalyticsPGDB, analyserRedisStore)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	analyserdeveloperpb.RegisterDevAnalyserServer(s, analyserDevService)

	serviceVar63, err := wire10.InitialiseAnalyserConsumerService(ctx, analyserConf, analyserGenConf, actorClient, piClient, merchantServiceClient, accountPIRelationClient, orderServiceClient, paymentClient, txnCategorizerClient, accountAggregatorClient, tieringClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	analyserconsumerpb.RegisterConsumerServer(s, serviceVar63)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, analyserGenConf.AnalyserOrderUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		analyserconsumerpb.RegisterProcessOrderUpdateEventMethodToSubscriber(subscriber, serviceVar63)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, analyserGenConf.AnalyserCategoryUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		analyserconsumerpb.RegisterProcessCategoryUpdateEventMethodToSubscriber(subscriber, serviceVar63)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, analyserGenConf.AnalyserAaTxnUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		analyserconsumerpb.RegisterProcessAATxnUpdateEventMethodToSubscriber(subscriber, serviceVar63)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar64 := wire10.InitialiseInvestmentAnalyticsService(epifiWealthAnalyticsPGDB, investmentAnalysisTaskPublisher, mFExternalOrdersClient, mFAnalyticsClient, analyserRedisStore, highPriorityInvestmentAnalysisTaskPublisher)

	investment3.RegisterInvestmentAnalyticsServer(s, serviceVar64)

	serviceVar65 := wire10.InitialiseAnalyserInvestmentConsumerService(analyserGenConf, epifiWealthAnalyticsPGDB, investmentAnalyticsClient, mFExternalOrdersClient, catalogManagerClientVar2, commsClientVar23, usersClient, analyserRedisStore)

	analyserinvestmentconsumerpb.RegisterConsumerServer(s, serviceVar65)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, analyserGenConf.AnalyserInvestmentEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		analyserinvestmentconsumerpb.RegisterProcessInvestmentEventMethodToSubscriber(subscriber, serviceVar65)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, analyserGenConf.AnalyserInvestmentAnalysisTaskSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		analyserinvestmentconsumerpb.RegisterProcessAnalysisTasksEventMethodToSubscriber(subscriber, serviceVar65)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, analyserGenConf.AnalyserHighPriorityInvestmentAnalysisTaskSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		analyserinvestmentconsumerpb.RegisterProcessAnalysisTasksEventMethodToSubscriber(subscriber, serviceVar65)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar66 := wire10.InitialiseAnalyserInvestmentConsumerService(analyserGenConf, epifiWealthAnalyticsPGDB, investmentAnalyticsClient, mFExternalOrdersClient, catalogManagerClientVar2, commsClientVar23, usersClient, analyserRedisStore)

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, analyserGenConf.RefreshMFPortfolioAnalyticsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			analyserinvestmentconsumerpb.RegisterRefreshMFPortfolioAnalyticsMethodToSubscriber(subscriber, serviceVar66)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}

	serviceVar67 := wire10.InitialiseDynamicElementsService(preApprovedLoanClient)

	dynamicelements2.RegisterDynamicElementsServer(s, serviceVar67)

	serviceVar68 := wire10.InitialiseVariableGeneratorService(serviceClient, investmentAnalyticsClient, usersClient, catalogManagerClientVar2, netWorthClient, securitiesCatalogClient, epfClient)

	analyservariablespb.RegisterVariableGeneratorServer(s, serviceVar68)

	configNameToConfMap[cfg.ConfigName(cfg.ANALYSER_SERVICE)] = &commonexplorer.Config{StaticConf: &analyserconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupCategorizer(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	categorizerPGDB types.CategorizerPGDB,
	orderServiceClient orderpb.OrderServiceClient,
	actorClient actor.ActorClient,
	groupClient usergrouppb.GroupClient,
	accountAggregatorClient aapb.AccountAggregatorClient,
	piClient pipb.PiClient,
	paymentClient paymentpb.PaymentClient,
	actorActivityClient actoractivitypb.ActorActivityClient,
	accountingClient fireflyaccpb.AccountingClient,
	payClient paypb.PayClient,
	uPIClient upipb.UPIClient,
	merchantServiceClient merchantpb.MerchantServiceClient,
	txnAggregatesClient txnaggregatespb.TxnAggregatesClient,
	connectedAccountClient connectedaccpb.ConnectedAccountClient,
	savingsClient savingspb.SavingsClient,
	fireflyClient firefly.FireflyClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	usersClient user.UsersClient,
	gPlaceClient gplace.GPlaceClient,
	locationClientVar2 location2.LocationClient,
	categorizerVMRedisStore wiretypes.CategorizerVMRedisStore) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	categorizerConf, err := categorizerconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CATEGORIZER_SERVICE))
		return err
	}
	_ = categorizerConf

	categorizerGenConf, err := dynconf.LoadConfig(categorizerconf.Load, genconf8.NewConfig, cfg.CATEGORIZER_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CATEGORIZER_SERVICE))
		return err
	}

	_ = categorizerGenConf

	txnCatPublisher, err := sns.NewSnsPublisherWithConfig(ctx, categorizerGenConf.TxnCatPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	batchTxnCategoryPublisher, err := sns.NewSnsPublisherWithConfig(ctx, categorizerGenConf.BatchTxnCategoryPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	s3Client := s3pkg.NewClient(awsConf, categorizerGenConf.CrowdAggregatedCategoryUpdateParams().S3BucketName())
	s3ClientVar2 := s3pkg.NewClient(awsConf, categorizerGenConf.CrowdAggregatedCategoryUpdateParams().S3BucketName())

	categorizerService := wire11.InitializeCategorizerService(categorizerGenConf, categorizerPGDB, orderServiceClient, actorClient, groupClient, txnCatPublisher, accountAggregatorClient, piClient, paymentClient, actorActivityClient, accountingClient, payClient, uPIClient, merchantServiceClient, txnAggregatesClient, connectedAccountClient, savingsClient, fireflyClient, accountPIRelationClient)

	categorizerpb.RegisterTxnCategorizerServer(s, categorizerService)

	serviceVar69 := wire11.InitializeCategorizerConsumerService(categorizerGenConf, categorizerPGDB, actorClient, piClient, uPIClient, merchantServiceClient, groupClient, usersClient, txnCatPublisher, gPlaceClient, locationClientVar2, batchTxnCategoryPublisher, categorizerVMRedisStore, s3Client)

	categorizerconsumerpb.RegisterConsumerServer(s, serviceVar69)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, categorizerGenConf.CategorizerUpdateOrderSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		categorizerconsumerpb.RegisterProcessOrderEventMethodToSubscriber(subscriber, serviceVar69)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, categorizerGenConf.CategorizerAATxnSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		categorizerconsumerpb.RegisterCategorizeAATxnEventMethodToSubscriber(subscriber, serviceVar69)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, categorizerGenConf.CategorizerCCTxnSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		categorizerconsumerpb.RegisterProcessCCTxnEventMethodToSubscriber(subscriber, serviceVar69)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, categorizerGenConf.CategorizerCrowdAggregationSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		categorizerconsumerpb.RegisterProcessCrowdAggregatedCategoryUpdateFileMethodToSubscriber(subscriber, serviceVar69)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, categorizerGenConf.CategorizerPayTxnBackfillSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		categorizerconsumerpb.RegisterProcessPayTxnBackfillEventMethodToSubscriber(subscriber, serviceVar69)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar70 := wire11.InitializeCategorizerConsumerService(categorizerGenConf, categorizerPGDB, actorClient, piClient, uPIClient, merchantServiceClient, groupClient, usersClient, txnCatPublisher, gPlaceClient, locationClientVar2, batchTxnCategoryPublisher, categorizerVMRedisStore, s3ClientVar2)

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, categorizerGenConf.TxnCategoryBatchUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			categorizerconsumerpb.RegisterProcessBatchOrderDetailsEventMethodToSubscriber(subscriber, serviceVar70)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, categorizerGenConf.CategorizerBatchAATxnDetailsSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			categorizerconsumerpb.RegisterProcessBatchAATransactionDetailsEventMethodToSubscriber(subscriber, serviceVar70)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}

	categorizerDevService := wire11.InitialiseCategorizerDevEntityService(categorizerPGDB, categorizerGenConf)

	categorizerdeveloper.RegisterDevCategorizerServer(s, categorizerDevService)

	configNameToConfMap[cfg.ConfigName(cfg.CATEGORIZER_SERVICE)] = &commonexplorer.Config{StaticConf: &categorizerconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupInsights(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	insightsPGDB types.InsightsPGDB,
	accessInfoClient accessinfopb.AccessInfoClient,
	actorClient actor.ActorClient,
	usersClient user.UsersClient,
	actorInsightsPGDB types.ActorInsightsPGDB,
	insightsClient insights.InsightsClient,
	commsClientVar25 types4.InsightsCommsClientWithInterceptors,
	connectedAccountClient connectedaccpb.ConnectedAccountClient,
	useCaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB],
	securitiesCatalogClient catalogpb2.SecuritiesCatalogClient,
	groupClient usergrouppb.GroupClient,
	txnAggregatesClient txnaggregatespb.TxnAggregatesClient,
	savingsClient savingspb.SavingsClient,
	merchantServiceClient merchantpb.MerchantServiceClient,
	piClient pipb.PiClient,
	txnCategorizerClient categorizerpb.TxnCategorizerClient,
	fireflyClient firefly.FireflyClient,
	authClient authpb.AuthClient,
	onboardingClient onboardingpb.OnboardingClient,
	emailParserClient emailparserpb.EmailParserClient,
	rewardsGeneratorClient rewardspb.RewardsGeneratorClient,
	creditReportManagerClient creditreport.CreditReportManagerClient,
	balanceClient accountbalancepb.BalanceClient,
	txnAggregatesClientVar4 ffpinotpb.TxnAggregatesClient,
	accountingClient fireflyaccpb.AccountingClient,
	rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient,
	employmentClientVar3 employment.EmploymentClient,
	uNNameCheckClient ncpb.UNNameCheckClient,
	analyserRedisStore types3.AnalyserRedisStore,
	employmentClient employmentpb.EmploymentClient,
	investmentAnalyticsClient investment3.InvestmentAnalyticsClient,
	investmentAggregatorClient aggregatorpb.InvestmentAggregatorClient,
	epfClient epfpb.EpfClient,
	mFExternalOrdersClient mfexternalorderpb.MFExternalOrdersClient,
	netWorthClient networthpb.NetWorthClient,
	internationalFundTransferClient internationalfundtransfer.InternationalFundTransferClient,
	zincSearchClient zinc.SearchClient,
	preApprovedLoanClient preapprovedloanpb.PreApprovedLoanClient,
	npsCatalogClient npspb.NpsCatalogClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	insightsConf, err := insightsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.INSIGHTS_SERVICE))
		return err
	}
	_ = insightsConf

	insightsGenConf, err := dynconf.LoadConfig(insightsconf.Load, genconf9.NewConfig, cfg.INSIGHTS_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.INSIGHTS_SERVICE))
		return err
	}

	_ = insightsGenConf

	mailDataParserPublisher, err := sqs.NewPublisherWithConfig(ctx, insightsGenConf.MailDataParserPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	userEmailAccessPublisher, err := sqs.NewPublisherWithConfig(ctx, insightsGenConf.UserEmailAccessPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	periodicEmaiSyncPublisher, err := sqs.NewPublisherWithConfig(ctx, insightsGenConf.PeriodicEmaiSyncPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	gmailUserSpendsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, insightsGenConf.GmailUserSpendsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	epfPassbookImportEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, insightsGenConf.EpfPassbookImportEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	serviceVar71 := wire12.InitializeMockEmailparserConsumerService(insightsConf, insightsPGDB, accessInfoClient, mailDataParserPublisher, gmailUserSpendsPublisher, actorClient, usersClient, awsConf)

	if func(conf *config.Config) bool {
		return cfg.IsTestEnv(conf.Environment) || cfg.IsDevelopmentEnv(conf.Environment)
	}(conf) {
		emailparserpb.RegisterConsumerServiceServer(s, serviceVar71)
	}

	if func(conf *config.Config) bool {
		return cfg.IsTestEnv(conf.Environment) || cfg.IsDevelopmentEnv(conf.Environment)
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, insightsGenConf.UserEmailAccessSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			emailparserpb.RegisterProcessNewUserMailAccessMethodToSubscriber(subscriber, serviceVar71)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return cfg.IsTestEnv(conf.Environment) || cfg.IsDevelopmentEnv(conf.Environment)
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, insightsGenConf.MailDataParserSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			emailparserpb.RegisterParseUserMailDataMethodToSubscriber(subscriber, serviceVar71)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return cfg.IsTestEnv(conf.Environment) || cfg.IsDevelopmentEnv(conf.Environment)
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, insightsGenConf.PeriodicEmailSyncSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			emailparserpb.RegisterProcessNewUserMailAccessMethodToSubscriber(subscriber, serviceVar71)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}

	serviceVar72 := wire12.InitializeEmailparserConsumerService(insightsConf, insightsPGDB, accessInfoClient, mailDataParserPublisher, gmailUserSpendsPublisher, actorClient, usersClient, awsConf)

	if func(conf *config.Config) bool {
		return !(cfg.IsTestEnv(conf.Environment) || cfg.IsDevelopmentEnv(conf.Environment))
	}(conf) {
		emailparserpb.RegisterConsumerServiceServer(s, serviceVar72)
	}

	if func(conf *config.Config) bool {
		return !(cfg.IsTestEnv(conf.Environment) || cfg.IsDevelopmentEnv(conf.Environment))
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, insightsGenConf.UserEmailAccessSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			emailparserpb.RegisterProcessNewUserMailAccessMethodToSubscriber(subscriber, serviceVar72)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !(cfg.IsTestEnv(conf.Environment) || cfg.IsDevelopmentEnv(conf.Environment))
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, insightsGenConf.MailDataParserSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			emailparserpb.RegisterParseUserMailDataMethodToSubscriber(subscriber, serviceVar72)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !(cfg.IsTestEnv(conf.Environment) || cfg.IsDevelopmentEnv(conf.Environment))
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, insightsGenConf.PeriodicEmailSyncSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			emailparserpb.RegisterProcessNewUserMailAccessMethodToSubscriber(subscriber, serviceVar72)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}

	serviceVar73 := wire12.InitializeActorInsightConsumerService(actorInsightsPGDB, insightsClient, commsClientVar25, connectedAccountClient, useCaseDbResourceProvider, securitiesCatalogClient)

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		consumerpb.RegisterConsumerServer(s, serviceVar73)
	}

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, insightsGenConf.CreateOrUpdateGenerationStatusSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			consumerpb.RegisterCreateOrUpdateGenerationStatusMethodToSubscriber(subscriber, serviceVar73)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, insightsGenConf.StoreGeneratedActorInsightsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			consumerpb.RegisterStoreGeneratedActorInsightsMethodToSubscriber(subscriber, serviceVar73)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, insightsGenConf.GenerateInsightsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			consumerpb.RegisterDeliverInsightsMethodToSubscriber(subscriber, serviceVar73)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, insightsGenConf.ProcessCaNewDataFetchEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			consumerpb.RegisterProcessCaNewDataFetchEventMethodToSubscriber(subscriber, serviceVar73)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}

	serviceVar74 := wire12.InitializeAccessInfoConsumerService(insightsPGDB)

	accessinfoconsumerpb.RegisterConsumerServer(s, serviceVar74)

	serviceVar75 := wire12.InitializeService(insightsGenConf, actorInsightsPGDB, actorClient, groupClient, usersClient, txnAggregatesClient, connectedAccountClient, savingsClient, merchantServiceClient, piClient, txnCategorizerClient, fireflyClient)

	insights.RegisterInsightsServer(s, serviceVar75)

	serviceVar76 := wire12.InitializeUserDeclarationService(insightsPGDB)

	userdeclarationpb.RegisterServiceServer(s, serviceVar76)

	serviceVar77 := wire12.InitializeMockAccessInfoService(insightsConf, insightsPGDB, userEmailAccessPublisher, awsConf, authClient, periodicEmaiSyncPublisher, onboardingClient, emailParserClient)

	if func(conf *config.Config) bool {
		return cfg.IsTestEnv(conf.Environment) || cfg.IsDevelopmentEnv(conf.Environment)
	}(conf) {
		accessinfopb.RegisterAccessInfoServer(s, serviceVar77)
	}

	serviceVar78 := wire12.InitializeAccessInfoService(insightsConf, insightsPGDB, userEmailAccessPublisher, awsConf, authClient, periodicEmaiSyncPublisher, onboardingClient, emailParserClient)

	if func(conf *config.Config) bool {
		return !(cfg.IsTestEnv(conf.Environment) || cfg.IsDevelopmentEnv(conf.Environment))
	}(conf) {
		accessinfopb.RegisterAccessInfoServer(s, serviceVar78)
	}

	serviceVar79 := wire12.InitializeEmailparserService(insightsConf, insightsPGDB, authClient, accessInfoClient, gmailUserSpendsPublisher)

	emailparserpb.RegisterEmailParserServer(s, serviceVar79)

	insightsDevService := wire12.InitializeInsightsDevEntityService(insightsPGDB, actorInsightsPGDB)

	developer14.RegisterDevInsightsServer(s, insightsDevService)

	serviceVar80 := wire12.InitializeStoryService(insightsGenConf, actorInsightsPGDB, rewardsGeneratorClient, usersClient, txnAggregatesClient, actorClient, savingsClient, connectedAccountClient, merchantServiceClient, piClient, creditReportManagerClient, txnCategorizerClient, groupClient, fireflyClient, balanceClient, txnAggregatesClientVar4, accountingClient, rewardsAggregatesClient)

	storypb.RegisterStoryServer(s, serviceVar80)

	serviceVar81 := wire12.InitializeEpfService(insightsGenConf, insightsPGDB, employmentClientVar3, usersClient, uNNameCheckClient, epfPassbookImportEventPublisher, broker, analyserRedisStore, employmentClient)

	epfpb.RegisterEpfServer(s, serviceVar81)

	serviceVar82 := wire12.InitialiseNetWorthService(insightsGenConf, insightsPGDB, savingsClient, connectedAccountClient, creditReportManagerClient, usersClient, investmentAnalyticsClient, investmentAggregatorClient, epfClient, balanceClient, onboardingClient, mFExternalOrdersClient, netWorthClient, internationalFundTransferClient, zincSearchClient, preApprovedLoanClient, employmentClient, useCaseDbResourceProvider, securitiesCatalogClient, npsCatalogClient, broker)

	networthpb.RegisterNetWorthServer(s, serviceVar82)

	serviceVar83 := wire12.InitializeKubairService(insightsGenConf, netWorthClient, connectedAccountClient)

	insightkubairpb.RegisterInsightKubairServer(s, serviceVar83)

	epfPassbookConsumerService := wire12.InitializeEpfPassbookConsumerService(insightsGenConf, insightsPGDB, analyserRedisStore)

	epfconsumer2.RegisterConsumerServer(s, epfPassbookConsumerService)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, insightsGenConf.EpfPassbookDataFlatteningSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		epfconsumer2.RegisterProcessEpfPassbookDataFlatteningMethodToSubscriber(subscriber, epfPassbookConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.INSIGHTS_SERVICE)] = &commonexplorer.Config{StaticConf: &insightsconf.Config{}, QuestIntegratedConfig: nil}
	err = insights3.AfterServiceGroupInit(zincSearchClient, insightsGenConf)
	if err != nil {
		logger.Error(ctx, "failed to run AfterServicesInitHook", zap.Error(err))
		return err
	}

	return nil

}

// nolint: funlen
func setupUpcomingtransactions(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	budgetingPGDB types.BudgetingPGDB,
	ruleManagerClient rmsmanagerpb.RuleManagerClient,
	recurringPaymentServiceClient recurringpaymentpb.RecurringPaymentServiceClient,
	actorClient actor.ActorClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	upcomingtransactionsConf, err := upcomingtransactionsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.UPCOMING_TRANSACTIONS_SERVICE))
		return err
	}
	_ = upcomingtransactionsConf

	upcomingtransactionsGenConf, err := dynconf.LoadConfig(upcomingtransactionsconf.Load, genconf10.NewConfig, cfg.UPCOMING_TRANSACTIONS_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.UPCOMING_TRANSACTIONS_SERVICE))
		return err
	}

	_ = upcomingtransactionsGenConf

	s3ClientVar3 := s3pkg.NewClient(awsConf, upcomingtransactionsGenConf.DsSubscriptionsIngestionParams().S3BucketName())

	serviceVar84 := wire13.InitializeUpcomingTransactionsService(budgetingPGDB, ruleManagerClient, recurringPaymentServiceClient, actorClient, upcomingtransactionsGenConf)

	upcomingtransactions2.RegisterUpcomingTransactionsServer(s, serviceVar84)

	consumerService := wire13.InitialiseUpcomingTransactionsConsumerService(budgetingPGDB, upcomingtransactionsGenConf, s3ClientVar3, actorClient)

	upcomingtrxpb.RegisterConsumerServer(s, consumerService)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upcomingtransactionsGenConf.ProcessRecurringTxnSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upcomingtrxpb.RegisterProcessRecurringTxnsFileMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upcomingtransactionsGenConf.UpdateUpcomingTxnStateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upcomingtrxpb.RegisterUpdateUpcomingTxnStateMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	upcomingTransactionsDevService := wire13.InitializeUpcomingTransactionsDevEntityService(budgetingPGDB)

	developer16.RegisterDevUpcomingTransactionsServer(s, upcomingTransactionsDevService)

	configNameToConfMap[cfg.ConfigName(cfg.UPCOMING_TRANSACTIONS_SERVICE)] = &commonexplorer.Config{StaticConf: &upcomingtransactionsconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupIndexer(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	indexerConf, err := indexerconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.INDEXER_SERVICE))
		return err
	}
	_ = indexerConf

	configNameToConfMap[cfg.ConfigName(cfg.INDEXER_SERVICE)] = &commonexplorer.Config{StaticConf: &indexerconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupSearch(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	piClient pipb.PiClient,
	depositClientVar5 depositpb.DepositClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	fitttClientVar2 fitttpb.FitttClient,
	accessInfoClient accessinfopb.AccessInfoClient,
	offerListingServiceClient casper.OfferListingServiceClient,
	searchRedisStore types5.SearchRedisStore,
	groupClient usergrouppb.GroupClient,
	accrualClient accrualpb.AccrualClient,
	rewardsGeneratorClient rewardspb.RewardsGeneratorClient,
	cardProvisioningClient cardpb.CardProvisioningClient,
	inAppReferralClient inappreferral.InAppReferralClient,
	connectedAccountClient connectedaccpb.ConnectedAccountClient,
	ruleManagerClient rmsmanagerpb.RuleManagerClient,
	merchantServiceClient merchantpb.MerchantServiceClient,
	savingsClient savingspb.SavingsClient,
	catalogManagerClientVar2 catalogpb.CatalogManagerClient,
	cardControlClient cardcontrolpb.CardControlClient,
	uPIClient upipb.UPIClient,
	timelineServiceClient timelinepb.TimelineServiceClient,
	p2PInvestmentClient p2pinvestment.P2PInvestmentClient,
	onboardingClient onboardingpb.OnboardingClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient employmentpb.EmploymentClient,
	orderServiceClient orderpb.OrderServiceClient,
	preApprovedLoanClient preapprovedloanpb.PreApprovedLoanClient,
	catalogManagerClient usstockscatalogmanagerpb.CatalogManagerClient,
	fireflyClient firefly.FireflyClient,
	reminderServiceClient reminderpb.ReminderServiceClient,
	portfolioManagerClient portfolio.PortfolioManagerClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	exchangerOfferServiceClient beexchangerpb.ExchangerOfferServiceClient,
	balanceClient accountbalancepb.BalanceClient,
	txnCategorizerClient categorizerpb.TxnCategorizerClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	searchConf, err := searchconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SEARCH_SERVICE))
		return err
	}
	_ = searchConf

	searchGenConf, err := dynconf.LoadConfig(searchconf.Load, genconf11.NewConfig, cfg.SEARCH_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SEARCH_SERVICE))
		return err
	}

	_ = searchGenConf

	rewardsEventPublisher, err := sqs.NewPublisherWithConfig(ctx, searchGenConf.RewardsEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	queryToSkillPathS3Client := s3pkg.NewClient(awsConf, searchGenConf.QueryToSkillPath().S3Bucket)

	indexConsumerService, err := wire14.InitializeIndexConsumerService(searchConf, usersClient, actorClient, piClient, depositClientVar5, accountPIRelationClient, fitttClientVar2)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	indexerpb.RegisterIndexerServer(s, indexConsumerService)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.FaqEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessFaqEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.AccountPiEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessPiEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.SavingsAccountStateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessSavingsAccountUpdateEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.TimeLineEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessTimeLineEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.TimeLineBulkUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessBulkTimelineEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.BulkTimelineBackfillSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessBulkTimelineEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.TxnEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessTxnEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.TxnBulkUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessBulkOrderMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.GmailEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessGmailEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.AaTxnEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessAATxnEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.CategorizerEventsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessCategorizerEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.CategorizerBatchEventsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessCategorizerBatchEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.CategorizerUpdateOntologyEventsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessCategorizerOntologyUpdateEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.CcTxnEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessCreditCardTransactionEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.AaBulkTxnEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessBulkAATransactionEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.FiTxnBulkUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessBulkTransactionEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.SherlockScriptEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessSherlockScriptsEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.SherlockSopEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		indexerpb.RegisterProcessSherlockSopEventMethodToSubscriber(subscriber, indexConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	searchServer, err := wire14.InitializeService(accessInfoClient, offerListingServiceClient, rewardsEventPublisher, broker, searchRedisStore, accountPIRelationClient, piClient, usersClient, groupClient, actorClient, accrualClient, rewardsGeneratorClient, cardProvisioningClient, inAppReferralClient, connectedAccountClient, ruleManagerClient, merchantServiceClient, savingsClient, depositClientVar5, catalogManagerClientVar2, cardControlClient, uPIClient, timelineServiceClient, p2PInvestmentClient, onboardingClient, salaryProgramClient, employmentClient, orderServiceClient, preApprovedLoanClient, catalogManagerClient, fireflyClient, reminderServiceClient, searchConf, portfolioManagerClient, bankCustomerServiceClient, exchangerOfferServiceClient, balanceClient, txnCategorizerClient, searchGenConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	search.RegisterActionBarServer(s, searchServer)

	developerService, err := wire14.InitializeSearchDeveloperService(searchConf, searchRedisStore, broker, queryToSkillPathS3Client)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	searchdevpb.RegisterDevSearchServer(s, developerService)

	indexConsumerServiceVar2, err := wire14.InitializeIndexConsumerService(searchConf, usersClient, actorClient, piClient, depositClientVar5, accountPIRelationClient, fitttClientVar2)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, searchGenConf.CategorizerBatchEventPerOrderSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			indexerpb.RegisterProcessCategoryBatchEventPerOrderEventMethodToSubscriber(subscriber, indexConsumerServiceVar2)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	configNameToConfMap[cfg.ConfigName(cfg.SEARCH_SERVICE)] = &commonexplorer.Config{StaticConf: &searchconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupBudgeting(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	ruleManagerClient rmsmanagerpb.RuleManagerClient,
	fireflyClient firefly.FireflyClient,
	billingClient fireflybilling.BillingClient,
	txnAggregatesClient txnaggregatespb.TxnAggregatesClient,
	savingsClient savingspb.SavingsClient,
	actorClient actor.ActorClient,
	budgetingPGDB types.BudgetingPGDB,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	accountingClient fireflyaccpb.AccountingClient,
	orderServiceClient orderpb.OrderServiceClient,
	commsClientVar17 comms.CommsClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	budgetingConf, err := budgetingconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.BUDGETING_SERVICE))
		return err
	}
	_ = budgetingConf

	budgetingGenConf, err := dynconf.LoadConfig(budgetingconf.Load, genconf12.NewConfig, cfg.BUDGETING_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.BUDGETING_SERVICE))
		return err
	}

	_ = budgetingGenConf

	reminderProcessorPublisher, err := sqs.NewPublisherWithConfig(ctx, budgetingGenConf.ReminderProcessorPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	rMSEventPublisherVar2, err := sqs.NewPublisherWithConfig(ctx, budgetingGenConf.RMSEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	reminderActionExecutorPublisher, err := sqs.NewPublisherWithConfig(ctx, budgetingGenConf.ReminderActionExecutorPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	reminderService := wire15.InitializeReminderService(ruleManagerClient, fireflyClient, billingClient, txnAggregatesClient, savingsClient, actorClient, budgetingPGDB, accountPIRelationClient, accountingClient, broker)

	reminderpb.RegisterReminderServiceServer(s, reminderService)

	serviceVar85 := wire15.InitializeReminderConsumerService(orderServiceClient, ruleManagerClient, accountPIRelationClient, txnAggregatesClient, commsClientVar17, actorClient, savingsClient, fireflyClient, accountingClient, reminderProcessorPublisher, rMSEventPublisherVar2, budgetingPGDB, reminderActionExecutorPublisher, broker, budgetingGenConf)

	reminderconsumerpb.RegisterConsumerServer(s, serviceVar85)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, budgetingGenConf.ReminderProcessorSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		reminderconsumerpb.RegisterProcessReminderSubscriptionsMethodToSubscriber(subscriber, serviceVar85)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, budgetingGenConf.CategorizerBudgetingEventsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		reminderconsumerpb.RegisterProcessCategoryEventMethodToSubscriber(subscriber, serviceVar85)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, budgetingGenConf.RmsEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		reminderconsumerpb.RegisterProcessRmsActionEventMethodToSubscriber(subscriber, serviceVar85)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, budgetingGenConf.ExecuteReminderActionSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		reminderconsumerpb.RegisterExecuteReminderActionMethodToSubscriber(subscriber, serviceVar85)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.BUDGETING_SERVICE)] = &commonexplorer.Config{StaticConf: &budgetingconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupGplace(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	vendordataPGDB types.VendordataPGDB,
	gPlaceClientVar3 vggplacepb.GPlaceClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	gplaceConf, err := gplaceconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.GPLACE_SERVICE))
		return err
	}
	_ = gplaceConf

	gPlaceService := wire16.InitializeGPlaceService(gplaceConf, vendordataPGDB, gPlaceClientVar3)

	gplace.RegisterGPlaceServer(s, gPlaceService)

	configNameToConfMap[cfg.ConfigName(cfg.GPLACE_SERVICE)] = &commonexplorer.Config{StaticConf: &gplaceconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupWealthonboarding(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiWealthCRDB types.EpifiWealthCRDB,
	ckycClient ckycvgpb.CkycClient,
	actorClient actor.ActorClient,
	usersClient user.UsersClient,
	groupClient usergrouppb.GroupClient,
	employmentClient employmentpb.EmploymentClient,
	savingsClient savingspb.SavingsClient,
	authClient authpb.AuthClient,
	customerClient vgcustomerpb.CustomerClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	livenessClientVar2 liveness.LivenessClient,
	nsdlClient nsdlvgpb.NsdlClient,
	cvlClient cvlvgpb.CvlClient,
	docsClient docs.DocsClient,
	manchClient manchvgpb.ManchClient,
	digioClient digiovgpb.DigioClient,
	consentClient consent.ConsentClient,
	ocrClient inhouseocr.OcrClient,
	digilockerClient digilockervgpb.DigilockerClient,
	commsClientVar17 comms.CommsClient,
	obfuscatorClient obfuscatorpb.ObfuscatorClient,
	uNNameCheckClient ncpb.UNNameCheckClient,
	wealthRedisStore wotypes.WealthRedisStore,
	ruleManagerClient rmsmanagerpb.RuleManagerClient,
	celestialClient celestialpb.CelestialClient,
	investmentProfileServiceClient profilepb.InvestmentProfileServiceClient,
	wealthOnboardingClient wealthpb.WealthOnboardingClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	wealthonboardingConf, err := wealthonboardingconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.WEALTH_ONBOARDING_SERVICE))
		return err
	}
	_ = wealthonboardingConf

	refreshFaceMatchStatusSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, wealthonboardingConf.RefreshFaceMatchStatusSqsPublisher, sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	userCommsDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, wealthonboardingConf.UserCommsPublisher, sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	wealthOnboardingStepsRetrySqsCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		wealthonboardingConf.WealthOnboardingStepsRetrySqsCustomDelayPublisher.OrchestratorSqsPublisher,
		customdelayqueue.QueueName(wealthonboardingConf.WealthOnboardingStepsRetrySqsCustomDelayPublisher.GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return err
	}

	authFactorUpdateWealthPublisher, err := sns.NewSnsPublisherWithConfig(ctx, wealthonboardingConf.AuthFactorUpdateWealthPublisher, awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	serviceVar86, err := wire17.InitializeWealthCkycService(epifiWealthCRDB, ckycClient, actorClient, usersClient, groupClient, wealthonboardingConf, employmentClient, awsConf, savingsClient, authClient, customerClient, bankCustomerServiceClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	wockycpb.RegisterCkycServer(s, serviceVar86)

	serviceVar87, err := wire17.InitializeService(epifiWealthCRDB, wealthonboardingConf, refreshFaceMatchStatusSqsPublisher, usersClient, groupClient, actorClient, livenessClientVar2, nsdlClient, cvlClient, ckycClient, docsClient, manchClient, digioClient, consentClient, employmentClient, ocrClient, authClient, digilockerClient, wealthOnboardingStepsRetrySqsCustomDelayPublisher, commsClientVar17, obfuscatorClient, broker, userCommsDelayPublisher, uNNameCheckClient, wealthRedisStore, ruleManagerClient, awsConf, celestialClient, savingsClient, investmentProfileServiceClient, customerClient, bankCustomerServiceClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	wealthpb.RegisterWealthOnboardingServer(s, serviceVar87)

	serviceVar88, err := wire17.InitializeWealthCxService(epifiWealthCRDB, wealthonboardingConf, refreshFaceMatchStatusSqsPublisher, usersClient, groupClient, actorClient, livenessClientVar2, nsdlClient, cvlClient, ckycClient, docsClient, manchClient, digioClient, consentClient, employmentClient, ocrClient, authClient, digilockerClient, wealthOnboardingStepsRetrySqsCustomDelayPublisher, commsClientVar17, obfuscatorClient, broker, userCommsDelayPublisher, uNNameCheckClient, wealthRedisStore, ruleManagerClient, awsConf, celestialClient, savingsClient, investmentProfileServiceClient, customerClient, bankCustomerServiceClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	wocxpb.RegisterWealthCxServiceServer(s, serviceVar88)

	woDevService := wire17.InitializeDevService(epifiWealthCRDB)

	wodevpb.RegisterDevWealthOnboardingServer(s, woDevService)

	serviceVar89, err := wire17.InitializeConsumerService(livenessClientVar2, wealthOnboardingClient, authFactorUpdateWealthPublisher, commsClientVar17, epifiWealthCRDB, ckycClient, actorClient, usersClient, groupClient, wealthonboardingConf, employmentClient, awsConf, savingsClient, authClient, customerClient, bankCustomerServiceClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithConfigV1(ctx, wealthonboardingConf.RefreshFaceMatchStatusSqsSubscriber, sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer14.RegisterRefreshFaceMatchStatusMethodToSubscriber(subscriber, serviceVar89)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithConfigV1(ctx, wealthonboardingConf.InitWealthOnboardingSubscriber, sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer14.RegisterInitWealthOnboardingMethodToSubscriber(subscriber, serviceVar89)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithConfigV1(ctx, wealthonboardingConf.WealthOnboardingStepsRetryDelaySqsSubscriber, sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer14.RegisterRetryOnboardingMethodToSubscriber(subscriber, serviceVar89)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithConfigV1(ctx, wealthonboardingConf.WealthAuthFactorUpdateEventSubscriber, sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer14.RegisterProcessAuthFactorUpdateEventMethodToSubscriber(subscriber, serviceVar89)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithConfigV1(ctx, wealthonboardingConf.UserCommsSubscriber, sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer14.RegisterSendUserNotificationsMethodToSubscriber(subscriber, serviceVar89)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.WEALTH_ONBOARDING_SERVICE)] = &commonexplorer.Config{StaticConf: &wealthonboardingconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupSecurities(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	stocksPGDB types.StocksPGDB,
	catalogClientVar3 vgcatalogpb2.CatalogClient,
	securitiesRedisStore wtypes4.SecuritiesRedisStore,
	catalogManagerClient usstockscatalogmanagerpb.CatalogManagerClient,
	investmentRedisStore types.InvestmentRedisStore) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	securitiesConf, err := securitiesconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SECURITIES_SERVICE))
		return err
	}
	_ = securitiesConf

	securitiesGenConf, err := dynconf.LoadConfig(securitiesconf.Load, genconf13.NewConfig, cfg.SECURITIES_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SECURITIES_SERVICE))
		return err
	}

	_ = securitiesGenConf

	stockCatalogRefreshPublisher, err := sqs.NewPublisherWithConfig(ctx, securitiesGenConf.StockCatalogRefreshPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	addNewSecuritiesPublisher, err := sqs.NewPublisherWithConfig(ctx, securitiesGenConf.AddNewSecuritiesPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	serviceVar90 := wire18.InitializeCatalogService(stocksPGDB, securitiesGenConf, catalogClientVar3, stockCatalogRefreshPublisher, securitiesRedisStore)

	catalogpb2.RegisterSecuritiesCatalogServer(s, serviceVar90)

	serviceVar91 := wire18.InitializeCatalogConsumerService(stocksPGDB, securitiesGenConf, catalogClientVar3, catalogManagerClient, addNewSecuritiesPublisher, investmentRedisStore)

	catalogconsumerpb.RegisterSecuritiesCatalogConsumerServer(s, serviceVar91)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, securitiesGenConf.StockCatalogRefreshSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		catalogconsumerpb.RegisterRefreshSecurityDetailsMethodToSubscriber(subscriber, serviceVar91)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, securitiesGenConf.SecuritiesCatalogAdditionSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		catalogconsumerpb.RegisterAddNewSecuritiesMethodToSubscriber(subscriber, serviceVar91)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, securitiesGenConf.SecuritiesHistoricalPriceSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		catalogconsumerpb.RegisterProcessSecurityListingHistoricalPricesMethodToSubscriber(subscriber, serviceVar91)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.SECURITIES_SERVICE)] = &commonexplorer.Config{StaticConf: &securitiesconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupNps(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	npsPGDB types.NpsPGDB,
	nPSClient nps.NPSClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	npsConf, err := npsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.NPS_SERVICE))
		return err
	}
	_ = npsConf

	serviceVar92, err := wire19.InitializeNPSService(npsPGDB, npsConf, nPSClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	npspb.RegisterNpsCatalogServer(s, serviceVar92)

	configNameToConfMap[cfg.ConfigName(cfg.NPS_SERVICE)] = &commonexplorer.Config{StaticConf: &npsconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.WEALTH_DMF_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
