# atlas service

## Service Specs

| Header        | Value         |
| ------------- |:-------------|
| Server binary      | gamma/cmd/atlas/server.go |
| Application Port      | 8000      |
| Health Check Port | 8001      |
| Health check path | /_health |
| Load balancer type | NLB / External |
| Config               | gamma/atlas/config/staging.yml |
| Inbound internet access | Yes |
| Outbound internet access | No |
| Service Dependencies | frontend, tokenizer |
| AWS Dependencies | No  |
| Database access | No |
