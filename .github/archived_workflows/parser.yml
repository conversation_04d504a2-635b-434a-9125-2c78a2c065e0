name: Parser
on:
  # Trigger the workflow when the pull request is labeled
  # This is done to avoid workflows triggers on each commit in the PR.
  # A jenkins job will be used to label the PR and add correct tags in description to check condition in the job's build rule
  pull_request:
    types: [ labeled ]
    paths:
      - 'pkg/**'
      - '!pkg/usstocks/**'
      - 'parser/**'
      - 'cmd/vendordata/config/parser-test.yml'
      - 'cmd/vendordata/config/parser-params.yml'
      - 'db/vendordata/fixture.sql'
      - 'db/vendordata/latest.sql'
      - '.github/workflows/parser.yml'
      - 'go.mod'
# Uncomment this to run on merge to master
#  push:
#    branches:
#      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  pr_status:
    if: ${{ (github.event.label.name == 'RunWorkflows') && (github.head_ref == 'validate-workflows') }}
    name: Check PR status - parser
    runs-on: [ self-hosted, gamma-small ]
    timeout-minutes: 3
    outputs:
      approved: ${{ startsWith(github.base_ref, 'epifi/m') || steps.approved.outputs.isApproved == 'true' }}
    steps:
      - name: Get Token
        id: get_workflow_token
        uses: peter-murray/workflow-application-token-action@dc0413987a085fa17d19df9e47d4677cf81ffef3
        with:
          application_id: ${{ secrets.FI_GITHUB_APP_ID }}
          application_private_key: ${{ secrets.FI_GITHUB_APP_PRIVATE_KEY }}
      - uses: epifi/github-actions@434512a7c43a4edd8866037c0d72132f52e3ba54
        if: ${{ !(startsWith(github.base_ref, 'epifi/m')) }}
        id: approved
        with:
          approvalsCount: '1'
        env:
          GITHUB_TOKEN: ${{ steps.get_workflow_token.outputs.token }}

  service:
    needs: [pr_status]
    if: ${{ ((github.event.label.name == 'RunWorkflows') && (github.head_ref == 'validate-workflows')) }}
    name: Parser
    runs-on: [ self-hosted, gamma-xlarge ]
    timeout-minutes: 20
    steps:


      # /usr/local/bin file permission is for coveralls to run
      - name: Fix file permission
        if: always()
        run: |
          sudo chown -Rf runner:runner /usr/local/bin
          sudo chown -Rf runner:runner /runner/_work

      - name: Check out code into the Go module directory
        uses: actions/checkout@v3
        with:
          path: go/src/github.com/epifi/gamma
          fetch-depth: 1
          ref: ${{ github.head_ref }}

      - name: Setup Databases
        if: always()
        run: ./scripts/github_action_helpers/start_cockroachdb.sh
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - id: go-cache-paths
        run: |
          echo "go-build=$(go env GOCACHE)" >> "$GITHUB_ENV"
          echo "go-mod=$(go env GOMODCACHE)" >> "$GITHUB_ENV"

      - name: Go Build Cache Restore
        uses: leroy-merlin-br/action-s3-cache@7fad0a81b31884660211f24b62e29b4777a6ac4c
        with:
          action: get
          aws-access-key-id: ${{ secrets.DEPLOY_GITHUB_ACTIONS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_GITHUB_ACTIONS_ACCESS_KEY_SECRET }}
          aws-region: ap-south-1 # Or whatever region your bucket was created
          bucket: "epifi-deploy-github-actions-cache"
          s3-class: STANDARD # It's STANDARD by default. It can be either STANDARD,
          # REDUCED_REDUDANCY, ONEZONE_IA, INTELLIGENT_TIERING, GLACIER, DEEP_ARCHIVE or STANDARD_IA.
          key: ${{ runner.os }}-go-build-master

      - name: generate wire_gen file
        run: |
          /home/<USER>/go/bin/wire ./parser/wire/
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: compare wire gen files
        run: |
          git add -N parser/wire/wire_gen.go
          current_status=`git status --porcelain parser/wire/wire_gen.go`
          if [ "$current_status" ]; then
            echo "*************** Hey there, pay attention to these files once ***************"
            echo "$current_status"
            echo "************************** BEGIN WIRE DIFFERENCES ***********************"
            git diff --color-words parser/wire/
            echo "************************** END WIRE DIFFERENCES *************************";
            exit 1;
          else
            echo "No Differences. You are doing great!"
          fi
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: install conf gen tool
        run: |
          go install -v ./tools/conf_gen/
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: install goimports dependency
        run: |
          go install -v golang.org/x/tools/cmd/goimports
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: generate conf files
        run: |
          go generate ./parser/config/
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: compare generated conf files
        run: |
          git add -N ./parser/config/
          current_status=`git status --porcelain ./parser/config/`
          if [ "$current_status" ]; then
            echo "*************** Hey there, pay attention to these files once ***************"
            echo "$current_status"
            echo "************************** BEGIN GENERATED CONF DIFFERENCES ***********************"
            git diff --color-words parser/config/
            echo "************************** END GENERATED CONF DIFFERENCES *************************";
            exit 1;
          else
            echo "No Differences. You are doing great!"
          fi
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: Get Token
        id: get_workflow_token
        uses: peter-murray/workflow-application-token-action@dc0413987a085fa17d19df9e47d4677cf81ffef3
        with:
          application_id: ${{ secrets.FI_GITHUB_APP_ID }}
          application_private_key: ${{ secrets.FI_GITHUB_APP_PRIVATE_KEY }}

      - name: Get added and modified files
        id: changed-files
        env:
          GITHUB_TOKEN: ${{ steps.get_workflow_token.outputs.token }}
          PR_NUMBER: ${{ github.event.number }}
        run: |
          go build ./tools/actions/pr_changed_files/pr_changed_files.go
          echo "added_and_modified_files=$(./pr_changed_files -ghtoken=$GITHUB_TOKEN -prnumber=$PR_NUMBER -skipremoved=true)" >> $GITHUB_OUTPUT
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: Run tests
        env:
          RUN_TEST_PARALLEL: true
        run: make test target=parser
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: Quality Gate - Test coverage should be above threshold
        env:
          TESTCOVERAGE_THRESHOLD: 60
        run: |
          echo "Quality Gate: checking test coverage is above threshold ..."
          echo "Threshold             : $TESTCOVERAGE_THRESHOLD %"
          grep -v "api/parser" code-coverage.out > temp.out
          totalCoverage=`go tool cover -func=temp.out | grep total | grep -Eo '[0-9]+\.[0-9]+'`
          echo "Current test coverage : $totalCoverage %"
          if (( $(echo "$totalCoverage $TESTCOVERAGE_THRESHOLD" | awk '{print ($1 > $2)}') )); then
            echo "OK"
          else
            echo "Current test coverage is below threshold. Please add more unit tests or adjust threshold to a lower value."
            echo "Failed"
          fi
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: Archive code coverage results
        uses: actions/upload-artifact@v1
        with:
          name: code-coverage-report
          path: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma/code-coverage.html

      - uses: LouisBrunner/checks-action@6b626ffbad7cc56fd58627f774b9067e6118af23
        if: ${{ steps.changes.outputs.impacted_paths != 'true' }}
        with:
          token: ${{ steps.get_workflow_token.outputs.token }}
          name: Parser / Verify build status - parser
          conclusion: ${{ job.status }}
