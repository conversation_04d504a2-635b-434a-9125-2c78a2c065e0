name: Update go build cache to s3
on:
  schedule:
    # run every hour on a week day from master
    - cron:  '0 4-16/1 * * 1-5'
    # run every 4 hours on a weekend day from master
    - cron:  '0 4-16/4 * * 0,6'


concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  cache:
    name: cache
    runs-on: [ self-hosted, gamma-xlarge-required-checks ]
    timeout-minutes: 45
    steps:
      - id: go-cache-paths
        run: |
          echo "go-build=$(go env GOCACHE)" >> "$GITHUB_OUTPUT"
          echo "go-mod=$(go env GOMODCACHE)" >> "$GITHUB_OUTPUT"

      - name: Clear cache
        run: |
          sudo rm -rf $(go env GOCACHE)
          sudo rm -rf $(go env GOMODCACHE)

      - name: Check out code into the Go module directory
        uses: actions/checkout@v3
        with:
          path: go/src/github.com/epifi/gamma
          fetch-depth: 1
          ref: ${{ github.head_ref }}

      - name: Build Apps
        run: |
          GOMAXPROCS=$(gomaxprocs) go build -v -mod=readonly -gcflags="-e" -ldflags '-s' -o /dev/null ./cmd/servers/qa/... ./cmd/worker/... || true
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: Save cache
        uses: leroy-merlin-br/action-s3-cache@7fad0a81b31884660211f24b62e29b4777a6ac4c
        with:
          action: put
          aws-access-key-id: ${{ secrets.DEPLOY_GITHUB_ACTIONS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_GITHUB_ACTIONS_ACCESS_KEY_SECRET }}
          aws-region: ap-south-1 # Or whatever region your bucket was created
          bucket: "epifi-deploy-github-actions-cache"
          s3-class: STANDARD # It's STANDARD by default. It can be either STANDARD,
          # REDUCED_REDUDANCY, ONEZONE_IA, INTELLIGENT_TIERING, GLACIER, DEEP_ARCHIVE or STANDARD_IA.
          key: ${{ runner.os }}-go-build-master
          artifacts: |
            ${{ steps.go-cache-paths.outputs.go-build }}
            ${{ steps.go-cache-paths.outputs.go-mod }}
