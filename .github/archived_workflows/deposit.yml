name: Deposit
on:
 # Trigger the workflow when the pull request is labeled
  # This is done to avoid workflows triggers on each commit in the PR.
  # A jenkins job will be used to label the PR and add correct tags in description to check condition in the job's build rule
  pull_request:
    types: [ labeled ]
    paths:
      - 'pkg/**'
      - '!pkg/usstocks/**'
      - 'deposit/**'
      - 'cmd/savings/config/deposit-test.yml'
      - 'cmd/savings/config/deposit-params.yml'
      - 'db/epifi/fixture.sql'
      - 'db/epifi/latest.sql'
      - '.github/workflows/deposit.yml'
      - 'go.mod'
# Uncomment this to run on merge to master
#  push:
#    branches:
#      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  pr_status:
    if: ${{ (github.event.label.name == 'RunWorkflows') && (github.head_ref == 'validate-workflows') }}
    name: Check PR status - deposit
    runs-on: [ self-hosted, gamma-small ]
    timeout-minutes: 3
    outputs:
      approved: ${{ startsWith(github.base_ref, 'epifi/m') || steps.approved.outputs.isApproved == 'true' }}
    steps:
      - name: Get Token
        id: get_workflow_token
        uses: peter-murray/workflow-application-token-action@dc0413987a085fa17d19df9e47d4677cf81ffef3
        with:
          application_id: ${{ secrets.FI_GITHUB_APP_ID }}
          application_private_key: ${{ secrets.FI_GITHUB_APP_PRIVATE_KEY }}
      - uses: epifi/github-actions@434512a7c43a4edd8866037c0d72132f52e3ba54
        if: ${{ !(startsWith(github.base_ref, 'epifi/m')) }}
        id: approved
        with:
          approvalsCount: '1'
        env:
          GITHUB_TOKEN: ${{ steps.get_workflow_token.outputs.token }}

  build:
    needs: [pr_status]
    if: ${{ ((github.event.label.name == 'RunWorkflows') && (github.head_ref == 'validate-workflows')) }}
    name: Deposit
    runs-on: [ self-hosted, go.crdb.postgres ]
    timeout-minutes: 20
    steps:


      # /usr/local/bin file permission is for coveralls to run
      - name: Fix file permission
        if: always()
        run: |
          sudo chown -Rf runner:runner /usr/local/bin
          sudo chown -Rf runner:runner /runner/_work

      - name: Check out code into the Go module directory
        uses: actions/checkout@v3
        with:
          path: go/src/github.com/epifi/gamma
          fetch-depth: 1
          ref: ${{ github.head_ref }}

      - name: Setup Databases
        if: always()
        run: ./scripts/github_action_helpers/start_cockroachdb.sh
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma


      # Cache go build cache, used to speedup go test
      - name: Go Build Cache Restore
        uses: leroy-merlin-br/action-s3-cache@7fad0a81b31884660211f24b62e29b4777a6ac4c
        with:
          action: get
          aws-access-key-id: ${{ secrets.DEPLOY_GITHUB_ACTIONS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_GITHUB_ACTIONS_ACCESS_KEY_SECRET }}
          aws-region: ap-south-1
          bucket: "epifi-deploy-github-actions-cache"
          s3-class: STANDARD
          key: ${{ runner.os }}-go-build-master

      - name: generate wire_gen file
        run: |
          /home/<USER>/go/bin/wire ./deposit/wire/
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: compare wire gen files
        run: |
          git add -N deposit/wire/wire_gen.go
          current_status=`git status --porcelain deposit/wire/wire_gen.go`
          if [ "$current_status" ]; then
            echo "*************** Hey there, pay attention to these files once ***************"
            echo "$current_status"
            echo "************************** BEGIN WIRE DIFFERENCES ***********************"
            git diff --color-words deposit/wire/
            echo "************************** END WIRE DIFFERENCES *************************";
            exit 1;
          else
            echo "No Differences. You are doing great!"
          fi
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: install conf gen tool
        run: |
          go install -v ./tools/conf_gen/
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: install goimports dependency
        run: |
          go install -v golang.org/x/tools/cmd/goimports
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: generate conf files
        run: |
          go generate ./deposit/config/
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: compare generated conf files
        run: |
          git add -N ./deposit/config/
          current_status=`git status --porcelain ./deposit/config/`
          if [ "$current_status" ]; then
            echo "*************** Hey there, pay attention to these files once ***************"
            echo "$current_status"
            echo "************************** BEGIN CONFIG DIFFERENCES ***********************"
            git diff --color-words deposit/config/
            echo "************************** END CONFIG DIFFERENCES *************************";
            exit 1;
          else
            echo "No Differences. You are doing great!"
          fi
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: Run tests
        env:
          USE_CRDB_SCHEMA_BACKUP: true
        run: make test target=deposit
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: Quality Gate - Test coverage should be above threshold
        env:
          TESTCOVERAGE_THRESHOLD: 60
        run: |
          echo "Quality Gate: checking test coverage is above threshold ..."
          echo "Threshold             : $TESTCOVERAGE_THRESHOLD %"
          grep -v "api/deposit" code-coverage.out > temp.out
          totalCoverage=`go tool cover -func=temp.out | grep total | grep -Eo '[0-9]+\.[0-9]+'`
          echo "Current test coverage : $totalCoverage %"
          if (( $(echo "$totalCoverage $TESTCOVERAGE_THRESHOLD" | awk '{print ($1 > $2)}') )); then
            echo "OK"
          else
            echo "Current test coverage is below threshold. Please add more unit tests or adjust threshold to a lower value."
            echo "Failed"
          fi
        shell: bash
        working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma

      - name: Archive code coverage results
        uses: actions/upload-artifact@v1
        with:
          name: code-coverage-report
          path: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma/code-coverage.html

      - name: Get Token
        id: get_workflow_token
        uses: peter-murray/workflow-application-token-action@dc0413987a085fa17d19df9e47d4677cf81ffef3
        with:
          application_id: ${{ secrets.FI_GITHUB_APP_ID }}
          application_private_key: ${{ secrets.FI_GITHUB_APP_PRIVATE_KEY }}

      - name: Send coverage to coveralls
        uses: coverallsapp/github-action@95b1a2355bd0e526ad2fd62da9fd386ad4c98474
        env:
          COVERALLS_TOKEN: ${{ steps.get_workflow_token.outputs.token }}
          COVERALLS_SERVICE_JOB_ID: ${{ github.sha }}
          COVERALLS_SERVICE_NUMBER: ${{ github.sha }}
        with:
          flag-name: 'deposit'
          github-token: ${{ steps.get_workflow_token.outputs.token }}
          file: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma/temp.out
          compare-ref: validate-workflows
        continue-on-error: true

      - uses: LouisBrunner/checks-action@6b626ffbad7cc56fd58627f774b9067e6118af23
        if: ${{ steps.changes.outputs.impacted_paths != 'true' }}
        with:
          token: ${{ steps.get_workflow_token.outputs.token }}
          name: Deposit / Verify build status - deposit
          conclusion: ${{ job.status }}
