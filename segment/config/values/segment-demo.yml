Application:
  Environment: "demo"
  Name: "segment"

# Segment service is actually initialized on the port defined in user-<env>.yml
# These properties are kept here from forward compatibility POV when we may want Segment service to be running on a
# different port in the user server
Server:
  Ports:
    GrpcPort: 8083
    GrpcSecurePort: 9521
    HttpPort: 9999

SegmentDb:
  AppName: "segment"
  StatementTimeout: 5s
  Name: "segment"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Secrets:
  Ids:
    DbCredentials: "demo/rds/postgres"
    RudderWriteKey: "demo/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "demo/gcloud/profiling-service-account-key"

AWS:
  Region: "ap-south-1"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.demo-common-cache-redis.wqltco.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12

Tracing:
  Enable: true

PaginationOptions:
  MaxPageSize: 1000

AWSPinpointOptions:
  S3RoleARN: "arn:aws:iam::************:role/non-prod-to-data-dev-s3-role"
  AssumeRoleARN: "arn:aws:iam::************:role/non-prod-to-data-dev-pinpoint-role"
  S3UrlPrefix: "s3://epifi-data-rewards-dev/segment-exports"

TriggerSegmentExportPublisher:
  QueueName: "demo-segment-trigger-segment-export-queue"

TriggerSegmentExportSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "demo-segment-trigger-segment-export-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

PollSegmentExportPublisher:
  QueueName: "demo-segment-poll-segment-export-queue"

PollSegmentExportSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "demo-segment-poll-segment-export-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"

ProcessSegmentExportPartFileSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 50
  QueueName: "demo-segment-process-segment-export-part-file-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 8
      MaxAttempts: 16
      TimeUnit: "Second"

UploadSegmentExportPartFileSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 50
  QueueName: "demo-segment-upload-segment-export-part-file-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 8
      MaxAttempts: 16
      TimeUnit: "Second"

UploadSegmentExportPartFilePublisher:
  QueueName: "demo-segment-upload-segment-export-part-file-queue"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true
