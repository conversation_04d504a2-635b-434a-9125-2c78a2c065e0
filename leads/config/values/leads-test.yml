Application:
  Environment: "test"
  Name: "leads"
  ServerName: "lending"

Server:
  Ports:
    GrpcPort: 8111
    GrpcSecurePort: 9528
    HttpPort: 9899

DbConfigMap:
  EPIFI_TECH_V2:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_epifi_tech_test"
    StatementTimeout: 5m
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

AWS:
  Region: "ap-south-1"
