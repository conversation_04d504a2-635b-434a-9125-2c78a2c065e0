Application:
  Environment: "uat"
  Name: "leads"
  ServerName: "lending"

Server:
  Ports:
    GrpcPort: 8111
    GrpcSecurePort: 9528
    HttpPort: 9899

DbConfigMap:
  EPIFI_TECH_V2:
    DbType: "PGDB"
    AppName: "leads"
    StatementTimeout: 5s
    Name: "loans_epifi_tech"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "uat/rds/epifimetis/loans_epifi_tech_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

AWS:
  Region: "ap-south-1"
