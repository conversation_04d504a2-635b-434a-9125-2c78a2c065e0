package consumer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"math"
	"strconv"
	"time"

	"go.uber.org/zap"
	gormv2 "gorm.io/gorm"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"

	authPb "github.com/epifi/gamma/api/auth"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payIncidentManagerPb "github.com/epifi/gamma/api/pay/payincidentmanager"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	typesPb "github.com/epifi/gamma/api/typesv2"
	upiPb "github.com/epifi/gamma/api/upi"
	vgPb "github.com/epifi/gamma/api/vendorgateway"
	vgHeaderPb "github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	vgUPIPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	events2 "github.com/epifi/gamma/frontend/pay/transaction/events"
	config "github.com/epifi/gamma/order/config/genconf"
	"github.com/epifi/gamma/order/dao"
	orderEvent "github.com/epifi/gamma/order/events"
	paymentProcessor "github.com/epifi/gamma/order/internal/payment"
	"github.com/epifi/gamma/order/internal/upi"
	"github.com/epifi/gamma/order/metrics"
	orderTypes "github.com/epifi/gamma/order/types"
	"github.com/epifi/gamma/order/wire/types"
	payPkgTxns "github.com/epifi/gamma/pay/pkg/transactions"
	"github.com/epifi/gamma/pkg/feature/release"
	payPkg "github.com/epifi/gamma/pkg/pay"
	upiPkg "github.com/epifi/gamma/pkg/upi"
	upiEvents "github.com/epifi/gamma/upi/events"
)

var (
	federalRequestIdRandomSequenceLen = 5

	errTxnInSuspect = fmt.Errorf("txn in suspect: %w", epifierrors.ErrTransient)

	errTxnFailed = fmt.Errorf("txn failed: %w", epifierrors.ErrPermanent)

	errTxnInManualIntervention = fmt.Errorf("txn in manual intervention: %w", epifierrors.ErrTransient)

	upiTxnTypeToChkTxnStatusTypeMap = map[upiPb.TransactionType]vgUPIPb.ReqCheckTxnStatusRequest_CheckTxnSubType{
		upiPb.TransactionType_PAY:     vgUPIPb.ReqCheckTxnStatusRequest_PAY,
		upiPb.TransactionType_COLLECT: vgUPIPb.ReqCheckTxnStatusRequest_COLLECT,
		// in order to maintain backward compatibility for unspecified txn type we default to PAY
		upiPb.TransactionType_TRANSACTION_TYPE_UNSPECIFIED: vgUPIPb.ReqCheckTxnStatusRequest_PAY,
	}

	declineTypeToErrorCategoryMap = map[vgPb.DeclineType]paymentPb.TransactionDetailedStatus_DetailedStatus_ErrorCategory{
		vgPb.DeclineType_TECHNICAL: paymentPb.TransactionDetailedStatus_DetailedStatus_SERVER,
		vgPb.DeclineType_BUSINESS:  paymentPb.TransactionDetailedStatus_DetailedStatus_USER,
	}

	updatedTxnStatusToQueueStatusMap = map[paymentPb.TransactionStatus]queuePb.MessageConsumptionStatus{
		paymentPb.TransactionStatus_SUCCESS:             queuePb.MessageConsumptionStatus_SUCCESS,
		paymentPb.TransactionStatus_MANUAL_INTERVENTION: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
	}
)

type Service struct {
	// UnimplementedConsumerServer is embedded to have forward compatible implementations
	paymentPb.UnimplementedConsumerServer

	dao                                  dao.TransactionDao
	vgPaymentClient                      vgPaymentPb.PaymentClient
	vgUPIClient                          vgUPIPb.UPIClient
	authClient                           authPb.AuthClient
	orderOrchestrationPublisher          orderTypes.OrderOrchestrationPublisher
	EventBroker                          events.Broker
	config                               *config.Config
	piClient                             piPb.PiClient
	orderClient                          orderPb.OrderServiceClient
	upiProcessor                         upi.UpiProcessor
	DeemedTransactionUPIEnquiryPublisher queue.Publisher
	paymentHealthProcessor               paymentProcessor.IPaymentHealthProcessor
	orderUpdatePublisher                 orderTypes.OrderUpdateEventPublisher
	evaluator                            release.IEvaluator
	payIncidentManagerClient             payIncidentManagerPb.PayIncidentManagerClient
	txnDetailedStatusUpdateSnsPublisher  types.TxnDetailedStatusUpdateSnsPublisher
}

// Factory method for creating an instance of payment consumer service. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewService(
	dao dao.TransactionDao,
	vgPaymentClient vgPaymentPb.PaymentClient,
	authClient authPb.AuthClient,
	orderOrchestrationPublisher orderTypes.OrderOrchestrationPublisher,
	vgUPIClient vgUPIPb.UPIClient,
	broker events.Broker,
	config *config.Config,
	piClient piPb.PiClient,
	orderClient orderPb.OrderServiceClient,
	upiProcessor upi.UpiProcessor,
	deemedTransactionUPIEnquiryPublisher orderTypes.DeemedTransactionUPIEnquiryPublisher,
	paymentHealthProcessor paymentProcessor.IPaymentHealthProcessor,
	orderUpdatePublisher orderTypes.OrderUpdateEventPublisher,
	evaluator release.IEvaluator,
	payIncidentManagerClient payIncidentManagerPb.PayIncidentManagerClient,
	txnDetailedStatusUpdateSnsPublisher types.TxnDetailedStatusUpdateSnsPublisher,
) *Service {
	return &Service{
		dao:                                  dao,
		vgPaymentClient:                      vgPaymentClient,
		vgUPIClient:                          vgUPIClient,
		authClient:                           authClient,
		orderOrchestrationPublisher:          orderOrchestrationPublisher,
		EventBroker:                          broker,
		config:                               config,
		piClient:                             piClient,
		orderClient:                          orderClient,
		upiProcessor:                         upiProcessor,
		DeemedTransactionUPIEnquiryPublisher: deemedTransactionUPIEnquiryPublisher,
		paymentHealthProcessor:               paymentHealthProcessor,
		orderUpdatePublisher:                 orderUpdatePublisher,
		evaluator:                            evaluator,
		payIncidentManagerClient:             payIncidentManagerClient,
		txnDetailedStatusUpdateSnsPublisher:  txnDetailedStatusUpdateSnsPublisher,
	}
}

// Flow for a packet reaching this consumer ->
// 1. When a transaction is created, a packet is pushed to pay-upi-enquiry queue.
// 2. From that queue, ProcessPayment consumes it and checks with vendor for the status,
//    updates it in db and then pushes it to order-orchestration-queue if success else it is
//    retried / dropped depending result of the call
// 3. This is consumed by ProcessOrder. Basically order has different stages and it keeps checking status
//    of different stages and is updated accordingly,
//    but transaction on the other side enquires with vendor, the result is published to order-orchestration queue
//    and order will be updated as per the transaction status
// 4. so basically order has 2 ways to update its status, it will check status of its different stages and update its status
//    and other is to poll order-orchestration queue and update itself based on txns status.

// NOTE:
//    Deemed txns were earlier marked as Success, now they are marked as Pending for 48 hours before marking
//    it as success, due to which they are retried. The volume of deemed txns is pretty high, so that affects
// 	  the processing of the other txns also, because deemed txns are being retried a lot. So in order to solve
//    it, deemed txns will be handled by a separate consumer, ProcessPaymentDeemed. So this consumer will be
//    offloaded and hence others txns will be processed more efficiently. As soon as ProcessPayment gets a
//    txn status as DEEMED for any txn, it will move it to a seperate queue whose packets will be consumed by
//    ProcessPaymentDeemed.

// ProcessPayment is mainly called by queue subscriber on receiving messages from queue.
// The method check payment status at the bank's end and update transaction status accordingly
// nolint: funlen
func (s *Service) ProcessPayment(ctx context.Context, req *paymentPb.ProcessPaymentRequest) (*paymentPb.ProcessPaymentResponse, error) {
	// will be used in helper functions to identify the consumer (ProcessPayment / ProcessPaymentDeemed)  which has initiated the call
	isConsumerForDeemedTxns := false
	logger.Info(ctx, "processing payment orchestration packet", logger.TxnId(req.GetTransactionId()))
	return s.processPaymentOrchestrationPacket(ctx, req, isConsumerForDeemedTxns)
}

// processPaymentOrchestrationPacket is common/helper method to process packets consumed by ProcessPayment and ProcessDeemedPayments.
// Most of the business logic for both the consumers is same. The difference just lies in the type of transactions processed (Deemed / Not-Deemed)
// So this method has been added in favour of reusability of code.
func (s *Service) processPaymentOrchestrationPacket(ctx context.Context, req *paymentPb.ProcessPaymentRequest, isConsumerForDeemedTxns bool) (*paymentPb.ProcessPaymentResponse, error) {
	var (
		res         = &paymentPb.ProcessPaymentResponse{}
		header      = &queuePb.ConsumerResponseHeader{}
		transaction *paymentPb.Transaction
		reqInfo     *paymentPb.PaymentRequestInformation
		err         error
	)

	res.ResponseHeader = header

	defer func() {
		if req.RequestHeader.IsLastAttempt && res.ResponseHeader.Status == queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE && transaction != nil {
			if isConsumerForDeemedTxns && transaction.IsDeemedTransaction() &&
				transaction.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
				res.ResponseHeader.Status = markSuccess(ctx, transaction, s.dao, s.piClient)
			} else {
				res.ResponseHeader.Status = markManualIntervention(ctx, transaction, s.dao, s.piClient)
			}
			if transaction.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
				s.upiProcessor.AddUpiMetricsForPspAsync(ctx, transaction.GetStatus(), transaction.GetPiFrom(), transaction.GetPiTo())
			}
		}

		if transaction.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
			upiTransactionEvent, er := getNewUpiTransactionEvent(req.GetActorId(), transaction, reqInfo)
			if er == nil {
				s.EventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), upiTransactionEvent)
			}
		}
	}()

	transaction, reqInfo, err = s.dao.GetByIdWithPaymentReqInfo(ctx, req.GetTransactionId())
	if err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			logger.Error(ctx, "record not found",
				zap.String("transaction-id", req.TransactionId),
				zap.Error(err))
			header.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		} else {
			logger.Error(ctx, "unable to fetch transaction details atm",
				zap.String("transaction-id", req.TransactionId),
				zap.Error(err))
			header.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
		}
		return res, nil
	}

	switch transaction.GetStatus() {
	// Based on various possible error scenarios a transaction can be in following state when process payment is invoked.
	// INITIATED - transaction was created and post that DB update failed.
	// IN_PROGRESS - transaction was created, call to VG returned a positive acknowledgment	and DB state was updated
	// post that.
	// UNKNOWN - transaction was created and then VG RPC call to initiate payment failed due to various reasons.
	// Some of them are	rpc timeout, lost response, etc.
	// In all the above cases a transaction status has to be moved to a terminal state after checking status at banks end
	// and actor has to be notified.
	case paymentPb.TransactionStatus_INITIATED, paymentPb.TransactionStatus_IN_PROGRESS, paymentPb.TransactionStatus_UNKNOWN:
		err = s.fetchPaymentStatusAndUpdateDB(ctx, transaction, reqInfo, req.PhoneNumber, req.GetActorId(), req.GetCustomerId(), isConsumerForDeemedTxns, req.GetRequestHeader().GetIsLastAttempt())
		switch {
		case errors.Is(err, errTxnInSuspect):
			logger.Info(ctx, "transaction still in suspect state", zap.String(logger.TXN_ID, transaction.GetId()))
			header.Status = queue.GetStatusFrom(err)
			return res, nil
		case errors.Is(err, errTxnFailed):
			logger.Info(ctx, "transaction failed", zap.String(logger.TXN_ID, transaction.GetId()))
			header.Status = queue.GetStatusFrom(err)
			return res, nil
		case errors.Is(err, errTxnInManualIntervention):
			logger.Info(ctx, "transaction is in Manual Intervention", zap.String(logger.TXN_ID, transaction.GetId()))
			header.Status = queue.GetStatusFrom(err)
			return res, nil
		case err != nil:
			logger.Error(ctx, "failed to fetch transaction status",
				zap.String("transaction-id", transaction.Id),
				zap.String("status", transaction.Status.String()),
				zap.Error(err))
			header.Status = queue.GetStatusFrom(err)
			return res, nil
		}

	//	Following states represents terminal states of transaction state machine. When ever transaction reaches a
	// a terminal state. Message is deleted from the queue. Re-appearing of the packet means a duplicate packet or a
	// possible failure while deletion. In such cases the message has to be deleted again.
	case paymentPb.TransactionStatus_MANUAL_INTERVENTION, paymentPb.TransactionStatus_FAILED, paymentPb.TransactionStatus_SUCCESS, paymentPb.TransactionStatus_REVERSED:
		logger.Info(ctx, "received duplicate packet for payment processing, transaction already in terminal status",
			zap.String("transaction-id", transaction.Id),
			zap.String("status", transaction.GetStatus().String()))
		header.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil

	default:
		logger.Error(ctx, "transaction found in unexpected state",
			zap.String("transaction-id", transaction.Id),
			zap.String("status", transaction.GetStatus().String()))
		header.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil
	}

	header.Status = queuePb.MessageConsumptionStatus_SUCCESS
	return res, nil
}

// fetchPaymentStatusAndUpdateDB fetches payment status from the bank and updates transaction status depending on the
// response.
func (s *Service) fetchPaymentStatusAndUpdateDB(ctx context.Context, txn *paymentPb.Transaction,
	reqInfo *paymentPb.PaymentRequestInformation, remitterPhoneNumber string, actorId string, customerId string, isConsumerForDeemedTxns, isLastAttempt bool) error {
	var (
		statusTobeUpdated     = txn.Status
		err                   error
		updateTransactionMask []paymentPb.TransactionFieldMask
	)
	switch {
	case txn.GetPaymentProtocol().IsAccountPaymentProtocol():
		updateTransactionMask, err = s.getAccountPaymentStatus(ctx, txn, reqInfo, remitterPhoneNumber, actorId, customerId)
	case txn.PaymentProtocol == paymentPb.PaymentProtocol_UPI:
		updateTransactionMask, err = s.getUPIPaymentStatus(ctx, actorId, txn, reqInfo, isLastAttempt)
	default:
		return fmt.Errorf("unknown payment protocol: %s: %w", txn.PaymentProtocol, queue.ErrPermanent)
	}

	if err != nil {
		return fmt.Errorf("can't fetch txn status: %w", err)
	}

	// Moving this packet to a separate queue / consumer in order to offload this consumer to process other transactions
	// this is being done because deemed transactions contribute for a very high volume as of now
	// and they keep on retrying which affects queue size drastically

	if !isConsumerForDeemedTxns && txn.GetLatestTxnDetailedStatus().GetState() == paymentPb.TransactionDetailedStatus_DetailedStatus_DEEMED {
		if er := s.publishToDeemedTxnsPaymentEnquiryQueue(ctx, txn, remitterPhoneNumber, actorId, customerId); er != nil {
			return fmt.Errorf("failed to publish packet to deemed-transactions-payment-enquiry-queue: %w", epifierrors.ErrTransient)
		}
		// now this packet will be processed by the new queue. We'll mark this packet processing as success
		// for current consumer, so that it's not retried by it.
		return nil
	}

	// TODO(nitesh): below operation has to be done in a transactional block
	if len(updateTransactionMask) != 0 {
		err = s.paymentHealthProcessor.UpdateTransactionAndPushMetrics(ctx, txn, reqInfo, updateTransactionMask)
		if err != nil {
			return fmt.Errorf("failed to update DB status to %s : %v : %w",
				statusTobeUpdated.String(), err.Error(), queue.ErrDB)
		}

		if txn.GetStatus() == paymentPb.TransactionStatus_SUCCESS {
			logger.Info(ctx, "transaction successful", zap.String(logger.TXN_ID, txn.GetId()), zap.String(logger.PAYMENT_PROTOCOL, txn.GetPaymentProtocol().String()))
		}

		if paymentProcessor.SearchInFieldMasks(updateTransactionMask, paymentPb.TransactionFieldMask_STATUS) {
			bankName, _ := payPkgTxns.GetBankNameForPiId(ctx, txn.GetPiFrom(), s.piClient)
			metrics.RecordOnAppTxnStateChangeMetrics(txn, bankName)
			if txn.GetStatus() == paymentPb.TransactionStatus_SUCCESS {
				// nocustomlint:goroutine
				go s.EventBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
					orderEvent.NewDebitTransactions(actorId, actorId, txn.GetId(), txn.GetPaymentProtocol().String(),
						txn.GetStatus().String(), txn.GetExecutionTS().AsTime()))
			}
			if txn.GetStatus() == paymentPb.TransactionStatus_FAILED {
				metrics.IncrementOnAppTxnFailureCount(txn.GetLatestTxnDetailedStatus().GetErrorCategory(), txn.GetPaymentProtocol())
			}
		}

		if err = s.publishToOrderQueue(ctx, txn, actorId); err != nil {
			return fmt.Errorf("publish failed: %v : %w", err.Error(), queue.ErrTransient)
		}
	}

	switch txn.Status {
	case paymentPb.TransactionStatus_SUCCESS:
		return nil
	case paymentPb.TransactionStatus_FAILED:
		return errTxnFailed
	case paymentPb.TransactionStatus_MANUAL_INTERVENTION:
		return errTxnInManualIntervention
	default:
		return errTxnInSuspect
	}
}

// getAccountPaymentStatus check payment status for bank account transfer protocols (IMPS/NEFT/RTGS/INTRA BANK)
// and map it to appropriate transaction status
func (s *Service) getAccountPaymentStatus(ctx context.Context, txn *paymentPb.Transaction, reqInfo *paymentPb.PaymentRequestInformation,
	remitterPhoneNumber string, actorId string, customerId string) ([]paymentPb.TransactionFieldMask, error) {
	var (
		updateMask    []paymentPb.TransactionFieldMask
		detailState   paymentPb.TransactionDetailedStatus_DetailedStatus_State
		errorCategory paymentPb.TransactionDetailedStatus_DetailedStatus_ErrorCategory
	)

	// Get device auth details for current actor id
	deviceId, deviceToken, userProfileId, err := s.getDeviceAuthDetails(ctx, actorId)
	if err != nil {
		return updateMask, fmt.Errorf("rpc call to get device auth details failed: %w", err)
	}

	payReq := &vgPaymentPb.GetStatusRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: txn.GetPartnerBank(),
		},
		Auth: &vgHeaderPb.Auth{
			DeviceId:      deviceId,
			DeviceToken:   deviceToken,
			UserProfileId: userProfileId,
			CustomerId:    customerId,
		},
		RequestId:         idgen.FederalRandomSequence("NEOENQRQ", federalRequestIdRandomSequenceLen),
		Protocol:          txn.PaymentProtocol,
		OriginalRequestId: reqInfo.ReqId,
		PhoneNumber:       remitterPhoneNumber,
	}

	payRes, err := s.vgPaymentClient.GetStatus(ctx, payReq)
	if err != nil {
		txnDetailedStatus := payPkg.GetTxnDetailedStatusForSystemErr(err, paymentPb.TransactionDetailedStatus_DetailedStatus_FUND_TRANSFER_PAYMENT_ENQUIRY)
		txn.AddDetailedStatus(txnDetailedStatus)
		updateMask = append(updateMask, paymentPb.TransactionFieldMask_DETAILED_STATUS)
		updateErr := s.dao.Update(ctx, txn, reqInfo, updateMask)
		publishErr := payPkg.PublishTxnDetailedStatusUpdateEventIfRequired(ctx, txn, s.txnDetailedStatusUpdateSnsPublisher, updateMask, false)
		if publishErr != nil {
			logger.Error(ctx, "failed to publish event to txn detailed status update topic", zap.Error(publishErr))
		}
		if updateErr != nil {
			logger.Error(ctx, "error updating transaction", zap.Error(updateErr))
		}
		return nil, fmt.Errorf("rpc call for check payment status failed: %v: %w", err.Error(), queue.ErrRPC)
	}

	switch {
	case payRes.Status.IsSuccess():
		updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_SUCCESS, paymentPb.TransactionFieldMask_STATUS, updateMask)
		updateMask = txn.SafeUpdateField(payRes.GetUtr(), paymentPb.TransactionFieldMask_UTR, updateMask)
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS
	case payRes.GetStatus().IsInProgress():
		// for neft/rtgs transaction we get in-progress response from vendor and sometime it will never move to success status as
		// vendor do not get response from creditor bank. As discussed with Vendor(i.e. Federal) , we can move such transactions to success
		// if we get in progress response after x time elapsed after transaction.
		if payPkg.ShouldMoveInProgressTransactionToSuccess(s.config.PaymentEnquiryParams(), payRes.GetStatusCode(), txn) {
			updateMask = txn.SafeUpdateField(payRes.GetUtr(), paymentPb.TransactionFieldMask_UTR, updateMask)
			updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_SUCCESS, paymentPb.TransactionFieldMask_STATUS, updateMask)
			detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS
		} else {
			updateMask = txn.SafeUpdateField(payRes.GetUtr(), paymentPb.TransactionFieldMask_UTR, updateMask)
			updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_IN_PROGRESS, paymentPb.TransactionFieldMask_STATUS, updateMask)
			detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS
		}
	case payRes.GetStatus().GetCode() == uint32(vgPaymentPb.GetStatusResponse_BUSINESS_FAILURE):
		updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_FAILED, paymentPb.TransactionFieldMask_STATUS, updateMask)
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE
		errorCategory = paymentPb.TransactionDetailedStatus_DetailedStatus_USER
	case payRes.GetStatus().GetCode() == uint32(vgPaymentPb.GetStatusResponse_FAILED),
		payRes.GetStatus().GetCode() == uint32(vgPaymentPb.GetStatusResponse_TRANSIENT_FAILURE),
		payRes.GetStatus().GetCode() == uint32(vgPaymentPb.GetStatusResponse_TECHNICAL_FAILURE),
		payRes.GetStatus().GetCode() == uint32(vgPaymentPb.GetStatusResponse_DEVICE_KEY_NOT_REGISTERED),
		payRes.GetStatus().GetCode() == uint32(vgPaymentPb.GetStatusResponse_INVALID_CRED_BLOCK):
		updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_FAILED, paymentPb.TransactionFieldMask_STATUS, updateMask)
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE
		errorCategory = paymentPb.TransactionDetailedStatus_DetailedStatus_SERVER
	case payRes.GetStatus().GetCode() == uint32(vgPaymentPb.GetStatusResponse_DEVICE_TEMPORARILY_DEACTIVATED):
		logger.Info(ctx, "Got response device deactivated temporarily. Moving transaction to failed state", zap.String(logger.TXN_ID, txn.GetId()))
		updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_FAILED, paymentPb.TransactionFieldMask_STATUS, updateMask)
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE
		errorCategory = paymentPb.TransactionDetailedStatus_DetailedStatus_USER
	case payRes.GetStatus().IsRecordNotFound():
		if payPkg.ShouldRetryForRecordNotFound(s.config.PaymentEnquiryParams(), txn) {
			updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_UNKNOWN, paymentPb.TransactionFieldMask_STATUS, updateMask)
			detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_UNKNOWN
		} else {
			updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_FAILED, paymentPb.TransactionFieldMask_STATUS, updateMask)
			detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE
		}
	case s.isImpsDeemedHandlingEnabled(ctx, actorId) && payRes.GetStatus().GetCode() == uint32(vgPaymentPb.GetStatusResponse_DEEMED):
		logger.Info(ctx, "imps txn is in deemed state", zap.String(logger.TXN_ID, txn.GetId()))
		txnStatus := paymentPb.TransactionStatus_IN_PROGRESS
		isEnquiryDurationOver, err := payPkg.IsDeemedTransactionEnquiryDurationOver(ctx, txn, s.piClient, s.config.PaymentEnquiryParams())
		if err != nil {
			return nil, fmt.Errorf("error while checking if enquiry duration for deemed transaction is over %w", err)
		}
		if isEnquiryDurationOver {
			// we update txn status to success if enquiry duration for deemed transaction is over
			logger.Debug(ctx, "enquiry duration for deemed txn is over, so changing the txn status to success.", zap.String(logger.TXN_ID, txn.GetId()))
			txnStatus = paymentPb.TransactionStatus_SUCCESS
		}
		updateMask = txn.SafeUpdateField(txnStatus, paymentPb.TransactionFieldMask_STATUS, updateMask)
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_DEEMED
	case payRes.GetStatus().IsUnknown():
		updateMask = txn.SafeUpdateField(payRes.GetUtr(), paymentPb.TransactionFieldMask_UTR, updateMask)
		updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_UNKNOWN, paymentPb.TransactionFieldMask_STATUS, updateMask)
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_UNKNOWN
	default:
		return nil, fmt.Errorf("status check RPC returned unexpected status: %s: %w", payRes.GetStatus(), queue.ErrTransient)
	}

	// for intra bank, a combination of (cbsID, batch serial id and date) is necessary to satisfy unique utr for the
	// transaction
	// However, since we dont get serialID in callback we dont update utr in DB. The utr in this case, is expected
	// to be updated via notification callback or recon
	if txn.PaymentProtocol == paymentPb.PaymentProtocol_INTRA_BANK &&
		paymentProcessor.SearchInFieldMasks(updateMask, paymentPb.TransactionFieldMask_UTR) {
		txn.Utr = ""
		updateMask = paymentProcessor.RemoveFieldMask(updateMask, paymentPb.TransactionFieldMask_UTR)
	}

	// TODO(Sundeep): Remove this once federal bank completely integrates the UTR22 changes.
	// This is added as a temporary fix to handle the case where we get the new UTR in the enquiry response, but not
	// in the CBS notification and because of that the txn is already created with the new UTR. And since we need
	// to persist the new UTR we update it here to override the older UTR, so that we persist the new UTR.
	if txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_NEFT &&
		txn.GetUtr() != payRes.GetUtr() &&
		len(payRes.GetUtr()) == 22 {

		txn.Utr = payRes.GetUtr()
		updateMask = append(updateMask, paymentPb.TransactionFieldMask_UTR)

	}

	txnDetailedStatus := payPkg.GetTxnDetailedStatusFundTransfer(
		detailState,
		payRes.GetStatusCode(),
		payRes.GetStatusDescriptionPayer(),
		payRes.GetStatusDescriptionPayee(),
		payRes.GetRawResponseCode(),
		payRes.GetRawResponseDescription(),
		paymentPb.TransactionDetailedStatus_DetailedStatus_FUND_TRANSFER_PAYMENT_ENQUIRY,
		errorCategory,
		"",
		"",
	)
	txn.AddDetailedStatus(txnDetailedStatus)
	updateMask = append(updateMask, paymentPb.TransactionFieldMask_DETAILED_STATUS)
	return updateMask, nil
}

// getUPIPaymentStatus checks payment status of UPI payment and map it to respective transaction status
//
//nolint:funlen
func (s *Service) getUPIPaymentStatus(ctx context.Context, actorId string, txn *paymentPb.Transaction,
	reqInfo *paymentPb.PaymentRequestInformation, isLastAttempt bool) ([]paymentPb.TransactionFieldMask, error) {
	var (
		updateMask        []paymentPb.TransactionFieldMask
		payerResponseCode string
		payeeResponseCode string
		payerReversalCode string
		payeeReversalCode string
		detailState       paymentPb.TransactionDetailedStatus_DetailedStatus_State
		errorCategory     paymentPb.TransactionDetailedStatus_DetailedStatus_ErrorCategory
	)

	// check if the collect request has already expired, then we should not call checkTxnStatus and move the transaction to expired
	// All below conditions should meet before marking any P2P Collect transaction in Expired state
	// 1. if the transaction is not debited, i.e if money has been debited we cant mark as expired
	// 2. if the transaction is not deemed, i.e if transaction is in Deemed you can't mark it as expired
	// 3. if the time elapsed since the transaction was initiated is greater than the expireAfter time
	if reqInfo.GetUpiInfo().GetTransactionType() == upiPb.TransactionType_COLLECT && txn.GetDebitedAt() == nil && txn.GetLatestTxnDetailedStatus().GetState() != paymentPb.TransactionDetailedStatus_DetailedStatus_DEEMED &&
		uint32(math.Floor(time.Since(reqInfo.GetUpiInfo().GetTxnOriginTimestamp().AsTime()).Minutes())) > reqInfo.GetUpiInfo().GetRules().GetExpireAfter() {
		logger.Info(ctx, "collect request already expired", zap.String(logger.TXN_ID, reqInfo.GetReqId()))
		updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_EXPIRED, paymentPb.TransactionFieldMask_STATUS, updateMask)
		orderRes, err := s.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: txn.GetOrderId()}})
		if err := epifigrpc.RPCError(orderRes, err); err != nil {
			logger.Error(ctx, "failed to get order by id", zap.Error(err))
		}
		// nocustomlint:goroutine
		go s.EventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewCollectRequestExpired(time.Now(), actorId,
			txn.GetOrderId(), s.getPayerType(ctx, txn), 1, orderRes.GetOrder().GetCreatedAt().AsTime()))
		return updateMask, nil
	}

	// Skip vendor API call for DEEMED transactions
	// Context: For DEEMED transactions, we want to avoid hitting the vendor (VG) ReqCheckTxnStatus API, and we will be relying on callbacks only,
	// and in cases when we do not receive callbacks we will move txns to SUCCESS after SLA (~5 days)
	if txn.IsDeemedTransaction() {
		logger.Info(ctx, "Skipping vendor API call for DEEMED txn, returning transient error",
			zap.String(logger.TXN_ID, txn.GetId()), zap.String(logger.ORDER_ID, txn.GetOrderId()))
		return nil, epifierrors.ErrTransient
	}

	internalVpa, err := s.getInternalVpaForUpiTxn(ctx, txn)
	if err != nil {
		return nil, fmt.Errorf("error fetching internal vpa for txn: %w", err)
	}
	txnHeader := &vgUPIPb.TransactionHeader{}
	err = upiPkg.PopulateTransactionHeader(txnHeader, txn.GetPartnerBank(), txn.GetRemarks())
	if err != nil {
		return nil, fmt.Errorf("unable to populate txn header: %v : %w", err, queue.ErrTransient)
	}
	txnHeader.InitiationMode = reqInfo.GetUpiInfo().GetInitiationMode()
	txnHeader.Purpose = reqInfo.GetUpiInfo().GetPurpose()

	payReq := &vgUPIPb.ReqCheckTxnStatusRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: txn.GetPartnerBank(),
		},
		TxnHeader:        txnHeader,
		SubType:          upiTxnTypeToChkTxnStatusTypeMap[reqInfo.GetUpiInfo().GetTransactionType()],
		OrgTxnId:         reqInfo.GetReqId(),
		OrgTxnDate:       reqInfo.GetUpiInfo().GetTxnOriginTimestamp(),
		EpifiCustomerVpa: internalVpa,
		ActorId:          actorId,
	}

	payRes, err := s.vgUPIClient.ReqCheckTxnStatus(ctx, payReq)
	if err != nil {
		txnDetailedStatus := payPkg.GetTxnDetailedStatusForSystemErr(err, paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY)
		txn.AddDetailedStatus(txnDetailedStatus)
		updateMask = append(updateMask, paymentPb.TransactionFieldMask_DETAILED_STATUS)
		updateErr := s.dao.Update(ctx, txn, reqInfo, updateMask)
		if updateErr != nil {
			logger.Error(ctx, "error updating transaction", zap.Error(updateErr))
		}
		publishErr := payPkg.PublishTxnDetailedStatusUpdateEventIfRequired(ctx, txn, s.txnDetailedStatusUpdateSnsPublisher, updateMask, false)
		if publishErr != nil {
			logger.Error(ctx, "failed to publish event to txn detailed status update topic", zap.Error(publishErr))
		}

		// nocustomlint:goroutine
		go s.EventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), upiEvents.NewChkTxnStatusTriggered(time.Now(), epificontext.ActorIdFromContext(ctx), upiEvents.Payee, txn.GetId(), reqInfo.GetReqId(), txn.GetAmount(),
			txn.GetPaymentProtocol(), upiEvents.Failure, txnDetailedStatus.GetRawStatusCode(), txnDetailedStatus.GetRawStatusDescription(),
			txnDetailedStatus.GetSystemErrorDescription(), txnDetailedStatus.GetStatusCodePayer(), txnDetailedStatus.GetStatusCodePayee(), txnDetailedStatus.GetStatusDescriptionPayer(), txnDetailedStatus.GetStatusDescriptionPayee(), s.config.Secrets().Ids[cfg.EventSalt],
			s.config.OrderEventAmountCategories()))
		if epificontext.SecondaryActorIdFromContext(ctx) != epificontext.UnknownId {
			// nocustomlint:goroutine
			go s.EventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), upiEvents.NewChkTxnStatusTriggered(time.Now(), epificontext.SecondaryActorIdFromContext(ctx), upiEvents.Payee, txn.GetId(), reqInfo.GetReqId(), txn.GetAmount(),
				txn.GetPaymentProtocol(), upiEvents.Failure, txnDetailedStatus.GetRawStatusCode(), txnDetailedStatus.GetRawStatusDescription(),
				txnDetailedStatus.GetSystemErrorDescription(), txnDetailedStatus.GetStatusCodePayer(), txnDetailedStatus.GetStatusCodePayee(), txnDetailedStatus.GetStatusDescriptionPayer(), txnDetailedStatus.GetStatusDescriptionPayee(), s.config.Secrets().Ids[cfg.EventSalt],
				s.config.OrderEventAmountCategories()))
		}

		return nil, fmt.Errorf("rpc call for check upi payment status failed: %v: %w", err.Error(), queue.ErrRPC)
	}

	switch {
	case payRes.GetStatus().IsSuccess():
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS
		updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_SUCCESS, paymentPb.TransactionFieldMask_STATUS, updateMask)
	case payRes.GetStatus().IsInProgress():
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS
		updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_IN_PROGRESS, paymentPb.TransactionFieldMask_STATUS, updateMask)
	case payRes.GetStatus().GetCode() == uint32(vgUPIPb.ReqCheckTxnStatusResponse_BUSINESS_FAILURE):
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE
		updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_FAILED, paymentPb.TransactionFieldMask_STATUS, updateMask)
		errorCategory = paymentPb.TransactionDetailedStatus_DetailedStatus_USER
	case payRes.GetStatus().GetCode() == uint32(vgUPIPb.ReqCheckTxnStatusResponse_FAILED),
		payRes.GetStatus().GetCode() == uint32(vgUPIPb.ReqCheckTxnStatusResponse_TECHNICAL_FAILURE),
		payRes.GetStatus().GetCode() == uint32(vgUPIPb.ReqCheckTxnStatusResponse_DEBIT_TIMED_OUT),
		payRes.GetStatus().GetCode() == uint32(vgUPIPb.ReqCheckTxnStatusResponse_TRANSACTION_LIMIT_REACHED_BY_REMITTER),
		payRes.GetStatus().GetCode() == uint32(vgUPIPb.ReqCheckTxnStatusResponse_AMOUNT_OR_TRANSACTION_ID_MISMATCH),
		payRes.GetStatus().GetCode() == uint32(vgUPIPb.ReqCheckTxnStatusResponse_PAYER_PAYEE_IFSC_MATCH),
		payRes.GetStatus().GetCode() == uint32(vgUPIPb.ReqCheckTxnStatusResponse_PSP_TIMEOUT):
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE
		updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_FAILED, paymentPb.TransactionFieldMask_STATUS, updateMask)
		errorCategory = paymentPb.TransactionDetailedStatus_DetailedStatus_SERVER
	case payRes.GetStatus().GetCode() == uint32(vgUPIPb.ReqCheckTxnStatusResponse_DEEMED):
		txnStatus := paymentPb.TransactionStatus_IN_PROGRESS
		isEnquiryDurationOver, err := payPkg.IsDeemedTransactionEnquiryDurationOver(ctx, txn, s.piClient, s.config.PaymentEnquiryParams())
		if err != nil {
			return nil, fmt.Errorf("error while checking if enquiry duration for deemed transaction is over %w", err)
		}
		if isEnquiryDurationOver {
			// we update txn status to success if enquiry duration for deemed transaction is over
			txnStatus = paymentPb.TransactionStatus_SUCCESS
		}
		updateMask = txn.SafeUpdateField(txnStatus, paymentPb.TransactionFieldMask_STATUS, updateMask)
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_DEEMED
	case payRes.GetStatus().IsRecordNotFound():
		if payPkg.ShouldRetryForRecordNotFound(s.config.PaymentEnquiryParams(), txn) {
			updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_UNKNOWN, paymentPb.TransactionFieldMask_STATUS, updateMask)
			detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_UNKNOWN
		} else {
			updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_FAILED, paymentPb.TransactionFieldMask_STATUS, updateMask)
			detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE
		}
	case payRes.GetStatus().IsUnknown():
		updateMask = txn.SafeUpdateField(paymentPb.TransactionStatus_UNKNOWN, paymentPb.TransactionFieldMask_STATUS, updateMask)
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_UNKNOWN
	default:
		return nil, fmt.Errorf("ReqCheckTxnStatus RPC returned unexpected status: %s: %w", payRes.GetStatus(), queue.ErrTransient)
	}

	if paymentPb.IsTerminalState(txn.GetStatus()) {
		if !payRes.GetOriginalTransactionDate().AsTime().IsZero() && payRes.GetOriginalTransactionDate() != nil {
			txn.PartnerExecutedAt = payRes.GetOriginalTransactionDate()
			updateMask = append(updateMask, paymentPb.TransactionFieldMask_PARTNER_EXECUTED_AT)
		}
	}

	for _, ref := range payRes.GetRef() {
		switch ref.GetType() {
		case vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYER:
			payerResponseCode = ref.GetRespCode()
		case vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYEE:
			payeeResponseCode = ref.GetRespCode()
		}
	}

	logger.Info(ctx, "check txn status", zap.String("txn-id", txn.Id),
		zap.Any("response", payRes.GetRawStatusCode()))

	updateMask = txn.SafeUpdateField(payRes.GetCustRefId(), paymentPb.TransactionFieldMask_UTR, updateMask)

	txnDetailedStatus := upiPkg.GetTxnDetailedStatus(
		detailState,
		payRes.GetRawStatusCode(),
		payRes.GetRawStatusDescription(),
		payerResponseCode,
		payeeResponseCode,
		payerReversalCode,
		payeeReversalCode,
		payRes.GetStatusCodePayer(),
		payRes.GetStatusCodePayee(),
		payRes.GetStatusDescriptionPayer(),
		payRes.GetStatusDescriptionPayee(),
		paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
		errorCategory,
	)

	// nocustomlint:goroutine
	go s.EventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), upiEvents.NewChkTxnStatusTriggered(time.Now(), epificontext.ActorIdFromContext(ctx), upiEvents.Payer, txn.GetId(), reqInfo.GetReqId(), txn.GetAmount(),
		txn.GetPaymentProtocol(), upiEvents.Success, txnDetailedStatus.GetRawStatusCode(), txnDetailedStatus.GetRawStatusDescription(),
		txnDetailedStatus.GetSystemErrorDescription(), txnDetailedStatus.GetStatusCodePayer(), txnDetailedStatus.GetStatusCodePayee(), txnDetailedStatus.GetStatusDescriptionPayer(), txnDetailedStatus.GetStatusDescriptionPayee(), s.config.Secrets().Ids[cfg.EventSalt],
		s.config.OrderEventAmountCategories()))
	if epificontext.SecondaryActorIdFromContext(ctx) != epificontext.UnknownId {
		// nocustomlint:goroutine
		go s.EventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), upiEvents.NewChkTxnStatusTriggered(time.Now(), epificontext.SecondaryActorIdFromContext(ctx), upiEvents.Payee, txn.GetId(), reqInfo.GetReqId(), txn.GetAmount(),
			txn.GetPaymentProtocol(), upiEvents.Success, txnDetailedStatus.GetRawStatusCode(), txnDetailedStatus.GetRawStatusDescription(),
			txnDetailedStatus.GetSystemErrorDescription(), txnDetailedStatus.GetStatusCodePayer(), txnDetailedStatus.GetStatusCodePayee(), txnDetailedStatus.GetStatusDescriptionPayer(), txnDetailedStatus.GetStatusDescriptionPayee(), s.config.Secrets().Ids[cfg.EventSalt],
			s.config.OrderEventAmountCategories()))
	}

	txn.AddDetailedStatus(txnDetailedStatus)
	updateMask = append(updateMask, paymentPb.TransactionFieldMask_DETAILED_STATUS)
	return updateMask, nil
}

// publishToDeemedTxnsPaymentEnquiryQueue builds payment orchestration queue and pushes the packet to the enquiry queues
// based on the transaction protocol
func (s *Service) publishToDeemedTxnsPaymentEnquiryQueue(
	ctx context.Context,
	txn *paymentPb.Transaction,
	remitterPhoneNumber string,
	actorId, customerId string,
) error {
	payload := &paymentPb.ProcessPaymentRequest{
		TransactionId: txn.Id,
		PhoneNumber:   remitterPhoneNumber,
		ActorId:       actorId,
		CustomerId:    customerId,
	}

	_, errPublish := s.DeemedTransactionUPIEnquiryPublisher.Publish(ctx, payload)
	if errPublish != nil {
		return fmt.Errorf("failed to publish transaction packet for a deemed txn to deemed-payment-upi-enquiry-queue: %w", errPublish)
	}

	// TODO(Sidhant https://monorail.pointz.in/p/fi-app/issues/detail?id=58860): Remove this once the long term fix for tracking transaction updates is implemented.
	// The following is a temporary hot-fix to enable tickets to be created for deemed UPI transactions
	// This is needed because when a transaction goes to deemed, currently the order status does not change and hence
	// no packet is published to order-update-topic. Since we are using order update topic to create tickets,
	// we have temporarily decided to publish order with txn to this queue when a transaction moves to deemed
	getOrderWithTxnRes, err := s.orderClient.GetOrderWithTransactions(ctx, &orderPb.GetOrderWithTransactionsRequest{OrderId: txn.GetOrderId()})
	if errPublish = epifigrpc.RPCError(getOrderWithTxnRes, err); errPublish != nil {
		return fmt.Errorf("failed to fetch orderWithTxn for a deemed txn: %w", errPublish)
	}

	_, errPublish = payPkg.PublishOrderUpdate(ctx, s.orderUpdatePublisher, getOrderWithTxnRes.GetOrderWithTransactions(), s.piClient)
	if errPublish != nil {
		return fmt.Errorf("failed to publish transaction packet for a deemed txn to order-update-topic: %w", errPublish)
	}

	return nil
}

// publishToOrderQueue publishes event to the order queue.
func (s *Service) publishToOrderQueue(ctx context.Context, txn *paymentPb.Transaction, actorId string) error {
	orderId, err := s.dao.GetOrderId(ctx, txn.GetId())
	if err != nil {
		return fmt.Errorf("order id not found for transaction")
	}

	payload := &orderPb.ProcessOrderRequest{
		OrderId: orderId,
	}

	_, err = s.orderOrchestrationPublisher.Publish(ctx, payload)
	if err != nil {
		// TODO(nitesh): figure out what to in case this fails
		return fmt.Errorf("failed to publish to the order queue")
	}

	if txn.GetStatus() == paymentPb.TransactionStatus_SUCCESS {
		// nocustomlint:goroutine
		go s.EventBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
			orderEvent.NewDebitTransactions(actorId, actorId, txn.GetId(), txn.GetPaymentProtocol().String(),
				txn.GetStatus().String(), txn.GetExecutionTS().AsTime()))
	}
	return nil
}

// getDeviceAuthDetails fetches device details for a given actorId
func (s *Service) getDeviceAuthDetails(ctx context.Context, currentActorId string) (deviceId, deviceToken, userProfileId string, err error) {
	authResp, err := s.authClient.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{
		ActorId: currentActorId,
	})
	switch {
	case err != nil:
		return "", "", "", fmt.Errorf("authClient.GetDeviceAuth() failed: %v : %w", err.Error(), queue.ErrTransient)

	case !authResp.GetStatus().IsSuccess():
		return "", "", "", fmt.Errorf("authClient.GetDeviceAuth() returned non ok status: %v : %w", authResp.GetStatus(), queue.ErrTransient)
	default:
		return authResp.GetDevice().GetDeviceId(), authResp.GetDeviceToken(),
			authResp.GetUserProfileId(), nil
	}
}

// getInternalVpaForUpiTxn fetches both the pis involved in a transaction and returns
// the vpa corresponding to any of the internal pi
func (s *Service) getInternalVpaForUpiTxn(ctx context.Context, txn *paymentPb.Transaction) (string, error) {
	piRes, err := s.piClient.GetPIsByIds(ctx, &piPb.GetPIsByIdsRequest{Ids: []string{
		txn.GetPiTo(),
		txn.GetPiFrom(),
	}})
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return "", fmt.Errorf("GetPiById() failed with error: %v, queueErr: %w", err, queue.ErrPermanent)
	case err != nil:
		return "", fmt.Errorf("GetPiById() failed with error: %v, queueErr: %w", err, queue.ErrTransient)
	case piRes.GetStatus().IsRecordNotFound():
		return "", fmt.Errorf("no pi found queueErr: %w", queue.ErrPermanent)
	case !piRes.GetStatus().IsSuccess():
		return "", fmt.Errorf("GetPiById() rpc failed, queueErr: %w", queue.ErrTransient)
	}
	// if there are two internal vpa we will send any one of them
	// internal vpa are used to determine the orgId for NPCI requests
	// if both the vpa are internal, orgId corresponding to anyone of them can be used
	for _, pi := range piRes.GetPaymentinstruments() {
		if pi.IsIssuedInternally() {
			return pi.GetVPA()
		}
	}
	return "", fmt.Errorf("no internal Pis associated with the txn: %s, queueErr: %w", txn.GetId(), queue.ErrPermanent)
}

// markManualIntervention marks the txn as manual intervention and return queue consumption status
func markManualIntervention(ctx context.Context, txn *paymentPb.Transaction, txnDao dao.TransactionDao, piClient piPb.PiClient) queuePb.MessageConsumptionStatus {
	return markTransactionStatus(ctx, txn, txnDao, piClient, paymentPb.TransactionStatus_MANUAL_INTERVENTION)
}

// markSuccess marks the txn as SUCCESS and return queue consumption status
// NOTE - Currently we are using this to move DEEMED transactions to SUCCESS if queue retries are exhausted.
func markSuccess(ctx context.Context, txn *paymentPb.Transaction, txnDao dao.TransactionDao, piClient piPb.PiClient) queuePb.MessageConsumptionStatus {
	return markTransactionStatus(ctx, txn, txnDao, piClient, paymentPb.TransactionStatus_SUCCESS)
}

func markTransactionStatus(ctx context.Context, txn *paymentPb.Transaction, txnDao dao.TransactionDao, piClient piPb.PiClient, finalTxnStatus paymentPb.TransactionStatus) queuePb.MessageConsumptionStatus {
	if txn == nil {
		return queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
	}
	logger.WarnWithCtx(ctx, "transaction callback processing max attempts exhausted moving to:",
		zap.String(logger.TXN_STATUS, finalTxnStatus.String()),
		zap.String(logger.TXN_ID, txn.GetId()), zap.String(logger.PAYMENT_PROTOCOL, txn.GetPaymentProtocol().String()))

	err := txnDao.Update(ctx, &paymentPb.Transaction{Id: txn.GetId(), Status: finalTxnStatus}, nil,
		[]paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS})
	if err != nil {
		logger.Error(ctx, "failed to update txn status to:",
			zap.String(logger.TXN_STATUS, finalTxnStatus.String()),
			zap.String(logger.TXN_ID, txn.GetId()),
			zap.Error(err))
		return queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
	}

	bankName, _ := payPkgTxns.GetBankNameForPiId(ctx, txn.GetPiFrom(), piClient)
	metrics.IncrementOnAppTxnStatusCount(finalTxnStatus, txn.GetPaymentProtocol(), bankName)

	if queueStatus, ok := updatedTxnStatusToQueueStatusMap[finalTxnStatus]; ok {
		return queueStatus
	}
	logger.Error(ctx, "Unsupported transaction status for update in markTransactionStatus",
		zap.String("txn_id", txn.GetId()), zap.String("status", finalTxnStatus.String()))
	return queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
}

func (s *Service) getPayerType(ctx context.Context, txn *paymentPb.Transaction) string {
	piId := txn.GetPiFrom()
	piRes, err := s.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{Id: piId})
	if err = epifigrpc.RPCError(piRes, err); err != nil {
		logger.Error(ctx, "failed to get pi response from piId", zap.Error(err))
		return ""
	}
	return piRes.GetPaymentInstrument().GetIssuerClassification().String()
}

func getNewUpiTransactionEvent(actorId string, txn *paymentPb.Transaction, reqInfo *paymentPb.PaymentRequestInformation) (*orderEvent.UpiTransactionEvent, error) {
	var (
		amountInPaisa int64
		err           error
	)
	if amountInPaisa, err = money.ToPaise(txn.GetAmount()); err != nil {
		return nil, fmt.Errorf("error converting money to paise: err = %w", err)
	}
	isDeemedTxn := false
	detailedStatusList := txn.GetLatestTxnDetailedStatus()
	if detailedStatusList.GetState() == paymentPb.TransactionDetailedStatus_DetailedStatus_DEEMED {
		isDeemedTxn = true
	}
	errCode := detailedStatusList.GetRawStatusCode()
	// ElapsedTime is the time to reach current transaction status from the time of creation
	elaspedTime := txn.GetUpdatedAt().AsTime().Sub(txn.GetCreatedAt().AsTime())
	return orderEvent.NewUpiTransactionEvent(actorId, txn.GetStatus().String(),
		reqInfo.GetUpiInfo().GetPayerVpa(), reqInfo.GetUpiInfo().GetPayeeVpa(), strconv.FormatInt(amountInPaisa, 10), reqInfo.GetUpiInfo().GetMerchantDetails().GetBrandName(),
		reqInfo.GetUpiInfo().GetMcc(), txn.GetRemarks(), errCode, "", "", isDeemedTxn, elaspedTime), nil
}

// isImpsDeemedHandlingEnabled : checks if imps deemed handling is enabled for the actor.
func (s *Service) isImpsDeemedHandlingEnabled(ctx context.Context, actorId string) bool {
	isEnabled, err := s.evaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_IMPS_DEEMED_HANDLING).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error while checking if imps deemed handling is enabled for the actor",
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Error(err))
		return false
	}
	return isEnabled
}
