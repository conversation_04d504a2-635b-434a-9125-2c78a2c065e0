Flags:
  TrimDebugMessageFromStatus: true
  IsFetchFaqV2DaoEnabled: true
  IsTrendingFaqSectionEnabledForFiLiteUsers: false
  IsTrendingFaqSectionEnabledForNonResidentUsers: false
  EnablePopulateChatHistoryForFreshChat: true

FaqArticleFeedbackConf:
  BottomSheetTitle: "Tell us how we can improve"
  BottomSheetOptions: [
      "I could not clearly understand this FAQ",
      "I prefer in-depth/detailed descriptions",
      "I want images/videos along with the text",
      "I felt the information provided was incorrect",
  ]
  BottomSheetActionDisplayValue: "SUBMIT"
  ReasonOptionsMap:
    ReasonOption_FAQ_NOT_CLEARLY_UNDERSTANDABLE: "I could not clearly understand this FAQ"
    ReasonOption_IN_DEPTH_DETAILED_DESCRIPTION_PREFERRED: "I prefer in-depth/detailed descriptions"
    ReasonOption_IMAGES_VIDEO_ALONG_WITH_THE_TEXT_NEEDED: "I want images/videos along with the text"
    ReasonOption_INFORMATION_PROVIDED_WAS_INCORRECT: "I felt the information provided was incorrect"
    ReasonOption_OTHERS: "Others"

InAppFeedbackConfig:
  DefaultMaxThreshold: 1
  DefaultQuestionsList:
    - QuestionCode: "terminal_q_2"
      ActualQuestion: "WHAT CAN WE DO TO IMPROVE?"
      AnswerDataType: "ANSWER_DATA_TYPE_TEXT"
      IsOptional: false
  FeedbackSurveyFlowToQuestionInfoMap:
    # this is placeholder only. have to remove feedback_survey_flow_insights when start using feedback survey flow
    feedback_survey_flow_insights:
      - QuestionCode: "screener_terminal_q_2"
        ActualQuestion: "WHAT CAN WE DO TO IMPROVE?"
        AnswerDataType: "ANSWER_DATA_TYPE_TEXT"
        IsOptional: false
    feedback_survey_flow_analyser:
      - QuestionCode: "analyser_feedback_q_11"
        ActualQuestion: "Is this what you were looking for?"
        AnswerDataType: "ANSWER_DATA_TYPE_THUMBS_UP_THUMBS_DOWN"
        IsOptional: false
  ScreenToQuestionInfoMap:
    app_screen_screener_terminal:
      - QuestionCode: "screener_terminal_q_2"
        ActualQuestion: "WHAT CAN WE DO TO IMPROVE?"
        AnswerDataType: "ANSWER_DATA_TYPE_TEXT"
        IsOptional: false
    app_screen_onboarding_terminal:
      - QuestionCode: "onboarding_terminal_q_2"
        ActualQuestion: "WHAT CAN WE DO TO IMPROVE?"
        AnswerDataType: "ANSWER_DATA_TYPE_TEXT"
        IsOptional: false
    app_screen_onboarding_dedupe_check_terminal:
      - QuestionCode: "onboarding_dedupe_terminal_q_2"
        ActualQuestion: "WHAT CAN WE DO TO IMPROVE?"
        AnswerDataType: "ANSWER_DATA_TYPE_TEXT"
        IsOptional: false
    app_screen_onboarding_pan_name_mismatch_rejection:
      - QuestionCode: "onboarding_pan_name_mismatch_rejection_q_2"
        ActualQuestion: "WHAT CAN WE DO TO IMPROVE?"
        AnswerDataType: "ANSWER_DATA_TYPE_TEXT"
        IsOptional: false
    app_screen_sd_preclosure:
      - QuestionCode: "screen_sd_preclosure_q_2"
        ActualQuestion: "Tell us why you started this deposit"
        AnswerDataType: "ANSWER_DATA_TYPE_CHECKBOXES"
        IsOptional: false
        AnswerOptions: [
          "I wanted to avoid spending this money",
          "I wanted to save a target amount",
          "I was saving for a purchase",
          "I wanted to earn higher interest than my savings account",
          "I was just exploring the feature",
          "I wanted to try FIT Rules",
          "Other",
        ]
        CheckBoxOptions:
          MultiSelectionAllowed: false
          AddAdditionalAnswer: true
    app_screen_rewards_sd_preclosure:
      - QuestionCode: "screen_rewards_sd_preclosure_q_2"
        ActualQuestion: "How can we improve this feature for you?"
        AnswerDataType: "ANSWER_DATA_TYPE_CHECKBOXES"
        IsOptional: false
        AnswerOptions: [
          "Let me set a target amount and date for my goals",
          "Remind me to continue saving",
          "Give me higher interest rates",
          "Have a shorter maturity period",
          "Allow me to add money via other payment methods",
          "Other",
        ]
        CheckBoxOptions:
          MultiSelectionAllowed: true
          AddAdditionalAnswer: true
    app_screen_pan_dob_drop_off:
      - QuestionCode: "screen_pan_dob_drop_off_q_1"
        ActualQuestion: "Reasons for leaving"
        AnswerDataType: "ANSWER_DATA_TYPE_CHECKBOXES"
        IsOptional: false
        AnswerOptions: [
            "Just trying Fi out",
            "I don't remember my PAN number",
            "I don't have a PAN",
            "I don't trust Fi with PAN number",
            "Other",
        ]
        CheckBoxOptions:
          MultiSelectionAllowed: true
          AddAdditionalAnswer: true

InAppHelpMediaConfig:
  UIContextToFiLiteVisibilityMapping:
    ui_context_help_home_popular_stories: false
    ui_context_faq_category_stories: false
    ui_context_txn_receipt_stories: true
    ui_context_mutual_fund_stories: true
    ui_context_us_stocks_landing_page_stories: true
    ui_context_investment_landing_page_stories: true


Tracing:
  Enable: false

FeedbackEngineConfig:
  GlobalCoolOffDurationForAllSurveys: "2m"
  QuestionIdToFeedbackSubscriberServiceNameMap:
    # in app help CSAT scale question subscription
    92e92154-b3d0-4539-a965-f2a729dcc48d : "CX_SERVICE"

FeedbackEngineWebConfig:
  AllSurveysTableHeaderList: [
    "Survey Id",
    "Survey Name",
    "Created By",
    "Approved By",
    "Approval Status",
    "Owner Service",
    "Updated At",
    "Action",
  ]
  EntityApprovalsTableHeaderList: [
    "Entity Id",
    "Entity Type",
    "Creator",
    "Approver",
    "Approval Status",
    "Updated At",
    "Action"
  ]
  SurveysRunningOnAnalyticsScreenNameTableHeaderList: [
    "Survey Id",
    "Survey Name",
    "Created By",
    "Approved By",
    "Approval Status",
    "Owner Service",
    "Updated At",
    "Action",
  ]

IssueReportingConfig:
  IsJsonFormatContextEnabled: false
  ContextTypeToConfigMap:
    USER_CONTEXT_TYPE_RECENT_ACTIVITY:
      ContextInitTemplate: "{#name#} has performed the following activities on Fi-App: \n"
      Template: "{#activity_type#} on Fi-App at {#timestamp#} additional details for activities are: {#activity_details#}"
      MaxNumberOfItemsToBeFetched: 10
      MaxNumberOfItemsInPrompt: 3
    USER_CONTEXT_TYPE_SUPPORT_TICKET:
      ContextInitTemplate: "{#name#} has following support tickets raised: \n"
      Template: "ID: {#ticket_id#} for issue related to {#issue_description#} created on {#timestamp#} and currently is in {#status#}"
      MaxNumberOfItemsToBeFetched: 10
      MaxNumberOfItemsInPrompt: 3
    USER_CONTEXT_TYPE_WATSON_INCIDENT:
      ContextInitTemplate: "{#name#} has faced following issues auto-detected by our system: \n"
      Template: "Issue related to {#issue_description#} at {#timestamp#}, an automatic support ticket has been created with ID: {#ticket_id#}"
      MaxNumberOfItemsToBeFetched: 10
      MaxNumberOfItemsInPrompt: 3
  CustomerMasterRecordRedisKeyPrefix: "cx_customer_360:"
  SearchHistoryConfig:
    IsSearchHistoryRequired: true
    SearchHistorySize: 7 # to be updated after discussion with product
    SearchHistoryRedisKeyPrefix: "inapphelp:iss_hist:"
    ValidityDuration: "2160h" # 24 * 30 * 3 = 3 months, duration provided by product
  IsTrendingIssueRequired: false
  TrendingIssueList: # trending issues and their categories is decided by CX OPS team
    - UserQuery: "Why is it important to link my Aadhaar and PAN?"
      IssueCategory:
        ProductCategory: "General enquiries about Fi"
        ProductCategoryDetails: "About Fi"
        SubCategory: "Info given"
      ModelResponse: "<html>  Linking your Aadhaar and PAN to your Fi account is important for account verification and compliance with regulatory requirements. We partner with Federal Bank (Licensed Bank) to provide you this product. Linking your Aadhaar to your phone number is necessary for Aadhaar OTP verification during the signup process, as per UIDAI guidelines.  </html> "
    - UserQuery: "Will my existing PIN and account details remain the same with new Physical debit card?"
      IssueCategory:
        ProductCategory: "Debit Card"
        ProductCategoryDetails: "Card Details"
        SubCategory: "Enquiry"
      ModelResponse: "<html>  It's a co-branded card wherein Federal Bank is the issuer of the card and Fi Brand Private Limited is the co-branded partner. Your existing PIN and account details will remain the same when you receive your new physical card. 😊  </html>"
    - UserQuery: "How do I reset my debit card PIN?"
      IssueCategory:
        ProductCategory: "Debit Card"
        ProductCategoryDetails: "Reset PIN"
        SubCategory: "Forgot ATM PIN"
      ModelResponse: "<html>  <p>You can reset your Fi-Federal Debit Card PIN through the Fi app. It's a co-branded card wherein Federal Bank is the issuer of the card and Fi Brand Private Limited is the co-branded partner.</p>  <p>To reset your debit card PIN, please follow these steps:</p>  <ol>  <li>Go to 'Card Settings' on the Debit Card home screen.</li>  <li>Select 'Reset ATM PIN'.</li>  <li>Follow the on-screen instructions, including a short video recording (for identity verification).</li>  </ol>  </html> "
    - UserQuery: "How do I request a new physical debit card?"
      IssueCategory:
        ProductCategory: "Debit Card"
        ProductCategoryDetails: "Card Details"
        SubCategory: "Enquiry"
      ModelResponse: "<html>  Hello! You can easily request a new <b>Fi-Federal Debit Card</b> directly through the Fi app. Here's how:  <br>  <ol>  <li>Go to the 'Request New Card' section in the Fi app.</li>  <li>Confirm your address information.</li>  <li>You will be informed of any applicable charges based on your Fi plan.</li>  <li>Follow the on-screen instructions to complete the process.</li>  </ol>  Your new debit card will be delivered within 7-14 working days. You may receive it sooner if you are located in a metropolitan area.  <br>  Please note that as a Standard tier user, there is a fee of ₹499 + 18% GST for a new debit card.  <br>  Let me know if you have any other questions.  </html>"
    - UserQuery: "How do I set up my UPI PIN for the first time?"
      IssueCategory:
        ProductCategory: "Accounts"
        ProductCategoryDetails: "PIN"
        SubCategory: "UPI PIN"
      ModelResponse: "<html> <body><p> Hello! It seems you're setting up your UPI PIN for the first time. To do this, you'd usually follow the instructions during the initial setup of your Federal Bank Savings Account linked to the Fi app. However, if you skipped that step, you can easily set it up now. You can set or change your UPI PIN by going to the UPI section within your Federal Bank Savings Account on the Fi app. Select 'Set UPI PIN' and follow the on-screen instructions. You will need your ATM PIN handy to complete the verification. Let me know if you have any other questions.</p></body></html>"
    - UserQuery: "Can I reapply for an instant loan right after being rejected?"
      IssueCategory:
        ProductCategory: "Instant Loans"
        ProductCategoryDetails: "General Enquiry"
        SubCategory: "Instant loans"
      ModelResponse: "<html> <body><p> Hello! I understand you are keen to reapply for an instant loan. However, if your application was recently rejected, we advise you to wait for a while before reapplying. Reapplying immediately doesn't guarantee approval, as the eligibility criteria are determined by our regulated entity partners like Federal Bank, LiquiLoans, IDFC, ABFL, Moneyview (as DSA) and are based on factors like your CIBIL score and app usage. We recommend focusing on improving your creditworthiness and ensuring you meet the eligibility requirements before you apply again. </p></body></html>"
  IssueCategorizerModelConfig:
    CategorizeApiEndpointRoute: "/issue_categorizer"
    RequestTimeout: "5s"
    RequiredRecommendationCount: 5
    # keeping the threshold to 75% for now, will be updated after discussion with the product
    MinConfidenceScore: 75
    IsModelV2Enabled: true
    CategorizeV2ApiEndpointRoute: "/issue_categorizer_all"
    ModelV2MinConfidenceScore: 80
  MinThresholdForNumberOfEmbeddings: 300
  ModelResponseCacheKey: "cx:model_response:%s"
  AsyncModelCallConfig:
    ModelResponseByQueryIdCacheKey: "cx:model_response:query_id:%s"
    ModelResponseByQueryIdStatusCacheKey: "cx:model_response_status:query_id_status:%s"
    ModelResponseCacheTTL: "180s"
    ModelResponseCachePollingTimeout: "50s"
    ModelResponseCachePollingInterval: "1s"
  TicketCreationConfig:
    IsTicketCreationEnabled: true
    IsNonFcrIssueTicketCreationEnabled: true
    IsFcrIssueTicketCreationEnabled: true
  SuggestedIssueConfig:
    MaxNumberOfSuggestions: 7
    MinNumberOfCharacterRequiredForSuggestion: 10
    MaxNumberOfCharacterForWhichSuggestionIsRequired: 30
  SmartGptServiceConfig:
    CompletionRequestTimeout: "50s"
    CompletionApiEndpoint: "/v1/completion"
    CompletionApiRouteFreshchat: "/v1/chat"
  NonFCRIssueGenericModelResponse: "<div style=\"font-family: Arial, sans-serif; color: #333;\">
                                        <p><strong>Here :</strong></p>
                                        <p>We're available 24×7 on Fi money customer care at <strong>080-47485490</strong> or in-app chat.</p>
                                        <p><strong>Next steps:</strong></p>
                                          <ul>
                                            <li>Tell our Customer care agent what happened in full detail.</li>
                                            <li>Note down the ticket number shared by the agent.</li>
                                          </ul>
                                        <p>We will do everything we can to help you figure things out.
                                      </p>
                                    </div>"
  AIGeneratedNote: "<div style=\"font-family: Arial, sans-serif; color: #333;\"><p><em>This content is AI generated</em></p></div>"
  PriorityWiseChannelConfig:
    CriticalPriorityOptimalChannel: ["CONTACT_OPTION_CALL", "CONTACT_OPTION_CHAT"]
    HighPriorityOptimalChannel: ["CONTACT_OPTION_CALL", "CONTACT_OPTION_CHAT"]
    MediumPriorityOptimalChannel: ["CONTACT_OPTION_CHAT"]
    LowPriorityOptimalChannel: ["CONTACT_OPTION_INVESTIGATIONS_EMAIL"]
  CreditFreezeUserPriorityWiseChannelConfig:
    HighPriorityOptimalChannel: ["CONTACT_OPTION_CALL","CONTACT_OPTION_CHAT"]
    MediumPriorityOptimalChannel: ["CONTACT_OPTION_CHAT"]
    LowPriorityOptimalChannel: ["CONTACT_OPTION_INVESTIGATIONS_EMAIL"]
  IsModelEnabledForNonFcr: true
  NonFcrContextReferenceScoreThreshold: 3
  RiskCategoryContactUsConfig:
    IsRiskCategoryContactUsEnabled: true
    L1CategoryToConsider: [ "Risk","Fraud & Risk" ]
    L2CategoryPatternToConsider: [ "Freeze","Complaint","Lien","Lea" ] # L2 categories can be lot more so having pattern match as this is just for identifying
    GenericCustomerResponse: "<html> Please check your inbox for an email from us and follow the instructions in it. In cases you have already responded to our email, Please give us some time and our team will reach out to you soon. </html>"
  AskFiConfig:
    IsAskFiIntegrationLive: true
    # As response of Ask.Fi were not helpful in most of the queries,
    # we are adding this checks to ensure we only show it when It's helpful to user
    # product shared this list after analyzing Ask.Fi responses for a set of user queries
    AllowedIssueCategoryIds: [
      "839d69b0-b6a0-50f3-a968-f715adad52a3",
      "f6426fe3-0b31-518b-8620-859cead92b37",
      "36014916-e353-5f09-bdf4-03b3ce67ea09",
      "cc1b7962-a4ba-5c00-bc42-4d6dc02eca28",
      "579b38bb-9edb-5c58-8c63-4be74171177d",
      "0fd962a5-61cb-595f-82d6-b9cbadabe577",
      "a24f1eca-a019-5afd-ba47-b78a36a54765",
      "46f308e2-7aad-556f-8ed4-6b54f99ff518",
    ]
  IssuePriorityCacheConfig:
    Key: "cx:iss_prio:%s"
    Ttl: "30m"
    AllowedPriorityValues: ["ISSUE_PRIORITY_HIGH", "ISSUE_PRIORITY_CRITICAL"]
    CachingTimeout: "1s"
  UserContextConfig:
    QueryContextMandatoryColumns: ["recent_activity","recent_tickets","user_name","account_number","kyc_level","account_tier", "amplifi_card_holder", "simplifi_card_holder","magnifi_card_holder","debit_card_form","days_to_kyc_due",
    "physical_dc_ordered_flag","account_open_date", "account_freeze_status","usstocks_account_status","account_operational_status","freeze_reason_codes","rekyc_due_date","re_kyc_latest_attempt_date","re_kyc_latest_attempt_mode"]
