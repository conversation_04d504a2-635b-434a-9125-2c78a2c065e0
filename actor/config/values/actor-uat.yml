Application:
  Environment: "uat"
  Name: "actor"
  IsSecureRedis: true


Server:
  Ports:
    GrpcPort: 8092
    GrpcSecurePort: 9501
    HttpPort: 9999
    HttpPProfPort: 9990



ActorDb:
  Name: "actor"
  DbType: "PGDB"
  DbServerAlias: "PLUTUS_RDS"
  EnableDebug: false
  SSLMode: "disable"
  AppName: "actor"
  SecretName: "uat/rds/epifiplutus/actor_dev_user"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true


AWS:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: false

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 13
  HystrixCommand:
    CommandName: "actor_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80


RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "uat/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "uat/gcloud/profiling-service-account-key"

StaticExternalUserIconMap:
  "actor-epifi-business-account": "https://epifi-icons.pointz.in/fibank/icon/timeline.png"
  "actor-federal-generic-account": "https://epifi-icons.pointz.in/merchant/federalbank.png"
  "actor-icici-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/ipru_logo.png"
  "actor-idfc-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/idfc_sm.png"
  "actor-axis-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/axis_sm.png"
  "actor-absl-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/ABSL_new_logo.png"
  "actor-dsp-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/dsp_sm.png"
  "actor-liquiloans": "https://epifi-icons.pointz.in/p2pinvestment/liquiloans_logo.png"
  "actor-kotak-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/kotak_sm.png"
  "actor-hdfc-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/Hdfc.png"
  "actor-lnt-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/L%26TL%26T.png"
  "actor-tata-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/Tata.png"
  "actor-motilal-oswal-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/Motilal+oswalMOST.png"
  "actor-canararobeco-business-account": "https://epifi-icons.pointz.in/amc_logos/canara_robeco.png"
  "actor-edelweiss-business-account": "https://epifi-icons.pointz.in/amc_logos/edelweiss.png"
  "actor-lic-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/lic_amc.png"
  "actor-nippon-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/Nippon.png"
  "actor-mahindra-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/Mahindra.png"
  "actor-pgim-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/PGIM.png"
  "actor-mirae-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/Mirae.png"
  "actor-uti-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/UTI.png"
  "actor-franklin-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/Franklin.png"
  "actor-ppfas-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/PPFAS.png"
  "actor-sundaram-amc-business-account": "https://epifi-icons.pointz.in/amc_logos/Sundaram.png"

ActorPiRelationPurgeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-actor-pi-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

ActorPurgePublisher:
  QueueName: "uat-actor-purge-queue"

ActorPurgeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-actor-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

PiPurgePublisher:
  QueueName: "uat-pi-purge-queue"

ActorCacheConfig:
  IsCachingEnabled: false
  ActorIdPrefix: "actor_id_"
  ActorCacheSecondaryIdPrefix: "actor_cache_secondary_key_"
  CacheTTl: "2m"

ActorPiResolutionCacheConfig:
  IsCachingEnabled: false
  ActorPiResolutionIdPrefix: "actor_pi_resolution_id_"
  CacheTTl: "2m"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

ActorCreationEventPublisher:
  TopicName: "uat-actor-creation-event-topic"

UsePgdbConnForActorDb: true
