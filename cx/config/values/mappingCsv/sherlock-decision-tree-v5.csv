Source,Condition,Destination,Data Type,Mandatory/Optional,Dispute type(Source+condition based),Product Category,Product Category Details,Subcategory
CARD-00,ATM,ATM-01,,,,,,
ATM-01,YES,DEAD END,,,,Debit Card,ATM,Debited but not dispensed at machine
ATM-01,NO,ATM-02,,Mandatory,,,,
ATM-02,*,DEAD END,NUMBER,,,Debit Card,ATM,Debited but not dispensed at machine
CARD,AUTHORISED,CARD-00,,,,,,
CARD-00,ECOM,ECOM-01,,,,,,
ECOM-01,YES,ECOM-02,,Mandatory,,,,
ECOM-01,NO,ECOM-02,,Mandatory,,,,
ECOM-02,*,ECOM-03,TEXT,,,,,
ECOM-03,Failed transactions,ECOM-04,,,,,,
ECOM-03,Duplicate/multiple billing,ECOM-05,,Mandatory,,,,
ECOM-03,Paid by alternative means,ECOM-06,,,,,,
ECOM-03,Goods & services not delivered,ECOM-07,,Mandatory,,,,
ECOM-03,Cancellation-refund not received,ECOM-08,,,,,,
ECOM-03,Returned- refund not received,ECOM-12,,,,,,
ECOM-03,Recurring transactions,ECOM-16,,,,,,
ECOM-03,Incorrect transaction amount,ECOM-18,,Mandatory,,,,
ECOM-18,*,ECOM-19,NUMBER,Mandatory,,,,
ECOM-19,*,ECOM-17,NUMBER,,,,,
ECOM-03,Digital goods transaction,ECOM-20,,,,,,
ECOM-03,Retrieval of charge slip requested,ECOM-22,,,,,,
ECOM-22,YES,ECOM-17,,,,,,
ECOM-22,NO,ECOM-17,,,,,,
ECOM-03,Billed for not show charge,ECOM-23,,,,,,
ECOM-23,YES,ECOM-17,,,,,,
ECOM-23,NO,ECOM-17,,,,,,
ECOM-04,YES,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
ECOM-04,NO,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
ECOM-05,*,DEAD END,NUMBER,,,Debit Card,POS/ECOM,Double Debit
ECOM-06,CASH,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
ECOM-06,CHEQUE,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
ECOM-06,OTHER CARD,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
ECOM-06,E WALLET,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
ECOM-06,PAYMENT APP,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
ECOM-06,QR PAYMENT,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
ECOM-07,*,DEAD END,DATE,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
ECOM-08,YES,ECOM-09,,Mandatory,,,,
ECOM-09,*,ECOM-10,DATE,,,,,
ECOM-10,*,ECOM-11,DATE,,,,,
ECOM-08,NO,ECOM-10,,,,,,
ECOM-12,YES,ECOM-13,,Mandatory,,,,
ECOM-13,*,ECOM-14,DATE,,,,,
ECOM-14,*,ECOM-15,DATE,,,,,
ECOM-12,NO,ECOM-14,,,,,,
ECOM-16,YES,ECOM-17,,Mandatory,,,,
ECOM-16,NO,DEAD END,,,,Debit Card,POS/ECOM,"Recurring Payment cancelled, amount debited"
ECOM-17,*,DEAD END,DATE,,,Debit Card,POS/ECOM,"Recurring Payment cancelled, amount debited"
ECOM-18,*,DEAD END,NUMBER,,,Debit Card,POS/ECOM,Incorrect Amount Debited
ECOM-19,*,DEAD END,NUMBER,,,Debit Card,POS/ECOM,Incorrect Amount Debited
ECOM-20,YES,ECOM-21,,Mandatory,,,,
ECOM-20,NO,ECOM-17,,,,,,
ECOM-11,*,DEAD END,TEXT,,,Debit Card,POS/ECOM,
ECOM-15,*,DEAD END,TEXT,,,Debit Card,POS/ECOM,
ECOM-21,*,DEAD END,DATE,,,,,
IMPS,AUTHORISED,IMPS-01,,Mandatory,,,,
IMPS-01,*,IMPS-03,DATE,Mandatory,,,,
IMPS-03,YES,IMPS-04,,Mandatory,Chargeback raise outward,,,
IMPS-03,NO,IMPS-05,,Mandatory,,,,
IMPS-05,NO,IMPS-06,,,,,,
IMPS-05,YES,DEAD END,,,Not settled  at beneficiary,Transactions,Amount debited but not credited to the beneficiary,
IMPS-06,YES,DEAD END,,,No refund received,Transactions,Other transaction issues,Refund pending
IMPS-04,*,DEAD END,NUMBER,,Chargeback raise outward,Transactions,Sent to Wrong User,
UPI,AUTHORISED,UPI-10,,Mandatory,,,,
UPI-10,P2M,UPI-11,,Mandatory,,,,
UPI-10,P2P,UPI-12,,Mandatory,,,,
UPI-11,Goods/services are not provided,DEAD END,,,GOODS_NOT_PROVIDED,,,
UPI-11,Credit not processed for cancelled or returned goods & services,DEAD END,,,CREDIT_NOT_PROCESSED_FOR_CANCELLED_GOODS,,,
UPI-11,Account debited but transaction confirmation not received by merchant,DEAD END,,,TRANSACTION_CONFIRMATION_NOT_RECEIVED,,,
UPI-11,Paid by alternate means/Duplicate payment,DEAD END,,,DUPLICATE_PAYMENT,,,
UPI-11,Customer account not credited back for failed merchant transaction,DEAD END,,,ACCOUNT_NOT_CREDITED_FOR_MERCHANT_TRANSACTION,,,
UPI-12,Beneficiary account is not credited for a pending / timeout transaction,DEAD END,,,ACCOUNT_NOT_CREDITED_FOR_PENDING_TRANSACTION,,,
UPI-12,Beneficiary account is not credited for successful transaction,DEAD END,,,ACCOUNT_NOT_CREDITED_FOR_SUCCESSFUL_TRANSACTION,,,
UPI-12,Customer account not credited back for failed P2P transaction,DEAD END,,,ACCOUNT_NOT_CREDITED_FOR_P2P_TRANSACTION,,,
CARD-00,POS,POS-01,,Mandatory,,,,
POS-01,YES,POS-02,,Mandatory,,,,
POS-01,NO,POS-02,,Mandatory,,,,
POS-02,*,POS-03,TEXT,Mandatory,,,,
POS-03,Failed transactions,POS-04,,,,,,
POS-03,Duplicate/multiple billing,POS-05,,Mandatory,,,,
POS-03,Paid by alternative means,POS-06,,,,,,
POS-03,Goods & services not delivered,POS-07,,Mandatory,,,,
POS-03,Cancellation-refund not received,POS-08,,,,,,
POS-03,Returned- refund not received,POS-12,,,,,,
POS-03,Recurring transactions,POS-16,,,,,,
POS-03,Incorrect transaction amount,POS-18,,Mandatory,,,,
POS-18,*,POS-19,NUMBER,Mandatory,,,,
POS-19,*,POS-17,NUMBER,,,,,
POS-03,Digital goods transaction,POS-20,,,,,,
POS-03,Retrieval of charge slip requested,POS-22,,,,,,
POS-22,YES,POS-17,,,,,,
POS-22,NO,POS-17,,,,,,
POS-03,Billed for not show charge,POS-23,,,,,,
POS-23,YES,POS-17,,,,,,
POS-23,NO,POS-17,,,,,,
POS-04,YES,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
POS-04,NO,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
POS-05,*,DEAD END,NUMBER,,,Debit Card,POS/ECOM,Double Debit
POS-06,CASH,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
POS-06,CHEQUE,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
POS-06,OTHER CARD,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
POS-06,E WALLET,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
POS-06,PAYMENT APP,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
POS-06,QR PAYMENT,DEAD END,,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
POS-07,*,DEAD END,DATE,,,Debit Card,POS/ECOM,Debited but not credited to the merchant
POS-08,YES,POS-09,,Mandatory,,,,
POS-09,*,POS-10,DATE,,,,,
POS-10,*,POS-11,DATE,,,,,
POS-08,NO,POS-10,,,,,,
POS-12,YES,POS-13,,Mandatory,,,,
POS-13,*,POS-14,DATE,,,,,
POS-14,*,POS-15,DATE,,,,,
POS-12,NO,POS-14,,,,,,
POS-16,YES,POS-17,,Mandatory,,,,
POS-16,NO,DEAD END,,,,Debit Card,POS/ECOM,"Recurring Payment cancelled, amount debited"
POS-17,*,DEAD END,DATE,,,Debit Card,POS/ECOM,"Recurring Payment cancelled, amount debited"
POS-18,*,DEAD END,NUMBER,,,Debit Card,POS/ECOM,Incorrect Amount Debited
POS-19,*,DEAD END,NUMBER,,,Debit Card,POS/ECOM,Incorrect Amount Debited
POS-20,YES,POS-21,,Mandatory,,,,
POS-20,NO,POS-17,,,,,,
POS-11,*,DEAD END,TEXT,,,Transactions,Other transaction issues,Refund pending
POS-15,*,DEAD END,NUMBER,,,Transactions,Other transaction issues,Refund pending
POS-21,*,DEAD END,DATE,,,,,
RECYCLER,AUTHORISED,REC-01,,Mandatory,,,,
REC-01,YES,REC-02,,Mandatory,,,,
REC-01,NO,REC-02,,Mandatory,,,,
REC-02,YES,REC-03,,Mandatory,,,,
REC-02,NO,REC-03,,Mandatory,,,,
REC-03,YES,DEAD END,,,,,,
REC-03,NO,DEAD END,,,,,,
NEFT,AUTHORISED,NEFT-01,,,,,,
NEFT-01,YES,NEFT-02,,Mandatory,,,,
NEFT-01,NO,NEFT-03,,,,,,
NEFT-03,YES,DEAD END,,,,Transactions,Sent to Wrong User,
NEFT-03,NO,NEFT-04,,,,,,
NEFT-04,YES,NEFT-05,,Mandatory,,,,
NEFT-04,NO,NEFT-06,,,,,,
NEFT-06,*,DEAD END,NO_INPUT,,,Transactions,Amount debited but not credited to the beneficiary,
NEFT-02,*,DEAD END,NUMBER,,,Transactions,Sent to Wrong User,
NEFT-05,*,DEAD END,NUMBER,,,Transactions,Other transaction issues,Incorrect amount debited
RTGS,AUTHORISED,RTGS-01,,,,,,
RTGS-01,YES,RTGS-02,,Mandatory,,,,
RTGS-01,NO,RTGS-03,,,,,,
RTGS-03,YES,DEAD END,,,,Transactions,Sent to Wrong User,
RTGS-03,NO,RTGS-04,,,,,,
RTGS-04,YES,RTGS-05,,Mandatory,,,,
RTGS-04,NO,RTGS-06,,,,,,
RTGS-06,*,DEAD END,NO_INPUT,,,Transactions,Amount debited but not credited to the beneficiary,
RTGS-02,*,DEAD END,NUMBER,,,Transactions,Sent to Wrong User,
RTGS-05,*,DEAD END,NUMBER,,,Transactions,Other transaction issues,Incorrect amount debited
CARD,UNAUTHORISED,CARD-UN-00,,,,,,
CARD-UN-00,ATM,ATM-UN-01,,Mandatory,,,,
ATM-UN-01,YES,ATM-UN-02,,Mandatory,,,,
ATM-UN-01,NO,ATM-UN-02,,Mandatory,,,,
ATM-UN-02,YES,ATM-UN-03,,Mandatory,,,,
ATM-UN-02,NO,ATM-UN-04,,Mandatory,,,,
ATM-UN-04,YES,DEAD END,,,,Debit Card,ATM,
ATM-UN-04,NO,DEAD END,,,,Debit Card,ATM,
ATM-UN-03,*,DEAD END,DATE,Mandatory,,Debit Card,ATM,
CARD-UN-00,ECOM,ECOM-UN-01,,Mandatory,,,,
ECOM-UN-01,YES,ECOM-UN-02,,Mandatory,,,,
ECOM-UN-01,NO,ECOM-UN-02,,Mandatory,,,,
ECOM-UN-02,YES,ECOM-UN-03,,Mandatory,,,,
ECOM-UN-02,NO,ECOM-UN-03,,Mandatory,,,,
ECOM-UN-03,YES,ECOM-UN-04,,,,,,
ECOM-UN-03,NO,ECOM-UN-04,,,,,,
ECOM-UN-04,*,ECOM-UN-05,NUMBER,,,,,
ECOM-UN-05,*,DEAD END,TEXT,,,Transactions,Unauthorised transaction,
IMPS,UNAUTHORISED,IMPS-UN-01,,Mandatory,,,,
IMPS-UN-01,YES,IMPS-UN-02,,Mandatory,,,,
IMPS-UN-01,NO,IMPS-UN-02,,Mandatory,,,,
IMPS-UN-02,YES,IMPS-UN-04,,Mandatory,,,,
IMPS-UN-02,NO,IMPS-UN-04,,Mandatory,,,,
IMPS-UN-04,YES,IMPS-UN-05,,Mandatory,,,,
IMPS-UN-04,NO,IMPS-UN-06,,Mandatory,,,,
IMPS-UN-05,*,IMPS-UN-06,NUMBER,Mandatory,,,,
IMPS-UN-06,YES,IMPS-UN-07,,Mandatory,,,,
IMPS-UN-06,NO,IMPS-UN-07,,Mandatory,,,,
IMPS-UN-07,YES,DEAD END,,,,Transactions,Unauthorised transaction,IMPS
IMPS-UN-07,NO,DEAD END,,,,Transactions,Unauthorised transaction,IMPS
CARD-UN-00,POS,POS-UN-01,,Mandatory,,,,
POS-UN-01,YES,POS-UN-02,,Mandatory,,,,
POS-UN-01,NO,POS-UN-02,,Mandatory,,,,
POS-UN-02,YES,DEAD END,,,,,,
POS-UN-02,NO,DEAD END,,,,,,
UPI,UNAUTHORISED,UPI-UN-01,,Mandatory,,,,
UPI-UN-01,YES,UPI-UN-02,,Mandatory,,,,
UPI-UN-01,NO,UPI-UN-02,,Mandatory,,,,
UPI-UN-02,YES,UPI-UN-03,,Mandatory,,,,
UPI-UN-02,NO,UPI-UN-03,,Mandatory,,,,
UPI-UN-03,YES,UPI-UN-04,,Mandatory,,,,
UPI-UN-03,NO,UPI-UN-04,,Mandatory,,,,
UPI-UN-04,YES,UPI-UN-05,,Mandatory,,,,
UPI-UN-04,NO,UPI-UN-05,,Mandatory,,,,
UPI-UN-05,YES,UPI-UN-06,,Mandatory,,,,
UPI-UN-05,NO,UPI-UN-07,,Mandatory,,,,
UPI-UN-06,*,UPI-UN-07,NUMBER,Mandatory,,,,
UPI-UN-07,YES,UPI-UN-08,,Mandatory,,,,
UPI-UN-07,NO,UPI-UN-08,,Mandatory,,,,
UPI-UN-08,YES,DEAD END,,,,Transactions,Unauthorised transaction,UPI
UPI-UN-08,NO,DEAD END,,,,Transactions,Unauthorised transaction,UPI
NEFT,UNAUTHORISED,NEFT-UN-01,,,,,,
NEFT-UN-01,YES,NEFT-UN-03,,,,,,
NEFT-UN-01,NO,NEFT-UN-03,,,,,,
NEFT-UN-03,YES,NEFT-UN-04,,,,,,
NEFT-UN-03,NO,NEFT-UN-04,,,,,,
NEFT-UN-04,YES,DEAD END,,,,Transactions,Unauthorised transaction,NEFT
NEFT-UN-04,NO,DEAD END,,,,Transactions,Unauthorised transaction,NEFT
RTGS,UNAUTHORISED,RTGS-UN-01,,,,,,
RTGS-UN-01,YES,RTGS-UN-03,,,,,,
RTGS-UN-01,NO,RTGS-UN-03,,,,,,
RTGS-UN-03,YES,RTGS-UN-04,,,,,,
RTGS-UN-03,NO,RTGS-UN-04,,,,,,
RTGS-UN-04,YES,DEAD END,,,,Transactions,Unauthorised transaction,RTGS
RTGS-UN-04,NO,DEAD END,,,,Transactions,Unauthorised transaction,RTGS
INTRA_BANK,AUTHORISED,INTRA_BANK-01,,Mandatory,,,,
INTRA_BANK-01,*,INTRA_BANK-02,DATE,Mandatory,,,,
INTRA_BANK-02,YES,INTRA_BANK-03,,Mandatory,,,,
INTRA_BANK-02,NO,INTRA_BANK-04,,,,,,
INTRA_BANK-03,*,DEAD END,TEXT,,,Transactions,Unauthorised transaction,Intrabank
INTRA_BANK-04,YES,DEAD END,,,,Transactions,Unauthorised transaction,Intrabank
INTRA_BANK-04,NO,INTRA_BANK-05,,,,,,
INTRA_BANK-05,*,DEAD END,NO_INPUT,,,Transactions,Unauthorised transaction,Intrabank
UPI,NON_UDIR_AUTHORISED,UPI-01,,Mandatory,,,,
UPI-01,YES,DEAD END,,,No refund received,Transactions,Amount debited but not credited to the beneficiary,
UPI-01,NO,UPI-02,,Mandatory,,,,
UPI-02,YES,UPI-03,,Mandatory,Chargeback raise outward,,,
UPI-02,NO,UPI-04,,,,,,
UPI-04,*,DEAD END,NO_INPUT,,Not settled  at beneficiary,Transactions,Amount debited but not credited to the beneficiary,
UPI-03,*,DEAD END,TEXT,,Chargeback raise outward,Transactions,Sent to Wrong User,
