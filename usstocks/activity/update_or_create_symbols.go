package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	temporalActivity "go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/usstocks/activity"
	pb "github.com/epifi/gamma/api/usstocks/catalog"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
)

type CreateStockRequest struct {
	StockSymbol string
	// stock's unique id from vendor
	StockBrokerId string
	// stock internal status
	StockInternalStatus pb.InternalStatus
	// stock exchange
	Exchange pb.Exchange
}

type CatalogActionType int

const (
	Unspecified CatalogActionType = iota
	CreateStock
	DisableStockBuys

	// DisableOldStockTradesAndCreateNewStock Corporate actions like name changes for an existing stock require disabling trades on the old stock symbol
	// and creating a new stock for further trades
	DisableOldStockTradesAndCreateNewStock
)

type CatalogAction struct {
	ActionType           CatalogActionType
	OldSymbol, NewSymbol string
	EffectiveDate        *date.Date
}

func (p *Processor) UpdateOrCreateSymbols(ctx context.Context, req *activity.UpdateOrCreateSymbolsRequest) (*activity.UpdateOrCreateSymbolsResponse, error) {
	lg := temporalActivity.GetLogger(ctx)

	catalogActions := getCatalogActionsForCorporateActions(req.GetCorporateActions())
	if len(catalogActions) == 0 {
		lg.Info("No new symbols to update")
		return nil, nil
	}
	err := p.updateCatalog(ctx, catalogActions)
	if err != nil {
		lg.Error("error updating catalog", zap.Error(err))
		return nil, epifitemporal.NewTransientError(errors.Wrapf(err, "error in updateCatalog symbol"))
	}
	return &activity.UpdateOrCreateSymbolsResponse{}, nil
}

func (p *Processor) updateCatalog(ctx context.Context, catalogActions []*CatalogAction) error {
	// TODO(Brijesh): How do we record actions taken to monitor issues in future?
	for _, action := range catalogActions {

		switch action.ActionType {
		case CreateStock:
			newAsset, err := p.getSymbolDetailsFromBroker(ctx, action.NewSymbol)
			if errors.Is(err, epifierrors.ErrRecordNotFound) || newAsset.GetStatus() != vgStocksPb.AssetStatus_ASSET_STATUS_ACTIVE {
				logger.Error(ctx, "symbol details not found/inactive at vendor, ignoring symbol creation", zap.Error(err), zap.String(logger.SYMBOL_ID, action.NewSymbol))
				continue
			} else if err != nil {
				return errors.Wrapf(err, "error getting new symbol details for CreateStock %s", action.NewSymbol)
			}
			err = p.validateAndCreateOrUpdateStock(ctx, newAsset, action.OldSymbol)
			if err != nil {
				return errors.Wrapf(err, "error validating and creating or updating stock with old symbol: %s and new symbol: %s", action.OldSymbol, action.NewSymbol)
			}

		case DisableStockBuys:
			oldAsset, err := p.getSymbolDetailsFromBroker(ctx, action.OldSymbol)
			if errors.Is(err, epifierrors.ErrRecordNotFound) || oldAsset.GetStatus() == vgStocksPb.AssetStatus_ASSET_STATUS_ACTIVE {
				logger.Error(ctx, "symbol details not found or is active at vendor, ignoring status update to disable_buy", zap.Error(err), zap.String(logger.SYMBOL_ID, action.OldSymbol))
				continue
			} else if err != nil {
				return errors.Wrapf(err, "error getting old symbol details for DisableStockBuys %s", action.OldSymbol)
			}
			err = p.updateSymbolStatus(ctx, oldAsset, pb.InternalStatus_INTERNAL_STATUS_DISABLED_BUY)
			if err != nil {
				return errors.Wrapf(err, "error updating stock status to disableBuy %s", action.OldSymbol)
			}

		case DisableOldStockTradesAndCreateNewStock:
			newAsset, err := p.getSymbolDetailsFromBroker(ctx, action.NewSymbol)
			if errors.Is(err, epifierrors.ErrRecordNotFound) || newAsset.GetStatus() != vgStocksPb.AssetStatus_ASSET_STATUS_ACTIVE {
				logger.Error(ctx, "new symbol details not found or is inactive at vendor, ignoring creation", zap.Error(err), zap.String(logger.SYMBOL_ID, action.OldSymbol))
				continue
			} else if err != nil {
				return errors.Wrapf(err, "error getting new symbol details for DisableOldStockTradesAndCreateNewStock %s", action.NewSymbol)
			}
			err = p.validateAndCreateOrUpdateStock(ctx, newAsset, action.OldSymbol)
			if err != nil {
				return errors.Wrapf(err, "error in validating old stock %s and new stock %s", action.OldSymbol, action.NewSymbol)
			}

			oldAsset, err := p.getSymbolDetailsFromBroker(ctx, action.OldSymbol)
			if errors.Is(err, epifierrors.ErrRecordNotFound) || oldAsset.GetStatus() == vgStocksPb.AssetStatus_ASSET_STATUS_ACTIVE {
				logger.Error(ctx, "old symbol details not found or is active at vendor, ignoring status update", zap.Error(err), zap.String(logger.SYMBOL_ID, action.OldSymbol))
				continue
			} else if err != nil {
				return errors.Wrapf(err, "error getting old symbol details for DisableOldStockTradesAndCreateNewStock %s", action.OldSymbol)
			}
			err = p.updateSymbolStatus(ctx, oldAsset, pb.InternalStatus_INTERNAL_STATUS_NON_TRADABLE)
			if err != nil {
				return errors.Wrapf(err, "error updating old symbol status to Non_Tradable %s", action.OldSymbol)
			}

		default:
			return errors.Errorf("invalid action type: %d", action.ActionType)
		}
	}
	return nil
}

func (p *Processor) validateAndCreateOrUpdateStock(ctx context.Context, asset *vgStocksPb.Asset, oldSymbol string) error {
	exchange := convertVGExchangeTypeToCatalogExchange(asset.GetExchange())
	_, err := p.getStockBySymbolExchange(ctx, asset.GetSymbol(), exchange)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			err = p.checkBrokerStockIdAndCreateOrUpdate(ctx, asset, exchange, oldSymbol)
			if err != nil {
				return errors.Wrapf(err, "error checkBrokerStockIdAndCreateOrUpdate for new symbol: %s", asset.GetSymbol())
			}
		}
		return errors.Wrapf(err, "error checking if symbol and exchange combo exists")
	}
	return nil
}

func (p *Processor) checkBrokerStockIdAndCreateOrUpdate(ctx context.Context, newAsset *vgStocksPb.Asset, exchange pb.Exchange, oldSymbol string) error {

	existingStock, err := p.stocksDao.GetByBrokerStockId(ctx, newAsset.GetId(), []pb.StockFieldMask{
		pb.StockFieldMask_STOCK_ID,
		pb.StockFieldMask_STOCK_EXCHANGE, pb.StockFieldMask_STOCK_SYMBOL, pb.StockFieldMask_STOCK_BROKER_STOCK_ID,
	})
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			internalStatus := pb.InternalStatus_INTERNAL_STATUS_REVIEW_REQUIRED
			stockCreateReq := &CreateStockRequest{
				StockSymbol:         newAsset.GetSymbol(),
				StockBrokerId:       newAsset.GetId(),
				StockInternalStatus: internalStatus,
				Exchange:            exchange,
			}
			err = p.validateAndCreateStock(ctx, stockCreateReq)
			if err != nil {
				return errors.Wrapf(err, "error creating stock with symbol: %s and asset id: %s", newAsset.GetSymbol(), newAsset.GetId())
			}
			return nil
		}
		return errors.Wrapf(err, "error getting stock by broker stock id %s", newAsset.GetId())
	}
	logger.Debug(ctx, "brokerStockId already exists, updating the symbol name", zap.String(logger.SYMBOL_ID, newAsset.GetSymbol()))
	if existingStock.GetSymbol() != oldSymbol && existingStock.GetSymbol() != newAsset.GetSymbol() {
		return errors.Errorf("more than one stock with same broker stock id: %s, existing symbol: %s, old symbol from corp_action: %s", newAsset.GetId(), existingStock.GetSymbol(), oldSymbol)
	}
	stockReq := &pb.Stock{
		Id:       existingStock.GetId(),
		Symbol:   newAsset.GetSymbol(),
		Exchange: convertVGExchangeTypeToCatalogExchange(newAsset.GetExchange()),
	}
	_, err = p.stocksDao.Update(ctx, stockReq, []pb.StockFieldMask{pb.StockFieldMask_STOCK_SYMBOL, pb.StockFieldMask_STOCK_EXCHANGE})
	if err != nil {
		return errors.Wrapf(err, "error updating old symbol %s to new symbol %s", oldSymbol, newAsset.GetSymbol())
	}
	logger.Info(ctx, fmt.Sprintf("successfully updated symbol from %s   to %s", oldSymbol, newAsset.GetSymbol()))
	return nil
}

func (p *Processor) updateSymbolStatus(ctx context.Context, symbolDetails *vgStocksPb.Asset, internalStatus pb.InternalStatus) error {

	exchange := convertVGExchangeTypeToCatalogExchange(symbolDetails.GetExchange())
	internalStockDetails, err := p.getStockBySymbolExchange(ctx, symbolDetails.GetSymbol(), exchange)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil
		}
		return errors.Wrapf(err, "error checking if symbol and exchange combo exists")
	}
	stockReq := &pb.Stock{
		Id:             internalStockDetails.GetId(),
		InternalStatus: internalStatus,
	}
	_, err = p.stocksDao.Update(ctx, stockReq, []pb.StockFieldMask{pb.StockFieldMask_STOCK_INTERNAL_STATUS})
	if err != nil {
		logger.Error(ctx, "error in updating symbol status to disableBuy", zap.Error(err), zap.String(logger.SYMBOL_ID, internalStockDetails.GetSymbol()))
		return errors.Wrapf(err, "error in updating symbol %s status", internalStockDetails.GetSymbol())
	}
	logger.Info(ctx, fmt.Sprintf("successfully updated symbol  %s   status to %s", internalStockDetails.GetSymbol(), internalStatus.String()))
	return nil
}

// nolint: funlen
func getCatalogActionsForCorporateActions(corporateActions *vgStocksPb.CorporateActions) []*CatalogAction {
	var catalogActions []*CatalogAction
	for _, nameChange := range corporateActions.GetNameChanges() {
		if strings.EqualFold(nameChange.GetNewSymbol(), nameChange.GetOldSymbol()) {
			continue
		}
		if isSymbolToConsider(nameChange.GetNewSymbol(), nameChange.GetProcessDate()) {
			catalogActions = append(catalogActions, &CatalogAction{
				ActionType:    DisableOldStockTradesAndCreateNewStock,
				OldSymbol:     nameChange.GetOldSymbol(),
				NewSymbol:     nameChange.GetNewSymbol(),
				EffectiveDate: nameChange.GetProcessDate(),
			})
		}
	}

	for _, worthlessRemoval := range corporateActions.GetWorthlessRemovals() {
		if isSymbolToConsider(worthlessRemoval.GetSymbol(), worthlessRemoval.GetProcessDate()) {
			catalogActions = append(catalogActions, &CatalogAction{
				ActionType:    DisableStockBuys,
				OldSymbol:     worthlessRemoval.GetSymbol(),
				EffectiveDate: worthlessRemoval.GetProcessDate(),
			})
		}
	}

	for _, spinOff := range corporateActions.GetSpinOffs() {
		catalogActions = append(catalogActions, &CatalogAction{
			ActionType:    CreateStock,
			NewSymbol:     spinOff.GetNewSymbol(),
			OldSymbol:     spinOff.GetSourceSymbol(),
			EffectiveDate: spinOff.GetProcessDate(),
		})
	}

	for _, cashMerger := range corporateActions.GetCashMergers() {
		catalogActions = append(catalogActions, &CatalogAction{
			ActionType:    CreateStock,
			NewSymbol:     cashMerger.GetAcquirerSymbol(),
			OldSymbol:     cashMerger.GetAcquireeSymbol(),
			EffectiveDate: cashMerger.GetProcessDate(),
		})
	}

	for _, stockMerger := range corporateActions.GetStockMergers() {
		catalogActions = append(catalogActions, &CatalogAction{
			ActionType:    CreateStock,
			NewSymbol:     stockMerger.GetAcquirerSymbol(),
			OldSymbol:     stockMerger.GetAcquireeSymbol(),
			EffectiveDate: stockMerger.GetProcessDate(),
		})
	}

	for _, stockAndCashMergers := range corporateActions.GetStockAndCashMergers() {
		catalogActions = append(catalogActions, &CatalogAction{
			ActionType:    CreateStock,
			NewSymbol:     stockAndCashMergers.GetAcquirerSymbol(),
			OldSymbol:     stockAndCashMergers.GetAcquireeSymbol(),
			EffectiveDate: stockAndCashMergers.GetProcessDate(),
		})
	}

	for _, unitSplits := range corporateActions.GetUnitSplits() {
		catalogActions = append(catalogActions, &CatalogAction{
			ActionType:    CreateStock,
			NewSymbol:     unitSplits.GetNewSymbol(),
			OldSymbol:     unitSplits.GetOldSymbol(),
			EffectiveDate: unitSplits.GetProcessDate(),
		})
	}
	return catalogActions
}

func (p *Processor) getSymbolDetailsFromBroker(ctx context.Context, symbol string) (*vgStocksPb.Asset, error) {
	if symbol == "" {
		logger.Error(ctx, "received empty symbol")
		return nil, nil
	}
	res, err := p.vgUSStocksClient.GetAsset(ctx, &vgStocksPb.GetAssetRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ALPACA},
		AssetIdentifier: &vgStocksPb.GetAssetRequest_AssetSymbol{
			AssetSymbol: symbol,
		},
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrapf(err, "error getting asset details from vendor %s", symbol)
	}
	return &vgStocksPb.Asset{
		Id:         res.GetAsset().GetId(),
		Exchange:   res.GetAsset().GetExchange(),
		Symbol:     res.GetAsset().GetSymbol(),
		Status:     res.GetAsset().GetStatus(),
		IsTradable: res.GetAsset().GetIsTradable(),
	}, nil
}

func (p *Processor) validateAndCreateStock(ctx context.Context, req *CreateStockRequest) error {
	stockModel := &pb.Stock{}
	if req.StockSymbol == "" || req.StockBrokerId == "" {
		logger.Error(ctx, "stockSymbol or brokerStockId is empty, ignoring creation", zap.String(logger.SYMBOL_ID, req.StockSymbol), zap.String("broker_id", req.StockBrokerId))
		// returning nil , in case we get symbol or broker id as blank to ignore creation and process remaining symbols
		return nil
	}
	stockModel.Symbol = req.StockSymbol
	stockModel.Exchange = req.Exchange
	stockModel.InternalStatus = req.StockInternalStatus
	stockModel.BrokerStockId = req.StockBrokerId
	logger.Debug(ctx, fmt.Sprintf("creating symbol in db %s ", req.StockSymbol))
	_, err := p.stocksDao.Create(ctx, stockModel)
	if err != nil {
		return errors.Wrapf(err, "error in creating record in DB for corp action symbol %s", req.StockSymbol)
	}
	logger.Debug(ctx, fmt.Sprintf("successfully created new symbol for %s", req.StockSymbol))
	return nil
}

// exclude blank values and EffectiveDate less than today from market data provider
func isSymbolToConsider(symbol string, processDate *date.Date) bool {
	if symbol == "" || processDate == nil {
		return false
	}
	effectiveDate := datetime.DateToTime(processDate, time.UTC)
	if effectiveDate.After(time.Now().UTC()) {
		return false
	}
	return true
}

func convertVGExchangeTypeToCatalogExchange(exchange vgStocksPb.AssetExchange) pb.Exchange {
	switch exchange {
	case vgStocksPb.AssetExchange_ASSET_EXCHANGE_UNSPECIFIED:
		return pb.Exchange_EXCHANGE_UNSPECIFIED
	case vgStocksPb.AssetExchange_ASSET_EXCHANGE_AMEX:
		return pb.Exchange_EXCHANGE_ASE
	case vgStocksPb.AssetExchange_ASSET_EXCHANGE_ARCA:
		return pb.Exchange_EXCHANGE_ARCA
	case vgStocksPb.AssetExchange_ASSET_EXCHANGE_BATS:
		return pb.Exchange_EXCHANGE_BATS
	case vgStocksPb.AssetExchange_ASSET_EXCHANGE_NYSE:
		return pb.Exchange_EXCHANGE_NYSE
	case vgStocksPb.AssetExchange_ASSET_EXCHANGE_NASDAQ:
		return pb.Exchange_EXCHANGE_NAS
	case vgStocksPb.AssetExchange_ASSET_EXCHANGE_NYSEARCA:
		return pb.Exchange_EXCHANGE_UNSPECIFIED
	case vgStocksPb.AssetExchange_ASSET_EXCHANGE_OTC:
		return pb.Exchange_EXCHANGE_OTC
	}
	return pb.Exchange_EXCHANGE_UNSPECIFIED
}

func (p *Processor) getStockBySymbolExchange(ctx context.Context, symbol string, exchange pb.Exchange) (*pb.Stock, error) {
	stock, err := p.stocksDao.GetBySymbolAndExchangeId(ctx, symbol, exchange, []pb.StockFieldMask{pb.StockFieldMask_STOCK_ID,
		pb.StockFieldMask_STOCK_SYMBOL,
		pb.StockFieldMask_STOCK_EXCHANGE,
		pb.StockFieldMask_STOCK_BROKER_STOCK_ID})
	if err != nil {
		return nil, errors.Wrapf(err, "error getting stock by symbol %s and exchange %s", symbol, exchange)
	}
	return stock, nil
}
