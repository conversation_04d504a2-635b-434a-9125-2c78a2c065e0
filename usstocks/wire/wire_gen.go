// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/aws/v2/lambda"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	genconf2 "github.com/epifi/be-common/pkg/cfg/genconf"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/ratelimiter"
	"github.com/epifi/be-common/pkg/ratelimiter/store"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/aml"
	"github.com/epifi/gamma/api/auth"
	liveness2 "github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/investment/aggregator"
	"github.com/epifi/gamma/api/investment/dynamic_ui_element"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/usstocks/account"
	"github.com/epifi/gamma/api/usstocks/catalog"
	order3 "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/api/usstocks/portfolio"
	"github.com/epifi/gamma/api/vendorgateway/liveness"
	"github.com/epifi/gamma/api/vendorgateway/location"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	internationalfundtransfer2 "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	"github.com/epifi/gamma/api/vendorgateway/stocks"
	catalog2 "github.com/epifi/gamma/api/vendorgateway/stocks/catalog"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	"github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/pkg/feature/release"
	genconf4 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/datafetcher"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/tcs"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/validation"
	"github.com/epifi/gamma/pkg/zinc/search"
	account2 "github.com/epifi/gamma/usstocks/account"
	"github.com/epifi/gamma/usstocks/account/account_update_observer"
	consumer3 "github.com/epifi/gamma/usstocks/account/consumer"
	"github.com/epifi/gamma/usstocks/account/dao"
	impl3 "github.com/epifi/gamma/usstocks/account/dao/impl"
	"github.com/epifi/gamma/usstocks/account/onboardingdatamanager"
	"github.com/epifi/gamma/usstocks/account/onboardingdatamanager/datamanager"
	"github.com/epifi/gamma/usstocks/account/suitability_questions"
	"github.com/epifi/gamma/usstocks/account/suitability_questions/option_selector/auto_fetch_selector/auto_fetch_data_collector"
	"github.com/epifi/gamma/usstocks/account/suitability_questions/option_selector/list_selector"
	"github.com/epifi/gamma/usstocks/account/validator"
	"github.com/epifi/gamma/usstocks/activity"
	"github.com/epifi/gamma/usstocks/activity/notificationgetter"
	"github.com/epifi/gamma/usstocks/activity/taskProcessor"
	catalog3 "github.com/epifi/gamma/usstocks/catalog"
	"github.com/epifi/gamma/usstocks/catalog/catalog_file_processor"
	"github.com/epifi/gamma/usstocks/catalog/collectionStockMappingProcessor"
	"github.com/epifi/gamma/usstocks/catalog/consumer"
	impl2 "github.com/epifi/gamma/usstocks/catalog/dao/impl"
	"github.com/epifi/gamma/usstocks/catalog/real_time_stock_price"
	"github.com/epifi/gamma/usstocks/catalog/real_time_stock_price/observers"
	"github.com/epifi/gamma/usstocks/catalog/stockprocessor"
	"github.com/epifi/gamma/usstocks/catalog/vendordao"
	"github.com/epifi/gamma/usstocks/config/genconf"
	"github.com/epifi/gamma/usstocks/config/worker"
	genconf3 "github.com/epifi/gamma/usstocks/config/worker/genconf"
	consumer4 "github.com/epifi/gamma/usstocks/consumer"
	"github.com/epifi/gamma/usstocks/consumer/workflow_update_processor"
	"github.com/epifi/gamma/usstocks/developer"
	"github.com/epifi/gamma/usstocks/developer/processor"
	"github.com/epifi/gamma/usstocks/dynamic_elements"
	"github.com/epifi/gamma/usstocks/dynamic_elements/dynamic_elements_getter"
	internationalfundtransfer3 "github.com/epifi/gamma/usstocks/internal/internationalfundtransfer"
	savings2 "github.com/epifi/gamma/usstocks/internal/savings"
	"github.com/epifi/gamma/usstocks/notifications"
	"github.com/epifi/gamma/usstocks/operations"
	order2 "github.com/epifi/gamma/usstocks/order"
	consumer2 "github.com/epifi/gamma/usstocks/order/consumer"
	"github.com/epifi/gamma/usstocks/order/consumer/orderupdateprocessor"
	"github.com/epifi/gamma/usstocks/order/dao/impl"
	"github.com/epifi/gamma/usstocks/order/fundtransfereventobserver"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transaction_processor/aggregated_transaction"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_bulk_getter/aggregated_transactions_getter"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_bulk_updater/aggregated_transactions_updater"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_file_generator/ttum_gst_file_generator"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_file_generator/ttum_gst_file_generator/recon_processor/rounding_recon_policy"
	"github.com/epifi/gamma/usstocks/order/journalupdateobserver"
	"github.com/epifi/gamma/usstocks/order/order_update_observer"
	"github.com/epifi/gamma/usstocks/order/remittance"
	"github.com/epifi/gamma/usstocks/order/uservalidator"
	"github.com/epifi/gamma/usstocks/order/walletorderprocessor"
	"github.com/epifi/gamma/usstocks/order/walletordervalidator"
	portfolio2 "github.com/epifi/gamma/usstocks/portfolio"
	impl4 "github.com/epifi/gamma/usstocks/refund/dao/impl"
	rewards2 "github.com/epifi/gamma/usstocks/rewards"
	dao2 "github.com/epifi/gamma/usstocks/rewards/dao"
	"github.com/epifi/gamma/usstocks/tax"
	"github.com/epifi/gamma/usstocks/tax/activitytransformer"
	consumer5 "github.com/epifi/gamma/usstocks/tax/consumer"
	dao3 "github.com/epifi/gamma/usstocks/tax/dao"
	"github.com/epifi/gamma/usstocks/tax/document"
	"github.com/epifi/gamma/usstocks/tax/document/generator"
	"github.com/epifi/gamma/usstocks/tax/document_params"
	"github.com/epifi/gamma/usstocks/tax/document_params/param_generator"
	"github.com/epifi/gamma/usstocks/tax/transactionprocessor"
	"github.com/epifi/gamma/usstocks/utils"
	types3 "github.com/epifi/gamma/usstocks/wire/types"
	types2 "github.com/epifi/gamma/vendorgateway/wire/types"
	"github.com/slack-go/slack"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
)

// Injectors from wire.go:

// config: {"s3Client": "CatalogS3Conf().BucketName"}
func InitializeOrderManagerService(db types.UsstocksAlpacaPGDB, genConf *genconf.Config, celestialClient celestial.CelestialClient, internationalFundTransferClient internationalfundtransfer.InternationalFundTransferClient, vgUSStocksClientWithInterceptors types2.VgStocksClientWithInterceptors, s3Client types3.OrderManagerS3Client, usersClient user.UsersClient, savingsClient savings.SavingsClient, ordersClient order.OrderServiceClient, vgInternationalFundTransferClient internationalfundtransfer2.InternationalFundTransferClient, bankCustomerServiceClient bankcust.BankCustomerServiceClient, commsClient comms.CommsClient, actorClient actor.ActorClient, portfolioManager portfolio.PortfolioManagerClient, catalogMgClient catalog.CatalogManagerClient, usStocksRedisStore types.USStocksRedisStore, iftFileGenSvc file_generator.FileGeneratorClient, accountManagerClient account.AccountManagerClient, preApprovedLoanClient preapprovedloan.PreApprovedLoanClient, vendorAccountActivitiesS3Client types3.VendorAccountActivitiesS3Client, riskProfileClient profile.ProfileClient, authClient auth.AuthClient, payClient pay.PayClient) *order2.Service {
	pgdbConn := pgdbMigrationConfigProvider(genConf)
	gormDB := GormProvider(db, pgdbConn)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	uint32_2 := MaxPageSizeProvider(genConf)
	implOrder := impl.NewOrder(gormDB, domainIdGenerator, uint32_2)
	orderPgdb := impl.NewOrderPgdb(pgdbConn, db, domainIdGenerator, uint32_2)
	ordersDao := impl.OrderProvider(pgdbConn, implOrder, orderPgdb)
	stockCrdb := impl2.NewStockCrdb(gormDB, domainIdGenerator)
	stockPgdb := impl2.NewStockPgdb(pgdbConn, db, domainIdGenerator)
	stockDao := impl2.StockProvider(pgdbConn, stockCrdb, stockPgdb)
	stocksClient := types2.VgStocksClientProvider(vgUSStocksClientWithInterceptors)
	accountCrdb := impl3.NewAccountCrdb(gormDB, domainIdGenerator)
	accountPgdb := impl3.NewAccountPgdb(pgdbConn, db, domainIdGenerator)
	accountDao := impl3.AccountDaoProvider(pgdbConn, accountCrdb, accountPgdb)
	doOnce := once.NewDoOnce(gormDB)
	service := notifications.NewService(commsClient, actorClient, usersClient, doOnce)
	accountActivity := impl.NewAccountActivity(gormDB, uint32_2)
	accountActivityPgdb := impl.NewAccountActivityPgdb(pgdbConn, db, uint32_2)
	accountActivityDao := impl.AccountActivityProvider(pgdbConn, accountActivity, accountActivityPgdb)
	accountActivitySyncCursor := impl.NewAccountActivitySyncCursor(gormDB)
	accountActivitySyncCursorPgdb := impl.NewAccountActivitySyncCursorPgdb(pgdbConn, db)
	accountActivitySyncCursorDao := impl.AccountActivitySyncCursorProvider(pgdbConn, accountActivitySyncCursor, accountActivitySyncCursorPgdb)
	cacheStorage := NewUSStocksRedisCache(usStocksRedisStore)
	positionsCacheRedis := portfolio2.NewPositionsCacheRedis(stocksClient, cacheStorage)
	orderUpdateSignalWorkflow := order_update_observer.NewOrderUpdateSignalWorkflow(celestialClient, positionsCacheRedis)
	orderUpdateEventsNotifier := order_update_observer.NewOrderUpdateEventsNotifier(orderUpdateSignalWorkflow)
	aggregateRemittanceTransactionPageSize := types3.AggregateRemittanceTransactionPageSizeProvider(uint32_2)
	aggregatedRemittanceTransaction := impl.NewAggregatedRemittanceTransaction(gormDB, domainIdGenerator, aggregateRemittanceTransactionPageSize)
	aggregatedRemittanceTransactionPgdb := impl.NewAggregatedRemittanceTransactionPgdb(pgdbConn, db, domainIdGenerator, aggregateRemittanceTransactionPageSize)
	aggregatedRemittanceTransactionDao := impl.AggregatedRemittanceTransactionProvider(pgdbConn, aggregatedRemittanceTransaction, aggregatedRemittanceTransactionPgdb)
	aggregatedTransactionGetter := aggregated_transactions_getter.NewAggregatedTransactionGetter(aggregatedRemittanceTransactionDao, genConf)
	transactionRemittanceRequest := impl.NewTransactionRemittanceRequest(gormDB, uint32_2)
	transactionRemittanceRequestPgdb := impl.NewTransactionRemittanceRequestPgdb(pgdbConn, db, uint32_2)
	transactionRemittanceRequestDao := impl.TransactionRemittanceRequestProvider(pgdbConn, transactionRemittanceRequest, transactionRemittanceRequestPgdb)
	aggregatedTransaction := aggregated_transaction.NewAggregatedTransaction(aggregatedRemittanceTransactionDao, transactionRemittanceRequestDao, celestialClient, accountActivityDao)
	aggregatedTransactionsUpdater := aggregated_transactions_updater.NewAggregatedTransactionsUpdater(aggregatedRemittanceTransactionDao, aggregatedTransaction)
	roundingReconPolicy := rounding_recon_policy.NewRoundingReconPolicy()
	accessor := savings2.NewAccessor(savingsClient)
	creditFreezeValidator := uservalidator.NewCreditFreezeValidator(genConf, accessor, riskProfileClient)
	client := usStocksSlackAlertClientProviderForServer(genConf)
	generateFileForTransaction := ttum_gst_file_generator.NewGenerateFileForTransaction(internationalFundTransferClient, roundingReconPolicy, iftFileGenSvc, genConf, bankCustomerServiceClient, creditFreezeValidator, accessor, client)
	acknowledgeInwardOpsProcessor := inward_remittance_ops_processor.NewAcknowledgeInwardOpsProcessor(aggregatedTransactionGetter, aggregatedTransactionsUpdater, generateFileForTransaction)
	walletOrder := impl.NewWalletOrder(gormDB, domainIdGenerator, uint32_2)
	walletOrderPgdb := impl.NewWalletOrderPgdb(pgdbConn, db, domainIdGenerator, uint32_2)
	walletOrderDao := impl.WalletOrderProvider(pgdbConn, walletOrder, walletOrderPgdb)
	invoiceValidator := walletordervalidator.NewInvoiceValidator(genConf)
	forexRateValidator := walletordervalidator.NewForexRateValidator(internationalFundTransferClient)
	personalLoanValidator := walletordervalidator.NewPersonalLoanValidator(preApprovedLoanClient)
	defaultTime := datetime.NewDefaultTime()
	afuChecker := uservalidator.NewAfuChecker(genConf, authClient, defaultTime)
	authFactorUpdateValidator := walletordervalidator.NewAuthFactorUpdateValidator(afuChecker)
	suitabilityLedgerPgdb := impl3.NewSuitabilityLedgerPgdb(pgdbConn, db, domainIdGenerator)
	suitabilityLedgerDao := impl3.SuitabilityLedgerDaoProvider(suitabilityLedgerPgdb)
	transactionAggregatesFetcher := datafetcher.NewTransactionAggregatesFetcher(payClient)
	fyRemittanceAmountValidation := validation.NewFYRemittanceAmountValidation(transactionAggregatesFetcher)
	fyRemittanceAmountValidator := walletordervalidator.NewFYRemittanceAmountValidator(suitabilityLedgerDao, fyRemittanceAmountValidation)
	internationalfundtransferAccessor := internationalfundtransfer3.NewAccessor(genConf, internationalFundTransferClient)
	addFundsOrderProcessor := walletorderprocessor.NewAddFundsOrderProcessor(invoiceValidator, forexRateValidator, personalLoanValidator, authFactorUpdateValidator, fyRemittanceAmountValidator, internationalfundtransferAccessor, walletOrderDao, celestialClient, suitabilityLedgerDao)
	walletordervalidatorCreditFreezeValidator := walletordervalidator.NewCreditFreezeValidator(creditFreezeValidator)
	withdrawableAmountValidator := walletordervalidator.NewWithdrawableAmountValidator(stocksClient)
	availableTransactionLimitValidator := walletordervalidator.NewAvailableTransactionLimitValidator(transactionRemittanceRequestDao, internationalFundTransferClient, walletOrderDao)
	withdrawFundsOrderProcessor := walletorderprocessor.NewWithdrawFundsOrderProcessor(walletordervalidatorCreditFreezeValidator, withdrawableAmountValidator, availableTransactionLimitValidator, walletOrderDao, celestialClient, authFactorUpdateValidator)
	sipOrderProcessor := walletorderprocessor.NewSipOrderProcessor(personalLoanValidator, authFactorUpdateValidator, fyRemittanceAmountValidator, celestialClient)
	factory := walletorderprocessor.NewWalletOrderProcessorFactory(addFundsOrderProcessor, withdrawFundsOrderProcessor, sipOrderProcessor)
	signalWorkflowJournalUpdateObserver := journalupdateobserver.NewSignalWorkflowJournalUpdateObserver(celestialClient, walletOrderDao, stocksClient)
	journalUpdateEventsNotifier := journalupdateobserver.NewJournalUpdateEventsNotifier(signalWorkflowJournalUpdateObserver)
	signalWorkflowFundTransferEventObserver := fundtransfereventobserver.NewSignalWorkflowFundTransferEventObserver(celestialClient, walletOrderDao)
	fundTransferEventNotifier := fundtransfereventobserver.NewFundTransferEventsNotifier(signalWorkflowFundTransferEventObserver)
	remittanceProcess := impl.NewRemittanceProcess(gormDB, domainIdGenerator)
	remittanceProcessPgdb := impl.NewRemittanceProcessPgdb(pgdbConn, db, domainIdGenerator)
	remittanceProcessDao := impl.RemittanceProcessProvider(pgdbConn, remittanceProcess, remittanceProcessPgdb)
	apiBasedTCSCalculator := tcs.NewAPIBasedTCSCalculator(bankCustomerServiceClient, vgInternationalFundTransferClient)
	source := idgen.NewCryptoSeededSource()
	runeNumberIdGenerator := idgen.NewNumberIdGenerator(source)
	stockProcessor := stockprocessor.NewStockProcessor(stockDao, stocksClient)
	orderService := order2.NewService(genConf, ordersDao, celestialClient, internationalFundTransferClient, stockDao, stocksClient, accountDao, s3Client, doOnce, usersClient, savingsClient, ordersClient, vgInternationalFundTransferClient, bankCustomerServiceClient, service, accountActivityDao, accountActivitySyncCursorDao, portfolioManager, catalogMgClient, orderUpdateEventsNotifier, iftFileGenSvc, acknowledgeInwardOpsProcessor, aggregatedTransaction, walletOrderDao, aggregatedRemittanceTransactionDao, factory, accountManagerClient, journalUpdateEventsNotifier, fundTransferEventNotifier, transactionRemittanceRequestDao, vendorAccountActivitiesS3Client, remittanceProcessDao, generateFileForTransaction, aggregatedTransactionsUpdater, apiBasedTCSCalculator, defaultTime, runeNumberIdGenerator, stockProcessor)
	return orderService
}

func InitializeCatalogManagerService(db types.UsstocksAlpacaPGDB, genConf *genconf.Config, vgUSStocksClientWithInterceptors types2.VgStocksClientWithInterceptors, vgUSStocksStreamClientWithInterceptors types3.VgStocksStreamClient, client search.SearchClient, rateLimiterCl types.RateLimiterRedisStore, vgCatalogClient catalog2.CatalogClient, stockCatalogRefreshPublisher types3.USStockCatalogRefreshPublisher, usStocksRedisStore types.USStocksRedisStore, actorClient actor.ActorClient, userClient user.UsersClient, userGroupClient group.GroupClient) *catalog3.Service {
	pgdbConn := pgdbMigrationConfigProvider(genConf)
	gormDB := GormProvider(db, pgdbConn)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	stockCrdb := impl2.NewStockCrdb(gormDB, domainIdGenerator)
	stockPgdb := impl2.NewStockPgdb(pgdbConn, db, domainIdGenerator)
	stockDao := impl2.StockProvider(pgdbConn, stockCrdb, stockPgdb)
	stocksClient := types2.VgStocksClientProvider(vgUSStocksClientWithInterceptors)
	realTimeStockPriceStreamer := observers.NewRealTimePriceStreamer()
	redisClient := types.RateLimiterRedisStoreRedisClientProvider(rateLimiterCl)
	slidingWindowLogWithRedisImpl := store.NewSlidingWindowLogWithRedis(redisClient)
	rateLimitConfig := PriceUpdateRateLimiterConfigProvider(genConf)
	v := DummyRateLimiterOptionsProvider()
	rateLimiterImpl := ratelimiter.NewRateLimiterV2(slidingWindowLogWithRedisImpl, rateLimitConfig, v...)
	cacheStorage := NewUSStocksRedisCache(usStocksRedisStore)
	stockPricesDaoRedis := vendordao.NewLatestStockPricesDaoRedis(genConf, stocksClient, cacheStorage)
	realTimeStockPriceStoreUpdater := observers.NewRealTimeStockPriceStoreUpdater(rateLimiterImpl, cacheStorage, stockPricesDaoRedis)
	realTimeStockPriceNotifier := real_time_stock_price.NewStockPriceNotifier(realTimeStockPriceStreamer, realTimeStockPriceStoreUpdater, stockDao, genConf)
	marketCategoryCrdb := impl2.NewMarketCategoryCrdb(gormDB, domainIdGenerator)
	marketCategoryPgdb := impl2.NewMarketCategoryPgdb(pgdbConn, db, domainIdGenerator)
	marketCategoryDao := impl2.MarketCategoryProvider(pgdbConn, marketCategoryCrdb, marketCategoryPgdb)
	collectionDaoCrdb := impl2.NewCollectionDaoCrdb(gormDB, domainIdGenerator)
	collectionDaoPgdb := impl2.NewCollectionDaoPgdb(pgdbConn, db, domainIdGenerator)
	collectionDao := impl2.CollectionDaoProvider(pgdbConn, collectionDaoCrdb, collectionDaoPgdb)
	collectionStockMappingDaoCrdb := impl2.NewCollectionStockMappingDaoCrdb(gormDB)
	collectionStockMappingDaoPgdb := impl2.NewCollectionStockMappingDaoPgdb(pgdbConn, db)
	collectionStockMappingDao := impl2.CollectionStockMappingDaoProvider(pgdbConn, collectionStockMappingDaoCrdb, collectionStockMappingDaoPgdb)
	watchlistDaoCrdb := impl2.NewWatchlistDaoCrdb(gormDB, domainIdGenerator)
	watchlistDaoPgdb := impl2.NewWatchlistDaoPgdb(pgdbConn, db, domainIdGenerator)
	watchlistDao := impl2.WatchlistDaoProvider(pgdbConn, watchlistDaoCrdb, watchlistDaoPgdb)
	watchlistStockMappingDaoCrdb := impl2.NewWatchlistStockMappingDaoCrdb(gormDB)
	watchlistStockMappingDaoPgdb := impl2.NewWatchlistStockMappingDaoPgdb(pgdbConn, db)
	watchlistStockMappingDao := impl2.WatchlistStockMappingDaoProvider(pgdbConn, watchlistStockMappingDaoCrdb, watchlistStockMappingDaoPgdb)
	stockVisitDaoCrdb := impl2.NewStockVisitDaoCrdb(gormDB)
	stockVisitDaoPgdb := impl2.NewStockVisitDaoPgdb(pgdbConn, db)
	stockVisitDao := impl2.StockVisitDaoProvider(pgdbConn, stockVisitDaoCrdb, stockVisitDaoPgdb)
	userPreferredStockMappingProcessor := collectionStockMappingProcessor.NewUserPreferredStockMappingProcessor(collectionStockMappingDao, stockVisitDao)
	staticCollectionStockMappingProcessor := collectionStockMappingProcessor.NewStaticCollectionStockMappingProcessor(collectionStockMappingDao)
	collectionStockMappingProcessorFactory := collectionStockMappingProcessor.NewCollectionStockMappingProcessorFactory(userPreferredStockMappingProcessor, staticCollectionStockMappingProcessor)
	stockProcessor := stockprocessor.NewStockProcessor(stockDao, stocksClient)
	service := catalog3.NewService(genConf, stockDao, stocksClient, vgUSStocksStreamClientWithInterceptors, realTimeStockPriceStreamer, realTimeStockPriceNotifier, marketCategoryDao, collectionDao, collectionStockMappingDao, client, rateLimiterImpl, watchlistDao, watchlistStockMappingDao, vgCatalogClient, stockCatalogRefreshPublisher, cacheStorage, actorClient, userClient, userGroupClient, stockVisitDao, collectionStockMappingProcessorFactory, stockPricesDaoRedis, stockProcessor)
	return service
}

// config: {"client": "AccountManagerConfig().KycDocumentsBucketName()"}
func InitializeAccountManagerService(db types.UsstocksAlpacaPGDB, userClient user.UsersClient, actorClient actor.ActorClient, vgUSStocksClientWithInterceptors types2.VgStocksClientWithInterceptors, celestialClient celestial.CelestialClient, amlClient aml.AmlClient, client types3.AccountManagerS3Client, config *genconf.Config, ocrClient inhouseocr.OcrClient, onboardingClient onboarding.OnboardingClient, lvVgClient liveness.LivenessClient, lvClient liveness2.LivenessClient, commsClient comms.CommsClient, client2 types3.USStocksClient, redisClient types.USStocksAccountRedisStore, vkycClient vkyc.VKYCClient, awsConf aws.Config, employmentClient employment.EmploymentClient, bcClient bankcust.BankCustomerServiceClient, panClient pan.PanClient, transferClient internationalfundtransfer.InternationalFundTransferClient, locationClient location.LocationClient, savingsClient savings.SavingsClient, wealthOnbClient wealthonboarding.WealthOnboardingClient) *account2.Service {
	pgdbConn := pgdbMigrationConfigProvider(config)
	gormDB := GormProvider(db, pgdbConn)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	accountCrdb := impl3.NewAccountCrdb(gormDB, domainIdGenerator)
	accountPgdb := impl3.NewAccountPgdb(pgdbConn, db, domainIdGenerator)
	accountDao := impl3.AccountDaoProvider(pgdbConn, accountCrdb, accountPgdb)
	investorCrdb := impl3.NewInvestorCrdb(gormDB, domainIdGenerator)
	investorPgdb := impl3.NewInvestorPgdb(pgdbConn, db, domainIdGenerator)
	investorDao := impl3.InvestorDaoProvider(pgdbConn, investorCrdb, investorPgdb)
	stocksClient := types2.VgStocksClientProvider(vgUSStocksClientWithInterceptors)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	processor := account2.NewProcessor(ocrClient, lvVgClient, lvClient, client, config)
	panOcrValidator := validator.NewPanOcrValidator()
	faceMatchValidator := validator.NewFaceMatchValidator()
	uint32_2 := MaxPageSizeProvider(config)
	implOrder := impl.NewOrder(gormDB, domainIdGenerator, uint32_2)
	orderPgdb := impl.NewOrderPgdb(pgdbConn, db, domainIdGenerator, uint32_2)
	ordersDao := impl.OrderProvider(pgdbConn, implOrder, orderPgdb)
	doOnce := once.NewDoOnce(gormDB)
	service := notifications.NewService(commsClient, actorClient, userClient, doOnce)
	clientClient := types3.USStocksClientProvider(client2)
	client3 := types.USStocksAccountRedisStoreRedisClientProvider(redisClient)
	lambdaClient := LambdaClientProvider(awsConf)
	manualReviewCrdb := impl3.NewManualReviewCrdb(gormDB)
	manualReviewPgdb := impl3.NewManualReviewPgdb(pgdbConn, db)
	manualReviewDao := impl3.ManualReviewDaoProvider(pgdbConn, manualReviewCrdb, manualReviewPgdb)
	accountUpdateSignalWorkflow := account_update_observer.NewAccountUpdateSignalWorkflow(celestialClient)
	accountDetailsUpdater := account_update_observer.NewAccountDetailsUpdater(accountDao, gormTxnExecutor)
	accountUpdateEventsNotifier := account_update_observer.NewAccountUpdateEventsNotifier(accountUpdateSignalWorkflow, accountDetailsUpdater)
	tradingAccountCrdb := impl3.NewTradingAccountCrdb(gormDB, domainIdGenerator)
	tradingAccountPgdb := impl3.NewTradingAccountPgdb(pgdbConn, db, domainIdGenerator)
	tradingAccountDao := impl3.TradingAccountDaoProvider(pgdbConn, tradingAccountCrdb, tradingAccountPgdb)
	suitabilityLedgerPgdb := impl3.NewSuitabilityLedgerPgdb(pgdbConn, db, domainIdGenerator)
	suitabilityLedgerDao := impl3.SuitabilityLedgerDaoProvider(suitabilityLedgerPgdb)
	defaultTime := datetime.NewDefaultTime()
	multiOptionSelector := list_selector.NewMultiOptionSelector(config)
	singleOptionSelector := list_selector.NewSingleOptionSelector(config)
	autoFetchDateOfBirth := auto_fetch_data_collector.NewAutoFetchDateOfBirth(userClient)
	autoFetchAnnualIncomeRange := auto_fetch_data_collector.NewAutoFetchAnnualIncomeRange(employmentClient)
	autoFetchOptionSelector := suitability_questions.NewAutoFetchOption(config, autoFetchDateOfBirth, autoFetchAnnualIncomeRange)
	suitabilityQuestionManager := suitability_questions.NewQuestionManager(config, multiOptionSelector, singleOptionSelector, autoFetchOptionSelector)
	iDataManagerFactory := OnboardingDataManagerFactoryProvider(db, pgdbConn, investorDao, accountDao, manualReviewDao, transferClient, celestialClient, wealthOnbClient)
	accountService := account2.NewService(accountDao, investorDao, userClient, actorClient, stocksClient, celestialClient, amlClient, client, config, gormTxnExecutor, ocrClient, onboardingClient, processor, panOcrValidator, faceMatchValidator, ordersDao, service, clientClient, client3, vkycClient, lambdaClient, employmentClient, manualReviewDao, lvClient, bcClient, accountUpdateEventsNotifier, panClient, tradingAccountDao, suitabilityLedgerDao, defaultTime, transferClient, suitabilityQuestionManager, locationClient, savingsClient, iDataManagerFactory)
	return accountService
}

func OnboardingDataManagerFactoryProvider(db types.UsstocksAlpacaPGDB, migrationConfig *genconf2.PgdbConn, investorDao dao.InvestorDao, accountDao dao.AccountDao, manualReviewDao dao.ManualReviewDao, iftClient internationalfundtransfer.InternationalFundTransferClient, celestialClient celestial.CelestialClient, wealthOnbClient wealthonboarding.WealthOnboardingClient) onboardingdatamanager.IDataManagerFactory {
	addressAndIdentityConsentDataManager := datamanager.NewAddressAndIdentityConsentDataManager(investorDao)
	agreementsDataManager := datamanager.NewAgreementsDataManager(investorDao)
	disclosuresDataManager := datamanager.NewDisclosuresDataManager(investorDao, accountDao)
	employmentDetailsDataManager := datamanager.NewEmploymentDetailsDataManager(investorDao)
	investmentInterestDataManager := datamanager.NewInvestmentInterestDataManager(investorDao)
	manualPanUploadDataManager := datamanager.NewManualPanUploadDataManager(investorDao, accountDao)
	riskDisclosureDataManager := datamanager.NewRiskDisclosureDataManager(investorDao, accountDao)
	sofDataManager := datamanager.NewSOFDataManager(iftClient, celestialClient)
	gormDB := GormProvider(db, migrationConfig)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	documentUploadDataManager := datamanager.NewDocumentUploadDataManager(wealthOnbClient, investorDao, accountDao, gormTxnExecutor)
	manualPANReviewDataManager := datamanager.NewManualPANReviewDataManager(manualReviewDao)
	dataManagerFactory := onboardingdatamanager.NewDataManagerFactory(addressAndIdentityConsentDataManager, agreementsDataManager, disclosuresDataManager, employmentDetailsDataManager, investmentInterestDataManager, manualPanUploadDataManager, riskDisclosureDataManager, sofDataManager, documentUploadDataManager, manualPANReviewDataManager)
	return dataManagerFactory
}

// config: {"operationsS3Client": "CatalogS3Conf().BucketName"}
func InitializeOperationsService(config *genconf.Config, operationsS3Client types3.OperationsS3Client) *operations.Service {
	service := operations.NewService(operationsS3Client, config)
	return service
}

// config: {"s3Client": "CatalogS3Conf().BucketName", "morningStarS3Client": "MorningStarS3Bucket().BucketName()"}
func InitializeCatalogConsumerService(db types.UsstocksAlpacaPGDB, s3Client types3.CatalogConsumerS3Client, vgCatalogClient catalog2.CatalogClient, config *genconf.Config, morningStarS3Client types3.MorningStarS3Client, catalogManager catalog.CatalogManagerClient) *consumer.Service {
	pgdbConn := pgdbMigrationConfigProvider(config)
	gormDB := GormProvider(db, pgdbConn)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	stockCrdb := impl2.NewStockCrdb(gormDB, domainIdGenerator)
	stockPgdb := impl2.NewStockPgdb(pgdbConn, db, domainIdGenerator)
	stockDao := impl2.StockProvider(pgdbConn, stockCrdb, stockPgdb)
	catalogFileProcessorFactory := catalog_file_processor.NewCatalogFileProcessorFactory(s3Client, stockDao, morningStarS3Client, config)
	marketCategoryCrdb := impl2.NewMarketCategoryCrdb(gormDB, domainIdGenerator)
	marketCategoryPgdb := impl2.NewMarketCategoryPgdb(pgdbConn, db, domainIdGenerator)
	marketCategoryDao := impl2.MarketCategoryProvider(pgdbConn, marketCategoryCrdb, marketCategoryPgdb)
	service := consumer.NewService(config, catalogFileProcessorFactory, stockDao, vgCatalogClient, marketCategoryDao, catalogManager)
	return service
}

func InitializePortfolioManagerService(db types.UsstocksAlpacaPGDB, config *genconf.Config, accountManagerClient account.AccountManagerClient, vgUSStocksClientWithInterceptors types2.VgStocksClientWithInterceptors, catalogManager catalog.CatalogManagerClient, ussOrderMgClient order3.OrderManagerClient, internationalFundTransferClient internationalfundtransfer.InternationalFundTransferClient, usStocksRedisStore types.USStocksRedisStore) *portfolio2.Service {
	stocksClient := types2.VgStocksClientProvider(vgUSStocksClientWithInterceptors)
	pgdbConn := pgdbMigrationConfigProvider(config)
	gormDB := GormProvider(db, pgdbConn)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	stockCrdb := impl2.NewStockCrdb(gormDB, domainIdGenerator)
	stockPgdb := impl2.NewStockPgdb(pgdbConn, db, domainIdGenerator)
	stockDao := impl2.StockProvider(pgdbConn, stockCrdb, stockPgdb)
	uint32_2 := MaxPageSizeProvider(config)
	implOrder := impl.NewOrder(gormDB, domainIdGenerator, uint32_2)
	orderPgdb := impl.NewOrderPgdb(pgdbConn, db, domainIdGenerator, uint32_2)
	ordersDao := impl.OrderProvider(pgdbConn, implOrder, orderPgdb)
	cacheStorage := NewUSStocksRedisCache(usStocksRedisStore)
	positionsCacheRedis := portfolio2.NewPositionsCacheRedis(stocksClient, cacheStorage)
	service := portfolio2.NewService(config, accountManagerClient, stocksClient, catalogManager, stockDao, ussOrderMgClient, ordersDao, internationalFundTransferClient, positionsCacheRedis)
	return service
}

func InitializeActivityProcessor(conf *worker.Config, dynConf *genconf3.Config, migrationConfig *genconf2.PgdbConn, db types.UsstocksAlpacaPGDB, maxPageSize uint32, vgUSStocksClientWithInterceptors types2.VgStocksClientWithInterceptors, accManagerClient account.AccountManagerClient, savingsClient savings.SavingsClient, actorClient actor.ActorClient, onboardingClient wealthonboarding.WealthOnboardingClient, celestialClient celestial.CelestialClient, commsClient comms.CommsClient, userClient user.UsersClient, catalogManagerClient catalog.CatalogManagerClient, OrderManagerClient order3.OrderManagerClient, iftClient internationalfundtransfer.InternationalFundTransferClient, invInstrumentPublisher activity.InvestmentInstrumentEventPublisher, usStocksRedisStore types.USStocksRedisStore, payClient pay.PayClient, relationClient account_pi.AccountPIRelationClient, omsClient order.OrderServiceClient, generatorClient file_generator.FileGeneratorClient, vgAccountStatementClient accounts.AccountsClient, iftVgClient internationalfundtransfer2.InternationalFundTransferClient, accountBalanceClient balance.BalanceClient) *activity.Processor {
	gormDB := GormProvider(db, migrationConfig)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	implOrder := impl.NewOrder(gormDB, domainIdGenerator, maxPageSize)
	orderPgdb := impl.NewOrderPgdb(migrationConfig, db, domainIdGenerator, maxPageSize)
	ordersDao := impl.OrderProvider(migrationConfig, implOrder, orderPgdb)
	stockCrdb := impl2.NewStockCrdb(gormDB, domainIdGenerator)
	stockPgdb := impl2.NewStockPgdb(migrationConfig, db, domainIdGenerator)
	stockDao := impl2.StockProvider(migrationConfig, stockCrdb, stockPgdb)
	stocksClient := types2.VgStocksClientProvider(vgUSStocksClientWithInterceptors)
	accountCrdb := impl3.NewAccountCrdb(gormDB, domainIdGenerator)
	accountPgdb := impl3.NewAccountPgdb(migrationConfig, db, domainIdGenerator)
	accountDao := impl3.AccountDaoProvider(migrationConfig, accountCrdb, accountPgdb)
	investorCrdb := impl3.NewInvestorCrdb(gormDB, domainIdGenerator)
	investorPgdb := impl3.NewInvestorPgdb(migrationConfig, db, domainIdGenerator)
	investorDao := impl3.InvestorDaoProvider(migrationConfig, investorCrdb, investorPgdb)
	manualReviewCrdb := impl3.NewManualReviewCrdb(gormDB)
	manualReviewPgdb := impl3.NewManualReviewPgdb(migrationConfig, db)
	manualReviewDao := impl3.ManualReviewDaoProvider(migrationConfig, manualReviewCrdb, manualReviewPgdb)
	iDataManagerFactory := OnboardingDataManagerFactoryProvider(db, migrationConfig, investorDao, accountDao, manualReviewDao, iftClient, celestialClient, onboardingClient)
	doOnce := once.NewDoOnce(gormDB)
	service := notifications.NewService(commsClient, actorClient, userClient, doOnce)
	cacheStorage := NewUSStocksRedisCache(usStocksRedisStore)
	positionsCacheRedis := portfolio2.NewPositionsCacheRedis(stocksClient, cacheStorage)
	stockDividendReceived := notificationgetter.NewStockDividendReceived(conf)
	stockDividendGstDebited := notificationgetter.NewStockDividendGstDebited(conf)
	stockDividendGstRefundInitiated := notificationgetter.NewStockDividendGstRefundInitiated(conf, omsClient)
	stockDividendGstRefunded := notificationgetter.NewStockDividendGstRefunded(conf, omsClient)
	aggDividendGstRefundInitiated := notificationgetter.NewAggDividendGstRefundInitiated(conf, omsClient)
	aggDividendGstRefunded := notificationgetter.NewAggDividendGstRefunded(conf, omsClient)
	notificationDataGetterFactory := notificationgetter.NewNotificationDataGetterFactory(stockDividendReceived, stockDividendGstDebited, stockDividendGstRefundInitiated, stockDividendGstRefunded, aggDividendGstRefundInitiated, aggDividendGstRefunded)
	refundRequestCrdb := impl4.NewRefundRequestCrdb(gormDB, domainIdGenerator)
	refundRequestPgdb := impl4.NewRefundRequestPgdb(migrationConfig, db, domainIdGenerator)
	refundRequestDao := impl4.RefundRequestProvider(migrationConfig, refundRequestCrdb, refundRequestPgdb)
	transactionRemittanceRequest := impl.NewTransactionRemittanceRequest(gormDB, maxPageSize)
	transactionRemittanceRequestPgdb := impl.NewTransactionRemittanceRequestPgdb(migrationConfig, db, maxPageSize)
	transactionRemittanceRequestDao := impl.TransactionRemittanceRequestProvider(migrationConfig, transactionRemittanceRequest, transactionRemittanceRequestPgdb)
	remittanceProcess := impl.NewRemittanceProcess(gormDB, domainIdGenerator)
	remittanceProcessPgdb := impl.NewRemittanceProcessPgdb(migrationConfig, db, domainIdGenerator)
	remittanceProcessDao := impl.RemittanceProcessProvider(migrationConfig, remittanceProcess, remittanceProcessPgdb)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	walletOrder := impl.NewWalletOrder(gormDB, domainIdGenerator, maxPageSize)
	walletOrderPgdb := impl.NewWalletOrderPgdb(migrationConfig, db, domainIdGenerator, maxPageSize)
	walletOrderDao := impl.WalletOrderProvider(migrationConfig, walletOrder, walletOrderPgdb)
	aggregateRemittanceTransactionPageSize := types3.AggregateRemittanceTransactionPageSizeProvider(maxPageSize)
	aggregatedRemittanceTransaction := impl.NewAggregatedRemittanceTransaction(gormDB, domainIdGenerator, aggregateRemittanceTransactionPageSize)
	aggregatedRemittanceTransactionPgdb := impl.NewAggregatedRemittanceTransactionPgdb(migrationConfig, db, domainIdGenerator, aggregateRemittanceTransactionPageSize)
	aggregatedRemittanceTransactionDao := impl.AggregatedRemittanceTransactionProvider(migrationConfig, aggregatedRemittanceTransaction, aggregatedRemittanceTransactionPgdb)
	inwardRemittanceFirmAccountId := types3.InwardRemittanceFirmAccountIdProvider(conf)
	remittanceProcessorImpl := remittance.NewRemittanceProcessorImpl(transactionRemittanceRequestDao, aggregatedRemittanceTransactionDao, stocksClient, inwardRemittanceFirmAccountId, iftClient)
	suitabilityLedgerPgdb := impl3.NewSuitabilityLedgerPgdb(migrationConfig, db, domainIdGenerator)
	suitabilityLedgerDao := impl3.SuitabilityLedgerDaoProvider(suitabilityLedgerPgdb)
	client := usStocksSlackAlertClientProvider(conf)
	rewardRequestDaoImpl := dao2.NewRewardRequestDaoImpl(migrationConfig, db, domainIdGenerator)
	collectionDaoCrdb := impl2.NewCollectionDaoCrdb(gormDB, domainIdGenerator)
	collectionDaoPgdb := impl2.NewCollectionDaoPgdb(migrationConfig, db, domainIdGenerator)
	collectionDao := impl2.CollectionDaoProvider(migrationConfig, collectionDaoCrdb, collectionDaoPgdb)
	collectionStockMappingDaoCrdb := impl2.NewCollectionStockMappingDaoCrdb(gormDB)
	collectionStockMappingDaoPgdb := impl2.NewCollectionStockMappingDaoPgdb(migrationConfig, db)
	collectionStockMappingDao := impl2.CollectionStockMappingDaoProvider(migrationConfig, collectionStockMappingDaoCrdb, collectionStockMappingDaoPgdb)
	baseCatalogProcessor := taskProcessor.NewBaseCatalogProcessor(collectionDao, collectionStockMappingDao, gormTxnExecutor)
	processTopTradedAmongLeadingMarketCap := taskProcessor.NewProcessTopTradedAmongLeadingMarketCap(stockDao, catalogManagerClient, baseCatalogProcessor)
	processTopGainersAcrossAllIndex := taskProcessor.NewProcessTopGainersAcrossAllIndex(catalogManagerClient, baseCatalogProcessor, OrderManagerClient)
	processTopLosersAcrossAllIndex := taskProcessor.NewProcessTopLosersAcrossAllIndex(catalogManagerClient, baseCatalogProcessor, OrderManagerClient)
	taskProcessorFactory := taskProcessor.NewTaskProcessorFactory(processTopTradedAmongLeadingMarketCap, processTopGainersAcrossAllIndex, processTopLosersAcrossAllIndex)
	processor := activity.NewProcessor(conf, dynConf, ordersDao, stockDao, stocksClient, accountDao, accManagerClient, savingsClient, actorClient, investorDao, onboardingClient, celestialClient, iDataManagerFactory, service, catalogManagerClient, OrderManagerClient, iftClient, invInstrumentPublisher, positionsCacheRedis, payClient, relationClient, omsClient, notificationDataGetterFactory, refundRequestDao, transactionRemittanceRequestDao, remittanceProcessDao, gormTxnExecutor, generatorClient, walletOrderDao, remittanceProcessorImpl, aggregatedRemittanceTransactionDao, suitabilityLedgerDao, client, rewardRequestDaoImpl, taskProcessorFactory, vgAccountStatementClient, iftVgClient, accountBalanceClient, userClient)
	return processor
}

func InitialiseEventConsumer(db types.UsstocksAlpacaPGDB, genConf *genconf.Config, ussOrderMgClient order3.OrderManagerClient, processAccountActivitySyncPublisher types3.ProcessAccountActivitySyncPublisher, celestialClient celestial.CelestialClient, iftClient internationalfundtransfer.InternationalFundTransferClient) *consumer2.EventConsumerService {
	pgdbConn := pgdbMigrationConfigProvider(genConf)
	gormDB := GormProvider(db, pgdbConn)
	accountActivitySyncCursor := impl.NewAccountActivitySyncCursor(gormDB)
	accountActivitySyncCursorPgdb := impl.NewAccountActivitySyncCursorPgdb(pgdbConn, db)
	accountActivitySyncCursorDao := impl.AccountActivitySyncCursorProvider(pgdbConn, accountActivitySyncCursor, accountActivitySyncCursorPgdb)
	dividendCreditProcessor := orderupdateprocessor.NewDividendCreditProcessor(celestialClient, iftClient)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	uint32_2 := MaxPageSizeProvider(genConf)
	aggregateRemittanceTransactionPageSize := types3.AggregateRemittanceTransactionPageSizeProvider(uint32_2)
	aggregatedRemittanceTransaction := impl.NewAggregatedRemittanceTransaction(gormDB, domainIdGenerator, aggregateRemittanceTransactionPageSize)
	aggregatedRemittanceTransactionPgdb := impl.NewAggregatedRemittanceTransactionPgdb(pgdbConn, db, domainIdGenerator, aggregateRemittanceTransactionPageSize)
	aggregatedRemittanceTransactionDao := impl.AggregatedRemittanceTransactionProvider(pgdbConn, aggregatedRemittanceTransaction, aggregatedRemittanceTransactionPgdb)
	transactionRemittanceRequest := impl.NewTransactionRemittanceRequest(gormDB, uint32_2)
	transactionRemittanceRequestPgdb := impl.NewTransactionRemittanceRequestPgdb(pgdbConn, db, uint32_2)
	transactionRemittanceRequestDao := impl.TransactionRemittanceRequestProvider(pgdbConn, transactionRemittanceRequest, transactionRemittanceRequestPgdb)
	aggregatedInwardRemittanceProcessor := orderupdateprocessor.NewAggregatedInwardRemittanceProcessor(celestialClient, iftClient, aggregatedRemittanceTransactionDao, transactionRemittanceRequestDao)
	orderUpdateProcessorFactory := orderupdateprocessor.NewOrderUpdateProcessorFactory(dividendCreditProcessor, aggregatedInwardRemittanceProcessor)
	eventConsumerService := consumer2.NewEventConsumerService(genConf, accountActivitySyncCursorDao, ussOrderMgClient, processAccountActivitySyncPublisher, orderUpdateProcessorFactory)
	return eventConsumerService
}

func InitialiseAccountConsumer(genConf *genconf.Config, db types.UsstocksAlpacaPGDB, usStocksSendMailToUsersPublisher types3.UsStocksSendMailToUsersPublisher, vgUSStocksClientWithInterceptors types2.VgStocksClientWithInterceptors, actorClient actor.ActorClient, commsClient comms.CommsClient, userClient user.UsersClient) *consumer3.Service {
	pgdbConn := pgdbMigrationConfigProvider(genConf)
	gormDB := GormProvider(db, pgdbConn)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	investorCrdb := impl3.NewInvestorCrdb(gormDB, domainIdGenerator)
	investorPgdb := impl3.NewInvestorPgdb(pgdbConn, db, domainIdGenerator)
	investorDao := impl3.InvestorDaoProvider(pgdbConn, investorCrdb, investorPgdb)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	uint32_2 := MaxPageSizeProvider(genConf)
	implOrder := impl.NewOrder(gormDB, domainIdGenerator, uint32_2)
	orderPgdb := impl.NewOrderPgdb(pgdbConn, db, domainIdGenerator, uint32_2)
	ordersDao := impl.OrderProvider(pgdbConn, implOrder, orderPgdb)
	stocksClient := types2.VgStocksClientProvider(vgUSStocksClientWithInterceptors)
	accountCrdb := impl3.NewAccountCrdb(gormDB, domainIdGenerator)
	accountPgdb := impl3.NewAccountPgdb(pgdbConn, db, domainIdGenerator)
	accountDao := impl3.AccountDaoProvider(pgdbConn, accountCrdb, accountPgdb)
	doOnce := once.NewDoOnce(gormDB)
	service := notifications.NewService(commsClient, actorClient, userClient, doOnce)
	consumerService := consumer3.NewService(genConf, investorDao, gormTxnExecutor, ordersDao, usStocksSendMailToUsersPublisher, stocksClient, accountDao, service, doOnce)
	return consumerService
}

func InitializeDBStateService(db types.UsstocksAlpacaPGDB, genConf *genconf.Config, client celestial.CelestialClient) *developer.DBStateService {
	pgdbConn := pgdbMigrationConfigProvider(genConf)
	gormDB := types.UsstocksAlpacaCRDBGormDBProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	stockCrdb := impl2.NewStockCrdb(gormDB, domainIdGenerator)
	stockPgdb := impl2.NewStockPgdb(pgdbConn, db, domainIdGenerator)
	stockDao := impl2.StockProvider(pgdbConn, stockCrdb, stockPgdb)
	stocksProcessor := processor.NewStockProcessor(stockDao)
	marketCategoryCrdb := impl2.NewMarketCategoryCrdb(gormDB, domainIdGenerator)
	marketCategoryPgdb := impl2.NewMarketCategoryPgdb(pgdbConn, db, domainIdGenerator)
	marketCategoryDao := impl2.MarketCategoryProvider(pgdbConn, marketCategoryCrdb, marketCategoryPgdb)
	marketCategoriesProcessor := processor.NewMarketCategoriesProcessor(marketCategoryDao)
	collectionDaoCrdb := impl2.NewCollectionDaoCrdb(gormDB, domainIdGenerator)
	collectionDaoPgdb := impl2.NewCollectionDaoPgdb(pgdbConn, db, domainIdGenerator)
	collectionDao := impl2.CollectionDaoProvider(pgdbConn, collectionDaoCrdb, collectionDaoPgdb)
	collectionsProcessor := processor.NewCollectionsProcessor(collectionDao)
	collectionStockMappingDaoCrdb := impl2.NewCollectionStockMappingDaoCrdb(gormDB)
	collectionStockMappingDaoPgdb := impl2.NewCollectionStockMappingDaoPgdb(pgdbConn, db)
	collectionStockMappingDao := impl2.CollectionStockMappingDaoProvider(pgdbConn, collectionStockMappingDaoCrdb, collectionStockMappingDaoPgdb)
	collectionStockMappingsProcessor := processor.NewCollectionStockMappingsProcessor(collectionStockMappingDao)
	investorCrdb := impl3.NewInvestorCrdb(gormDB, domainIdGenerator)
	investorPgdb := impl3.NewInvestorPgdb(pgdbConn, db, domainIdGenerator)
	investorDao := impl3.InvestorDaoProvider(pgdbConn, investorCrdb, investorPgdb)
	investorsProcessor := processor.NewInvestorsProcessor(investorDao)
	accountCrdb := impl3.NewAccountCrdb(gormDB, domainIdGenerator)
	accountPgdb := impl3.NewAccountPgdb(pgdbConn, db, domainIdGenerator)
	accountDao := impl3.AccountDaoProvider(pgdbConn, accountCrdb, accountPgdb)
	accountsProcessor := processor.NewAccountsProcessor(accountDao)
	uint32_2 := MaxPageSizeProvider(genConf)
	implOrder := impl.NewOrder(gormDB, domainIdGenerator, uint32_2)
	orderPgdb := impl.NewOrderPgdb(pgdbConn, db, domainIdGenerator, uint32_2)
	ordersDao := impl.OrderProvider(pgdbConn, implOrder, orderPgdb)
	ordersProcessor := processor.NewOrdersProcessor(ordersDao)
	accountSummaryProcessor := processor.NewAccountSummaryProcessor(accountDao, investorDao, client)
	walletOrder := impl.NewWalletOrder(gormDB, domainIdGenerator, uint32_2)
	walletOrderPgdb := impl.NewWalletOrderPgdb(pgdbConn, db, domainIdGenerator, uint32_2)
	walletOrderDao := impl.WalletOrderProvider(pgdbConn, walletOrder, walletOrderPgdb)
	walletOrderProcessor := processor.NewWalletOrderProcessor(walletOrderDao)
	remittanceProcess := impl.NewRemittanceProcess(gormDB, domainIdGenerator)
	remittanceProcessPgdb := impl.NewRemittanceProcessPgdb(pgdbConn, db, domainIdGenerator)
	remittanceProcessDao := impl.RemittanceProcessProvider(pgdbConn, remittanceProcess, remittanceProcessPgdb)
	remittanceProcessProcessor := processor.NewRemittanceProcessProcessor(remittanceProcessDao)
	transactionRemittanceRequest := impl.NewTransactionRemittanceRequest(gormDB, uint32_2)
	transactionRemittanceRequestPgdb := impl.NewTransactionRemittanceRequestPgdb(pgdbConn, db, uint32_2)
	transactionRemittanceRequestDao := impl.TransactionRemittanceRequestProvider(pgdbConn, transactionRemittanceRequest, transactionRemittanceRequestPgdb)
	transactionRemittanceRequestProcessor := processor.NewTransactionRemittanceRequestProcessor(transactionRemittanceRequestDao)
	suitabilityLedgerPgdb := impl3.NewSuitabilityLedgerPgdb(pgdbConn, db, domainIdGenerator)
	suitabilityLedgerDao := impl3.SuitabilityLedgerDaoProvider(suitabilityLedgerPgdb)
	suitabilityLedgerProcessor := processor.NewSuitabilityLedgerProcessor(suitabilityLedgerDao)
	aggregateRemittanceTransactionPageSize := types3.AggregateRemittanceTransactionPageSizeProvider(uint32_2)
	aggregatedRemittanceTransaction := impl.NewAggregatedRemittanceTransaction(gormDB, domainIdGenerator, aggregateRemittanceTransactionPageSize)
	aggregatedRemittanceTransactionPgdb := impl.NewAggregatedRemittanceTransactionPgdb(pgdbConn, db, domainIdGenerator, aggregateRemittanceTransactionPageSize)
	aggregatedRemittanceTransactionDao := impl.AggregatedRemittanceTransactionProvider(pgdbConn, aggregatedRemittanceTransaction, aggregatedRemittanceTransactionPgdb)
	aggRemittanceTxnProcessor := processor.NewAggRemittanceTxnProcessor(aggregatedRemittanceTransactionDao)
	manualReviewCrdb := impl3.NewManualReviewCrdb(gormDB)
	manualReviewPgdb := impl3.NewManualReviewPgdb(pgdbConn, db)
	manualReviewDao := impl3.ManualReviewDaoProvider(pgdbConn, manualReviewCrdb, manualReviewPgdb)
	manualReviewsProcessor := processor.NewManualReviewsProcessor(manualReviewDao)
	watchlistDaoCrdb := impl2.NewWatchlistDaoCrdb(gormDB, domainIdGenerator)
	watchlistDaoPgdb := impl2.NewWatchlistDaoPgdb(pgdbConn, db, domainIdGenerator)
	watchlistDao := impl2.WatchlistDaoProvider(pgdbConn, watchlistDaoCrdb, watchlistDaoPgdb)
	watchlistStockMappingDaoCrdb := impl2.NewWatchlistStockMappingDaoCrdb(gormDB)
	watchlistStockMappingDaoPgdb := impl2.NewWatchlistStockMappingDaoPgdb(pgdbConn, db)
	watchlistStockMappingDao := impl2.WatchlistStockMappingDaoProvider(pgdbConn, watchlistStockMappingDaoCrdb, watchlistStockMappingDaoPgdb)
	watchlistsWithStockMappingsProcessor := processor.NewWatchlistsWithStockMappingsProcessor(watchlistDao, watchlistStockMappingDao)
	devFactory := developer.NewDevFactory(stocksProcessor, marketCategoriesProcessor, collectionsProcessor, collectionStockMappingsProcessor, investorsProcessor, accountsProcessor, ordersProcessor, accountSummaryProcessor, walletOrderProcessor, remittanceProcessProcessor, transactionRemittanceRequestProcessor, suitabilityLedgerProcessor, aggRemittanceTxnProcessor, manualReviewsProcessor, watchlistsWithStockMappingsProcessor)
	dbStateService := developer.NewDBStateService(devFactory)
	return dbStateService
}

func InitialiseConsumer(genConf *genconf.Config, celestialClient celestial.CelestialClient, db types.UsstocksAlpacaPGDB, eventPublisher types3.NonFinancialEventSqsPublisher, eventBroker events.Broker, investmentAggClient aggregator.InvestmentAggregatorClient, catalogMgClient catalog.CatalogManagerClient, employmentClient employment.EmploymentClient, userClient user.UsersClient, usStocksTemporalClient types3.USStocksClient) *consumer4.Service {
	pgdbConn := pgdbMigrationConfigProvider(genConf)
	gormDB := GormProvider(db, pgdbConn)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	remittanceProcess := impl.NewRemittanceProcess(gormDB, domainIdGenerator)
	remittanceProcessPgdb := impl.NewRemittanceProcessPgdb(pgdbConn, db, domainIdGenerator)
	remittanceProcessDao := impl.RemittanceProcessProvider(pgdbConn, remittanceProcess, remittanceProcessPgdb)
	ussAccountActivatedProcessor := workflow_update_processor.NewUssAccountActivatedProcessor(eventBroker, eventPublisher)
	accountCrdb := impl3.NewAccountCrdb(gormDB, domainIdGenerator)
	accountPgdb := impl3.NewAccountPgdb(pgdbConn, db, domainIdGenerator)
	accountDao := impl3.AccountDaoProvider(pgdbConn, accountCrdb, accountPgdb)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	ussAccountCreationInitiatedAtVendorProcessor := workflow_update_processor.NewUssAccountCreationInitiatedAtVendorProcessor(accountDao, gormTxnExecutor, celestialClient)
	uint32_2 := MaxPageSizeProvider(genConf)
	implOrder := impl.NewOrder(gormDB, domainIdGenerator, uint32_2)
	orderPgdb := impl.NewOrderPgdb(pgdbConn, db, domainIdGenerator, uint32_2)
	ordersDao := impl.OrderProvider(pgdbConn, implOrder, orderPgdb)
	ussBuyOrderSuccessfulProcessor := workflow_update_processor.NewUSSBuyOrderSuccessfulProcessor(ordersDao, investmentAggClient, catalogMgClient)
	v := workflow_update_processor.NewWorkflowUpdateProcessors(ussAccountActivatedProcessor, ussAccountCreationInitiatedAtVendorProcessor, ussBuyOrderSuccessfulProcessor)
	multiOptionSelector := list_selector.NewMultiOptionSelector(genConf)
	singleOptionSelector := list_selector.NewSingleOptionSelector(genConf)
	autoFetchDateOfBirth := auto_fetch_data_collector.NewAutoFetchDateOfBirth(userClient)
	autoFetchAnnualIncomeRange := auto_fetch_data_collector.NewAutoFetchAnnualIncomeRange(employmentClient)
	autoFetchOptionSelector := suitability_questions.NewAutoFetchOption(genConf, autoFetchDateOfBirth, autoFetchAnnualIncomeRange)
	suitabilityQuestionManager := suitability_questions.NewQuestionManager(genConf, multiOptionSelector, singleOptionSelector, autoFetchOptionSelector)
	suitabilityLedgerPgdb := impl3.NewSuitabilityLedgerPgdb(pgdbConn, db, domainIdGenerator)
	suitabilityLedgerDao := impl3.SuitabilityLedgerDaoProvider(suitabilityLedgerPgdb)
	client := types3.USStocksClientProvider(usStocksTemporalClient)
	service := consumer4.NewService(genConf, celestialClient, remittanceProcessDao, v, suitabilityQuestionManager, suitabilityLedgerDao, client)
	return service
}

func InitialiseDynamicElementManagerService(genConf *genconf.Config, dynamicElementSvc dynamic_ui_element.DynamicUIElementServiceClient, catalogClient catalog.CatalogManagerClient, portfolioClient portfolio.PortfolioManagerClient, actorClient actor.ActorClient, usersClient user.UsersClient, userGrpClient group.GroupClient) *dynamic_elements.Service {
	ussLandingDynamicElementsGetter := dynamic_elements_getter.NewUSSLandingDynamicElementsGetter(genConf, dynamicElementSvc)
	walletPageDynamicElementsGetter := dynamic_elements_getter.NewWalletPageDynamicElementsGetter(genConf)
	featureReleaseConfig := ReleaseConfigProvider(genConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	ussHomeDynamicElementsGetter := dynamic_elements_getter.NewUSSHomeDynamicElementsGetter(genConf, dynamicElementSvc, catalogClient, portfolioClient, evaluator)
	dynamicElementsGetterFactory := dynamic_elements.NewDynamicElementsGetterFactory(ussLandingDynamicElementsGetter, walletPageDynamicElementsGetter, ussHomeDynamicElementsGetter)
	service := dynamic_elements.NewService(dynamicElementsGetterFactory)
	return service
}

func InitialiseUssRewardManagerService(genConf *genconf.Config, db types.UsstocksAlpacaPGDB, rewardClient rewards.RewardsGeneratorClient, celestialClient celestial.CelestialClient) *rewards2.Service {
	pgdbConn := pgdbMigrationConfigProvider(genConf)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	rewardRequestDaoImpl := dao2.NewRewardRequestDaoImpl(pgdbConn, db, domainIdGenerator)
	gormDB := types.UsstocksAlpacaCRDBGormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	service := rewards2.NewService(genConf, rewardRequestDaoImpl, rewardClient, celestialClient, gormTxnExecutor)
	return service
}

func InitializeTaxConsumerService(genConf *genconf.Config, db types.UsstocksAlpacaPGDB, taxS3Client types3.TaxDocumentsS3Client, ussAccountClient account.AccountManagerClient, vgStocksClient stocks.StocksClient, ussCatalogMgrClient catalog.CatalogManagerClient, usStocksRedisStore types.USStocksRedisStore) *consumer5.Service {
	buySellProcessor := activitytransformer.NewBuySellProcessor()
	dividendProcessor := activitytransformer.NewDividendProcessor()
	dividendAdjustmentProcessor := activitytransformer.NewDividendAdjustmentProcessor()
	interestProcessor := activitytransformer.NewInterestProcessor()
	cashProcessor := activitytransformer.NewCashProcessor()
	feeProcessor := activitytransformer.NewFeeProcessor()
	corporateActionsFetcher := utils.NewCorporateActionsFetcher(genConf, vgStocksClient)
	stockSplitProcessor := activitytransformer.NewStockSplitProcessor(corporateActionsFetcher)
	stockNameChangeProcessor := activitytransformer.NewStockNameChangeProcessor(corporateActionsFetcher)
	stockSpinOffProcessor := activitytransformer.NewStockSpinOffProcessor(corporateActionsFetcher)
	acquisitionProcessor := activitytransformer.NewAcquisitionProcessor(vgStocksClient)
	activityTransformer := activitytransformer.NewProcessor(buySellProcessor, dividendProcessor, dividendAdjustmentProcessor, interestProcessor, cashProcessor, feeProcessor, stockSplitProcessor, stockNameChangeProcessor, stockSpinOffProcessor, acquisitionProcessor)
	costInflationIndexProviderImpl := utils.NewCostInflationIndexProviderImpl(genConf)
	sbiExchangeRateProviderImpl := utils.NewSBIExchangeRateProviderImpl(genConf)
	transactionProcessor := transactionprocessor.NewTransactionProcessor(genConf, costInflationIndexProviderImpl, sbiExchangeRateProviderImpl, ussCatalogMgrClient)
	cacheStorage := NewUSStocksRedisCache(usStocksRedisStore)
	paramsGeneratorFactoryImpl := param_generator.NewParamsGeneratorFactory(ussAccountClient, transactionProcessor, sbiExchangeRateProviderImpl, cacheStorage)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	taxDocumentParamsDaoPgdb := dao3.NewTaxDocumentParamsDaoPgdb(db, domainIdGenerator)
	paramsGenerator := document_params.NewParamsGenerator(paramsGeneratorFactoryImpl, taxDocumentParamsDaoPgdb)
	taxDocumentDaoPgdb := dao3.NewTaxDocumentDaoPgdb(db, domainIdGenerator)
	gormDB := types.UsstocksAlpacaCRDBGormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	defaultTime := datetime.NewDefaultTime()
	capitalGainsGenerator := generator.NewCapitalGainsGenerator()
	form67Generator := generator.NewForm67Generator()
	scheduleFaGenerator := generator.NewScheduleFaGenerator()
	scheduleFSIGenerator := generator.NewScheduleFSIGenerator()
	scheduleTrGenerator := generator.NewScheduleTrGenerator()
	documentProcessor := document.NewProcessor(taxDocumentDaoPgdb, taxDocumentParamsDaoPgdb, gormTxnExecutor, defaultTime, taxS3Client, capitalGainsGenerator, form67Generator, scheduleFaGenerator, scheduleFSIGenerator, scheduleTrGenerator)
	service := consumer5.NewService(ussAccountClient, vgStocksClient, activityTransformer, paramsGenerator, documentProcessor)
	return service
}

func InitializeUssTaxService(db types.UsstocksAlpacaPGDB, ussAccountMgrClient account.AccountManagerClient, commsClient comms.CommsClient, userClient user.UsersClient, s3Client types3.TaxDocumentsS3Client) *tax.UssTaxService {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	taxDocumentDaoPgdb := dao3.NewTaxDocumentDaoPgdb(db, domainIdGenerator)
	ussTaxService := tax.NewUssTaxService(taxDocumentDaoPgdb, ussAccountMgrClient, commsClient, userClient, s3Client)
	return ussTaxService
}

// wire.go:

func pgdbMigrationConfigProvider(conf *genconf.Config) *genconf2.PgdbConn {
	return conf.PgdbMigrationConf()
}

func GormProvider(db types.UsstocksAlpacaPGDB, pgdbMigrationConf *genconf2.PgdbConn) *gorm.DB {
	var gormDb *gorm.DB = db

	if pgdbMigrationConf.PgdbConnAlias() != "" && pgdbMigrationConf.UsePgdb() {
		gormDb = gormDb.Clauses(dbresolver.Use(pgdbMigrationConf.PgdbConnAlias()))
	}
	return gormDb
}

func MaxPageSizeProvider(conf *genconf.Config) uint32 { return conf.MaxPageSize() }

func PriceUpdateRateLimiterConfigProvider(conf *genconf.Config) *genconf2.RateLimitConfig {
	return conf.PriceUpdateRateLimiterConfig()
}

func DummyRateLimiterOptionsProvider() []ratelimiter.Option {
	return nil
}

// https://epifi.slack.com/archives/C01993EFXDZ/p1675854285086199
// This function is not written by following correct wire patterns. We should ideally use wireset like RedisStorageWireSet to inject the dependency.
// But this couldn't be achieved since we require 2 redis client as parameters. So they couldn't be mapped correctly as per requirements.
// You can read more about this issue in the above slack thread.
func NewUSStocksRedisCache(cl types.USStocksRedisStore) cache.CacheStorage {
	return cache.NewRedisCacheStorage(types.USStocksRedisStoreRedisClientProvider(cl))
}

func LambdaClientProvider(awsConf aws.Config) lambda.LambdaClient {
	return lambda.NewAwsLambda(awsConf)
}

func ReleaseConfigProvider(genConf *genconf.Config) *genconf4.FeatureReleaseConfig {
	return genConf.FeatureReleaseConfig()
}

func usStocksSlackAlertClientProvider(conf *worker.Config) *slack.Client {
	var slackClient *slack.Client
	if cfg.IsProdEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[worker.UsStocksSlackBotOauthToken], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[worker.UsStocksSlackBotOauthToken], slack.OptionDebug(true))
	}
	return slackClient
}

func usStocksSlackAlertClientProviderForServer(genConf *genconf.Config) *slack.Client {
	var slackClient *slack.Client
	if cfg.IsProdEnv(genConf.Application().Environment) {
		slackClient = slack.New(genConf.Secrets().Ids[worker.UsStocksSlackBotOauthToken], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(genConf.Application().Environment) {
		slackClient = slack.New(genConf.Secrets().Ids[worker.UsStocksSlackBotOauthToken], slack.OptionDebug(true))
	}
	return slackClient
}
