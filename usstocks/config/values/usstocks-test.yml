Application:
  Environment: "test"
  Name: "usstocks"

Server:
  Ports:
    GrpcPort: 8110
    GrpcSecurePort: 9526
    HttpPort: 9999

USStocksAlpacaDb:
  DbType: "CRDB"
  AppName: "usstocks"
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "usstocks_alpaca_test"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "usstocks_alpaca_pgdb"
        DbDsn:
          DbType: "PGDB"
          Host: "localhost"
          Port: 5432
          Username: "root"
          Name: "usstocks_alpaca_test"
          SSLMode: "disable"
          SecretName: "{\"username\": \"root\", \"password\": \"\"}"


AWS:
  Region: "ap-south-1"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"
    ZincCredentials: "{\"username\": \"admin\", \"password\": \"Complexpass#123\"}"
    UsStocksSlackBotOauthToken: "dummy/ift/slack-bot-oauth-token"

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

ProcessUSStockCatalogUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "usstock-process-catalog-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

ProcessUSEtfCatalogUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "usetf-process-catalog-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

ProcessOrderUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "usstocks-oms-order-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"

ProcessAccountActivitySyncSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "usstocks-account-activity-sync-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

ProcessAccountActivitySyncPublisher:
  QueueName: "usstocks-account-activity-sync-queue"

UsStocksSendMailToUsersPublisher:
  QueueName: "usstocks-send-mail-to-users-queue"

UsStocksSendMailToUsersSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "usstocks-send-mail-to-users-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"


PriceUpdatesConnectionInfo:
  Enabled: false
  VGConnectionRetryInterval: "1s" # retry after 30 seconds of VG connection failure
  # no of retries are intentionally kept higher for handling first prod push where gap between vg and investment server deployment can be more than 30 mins.
  # this should be revisited and changed to some smaller number eg: 20
  VGConnectionMaxRetries: 1  # retry for max 100 times after every 30 seconds. connection retry happens for ~ 50 mins

OrderUpdateEventsConnectionInfo:
  Enable: false
  VGConnectionRetryInterval: "10s" # retry after 10 seconds of VG connection failure
  VGConnectionMaxRetries: 100

AccountUpdateEventsConnectionInfo:
  Enable: false
  VGConnectionRetryInterval: "10s" # retry after 10 seconds of VG connection failure
  VGConnectionMaxRetries: 100

LivenessS3BucketName: "epifi-liveness"

FaceMatchThreshold: 30

AmlActionEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "usstocks-aml-action-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

CelestialWorkflowUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "usstocks-celestial-wf-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

USStockCatalogRefreshPublisher:
  QueueName: "usstocks-catalog-refresh-queue"

USStockCatalogRefreshSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "usstocks-catalog-refresh-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"

CommsEmail:
  FromEmailId:
    EmailId: "<EMAIL>"
    EmailName: "Fi Money"
  AlpacaEmail:
    EmailId: "<EMAIL>"
    EmailName: "Fi Money"

VendorMappingRedisOptions:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0

USStocksRedisOptions:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0

SkipPanValidation: false

USStocksIFTRemittanceFileProcessingEventsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "usstocks-remittance-file-processing-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

BrokerFirmAccountDetailsForForeignRemittance:
  OutwardRemittanceAccount:
    AccountId: "OutwardFirmAccountId"
  InwardRemittanceAccount:
    AccountId: "InwardFirmAccountId"
    BankRelationshipId: "BankRelationshipId"
  RewardsFirmAccount:
    AccountId: "RewardsFirmAccountId"

JournalUpdateEventsConnectionInfo:
  Enable: false
  VGConnectionRetryInterval: "10s" # retry after 10 seconds of VG connection failure
  VGConnectionMaxRetries: 100

FundTransferUpdateEventsConnectionInfo:
  Enable: false
  VGConnectionRetryInterval: "10s" # retry after 10 seconds of VG connection failure
  VGConnectionMaxRetries: 100

CatalogS3Conf:
  BucketName: "epifi-usstocks"

MorningStarS3Bucket:
  BucketName: "epifi-usstocks-morningstar-u"

AccountManagerConfig:
  KycDocumentsBucketName: "epifi-usstocks"

NonFinancialEventSqsPublisher:
  QueueName: "non-financial-investment-event-queue"

PgdbMigrationConf:
  UsePgdb: true

# Scores for options are different in test environment for easier unit testing
SuitabilityQuestionConfigFilePath: "mappingJson/suitability_question_config_test.json"

GetStocksPriceSnapshotsBatchConfig:
  BatchSize: 5
  JitterSeconds: 4

USStocksRewardDetails:
  Option1StockName: "Apple"
  Option2StockName: "Tesla"
  Amount:
    CurrencyCode: "INR"
    Units: 100
  BgLottieUrl: "https://epifi-icons.pointz.in/usstocks_images/USS-Landing-Promo.json"

VendorAccountActivitiesBucketName: "epifi-usstocks"

# this was added to map expected objects in UT
MarketIndexConfig:
  - Nasdaq100:
      DisplayName: "Nasdaq 100"
      ProxyEtf: "QQQM"
      ConstituentSymbols:
        - "PANW"
        - "CEG"
        - "ZS"
        - "MRVL"

Brokerage:
  Enabled: false
  BrokerageInPercentage: 0.25

CreditFrozenActorIds:
  - "sample-credit-frozen-actor-id"

USStocksIncomeUpdateEventsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "usstocks-income-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"

USStocksTaxDocumentGenerationRequestSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "usstocks-tax-document-generation-request-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"

TaxDocumentsBucketName: "epifi-usstocks-alpaca"

Flags:
  EnableModifiedTransactionRemarkForInwardRemittance: true
  # We are setting this flag to false because Temporal's execute async feature does not have a mock available, making testing for this complicated.
  EnableInwardRemittanceGstReportingFlowUsingApi: false

IsNewStockUniverseEnabled: true
