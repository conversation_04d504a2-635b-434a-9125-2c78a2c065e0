package savings

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/samber/lo"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"

	operationalStatusEnums "github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/pkg/frontend/cx"

	"github.com/epifi/be-common/pkg/constants"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	usersPb "github.com/epifi/gamma/api/user"
	feErrors "github.com/epifi/gamma/pkg/frontend/errors"

	"go.uber.org/zap"
)

func AcctClosureNextAction(ctx context.Context, actorId string, accessRvkReason usersPb.AccessRevokeReason, savClient savingsPb.SavingsClient, extAcctClient extacct.ExternalAccountsClient) (*deeplinkPb.Deeplink, *feErrors.ClientError) {
	savingsAccount, cbtEntries, extAcctResp, gatherErr := gatherAccountClosureNextActionData(ctx, actorId, savClient, extAcctClient)
	if gatherErr != nil {
		return nil, gatherErr
	}

	return GetAcctClosureNextActionFromGatheredData(ctx, actorId, accessRvkReason, savingsAccount, cbtEntries, extAcctResp)
}

func gatherAccountClosureNextActionData(ctx context.Context, actorId string, savClient savingsPb.SavingsClient, extAcctClient extacct.ExternalAccountsClient) (*savingsPb.Account, []*savingsPb.ClosedAccountBalanceTransfer, *extacct.GetBankAccountsResponse, *feErrors.ClientError) {
	savResp, cbtResp := getSavAccAndCbt(ctx, actorId, savClient)
	baRes, err := extAcctClient.GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(baRes, err); grpcErr != nil {
		logger.Error(ctx, "error fetching existing external accounts for user", zap.Error(grpcErr))
		return nil, nil, nil, feErrors.NewClientError(feErrors.UNKNOWN_CLIENT_ERR)
	}

	return savResp.GetAccount(), cbtResp.GetEntries(), baRes, nil
}

// in case we were not able to fetch closed account balance transfer data, we should not punish the user
// gracefully handling errors
func getSavAccAndCbt(ctx context.Context, actorId string, savClient savingsPb.SavingsClient) (*savingsPb.GetAccountResponse, *savingsPb.GetClosedAccountBalTransferDataResponse) {
	savResp, savErr := savClient.GetAccount(ctx, &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_ActorId{ActorId: actorId}})
	if savErr != nil || savResp == nil {
		logger.Error(ctx, "error fetching savings account for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(savErr))
		return nil, nil
	}

	// initiate StoreClosedAccountBalTransferDataFromStatement rpc call to get the latest closed account balance data
	// StoreClosedAccountBalTransferDataFromStatement method fetches balance data from statement if balance data is not found in table already
	storeCbtResp, storeCbtErr := savClient.StoreClosedAccountBalTransferDataFromStatement(ctx, &savingsPb.StoreClosedAccountBalTransferDataFromStatementRequest{
		SavingsAccountId: savResp.GetAccount().GetId(),
	})
	if rpcErr := epifigrpc.RPCError(storeCbtResp, storeCbtErr); rpcErr != nil {
		logger.Error(ctx, "error while storing closed account balance transfer data from statement", zap.String(logger.ACCOUNT_ID, savResp.GetAccount().GetId()), zap.Error(rpcErr))
		return nil, nil
	}

	cbtResp, cbtErr := savClient.GetClosedAccountBalTransferData(ctx, &savingsPb.GetClosedAccountBalTransferDataRequest{SavingsAccountId: savResp.GetAccount().GetId()})
	if err := epifigrpc.RPCError(cbtResp, cbtErr); err != nil && !cbtResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error while getting closed accounts balance transfer data", zap.String(logger.ACCOUNT_ID, savResp.GetAccount().GetId()))
		return nil, nil
	}

	return savResp, cbtResp
}

// AcctClosureNextAction evaluates the status of Account closure for a user and returns the appropriate next action
// TODO(https://monorail.pointz.in/p/fi-app/issues/detail?id=63085): make this an RPC
// nolint: funlen
func GetAcctClosureNextActionFromGatheredData(ctx context.Context, actorId string, accessRvkReason usersPb.AccessRevokeReason, savingsAccount *savingsPb.Account, cbtEntries []*savingsPb.ClosedAccountBalanceTransfer, extAccResp *extacct.GetBankAccountsResponse) (*deeplinkPb.Deeplink, *feErrors.ClientError) {
	cbt := GetLatestClosedAccountInfo(cbtEntries)
	switch {
	case cbt.GetReportedClosureBalance() != nil && cbt.GetReportedClosureBalance().GetUnits() <= constants.ClosedAccountBalanceTransferMinThreshold:
		return nil, getReportedBalanceLessThanThresholdResponse()
	case cbt.GetReportedClosureBalance() == nil && cbt.GetLastKnownBalance().GetAvailableBalance() != nil && cbt.GetLastKnownBalance().GetAvailableBalance().GetUnits() <= constants.ClosedAccountBalanceTransferMinThreshold:
		return nil, getLastKnownBalanceLessThanThresholdResponse()
	case cbt.GetBalanceCapturedFromStatement() != nil && cbt.GetBalanceCapturedFromStatement().GetUnits() <= constants.ClosedAccountBalanceTransferMinThreshold:
		return nil, getReportedBalanceLessThanThresholdResponse()
	}

	//  bank account verification is success, money transfer could be in-progress or completed
	if len(extAccResp.GetBankAccounts()) > 0 {
		if cbt.IsBalanceTransferCompleted() {
			for _, bav := range extAccResp.GetBankAccountVerifications() {
				if bav.GetId() == cbt.GetBavId() {
					return getBalanceTransferredWithUtrScreen(ctx, cbt.GetUtr(), bav.GetAccountNumber(), bav.GetIfsc())
				}
			}
		}
		logger.Error(ctx, "cbt alternate account does not belong to user",
			zap.String(logger.ACCOUNT_ID, savingsAccount.GetId()), zap.String(logger.ACTOR_ID_V2, actorId))
		return AcctClosureTransferInitScreen(ctx, accessRvkReason, extAccResp.GetBankAccounts()[0]), nil
	}

	// user has exhausted their retries for adding bank account
	if extAccResp.GetAccVerificationRetriesLeft() <= 0 || extAccResp.GetNameMatchRetriesLeft() <= 0 {
		return nil, feErrors.NewClientError(feErrors.ACCESS_REVOKED_EXT_ACCOUNT_MAX_RETRIES)
	}

	// bank account verification not attempted by the user
	if len(extAccResp.GetBankAccountVerifications()) == 0 {
		return accountClosureLandingScreen(accessRvkReason), nil
	}

	// evaluate & send status of the last attempt
	lastBAV := extAccResp.GetBankAccountVerifications()[0]
	logger.Info(ctx, fmt.Sprintf("deciding next action based on ext acct status: %v", lastBAV.GetOverallStatus()))
	return nextActionFromExtAcctStatus(ctx, lastBAV, accessRvkReason)
}

func GetLatestClosedAccountInfo(cbts []*savingsPb.ClosedAccountBalanceTransfer) *savingsPb.ClosedAccountBalanceTransfer {
	if len(cbts) == 0 {
		return nil
	}
	sort.Slice(cbts, func(i, j int) bool {
		return cbts[i].GetUpdatedAt().AsTime().After(cbts[j].GetUpdatedAt().AsTime())
	})
	return cbts[0]
}

func nextActionFromExtAcctStatus(ctx context.Context, bav *extacct.BankAccountVerification, accessRvkReason usersPb.AccessRevokeReason) (*deeplinkPb.Deeplink, *feErrors.ClientError) {
	switch bav.GetOverallStatus() {
	case extacct.OverallStatus_OVERALL_STATUS_SUCCESS:
		return AcctClosureTransferInitScreen(ctx, accessRvkReason, &extacct.BankAccount{
			Ifsc:          bav.GetIfsc(),
			AccountNumber: bav.GetAccountNumber(),
			Name:          bav.GetNameAtBank(),
		}), nil

	case extacct.OverallStatus_OVERALL_STATUS_FAILURE:
		return accountClosureLandingScreen(accessRvkReason), nil

	case extacct.OverallStatus_OVERALL_STATUS_IN_PROGRESS:
		logger.Info(ctx, "user initiated penny drop is in IN_PROGRESS state", zap.String(logger.ACTOR_ID_V2, bav.GetActorId()),
			zap.Time(logger.UPDATED_AT, bav.GetUpdatedAt().AsTime()))
		if time.Since(bav.GetUpdatedAt().AsTime()) > 5*time.Minute {
			return accountClosureLandingScreen(accessRvkReason), nil
		}
		return nil, feErrors.NewClientError(feErrors.ACCOUNT_VERIFICATION_PENNY_DROP_IN_PROGRESS)

	default:
		logger.Error(ctx, "unknown ext account overall status", zap.String(logger.STATE, bav.GetOverallStatus().String()))
		return nil, feErrors.NewClientError(feErrors.UNKNOWN_CLIENT_ERR)
	}
}

// keeping this value in constant instead of keeping in config since this is used in multiple services
const isFormEnabledForFullKyc = true

func ShouldShowAlternateAccountCollectionForm(accessRevokeReason usersPb.AccessRevokeReason,
	operStatus operationalStatusEnums.OperationalStatus) bool {
	if lo.Contains([]usersPb.AccessRevokeReason{
		usersPb.AccessRevokeReason_ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY,
		usersPb.AccessRevokeReason_ACCESS_REVOKE_REASON_LSO_USER_VKYC_PENDING,
	}, accessRevokeReason) {
		return true
	}

	if isFormEnabledForFullKyc && operStatus == operationalStatusEnums.OperationalStatus_OPERATIONAL_STATUS_CLOSED {
		return true
	}

	return false
}

// accountClosureLandingScreen returns INFO_ACK screen with CTA to trasnfer money
func accountClosureLandingScreen(accessRvkReason usersPb.AccessRevokeReason) *deeplinkPb.Deeplink {
	title := AccessReasonToBalTxnTitle[accessRvkReason]
	subtitle := AccessReasonToBalTxnSubtl[accessRvkReason]
	var cxCtas []*deeplinkPb.Cta
	cxCtas = append(cxCtas, &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CUSTOM,
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
		Text:         constants.AddBankAccountDetails,
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PAY_VIA_BANK_TRANSFER,
			ScreenOptions: &deeplinkPb.Deeplink_PayViaBankTransferScreenOptions{
				PayViaBankTransferScreenOptions: &deeplinkPb.PayViaBankTransferScreenOptions{
					PayViaBankTransferFlow: deeplinkPb.PayViaBankTransferScreenOptions_BANK_TRANSFER_FLOW_MIN_KYC_ACCOUNT_CLOSURE,
				},
			},
		},
	})
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_INFO_ACKNOWLEDGEMENT_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_InfoAcknowledgementScreenOptions{
			InfoAcknowledgementScreenOptions: &deeplinkPb.InfoAcknowledgementScreenOptions{
				ScreenTheme: deeplinkPb.InfoAcknowledgementScreenOptions_SCREEN_THEME_1,
				Title:       title,
				Subtitle:    subtitle,
				Ctas:        cxCtas,
				ScreenContent: &deeplinkPb.InfoAcknowledgementScreenOptions_ScreenContentTheme1_{
					ScreenContentTheme1: &deeplinkPb.InfoAcknowledgementScreenOptions_ScreenContentTheme1{
						ImageUrl: constants.ShutterDownImgUrl,
						Title:    title,
						Subtitle: subtitle,
					},
				},
			},
		},
	}
}

// AcctClosureTransferInitScreen returns deeplink.Screen_ACCOUNT_CLOSURE_TRANSFER_INITIATED
func AcctClosureTransferInitScreen(ctx context.Context, accessRvkReason usersPb.AccessRevokeReason, account *extacct.BankAccount) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_ACCOUNT_CLOSURE_TRANSFER_INITIATED,
		ScreenOptions: &deeplinkPb.Deeplink_AccountClosureTransferInitiatedScreenOptions{
			AccountClosureTransferInitiatedScreenOptions: &deeplinkPb.AccountClosureTransferInitiatedScreenOptions{
				Title:    constants.AccValidatedTitle,
				Subtitle: getAccValidatedSubtitle(ctx),
				KnowMoreObject: &deeplinkPb.AccountClosureTransferInitiatedScreenOptions_KnowMoreObject{
					Title:    constants.KnowMoreAccClosedTitle,
					Subtitle: AccessReasonToKnowMoreSubtl[accessRvkReason],
				},
				AccountDetails: &deeplinkPb.AccountClosureTransferInitiatedScreenOptions_AccountDetails{
					Line1: &commontypes.Text{
						Text: fmt.Sprintf("IFSC: %s", account.GetIfsc()),
					},
					Line2: &commontypes.Text{
						Text: fmt.Sprintf("A/c no: %s", account.GetAccountNumber())},
				},
			},
		},
	}
}

func getAccValidatedSubtitle(ctx context.Context) string {
	if platform, _ := epificontext.AppPlatformAndVersion(ctx); platform == commontypes.Platform_WEB {
		return constants.AccValidatedWithoutSubtitle
	}
	return constants.AccValidatedSubtitle
}

func getLastKnownBalanceLessThanThresholdResponse() *feErrors.ClientError {
	return feErrors.NewClientError(feErrors.ACCOUNT_CLOSED_LAST_KNOWN_LESS_THAN_ONE)
}

func getReportedBalanceLessThanThresholdResponse() *feErrors.ClientError {
	return feErrors.NewClientError(feErrors.ACCOUNT_CLOSED_REPORTED_LESS_THAN_ONE)
}

func getBalanceTransferredWithUtrScreen(ctx context.Context, utr, accNo, ifsc string) (*deeplinkPb.Deeplink, *feErrors.ClientError) {
	if plat, _ := epificontext.AppPlatformAndVersion(ctx); plat == commontypes.Platform_WEB {
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DETAILED_ERROR_VIEW_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_DetailedErrorViewScreenOptions{
				DetailedErrorViewScreenOptions: &deeplinkPb.DetailedErrorViewScreenOptions{
					Title:            constants.ClosedAccountFundsTransferredTitle,
					Subtitle:         fmt.Sprintf(constants.ClosedAccFundsTransferredDescWeb, accNo, ifsc, utr),
					ScreenIdentifier: constants.ClosedAccFundsTransferredScreenWeb,
				},
			},
		}, nil
	}
	return nil, feErrors.NewClientError(feErrors.ACCOUNT_CLOSED_UTR_SHARED, accNo, ifsc, utr)
}

func ClientErrorAsDeeplink(clientErr *feErrors.ClientError) *deeplinkPb.Deeplink {
	ctaHeader := ""
	var ctas []*deeplinkPb.Cta

	if clientErr.ShowChat || clientErr.ShowContact {
		ctas = append(ctas, cx.GetContactUsCta())
		ctaHeader = constants.NeedFurtherAssistance
	}
	if clientErr.ShowRetry {
		ctas = append(ctas, &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_RETRY,
			Text:         constants.RetryText,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		})
	}
	if clientErr.HasLogout {
		ctas = append(ctas, &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_LOGOUT,
			Text:         constants.BackToLoginText,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		})
	}
	imageUrl := constants.AFUFacePalmIllustrationImageUrl

	if clientErr.ImageUrl != "" {
		imageUrl = clientErr.ImageUrl
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &deeplinkPb.DetailedErrorViewScreenOptions{
				Title:         clientErr.Title,
				Subtitle:      clientErr.Subtitle,
				FailureReason: clientErr.Reason,
				HasLogOut:     clientErr.HasLogout,
				Ctas:          ctas,
				CtaHeader:     ctaHeader,
				ImageUrl:      imageUrl,
			},
		},
	}
}

// Date from which federal api for doing balance transfer will work.
// only accounts closed after automatedFundTransferStartDate is eligible for Third party account sharing api.
var automatedFundTransferStartDate = time.Date(2024, 9, 4, 0, 0, 0, 0, datetime.IST)

// IsAccountEligibleForThirdPartyAccountSharingAutomatedFlow
// only accounts that are access revoked due to min kyc expiry or lso user vkyc pending users
// and account closed after 4 sept is eligible for automated fund transfer
func IsAccountEligibleForThirdPartyAccountSharingAutomatedFlow(accessRevokeDetails *usersPb.AccessRevokeDetails) bool {
	if accessRevokeDetails.GetAccessRevokeStatus() == usersPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED &&
		(accessRevokeDetails.GetReason() == usersPb.AccessRevokeReason_ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY ||
			accessRevokeDetails.GetReason() == usersPb.AccessRevokeReason_ACCESS_REVOKE_REASON_LSO_USER_VKYC_PENDING) &&
		accessRevokeDetails.GetUpdatedAt().AsTime().After(automatedFundTransferStartDate) {
		return true
	}

	// todo: enable third party sharing for full kyc closed accounts as well when supported by federal

	return false
}
