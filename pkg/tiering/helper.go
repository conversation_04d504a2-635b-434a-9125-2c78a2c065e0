package tiering

import (
	tieringCriteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	pkgErrors "github.com/epifi/gamma/pkg/tiering/errors"
)

// GetAllCriteriaMinValuesFromOptions extracts all criteria minimum values from a list of tiering options
func GetAllCriteriaMinValuesFromOptions(tierOptions []*tieringCriteriaPb.Option) ([]*CriteriaMinValue, error) {
	var criteriaMinValues []*CriteriaMinValue

	for _, option := range tierOptions {
		for _, action := range option.GetActions() {
			ac := action.GetActionDetails().GetCriteria()
			switch actionCriteria := ac.(type) {
			case *tieringCriteriaPb.QualifyingCriteria_UsStocksSip:
				criteriaMinValues = append(criteriaMinValues,
					&CriteriaMinValue{
						Criteria: tieringEnumPb.CriteriaOptionType_US_STOCKS_SIP_AND_KYC,
						MinValue: actionCriteria.UsStocksSip.GetMinWalletAddFunds(),
					})
			case *tieringCriteriaPb.QualifyingCriteria_Deposits:
				criteriaMinValues = append(criteriaMinValues,
					&CriteriaMinValue{
						Criteria: tieringEnumPb.CriteriaOptionType_DEPOSITS_AND_KYC,
						MinValue: actionCriteria.Deposits.GetMinDepositsAmount(),
					})
			case *tieringCriteriaPb.QualifyingCriteria_BalanceV2:
				criteriaMinValues = append(criteriaMinValues,
					&CriteriaMinValue{
						Criteria: tieringEnumPb.CriteriaOptionType_BALANCE_V2_AND_KYC,
						MinValue: actionCriteria.BalanceV2.GetMinBalanceForUpgrade(),
					})
			}
		}
	}

	if len(criteriaMinValues) > 0 {
		return criteriaMinValues, nil
	}

	return nil, pkgErrors.ErrTierHasNoMinBalanceCriteria
}
