package tiering

import (
	"testing"

	"github.com/stretchr/testify/require"
	gmoney "google.golang.org/genproto/googleapis/type/money"

	tieringCriteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/pkg/tiering/errors"

	"github.com/stretchr/testify/assert"
)

func TestGetAllCriteriaMinValuesFromOptions(t *testing.T) {
	testCases := []struct {
		name        string
		options     []*tieringCriteriaPb.Option
		expected    []*CriteriaMinValue
		expectedErr error
	}{
		{
			name:        "Empty options",
			options:     []*tieringCriteriaPb.Option{},
			expected:    nil,
			expectedErr: errors.ErrTierHasNoMinBalanceCriteria,
		},
		{
			name: "Options with criteria",
			options: []*tieringCriteriaPb.Option{
				{
					Actions: []*tieringCriteriaPb.Action{
						{
							ActionDetails: &tieringCriteriaPb.QualifyingCriteria{
								Criteria: &tieringCriteriaPb.QualifyingCriteria_UsStocksSip{
									UsStocksSip: &tieringCriteriaPb.UsStocksSip{
										MinWalletAddFunds: &gmoney.Money{
											CurrencyCode: "INR",
											Units:        1000,
										},
									},
								},
							},
						},
					},
				},
			},
			expected: []*CriteriaMinValue{
				{
					Criteria: tieringEnumPb.CriteriaOptionType_US_STOCKS_SIP_AND_KYC,
					MinValue: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        1000,
					},
				},
			},
			expectedErr: nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := GetAllCriteriaMinValuesFromOptions(tc.options)

			if tc.expectedErr != nil {
				assert.Equal(t, tc.expectedErr, err)
			} else {
				require.NoError(t, err)
				assert.Len(t, tc.expected, len(result))

				if len(tc.expected) > 0 {
					assert.Equal(t, tc.expected[0].Criteria, result[0].Criteria)
					assert.Equal(t, tc.expected[0].MinValue.CurrencyCode, result[0].MinValue.CurrencyCode)
					assert.Equal(t, tc.expected[0].MinValue.Units, result[0].MinValue.Units)
				}
			}
		})
	}
}
