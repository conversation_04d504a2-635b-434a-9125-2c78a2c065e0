//go:generate gen_sql -types=CourierPartner,ShipmentType,Vendor,ShipmentStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/shipment/enums.proto

package shipment

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CourierPartner int32

const (
	CourierPartner_COURIER_PARTNER_UNSPECIFIED CourierPartner = 0
)

// Enum value maps for CourierPartner.
var (
	CourierPartner_name = map[int32]string{
		0: "COURIER_PARTNER_UNSPECIFIED",
	}
	CourierPartner_value = map[string]int32{
		"COURIER_PARTNER_UNSPECIFIED": 0,
	}
)

func (x CourierPartner) Enum() *CourierPartner {
	p := new(CourierPartner)
	*p = x
	return p
}

func (x CourierPartner) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CourierPartner) Descriptor() protoreflect.EnumDescriptor {
	return file_api_shipment_enums_proto_enumTypes[0].Descriptor()
}

func (CourierPartner) Type() protoreflect.EnumType {
	return &file_api_shipment_enums_proto_enumTypes[0]
}

func (x CourierPartner) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CourierPartner.Descriptor instead.
func (CourierPartner) EnumDescriptor() ([]byte, []int) {
	return file_api_shipment_enums_proto_rawDescGZIP(), []int{0}
}

type ShipmentType int32

const (
	ShipmentType_SHIPMENT_TYPE_UNSPECIFIED ShipmentType = 0
	ShipmentType_SHIPMENT_TYPE_CHEQUEBOOK  ShipmentType = 1
)

// Enum value maps for ShipmentType.
var (
	ShipmentType_name = map[int32]string{
		0: "SHIPMENT_TYPE_UNSPECIFIED",
		1: "SHIPMENT_TYPE_CHEQUEBOOK",
	}
	ShipmentType_value = map[string]int32{
		"SHIPMENT_TYPE_UNSPECIFIED": 0,
		"SHIPMENT_TYPE_CHEQUEBOOK":  1,
	}
)

func (x ShipmentType) Enum() *ShipmentType {
	p := new(ShipmentType)
	*p = x
	return p
}

func (x ShipmentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ShipmentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_shipment_enums_proto_enumTypes[1].Descriptor()
}

func (ShipmentType) Type() protoreflect.EnumType {
	return &file_api_shipment_enums_proto_enumTypes[1]
}

func (x ShipmentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ShipmentType.Descriptor instead.
func (ShipmentType) EnumDescriptor() ([]byte, []int) {
	return file_api_shipment_enums_proto_rawDescGZIP(), []int{1}
}

type ShipmentStatus int32

const (
	ShipmentStatus_SHIPMENT_STATUS_UNSPECIFIED                 ShipmentStatus = 0
	ShipmentStatus_SHIPMENT_STATUS_REGISTERED_AT_VENDOR        ShipmentStatus = 1
	ShipmentStatus_SHIPMENT_STATUS_IN_TRANSIT                  ShipmentStatus = 2
	ShipmentStatus_SHIPMENT_STATUS_DELIVERED                   ShipmentStatus = 3
	ShipmentStatus_SHIPMENT_STATUS_OUT_FOR_DELIVERY            ShipmentStatus = 4
	ShipmentStatus_SHIPMENT_STATUS_UNDELIVERED                 ShipmentStatus = 5
	ShipmentStatus_SHIPMENT_STATUS_RETURN_TO_ORIGIN_IN_TRANSIT ShipmentStatus = 6
	ShipmentStatus_SHIPMENT_STATUS_RETURN_TO_ORIGIN_DELIVERED  ShipmentStatus = 7
)

// Enum value maps for ShipmentStatus.
var (
	ShipmentStatus_name = map[int32]string{
		0: "SHIPMENT_STATUS_UNSPECIFIED",
		1: "SHIPMENT_STATUS_REGISTERED_AT_VENDOR",
		2: "SHIPMENT_STATUS_IN_TRANSIT",
		3: "SHIPMENT_STATUS_DELIVERED",
		4: "SHIPMENT_STATUS_OUT_FOR_DELIVERY",
		5: "SHIPMENT_STATUS_UNDELIVERED",
		6: "SHIPMENT_STATUS_RETURN_TO_ORIGIN_IN_TRANSIT",
		7: "SHIPMENT_STATUS_RETURN_TO_ORIGIN_DELIVERED",
	}
	ShipmentStatus_value = map[string]int32{
		"SHIPMENT_STATUS_UNSPECIFIED":                 0,
		"SHIPMENT_STATUS_REGISTERED_AT_VENDOR":        1,
		"SHIPMENT_STATUS_IN_TRANSIT":                  2,
		"SHIPMENT_STATUS_DELIVERED":                   3,
		"SHIPMENT_STATUS_OUT_FOR_DELIVERY":            4,
		"SHIPMENT_STATUS_UNDELIVERED":                 5,
		"SHIPMENT_STATUS_RETURN_TO_ORIGIN_IN_TRANSIT": 6,
		"SHIPMENT_STATUS_RETURN_TO_ORIGIN_DELIVERED":  7,
	}
)

func (x ShipmentStatus) Enum() *ShipmentStatus {
	p := new(ShipmentStatus)
	*p = x
	return p
}

func (x ShipmentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ShipmentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_shipment_enums_proto_enumTypes[2].Descriptor()
}

func (ShipmentStatus) Type() protoreflect.EnumType {
	return &file_api_shipment_enums_proto_enumTypes[2]
}

func (x ShipmentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ShipmentStatus.Descriptor instead.
func (ShipmentStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_shipment_enums_proto_rawDescGZIP(), []int{2}
}

type ShipmentTrackingRequestFieldMask int32

const (
	ShipmentTrackingRequestFieldMask_SHIPMENT_TRACKING_REQUEST_FIELD_MASK_UNSPECIFIED            ShipmentTrackingRequestFieldMask = 0
	ShipmentTrackingRequestFieldMask_SHIPMENT_TRACKING_REQUEST_FIELD_MASK_SHIPMENT_STATUS        ShipmentTrackingRequestFieldMask = 1
	ShipmentTrackingRequestFieldMask_SHIPMENT_TRACKING_REQUEST_FIELD_MASK_EXPECTED_DELIVERY_DATE ShipmentTrackingRequestFieldMask = 2
	ShipmentTrackingRequestFieldMask_SHIPMENT_TRACKING_REQUEST_FIELD_MASK_PICKUP_DATE            ShipmentTrackingRequestFieldMask = 3
	ShipmentTrackingRequestFieldMask_SHIPMENT_TRACKING_REQUEST_FIELD_MASK_VENDOR_RESPONSE_DATA   ShipmentTrackingRequestFieldMask = 4
)

// Enum value maps for ShipmentTrackingRequestFieldMask.
var (
	ShipmentTrackingRequestFieldMask_name = map[int32]string{
		0: "SHIPMENT_TRACKING_REQUEST_FIELD_MASK_UNSPECIFIED",
		1: "SHIPMENT_TRACKING_REQUEST_FIELD_MASK_SHIPMENT_STATUS",
		2: "SHIPMENT_TRACKING_REQUEST_FIELD_MASK_EXPECTED_DELIVERY_DATE",
		3: "SHIPMENT_TRACKING_REQUEST_FIELD_MASK_PICKUP_DATE",
		4: "SHIPMENT_TRACKING_REQUEST_FIELD_MASK_VENDOR_RESPONSE_DATA",
	}
	ShipmentTrackingRequestFieldMask_value = map[string]int32{
		"SHIPMENT_TRACKING_REQUEST_FIELD_MASK_UNSPECIFIED":            0,
		"SHIPMENT_TRACKING_REQUEST_FIELD_MASK_SHIPMENT_STATUS":        1,
		"SHIPMENT_TRACKING_REQUEST_FIELD_MASK_EXPECTED_DELIVERY_DATE": 2,
		"SHIPMENT_TRACKING_REQUEST_FIELD_MASK_PICKUP_DATE":            3,
		"SHIPMENT_TRACKING_REQUEST_FIELD_MASK_VENDOR_RESPONSE_DATA":   4,
	}
)

func (x ShipmentTrackingRequestFieldMask) Enum() *ShipmentTrackingRequestFieldMask {
	p := new(ShipmentTrackingRequestFieldMask)
	*p = x
	return p
}

func (x ShipmentTrackingRequestFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ShipmentTrackingRequestFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_shipment_enums_proto_enumTypes[3].Descriptor()
}

func (ShipmentTrackingRequestFieldMask) Type() protoreflect.EnumType {
	return &file_api_shipment_enums_proto_enumTypes[3]
}

func (x ShipmentTrackingRequestFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ShipmentTrackingRequestFieldMask.Descriptor instead.
func (ShipmentTrackingRequestFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_shipment_enums_proto_rawDescGZIP(), []int{3}
}

var File_api_shipment_enums_proto protoreflect.FileDescriptor

var file_api_shipment_enums_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x73, 0x68, 0x69, 0x70,
	0x6d, 0x65, 0x6e, 0x74, 0x2a, 0x31, 0x0a, 0x0e, 0x43, 0x6f, 0x75, 0x72, 0x69, 0x65, 0x72, 0x50,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4f, 0x55, 0x52, 0x49, 0x45,
	0x52, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x2a, 0x4b, 0x0a, 0x0c, 0x53, 0x68, 0x69, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x48, 0x49, 0x50, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x48, 0x49, 0x50, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x51, 0x55, 0x45, 0x42, 0x4f,
	0x4f, 0x4b, 0x10, 0x01, 0x2a, 0xc2, 0x02, 0x0a, 0x0e, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x48, 0x49, 0x50, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x48, 0x49, 0x50,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x47, 0x49,
	0x53, 0x54, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52,
	0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x48, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x54,
	0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x48, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10,
	0x03, 0x12, 0x24, 0x0a, 0x20, 0x53, 0x48, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x4c,
	0x49, 0x56, 0x45, 0x52, 0x59, 0x10, 0x04, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x48, 0x49, 0x50, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x44, 0x45, 0x4c,
	0x49, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x05, 0x12, 0x2f, 0x0a, 0x2b, 0x53, 0x48, 0x49, 0x50,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x54, 0x55,
	0x52, 0x4e, 0x5f, 0x54, 0x4f, 0x5f, 0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e, 0x5f, 0x49, 0x4e, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x54, 0x10, 0x06, 0x12, 0x2e, 0x0a, 0x2a, 0x53, 0x48, 0x49,
	0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x54,
	0x55, 0x52, 0x4e, 0x5f, 0x54, 0x4f, 0x5f, 0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e, 0x5f, 0x44, 0x45,
	0x4c, 0x49, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x07, 0x2a, 0xc8, 0x02, 0x0a, 0x20, 0x53, 0x68,
	0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x34,
	0x0a, 0x30, 0x53, 0x48, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b,
	0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x38, 0x0a, 0x34, 0x53, 0x48, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x48, 0x49,
	0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x01, 0x12, 0x3f,
	0x0a, 0x3b, 0x53, 0x48, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b,
	0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x58, 0x50, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f,
	0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x02, 0x12,
	0x34, 0x0a, 0x30, 0x53, 0x48, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x43,
	0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x49, 0x43, 0x4b, 0x55, 0x50, 0x5f, 0x44,
	0x41, 0x54, 0x45, 0x10, 0x03, 0x12, 0x3d, 0x0a, 0x39, 0x53, 0x48, 0x49, 0x50, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45,
	0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x10, 0x04, 0x42, 0x4a, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x5a, 0x23, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_shipment_enums_proto_rawDescOnce sync.Once
	file_api_shipment_enums_proto_rawDescData = file_api_shipment_enums_proto_rawDesc
)

func file_api_shipment_enums_proto_rawDescGZIP() []byte {
	file_api_shipment_enums_proto_rawDescOnce.Do(func() {
		file_api_shipment_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_shipment_enums_proto_rawDescData)
	})
	return file_api_shipment_enums_proto_rawDescData
}

var file_api_shipment_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_shipment_enums_proto_goTypes = []interface{}{
	(CourierPartner)(0),                   // 0: shipment.CourierPartner
	(ShipmentType)(0),                     // 1: shipment.ShipmentType
	(ShipmentStatus)(0),                   // 2: shipment.ShipmentStatus
	(ShipmentTrackingRequestFieldMask)(0), // 3: shipment.ShipmentTrackingRequestFieldMask
}
var file_api_shipment_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_shipment_enums_proto_init() }
func file_api_shipment_enums_proto_init() {
	if File_api_shipment_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_shipment_enums_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_shipment_enums_proto_goTypes,
		DependencyIndexes: file_api_shipment_enums_proto_depIdxs,
		EnumInfos:         file_api_shipment_enums_proto_enumTypes,
	}.Build()
	File_api_shipment_enums_proto = out.File
	file_api_shipment_enums_proto_rawDesc = nil
	file_api_shipment_enums_proto_goTypes = nil
	file_api_shipment_enums_proto_depIdxs = nil
}
