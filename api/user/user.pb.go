// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=DataVerificationDetail,DataVerificationDetails,AttributionDetails

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/user/user.proto

package user

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	queue "github.com/epifi/be-common/api/queue"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	employment "github.com/epifi/gamma/api/employment"
	kyc "github.com/epifi/gamma/api/kyc"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AccessRevokeStatus int32

const (
	// denotes unspecified revoke status
	AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNSPECIFIED AccessRevokeStatus = 0
	// denotes user is blocked
	AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED AccessRevokeStatus = 1
	// denotes user is unblocked
	AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED AccessRevokeStatus = 2
)

// Enum value maps for AccessRevokeStatus.
var (
	AccessRevokeStatus_name = map[int32]string{
		0: "ACCESS_REVOKE_STATUS_UNSPECIFIED",
		1: "ACCESS_REVOKE_STATUS_BLOCKED",
		2: "ACCESS_REVOKE_STATUS_UNBLOCKED",
	}
	AccessRevokeStatus_value = map[string]int32{
		"ACCESS_REVOKE_STATUS_UNSPECIFIED": 0,
		"ACCESS_REVOKE_STATUS_BLOCKED":     1,
		"ACCESS_REVOKE_STATUS_UNBLOCKED":   2,
	}
)

func (x AccessRevokeStatus) Enum() *AccessRevokeStatus {
	p := new(AccessRevokeStatus)
	*p = x
	return p
}

func (x AccessRevokeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccessRevokeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_user_proto_enumTypes[0].Descriptor()
}

func (AccessRevokeStatus) Type() protoreflect.EnumType {
	return &file_api_user_user_proto_enumTypes[0]
}

func (x AccessRevokeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccessRevokeStatus.Descriptor instead.
func (AccessRevokeStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{0}
}

// Deprecated: Marked as deprecated in api/user/user.proto.
type AccessRevokedStatus int32

const (
	// denotes detail not available
	AccessRevokedStatus_ACCESS_REVOKED_STATUS_UNSPECIFIED AccessRevokedStatus = 0
	// denotes user is blocked
	AccessRevokedStatus_ACCESS_REVOKED_STATUS_BLOCKED AccessRevokedStatus = 1
	// denotes user is unblocked
	AccessRevokedStatus_ACCESS_REVOKED_STATUS_UNBLOCKED AccessRevokedStatus = 2
)

// Enum value maps for AccessRevokedStatus.
var (
	AccessRevokedStatus_name = map[int32]string{
		0: "ACCESS_REVOKED_STATUS_UNSPECIFIED",
		1: "ACCESS_REVOKED_STATUS_BLOCKED",
		2: "ACCESS_REVOKED_STATUS_UNBLOCKED",
	}
	AccessRevokedStatus_value = map[string]int32{
		"ACCESS_REVOKED_STATUS_UNSPECIFIED": 0,
		"ACCESS_REVOKED_STATUS_BLOCKED":     1,
		"ACCESS_REVOKED_STATUS_UNBLOCKED":   2,
	}
)

func (x AccessRevokedStatus) Enum() *AccessRevokedStatus {
	p := new(AccessRevokedStatus)
	*p = x
	return p
}

func (x AccessRevokedStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccessRevokedStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_user_proto_enumTypes[1].Descriptor()
}

func (AccessRevokedStatus) Type() protoreflect.EnumType {
	return &file_api_user_user_proto_enumTypes[1]
}

func (x AccessRevokedStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccessRevokedStatus.Descriptor instead.
func (AccessRevokedStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{1}
}

type AccessRevokeReason int32

const (
	AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED AccessRevokeReason = 0
	// denotes user's account is deleted at fi as it is requested by user
	AccessRevokeReason_ACCESS_REVOKE_REASON_ACCOUNT_DELETION_REQUEST AccessRevokeReason = 1
	// denotes user's account is deleted by fi
	// possible reason could be liveness or facematch issue
	// deprecated in favour of ACCESS_REVOKE_REASON_FRAUDULENT_ACCOUNT
	//
	// Deprecated: Marked as deprecated in api/user/user.proto.
	AccessRevokeReason_ACCESS_REVOKE_REASON_SPOOF AccessRevokeReason = 2
	// For other remarks will contain detailed remark
	AccessRevokeReason_ACCESS_REVOKE_REASON_OTHER AccessRevokeReason = 3
	// denotes user's account is closed due to min kyc expiry
	AccessRevokeReason_ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY AccessRevokeReason = 4
	// denotes user's account is deleted by fi
	// possible reason could be liveness or facematch issue
	AccessRevokeReason_ACCESS_REVOKE_REASON_FRAUDULENT_ACCOUNT AccessRevokeReason = 5
	// kyc issues like face match, liveness, name match
	AccessRevokeReason_ACCESS_REVOKE_REASON_CORE_KYC_ISSUE AccessRevokeReason = 6
	// suspicious email patterns, AA info mismatch, location based issues
	// and also if data analytics has pointed a user as risky
	AccessRevokeReason_ACCESS_REVOKE_REASON_PROFILE_INDICATORS AccessRevokeReason = 7
	// penny drop abuse as name suggests
	AccessRevokeReason_ACCESS_REVOKE_REASON_PENNY_DROP_ABUSE AccessRevokeReason = 8
	// received funds from known fraudsters
	AccessRevokeReason_ACCESS_REVOKE_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS AccessRevokeReason = 9
	// high number of withdrawals from atms
	AccessRevokeReason_ACCESS_REVOKE_REASON_HIGH_ATM_WITHDRAWAL_COUNT AccessRevokeReason = 10
	// intersection rule in transaction monitoring
	AccessRevokeReason_ACCESS_REVOKE_REASON_TM_ALERT AccessRevokeReason = 11
	// transaction monitoring profile mismatch
	AccessRevokeReason_ACCESS_REVOKE_REASON_TM_PROFILE_MISMATCH AccessRevokeReason = 12
	// account frozen under LEA
	AccessRevokeReason_ACCESS_REVOKE_REASON_LEA_COMPLAINT AccessRevokeReason = 13
	// account inquired under LEA
	AccessRevokeReason_ACCESS_REVOKE_REASON_LEA_ENQUIRY AccessRevokeReason = 14
	// account frozen under NPCI
	AccessRevokeReason_ACCESS_REVOKE_REASON_NPCI_COMPLAINT AccessRevokeReason = 15
	// account frozen under FEDERAL rules
	AccessRevokeReason_ACCESS_REVOKE_REASON_FEDERAL_RULES AccessRevokeReason = 16
	// LSO users haven't complete their vkyc
	AccessRevokeReason_ACCESS_REVOKE_REASON_LSO_USER_VKYC_PENDING AccessRevokeReason = 17
	// reason to stop onboarding journey for users (no comms triggered)
	AccessRevokeReason_ACCESS_REVOKE_REASON_BLOCK_ONBOARDING AccessRevokeReason = 18
	// UPI PIN retries exceeded post successful AFU
	AccessRevokeReason_ACCESS_REVOKE_REASON_COOLDOWN_UPI_PIN_RETRIES_EXCEEDED_POST_AFU AccessRevokeReason = 19
	// users who were part of revkyc queue and did not complete re vkyc
	AccessRevokeReason_ACCESS_REVOKE_REASON_RE_VKYC_NOT_COMPLETED AccessRevokeReason = 20
)

// Enum value maps for AccessRevokeReason.
var (
	AccessRevokeReason_name = map[int32]string{
		0:  "ACCESS_REVOKE_REASON_UNSPECIFIED",
		1:  "ACCESS_REVOKE_REASON_ACCOUNT_DELETION_REQUEST",
		2:  "ACCESS_REVOKE_REASON_SPOOF",
		3:  "ACCESS_REVOKE_REASON_OTHER",
		4:  "ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY",
		5:  "ACCESS_REVOKE_REASON_FRAUDULENT_ACCOUNT",
		6:  "ACCESS_REVOKE_REASON_CORE_KYC_ISSUE",
		7:  "ACCESS_REVOKE_REASON_PROFILE_INDICATORS",
		8:  "ACCESS_REVOKE_REASON_PENNY_DROP_ABUSE",
		9:  "ACCESS_REVOKE_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS",
		10: "ACCESS_REVOKE_REASON_HIGH_ATM_WITHDRAWAL_COUNT",
		11: "ACCESS_REVOKE_REASON_TM_ALERT",
		12: "ACCESS_REVOKE_REASON_TM_PROFILE_MISMATCH",
		13: "ACCESS_REVOKE_REASON_LEA_COMPLAINT",
		14: "ACCESS_REVOKE_REASON_LEA_ENQUIRY",
		15: "ACCESS_REVOKE_REASON_NPCI_COMPLAINT",
		16: "ACCESS_REVOKE_REASON_FEDERAL_RULES",
		17: "ACCESS_REVOKE_REASON_LSO_USER_VKYC_PENDING",
		18: "ACCESS_REVOKE_REASON_BLOCK_ONBOARDING",
		19: "ACCESS_REVOKE_REASON_COOLDOWN_UPI_PIN_RETRIES_EXCEEDED_POST_AFU",
		20: "ACCESS_REVOKE_REASON_RE_VKYC_NOT_COMPLETED",
	}
	AccessRevokeReason_value = map[string]int32{
		"ACCESS_REVOKE_REASON_UNSPECIFIED":                                0,
		"ACCESS_REVOKE_REASON_ACCOUNT_DELETION_REQUEST":                   1,
		"ACCESS_REVOKE_REASON_SPOOF":                                      2,
		"ACCESS_REVOKE_REASON_OTHER":                                      3,
		"ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY":                     4,
		"ACCESS_REVOKE_REASON_FRAUDULENT_ACCOUNT":                         5,
		"ACCESS_REVOKE_REASON_CORE_KYC_ISSUE":                             6,
		"ACCESS_REVOKE_REASON_PROFILE_INDICATORS":                         7,
		"ACCESS_REVOKE_REASON_PENNY_DROP_ABUSE":                           8,
		"ACCESS_REVOKE_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS":             9,
		"ACCESS_REVOKE_REASON_HIGH_ATM_WITHDRAWAL_COUNT":                  10,
		"ACCESS_REVOKE_REASON_TM_ALERT":                                   11,
		"ACCESS_REVOKE_REASON_TM_PROFILE_MISMATCH":                        12,
		"ACCESS_REVOKE_REASON_LEA_COMPLAINT":                              13,
		"ACCESS_REVOKE_REASON_LEA_ENQUIRY":                                14,
		"ACCESS_REVOKE_REASON_NPCI_COMPLAINT":                             15,
		"ACCESS_REVOKE_REASON_FEDERAL_RULES":                              16,
		"ACCESS_REVOKE_REASON_LSO_USER_VKYC_PENDING":                      17,
		"ACCESS_REVOKE_REASON_BLOCK_ONBOARDING":                           18,
		"ACCESS_REVOKE_REASON_COOLDOWN_UPI_PIN_RETRIES_EXCEEDED_POST_AFU": 19,
		"ACCESS_REVOKE_REASON_RE_VKYC_NOT_COMPLETED":                      20,
	}
)

func (x AccessRevokeReason) Enum() *AccessRevokeReason {
	p := new(AccessRevokeReason)
	*p = x
	return p
}

func (x AccessRevokeReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccessRevokeReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_user_proto_enumTypes[2].Descriptor()
}

func (AccessRevokeReason) Type() protoreflect.EnumType {
	return &file_api_user_user_proto_enumTypes[2]
}

func (x AccessRevokeReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccessRevokeReason.Descriptor instead.
func (AccessRevokeReason) EnumDescriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{2}
}

// AccessRestoreReason denotes the reason for which app access was unblocked
type AccessRestoreReason int32

const (
	AccessRestoreReason_ACCESS_RESTORE_REASON_UNSCPECIFIED AccessRestoreReason = 0
	// unblock reason: after due dilligence
	AccessRestoreReason_ACCESS_RESTORE_REASON_DUE_DILIGENCE AccessRestoreReason = 1
	// unblock reason: after customer outcall
	AccessRestoreReason_ACCESS_RESTORE_REASON_CUSTOMER_OUTCALL AccessRestoreReason = 2
	// unblock reason: credit freeze imposed on savings account,
	// app access need not be revoked for this
	AccessRestoreReason_ACCESS_RESTORE_REASON_CREDIT_FREEZE AccessRestoreReason = 3
	AccessRestoreReason_ACCESS_RESTORE_REASON_OTHERS        AccessRestoreReason = 4
	// unfreeze reason: LEA
	AccessRestoreReason_ACCESS_RESTORE_REASON_LEA_UNFREEZE AccessRestoreReason = 5
	// unblock reason: debit freeze imposed on savings account
	AccessRestoreReason_ACCESS_RESTORE_REASON_DEBIT_FREEZE AccessRestoreReason = 6
	// unblock to use cc or pl
	AccessRestoreReason_ACCESS_RESTORE_REASON_REASON_CC_OR_PL_USER AccessRestoreReason = 7
	AccessRestoreReason_ACCESS_RESTORE_REASON_COOLDOWN_LIFTED      AccessRestoreReason = 8
)

// Enum value maps for AccessRestoreReason.
var (
	AccessRestoreReason_name = map[int32]string{
		0: "ACCESS_RESTORE_REASON_UNSCPECIFIED",
		1: "ACCESS_RESTORE_REASON_DUE_DILIGENCE",
		2: "ACCESS_RESTORE_REASON_CUSTOMER_OUTCALL",
		3: "ACCESS_RESTORE_REASON_CREDIT_FREEZE",
		4: "ACCESS_RESTORE_REASON_OTHERS",
		5: "ACCESS_RESTORE_REASON_LEA_UNFREEZE",
		6: "ACCESS_RESTORE_REASON_DEBIT_FREEZE",
		7: "ACCESS_RESTORE_REASON_REASON_CC_OR_PL_USER",
		8: "ACCESS_RESTORE_REASON_COOLDOWN_LIFTED",
	}
	AccessRestoreReason_value = map[string]int32{
		"ACCESS_RESTORE_REASON_UNSCPECIFIED":         0,
		"ACCESS_RESTORE_REASON_DUE_DILIGENCE":        1,
		"ACCESS_RESTORE_REASON_CUSTOMER_OUTCALL":     2,
		"ACCESS_RESTORE_REASON_CREDIT_FREEZE":        3,
		"ACCESS_RESTORE_REASON_OTHERS":               4,
		"ACCESS_RESTORE_REASON_LEA_UNFREEZE":         5,
		"ACCESS_RESTORE_REASON_DEBIT_FREEZE":         6,
		"ACCESS_RESTORE_REASON_REASON_CC_OR_PL_USER": 7,
		"ACCESS_RESTORE_REASON_COOLDOWN_LIFTED":      8,
	}
)

func (x AccessRestoreReason) Enum() *AccessRestoreReason {
	p := new(AccessRestoreReason)
	*p = x
	return p
}

func (x AccessRestoreReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccessRestoreReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_user_proto_enumTypes[3].Descriptor()
}

func (AccessRestoreReason) Type() protoreflect.EnumType {
	return &file_api_user_user_proto_enumTypes[3]
}

func (x AccessRestoreReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccessRestoreReason.Descriptor instead.
func (AccessRestoreReason) EnumDescriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{3}
}

// Deprecated: Marked as deprecated in api/user/user.proto.
type AccessRevokeState int32

const (
	// default value used to denote unblocked user. This will also be used to update and unblock an user.
	AccessRevokeState_ACCESS_REVOKE_STATE_UNSPECIFIED AccessRevokeState = 0
	// denotes user's account is deleted only at Fi as requested by user
	AccessRevokeState_ACCESS_REVOKE_STATE_SOFT_BLOCK AccessRevokeState = 1
	// denotes user's account is blacklisted by Fi
	AccessRevokeState_ACCESS_REVOKE_STATE_BLACKLISTED AccessRevokeState = 2
)

// Enum value maps for AccessRevokeState.
var (
	AccessRevokeState_name = map[int32]string{
		0: "ACCESS_REVOKE_STATE_UNSPECIFIED",
		1: "ACCESS_REVOKE_STATE_SOFT_BLOCK",
		2: "ACCESS_REVOKE_STATE_BLACKLISTED",
	}
	AccessRevokeState_value = map[string]int32{
		"ACCESS_REVOKE_STATE_UNSPECIFIED": 0,
		"ACCESS_REVOKE_STATE_SOFT_BLOCK":  1,
		"ACCESS_REVOKE_STATE_BLACKLISTED": 2,
	}
)

func (x AccessRevokeState) Enum() *AccessRevokeState {
	p := new(AccessRevokeState)
	*p = x
	return p
}

func (x AccessRevokeState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccessRevokeState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_user_proto_enumTypes[4].Descriptor()
}

func (AccessRevokeState) Type() protoreflect.EnumType {
	return &file_api_user_user_proto_enumTypes[4]
}

func (x AccessRevokeState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccessRevokeState.Descriptor instead.
func (AccessRevokeState) EnumDescriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{4}
}

type CustomerCreationStatus int32

const (
	CustomerCreationStatus_CUSTOMER_CREATION_STATUS_UNSPECIFIED CustomerCreationStatus = 0
	// INITIATED-  req is registered on epifi server and message is enqueued
	CustomerCreationStatus_INITIATED CustomerCreationStatus = 1
	// IN_PROGRESS- at least one attempt was made at calling the vendor for creating a customer. The status needs to be
	//
	//	polled. Requests can be in this state -
	//	1. after successful acknowledgement of receiving the request by a vendor in case the APIs are async.
	//	In this case status will be polled till we get a terminal status back.
	//	2. after seeing transient errors e.g. vendor systems are down or the vendor call timed out.
	//	In this case status will be polled and appropriate retries will be done if required.
	CustomerCreationStatus_IN_PROGRESS CustomerCreationStatus = 2
	// CREATED- customer has been created for this user at vendor's end
	CustomerCreationStatus_CREATED CustomerCreationStatus = 3
	// FAILED- customer creation has failed due to some permanent failure at vendor's end.
	// an example of permanent failure would be missing documents or unable to verify user details, etc.
	CustomerCreationStatus_FAILED CustomerCreationStatus = 4
	// MANUAL_INTERVENTION- System has exhausted all the retries post transient errors so this needs attention from a human.
	//
	// Deprecated: Marked as deprecated in api/user/user.proto.
	CustomerCreationStatus_MANUAL_INTERVENTION CustomerCreationStatus = 5
	// Dedupe customer
	CustomerCreationStatus_DEDUPE_CUSTOMER CustomerCreationStatus = 6
	// Max retry
	CustomerCreationStatus_MAX_RETRY_CREATION_STEP CustomerCreationStatus = 7
	// Max retry reached in check status api
	CustomerCreationStatus_MAX_RETRY_STATUS_CHECK_STEP CustomerCreationStatus = 8
)

// Enum value maps for CustomerCreationStatus.
var (
	CustomerCreationStatus_name = map[int32]string{
		0: "CUSTOMER_CREATION_STATUS_UNSPECIFIED",
		1: "INITIATED",
		2: "IN_PROGRESS",
		3: "CREATED",
		4: "FAILED",
		5: "MANUAL_INTERVENTION",
		6: "DEDUPE_CUSTOMER",
		7: "MAX_RETRY_CREATION_STEP",
		8: "MAX_RETRY_STATUS_CHECK_STEP",
	}
	CustomerCreationStatus_value = map[string]int32{
		"CUSTOMER_CREATION_STATUS_UNSPECIFIED": 0,
		"INITIATED":                            1,
		"IN_PROGRESS":                          2,
		"CREATED":                              3,
		"FAILED":                               4,
		"MANUAL_INTERVENTION":                  5,
		"DEDUPE_CUSTOMER":                      6,
		"MAX_RETRY_CREATION_STEP":              7,
		"MAX_RETRY_STATUS_CHECK_STEP":          8,
	}
)

func (x CustomerCreationStatus) Enum() *CustomerCreationStatus {
	p := new(CustomerCreationStatus)
	*p = x
	return p
}

func (x CustomerCreationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerCreationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_user_proto_enumTypes[5].Descriptor()
}

func (CustomerCreationStatus) Type() protoreflect.EnumType {
	return &file_api_user_user_proto_enumTypes[5]
}

func (x CustomerCreationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerCreationStatus.Descriptor instead.
func (CustomerCreationStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{5}
}

// User Field mask specifies which fields need to be considered for a particular operation.
// For example, when updating user information.
type UserFieldMask int32

const (
	UserFieldMask_FIELD_MASK_UNSPECIFIED UserFieldMask = 0
	// User profile fields such as name, address, phone number, etc. excluding PAN
	UserFieldMask_USER_PROFILE UserFieldMask = 1
	// Bank customer information such as customer id, request id for
	// customer creation, etc.
	UserFieldMask_BANK_CUSTOMER_INFO UserFieldMask = 2
	// mask to update user's ACCESS_REVOKE_STATE
	UserFieldMask_ACCESS_REVOKE_STATE UserFieldMask = 3
	UserFieldMask_PHONE_NUMBER        UserFieldMask = 4
	UserFieldMask_EMAIL               UserFieldMask = 5
	UserFieldMask_PAN                 UserFieldMask = 6
	UserFieldMask_DOB                 UserFieldMask = 7
	UserFieldMask_KYC_NAME            UserFieldMask = 8
	UserFieldMask_PAN_NAME            UserFieldMask = 15
	UserFieldMask_DEBIT_CARD_NAME     UserFieldMask = 9
	UserFieldMask_USER_LIVENESS_PHOTO UserFieldMask = 10
	// Deprecated: Marked as deprecated in api/user/user.proto.
	UserFieldMask_PROFILE_IMAGE_URL          UserFieldMask = 11
	UserFieldMask_MOTHER_NAME                UserFieldMask = 12
	UserFieldMask_FATHER_NAME                UserFieldMask = 13
	UserFieldMask_PRIVACY_SETTINGS           UserFieldMask = 14
	UserFieldMask_PHOTO                      UserFieldMask = 16
	UserFieldMask_SALARY_RANGE               UserFieldMask = 17
	UserFieldMask_ADDRESSES                  UserFieldMask = 18
	UserFieldMask_LEGAL_NAME_BY_USER         UserFieldMask = 19
	UserFieldMask_ACCESS_REVOKE_INFO         UserFieldMask = 20
	UserFieldMask_PROFILE_IMAGE_S3_FILE_PATH UserFieldMask = 21
	UserFieldMask_ACCESS_REVOKE_DETAILS      UserFieldMask = 22
	UserFieldMask_ACQUISITION_INFO           UserFieldMask = 23
	UserFieldMask_KYC_GENDER                 UserFieldMask = 24
	UserFieldMask_GIVEN_NAME                 UserFieldMask = 25
	UserFieldMask_GIVEN_GENDER               UserFieldMask = 26
	UserFieldMask_DATA_VERIFICATION_DETAILS  UserFieldMask = 27
	UserFieldMask_ACTOR_ID                   UserFieldMask = 28
	UserFieldMask_QUALIFICATION              UserFieldMask = 29
	UserFieldMask_DESIGNATION                UserFieldMask = 30
	UserFieldMask_COMMUNITY                  UserFieldMask = 31
	UserFieldMask_RELIGION                   UserFieldMask = 32
	UserFieldMask_CATEGORY                   UserFieldMask = 33
	UserFieldMask_DISABILITY_TYPE            UserFieldMask = 34
)

// Enum value maps for UserFieldMask.
var (
	UserFieldMask_name = map[int32]string{
		0:  "FIELD_MASK_UNSPECIFIED",
		1:  "USER_PROFILE",
		2:  "BANK_CUSTOMER_INFO",
		3:  "ACCESS_REVOKE_STATE",
		4:  "PHONE_NUMBER",
		5:  "EMAIL",
		6:  "PAN",
		7:  "DOB",
		8:  "KYC_NAME",
		15: "PAN_NAME",
		9:  "DEBIT_CARD_NAME",
		10: "USER_LIVENESS_PHOTO",
		11: "PROFILE_IMAGE_URL",
		12: "MOTHER_NAME",
		13: "FATHER_NAME",
		14: "PRIVACY_SETTINGS",
		16: "PHOTO",
		17: "SALARY_RANGE",
		18: "ADDRESSES",
		19: "LEGAL_NAME_BY_USER",
		20: "ACCESS_REVOKE_INFO",
		21: "PROFILE_IMAGE_S3_FILE_PATH",
		22: "ACCESS_REVOKE_DETAILS",
		23: "ACQUISITION_INFO",
		24: "KYC_GENDER",
		25: "GIVEN_NAME",
		26: "GIVEN_GENDER",
		27: "DATA_VERIFICATION_DETAILS",
		28: "ACTOR_ID",
		29: "QUALIFICATION",
		30: "DESIGNATION",
		31: "COMMUNITY",
		32: "RELIGION",
		33: "CATEGORY",
		34: "DISABILITY_TYPE",
	}
	UserFieldMask_value = map[string]int32{
		"FIELD_MASK_UNSPECIFIED":     0,
		"USER_PROFILE":               1,
		"BANK_CUSTOMER_INFO":         2,
		"ACCESS_REVOKE_STATE":        3,
		"PHONE_NUMBER":               4,
		"EMAIL":                      5,
		"PAN":                        6,
		"DOB":                        7,
		"KYC_NAME":                   8,
		"PAN_NAME":                   15,
		"DEBIT_CARD_NAME":            9,
		"USER_LIVENESS_PHOTO":        10,
		"PROFILE_IMAGE_URL":          11,
		"MOTHER_NAME":                12,
		"FATHER_NAME":                13,
		"PRIVACY_SETTINGS":           14,
		"PHOTO":                      16,
		"SALARY_RANGE":               17,
		"ADDRESSES":                  18,
		"LEGAL_NAME_BY_USER":         19,
		"ACCESS_REVOKE_INFO":         20,
		"PROFILE_IMAGE_S3_FILE_PATH": 21,
		"ACCESS_REVOKE_DETAILS":      22,
		"ACQUISITION_INFO":           23,
		"KYC_GENDER":                 24,
		"GIVEN_NAME":                 25,
		"GIVEN_GENDER":               26,
		"DATA_VERIFICATION_DETAILS":  27,
		"ACTOR_ID":                   28,
		"QUALIFICATION":              29,
		"DESIGNATION":                30,
		"COMMUNITY":                  31,
		"RELIGION":                   32,
		"CATEGORY":                   33,
		"DISABILITY_TYPE":            34,
	}
)

func (x UserFieldMask) Enum() *UserFieldMask {
	p := new(UserFieldMask)
	*p = x
	return p
}

func (x UserFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_user_proto_enumTypes[6].Descriptor()
}

func (UserFieldMask) Type() protoreflect.EnumType {
	return &file_api_user_user_proto_enumTypes[6]
}

func (x UserFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserFieldMask.Descriptor instead.
func (UserFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{6}
}

// flow via which kyc level was updated
// Ideally, this is to be populated only when kyc level is updated to full kyc
type KycLevelUpdateFlow int32

const (
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_UNSPECIFIED          KycLevelUpdateFlow = 0
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_DEDUPE_CUSTOMER      KycLevelUpdateFlow = 1
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_POST_ONBOARDING KycLevelUpdateFlow = 2
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_LSO  KycLevelUpdateFlow = 3
	// CKYC Type O user
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_O                                   KycLevelUpdateFlow = 4
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_STUDENT                             KycLevelUpdateFlow = 5
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC                  KycLevelUpdateFlow = 6
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_EKYC_NUMBER_MISMATCH                KycLevelUpdateFlow = 7
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_LOW_QUALITY_USERS                   KycLevelUpdateFlow = 8
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_FI_LITE_USERS                                  KycLevelUpdateFlow = 9
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_CLOSED_ACCOUNT_REOPENING                       KycLevelUpdateFlow = 10
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_BKYC_POST_ONBOARDING                                KycLevelUpdateFlow = 11
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_NON_RESIDENT_ONBOARDING                             KycLevelUpdateFlow = 12
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_FEDERAL_LOANS                                       KycLevelUpdateFlow = 13
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC_LATEST_NO_DEDUPE KycLevelUpdateFlow = 14
	KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_BKYC_PRE_ONBOARDING                                 KycLevelUpdateFlow = 15
)

// Enum value maps for KycLevelUpdateFlow.
var (
	KycLevelUpdateFlow_name = map[int32]string{
		0:  "KYC_LEVEL_UPDATE_FLOW_UNSPECIFIED",
		1:  "KYC_LEVEL_UPDATE_FLOW_DEDUPE_CUSTOMER",
		2:  "KYC_LEVEL_UPDATE_FLOW_VKYC_POST_ONBOARDING",
		3:  "KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_LSO",
		4:  "KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_O",
		5:  "KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_STUDENT",
		6:  "KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC",
		7:  "KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_EKYC_NUMBER_MISMATCH",
		8:  "KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_LOW_QUALITY_USERS",
		9:  "KYC_LEVEL_UPDATE_FLOW_VKYC_FI_LITE_USERS",
		10: "KYC_LEVEL_UPDATE_FLOW_VKYC_CLOSED_ACCOUNT_REOPENING",
		11: "KYC_LEVEL_UPDATE_FLOW_BKYC_POST_ONBOARDING",
		12: "KYC_LEVEL_UPDATE_FLOW_NON_RESIDENT_ONBOARDING",
		13: "KYC_LEVEL_UPDATE_FLOW_FEDERAL_LOANS",
		14: "KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC_LATEST_NO_DEDUPE",
		15: "KYC_LEVEL_UPDATE_FLOW_BKYC_PRE_ONBOARDING",
	}
	KycLevelUpdateFlow_value = map[string]int32{
		"KYC_LEVEL_UPDATE_FLOW_UNSPECIFIED":                                         0,
		"KYC_LEVEL_UPDATE_FLOW_DEDUPE_CUSTOMER":                                     1,
		"KYC_LEVEL_UPDATE_FLOW_VKYC_POST_ONBOARDING":                                2,
		"KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_LSO":                                 3,
		"KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_O":                                   4,
		"KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_STUDENT":                             5,
		"KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC":                  6,
		"KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_EKYC_NUMBER_MISMATCH":                7,
		"KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_LOW_QUALITY_USERS":                   8,
		"KYC_LEVEL_UPDATE_FLOW_VKYC_FI_LITE_USERS":                                  9,
		"KYC_LEVEL_UPDATE_FLOW_VKYC_CLOSED_ACCOUNT_REOPENING":                       10,
		"KYC_LEVEL_UPDATE_FLOW_BKYC_POST_ONBOARDING":                                11,
		"KYC_LEVEL_UPDATE_FLOW_NON_RESIDENT_ONBOARDING":                             12,
		"KYC_LEVEL_UPDATE_FLOW_FEDERAL_LOANS":                                       13,
		"KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC_LATEST_NO_DEDUPE": 14,
		"KYC_LEVEL_UPDATE_FLOW_BKYC_PRE_ONBOARDING":                                 15,
	}
)

func (x KycLevelUpdateFlow) Enum() *KycLevelUpdateFlow {
	p := new(KycLevelUpdateFlow)
	*p = x
	return p
}

func (x KycLevelUpdateFlow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KycLevelUpdateFlow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_user_proto_enumTypes[7].Descriptor()
}

func (KycLevelUpdateFlow) Type() protoreflect.EnumType {
	return &file_api_user_user_proto_enumTypes[7]
}

func (x KycLevelUpdateFlow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KycLevelUpdateFlow.Descriptor instead.
func (KycLevelUpdateFlow) EnumDescriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{7}
}

// Source of the user coming to Fi from, i.e. paid marketing, organic etc.
type AcquisitionChannel int32

const (
	AcquisitionChannel_ACQUISITION_CHANNEL_UNSPECIFIED AcquisitionChannel = 0
	AcquisitionChannel_STUDENT_PROGRAM_VIT             AcquisitionChannel = 1
	// user coming via B2B salary program flow
	AcquisitionChannel_B2B_SALARY_PROGRAM AcquisitionChannel = 2
	// user coming from ads shown via affiliates
	AcquisitionChannel_ACQUISITION_CHANNEL_AFFILIATES AcquisitionChannel = 3
	// user coming from ads shown via GoogleAds
	AcquisitionChannel_ACQUISITION_CHANNEL_GOOGLE AcquisitionChannel = 4
	// user coming from ads shown via Facebook
	AcquisitionChannel_ACQUISITION_CHANNEL_FACEBOOK AcquisitionChannel = 5
	// user coming from ads shown during search in App Store
	AcquisitionChannel_ACQUISITION_CHANNEL_APPLE_SEARCH_ADS AcquisitionChannel = 6
	// user coming organically
	AcquisitionChannel_ACQUISITION_CHANNEL_ORGANIC AcquisitionChannel = 7
	// user coming via referrals
	AcquisitionChannel_ACQUISITION_CHANNEL_REFERRALS AcquisitionChannel = 8
	// user coming via web channel
	AcquisitionChannel_ACQUISITION_CHANNEL_WEB AcquisitionChannel = 9
	// user coming from a source not from the above list
	AcquisitionChannel_ACQUISITION_CHANNEL_OTHERS AcquisitionChannel = 15
)

// Enum value maps for AcquisitionChannel.
var (
	AcquisitionChannel_name = map[int32]string{
		0:  "ACQUISITION_CHANNEL_UNSPECIFIED",
		1:  "STUDENT_PROGRAM_VIT",
		2:  "B2B_SALARY_PROGRAM",
		3:  "ACQUISITION_CHANNEL_AFFILIATES",
		4:  "ACQUISITION_CHANNEL_GOOGLE",
		5:  "ACQUISITION_CHANNEL_FACEBOOK",
		6:  "ACQUISITION_CHANNEL_APPLE_SEARCH_ADS",
		7:  "ACQUISITION_CHANNEL_ORGANIC",
		8:  "ACQUISITION_CHANNEL_REFERRALS",
		9:  "ACQUISITION_CHANNEL_WEB",
		15: "ACQUISITION_CHANNEL_OTHERS",
	}
	AcquisitionChannel_value = map[string]int32{
		"ACQUISITION_CHANNEL_UNSPECIFIED":      0,
		"STUDENT_PROGRAM_VIT":                  1,
		"B2B_SALARY_PROGRAM":                   2,
		"ACQUISITION_CHANNEL_AFFILIATES":       3,
		"ACQUISITION_CHANNEL_GOOGLE":           4,
		"ACQUISITION_CHANNEL_FACEBOOK":         5,
		"ACQUISITION_CHANNEL_APPLE_SEARCH_ADS": 6,
		"ACQUISITION_CHANNEL_ORGANIC":          7,
		"ACQUISITION_CHANNEL_REFERRALS":        8,
		"ACQUISITION_CHANNEL_WEB":              9,
		"ACQUISITION_CHANNEL_OTHERS":           15,
	}
)

func (x AcquisitionChannel) Enum() *AcquisitionChannel {
	p := new(AcquisitionChannel)
	*p = x
	return p
}

func (x AcquisitionChannel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AcquisitionChannel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_user_proto_enumTypes[8].Descriptor()
}

func (AcquisitionChannel) Type() protoreflect.EnumType {
	return &file_api_user_user_proto_enumTypes[8]
}

func (x AcquisitionChannel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AcquisitionChannel.Descriptor instead.
func (AcquisitionChannel) EnumDescriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{8}
}

// Intent of the user coming on Fi app.
// This can depend upon the type of Ad shown to the user, offline campaign, non-Ad acquisitions etc.
type AcquisitionIntent int32

const (
	AcquisitionIntent_ACQUISITION_INTENT_UNSPECIFIED AcquisitionIntent = 0
	// Ad conveyed the intent of Banking (Savings Account)
	AcquisitionIntent_ACQUISITION_INTENT_BANKING AcquisitionIntent = 1
	// Ad conveyed the intent of Personal Loans
	AcquisitionIntent_ACQUISITION_INTENT_PERSONAL_LOANS AcquisitionIntent = 2
	// Ad conveyed the intent of Credit Cards
	AcquisitionIntent_ACQUISITION_INTENT_CREDIT_CARDS AcquisitionIntent = 3
	// Ad conveyed the intent of Networth
	AcquisitionIntent_ACQUISITION_INTENT_NET_WORTH AcquisitionIntent = 4
	// Ad conveyed the intent of Debit card
	AcquisitionIntent_ACQUISITION_INTENT_DEBIT_CARD AcquisitionIntent = 5
	// Ad conveyed the intent of Wealth Analyser
	AcquisitionIntent_ACQUISITION_INTENT_WEALTH_ANALYSER AcquisitionIntent = 6
)

// Enum value maps for AcquisitionIntent.
var (
	AcquisitionIntent_name = map[int32]string{
		0: "ACQUISITION_INTENT_UNSPECIFIED",
		1: "ACQUISITION_INTENT_BANKING",
		2: "ACQUISITION_INTENT_PERSONAL_LOANS",
		3: "ACQUISITION_INTENT_CREDIT_CARDS",
		4: "ACQUISITION_INTENT_NET_WORTH",
		5: "ACQUISITION_INTENT_DEBIT_CARD",
		6: "ACQUISITION_INTENT_WEALTH_ANALYSER",
	}
	AcquisitionIntent_value = map[string]int32{
		"ACQUISITION_INTENT_UNSPECIFIED":     0,
		"ACQUISITION_INTENT_BANKING":         1,
		"ACQUISITION_INTENT_PERSONAL_LOANS":  2,
		"ACQUISITION_INTENT_CREDIT_CARDS":    3,
		"ACQUISITION_INTENT_NET_WORTH":       4,
		"ACQUISITION_INTENT_DEBIT_CARD":      5,
		"ACQUISITION_INTENT_WEALTH_ANALYSER": 6,
	}
)

func (x AcquisitionIntent) Enum() *AcquisitionIntent {
	p := new(AcquisitionIntent)
	*p = x
	return p
}

func (x AcquisitionIntent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AcquisitionIntent) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_user_proto_enumTypes[9].Descriptor()
}

func (AcquisitionIntent) Type() protoreflect.EnumType {
	return &file_api_user_user_proto_enumTypes[9]
}

func (x AcquisitionIntent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AcquisitionIntent.Descriptor instead.
func (AcquisitionIntent) EnumDescriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{9}
}

// deletion reason
type DeletionDetails_DeletionReason int32

const (
	DeletionDetails_DELETION_REASON_UNSPECIFIED        DeletionDetails_DeletionReason = 0
	DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG DeletionDetails_DeletionReason = 1
	// user is deleted as user's phone number is conflicting with the user trying to perform AFU
	DeletionDetails_DELETION_REASON_AFU_CONFLICT_PHONE DeletionDetails_DeletionReason = 2
	// user is deleted as user's email is conflicting with the user trying to perform AFU
	DeletionDetails_DELETION_REASON_AFU_CONFLICT_EMAIL DeletionDetails_DeletionReason = 3
	// user is deleted if user attempts afu before customer creation
	DeletionDetails_DELETION_REASON_AFU_BEFORE_CUSTOMER_CREATION DeletionDetails_DeletionReason = 4
	DeletionDetails_DELETION_REASON_DEDUPE_MISMATCH              DeletionDetails_DeletionReason = 5
	DeletionDetails_DELETION_REASON_SCRIPT                       DeletionDetails_DeletionReason = 6
	DeletionDetails_DELETION_REASON_OLD_USER_ONB_JOURNEY_RESET   DeletionDetails_DeletionReason = 7
	DeletionDetails_DELETION_REASON_CLOSED_SAVINGS_ACCOUNT       DeletionDetails_DeletionReason = 8
	DeletionDetails_DELETION_REASON_DEV_ACTION                   DeletionDetails_DeletionReason = 9
	DeletionDetails_DELETION_REASON_CX_ADMIN                     DeletionDetails_DeletionReason = 10
	// User journey reset in case mobile number does not match with the number linked to aadhar
	DeletionDetails_DELETION_REASON_AADHAR_MOBILE_MISMATCH DeletionDetails_DeletionReason = 11
	// User journey reset in case NR user onboard with indian number
	DeletionDetails_DELETION_REASON_NRI_WITH_INDIAN_PH_NUM DeletionDetails_DeletionReason = 12
)

// Enum value maps for DeletionDetails_DeletionReason.
var (
	DeletionDetails_DeletionReason_name = map[int32]string{
		0:  "DELETION_REASON_UNSPECIFIED",
		1:  "DELETION_REASON_AFU_BEFORE_DEV_REG",
		2:  "DELETION_REASON_AFU_CONFLICT_PHONE",
		3:  "DELETION_REASON_AFU_CONFLICT_EMAIL",
		4:  "DELETION_REASON_AFU_BEFORE_CUSTOMER_CREATION",
		5:  "DELETION_REASON_DEDUPE_MISMATCH",
		6:  "DELETION_REASON_SCRIPT",
		7:  "DELETION_REASON_OLD_USER_ONB_JOURNEY_RESET",
		8:  "DELETION_REASON_CLOSED_SAVINGS_ACCOUNT",
		9:  "DELETION_REASON_DEV_ACTION",
		10: "DELETION_REASON_CX_ADMIN",
		11: "DELETION_REASON_AADHAR_MOBILE_MISMATCH",
		12: "DELETION_REASON_NRI_WITH_INDIAN_PH_NUM",
	}
	DeletionDetails_DeletionReason_value = map[string]int32{
		"DELETION_REASON_UNSPECIFIED":                  0,
		"DELETION_REASON_AFU_BEFORE_DEV_REG":           1,
		"DELETION_REASON_AFU_CONFLICT_PHONE":           2,
		"DELETION_REASON_AFU_CONFLICT_EMAIL":           3,
		"DELETION_REASON_AFU_BEFORE_CUSTOMER_CREATION": 4,
		"DELETION_REASON_DEDUPE_MISMATCH":              5,
		"DELETION_REASON_SCRIPT":                       6,
		"DELETION_REASON_OLD_USER_ONB_JOURNEY_RESET":   7,
		"DELETION_REASON_CLOSED_SAVINGS_ACCOUNT":       8,
		"DELETION_REASON_DEV_ACTION":                   9,
		"DELETION_REASON_CX_ADMIN":                     10,
		"DELETION_REASON_AADHAR_MOBILE_MISMATCH":       11,
		"DELETION_REASON_NRI_WITH_INDIAN_PH_NUM":       12,
	}
)

func (x DeletionDetails_DeletionReason) Enum() *DeletionDetails_DeletionReason {
	p := new(DeletionDetails_DeletionReason)
	*p = x
	return p
}

func (x DeletionDetails_DeletionReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeletionDetails_DeletionReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_user_proto_enumTypes[10].Descriptor()
}

func (DeletionDetails_DeletionReason) Type() protoreflect.EnumType {
	return &file_api_user_user_proto_enumTypes[10]
}

func (x DeletionDetails_DeletionReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeletionDetails_DeletionReason.Descriptor instead.
func (DeletionDetails_DeletionReason) EnumDescriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{3, 0}
}

// This denotes if customer was dedupe customer or not
type BankCustomerInfo_CustomerType int32

const (
	BankCustomerInfo_CUSTOMER_TYPE_UNSPECIFIED BankCustomerInfo_CustomerType = 0
	BankCustomerInfo_DEDUPE_CUSTOMER           BankCustomerInfo_CustomerType = 1
)

// Enum value maps for BankCustomerInfo_CustomerType.
var (
	BankCustomerInfo_CustomerType_name = map[int32]string{
		0: "CUSTOMER_TYPE_UNSPECIFIED",
		1: "DEDUPE_CUSTOMER",
	}
	BankCustomerInfo_CustomerType_value = map[string]int32{
		"CUSTOMER_TYPE_UNSPECIFIED": 0,
		"DEDUPE_CUSTOMER":           1,
	}
)

func (x BankCustomerInfo_CustomerType) Enum() *BankCustomerInfo_CustomerType {
	p := new(BankCustomerInfo_CustomerType)
	*p = x
	return p
}

func (x BankCustomerInfo_CustomerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BankCustomerInfo_CustomerType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_user_proto_enumTypes[11].Descriptor()
}

func (BankCustomerInfo_CustomerType) Type() protoreflect.EnumType {
	return &file_api_user_user_proto_enumTypes[11]
}

func (x BankCustomerInfo_CustomerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BankCustomerInfo_CustomerType.Descriptor instead.
func (BankCustomerInfo_CustomerType) EnumDescriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{7, 0}
}

// Message representing a user.
type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Profile *Profile `protobuf:"bytes,2,opt,name=profile,proto3" json:"profile,omitempty"`
	// contains information regarding bank customers for a particular user
	// a user can have multiple customer id as we on-board more vendor banks
	// for now we have only one partner vendor i.e. FEDERAL it might have only
	// one entry going initially.
	// Deprecated: Use bank customer service
	//
	// Deprecated: Marked as deprecated in api/user/user.proto.
	CustomerInfos []*BankCustomerInfo `protobuf:"bytes,3,rep,name=customer_infos,json=customerInfos,proto3" json:"customer_infos,omitempty"`
	// Deprecated: use AccessRevokeInfo
	//
	// Deprecated: Marked as deprecated in api/user/user.proto.
	AccessRevokeState AccessRevokeState `protobuf:"varint,4,opt,name=access_revoke_state,json=accessRevokeState,proto3,enum=user.AccessRevokeState" json:"access_revoke_state,omitempty"`
	// deprecated: use AccessRevokeDetails
	// contains all access related detail status, reason for a user
	//
	// Deprecated: Marked as deprecated in api/user/user.proto.
	AccessRevokeInfo *AccessRevokeInfo `protobuf:"bytes,5,opt,name=access_revoke_info,json=accessRevokeInfo,proto3" json:"access_revoke_info,omitempty"`
	// contains all access revoke details like status, reason, remarks, updated by
	AccessRevokeDetails *AccessRevokeDetails `protobuf:"bytes,6,opt,name=access_revoke_details,json=accessRevokeDetails,proto3" json:"access_revoke_details,omitempty"`
	// contains all acquisition related details like acquisition source, platform, url through which acquisition happened
	AcquisitionInfo *AcquisitionInfo `protobuf:"bytes,7,opt,name=acquisition_info,json=acquisitionInfo,proto3" json:"acquisition_info,omitempty"`
	// contains all deletion details like reason, new user ID if created
	DeletionDetails *DeletionDetails `protobuf:"bytes,8,opt,name=deletion_details,json=deletionDetails,proto3" json:"deletion_details,omitempty"`
	// stores the verification data from different flows of various entities like PAN,DOB etc
	DataVerificationDetails *DataVerificationDetails `protobuf:"bytes,9,opt,name=data_verification_details,json=dataVerificationDetails,proto3" json:"data_verification_details,omitempty"`
	// stores reference to the id of actor with entityId as user id
	ActorId string `protobuf:"bytes,10,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// deleted_at maps to deleted_at_unix in user table
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *User) GetProfile() *Profile {
	if x != nil {
		return x.Profile
	}
	return nil
}

// Deprecated: Marked as deprecated in api/user/user.proto.
func (x *User) GetCustomerInfos() []*BankCustomerInfo {
	if x != nil {
		return x.CustomerInfos
	}
	return nil
}

// Deprecated: Marked as deprecated in api/user/user.proto.
func (x *User) GetAccessRevokeState() AccessRevokeState {
	if x != nil {
		return x.AccessRevokeState
	}
	return AccessRevokeState_ACCESS_REVOKE_STATE_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/user/user.proto.
func (x *User) GetAccessRevokeInfo() *AccessRevokeInfo {
	if x != nil {
		return x.AccessRevokeInfo
	}
	return nil
}

func (x *User) GetAccessRevokeDetails() *AccessRevokeDetails {
	if x != nil {
		return x.AccessRevokeDetails
	}
	return nil
}

func (x *User) GetAcquisitionInfo() *AcquisitionInfo {
	if x != nil {
		return x.AcquisitionInfo
	}
	return nil
}

func (x *User) GetDeletionDetails() *DeletionDetails {
	if x != nil {
		return x.DeletionDetails
	}
	return nil
}

func (x *User) GetDataVerificationDetails() *DataVerificationDetails {
	if x != nil {
		return x.DataVerificationDetails
	}
	return nil
}

func (x *User) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *User) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type DataVerificationDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataType DataType `protobuf:"varint,1,opt,name=data_type,json=dataType,proto3,enum=user.DataType" json:"data_type,omitempty"`
	// Types that are assignable to DataValue:
	//
	//	*DataVerificationDetail_PanNumber
	//	*DataVerificationDetail_DOB
	//	*DataVerificationDetail_PanName
	//	*DataVerificationDetail_EmploymentDetail_
	//	*DataVerificationDetail_MaritalStatus
	//	*DataVerificationDetail_AddressDetails
	DataValue          isDataVerificationDetail_DataValue `protobuf_oneof:"data_value"`
	VerificationEntity vendorgateway.Vendor               `protobuf:"varint,9,opt,name=verification_entity,json=verificationEntity,proto3,enum=vendorgateway.Vendor" json:"verification_entity,omitempty"`
	VerificationMethod VerificationMethod                 `protobuf:"varint,10,opt,name=verification_method,json=verificationMethod,proto3,enum=user.VerificationMethod" json:"verification_method,omitempty"`
	VerifiedTime       *timestamppb.Timestamp             `protobuf:"bytes,11,opt,name=verified_time,json=verifiedTime,proto3" json:"verified_time,omitempty"`
}

func (x *DataVerificationDetail) Reset() {
	*x = DataVerificationDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataVerificationDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataVerificationDetail) ProtoMessage() {}

func (x *DataVerificationDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataVerificationDetail.ProtoReflect.Descriptor instead.
func (*DataVerificationDetail) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{1}
}

func (x *DataVerificationDetail) GetDataType() DataType {
	if x != nil {
		return x.DataType
	}
	return DataType_DATA_TYPE_UNSPECIFIED
}

func (m *DataVerificationDetail) GetDataValue() isDataVerificationDetail_DataValue {
	if m != nil {
		return m.DataValue
	}
	return nil
}

func (x *DataVerificationDetail) GetPanNumber() string {
	if x, ok := x.GetDataValue().(*DataVerificationDetail_PanNumber); ok {
		return x.PanNumber
	}
	return ""
}

func (x *DataVerificationDetail) GetDOB() *date.Date {
	if x, ok := x.GetDataValue().(*DataVerificationDetail_DOB); ok {
		return x.DOB
	}
	return nil
}

func (x *DataVerificationDetail) GetPanName() *common.Name {
	if x, ok := x.GetDataValue().(*DataVerificationDetail_PanName); ok {
		return x.PanName
	}
	return nil
}

func (x *DataVerificationDetail) GetEmploymentDetail() *DataVerificationDetail_EmploymentDetail {
	if x, ok := x.GetDataValue().(*DataVerificationDetail_EmploymentDetail_); ok {
		return x.EmploymentDetail
	}
	return nil
}

func (x *DataVerificationDetail) GetMaritalStatus() typesv2.MaritalStatus {
	if x, ok := x.GetDataValue().(*DataVerificationDetail_MaritalStatus); ok {
		return x.MaritalStatus
	}
	return typesv2.MaritalStatus(0)
}

func (x *DataVerificationDetail) GetAddressDetails() *DataVerificationDetail_ResidenceDetails {
	if x, ok := x.GetDataValue().(*DataVerificationDetail_AddressDetails); ok {
		return x.AddressDetails
	}
	return nil
}

func (x *DataVerificationDetail) GetVerificationEntity() vendorgateway.Vendor {
	if x != nil {
		return x.VerificationEntity
	}
	return vendorgateway.Vendor(0)
}

func (x *DataVerificationDetail) GetVerificationMethod() VerificationMethod {
	if x != nil {
		return x.VerificationMethod
	}
	return VerificationMethod_VERIFICATION_METHOD_UNSPECIFIED
}

func (x *DataVerificationDetail) GetVerifiedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.VerifiedTime
	}
	return nil
}

type isDataVerificationDetail_DataValue interface {
	isDataVerificationDetail_DataValue()
}

type DataVerificationDetail_PanNumber struct {
	PanNumber string `protobuf:"bytes,2,opt,name=pan_number,json=panNumber,proto3,oneof"`
}

type DataVerificationDetail_DOB struct {
	DOB *date.Date `protobuf:"bytes,3,opt,name=d_o_b,json=dOB,proto3,oneof"`
}

type DataVerificationDetail_PanName struct {
	PanName *common.Name `protobuf:"bytes,4,opt,name=pan_name,json=panName,proto3,oneof"`
}

type DataVerificationDetail_EmploymentDetail_ struct {
	EmploymentDetail *DataVerificationDetail_EmploymentDetail `protobuf:"bytes,5,opt,name=employment_detail,json=employmentDetail,proto3,oneof"`
}

type DataVerificationDetail_MaritalStatus struct {
	MaritalStatus typesv2.MaritalStatus `protobuf:"varint,6,opt,name=marital_status,json=maritalStatus,proto3,enum=api.typesv2.MaritalStatus,oneof"`
}

type DataVerificationDetail_AddressDetails struct {
	AddressDetails *DataVerificationDetail_ResidenceDetails `protobuf:"bytes,7,opt,name=address_details,json=addressDetails,proto3,oneof"`
}

func (*DataVerificationDetail_PanNumber) isDataVerificationDetail_DataValue() {}

func (*DataVerificationDetail_DOB) isDataVerificationDetail_DataValue() {}

func (*DataVerificationDetail_PanName) isDataVerificationDetail_DataValue() {}

func (*DataVerificationDetail_EmploymentDetail_) isDataVerificationDetail_DataValue() {}

func (*DataVerificationDetail_MaritalStatus) isDataVerificationDetail_DataValue() {}

func (*DataVerificationDetail_AddressDetails) isDataVerificationDetail_DataValue() {}

type DataVerificationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataVerificationDetails []*DataVerificationDetail `protobuf:"bytes,1,rep,name=data_verification_details,json=dataVerificationDetails,proto3" json:"data_verification_details,omitempty"`
}

func (x *DataVerificationDetails) Reset() {
	*x = DataVerificationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataVerificationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataVerificationDetails) ProtoMessage() {}

func (x *DataVerificationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataVerificationDetails.ProtoReflect.Descriptor instead.
func (*DataVerificationDetails) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{2}
}

func (x *DataVerificationDetails) GetDataVerificationDetails() []*DataVerificationDetail {
	if x != nil {
		return x.DataVerificationDetails
	}
	return nil
}

type DeletionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeletionReason DeletionDetails_DeletionReason `protobuf:"varint,1,opt,name=deletion_reason,json=deletionReason,proto3,enum=user.DeletionDetails_DeletionReason" json:"deletion_reason,omitempty"`
}

func (x *DeletionDetails) Reset() {
	*x = DeletionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletionDetails) ProtoMessage() {}

func (x *DeletionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletionDetails.ProtoReflect.Descriptor instead.
func (*DeletionDetails) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{3}
}

func (x *DeletionDetails) GetDeletionReason() DeletionDetails_DeletionReason {
	if x != nil {
		return x.DeletionReason
	}
	return DeletionDetails_DELETION_REASON_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/user/user.proto.
type AccessRevokeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes if user is blocked or not
	IsAccessRevoked AccessRevokedStatus `protobuf:"varint,1,opt,name=is_access_revoked,json=isAccessRevoked,proto3,enum=user.AccessRevokedStatus" json:"is_access_revoked,omitempty"`
	// Specifies reason for which user is blocked
	Reason AccessRevokeReason `protobuf:"varint,2,opt,name=reason,proto3,enum=user.AccessRevokeReason" json:"reason,omitempty"`
	// remarks should be non empty if reason is other
	Remarks string `protobuf:"bytes,3,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// time at which user blocked
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// it should be email
	UpdatedBy string `protobuf:"bytes,5,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
}

func (x *AccessRevokeInfo) Reset() {
	*x = AccessRevokeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessRevokeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessRevokeInfo) ProtoMessage() {}

func (x *AccessRevokeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessRevokeInfo.ProtoReflect.Descriptor instead.
func (*AccessRevokeInfo) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{4}
}

func (x *AccessRevokeInfo) GetIsAccessRevoked() AccessRevokedStatus {
	if x != nil {
		return x.IsAccessRevoked
	}
	return AccessRevokedStatus_ACCESS_REVOKED_STATUS_UNSPECIFIED
}

func (x *AccessRevokeInfo) GetReason() AccessRevokeReason {
	if x != nil {
		return x.Reason
	}
	return AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED
}

func (x *AccessRevokeInfo) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *AccessRevokeInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *AccessRevokeInfo) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

type AccessRevokeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the user access revoke status
	AccessRevokeStatus AccessRevokeStatus `protobuf:"varint,1,opt,name=access_revoke_status,json=accessRevokeStatus,proto3,enum=user.AccessRevokeStatus" json:"access_revoke_status,omitempty"`
	// Specifies reason for which user's access is revoked
	Reason AccessRevokeReason `protobuf:"varint,2,opt,name=reason,proto3,enum=user.AccessRevokeReason" json:"reason,omitempty"`
	// specifies reason when app access is restored
	RestoreReason AccessRestoreReason `protobuf:"varint,6,opt,name=restore_reason,json=restoreReason,proto3,enum=user.AccessRestoreReason" json:"restore_reason,omitempty"`
	// remarks should be non empty if reason is other
	Remarks string `protobuf:"bytes,3,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// time at which user blocked
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// indicates who updated the details
	// it should be email
	UpdatedBy string `protobuf:"bytes,5,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
}

func (x *AccessRevokeDetails) Reset() {
	*x = AccessRevokeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessRevokeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessRevokeDetails) ProtoMessage() {}

func (x *AccessRevokeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessRevokeDetails.ProtoReflect.Descriptor instead.
func (*AccessRevokeDetails) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{5}
}

func (x *AccessRevokeDetails) GetAccessRevokeStatus() AccessRevokeStatus {
	if x != nil {
		return x.AccessRevokeStatus
	}
	return AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNSPECIFIED
}

func (x *AccessRevokeDetails) GetReason() AccessRevokeReason {
	if x != nil {
		return x.Reason
	}
	return AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED
}

func (x *AccessRevokeDetails) GetRestoreReason() AccessRestoreReason {
	if x != nil {
		return x.RestoreReason
	}
	return AccessRestoreReason_ACCESS_RESTORE_REASON_UNSCPECIFIED
}

func (x *AccessRevokeDetails) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *AccessRevokeDetails) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *AccessRevokeDetails) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

// User's personally identifying details.
type Profile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Customer's Display name.
	//
	// Deprecated: Marked as deprecated in api/user/user.proto.
	Name        *common.Name        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DateOfBirth *date.Date          `protobuf:"bytes,3,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,5,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Email       string              `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	// This facilitates to save multiple addresses for a user. Each address must belong to either of the types defined
	// in api.typesv2.AddressType enum.
	// Represents a map from api.typesv2.AddressType (converted to string) to Address.
	Addresses map[string]*postaladdress.PostalAddress `protobuf:"bytes,6,rep,name=addresses,proto3" json:"addresses,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// PAN Number of the user
	PAN string `protobuf:"bytes,7,opt,name=PAN,proto3" json:"PAN,omitempty"`
	// profile image of user. This will be the image location in S3
	//
	// Deprecated: Marked as deprecated in api/user/user.proto.
	ProfileImageUrl string `protobuf:"bytes,8,opt,name=profile_image_url,json=profileImageUrl,proto3" json:"profile_image_url,omitempty"`
	// Mother name
	MotherName *common.Name `protobuf:"bytes,9,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	// Father name
	FatherName *common.Name `protobuf:"bytes,10,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	// privacy settings
	PrivacySettings *PrivacySettings `protobuf:"bytes,11,opt,name=privacy_settings,json=privacySettings,proto3" json:"privacy_settings,omitempty"`
	// deprecated in favour of kyc_name
	//
	// Deprecated: Marked as deprecated in api/user/user.proto.
	LegalName *common.Name `protobuf:"bytes,12,opt,name=legal_name,json=legalName,proto3" json:"legal_name,omitempty"`
	// photo of the user that will be permanently stored with us.
	Photo *common.Image `protobuf:"bytes,13,opt,name=photo,proto3" json:"photo,omitempty"`
	// customer name as per KYC record
	KycName *common.Name `protobuf:"bytes,14,opt,name=kyc_name,json=kycName,proto3" json:"kyc_name,omitempty"`
	// customer name on PAN card
	PanName *common.Name `protobuf:"bytes,15,opt,name=pan_name,json=panName,proto3" json:"pan_name,omitempty"`
	// customer entered legal name
	LegalNameByUser *common.Name `protobuf:"bytes,16,opt,name=legal_name_by_user,json=legalNameByUser,proto3" json:"legal_name_by_user,omitempty"`
	// name to be printed on the user's Debit card.
	DebitCardName *common.Name `protobuf:"bytes,17,opt,name=debit_card_name,json=debitCardName,proto3" json:"debit_card_name,omitempty"`
	// Salary range
	SalaryRange *SalaryRange `protobuf:"bytes,18,opt,name=salary_range,json=salaryRange,proto3" json:"salary_range,omitempty"`
	// name recorded in user's gmail account
	GmailName              *common.Name `protobuf:"bytes,19,opt,name=gmail_name,json=gmailName,proto3" json:"gmail_name,omitempty"`
	ProfileImageS3FilePath string       `protobuf:"bytes,20,opt,name=profile_image_s3_file_path,json=profileImageS3FilePath,proto3" json:"profile_image_s3_file_path,omitempty"`
	// to store the hashed phone number from DB
	HashedPhoneNumber string `protobuf:"bytes,21,opt,name=hashed_phone_number,json=hashedPhoneNumber,proto3" json:"hashed_phone_number,omitempty"`
	// Gender of user based on KYC data
	KycGender typesv2.Gender `protobuf:"varint,22,opt,name=kyc_gender,json=kycGender,proto3,enum=api.typesv2.Gender" json:"kyc_gender,omitempty"`
	// Name given by user, will be unverified ... use cautiously!
	GivenName *common.Name `protobuf:"bytes,23,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// Gender given by user
	GivenGender typesv2.Gender `protobuf:"varint,24,opt,name=given_gender,json=givenGender,proto3,enum=api.typesv2.Gender" json:"given_gender,omitempty"`
	// User Qualification data
	Qualification typesv2.Qualification `protobuf:"varint,25,opt,name=qualification,proto3,enum=api.typesv2.Qualification" json:"qualification,omitempty"`
	// User Designation data
	Designation typesv2.Designation `protobuf:"varint,26,opt,name=designation,proto3,enum=api.typesv2.Designation" json:"designation,omitempty"`
	// User Community data
	Community typesv2.Community `protobuf:"varint,27,opt,name=community,proto3,enum=api.typesv2.Community" json:"community,omitempty"`
	// User Religion data
	Religion typesv2.Religion `protobuf:"varint,28,opt,name=religion,proto3,enum=api.typesv2.Religion" json:"religion,omitempty"`
	// User Category data
	Category typesv2.Category `protobuf:"varint,29,opt,name=category,proto3,enum=api.typesv2.Category" json:"category,omitempty"`
	// User Disability type
	DisabilityType typesv2.DisabilityType `protobuf:"varint,30,opt,name=disability_type,json=disabilityType,proto3,enum=api.typesv2.DisabilityType" json:"disability_type,omitempty"`
}

func (x *Profile) Reset() {
	*x = Profile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Profile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Profile) ProtoMessage() {}

func (x *Profile) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Profile.ProtoReflect.Descriptor instead.
func (*Profile) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{6}
}

// Deprecated: Marked as deprecated in api/user/user.proto.
func (x *Profile) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *Profile) GetDateOfBirth() *date.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *Profile) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *Profile) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Profile) GetAddresses() map[string]*postaladdress.PostalAddress {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *Profile) GetPAN() string {
	if x != nil {
		return x.PAN
	}
	return ""
}

// Deprecated: Marked as deprecated in api/user/user.proto.
func (x *Profile) GetProfileImageUrl() string {
	if x != nil {
		return x.ProfileImageUrl
	}
	return ""
}

func (x *Profile) GetMotherName() *common.Name {
	if x != nil {
		return x.MotherName
	}
	return nil
}

func (x *Profile) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

func (x *Profile) GetPrivacySettings() *PrivacySettings {
	if x != nil {
		return x.PrivacySettings
	}
	return nil
}

// Deprecated: Marked as deprecated in api/user/user.proto.
func (x *Profile) GetLegalName() *common.Name {
	if x != nil {
		return x.LegalName
	}
	return nil
}

func (x *Profile) GetPhoto() *common.Image {
	if x != nil {
		return x.Photo
	}
	return nil
}

func (x *Profile) GetKycName() *common.Name {
	if x != nil {
		return x.KycName
	}
	return nil
}

func (x *Profile) GetPanName() *common.Name {
	if x != nil {
		return x.PanName
	}
	return nil
}

func (x *Profile) GetLegalNameByUser() *common.Name {
	if x != nil {
		return x.LegalNameByUser
	}
	return nil
}

func (x *Profile) GetDebitCardName() *common.Name {
	if x != nil {
		return x.DebitCardName
	}
	return nil
}

func (x *Profile) GetSalaryRange() *SalaryRange {
	if x != nil {
		return x.SalaryRange
	}
	return nil
}

func (x *Profile) GetGmailName() *common.Name {
	if x != nil {
		return x.GmailName
	}
	return nil
}

func (x *Profile) GetProfileImageS3FilePath() string {
	if x != nil {
		return x.ProfileImageS3FilePath
	}
	return ""
}

func (x *Profile) GetHashedPhoneNumber() string {
	if x != nil {
		return x.HashedPhoneNumber
	}
	return ""
}

func (x *Profile) GetKycGender() typesv2.Gender {
	if x != nil {
		return x.KycGender
	}
	return typesv2.Gender(0)
}

func (x *Profile) GetGivenName() *common.Name {
	if x != nil {
		return x.GivenName
	}
	return nil
}

func (x *Profile) GetGivenGender() typesv2.Gender {
	if x != nil {
		return x.GivenGender
	}
	return typesv2.Gender(0)
}

func (x *Profile) GetQualification() typesv2.Qualification {
	if x != nil {
		return x.Qualification
	}
	return typesv2.Qualification(0)
}

func (x *Profile) GetDesignation() typesv2.Designation {
	if x != nil {
		return x.Designation
	}
	return typesv2.Designation(0)
}

func (x *Profile) GetCommunity() typesv2.Community {
	if x != nil {
		return x.Community
	}
	return typesv2.Community(0)
}

func (x *Profile) GetReligion() typesv2.Religion {
	if x != nil {
		return x.Religion
	}
	return typesv2.Religion(0)
}

func (x *Profile) GetCategory() typesv2.Category {
	if x != nil {
		return x.Category
	}
	return typesv2.Category(0)
}

func (x *Profile) GetDisabilityType() typesv2.DisabilityType {
	if x != nil {
		return x.DisabilityType
	}
	return typesv2.DisabilityType(0)
}

// Message representing information regarding the customer created at vendor's end
type BankCustomerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id as provided by vendor
	// will be empty if customer creation is in progress/ failed / initiated state
	Id     string               `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Vendor vendorgateway.Vendor `protobuf:"varint,2,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	// Status of customer creation
	CustomerCreationStatus CustomerCreationStatus `protobuf:"varint,3,opt,name=customer_creation_status,json=customerCreationStatus,proto3,enum=user.CustomerCreationStatus" json:"customer_creation_status,omitempty"`
	// vendor specific customer name
	Name         *common.Name                  `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	CustomerType BankCustomerInfo_CustomerType `protobuf:"varint,5,opt,name=customer_type,json=customerType,proto3,enum=user.BankCustomerInfo_CustomerType" json:"customer_type,omitempty"`
	// This parameter is only for dedupe customer, and denotes if the used has done partial/full kyc with vendor.
	// This kyc level is in regard to the kyc done by the user with federal/other vendor outside of Epifi
	OriginalKycLevelWithVendor kyc.KYCLevel `protobuf:"varint,6,opt,name=original_kyc_level_with_vendor,json=originalKycLevelWithVendor,proto3,enum=kyc.KYCLevel" json:"original_kyc_level_with_vendor,omitempty"`
	// Kyc level of user is dependent on the original kyc level with vendor, and the kyc done by user with epifi.
	// This parameter represents the consolidated kyc level of both the parameters.
	KycLevel kyc.KYCLevel `protobuf:"varint,7,opt,name=kyc_level,json=kycLevel,proto3,enum=kyc.KYCLevel" json:"kyc_level,omitempty"`
	// time at which bank customer creation was triggered at vendor's end
	CreationStartedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=creation_started_at,json=creationStartedAt,proto3" json:"creation_started_at,omitempty"`
	// time at which bank customer was created at partner bank's end
	VendorCreationSucceededAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=vendor_creation_succeeded_at,json=vendorCreationSucceededAt,proto3" json:"vendor_creation_succeeded_at,omitempty"`
	// time at which bank customer was marked created at epifi's end
	FiCreationSucceededAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=fi_creation_succeeded_at,json=fiCreationSucceededAt,proto3" json:"fi_creation_succeeded_at,omitempty"`
	// ekyc rrn number used during customer creation
	EkycRrnNo string `protobuf:"bytes,11,opt,name=ekyc_rrn_no,json=ekycRrnNo,proto3" json:"ekyc_rrn_no,omitempty"`
	// flow via which kyc level was updated
	// Ideally, this is to be populated only when kyc level is updated to full kyc
	KycLevelUpdateFlow KycLevelUpdateFlow `protobuf:"varint,12,opt,name=kyc_level_update_flow,json=kycLevelUpdateFlow,proto3,enum=user.KycLevelUpdateFlow" json:"kyc_level_update_flow,omitempty"`
}

func (x *BankCustomerInfo) Reset() {
	*x = BankCustomerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankCustomerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankCustomerInfo) ProtoMessage() {}

func (x *BankCustomerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankCustomerInfo.ProtoReflect.Descriptor instead.
func (*BankCustomerInfo) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{7}
}

func (x *BankCustomerInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BankCustomerInfo) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *BankCustomerInfo) GetCustomerCreationStatus() CustomerCreationStatus {
	if x != nil {
		return x.CustomerCreationStatus
	}
	return CustomerCreationStatus_CUSTOMER_CREATION_STATUS_UNSPECIFIED
}

func (x *BankCustomerInfo) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *BankCustomerInfo) GetCustomerType() BankCustomerInfo_CustomerType {
	if x != nil {
		return x.CustomerType
	}
	return BankCustomerInfo_CUSTOMER_TYPE_UNSPECIFIED
}

func (x *BankCustomerInfo) GetOriginalKycLevelWithVendor() kyc.KYCLevel {
	if x != nil {
		return x.OriginalKycLevelWithVendor
	}
	return kyc.KYCLevel(0)
}

func (x *BankCustomerInfo) GetKycLevel() kyc.KYCLevel {
	if x != nil {
		return x.KycLevel
	}
	return kyc.KYCLevel(0)
}

func (x *BankCustomerInfo) GetCreationStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreationStartedAt
	}
	return nil
}

func (x *BankCustomerInfo) GetVendorCreationSucceededAt() *timestamppb.Timestamp {
	if x != nil {
		return x.VendorCreationSucceededAt
	}
	return nil
}

func (x *BankCustomerInfo) GetFiCreationSucceededAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FiCreationSucceededAt
	}
	return nil
}

func (x *BankCustomerInfo) GetEkycRrnNo() string {
	if x != nil {
		return x.EkycRrnNo
	}
	return ""
}

func (x *BankCustomerInfo) GetKycLevelUpdateFlow() KycLevelUpdateFlow {
	if x != nil {
		return x.KycLevelUpdateFlow
	}
	return KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_UNSPECIFIED
}

// Message representing information regarding the customer creation retry
type QueueRetryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of queue msg for retry debugging
	QueueMsgId string `protobuf:"bytes,1,opt,name=queue_msg_id,json=queueMsgId,proto3" json:"queue_msg_id,omitempty"`
	// epifi generated id customer creation
	ReqId string `protobuf:"bytes,2,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// number of attempts that has been made to create customer
	Attempts int32 `protobuf:"varint,3,opt,name=attempts,proto3" json:"attempts,omitempty"`
}

func (x *QueueRetryInfo) Reset() {
	*x = QueueRetryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueueRetryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueueRetryInfo) ProtoMessage() {}

func (x *QueueRetryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueueRetryInfo.ProtoReflect.Descriptor instead.
func (*QueueRetryInfo) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{8}
}

func (x *QueueRetryInfo) GetQueueMsgId() string {
	if x != nil {
		return x.QueueMsgId
	}
	return ""
}

func (x *QueueRetryInfo) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *QueueRetryInfo) GetAttempts() int32 {
	if x != nil {
		return x.Attempts
	}
	return 0
}

type ShippingPreference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID for the record in the database, if the preference info is persisted in the database
	Id           string               `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId      string               `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ShippingItem typesv2.ShippingItem `protobuf:"varint,3,opt,name=shipping_item,json=shippingItem,proto3,enum=api.typesv2.ShippingItem" json:"shipping_item,omitempty"`
	AddressType  typesv2.AddressType  `protobuf:"varint,4,opt,name=address_type,json=addressType,proto3,enum=api.typesv2.AddressType" json:"address_type,omitempty"`
}

func (x *ShippingPreference) Reset() {
	*x = ShippingPreference{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShippingPreference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShippingPreference) ProtoMessage() {}

func (x *ShippingPreference) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShippingPreference.ProtoReflect.Descriptor instead.
func (*ShippingPreference) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{9}
}

func (x *ShippingPreference) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ShippingPreference) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ShippingPreference) GetShippingItem() typesv2.ShippingItem {
	if x != nil {
		return x.ShippingItem
	}
	return typesv2.ShippingItem(0)
}

func (x *ShippingPreference) GetAddressType() typesv2.AddressType {
	if x != nil {
		return x.AddressType
	}
	return typesv2.AddressType(0)
}

type UserUpdateEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique id to represent an event uniquely(uuid for now)
	EventId string `protobuf:"bytes,1,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`
	// time at which the event was published
	EventTimestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=event_timestamp,json=eventTimestamp,proto3" json:"event_timestamp,omitempty"`
	// common request header across all the consumer grpc services.
	// contains important information related to message retry state.
	// passed along by queue subscriber.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,3,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Mandatory
	ActorId string `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// user id
	UserId string `protobuf:"bytes,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// customer_id created at federal's end
	FedCustomerId string `protobuf:"bytes,6,opt,name=fed_customer_id,json=fedCustomerId,proto3" json:"fed_customer_id,omitempty"`
	// account type - min account/full account depends on kyc level
	KycLevel               kyc.KYCLevel                `protobuf:"varint,7,opt,name=kyc_level,json=kycLevel,proto3,enum=kyc.KYCLevel" json:"kyc_level,omitempty"`
	VendorStatus           *vendorgateway.VendorStatus `protobuf:"bytes,8,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
	CustomerCreationStatus CustomerCreationStatus      `protobuf:"varint,9,opt,name=customer_creation_status,json=customerCreationStatus,proto3,enum=user.CustomerCreationStatus" json:"customer_creation_status,omitempty"`
}

func (x *UserUpdateEvent) Reset() {
	*x = UserUpdateEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserUpdateEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserUpdateEvent) ProtoMessage() {}

func (x *UserUpdateEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserUpdateEvent.ProtoReflect.Descriptor instead.
func (*UserUpdateEvent) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{10}
}

func (x *UserUpdateEvent) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *UserUpdateEvent) GetEventTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.EventTimestamp
	}
	return nil
}

func (x *UserUpdateEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UserUpdateEvent) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UserUpdateEvent) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserUpdateEvent) GetFedCustomerId() string {
	if x != nil {
		return x.FedCustomerId
	}
	return ""
}

func (x *UserUpdateEvent) GetKycLevel() kyc.KYCLevel {
	if x != nil {
		return x.KycLevel
	}
	return kyc.KYCLevel(0)
}

func (x *UserUpdateEvent) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

func (x *UserUpdateEvent) GetCustomerCreationStatus() CustomerCreationStatus {
	if x != nil {
		return x.CustomerCreationStatus
	}
	return CustomerCreationStatus_CUSTOMER_CREATION_STATUS_UNSPECIFIED
}

type UserDeviceProperty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier of type uuid
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// foreign key to actor for whom we are storing the device properties
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// phone number associated with the user
	PhoneNumber string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// enum for type of property that we are storing. e.g device_language, device location_token etc.
	DeviceProperty typesv2.DeviceProperty `protobuf:"varint,4,opt,name=device_property,json=deviceProperty,proto3,enum=api.typesv2.DeviceProperty" json:"device_property,omitempty"`
	// value of the property that we are storing, value in the PropertyValue message
	// could be one of various types of device properties
	PropertyValue *typesv2.PropertyValue `protobuf:"bytes,5,opt,name=property_value,json=propertyValue,proto3" json:"property_value,omitempty"`
	// timestamp corresponding to  deletion of entry
	DeletedAtUnix int64 `protobuf:"varint,6,opt,name=deleted_at_unix,json=deletedAtUnix,proto3" json:"deleted_at_unix,omitempty"`
	// timestamp corresponding to creation of entry
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *UserDeviceProperty) Reset() {
	*x = UserDeviceProperty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserDeviceProperty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDeviceProperty) ProtoMessage() {}

func (x *UserDeviceProperty) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDeviceProperty.ProtoReflect.Descriptor instead.
func (*UserDeviceProperty) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{11}
}

func (x *UserDeviceProperty) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UserDeviceProperty) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UserDeviceProperty) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *UserDeviceProperty) GetDeviceProperty() typesv2.DeviceProperty {
	if x != nil {
		return x.DeviceProperty
	}
	return typesv2.DeviceProperty(0)
}

func (x *UserDeviceProperty) GetPropertyValue() *typesv2.PropertyValue {
	if x != nil {
		return x.PropertyValue
	}
	return nil
}

func (x *UserDeviceProperty) GetDeletedAtUnix() int64 {
	if x != nil {
		return x.DeletedAtUnix
	}
	return 0
}

func (x *UserDeviceProperty) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// contains acquisition related details
type AcquisitionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Platform used to register with fi
	Platform common.Platform `protobuf:"varint,1,opt,name=platform,proto3,enum=api.typesv2.common.Platform" json:"platform,omitempty"`
	// source from which acquisition happened, can be like B2B,Facebook,etc.
	// Note: this stores the raw string of source in case the enum field `AcquisitionChannel` doesn't support
	// the identified source.
	AcquisitionSource string `protobuf:"bytes,2,opt,name=acquisition_source,json=acquisitionSource,proto3" json:"acquisition_source,omitempty"`
	// url used to register with fi when platform is web
	WebUrl string `protobuf:"bytes,3,opt,name=web_url,json=webUrl,proto3" json:"web_url,omitempty"`
	// source/channel of acquiring the user from.
	// Note: it's an enum entry for `acquisition_source` field.
	AcquisitionChannel AcquisitionChannel `protobuf:"varint,4,opt,name=acquisition_channel,json=acquisitionChannel,proto3,enum=user.AcquisitionChannel" json:"acquisition_channel,omitempty"`
	// Intent of the user coming on Fi app.
	// If intent can't be identified, it shall be considered as BANKING.
	// Note: it's an enum entry for `acquisition_intent_raw` field.
	AcquisitionIntent AcquisitionIntent `protobuf:"varint,5,opt,name=acquisition_intent,json=acquisitionIntent,proto3,enum=user.AcquisitionIntent" json:"acquisition_intent,omitempty"`
	// Intent of the user coming on Fi app.
	// Note: this stores the raw string of intent in case the enum field `AcquisitionIntent` doesn't support
	// the identified intent.
	AcquisitionIntentRaw string `protobuf:"bytes,6,opt,name=acquisition_intent_raw,json=acquisitionIntentRaw,proto3" json:"acquisition_intent_raw,omitempty"`
	// Raw attribution details which helps in determining the source and intent of the user
	AttributionDetails *AttributionDetails `protobuf:"bytes,7,opt,name=attribution_details,json=attributionDetails,proto3" json:"attribution_details,omitempty"`
}

func (x *AcquisitionInfo) Reset() {
	*x = AcquisitionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcquisitionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcquisitionInfo) ProtoMessage() {}

func (x *AcquisitionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcquisitionInfo.ProtoReflect.Descriptor instead.
func (*AcquisitionInfo) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{12}
}

func (x *AcquisitionInfo) GetPlatform() common.Platform {
	if x != nil {
		return x.Platform
	}
	return common.Platform(0)
}

func (x *AcquisitionInfo) GetAcquisitionSource() string {
	if x != nil {
		return x.AcquisitionSource
	}
	return ""
}

func (x *AcquisitionInfo) GetWebUrl() string {
	if x != nil {
		return x.WebUrl
	}
	return ""
}

func (x *AcquisitionInfo) GetAcquisitionChannel() AcquisitionChannel {
	if x != nil {
		return x.AcquisitionChannel
	}
	return AcquisitionChannel_ACQUISITION_CHANNEL_UNSPECIFIED
}

func (x *AcquisitionInfo) GetAcquisitionIntent() AcquisitionIntent {
	if x != nil {
		return x.AcquisitionIntent
	}
	return AcquisitionIntent_ACQUISITION_INTENT_UNSPECIFIED
}

func (x *AcquisitionInfo) GetAcquisitionIntentRaw() string {
	if x != nil {
		return x.AcquisitionIntentRaw
	}
	return ""
}

func (x *AcquisitionInfo) GetAttributionDetails() *AttributionDetails {
	if x != nil {
		return x.AttributionDetails
	}
	return nil
}

type AttributionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appsflyer callback with attribution details
	AppsflyerAttributionData *structpb.Struct `protobuf:"bytes,1,opt,name=appsflyer_attribution_data,json=appsflyerAttributionData,proto3" json:"appsflyer_attribution_data,omitempty"`
	// Attribution details of the user retrieved from Google's Install Referrer API
	// Ref - https://developer.android.com/google/play/installreferrer
	InstallReferrerData *structpb.Struct `protobuf:"bytes,2,opt,name=install_referrer_data,json=installReferrerData,proto3" json:"install_referrer_data,omitempty"`
}

func (x *AttributionDetails) Reset() {
	*x = AttributionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttributionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttributionDetails) ProtoMessage() {}

func (x *AttributionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttributionDetails.ProtoReflect.Descriptor instead.
func (*AttributionDetails) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{13}
}

func (x *AttributionDetails) GetAppsflyerAttributionData() *structpb.Struct {
	if x != nil {
		return x.AppsflyerAttributionData
	}
	return nil
}

func (x *AttributionDetails) GetInstallReferrerData() *structpb.Struct {
	if x != nil {
		return x.InstallReferrerData
	}
	return nil
}

type DataVerificationDetail_EmploymentDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmploymentType   typesv2.EmploymentType `protobuf:"varint,1,opt,name=employment_type,json=employmentType,proto3,enum=api.typesv2.EmploymentType" json:"employment_type,omitempty"`
	OrganizationName string                 `protobuf:"bytes,2,opt,name=organization_name,json=organizationName,proto3" json:"organization_name,omitempty"`
	// applicable for salaried individuals
	MonthlyIncome  *money.Money              `protobuf:"bytes,3,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
	WorkEmail      string                    `protobuf:"bytes,4,opt,name=work_email,json=workEmail,proto3" json:"work_email,omitempty"`
	OccupationType employment.OccupationType `protobuf:"varint,5,opt,name=occupation_type,json=occupationType,proto3,enum=employment.OccupationType" json:"occupation_type,omitempty"`
	// applicable for business owners
	GSTIN string `protobuf:"bytes,6,opt,name=GSTIN,proto3" json:"GSTIN,omitempty"`
	// applicable for business owners
	AnnualRevenue *money.Money `protobuf:"bytes,7,opt,name=annual_revenue,json=annualRevenue,proto3" json:"annual_revenue,omitempty"`
}

func (x *DataVerificationDetail_EmploymentDetail) Reset() {
	*x = DataVerificationDetail_EmploymentDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataVerificationDetail_EmploymentDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataVerificationDetail_EmploymentDetail) ProtoMessage() {}

func (x *DataVerificationDetail_EmploymentDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataVerificationDetail_EmploymentDetail.ProtoReflect.Descriptor instead.
func (*DataVerificationDetail_EmploymentDetail) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{1, 0}
}

func (x *DataVerificationDetail_EmploymentDetail) GetEmploymentType() typesv2.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return typesv2.EmploymentType(0)
}

func (x *DataVerificationDetail_EmploymentDetail) GetOrganizationName() string {
	if x != nil {
		return x.OrganizationName
	}
	return ""
}

func (x *DataVerificationDetail_EmploymentDetail) GetMonthlyIncome() *money.Money {
	if x != nil {
		return x.MonthlyIncome
	}
	return nil
}

func (x *DataVerificationDetail_EmploymentDetail) GetWorkEmail() string {
	if x != nil {
		return x.WorkEmail
	}
	return ""
}

func (x *DataVerificationDetail_EmploymentDetail) GetOccupationType() employment.OccupationType {
	if x != nil {
		return x.OccupationType
	}
	return employment.OccupationType(0)
}

func (x *DataVerificationDetail_EmploymentDetail) GetGSTIN() string {
	if x != nil {
		return x.GSTIN
	}
	return ""
}

func (x *DataVerificationDetail_EmploymentDetail) GetAnnualRevenue() *money.Money {
	if x != nil {
		return x.AnnualRevenue
	}
	return nil
}

type DataVerificationDetail_ResidenceDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResidentialAddress *typesv2.ResidentialAddress `protobuf:"bytes,1,opt,name=residential_address,json=residentialAddress,proto3" json:"residential_address,omitempty"`
	// applicable only for ResidenceTypes RENTED and PAYING_GUEST
	MonthlyRent *money.Money `protobuf:"bytes,2,opt,name=monthly_rent,json=monthlyRent,proto3" json:"monthly_rent,omitempty"`
}

func (x *DataVerificationDetail_ResidenceDetails) Reset() {
	*x = DataVerificationDetail_ResidenceDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_user_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataVerificationDetail_ResidenceDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataVerificationDetail_ResidenceDetails) ProtoMessage() {}

func (x *DataVerificationDetail_ResidenceDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_user_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataVerificationDetail_ResidenceDetails.ProtoReflect.Descriptor instead.
func (*DataVerificationDetail_ResidenceDetails) Descriptor() ([]byte, []int) {
	return file_api_user_user_proto_rawDescGZIP(), []int{1, 1}
}

func (x *DataVerificationDetail_ResidenceDetails) GetResidentialAddress() *typesv2.ResidentialAddress {
	if x != nil {
		return x.ResidentialAddress
	}
	return nil
}

func (x *DataVerificationDetail_ResidenceDetails) GetMonthlyRent() *money.Money {
	if x != nil {
		return x.MonthlyRent
	}
	return nil
}

var File_api_user_user_proto protoreflect.FileDescriptor

var file_api_user_user_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x75, 0x73, 0x65, 0x72, 0x1a, 0x24, 0x61, 0x70, 0x69,
	0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x6b, 0x79, 0x63, 0x2f, 0x6b, 0x79, 0x63, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64,
	0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d, 0x61,
	0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x73, 0x68, 0x69,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x16, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x70, 0x6f, 0x73, 0x74,
	0x61, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9d, 0x05, 0x0a, 0x04, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x27, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x4b,
	0x0a, 0x13, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x48, 0x0a, 0x12, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x10, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4d, 0x0a, 0x15, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x13, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x40, 0x0a, 0x10, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x40, 0x0a, 0x10, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x59, 0x0a, 0x19, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x17, 0x64, 0x61, 0x74, 0x61,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x39,
	0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xbb, 0x09, 0x0a, 0x16, 0x44, 0x61,
	0x74, 0x61, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x2b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x70, 0x61, 0x6e, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x27, 0x0a, 0x05, 0x64, 0x5f, 0x6f, 0x5f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x03, 0x64, 0x4f, 0x42, 0x12, 0x35, 0x0a, 0x08, 0x70,
	0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x48, 0x00, 0x52, 0x07, 0x70, 0x61, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x5c, 0x0a, 0x11, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x10,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x43, 0x0a, 0x0e, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x0d, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x58, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x52, 0x65, 0x73,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52,
	0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x46, 0x0a, 0x13, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x12, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x49, 0x0a, 0x13, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x12,
	0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0x3f, 0x0a, 0x0d, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x1a, 0xf5, 0x02, 0x0a, 0x10, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x44, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b,
	0x0a, 0x11, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x6d,
	0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79,
	0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x43, 0x0a, 0x0f, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a,
	0x2e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x63, 0x63, 0x75,
	0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x6f, 0x63, 0x63, 0x75,
	0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x47, 0x53,
	0x54, 0x49, 0x4e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x47, 0x53, 0x54, 0x49, 0x4e,
	0x12, 0x39, 0x0a, 0x0e, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e,
	0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x61, 0x6e,
	0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x1a, 0x9b, 0x01, 0x0a, 0x10,
	0x52, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x50, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x73, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x12,
	0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x35, 0x0a, 0x0c, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x72, 0x65,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x6e, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x73, 0x0a, 0x17, 0x44, 0x61, 0x74, 0x61, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x58, 0x0a, 0x19, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x17, 0x64, 0x61, 0x74, 0x61, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xf1, 0x04, 0x0a,
	0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x4d, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52,
	0x0e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22,
	0x8e, 0x04, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41, 0x46, 0x55, 0x5f, 0x42, 0x45, 0x46, 0x4f, 0x52,
	0x45, 0x5f, 0x44, 0x45, 0x56, 0x5f, 0x52, 0x45, 0x47, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x44,
	0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41,
	0x46, 0x55, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x4c, 0x49, 0x43, 0x54, 0x5f, 0x50, 0x48, 0x4f, 0x4e,
	0x45, 0x10, 0x02, 0x12, 0x26, 0x0a, 0x22, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41, 0x46, 0x55, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x4c,
	0x49, 0x43, 0x54, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x03, 0x12, 0x30, 0x0a, 0x2c, 0x44,
	0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41,
	0x46, 0x55, 0x5f, 0x42, 0x45, 0x46, 0x4f, 0x52, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x23, 0x0a,
	0x1f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48,
	0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x10, 0x06, 0x12, 0x2e,
	0x0a, 0x2a, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f,
	0x4e, 0x5f, 0x4f, 0x4c, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x4a,
	0x4f, 0x55, 0x52, 0x4e, 0x45, 0x59, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x10, 0x07, 0x12, 0x2a,
	0x0a, 0x26, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f,
	0x4e, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x08, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x45,
	0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x44, 0x45,
	0x56, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x45,
	0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x43, 0x58,
	0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x0a, 0x12, 0x2a, 0x0a, 0x26, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41, 0x41, 0x44, 0x48,
	0x41, 0x52, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x10, 0x0b, 0x12, 0x2a, 0x0a, 0x26, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4e, 0x52, 0x49, 0x5f, 0x57, 0x49, 0x54, 0x48,
	0x5f, 0x49, 0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x50, 0x48, 0x5f, 0x4e, 0x55, 0x4d, 0x10, 0x0c,
	0x22, 0x83, 0x02, 0x0a, 0x10, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x45, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x76, 0x6f, 0x6b, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x69, 0x73, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x64, 0x12, 0x30, 0x0a, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x79, 0x3a, 0x02, 0x18, 0x01, 0x22, 0xc9, 0x02, 0x0a, 0x13, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4a,
	0x0a, 0x14, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x12, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x76, 0x6f, 0x6b, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0e,
	0x72, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52,
	0x0d, 0x72, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x79, 0x22, 0x9d, 0x0d, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x30,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x35, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65,
	0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x3a, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x10, 0x0a,
	0x03, 0x50, 0x41, 0x4e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x50, 0x41, 0x4e, 0x12,
	0x2e, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12,
	0x39, 0x0a, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a,
	0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x66, 0x61,
	0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3b, 0x0a, 0x0a, 0x6c, 0x65, 0x67, 0x61, 0x6c,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x6c, 0x65, 0x67, 0x61, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05,
	0x70, 0x68, 0x6f, 0x74, 0x6f, 0x12, 0x33, 0x0a, 0x08, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x07, 0x6b, 0x79, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x08, 0x70, 0x61,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x07, 0x70, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x45, 0x0a, 0x12, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x79,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0f, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x0f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0d, 0x64, 0x65, 0x62, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x0c, 0x73, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x0b, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x37,
	0x0a, 0x0a, 0x67, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x09, 0x67, 0x6d,
	0x61, 0x69, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x1a, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x33, 0x5f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x33, 0x46, 0x69, 0x6c, 0x65, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x2e, 0x0a, 0x13, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x0a, 0x6b, 0x79, 0x63, 0x5f, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x09, 0x6b, 0x79,
	0x63, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x0a, 0x67, 0x69, 0x76, 0x65, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x09, 0x67, 0x69, 0x76, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x36, 0x0a, 0x0c, 0x67, 0x69, 0x76, 0x65, 0x6e, 0x5f, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x0b, 0x67, 0x69, 0x76,
	0x65, 0x6e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x0d, 0x71, 0x75, 0x61, 0x6c,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x51, 0x75,
	0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x71, 0x75, 0x61,
	0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x65,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e,
	0x69, 0x74, 0x79, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74,
	0x79, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x12, 0x31, 0x0a, 0x08,
	0x72, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x6c,
	0x69, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x6f, 0x6e, 0x12,
	0x31, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x1d, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x12, 0x44, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x58, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xcf, 0x06, 0x0a, 0x10, 0x42, 0x61, 0x6e, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x56, 0x0a, 0x18, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x16, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x0d,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x51, 0x0a, 0x1e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x6b, 0x79, 0x63, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x77, 0x69, 0x74,
	0x68, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d,
	0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x4b, 0x59, 0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x1a, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x4b, 0x79, 0x63, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x57,
	0x69, 0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x2a, 0x0a, 0x09, 0x6b, 0x79, 0x63,
	0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x6b,
	0x79, 0x63, 0x2e, 0x4b, 0x59, 0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x08, 0x6b, 0x79, 0x63,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x4a, 0x0a, 0x13, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x11,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x5b, 0x0a, 0x1c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x19, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x53,
	0x0a, 0x18, 0x66, 0x69, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x15, 0x66, 0x69,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x65, 0x6b, 0x79, 0x63, 0x5f, 0x72, 0x72, 0x6e, 0x5f,
	0x6e, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6b, 0x79, 0x63, 0x52, 0x72,
	0x6e, 0x4e, 0x6f, 0x12, 0x4b, 0x0a, 0x15, 0x6b, 0x79, 0x63, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x4b, 0x79, 0x63, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x12, 0x6b, 0x79,
	0x63, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6c, 0x6f, 0x77,
	0x22, 0x42, 0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1d, 0x0a, 0x19, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x13, 0x0a, 0x0f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x45, 0x52, 0x10, 0x01, 0x22, 0x65, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x74,
	0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f,
	0x6d, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x22, 0xc5, 0x01, 0x0a, 0x12,
	0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0d, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x53, 0x68, 0x69, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0c, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x3b, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x22, 0xd8, 0x03, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x43, 0x0a, 0x0f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0f, 0x66, 0x65, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x65, 0x64, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x09, 0x6b, 0x79, 0x63, 0x5f,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x6b, 0x79,
	0x63, 0x2e, 0x4b, 0x59, 0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x08, 0x6b, 0x79, 0x63, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x56, 0x0a, 0x18, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x16, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xce,
	0x02, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x41, 0x0a, 0x0e, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x26, 0x0a, 0x0f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x78, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x55, 0x6e, 0x69, 0x78, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0xa7, 0x03, 0x0a, 0x0f, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x2d, 0x0a,
	0x12, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x77, 0x65, 0x62, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77,
	0x65, 0x62, 0x55, 0x72, 0x6c, 0x12, 0x49, 0x0a, 0x13, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x12, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x12, 0x46, 0x0a, 0x12, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x61, 0x77, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x61, 0x63, 0x71, 0x75, 0x69, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x61, 0x77, 0x12, 0x49,
	0x0a, 0x13, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xb8, 0x01, 0x0a, 0x12, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x55, 0x0a, 0x1a, 0x61, 0x70, 0x70, 0x73, 0x66, 0x6c, 0x79, 0x65, 0x72, 0x5f, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x18, 0x61,
	0x70, 0x70, 0x73, 0x66, 0x6c, 0x79, 0x65, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4b, 0x0a, 0x15, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52,
	0x13, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x2a, 0x80, 0x01, 0x0a, 0x12, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x76, 0x6f, 0x6b, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x20, 0x41,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f,
	0x4b, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45,
	0x44, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45,
	0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x42, 0x4c,
	0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x88, 0x01, 0x0a, 0x13, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x25, 0x0a, 0x21, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45,
	0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x02, 0x1a, 0x02,
	0x18, 0x01, 0x2a, 0xbd, 0x07, 0x0a, 0x12, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76,
	0x6f, 0x6b, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f,
	0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x31, 0x0a, 0x2d, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x10, 0x01, 0x12, 0x22, 0x0a, 0x1a, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56,
	0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x53, 0x50, 0x4f, 0x4f, 0x46,
	0x10, 0x02, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x10, 0x03, 0x12, 0x2f, 0x0a, 0x2b, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4d,
	0x49, 0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x45,
	0x58, 0x50, 0x49, 0x52, 0x59, 0x10, 0x04, 0x12, 0x2b, 0x0a, 0x27, 0x41, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x46, 0x52, 0x41, 0x55, 0x44, 0x55, 0x4c, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x10, 0x05, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52,
	0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x52,
	0x45, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x10, 0x06, 0x12, 0x2b, 0x0a,
	0x27, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x49, 0x4e,
	0x44, 0x49, 0x43, 0x41, 0x54, 0x4f, 0x52, 0x53, 0x10, 0x07, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x4e, 0x4e, 0x59, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x41, 0x42,
	0x55, 0x53, 0x45, 0x10, 0x08, 0x12, 0x37, 0x0a, 0x33, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f,
	0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x43, 0x45, 0x49, 0x56, 0x45, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x46, 0x52, 0x4f,
	0x4d, 0x5f, 0x46, 0x52, 0x41, 0x55, 0x44, 0x53, 0x54, 0x45, 0x52, 0x53, 0x10, 0x09, 0x12, 0x32,
	0x0a, 0x2e, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x5f, 0x41, 0x54, 0x4d, 0x5f,
	0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x10, 0x0a, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56,
	0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x4d, 0x5f, 0x41, 0x4c,
	0x45, 0x52, 0x54, 0x10, 0x0b, 0x12, 0x2c, 0x0a, 0x28, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f,
	0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x4d,
	0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x10, 0x0c, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45,
	0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x41, 0x5f,
	0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x10, 0x0d, 0x12, 0x24, 0x0a, 0x20, 0x41,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52, 0x59, 0x10,
	0x0e, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f,
	0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4e, 0x50, 0x43, 0x49, 0x5f, 0x43,
	0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x10, 0x0f, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x53,
	0x10, 0x10, 0x12, 0x2e, 0x0a, 0x2a, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56,
	0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4c, 0x53, 0x4f, 0x5f, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x11, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56,
	0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b,
	0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x12, 0x12, 0x43, 0x0a,
	0x3f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4f, 0x4c, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x55,
	0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x45,
	0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x5f, 0x50, 0x4f, 0x53, 0x54, 0x5f, 0x41, 0x46, 0x55,
	0x10, 0x13, 0x12, 0x2e, 0x0a, 0x2a, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56,
	0x4f, 0x4b, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x5f, 0x56, 0x4b,
	0x59, 0x43, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44,
	0x10, 0x14, 0x2a, 0x88, 0x03, 0x0a, 0x13, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x43, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x53,
	0x54, 0x4f, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x44, 0x55, 0x45, 0x5f,
	0x44, 0x49, 0x4c, 0x49, 0x47, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26, 0x41,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x52, 0x45,
	0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x4f, 0x55,
	0x54, 0x43, 0x41, 0x4c, 0x4c, 0x10, 0x02, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x10, 0x03,
	0x12, 0x20, 0x0a, 0x1c, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x4f,
	0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53,
	0x10, 0x04, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x53,
	0x54, 0x4f, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x41, 0x5f,
	0x55, 0x4e, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x10, 0x05, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45,
	0x10, 0x06, 0x12, 0x2e, 0x0a, 0x2a, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x53,
	0x54, 0x4f, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x43, 0x43, 0x5f, 0x4f, 0x52, 0x5f, 0x50, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x10, 0x07, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x53,
	0x54, 0x4f, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4f, 0x4c,
	0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x4c, 0x49, 0x46, 0x54, 0x45, 0x44, 0x10, 0x08, 0x2a, 0x85, 0x01,
	0x0a, 0x11, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45,
	0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x53, 0x4f, 0x46, 0x54, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f,
	0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x4c, 0x49, 0x53, 0x54, 0x45, 0x44, 0x10,
	0x02, 0x1a, 0x02, 0x18, 0x01, 0x2a, 0xeb, 0x01, 0x0a, 0x16, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x28, 0x0a, 0x24, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e,
	0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x5f,
	0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x13, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x1a, 0x02, 0x08, 0x01,
	0x12, 0x13, 0x0a, 0x0f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x4d, 0x45, 0x52, 0x10, 0x06, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x45, 0x54,
	0x52, 0x59, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x10, 0x07, 0x12, 0x1f, 0x0a, 0x1b, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x10, 0x08, 0x2a, 0xaa, 0x05, 0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c,
	0x45, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x41,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55,
	0x4d, 0x42, 0x45, 0x52, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10,
	0x05, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x41, 0x4e, 0x10, 0x06, 0x12, 0x07, 0x0a, 0x03, 0x44, 0x4f,
	0x42, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10,
	0x08, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x0f, 0x12,
	0x13, 0x0a, 0x0f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x10, 0x09, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x48, 0x4f, 0x54, 0x4f, 0x10, 0x0a, 0x12, 0x19, 0x0a,
	0x11, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x55,
	0x52, 0x4c, 0x10, 0x0b, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x4f, 0x54, 0x48,
	0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x0c, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x41, 0x54,
	0x48, 0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x52,
	0x49, 0x56, 0x41, 0x43, 0x59, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x0e,
	0x12, 0x09, 0x0a, 0x05, 0x50, 0x48, 0x4f, 0x54, 0x4f, 0x10, 0x10, 0x12, 0x10, 0x0a, 0x0c, 0x53,
	0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x11, 0x12, 0x0d, 0x0a,
	0x09, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x45, 0x53, 0x10, 0x12, 0x12, 0x16, 0x0a, 0x12,
	0x4c, 0x45, 0x47, 0x41, 0x4c, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x42, 0x59, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x10, 0x13, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52,
	0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x14, 0x12, 0x1e, 0x0a, 0x1a,
	0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x33,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x54, 0x48, 0x10, 0x15, 0x12, 0x19, 0x0a, 0x15,
	0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x16, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x43, 0x51, 0x55, 0x49,
	0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x17, 0x12, 0x0e, 0x0a,
	0x0a, 0x4b, 0x59, 0x43, 0x5f, 0x47, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x10, 0x18, 0x12, 0x0e, 0x0a,
	0x0a, 0x47, 0x49, 0x56, 0x45, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x19, 0x12, 0x10, 0x0a,
	0x0c, 0x47, 0x49, 0x56, 0x45, 0x4e, 0x5f, 0x47, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x10, 0x1a, 0x12,
	0x1d, 0x0a, 0x19, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x1b, 0x12, 0x0c,
	0x0a, 0x08, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x1c, 0x12, 0x11, 0x0a, 0x0d,
	0x51, 0x55, 0x41, 0x4c, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x1d, 0x12,
	0x0f, 0x0a, 0x0b, 0x44, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x1e,
	0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4d, 0x4d, 0x55, 0x4e, 0x49, 0x54, 0x59, 0x10, 0x1f, 0x12,
	0x0c, 0x0a, 0x08, 0x52, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x4f, 0x4e, 0x10, 0x20, 0x12, 0x0c, 0x0a,
	0x08, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0x21, 0x12, 0x13, 0x0a, 0x0f, 0x44,
	0x49, 0x53, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x22,
	0x2a, 0xd1, 0x06, 0x0a, 0x12, 0x4b, 0x79, 0x63, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x25, 0x0a, 0x21, 0x4b, 0x59, 0x43, 0x5f, 0x4c,
	0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29,
	0x0a, 0x25, 0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x01, 0x12, 0x2e, 0x0a, 0x2a, 0x4b, 0x59, 0x43,
	0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x4f, 0x53, 0x54, 0x5f, 0x4f, 0x4e, 0x42,
	0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29, 0x4b, 0x59, 0x43,
	0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x4c, 0x53, 0x4f, 0x10, 0x03, 0x12, 0x2b, 0x0a, 0x27, 0x4b, 0x59, 0x43, 0x5f,
	0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x4f, 0x10, 0x04, 0x12, 0x31, 0x0a, 0x2d, 0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x45, 0x56,
	0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x56,
	0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53,
	0x54, 0x55, 0x44, 0x45, 0x4e, 0x54, 0x10, 0x05, 0x12, 0x3c, 0x0a, 0x38, 0x4b, 0x59, 0x43, 0x5f,
	0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c,
	0x5f, 0x4b, 0x59, 0x43, 0x10, 0x06, 0x12, 0x3e, 0x0a, 0x3a, 0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x45,
	0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x45, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4d, 0x49, 0x53, 0x4d,
	0x41, 0x54, 0x43, 0x48, 0x10, 0x07, 0x12, 0x3b, 0x0a, 0x37, 0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x45,
	0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x4c, 0x4f, 0x57, 0x5f, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x53, 0x10, 0x08, 0x12, 0x2c, 0x0a, 0x28, 0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x56, 0x4b, 0x59,
	0x43, 0x5f, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x10,
	0x09, 0x12, 0x37, 0x0a, 0x33, 0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f,
	0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x52,
	0x45, 0x4f, 0x50, 0x45, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x0a, 0x12, 0x2e, 0x0a, 0x2a, 0x4b, 0x59,
	0x43, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x42, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x4f, 0x53, 0x54, 0x5f, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x0b, 0x12, 0x31, 0x0a, 0x2d, 0x4b, 0x59,
	0x43, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x0c, 0x12, 0x27, 0x0a,
	0x23, 0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x53, 0x10, 0x0d, 0x12, 0x4d, 0x0a, 0x49, 0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x45,
	0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x4b,
	0x59, 0x43, 0x5f, 0x4c, 0x41, 0x54, 0x45, 0x53, 0x54, 0x5f, 0x4e, 0x4f, 0x5f, 0x44, 0x45, 0x44,
	0x55, 0x50, 0x45, 0x10, 0x0e, 0x12, 0x2d, 0x0a, 0x29, 0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x45, 0x56,
	0x45, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x42,
	0x4b, 0x59, 0x43, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x0f, 0x2a, 0xfb, 0x02, 0x0a, 0x12, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x23, 0x0a, 0x1f, 0x41,
	0x43, 0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e,
	0x45, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x17, 0x0a, 0x13, 0x53, 0x54, 0x55, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x47,
	0x52, 0x41, 0x4d, 0x5f, 0x56, 0x49, 0x54, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x32, 0x42,
	0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x10,
	0x02, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x41, 0x46, 0x46, 0x49, 0x4c, 0x49, 0x41,
	0x54, 0x45, 0x53, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x47, 0x4f, 0x4f,
	0x47, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x46, 0x41, 0x43,
	0x45, 0x42, 0x4f, 0x4f, 0x4b, 0x10, 0x05, 0x12, 0x28, 0x0a, 0x24, 0x41, 0x43, 0x51, 0x55, 0x49,
	0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x41,
	0x50, 0x50, 0x4c, 0x45, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x41, 0x44, 0x53, 0x10,
	0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x4f, 0x52, 0x47, 0x41, 0x4e, 0x49, 0x43,
	0x10, 0x07, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52,
	0x41, 0x4c, 0x53, 0x10, 0x08, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x57, 0x45, 0x42,
	0x10, 0x09, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53,
	0x10, 0x0f, 0x2a, 0x90, 0x02, 0x0a, 0x11, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x43, 0x51, 0x55,
	0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a,
	0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x4e, 0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21,
	0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x4e, 0x54, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e,
	0x53, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x53, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x43, 0x51, 0x55,
	0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x4e,
	0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x10, 0x04, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x43,
	0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54,
	0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x05, 0x12, 0x26, 0x0a,
	0x22, 0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x54,
	0x45, 0x4e, 0x54, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59,
	0x53, 0x45, 0x52, 0x10, 0x06, 0x42, 0x42, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_user_user_proto_rawDescOnce sync.Once
	file_api_user_user_proto_rawDescData = file_api_user_user_proto_rawDesc
)

func file_api_user_user_proto_rawDescGZIP() []byte {
	file_api_user_user_proto_rawDescOnce.Do(func() {
		file_api_user_user_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_user_user_proto_rawDescData)
	})
	return file_api_user_user_proto_rawDescData
}

var file_api_user_user_proto_enumTypes = make([]protoimpl.EnumInfo, 12)
var file_api_user_user_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_api_user_user_proto_goTypes = []interface{}{
	(AccessRevokeStatus)(0),                         // 0: user.AccessRevokeStatus
	(AccessRevokedStatus)(0),                        // 1: user.AccessRevokedStatus
	(AccessRevokeReason)(0),                         // 2: user.AccessRevokeReason
	(AccessRestoreReason)(0),                        // 3: user.AccessRestoreReason
	(AccessRevokeState)(0),                          // 4: user.AccessRevokeState
	(CustomerCreationStatus)(0),                     // 5: user.CustomerCreationStatus
	(UserFieldMask)(0),                              // 6: user.UserFieldMask
	(KycLevelUpdateFlow)(0),                         // 7: user.KycLevelUpdateFlow
	(AcquisitionChannel)(0),                         // 8: user.AcquisitionChannel
	(AcquisitionIntent)(0),                          // 9: user.AcquisitionIntent
	(DeletionDetails_DeletionReason)(0),             // 10: user.DeletionDetails.DeletionReason
	(BankCustomerInfo_CustomerType)(0),              // 11: user.BankCustomerInfo.CustomerType
	(*User)(nil),                                    // 12: user.User
	(*DataVerificationDetail)(nil),                  // 13: user.DataVerificationDetail
	(*DataVerificationDetails)(nil),                 // 14: user.DataVerificationDetails
	(*DeletionDetails)(nil),                         // 15: user.DeletionDetails
	(*AccessRevokeInfo)(nil),                        // 16: user.AccessRevokeInfo
	(*AccessRevokeDetails)(nil),                     // 17: user.AccessRevokeDetails
	(*Profile)(nil),                                 // 18: user.Profile
	(*BankCustomerInfo)(nil),                        // 19: user.BankCustomerInfo
	(*QueueRetryInfo)(nil),                          // 20: user.QueueRetryInfo
	(*ShippingPreference)(nil),                      // 21: user.ShippingPreference
	(*UserUpdateEvent)(nil),                         // 22: user.UserUpdateEvent
	(*UserDeviceProperty)(nil),                      // 23: user.UserDeviceProperty
	(*AcquisitionInfo)(nil),                         // 24: user.AcquisitionInfo
	(*AttributionDetails)(nil),                      // 25: user.AttributionDetails
	(*DataVerificationDetail_EmploymentDetail)(nil), // 26: user.DataVerificationDetail.EmploymentDetail
	(*DataVerificationDetail_ResidenceDetails)(nil), // 27: user.DataVerificationDetail.ResidenceDetails
	nil,                                 // 28: user.Profile.AddressesEntry
	(*timestamppb.Timestamp)(nil),       // 29: google.protobuf.Timestamp
	(DataType)(0),                       // 30: user.DataType
	(*date.Date)(nil),                   // 31: google.type.Date
	(*common.Name)(nil),                 // 32: api.typesv2.common.Name
	(typesv2.MaritalStatus)(0),          // 33: api.typesv2.MaritalStatus
	(vendorgateway.Vendor)(0),           // 34: vendorgateway.Vendor
	(VerificationMethod)(0),             // 35: user.VerificationMethod
	(*common.PhoneNumber)(nil),          // 36: api.typesv2.common.PhoneNumber
	(*PrivacySettings)(nil),             // 37: user.PrivacySettings
	(*common.Image)(nil),                // 38: api.typesv2.common.Image
	(*SalaryRange)(nil),                 // 39: user.SalaryRange
	(typesv2.Gender)(0),                 // 40: api.typesv2.Gender
	(typesv2.Qualification)(0),          // 41: api.typesv2.Qualification
	(typesv2.Designation)(0),            // 42: api.typesv2.Designation
	(typesv2.Community)(0),              // 43: api.typesv2.Community
	(typesv2.Religion)(0),               // 44: api.typesv2.Religion
	(typesv2.Category)(0),               // 45: api.typesv2.Category
	(typesv2.DisabilityType)(0),         // 46: api.typesv2.DisabilityType
	(kyc.KYCLevel)(0),                   // 47: kyc.KYCLevel
	(typesv2.ShippingItem)(0),           // 48: api.typesv2.ShippingItem
	(typesv2.AddressType)(0),            // 49: api.typesv2.AddressType
	(*queue.ConsumerRequestHeader)(nil), // 50: queue.ConsumerRequestHeader
	(*vendorgateway.VendorStatus)(nil),  // 51: vendorgateway.VendorStatus
	(typesv2.DeviceProperty)(0),         // 52: api.typesv2.DeviceProperty
	(*typesv2.PropertyValue)(nil),       // 53: api.typesv2.PropertyValue
	(common.Platform)(0),                // 54: api.typesv2.common.Platform
	(*structpb.Struct)(nil),             // 55: google.protobuf.Struct
	(typesv2.EmploymentType)(0),         // 56: api.typesv2.EmploymentType
	(*money.Money)(nil),                 // 57: google.type.Money
	(employment.OccupationType)(0),      // 58: employment.OccupationType
	(*typesv2.ResidentialAddress)(nil),  // 59: api.typesv2.ResidentialAddress
	(*postaladdress.PostalAddress)(nil), // 60: google.type.PostalAddress
}
var file_api_user_user_proto_depIdxs = []int32{
	18, // 0: user.User.profile:type_name -> user.Profile
	19, // 1: user.User.customer_infos:type_name -> user.BankCustomerInfo
	4,  // 2: user.User.access_revoke_state:type_name -> user.AccessRevokeState
	16, // 3: user.User.access_revoke_info:type_name -> user.AccessRevokeInfo
	17, // 4: user.User.access_revoke_details:type_name -> user.AccessRevokeDetails
	24, // 5: user.User.acquisition_info:type_name -> user.AcquisitionInfo
	15, // 6: user.User.deletion_details:type_name -> user.DeletionDetails
	14, // 7: user.User.data_verification_details:type_name -> user.DataVerificationDetails
	29, // 8: user.User.deleted_at:type_name -> google.protobuf.Timestamp
	30, // 9: user.DataVerificationDetail.data_type:type_name -> user.DataType
	31, // 10: user.DataVerificationDetail.d_o_b:type_name -> google.type.Date
	32, // 11: user.DataVerificationDetail.pan_name:type_name -> api.typesv2.common.Name
	26, // 12: user.DataVerificationDetail.employment_detail:type_name -> user.DataVerificationDetail.EmploymentDetail
	33, // 13: user.DataVerificationDetail.marital_status:type_name -> api.typesv2.MaritalStatus
	27, // 14: user.DataVerificationDetail.address_details:type_name -> user.DataVerificationDetail.ResidenceDetails
	34, // 15: user.DataVerificationDetail.verification_entity:type_name -> vendorgateway.Vendor
	35, // 16: user.DataVerificationDetail.verification_method:type_name -> user.VerificationMethod
	29, // 17: user.DataVerificationDetail.verified_time:type_name -> google.protobuf.Timestamp
	13, // 18: user.DataVerificationDetails.data_verification_details:type_name -> user.DataVerificationDetail
	10, // 19: user.DeletionDetails.deletion_reason:type_name -> user.DeletionDetails.DeletionReason
	1,  // 20: user.AccessRevokeInfo.is_access_revoked:type_name -> user.AccessRevokedStatus
	2,  // 21: user.AccessRevokeInfo.reason:type_name -> user.AccessRevokeReason
	29, // 22: user.AccessRevokeInfo.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 23: user.AccessRevokeDetails.access_revoke_status:type_name -> user.AccessRevokeStatus
	2,  // 24: user.AccessRevokeDetails.reason:type_name -> user.AccessRevokeReason
	3,  // 25: user.AccessRevokeDetails.restore_reason:type_name -> user.AccessRestoreReason
	29, // 26: user.AccessRevokeDetails.updated_at:type_name -> google.protobuf.Timestamp
	32, // 27: user.Profile.name:type_name -> api.typesv2.common.Name
	31, // 28: user.Profile.date_of_birth:type_name -> google.type.Date
	36, // 29: user.Profile.phone_number:type_name -> api.typesv2.common.PhoneNumber
	28, // 30: user.Profile.addresses:type_name -> user.Profile.AddressesEntry
	32, // 31: user.Profile.mother_name:type_name -> api.typesv2.common.Name
	32, // 32: user.Profile.father_name:type_name -> api.typesv2.common.Name
	37, // 33: user.Profile.privacy_settings:type_name -> user.PrivacySettings
	32, // 34: user.Profile.legal_name:type_name -> api.typesv2.common.Name
	38, // 35: user.Profile.photo:type_name -> api.typesv2.common.Image
	32, // 36: user.Profile.kyc_name:type_name -> api.typesv2.common.Name
	32, // 37: user.Profile.pan_name:type_name -> api.typesv2.common.Name
	32, // 38: user.Profile.legal_name_by_user:type_name -> api.typesv2.common.Name
	32, // 39: user.Profile.debit_card_name:type_name -> api.typesv2.common.Name
	39, // 40: user.Profile.salary_range:type_name -> user.SalaryRange
	32, // 41: user.Profile.gmail_name:type_name -> api.typesv2.common.Name
	40, // 42: user.Profile.kyc_gender:type_name -> api.typesv2.Gender
	32, // 43: user.Profile.given_name:type_name -> api.typesv2.common.Name
	40, // 44: user.Profile.given_gender:type_name -> api.typesv2.Gender
	41, // 45: user.Profile.qualification:type_name -> api.typesv2.Qualification
	42, // 46: user.Profile.designation:type_name -> api.typesv2.Designation
	43, // 47: user.Profile.community:type_name -> api.typesv2.Community
	44, // 48: user.Profile.religion:type_name -> api.typesv2.Religion
	45, // 49: user.Profile.category:type_name -> api.typesv2.Category
	46, // 50: user.Profile.disability_type:type_name -> api.typesv2.DisabilityType
	34, // 51: user.BankCustomerInfo.vendor:type_name -> vendorgateway.Vendor
	5,  // 52: user.BankCustomerInfo.customer_creation_status:type_name -> user.CustomerCreationStatus
	32, // 53: user.BankCustomerInfo.name:type_name -> api.typesv2.common.Name
	11, // 54: user.BankCustomerInfo.customer_type:type_name -> user.BankCustomerInfo.CustomerType
	47, // 55: user.BankCustomerInfo.original_kyc_level_with_vendor:type_name -> kyc.KYCLevel
	47, // 56: user.BankCustomerInfo.kyc_level:type_name -> kyc.KYCLevel
	29, // 57: user.BankCustomerInfo.creation_started_at:type_name -> google.protobuf.Timestamp
	29, // 58: user.BankCustomerInfo.vendor_creation_succeeded_at:type_name -> google.protobuf.Timestamp
	29, // 59: user.BankCustomerInfo.fi_creation_succeeded_at:type_name -> google.protobuf.Timestamp
	7,  // 60: user.BankCustomerInfo.kyc_level_update_flow:type_name -> user.KycLevelUpdateFlow
	48, // 61: user.ShippingPreference.shipping_item:type_name -> api.typesv2.ShippingItem
	49, // 62: user.ShippingPreference.address_type:type_name -> api.typesv2.AddressType
	29, // 63: user.UserUpdateEvent.event_timestamp:type_name -> google.protobuf.Timestamp
	50, // 64: user.UserUpdateEvent.request_header:type_name -> queue.ConsumerRequestHeader
	47, // 65: user.UserUpdateEvent.kyc_level:type_name -> kyc.KYCLevel
	51, // 66: user.UserUpdateEvent.vendor_status:type_name -> vendorgateway.VendorStatus
	5,  // 67: user.UserUpdateEvent.customer_creation_status:type_name -> user.CustomerCreationStatus
	52, // 68: user.UserDeviceProperty.device_property:type_name -> api.typesv2.DeviceProperty
	53, // 69: user.UserDeviceProperty.property_value:type_name -> api.typesv2.PropertyValue
	29, // 70: user.UserDeviceProperty.created_at:type_name -> google.protobuf.Timestamp
	54, // 71: user.AcquisitionInfo.platform:type_name -> api.typesv2.common.Platform
	8,  // 72: user.AcquisitionInfo.acquisition_channel:type_name -> user.AcquisitionChannel
	9,  // 73: user.AcquisitionInfo.acquisition_intent:type_name -> user.AcquisitionIntent
	25, // 74: user.AcquisitionInfo.attribution_details:type_name -> user.AttributionDetails
	55, // 75: user.AttributionDetails.appsflyer_attribution_data:type_name -> google.protobuf.Struct
	55, // 76: user.AttributionDetails.install_referrer_data:type_name -> google.protobuf.Struct
	56, // 77: user.DataVerificationDetail.EmploymentDetail.employment_type:type_name -> api.typesv2.EmploymentType
	57, // 78: user.DataVerificationDetail.EmploymentDetail.monthly_income:type_name -> google.type.Money
	58, // 79: user.DataVerificationDetail.EmploymentDetail.occupation_type:type_name -> employment.OccupationType
	57, // 80: user.DataVerificationDetail.EmploymentDetail.annual_revenue:type_name -> google.type.Money
	59, // 81: user.DataVerificationDetail.ResidenceDetails.residential_address:type_name -> api.typesv2.ResidentialAddress
	57, // 82: user.DataVerificationDetail.ResidenceDetails.monthly_rent:type_name -> google.type.Money
	60, // 83: user.Profile.AddressesEntry.value:type_name -> google.type.PostalAddress
	84, // [84:84] is the sub-list for method output_type
	84, // [84:84] is the sub-list for method input_type
	84, // [84:84] is the sub-list for extension type_name
	84, // [84:84] is the sub-list for extension extendee
	0,  // [0:84] is the sub-list for field type_name
}

func init() { file_api_user_user_proto_init() }
func file_api_user_user_proto_init() {
	if File_api_user_user_proto != nil {
		return
	}
	file_api_user_enums_proto_init()
	file_api_user_profile_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_user_user_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataVerificationDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataVerificationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccessRevokeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccessRevokeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Profile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankCustomerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueueRetryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShippingPreference); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserUpdateEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserDeviceProperty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcquisitionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttributionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataVerificationDetail_EmploymentDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_user_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataVerificationDetail_ResidenceDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_user_user_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*DataVerificationDetail_PanNumber)(nil),
		(*DataVerificationDetail_DOB)(nil),
		(*DataVerificationDetail_PanName)(nil),
		(*DataVerificationDetail_EmploymentDetail_)(nil),
		(*DataVerificationDetail_MaritalStatus)(nil),
		(*DataVerificationDetail_AddressDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_user_user_proto_rawDesc,
			NumEnums:      12,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_user_user_proto_goTypes,
		DependencyIndexes: file_api_user_user_proto_depIdxs,
		EnumInfos:         file_api_user_user_proto_enumTypes,
		MessageInfos:      file_api_user_user_proto_msgTypes,
	}.Build()
	File_api_user_user_proto = out.File
	file_api_user_user_proto_rawDesc = nil
	file_api_user_user_proto_goTypes = nil
	file_api_user_user_proto_depIdxs = nil
}
