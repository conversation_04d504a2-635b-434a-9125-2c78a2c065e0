// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/saven/creditcard/cc_delivery_status.proto

package creditcard

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetCreditCardTrackingDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCreditCardTrackingDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditCardTrackingDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCreditCardTrackingDetailsResponseMultiError, or nil if none found.
func (m *GetCreditCardTrackingDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditCardTrackingDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTrackingData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditCardTrackingDetailsResponseValidationError{
					field:  "TrackingData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditCardTrackingDetailsResponseValidationError{
					field:  "TrackingData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTrackingData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditCardTrackingDetailsResponseValidationError{
				field:  "TrackingData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetException()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditCardTrackingDetailsResponseValidationError{
					field:  "Exception",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditCardTrackingDetailsResponseValidationError{
					field:  "Exception",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetException()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditCardTrackingDetailsResponseValidationError{
				field:  "Exception",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCreditCardTrackingDetailsResponseMultiError(errors)
	}

	return nil
}

// GetCreditCardTrackingDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetCreditCardTrackingDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCreditCardTrackingDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditCardTrackingDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditCardTrackingDetailsResponseMultiError) AllErrors() []error { return m }

// GetCreditCardTrackingDetailsResponseValidationError is the validation error
// returned by GetCreditCardTrackingDetailsResponse.Validate if the designated
// constraints aren't met.
type GetCreditCardTrackingDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditCardTrackingDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditCardTrackingDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditCardTrackingDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditCardTrackingDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditCardTrackingDetailsResponseValidationError) ErrorName() string {
	return "GetCreditCardTrackingDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditCardTrackingDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditCardTrackingDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditCardTrackingDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditCardTrackingDetailsResponseValidationError{}

// Validate checks the field values on TrackingData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TrackingData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TrackingData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TrackingDataMultiError, or
// nil if none found.
func (m *TrackingData) ValidateAll() error {
	return m.validate(true)
}

func (m *TrackingData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PickupDate

	// no validation rules for CurrentStatus

	for idx, item := range m.GetScans() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TrackingDataValidationError{
						field:  fmt.Sprintf("Scans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TrackingDataValidationError{
						field:  fmt.Sprintf("Scans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TrackingDataValidationError{
					field:  fmt.Sprintf("Scans[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Carrier

	// no validation rules for CardPrintingVendor

	// no validation rules for AwbNumber

	if all {
		switch v := interface{}(m.GetExtraFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TrackingDataValidationError{
					field:  "ExtraFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TrackingDataValidationError{
					field:  "ExtraFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtraFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TrackingDataValidationError{
				field:  "ExtraFields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TrackingUrl

	if len(errors) > 0 {
		return TrackingDataMultiError(errors)
	}

	return nil
}

// TrackingDataMultiError is an error wrapping multiple validation errors
// returned by TrackingData.ValidateAll() if the designated constraints aren't met.
type TrackingDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TrackingDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TrackingDataMultiError) AllErrors() []error { return m }

// TrackingDataValidationError is the validation error returned by
// TrackingData.Validate if the designated constraints aren't met.
type TrackingDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TrackingDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TrackingDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TrackingDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TrackingDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TrackingDataValidationError) ErrorName() string { return "TrackingDataValidationError" }

// Error satisfies the builtin error interface
func (e TrackingDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTrackingData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TrackingDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TrackingDataValidationError{}

// Validate checks the field values on Exception with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Exception) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Exception with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExceptionMultiError, or nil
// if none found.
func (m *Exception) ValidateAll() error {
	return m.validate(true)
}

func (m *Exception) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StatusCode

	// no validation rules for ErrorMessage

	if len(errors) > 0 {
		return ExceptionMultiError(errors)
	}

	return nil
}

// ExceptionMultiError is an error wrapping multiple validation errors returned
// by Exception.ValidateAll() if the designated constraints aren't met.
type ExceptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExceptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExceptionMultiError) AllErrors() []error { return m }

// ExceptionValidationError is the validation error returned by
// Exception.Validate if the designated constraints aren't met.
type ExceptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExceptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExceptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExceptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExceptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExceptionValidationError) ErrorName() string { return "ExceptionValidationError" }

// Error satisfies the builtin error interface
func (e ExceptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sException.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExceptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExceptionValidationError{}

// Validate checks the field values on Scan with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Scan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Scan with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ScanMultiError, or nil if none found.
func (m *Scan) ValidateAll() error {
	return m.validate(true)
}

func (m *Scan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ScanTime

	// no validation rules for Location

	// no validation rules for StatusDetail

	if len(errors) > 0 {
		return ScanMultiError(errors)
	}

	return nil
}

// ScanMultiError is an error wrapping multiple validation errors returned by
// Scan.ValidateAll() if the designated constraints aren't met.
type ScanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScanMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScanMultiError) AllErrors() []error { return m }

// ScanValidationError is the validation error returned by Scan.Validate if the
// designated constraints aren't met.
type ScanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScanValidationError) ErrorName() string { return "ScanValidationError" }

// Error satisfies the builtin error interface
func (e ScanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScanValidationError{}

// Validate checks the field values on ExtraFields with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExtraFields) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExtraFields with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExtraFieldsMultiError, or
// nil if none found.
func (m *ExtraFields) ValidateAll() error {
	return m.validate(true)
}

func (m *ExtraFields) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExpectedDeliveryDate

	if len(errors) > 0 {
		return ExtraFieldsMultiError(errors)
	}

	return nil
}

// ExtraFieldsMultiError is an error wrapping multiple validation errors
// returned by ExtraFields.ValidateAll() if the designated constraints aren't met.
type ExtraFieldsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExtraFieldsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExtraFieldsMultiError) AllErrors() []error { return m }

// ExtraFieldsValidationError is the validation error returned by
// ExtraFields.Validate if the designated constraints aren't met.
type ExtraFieldsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExtraFieldsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExtraFieldsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExtraFieldsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExtraFieldsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExtraFieldsValidationError) ErrorName() string { return "ExtraFieldsValidationError" }

// Error satisfies the builtin error interface
func (e ExtraFieldsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExtraFields.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExtraFieldsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExtraFieldsValidationError{}

// Validate checks the field values on GetCreditCardTrackingDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCreditCardTrackingDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditCardTrackingDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCreditCardTrackingDetailsRequestMultiError, or nil if none found.
func (m *GetCreditCardTrackingDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditCardTrackingDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InternalUserId

	if len(errors) > 0 {
		return GetCreditCardTrackingDetailsRequestMultiError(errors)
	}

	return nil
}

// GetCreditCardTrackingDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetCreditCardTrackingDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCreditCardTrackingDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditCardTrackingDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditCardTrackingDetailsRequestMultiError) AllErrors() []error { return m }

// GetCreditCardTrackingDetailsRequestValidationError is the validation error
// returned by GetCreditCardTrackingDetailsRequest.Validate if the designated
// constraints aren't met.
type GetCreditCardTrackingDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditCardTrackingDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditCardTrackingDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditCardTrackingDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditCardTrackingDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditCardTrackingDetailsRequestValidationError) ErrorName() string {
	return "GetCreditCardTrackingDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditCardTrackingDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditCardTrackingDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditCardTrackingDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditCardTrackingDetailsRequestValidationError{}
