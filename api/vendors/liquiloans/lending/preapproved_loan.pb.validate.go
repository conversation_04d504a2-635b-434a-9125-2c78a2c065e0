// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/liquiloans/lending/preapproved_loan.proto

package lending

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetCreditLineDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditLineDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditLineDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCreditLineDetailsRequestMultiError, or nil if none found.
func (m *GetCreditLineDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditLineDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicantId

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GetCreditLineDetailsRequestMultiError(errors)
	}

	return nil
}

// GetCreditLineDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by GetCreditLineDetailsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetCreditLineDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditLineDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditLineDetailsRequestMultiError) AllErrors() []error { return m }

// GetCreditLineDetailsRequestValidationError is the validation error returned
// by GetCreditLineDetailsRequest.Validate if the designated constraints
// aren't met.
type GetCreditLineDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditLineDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditLineDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditLineDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditLineDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditLineDetailsRequestValidationError) ErrorName() string {
	return "GetCreditLineDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditLineDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditLineDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditLineDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditLineDetailsRequestValidationError{}

// Validate checks the field values on GetCreditLineDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditLineDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditLineDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCreditLineDetailsResponseMultiError, or nil if none found.
func (m *GetCreditLineDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditLineDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineDetailsResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineDetailsResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineDetailsResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GetCreditLineDetailsResponseMultiError(errors)
	}

	return nil
}

// GetCreditLineDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by GetCreditLineDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetCreditLineDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditLineDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditLineDetailsResponseMultiError) AllErrors() []error { return m }

// GetCreditLineDetailsResponseValidationError is the validation error returned
// by GetCreditLineDetailsResponse.Validate if the designated constraints
// aren't met.
type GetCreditLineDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditLineDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditLineDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditLineDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditLineDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditLineDetailsResponseValidationError) ErrorName() string {
	return "GetCreditLineDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditLineDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditLineDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditLineDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditLineDetailsResponseValidationError{}

// Validate checks the field values on AddPersonalDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddPersonalDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddPersonalDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddPersonalDetailsRequestMultiError, or nil if none found.
func (m *AddPersonalDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddPersonalDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for Name

	// no validation rules for Email

	// no validation rules for ContactNumber

	// no validation rules for Pan

	// no validation rules for Gender

	// no validation rules for Dob

	// no validation rules for Checksum

	// no validation rules for Urn

	// no validation rules for Udf1

	// no validation rules for Udf8

	// no validation rules for Udf9

	// no validation rules for Udf4

	// no validation rules for Udf5

	// no validation rules for Udf2

	if len(errors) > 0 {
		return AddPersonalDetailsRequestMultiError(errors)
	}

	return nil
}

// AddPersonalDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by AddPersonalDetailsRequest.ValidateAll() if the
// designated constraints aren't met.
type AddPersonalDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddPersonalDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddPersonalDetailsRequestMultiError) AllErrors() []error { return m }

// AddPersonalDetailsRequestValidationError is the validation error returned by
// AddPersonalDetailsRequest.Validate if the designated constraints aren't met.
type AddPersonalDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddPersonalDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddPersonalDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddPersonalDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddPersonalDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddPersonalDetailsRequestValidationError) ErrorName() string {
	return "AddPersonalDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddPersonalDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddPersonalDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddPersonalDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddPersonalDetailsRequestValidationError{}

// Validate checks the field values on AddPersonalDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddPersonalDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddPersonalDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddPersonalDetailsResponseMultiError, or nil if none found.
func (m *AddPersonalDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddPersonalDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddPersonalDetailsResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddPersonalDetailsResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddPersonalDetailsResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return AddPersonalDetailsResponseMultiError(errors)
	}

	return nil
}

// AddPersonalDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by AddPersonalDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type AddPersonalDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddPersonalDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddPersonalDetailsResponseMultiError) AllErrors() []error { return m }

// AddPersonalDetailsResponseValidationError is the validation error returned
// by AddPersonalDetailsResponse.Validate if the designated constraints aren't met.
type AddPersonalDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddPersonalDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddPersonalDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddPersonalDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddPersonalDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddPersonalDetailsResponseValidationError) ErrorName() string {
	return "AddPersonalDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddPersonalDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddPersonalDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddPersonalDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddPersonalDetailsResponseValidationError{}

// Validate checks the field values on AddBankingDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddBankingDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddBankingDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddBankingDetailsRequestMultiError, or nil if none found.
func (m *AddBankingDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddBankingDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicantId

	// no validation rules for AccountType

	// no validation rules for BankName

	// no validation rules for Ifsc

	// no validation rules for AccountNumber

	// no validation rules for AccountHolderName

	// no validation rules for Sid

	// no validation rules for Checksum

	if len(errors) > 0 {
		return AddBankingDetailsRequestMultiError(errors)
	}

	return nil
}

// AddBankingDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by AddBankingDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type AddBankingDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddBankingDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddBankingDetailsRequestMultiError) AllErrors() []error { return m }

// AddBankingDetailsRequestValidationError is the validation error returned by
// AddBankingDetailsRequest.Validate if the designated constraints aren't met.
type AddBankingDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddBankingDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddBankingDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddBankingDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddBankingDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddBankingDetailsRequestValidationError) ErrorName() string {
	return "AddBankingDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddBankingDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddBankingDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddBankingDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddBankingDetailsRequestValidationError{}

// Validate checks the field values on AddAddressDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddAddressDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddAddressDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddAddressDetailsRequestMultiError, or nil if none found.
func (m *AddAddressDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddAddressDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicantId

	// no validation rules for AddressLine_1

	// no validation rules for AddressLine_2

	// no validation rules for Area

	// no validation rules for PinCode

	// no validation rules for City

	// no validation rules for State

	// no validation rules for AddressType

	// no validation rules for Sid

	// no validation rules for Checksum

	if len(errors) > 0 {
		return AddAddressDetailsRequestMultiError(errors)
	}

	return nil
}

// AddAddressDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by AddAddressDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type AddAddressDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddAddressDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddAddressDetailsRequestMultiError) AllErrors() []error { return m }

// AddAddressDetailsRequestValidationError is the validation error returned by
// AddAddressDetailsRequest.Validate if the designated constraints aren't met.
type AddAddressDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddAddressDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddAddressDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddAddressDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddAddressDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddAddressDetailsRequestValidationError) ErrorName() string {
	return "AddAddressDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddAddressDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddAddressDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddAddressDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddAddressDetailsRequestValidationError{}

// Validate checks the field values on AddEmploymentDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddEmploymentDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddEmploymentDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddEmploymentDetailsRequestMultiError, or nil if none found.
func (m *AddEmploymentDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddEmploymentDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicantId

	// no validation rules for Occupation

	// no validation rules for OrganizationName

	// no validation rules for MonthlyIncome

	// no validation rules for Checksum

	// no validation rules for Designation

	if len(errors) > 0 {
		return AddEmploymentDetailsRequestMultiError(errors)
	}

	return nil
}

// AddEmploymentDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by AddEmploymentDetailsRequest.ValidateAll() if
// the designated constraints aren't met.
type AddEmploymentDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddEmploymentDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddEmploymentDetailsRequestMultiError) AllErrors() []error { return m }

// AddEmploymentDetailsRequestValidationError is the validation error returned
// by AddEmploymentDetailsRequest.Validate if the designated constraints
// aren't met.
type AddEmploymentDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddEmploymentDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddEmploymentDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddEmploymentDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddEmploymentDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddEmploymentDetailsRequestValidationError) ErrorName() string {
	return "AddEmploymentDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddEmploymentDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddEmploymentDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddEmploymentDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddEmploymentDetailsRequestValidationError{}

// Validate checks the field values on AddDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddDetailsResponseMultiError, or nil if none found.
func (m *AddDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddDetailsResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddDetailsResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddDetailsResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return AddDetailsResponseMultiError(errors)
	}

	return nil
}

// AddDetailsResponseMultiError is an error wrapping multiple validation errors
// returned by AddDetailsResponse.ValidateAll() if the designated constraints
// aren't met.
type AddDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddDetailsResponseMultiError) AllErrors() []error { return m }

// AddDetailsResponseValidationError is the validation error returned by
// AddDetailsResponse.Validate if the designated constraints aren't met.
type AddDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddDetailsResponseValidationError) ErrorName() string {
	return "AddDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddDetailsResponseValidationError{}

// Validate checks the field values on GetLimitRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetLimitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLimitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLimitRequestMultiError, or nil if none found.
func (m *GetLimitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLimitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicantId

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GetLimitRequestMultiError(errors)
	}

	return nil
}

// GetLimitRequestMultiError is an error wrapping multiple validation errors
// returned by GetLimitRequest.ValidateAll() if the designated constraints
// aren't met.
type GetLimitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLimitRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLimitRequestMultiError) AllErrors() []error { return m }

// GetLimitRequestValidationError is the validation error returned by
// GetLimitRequest.Validate if the designated constraints aren't met.
type GetLimitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLimitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLimitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLimitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLimitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLimitRequestValidationError) ErrorName() string { return "GetLimitRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetLimitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLimitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLimitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLimitRequestValidationError{}

// Validate checks the field values on GetLimitResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetLimitResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLimitResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLimitResponseMultiError, or nil if none found.
func (m *GetLimitResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLimitResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLimitResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLimitResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLimitResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GetLimitResponseMultiError(errors)
	}

	return nil
}

// GetLimitResponseMultiError is an error wrapping multiple validation errors
// returned by GetLimitResponse.ValidateAll() if the designated constraints
// aren't met.
type GetLimitResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLimitResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLimitResponseMultiError) AllErrors() []error { return m }

// GetLimitResponseValidationError is the validation error returned by
// GetLimitResponse.Validate if the designated constraints aren't met.
type GetLimitResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLimitResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLimitResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLimitResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLimitResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLimitResponseValidationError) ErrorName() string { return "GetLimitResponseValidationError" }

// Error satisfies the builtin error interface
func (e GetLimitResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLimitResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLimitResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLimitResponseValidationError{}

// Validate checks the field values on ApplicantLookupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApplicantLookupRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicantLookupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplicantLookupRequestMultiError, or nil if none found.
func (m *ApplicantLookupRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicantLookupRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for Pan

	// no validation rules for Checksum

	if len(errors) > 0 {
		return ApplicantLookupRequestMultiError(errors)
	}

	return nil
}

// ApplicantLookupRequestMultiError is an error wrapping multiple validation
// errors returned by ApplicantLookupRequest.ValidateAll() if the designated
// constraints aren't met.
type ApplicantLookupRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantLookupRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantLookupRequestMultiError) AllErrors() []error { return m }

// ApplicantLookupRequestValidationError is the validation error returned by
// ApplicantLookupRequest.Validate if the designated constraints aren't met.
type ApplicantLookupRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantLookupRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantLookupRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicantLookupRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantLookupRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantLookupRequestValidationError) ErrorName() string {
	return "ApplicantLookupRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicantLookupRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicantLookupRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantLookupRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantLookupRequestValidationError{}

// Validate checks the field values on ApplicantLookupResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApplicantLookupResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicantLookupResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplicantLookupResponseMultiError, or nil if none found.
func (m *ApplicantLookupResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicantLookupResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicantLookupResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicantLookupResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicantLookupResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return ApplicantLookupResponseMultiError(errors)
	}

	return nil
}

// ApplicantLookupResponseMultiError is an error wrapping multiple validation
// errors returned by ApplicantLookupResponse.ValidateAll() if the designated
// constraints aren't met.
type ApplicantLookupResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantLookupResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantLookupResponseMultiError) AllErrors() []error { return m }

// ApplicantLookupResponseValidationError is the validation error returned by
// ApplicantLookupResponse.Validate if the designated constraints aren't met.
type ApplicantLookupResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantLookupResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantLookupResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicantLookupResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantLookupResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantLookupResponseValidationError) ErrorName() string {
	return "ApplicantLookupResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicantLookupResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicantLookupResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantLookupResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantLookupResponseValidationError{}

// Validate checks the field values on GetMandateLinkRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMandateLinkRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMandateLinkRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMandateLinkRequestMultiError, or nil if none found.
func (m *GetMandateLinkRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMandateLinkRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicantId

	// no validation rules for Checksum

	// no validation rules for RecreateUrl

	if len(errors) > 0 {
		return GetMandateLinkRequestMultiError(errors)
	}

	return nil
}

// GetMandateLinkRequestMultiError is an error wrapping multiple validation
// errors returned by GetMandateLinkRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMandateLinkRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMandateLinkRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMandateLinkRequestMultiError) AllErrors() []error { return m }

// GetMandateLinkRequestValidationError is the validation error returned by
// GetMandateLinkRequest.Validate if the designated constraints aren't met.
type GetMandateLinkRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMandateLinkRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMandateLinkRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMandateLinkRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMandateLinkRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMandateLinkRequestValidationError) ErrorName() string {
	return "GetMandateLinkRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMandateLinkRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMandateLinkRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMandateLinkRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMandateLinkRequestValidationError{}

// Validate checks the field values on GetMandateLinkResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMandateLinkResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMandateLinkResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMandateLinkResponseMultiError, or nil if none found.
func (m *GetMandateLinkResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMandateLinkResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMandateLinkResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMandateLinkResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMandateLinkResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GetMandateLinkResponseMultiError(errors)
	}

	return nil
}

// GetMandateLinkResponseMultiError is an error wrapping multiple validation
// errors returned by GetMandateLinkResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMandateLinkResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMandateLinkResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMandateLinkResponseMultiError) AllErrors() []error { return m }

// GetMandateLinkResponseValidationError is the validation error returned by
// GetMandateLinkResponse.Validate if the designated constraints aren't met.
type GetMandateLinkResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMandateLinkResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMandateLinkResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMandateLinkResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMandateLinkResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMandateLinkResponseValidationError) ErrorName() string {
	return "GetMandateLinkResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMandateLinkResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMandateLinkResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMandateLinkResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMandateLinkResponseValidationError{}

// Validate checks the field values on GetMandateStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMandateStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMandateStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMandateStatusRequestMultiError, or nil if none found.
func (m *GetMandateStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMandateStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicantId

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GetMandateStatusRequestMultiError(errors)
	}

	return nil
}

// GetMandateStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetMandateStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMandateStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMandateStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMandateStatusRequestMultiError) AllErrors() []error { return m }

// GetMandateStatusRequestValidationError is the validation error returned by
// GetMandateStatusRequest.Validate if the designated constraints aren't met.
type GetMandateStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMandateStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMandateStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMandateStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMandateStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMandateStatusRequestValidationError) ErrorName() string {
	return "GetMandateStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMandateStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMandateStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMandateStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMandateStatusRequestValidationError{}

// Validate checks the field values on GetMandateStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMandateStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMandateStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMandateStatusResponseMultiError, or nil if none found.
func (m *GetMandateStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMandateStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMandateStatusResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMandateStatusResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMandateStatusResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GetMandateStatusResponseMultiError(errors)
	}

	return nil
}

// GetMandateStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetMandateStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMandateStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMandateStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMandateStatusResponseMultiError) AllErrors() []error { return m }

// GetMandateStatusResponseValidationError is the validation error returned by
// GetMandateStatusResponse.Validate if the designated constraints aren't met.
type GetMandateStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMandateStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMandateStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMandateStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMandateStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMandateStatusResponseValidationError) ErrorName() string {
	return "GetMandateStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMandateStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMandateStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMandateStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMandateStatusResponseValidationError{}

// Validate checks the field values on MakeDrawdownRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MakeDrawdownRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MakeDrawdownRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MakeDrawdownRequestMultiError, or nil if none found.
func (m *MakeDrawdownRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MakeDrawdownRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicantId

	// no validation rules for Amount

	// no validation rules for TenureFrequency

	// no validation rules for Tenure

	// no validation rules for Checksum

	// no validation rules for Urn

	// no validation rules for SchemeCode

	if len(errors) > 0 {
		return MakeDrawdownRequestMultiError(errors)
	}

	return nil
}

// MakeDrawdownRequestMultiError is an error wrapping multiple validation
// errors returned by MakeDrawdownRequest.ValidateAll() if the designated
// constraints aren't met.
type MakeDrawdownRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MakeDrawdownRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MakeDrawdownRequestMultiError) AllErrors() []error { return m }

// MakeDrawdownRequestValidationError is the validation error returned by
// MakeDrawdownRequest.Validate if the designated constraints aren't met.
type MakeDrawdownRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MakeDrawdownRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MakeDrawdownRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MakeDrawdownRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MakeDrawdownRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MakeDrawdownRequestValidationError) ErrorName() string {
	return "MakeDrawdownRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MakeDrawdownRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMakeDrawdownRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MakeDrawdownRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MakeDrawdownRequestValidationError{}

// Validate checks the field values on MakeDrawdownResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MakeDrawdownResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MakeDrawdownResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MakeDrawdownResponseMultiError, or nil if none found.
func (m *MakeDrawdownResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MakeDrawdownResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MakeDrawdownResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MakeDrawdownResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MakeDrawdownResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return MakeDrawdownResponseMultiError(errors)
	}

	return nil
}

// MakeDrawdownResponseMultiError is an error wrapping multiple validation
// errors returned by MakeDrawdownResponse.ValidateAll() if the designated
// constraints aren't met.
type MakeDrawdownResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MakeDrawdownResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MakeDrawdownResponseMultiError) AllErrors() []error { return m }

// MakeDrawdownResponseValidationError is the validation error returned by
// MakeDrawdownResponse.Validate if the designated constraints aren't met.
type MakeDrawdownResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MakeDrawdownResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MakeDrawdownResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MakeDrawdownResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MakeDrawdownResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MakeDrawdownResponseValidationError) ErrorName() string {
	return "MakeDrawdownResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MakeDrawdownResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMakeDrawdownResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MakeDrawdownResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MakeDrawdownResponseValidationError{}

// Validate checks the field values on GetPdfAgreementRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPdfAgreementRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPdfAgreementRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPdfAgreementRequestMultiError, or nil if none found.
func (m *GetPdfAgreementRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPdfAgreementRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicantId

	// no validation rules for ApplicationId

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GetPdfAgreementRequestMultiError(errors)
	}

	return nil
}

// GetPdfAgreementRequestMultiError is an error wrapping multiple validation
// errors returned by GetPdfAgreementRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPdfAgreementRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPdfAgreementRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPdfAgreementRequestMultiError) AllErrors() []error { return m }

// GetPdfAgreementRequestValidationError is the validation error returned by
// GetPdfAgreementRequest.Validate if the designated constraints aren't met.
type GetPdfAgreementRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPdfAgreementRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPdfAgreementRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPdfAgreementRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPdfAgreementRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPdfAgreementRequestValidationError) ErrorName() string {
	return "GetPdfAgreementRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPdfAgreementRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPdfAgreementRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPdfAgreementRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPdfAgreementRequestValidationError{}

// Validate checks the field values on GetPdfAgreementResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPdfAgreementResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPdfAgreementResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPdfAgreementResponseMultiError, or nil if none found.
func (m *GetPdfAgreementResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPdfAgreementResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPdfAgreementResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPdfAgreementResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPdfAgreementResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GetPdfAgreementResponseMultiError(errors)
	}

	return nil
}

// GetPdfAgreementResponseMultiError is an error wrapping multiple validation
// errors returned by GetPdfAgreementResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPdfAgreementResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPdfAgreementResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPdfAgreementResponseMultiError) AllErrors() []error { return m }

// GetPdfAgreementResponseValidationError is the validation error returned by
// GetPdfAgreementResponse.Validate if the designated constraints aren't met.
type GetPdfAgreementResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPdfAgreementResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPdfAgreementResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPdfAgreementResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPdfAgreementResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPdfAgreementResponseValidationError) ErrorName() string {
	return "GetPdfAgreementResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPdfAgreementResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPdfAgreementResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPdfAgreementResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPdfAgreementResponseValidationError{}

// Validate checks the field values on SendBorrowerAgreementOtpRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendBorrowerAgreementOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendBorrowerAgreementOtpRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SendBorrowerAgreementOtpRequestMultiError, or nil if none found.
func (m *SendBorrowerAgreementOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendBorrowerAgreementOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicantId

	// no validation rules for ApplicationId

	// no validation rules for Checksum

	if len(errors) > 0 {
		return SendBorrowerAgreementOtpRequestMultiError(errors)
	}

	return nil
}

// SendBorrowerAgreementOtpRequestMultiError is an error wrapping multiple
// validation errors returned by SendBorrowerAgreementOtpRequest.ValidateAll()
// if the designated constraints aren't met.
type SendBorrowerAgreementOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendBorrowerAgreementOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendBorrowerAgreementOtpRequestMultiError) AllErrors() []error { return m }

// SendBorrowerAgreementOtpRequestValidationError is the validation error
// returned by SendBorrowerAgreementOtpRequest.Validate if the designated
// constraints aren't met.
type SendBorrowerAgreementOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendBorrowerAgreementOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendBorrowerAgreementOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendBorrowerAgreementOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendBorrowerAgreementOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendBorrowerAgreementOtpRequestValidationError) ErrorName() string {
	return "SendBorrowerAgreementOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendBorrowerAgreementOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendBorrowerAgreementOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendBorrowerAgreementOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendBorrowerAgreementOtpRequestValidationError{}

// Validate checks the field values on SendBorrowerAgreementOtpResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SendBorrowerAgreementOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendBorrowerAgreementOtpResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SendBorrowerAgreementOtpResponseMultiError, or nil if none found.
func (m *SendBorrowerAgreementOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SendBorrowerAgreementOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SendBorrowerAgreementOtpResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SendBorrowerAgreementOtpResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SendBorrowerAgreementOtpResponseValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return SendBorrowerAgreementOtpResponseMultiError(errors)
	}

	return nil
}

// SendBorrowerAgreementOtpResponseMultiError is an error wrapping multiple
// validation errors returned by
// SendBorrowerAgreementOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type SendBorrowerAgreementOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendBorrowerAgreementOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendBorrowerAgreementOtpResponseMultiError) AllErrors() []error { return m }

// SendBorrowerAgreementOtpResponseValidationError is the validation error
// returned by SendBorrowerAgreementOtpResponse.Validate if the designated
// constraints aren't met.
type SendBorrowerAgreementOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendBorrowerAgreementOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendBorrowerAgreementOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendBorrowerAgreementOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendBorrowerAgreementOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendBorrowerAgreementOtpResponseValidationError) ErrorName() string {
	return "SendBorrowerAgreementOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SendBorrowerAgreementOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendBorrowerAgreementOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendBorrowerAgreementOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendBorrowerAgreementOtpResponseValidationError{}

// Validate checks the field values on VerifyBorrowerAgreementOtpRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *VerifyBorrowerAgreementOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyBorrowerAgreementOtpRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// VerifyBorrowerAgreementOtpRequestMultiError, or nil if none found.
func (m *VerifyBorrowerAgreementOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyBorrowerAgreementOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicantId

	// no validation rules for ApplicationId

	// no validation rules for Checksum

	// no validation rules for DocId

	// no validation rules for Otp

	if len(errors) > 0 {
		return VerifyBorrowerAgreementOtpRequestMultiError(errors)
	}

	return nil
}

// VerifyBorrowerAgreementOtpRequestMultiError is an error wrapping multiple
// validation errors returned by
// VerifyBorrowerAgreementOtpRequest.ValidateAll() if the designated
// constraints aren't met.
type VerifyBorrowerAgreementOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyBorrowerAgreementOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyBorrowerAgreementOtpRequestMultiError) AllErrors() []error { return m }

// VerifyBorrowerAgreementOtpRequestValidationError is the validation error
// returned by VerifyBorrowerAgreementOtpRequest.Validate if the designated
// constraints aren't met.
type VerifyBorrowerAgreementOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyBorrowerAgreementOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyBorrowerAgreementOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyBorrowerAgreementOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyBorrowerAgreementOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyBorrowerAgreementOtpRequestValidationError) ErrorName() string {
	return "VerifyBorrowerAgreementOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyBorrowerAgreementOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyBorrowerAgreementOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyBorrowerAgreementOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyBorrowerAgreementOtpRequestValidationError{}

// Validate checks the field values on VerifyBorrowerAgreementOtpResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *VerifyBorrowerAgreementOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyBorrowerAgreementOtpResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// VerifyBorrowerAgreementOtpResponseMultiError, or nil if none found.
func (m *VerifyBorrowerAgreementOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyBorrowerAgreementOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyBorrowerAgreementOtpResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyBorrowerAgreementOtpResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyBorrowerAgreementOtpResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return VerifyBorrowerAgreementOtpResponseMultiError(errors)
	}

	return nil
}

// VerifyBorrowerAgreementOtpResponseMultiError is an error wrapping multiple
// validation errors returned by
// VerifyBorrowerAgreementOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type VerifyBorrowerAgreementOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyBorrowerAgreementOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyBorrowerAgreementOtpResponseMultiError) AllErrors() []error { return m }

// VerifyBorrowerAgreementOtpResponseValidationError is the validation error
// returned by VerifyBorrowerAgreementOtpResponse.Validate if the designated
// constraints aren't met.
type VerifyBorrowerAgreementOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyBorrowerAgreementOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyBorrowerAgreementOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyBorrowerAgreementOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyBorrowerAgreementOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyBorrowerAgreementOtpResponseValidationError) ErrorName() string {
	return "VerifyBorrowerAgreementOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyBorrowerAgreementOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyBorrowerAgreementOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyBorrowerAgreementOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyBorrowerAgreementOtpResponseValidationError{}

// Validate checks the field values on GetLoanStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanStatusRequestMultiError, or nil if none found.
func (m *GetLoanStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicationId

	// no validation rules for Timestamp

	// no validation rules for Checksum

	// no validation rules for Urn

	if len(errors) > 0 {
		return GetLoanStatusRequestMultiError(errors)
	}

	return nil
}

// GetLoanStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetLoanStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLoanStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanStatusRequestMultiError) AllErrors() []error { return m }

// GetLoanStatusRequestValidationError is the validation error returned by
// GetLoanStatusRequest.Validate if the designated constraints aren't met.
type GetLoanStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanStatusRequestValidationError) ErrorName() string {
	return "GetLoanStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanStatusRequestValidationError{}

// Validate checks the field values on GetLoanStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanStatusResponseMultiError, or nil if none found.
func (m *GetLoanStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanStatusResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanStatusResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanStatusResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GetLoanStatusResponseMultiError(errors)
	}

	return nil
}

// GetLoanStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetLoanStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLoanStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanStatusResponseMultiError) AllErrors() []error { return m }

// GetLoanStatusResponseValidationError is the validation error returned by
// GetLoanStatusResponse.Validate if the designated constraints aren't met.
type GetLoanStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanStatusResponseValidationError) ErrorName() string {
	return "GetLoanStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanStatusResponseValidationError{}

// Validate checks the field values on VerifyAndDownloadCkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyAndDownloadCkycRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyAndDownloadCkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyAndDownloadCkycRequestMultiError, or nil if none found.
func (m *VerifyAndDownloadCkycRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyAndDownloadCkycRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicantId

	// no validation rules for Checksum

	// no validation rules for Timestamp

	// no validation rules for IdType

	// no validation rules for IdNo

	// no validation rules for AuthFactorType

	// no validation rules for AuthFactor

	if len(errors) > 0 {
		return VerifyAndDownloadCkycRequestMultiError(errors)
	}

	return nil
}

// VerifyAndDownloadCkycRequestMultiError is an error wrapping multiple
// validation errors returned by VerifyAndDownloadCkycRequest.ValidateAll() if
// the designated constraints aren't met.
type VerifyAndDownloadCkycRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyAndDownloadCkycRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyAndDownloadCkycRequestMultiError) AllErrors() []error { return m }

// VerifyAndDownloadCkycRequestValidationError is the validation error returned
// by VerifyAndDownloadCkycRequest.Validate if the designated constraints
// aren't met.
type VerifyAndDownloadCkycRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyAndDownloadCkycRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyAndDownloadCkycRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyAndDownloadCkycRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyAndDownloadCkycRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyAndDownloadCkycRequestValidationError) ErrorName() string {
	return "VerifyAndDownloadCkycRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyAndDownloadCkycRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyAndDownloadCkycRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyAndDownloadCkycRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyAndDownloadCkycRequestValidationError{}

// Validate checks the field values on VerifyAndDownloadCkycResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyAndDownloadCkycResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyAndDownloadCkycResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// VerifyAndDownloadCkycResponseMultiError, or nil if none found.
func (m *VerifyAndDownloadCkycResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyAndDownloadCkycResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return VerifyAndDownloadCkycResponseMultiError(errors)
	}

	return nil
}

// VerifyAndDownloadCkycResponseMultiError is an error wrapping multiple
// validation errors returned by VerifyAndDownloadCkycResponse.ValidateAll()
// if the designated constraints aren't met.
type VerifyAndDownloadCkycResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyAndDownloadCkycResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyAndDownloadCkycResponseMultiError) AllErrors() []error { return m }

// VerifyAndDownloadCkycResponseValidationError is the validation error
// returned by VerifyAndDownloadCkycResponse.Validate if the designated
// constraints aren't met.
type VerifyAndDownloadCkycResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyAndDownloadCkycResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyAndDownloadCkycResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyAndDownloadCkycResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyAndDownloadCkycResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyAndDownloadCkycResponseValidationError) ErrorName() string {
	return "VerifyAndDownloadCkycResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyAndDownloadCkycResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyAndDownloadCkycResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyAndDownloadCkycResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyAndDownloadCkycResponseValidationError{}

// Validate checks the field values on GetRepaymentScheduleRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRepaymentScheduleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRepaymentScheduleRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRepaymentScheduleRequestMultiError, or nil if none found.
func (m *GetRepaymentScheduleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRepaymentScheduleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for LoanId

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GetRepaymentScheduleRequestMultiError(errors)
	}

	return nil
}

// GetRepaymentScheduleRequestMultiError is an error wrapping multiple
// validation errors returned by GetRepaymentScheduleRequest.ValidateAll() if
// the designated constraints aren't met.
type GetRepaymentScheduleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRepaymentScheduleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRepaymentScheduleRequestMultiError) AllErrors() []error { return m }

// GetRepaymentScheduleRequestValidationError is the validation error returned
// by GetRepaymentScheduleRequest.Validate if the designated constraints
// aren't met.
type GetRepaymentScheduleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRepaymentScheduleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRepaymentScheduleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRepaymentScheduleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRepaymentScheduleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRepaymentScheduleRequestValidationError) ErrorName() string {
	return "GetRepaymentScheduleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRepaymentScheduleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRepaymentScheduleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRepaymentScheduleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRepaymentScheduleRequestValidationError{}

// Validate checks the field values on GetRepaymentScheduleRequestV4 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRepaymentScheduleRequestV4) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRepaymentScheduleRequestV4 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRepaymentScheduleRequestV4MultiError, or nil if none found.
func (m *GetRepaymentScheduleRequestV4) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRepaymentScheduleRequestV4) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for LoanId

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GetRepaymentScheduleRequestV4MultiError(errors)
	}

	return nil
}

// GetRepaymentScheduleRequestV4MultiError is an error wrapping multiple
// validation errors returned by GetRepaymentScheduleRequestV4.ValidateAll()
// if the designated constraints aren't met.
type GetRepaymentScheduleRequestV4MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRepaymentScheduleRequestV4MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRepaymentScheduleRequestV4MultiError) AllErrors() []error { return m }

// GetRepaymentScheduleRequestV4ValidationError is the validation error
// returned by GetRepaymentScheduleRequestV4.Validate if the designated
// constraints aren't met.
type GetRepaymentScheduleRequestV4ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRepaymentScheduleRequestV4ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRepaymentScheduleRequestV4ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRepaymentScheduleRequestV4ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRepaymentScheduleRequestV4ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRepaymentScheduleRequestV4ValidationError) ErrorName() string {
	return "GetRepaymentScheduleRequestV4ValidationError"
}

// Error satisfies the builtin error interface
func (e GetRepaymentScheduleRequestV4ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRepaymentScheduleRequestV4.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRepaymentScheduleRequestV4ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRepaymentScheduleRequestV4ValidationError{}

// Validate checks the field values on GetRepaymentScheduleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRepaymentScheduleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRepaymentScheduleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRepaymentScheduleResponseMultiError, or nil if none found.
func (m *GetRepaymentScheduleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRepaymentScheduleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRepaymentScheduleResponseMultiError(errors)
	}

	return nil
}

// GetRepaymentScheduleResponseMultiError is an error wrapping multiple
// validation errors returned by GetRepaymentScheduleResponse.ValidateAll() if
// the designated constraints aren't met.
type GetRepaymentScheduleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRepaymentScheduleResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRepaymentScheduleResponseMultiError) AllErrors() []error { return m }

// GetRepaymentScheduleResponseValidationError is the validation error returned
// by GetRepaymentScheduleResponse.Validate if the designated constraints
// aren't met.
type GetRepaymentScheduleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRepaymentScheduleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRepaymentScheduleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRepaymentScheduleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRepaymentScheduleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRepaymentScheduleResponseValidationError) ErrorName() string {
	return "GetRepaymentScheduleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRepaymentScheduleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRepaymentScheduleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRepaymentScheduleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRepaymentScheduleResponseValidationError{}

// Validate checks the field values on Schedule with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Schedule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Schedule with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ScheduleMultiError, or nil
// if none found.
func (m *Schedule) ValidateAll() error {
	return m.validate(true)
}

func (m *Schedule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for InstallmentNumber

	// no validation rules for DueDate

	// no validation rules for DueAmount

	// no validation rules for PrincipalAmount

	// no validation rules for InterestAmount

	// no validation rules for PaymentStatus

	// no validation rules for ReceivedDate

	// no validation rules for ReceivedAmount

	// no validation rules for PaidPrincipalAmount

	// no validation rules for PaidInterestAmount

	// no validation rules for Lpi

	// no validation rules for OtherCharges

	// no validation rules for BounceCharges

	// no validation rules for PostPaymentPrincipalOutstanding

	// no validation rules for PostPaymentInterestOutstanding

	// no validation rules for WaivedCharges

	// no validation rules for PaidLpi

	// no validation rules for PaidOtherCharges

	// no validation rules for PaidBounceCharges

	// no validation rules for PostPaymentChargesOutstanding

	if len(errors) > 0 {
		return ScheduleMultiError(errors)
	}

	return nil
}

// ScheduleMultiError is an error wrapping multiple validation errors returned
// by Schedule.ValidateAll() if the designated constraints aren't met.
type ScheduleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScheduleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScheduleMultiError) AllErrors() []error { return m }

// ScheduleValidationError is the validation error returned by
// Schedule.Validate if the designated constraints aren't met.
type ScheduleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScheduleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScheduleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScheduleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScheduleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScheduleValidationError) ErrorName() string { return "ScheduleValidationError" }

// Error satisfies the builtin error interface
func (e ScheduleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSchedule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScheduleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScheduleValidationError{}

// Validate checks the field values on GetRepaymentScheduleErrorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetRepaymentScheduleErrorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRepaymentScheduleErrorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRepaymentScheduleErrorResponseMultiError, or nil if none found.
func (m *GetRepaymentScheduleErrorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRepaymentScheduleErrorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRepaymentScheduleErrorResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRepaymentScheduleErrorResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRepaymentScheduleErrorResponseValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRepaymentScheduleErrorResponseMultiError(errors)
	}

	return nil
}

// GetRepaymentScheduleErrorResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetRepaymentScheduleErrorResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRepaymentScheduleErrorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRepaymentScheduleErrorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRepaymentScheduleErrorResponseMultiError) AllErrors() []error { return m }

// GetRepaymentScheduleErrorResponseValidationError is the validation error
// returned by GetRepaymentScheduleErrorResponse.Validate if the designated
// constraints aren't met.
type GetRepaymentScheduleErrorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRepaymentScheduleErrorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRepaymentScheduleErrorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRepaymentScheduleErrorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRepaymentScheduleErrorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRepaymentScheduleErrorResponseValidationError) ErrorName() string {
	return "GetRepaymentScheduleErrorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRepaymentScheduleErrorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRepaymentScheduleErrorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRepaymentScheduleErrorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRepaymentScheduleErrorResponseValidationError{}

// Validate checks the field values on ErrorResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ErrorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ErrorResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ErrorResponseMultiError, or
// nil if none found.
func (m *ErrorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ErrorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ErrorResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ErrorResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ErrorResponseValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return ErrorResponseMultiError(errors)
	}

	return nil
}

// ErrorResponseMultiError is an error wrapping multiple validation errors
// returned by ErrorResponse.ValidateAll() if the designated constraints
// aren't met.
type ErrorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ErrorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ErrorResponseMultiError) AllErrors() []error { return m }

// ErrorResponseValidationError is the validation error returned by
// ErrorResponse.Validate if the designated constraints aren't met.
type ErrorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ErrorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ErrorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ErrorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ErrorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ErrorResponseValidationError) ErrorName() string { return "ErrorResponseValidationError" }

// Error satisfies the builtin error interface
func (e ErrorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sErrorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ErrorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ErrorResponseValidationError{}

// Validate checks the field values on UploadDocumentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadDocumentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadDocumentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadDocumentRequestMultiError, or nil if none found.
func (m *UploadDocumentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadDocumentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicationId

	// no validation rules for DocumentType

	// no validation rules for Remarks

	// no validation rules for Checksum

	// no validation rules for File

	if len(errors) > 0 {
		return UploadDocumentRequestMultiError(errors)
	}

	return nil
}

// UploadDocumentRequestMultiError is an error wrapping multiple validation
// errors returned by UploadDocumentRequest.ValidateAll() if the designated
// constraints aren't met.
type UploadDocumentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadDocumentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadDocumentRequestMultiError) AllErrors() []error { return m }

// UploadDocumentRequestValidationError is the validation error returned by
// UploadDocumentRequest.Validate if the designated constraints aren't met.
type UploadDocumentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadDocumentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadDocumentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadDocumentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadDocumentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadDocumentRequestValidationError) ErrorName() string {
	return "UploadDocumentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadDocumentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadDocumentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadDocumentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadDocumentRequestValidationError{}

// Validate checks the field values on UploadDocumentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadDocumentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadDocumentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadDocumentResponseMultiError, or nil if none found.
func (m *UploadDocumentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadDocumentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadDocumentResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadDocumentResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadDocumentResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return UploadDocumentResponseMultiError(errors)
	}

	return nil
}

// UploadDocumentResponseMultiError is an error wrapping multiple validation
// errors returned by UploadDocumentResponse.ValidateAll() if the designated
// constraints aren't met.
type UploadDocumentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadDocumentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadDocumentResponseMultiError) AllErrors() []error { return m }

// UploadDocumentResponseValidationError is the validation error returned by
// UploadDocumentResponse.Validate if the designated constraints aren't met.
type UploadDocumentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadDocumentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadDocumentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadDocumentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadDocumentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadDocumentResponseValidationError) ErrorName() string {
	return "UploadDocumentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadDocumentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadDocumentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadDocumentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadDocumentResponseValidationError{}

// Validate checks the field values on SaveCollectionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveCollectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveCollectionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveCollectionRequestMultiError, or nil if none found.
func (m *SaveCollectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveCollectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for LoanId

	// no validation rules for Checksum

	for idx, item := range m.GetPaymentSchedule() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SaveCollectionRequestValidationError{
						field:  fmt.Sprintf("PaymentSchedule[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SaveCollectionRequestValidationError{
						field:  fmt.Sprintf("PaymentSchedule[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SaveCollectionRequestValidationError{
					field:  fmt.Sprintf("PaymentSchedule[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SaveCollectionRequestMultiError(errors)
	}

	return nil
}

// SaveCollectionRequestMultiError is an error wrapping multiple validation
// errors returned by SaveCollectionRequest.ValidateAll() if the designated
// constraints aren't met.
type SaveCollectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveCollectionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveCollectionRequestMultiError) AllErrors() []error { return m }

// SaveCollectionRequestValidationError is the validation error returned by
// SaveCollectionRequest.Validate if the designated constraints aren't met.
type SaveCollectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveCollectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveCollectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveCollectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveCollectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveCollectionRequestValidationError) ErrorName() string {
	return "SaveCollectionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SaveCollectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveCollectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveCollectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveCollectionRequestValidationError{}

// Validate checks the field values on SaveCollectionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveCollectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveCollectionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveCollectionResponseMultiError, or nil if none found.
func (m *SaveCollectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveCollectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveCollectionResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveCollectionResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveCollectionResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return SaveCollectionResponseMultiError(errors)
	}

	return nil
}

// SaveCollectionResponseMultiError is an error wrapping multiple validation
// errors returned by SaveCollectionResponse.ValidateAll() if the designated
// constraints aren't met.
type SaveCollectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveCollectionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveCollectionResponseMultiError) AllErrors() []error { return m }

// SaveCollectionResponseValidationError is the validation error returned by
// SaveCollectionResponse.Validate if the designated constraints aren't met.
type SaveCollectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveCollectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveCollectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveCollectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveCollectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveCollectionResponseValidationError) ErrorName() string {
	return "SaveCollectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SaveCollectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveCollectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveCollectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveCollectionResponseValidationError{}

// Validate checks the field values on HashGenerationForOkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HashGenerationForOkycRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HashGenerationForOkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HashGenerationForOkycRequestMultiError, or nil if none found.
func (m *HashGenerationForOkycRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HashGenerationForOkycRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicationId

	// no validation rules for Checksum

	if len(errors) > 0 {
		return HashGenerationForOkycRequestMultiError(errors)
	}

	return nil
}

// HashGenerationForOkycRequestMultiError is an error wrapping multiple
// validation errors returned by HashGenerationForOkycRequest.ValidateAll() if
// the designated constraints aren't met.
type HashGenerationForOkycRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HashGenerationForOkycRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HashGenerationForOkycRequestMultiError) AllErrors() []error { return m }

// HashGenerationForOkycRequestValidationError is the validation error returned
// by HashGenerationForOkycRequest.Validate if the designated constraints
// aren't met.
type HashGenerationForOkycRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HashGenerationForOkycRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HashGenerationForOkycRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HashGenerationForOkycRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HashGenerationForOkycRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HashGenerationForOkycRequestValidationError) ErrorName() string {
	return "HashGenerationForOkycRequestValidationError"
}

// Error satisfies the builtin error interface
func (e HashGenerationForOkycRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHashGenerationForOkycRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HashGenerationForOkycRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HashGenerationForOkycRequestValidationError{}

// Validate checks the field values on HashGenerationForOkycResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HashGenerationForOkycResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HashGenerationForOkycResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// HashGenerationForOkycResponseMultiError, or nil if none found.
func (m *HashGenerationForOkycResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *HashGenerationForOkycResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HashGenerationForOkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HashGenerationForOkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HashGenerationForOkycResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return HashGenerationForOkycResponseMultiError(errors)
	}

	return nil
}

// HashGenerationForOkycResponseMultiError is an error wrapping multiple
// validation errors returned by HashGenerationForOkycResponse.ValidateAll()
// if the designated constraints aren't met.
type HashGenerationForOkycResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HashGenerationForOkycResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HashGenerationForOkycResponseMultiError) AllErrors() []error { return m }

// HashGenerationForOkycResponseValidationError is the validation error
// returned by HashGenerationForOkycResponse.Validate if the designated
// constraints aren't met.
type HashGenerationForOkycResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HashGenerationForOkycResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HashGenerationForOkycResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HashGenerationForOkycResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HashGenerationForOkycResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HashGenerationForOkycResponseValidationError) ErrorName() string {
	return "HashGenerationForOkycResponseValidationError"
}

// Error satisfies the builtin error interface
func (e HashGenerationForOkycResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHashGenerationForOkycResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HashGenerationForOkycResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HashGenerationForOkycResponseValidationError{}

// Validate checks the field values on CaptchaGenerationForOkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CaptchaGenerationForOkycRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaptchaGenerationForOkycRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CaptchaGenerationForOkycRequestMultiError, or nil if none found.
func (m *CaptchaGenerationForOkycRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CaptchaGenerationForOkycRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicationId

	// no validation rules for Hash

	// no validation rules for Checksum

	if len(errors) > 0 {
		return CaptchaGenerationForOkycRequestMultiError(errors)
	}

	return nil
}

// CaptchaGenerationForOkycRequestMultiError is an error wrapping multiple
// validation errors returned by CaptchaGenerationForOkycRequest.ValidateAll()
// if the designated constraints aren't met.
type CaptchaGenerationForOkycRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaptchaGenerationForOkycRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaptchaGenerationForOkycRequestMultiError) AllErrors() []error { return m }

// CaptchaGenerationForOkycRequestValidationError is the validation error
// returned by CaptchaGenerationForOkycRequest.Validate if the designated
// constraints aren't met.
type CaptchaGenerationForOkycRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaptchaGenerationForOkycRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaptchaGenerationForOkycRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaptchaGenerationForOkycRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaptchaGenerationForOkycRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaptchaGenerationForOkycRequestValidationError) ErrorName() string {
	return "CaptchaGenerationForOkycRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CaptchaGenerationForOkycRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaptchaGenerationForOkycRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaptchaGenerationForOkycRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaptchaGenerationForOkycRequestValidationError{}

// Validate checks the field values on CaptchaGenerationForOkycResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CaptchaGenerationForOkycResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaptchaGenerationForOkycResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CaptchaGenerationForOkycResponseMultiError, or nil if none found.
func (m *CaptchaGenerationForOkycResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CaptchaGenerationForOkycResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaptchaGenerationForOkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaptchaGenerationForOkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaptchaGenerationForOkycResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return CaptchaGenerationForOkycResponseMultiError(errors)
	}

	return nil
}

// CaptchaGenerationForOkycResponseMultiError is an error wrapping multiple
// validation errors returned by
// CaptchaGenerationForOkycResponse.ValidateAll() if the designated
// constraints aren't met.
type CaptchaGenerationForOkycResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaptchaGenerationForOkycResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaptchaGenerationForOkycResponseMultiError) AllErrors() []error { return m }

// CaptchaGenerationForOkycResponseValidationError is the validation error
// returned by CaptchaGenerationForOkycResponse.Validate if the designated
// constraints aren't met.
type CaptchaGenerationForOkycResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaptchaGenerationForOkycResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaptchaGenerationForOkycResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaptchaGenerationForOkycResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaptchaGenerationForOkycResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaptchaGenerationForOkycResponseValidationError) ErrorName() string {
	return "CaptchaGenerationForOkycResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CaptchaGenerationForOkycResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaptchaGenerationForOkycResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaptchaGenerationForOkycResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaptchaGenerationForOkycResponseValidationError{}

// Validate checks the field values on GenerateOtpForOkycRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpForOkycRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpForOkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOtpForOkycRequestMultiError, or nil if none found.
func (m *GenerateOtpForOkycRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpForOkycRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicationId

	// no validation rules for Hash

	// no validation rules for UidNo

	// no validation rules for CaptchaCode

	// no validation rules for RequestToken

	// no validation rules for CaptchaTxnId

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GenerateOtpForOkycRequestMultiError(errors)
	}

	return nil
}

// GenerateOtpForOkycRequestMultiError is an error wrapping multiple validation
// errors returned by GenerateOtpForOkycRequest.ValidateAll() if the
// designated constraints aren't met.
type GenerateOtpForOkycRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpForOkycRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpForOkycRequestMultiError) AllErrors() []error { return m }

// GenerateOtpForOkycRequestValidationError is the validation error returned by
// GenerateOtpForOkycRequest.Validate if the designated constraints aren't met.
type GenerateOtpForOkycRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpForOkycRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpForOkycRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpForOkycRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpForOkycRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpForOkycRequestValidationError) ErrorName() string {
	return "GenerateOtpForOkycRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpForOkycRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpForOkycRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpForOkycRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpForOkycRequestValidationError{}

// Validate checks the field values on GenerateOtpForOkycResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpForOkycResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpForOkycResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOtpForOkycResponseMultiError, or nil if none found.
func (m *GenerateOtpForOkycResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpForOkycResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpForOkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpForOkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpForOkycResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return GenerateOtpForOkycResponseMultiError(errors)
	}

	return nil
}

// GenerateOtpForOkycResponseMultiError is an error wrapping multiple
// validation errors returned by GenerateOtpForOkycResponse.ValidateAll() if
// the designated constraints aren't met.
type GenerateOtpForOkycResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpForOkycResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpForOkycResponseMultiError) AllErrors() []error { return m }

// GenerateOtpForOkycResponseValidationError is the validation error returned
// by GenerateOtpForOkycResponse.Validate if the designated constraints aren't met.
type GenerateOtpForOkycResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpForOkycResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpForOkycResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpForOkycResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpForOkycResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpForOkycResponseValidationError) ErrorName() string {
	return "GenerateOtpForOkycResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpForOkycResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpForOkycResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpForOkycResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpForOkycResponseValidationError{}

// Validate checks the field values on ValidateOtpForOkycRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateOtpForOkycRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateOtpForOkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateOtpForOkycRequestMultiError, or nil if none found.
func (m *ValidateOtpForOkycRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateOtpForOkycRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicationId

	// no validation rules for Otp

	// no validation rules for Hash

	// no validation rules for RequestToken

	// no validation rules for Checksum

	if len(errors) > 0 {
		return ValidateOtpForOkycRequestMultiError(errors)
	}

	return nil
}

// ValidateOtpForOkycRequestMultiError is an error wrapping multiple validation
// errors returned by ValidateOtpForOkycRequest.ValidateAll() if the
// designated constraints aren't met.
type ValidateOtpForOkycRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateOtpForOkycRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateOtpForOkycRequestMultiError) AllErrors() []error { return m }

// ValidateOtpForOkycRequestValidationError is the validation error returned by
// ValidateOtpForOkycRequest.Validate if the designated constraints aren't met.
type ValidateOtpForOkycRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateOtpForOkycRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateOtpForOkycRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateOtpForOkycRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateOtpForOkycRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateOtpForOkycRequestValidationError) ErrorName() string {
	return "ValidateOtpForOkycRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateOtpForOkycRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateOtpForOkycRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateOtpForOkycRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateOtpForOkycRequestValidationError{}

// Validate checks the field values on ValidateOtpForOkycResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateOtpForOkycResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateOtpForOkycResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateOtpForOkycResponseMultiError, or nil if none found.
func (m *ValidateOtpForOkycResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateOtpForOkycResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ValidateOtpForOkycResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ValidateOtpForOkycResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ValidateOtpForOkycResponseValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return ValidateOtpForOkycResponseMultiError(errors)
	}

	return nil
}

// ValidateOtpForOkycResponseMultiError is an error wrapping multiple
// validation errors returned by ValidateOtpForOkycResponse.ValidateAll() if
// the designated constraints aren't met.
type ValidateOtpForOkycResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateOtpForOkycResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateOtpForOkycResponseMultiError) AllErrors() []error { return m }

// ValidateOtpForOkycResponseValidationError is the validation error returned
// by ValidateOtpForOkycResponse.Validate if the designated constraints aren't met.
type ValidateOtpForOkycResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateOtpForOkycResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateOtpForOkycResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateOtpForOkycResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateOtpForOkycResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateOtpForOkycResponseValidationError) ErrorName() string {
	return "ValidateOtpForOkycResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateOtpForOkycResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateOtpForOkycResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateOtpForOkycResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateOtpForOkycResponseValidationError{}

// Validate checks the field values on UpdateLeadRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateLeadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLeadRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLeadRequestMultiError, or nil if none found.
func (m *UpdateLeadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLeadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for Checksum

	// no validation rules for Urn

	// no validation rules for ApplicationId

	// no validation rules for Amount

	if all {
		switch v := interface{}(m.GetSchemeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLeadRequestValidationError{
					field:  "SchemeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLeadRequestValidationError{
					field:  "SchemeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSchemeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLeadRequestValidationError{
				field:  "SchemeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateLeadRequestMultiError(errors)
	}

	return nil
}

// UpdateLeadRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateLeadRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateLeadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLeadRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLeadRequestMultiError) AllErrors() []error { return m }

// UpdateLeadRequestValidationError is the validation error returned by
// UpdateLeadRequest.Validate if the designated constraints aren't met.
type UpdateLeadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLeadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLeadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLeadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLeadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLeadRequestValidationError) ErrorName() string {
	return "UpdateLeadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLeadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLeadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLeadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLeadRequestValidationError{}

// Validate checks the field values on UpdateLeadResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLeadResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLeadResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLeadResponseMultiError, or nil if none found.
func (m *UpdateLeadResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLeadResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLeadResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLeadResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLeadResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return UpdateLeadResponseMultiError(errors)
	}

	return nil
}

// UpdateLeadResponseMultiError is an error wrapping multiple validation errors
// returned by UpdateLeadResponse.ValidateAll() if the designated constraints
// aren't met.
type UpdateLeadResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLeadResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLeadResponseMultiError) AllErrors() []error { return m }

// UpdateLeadResponseValidationError is the validation error returned by
// UpdateLeadResponse.Validate if the designated constraints aren't met.
type UpdateLeadResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLeadResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLeadResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLeadResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLeadResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLeadResponseValidationError) ErrorName() string {
	return "UpdateLeadResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLeadResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLeadResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLeadResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLeadResponseValidationError{}

// Validate checks the field values on CancelLeadRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CancelLeadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelLeadRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelLeadRequestMultiError, or nil if none found.
func (m *CancelLeadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelLeadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicationId

	// no validation rules for ApplicantId

	// no validation rules for Checksum

	// no validation rules for Timestamp

	if len(errors) > 0 {
		return CancelLeadRequestMultiError(errors)
	}

	return nil
}

// CancelLeadRequestMultiError is an error wrapping multiple validation errors
// returned by CancelLeadRequest.ValidateAll() if the designated constraints
// aren't met.
type CancelLeadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelLeadRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelLeadRequestMultiError) AllErrors() []error { return m }

// CancelLeadRequestValidationError is the validation error returned by
// CancelLeadRequest.Validate if the designated constraints aren't met.
type CancelLeadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelLeadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelLeadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelLeadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelLeadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelLeadRequestValidationError) ErrorName() string {
	return "CancelLeadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CancelLeadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelLeadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelLeadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelLeadRequestValidationError{}

// Validate checks the field values on CancelLeadResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelLeadResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelLeadResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelLeadResponseMultiError, or nil if none found.
func (m *CancelLeadResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelLeadResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CancelLeadResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CancelLeadResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CancelLeadResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return CancelLeadResponseMultiError(errors)
	}

	return nil
}

// CancelLeadResponseMultiError is an error wrapping multiple validation errors
// returned by CancelLeadResponse.ValidateAll() if the designated constraints
// aren't met.
type CancelLeadResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelLeadResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelLeadResponseMultiError) AllErrors() []error { return m }

// CancelLeadResponseValidationError is the validation error returned by
// CancelLeadResponse.Validate if the designated constraints aren't met.
type CancelLeadResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelLeadResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelLeadResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelLeadResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelLeadResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelLeadResponseValidationError) ErrorName() string {
	return "CancelLeadResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CancelLeadResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelLeadResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelLeadResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelLeadResponseValidationError{}

// Validate checks the field values on CancelLeadErrorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelLeadErrorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelLeadErrorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelLeadErrorResponseMultiError, or nil if none found.
func (m *CancelLeadErrorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelLeadErrorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return CancelLeadErrorResponseMultiError(errors)
	}

	return nil
}

// CancelLeadErrorResponseMultiError is an error wrapping multiple validation
// errors returned by CancelLeadErrorResponse.ValidateAll() if the designated
// constraints aren't met.
type CancelLeadErrorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelLeadErrorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelLeadErrorResponseMultiError) AllErrors() []error { return m }

// CancelLeadErrorResponseValidationError is the validation error returned by
// CancelLeadErrorResponse.Validate if the designated constraints aren't met.
type CancelLeadErrorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelLeadErrorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelLeadErrorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelLeadErrorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelLeadErrorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelLeadErrorResponseValidationError) ErrorName() string {
	return "CancelLeadErrorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CancelLeadErrorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelLeadErrorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelLeadErrorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelLeadErrorResponseValidationError{}

// Validate checks the field values on ForeClosureDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForeClosureDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForeClosureDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForeClosureDetailsRequestMultiError, or nil if none found.
func (m *ForeClosureDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ForeClosureDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicationId

	// no validation rules for Checksum

	// no validation rules for Timestamp

	if len(errors) > 0 {
		return ForeClosureDetailsRequestMultiError(errors)
	}

	return nil
}

// ForeClosureDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by ForeClosureDetailsRequest.ValidateAll() if the
// designated constraints aren't met.
type ForeClosureDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForeClosureDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForeClosureDetailsRequestMultiError) AllErrors() []error { return m }

// ForeClosureDetailsRequestValidationError is the validation error returned by
// ForeClosureDetailsRequest.Validate if the designated constraints aren't met.
type ForeClosureDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForeClosureDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForeClosureDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForeClosureDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForeClosureDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForeClosureDetailsRequestValidationError) ErrorName() string {
	return "ForeClosureDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ForeClosureDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForeClosureDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForeClosureDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForeClosureDetailsRequestValidationError{}

// Validate checks the field values on ForeClosureDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForeClosureDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForeClosureDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForeClosureDetailsResponseMultiError, or nil if none found.
func (m *ForeClosureDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ForeClosureDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeClosureDetailsResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeClosureDetailsResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeClosureDetailsResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return ForeClosureDetailsResponseMultiError(errors)
	}

	return nil
}

// ForeClosureDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by ForeClosureDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type ForeClosureDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForeClosureDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForeClosureDetailsResponseMultiError) AllErrors() []error { return m }

// ForeClosureDetailsResponseValidationError is the validation error returned
// by ForeClosureDetailsResponse.Validate if the designated constraints aren't met.
type ForeClosureDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForeClosureDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForeClosureDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForeClosureDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForeClosureDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForeClosureDetailsResponseValidationError) ErrorName() string {
	return "ForeClosureDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ForeClosureDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForeClosureDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForeClosureDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForeClosureDetailsResponseValidationError{}

// Validate checks the field values on UpdateApplicantUdfRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateApplicantUdfRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateApplicantUdfRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateApplicantUdfRequestMultiError, or nil if none found.
func (m *UpdateApplicantUdfRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateApplicantUdfRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for ApplicantId

	// no validation rules for Checksum

	// no validation rules for Udf8

	// no validation rules for Udf9

	// no validation rules for Udf4

	// no validation rules for Udf5

	if len(errors) > 0 {
		return UpdateApplicantUdfRequestMultiError(errors)
	}

	return nil
}

// UpdateApplicantUdfRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateApplicantUdfRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateApplicantUdfRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateApplicantUdfRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateApplicantUdfRequestMultiError) AllErrors() []error { return m }

// UpdateApplicantUdfRequestValidationError is the validation error returned by
// UpdateApplicantUdfRequest.Validate if the designated constraints aren't met.
type UpdateApplicantUdfRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateApplicantUdfRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateApplicantUdfRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateApplicantUdfRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateApplicantUdfRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateApplicantUdfRequestValidationError) ErrorName() string {
	return "UpdateApplicantUdfRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateApplicantUdfRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateApplicantUdfRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateApplicantUdfRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateApplicantUdfRequestValidationError{}

// Validate checks the field values on UpdateApplicantUdfResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateApplicantUdfResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateApplicantUdfResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateApplicantUdfResponseMultiError, or nil if none found.
func (m *UpdateApplicantUdfResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateApplicantUdfResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	// no validation rules for Code

	// no validation rules for Checksum

	if len(errors) > 0 {
		return UpdateApplicantUdfResponseMultiError(errors)
	}

	return nil
}

// UpdateApplicantUdfResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateApplicantUdfResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateApplicantUdfResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateApplicantUdfResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateApplicantUdfResponseMultiError) AllErrors() []error { return m }

// UpdateApplicantUdfResponseValidationError is the validation error returned
// by UpdateApplicantUdfResponse.Validate if the designated constraints aren't met.
type UpdateApplicantUdfResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateApplicantUdfResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateApplicantUdfResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateApplicantUdfResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateApplicantUdfResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateApplicantUdfResponseValidationError) ErrorName() string {
	return "UpdateApplicantUdfResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateApplicantUdfResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateApplicantUdfResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateApplicantUdfResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateApplicantUdfResponseValidationError{}

// Validate checks the field values on CreateRepaymentScheduleRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRepaymentScheduleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRepaymentScheduleRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateRepaymentScheduleRequestMultiError, or nil if none found.
func (m *CreateRepaymentScheduleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRepaymentScheduleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanId

	// no validation rules for Sid

	// no validation rules for RepaymentFrequency

	// no validation rules for EmiTenure

	// no validation rules for Checksum

	// no validation rules for EmiStartDate

	for idx, item := range m.GetPaymentSchedules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateRepaymentScheduleRequestValidationError{
						field:  fmt.Sprintf("PaymentSchedules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateRepaymentScheduleRequestValidationError{
						field:  fmt.Sprintf("PaymentSchedules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateRepaymentScheduleRequestValidationError{
					field:  fmt.Sprintf("PaymentSchedules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateRepaymentScheduleRequestMultiError(errors)
	}

	return nil
}

// CreateRepaymentScheduleRequestMultiError is an error wrapping multiple
// validation errors returned by CreateRepaymentScheduleRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateRepaymentScheduleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRepaymentScheduleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRepaymentScheduleRequestMultiError) AllErrors() []error { return m }

// CreateRepaymentScheduleRequestValidationError is the validation error
// returned by CreateRepaymentScheduleRequest.Validate if the designated
// constraints aren't met.
type CreateRepaymentScheduleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRepaymentScheduleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRepaymentScheduleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRepaymentScheduleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRepaymentScheduleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRepaymentScheduleRequestValidationError) ErrorName() string {
	return "CreateRepaymentScheduleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRepaymentScheduleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRepaymentScheduleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRepaymentScheduleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRepaymentScheduleRequestValidationError{}

// Validate checks the field values on CreateRepaymentScheduleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRepaymentScheduleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRepaymentScheduleResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateRepaymentScheduleResponseMultiError, or nil if none found.
func (m *CreateRepaymentScheduleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRepaymentScheduleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	// no validation rules for Code

	// no validation rules for Checksum

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRepaymentScheduleResponseMultiError(errors)
	}

	return nil
}

// CreateRepaymentScheduleResponseMultiError is an error wrapping multiple
// validation errors returned by CreateRepaymentScheduleResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateRepaymentScheduleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRepaymentScheduleResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRepaymentScheduleResponseMultiError) AllErrors() []error { return m }

// CreateRepaymentScheduleResponseValidationError is the validation error
// returned by CreateRepaymentScheduleResponse.Validate if the designated
// constraints aren't met.
type CreateRepaymentScheduleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRepaymentScheduleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRepaymentScheduleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRepaymentScheduleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRepaymentScheduleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRepaymentScheduleResponseValidationError) ErrorName() string {
	return "CreateRepaymentScheduleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRepaymentScheduleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRepaymentScheduleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRepaymentScheduleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRepaymentScheduleResponseValidationError{}

// Validate checks the field values on GetCreditLineDetailsResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCreditLineDetailsResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditLineDetailsResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCreditLineDetailsResponse_DataMultiError, or nil if none found.
func (m *GetCreditLineDetailsResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditLineDetailsResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCreditLineDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineDetailsResponse_DataValidationError{
					field:  "CreditLineDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineDetailsResponse_DataValidationError{
					field:  "CreditLineDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditLineDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineDetailsResponse_DataValidationError{
				field:  "CreditLineDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCreditLineSchemes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCreditLineDetailsResponse_DataValidationError{
						field:  fmt.Sprintf("CreditLineSchemes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCreditLineDetailsResponse_DataValidationError{
						field:  fmt.Sprintf("CreditLineSchemes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCreditLineDetailsResponse_DataValidationError{
					field:  fmt.Sprintf("CreditLineSchemes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCreditLineDetailsResponse_DataMultiError(errors)
	}

	return nil
}

// GetCreditLineDetailsResponse_DataMultiError is an error wrapping multiple
// validation errors returned by
// GetCreditLineDetailsResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type GetCreditLineDetailsResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditLineDetailsResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditLineDetailsResponse_DataMultiError) AllErrors() []error { return m }

// GetCreditLineDetailsResponse_DataValidationError is the validation error
// returned by GetCreditLineDetailsResponse_Data.Validate if the designated
// constraints aren't met.
type GetCreditLineDetailsResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditLineDetailsResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditLineDetailsResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditLineDetailsResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditLineDetailsResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditLineDetailsResponse_DataValidationError) ErrorName() string {
	return "GetCreditLineDetailsResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditLineDetailsResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditLineDetailsResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditLineDetailsResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditLineDetailsResponse_DataValidationError{}

// Validate checks the field values on
// GetCreditLineDetailsResponse_Data_CreditLineDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCreditLineDetailsResponse_Data_CreditLineDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCreditLineDetailsResponse_Data_CreditLineDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetCreditLineDetailsResponse_Data_CreditLineDetailsMultiError, or nil if
// none found.
func (m *GetCreditLineDetailsResponse_Data_CreditLineDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditLineDetailsResponse_Data_CreditLineDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicantId

	// no validation rules for UpperLimit

	// no validation rules for AvailableLimit

	// no validation rules for BlockLimit

	if len(errors) > 0 {
		return GetCreditLineDetailsResponse_Data_CreditLineDetailsMultiError(errors)
	}

	return nil
}

// GetCreditLineDetailsResponse_Data_CreditLineDetailsMultiError is an error
// wrapping multiple validation errors returned by
// GetCreditLineDetailsResponse_Data_CreditLineDetails.ValidateAll() if the
// designated constraints aren't met.
type GetCreditLineDetailsResponse_Data_CreditLineDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditLineDetailsResponse_Data_CreditLineDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditLineDetailsResponse_Data_CreditLineDetailsMultiError) AllErrors() []error { return m }

// GetCreditLineDetailsResponse_Data_CreditLineDetailsValidationError is the
// validation error returned by
// GetCreditLineDetailsResponse_Data_CreditLineDetails.Validate if the
// designated constraints aren't met.
type GetCreditLineDetailsResponse_Data_CreditLineDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditLineDetailsResponse_Data_CreditLineDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetCreditLineDetailsResponse_Data_CreditLineDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCreditLineDetailsResponse_Data_CreditLineDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetCreditLineDetailsResponse_Data_CreditLineDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditLineDetailsResponse_Data_CreditLineDetailsValidationError) ErrorName() string {
	return "GetCreditLineDetailsResponse_Data_CreditLineDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditLineDetailsResponse_Data_CreditLineDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditLineDetailsResponse_Data_CreditLineDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditLineDetailsResponse_Data_CreditLineDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditLineDetailsResponse_Data_CreditLineDetailsValidationError{}

// Validate checks the field values on
// GetCreditLineDetailsResponse_Data_CreditLineSchemes with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCreditLineDetailsResponse_Data_CreditLineSchemes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCreditLineDetailsResponse_Data_CreditLineSchemes with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetCreditLineDetailsResponse_Data_CreditLineSchemesMultiError, or nil if
// none found.
func (m *GetCreditLineDetailsResponse_Data_CreditLineSchemes) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditLineDetailsResponse_Data_CreditLineSchemes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Roi

	// no validation rules for RoiType

	// no validation rules for MaxEmiAllowed

	// no validation rules for MinTenure

	// no validation rules for MaxTenure

	// no validation rules for MinDrawdownAmount

	// no validation rules for MaxDrawdownAmount

	// no validation rules for EmiDueDate

	// no validation rules for PfType

	// no validation rules for PfFees

	// no validation rules for FrankingType

	// no validation rules for FrankingCharges

	// no validation rules for TenureFrequency

	if len(errors) > 0 {
		return GetCreditLineDetailsResponse_Data_CreditLineSchemesMultiError(errors)
	}

	return nil
}

// GetCreditLineDetailsResponse_Data_CreditLineSchemesMultiError is an error
// wrapping multiple validation errors returned by
// GetCreditLineDetailsResponse_Data_CreditLineSchemes.ValidateAll() if the
// designated constraints aren't met.
type GetCreditLineDetailsResponse_Data_CreditLineSchemesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditLineDetailsResponse_Data_CreditLineSchemesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditLineDetailsResponse_Data_CreditLineSchemesMultiError) AllErrors() []error { return m }

// GetCreditLineDetailsResponse_Data_CreditLineSchemesValidationError is the
// validation error returned by
// GetCreditLineDetailsResponse_Data_CreditLineSchemes.Validate if the
// designated constraints aren't met.
type GetCreditLineDetailsResponse_Data_CreditLineSchemesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditLineDetailsResponse_Data_CreditLineSchemesValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetCreditLineDetailsResponse_Data_CreditLineSchemesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCreditLineDetailsResponse_Data_CreditLineSchemesValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetCreditLineDetailsResponse_Data_CreditLineSchemesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditLineDetailsResponse_Data_CreditLineSchemesValidationError) ErrorName() string {
	return "GetCreditLineDetailsResponse_Data_CreditLineSchemesValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditLineDetailsResponse_Data_CreditLineSchemesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditLineDetailsResponse_Data_CreditLineSchemes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditLineDetailsResponse_Data_CreditLineSchemesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditLineDetailsResponse_Data_CreditLineSchemesValidationError{}

// Validate checks the field values on AddPersonalDetailsResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddPersonalDetailsResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddPersonalDetailsResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AddPersonalDetailsResponse_DataMultiError, or nil if none found.
func (m *AddPersonalDetailsResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *AddPersonalDetailsResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicantId

	// no validation rules for Name

	// no validation rules for Email

	// no validation rules for ContactNumber

	if len(errors) > 0 {
		return AddPersonalDetailsResponse_DataMultiError(errors)
	}

	return nil
}

// AddPersonalDetailsResponse_DataMultiError is an error wrapping multiple
// validation errors returned by AddPersonalDetailsResponse_Data.ValidateAll()
// if the designated constraints aren't met.
type AddPersonalDetailsResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddPersonalDetailsResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddPersonalDetailsResponse_DataMultiError) AllErrors() []error { return m }

// AddPersonalDetailsResponse_DataValidationError is the validation error
// returned by AddPersonalDetailsResponse_Data.Validate if the designated
// constraints aren't met.
type AddPersonalDetailsResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddPersonalDetailsResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddPersonalDetailsResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddPersonalDetailsResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddPersonalDetailsResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddPersonalDetailsResponse_DataValidationError) ErrorName() string {
	return "AddPersonalDetailsResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e AddPersonalDetailsResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddPersonalDetailsResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddPersonalDetailsResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddPersonalDetailsResponse_DataValidationError{}

// Validate checks the field values on GetLimitResponse_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLimitResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLimitResponse_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLimitResponse_DataMultiError, or nil if none found.
func (m *GetLimitResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLimitResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicantId

	// no validation rules for Name

	// no validation rules for Email

	// no validation rules for ContactNumber

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetUpperLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLimitResponse_DataValidationError{
					field:  "UpperLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLimitResponse_DataValidationError{
					field:  "UpperLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpperLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLimitResponse_DataValidationError{
				field:  "UpperLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAvailableLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLimitResponse_DataValidationError{
					field:  "AvailableLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLimitResponse_DataValidationError{
					field:  "AvailableLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvailableLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLimitResponse_DataValidationError{
				field:  "AvailableLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LimitExpiry

	// no validation rules for Status

	if len(errors) > 0 {
		return GetLimitResponse_DataMultiError(errors)
	}

	return nil
}

// GetLimitResponse_DataMultiError is an error wrapping multiple validation
// errors returned by GetLimitResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type GetLimitResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLimitResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLimitResponse_DataMultiError) AllErrors() []error { return m }

// GetLimitResponse_DataValidationError is the validation error returned by
// GetLimitResponse_Data.Validate if the designated constraints aren't met.
type GetLimitResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLimitResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLimitResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLimitResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLimitResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLimitResponse_DataValidationError) ErrorName() string {
	return "GetLimitResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetLimitResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLimitResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLimitResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLimitResponse_DataValidationError{}

// Validate checks the field values on GetLimitResponse_Data_LimitValue with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLimitResponse_Data_LimitValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLimitResponse_Data_LimitValue with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLimitResponse_Data_LimitValueMultiError, or nil if none found.
func (m *GetLimitResponse_Data_LimitValue) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLimitResponse_Data_LimitValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Value

	// no validation rules for LastUpdated

	if len(errors) > 0 {
		return GetLimitResponse_Data_LimitValueMultiError(errors)
	}

	return nil
}

// GetLimitResponse_Data_LimitValueMultiError is an error wrapping multiple
// validation errors returned by
// GetLimitResponse_Data_LimitValue.ValidateAll() if the designated
// constraints aren't met.
type GetLimitResponse_Data_LimitValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLimitResponse_Data_LimitValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLimitResponse_Data_LimitValueMultiError) AllErrors() []error { return m }

// GetLimitResponse_Data_LimitValueValidationError is the validation error
// returned by GetLimitResponse_Data_LimitValue.Validate if the designated
// constraints aren't met.
type GetLimitResponse_Data_LimitValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLimitResponse_Data_LimitValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLimitResponse_Data_LimitValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLimitResponse_Data_LimitValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLimitResponse_Data_LimitValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLimitResponse_Data_LimitValueValidationError) ErrorName() string {
	return "GetLimitResponse_Data_LimitValueValidationError"
}

// Error satisfies the builtin error interface
func (e GetLimitResponse_Data_LimitValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLimitResponse_Data_LimitValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLimitResponse_Data_LimitValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLimitResponse_Data_LimitValueValidationError{}

// Validate checks the field values on ApplicantLookupResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApplicantLookupResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicantLookupResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplicantLookupResponse_DataMultiError, or nil if none found.
func (m *ApplicantLookupResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicantLookupResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicantId

	// no validation rules for Name

	// no validation rules for Email

	// no validation rules for ContactNumber

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicantLookupResponse_DataValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicantLookupResponse_DataValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicantLookupResponse_DataValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActivation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicantLookupResponse_DataValidationError{
					field:  "Activation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicantLookupResponse_DataValidationError{
					field:  "Activation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActivation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicantLookupResponse_DataValidationError{
				field:  "Activation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ApplicantLookupResponse_DataMultiError(errors)
	}

	return nil
}

// ApplicantLookupResponse_DataMultiError is an error wrapping multiple
// validation errors returned by ApplicantLookupResponse_Data.ValidateAll() if
// the designated constraints aren't met.
type ApplicantLookupResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantLookupResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantLookupResponse_DataMultiError) AllErrors() []error { return m }

// ApplicantLookupResponse_DataValidationError is the validation error returned
// by ApplicantLookupResponse_Data.Validate if the designated constraints
// aren't met.
type ApplicantLookupResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantLookupResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantLookupResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicantLookupResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantLookupResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantLookupResponse_DataValidationError) ErrorName() string {
	return "ApplicantLookupResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicantLookupResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicantLookupResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantLookupResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantLookupResponse_DataValidationError{}

// Validate checks the field values on ApplicantLookupResponse_Data_Details
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ApplicantLookupResponse_Data_Details) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicantLookupResponse_Data_Details
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ApplicantLookupResponse_Data_DetailsMultiError, or nil if none found.
func (m *ApplicantLookupResponse_Data_Details) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicantLookupResponse_Data_Details) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Banking

	// no validation rules for Address

	// no validation rules for Employment

	if len(errors) > 0 {
		return ApplicantLookupResponse_Data_DetailsMultiError(errors)
	}

	return nil
}

// ApplicantLookupResponse_Data_DetailsMultiError is an error wrapping multiple
// validation errors returned by
// ApplicantLookupResponse_Data_Details.ValidateAll() if the designated
// constraints aren't met.
type ApplicantLookupResponse_Data_DetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantLookupResponse_Data_DetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantLookupResponse_Data_DetailsMultiError) AllErrors() []error { return m }

// ApplicantLookupResponse_Data_DetailsValidationError is the validation error
// returned by ApplicantLookupResponse_Data_Details.Validate if the designated
// constraints aren't met.
type ApplicantLookupResponse_Data_DetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantLookupResponse_Data_DetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantLookupResponse_Data_DetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicantLookupResponse_Data_DetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantLookupResponse_Data_DetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantLookupResponse_Data_DetailsValidationError) ErrorName() string {
	return "ApplicantLookupResponse_Data_DetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicantLookupResponse_Data_DetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicantLookupResponse_Data_Details.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantLookupResponse_Data_DetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantLookupResponse_Data_DetailsValidationError{}

// Validate checks the field values on ApplicantLookupResponse_Data_Activation
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ApplicantLookupResponse_Data_Activation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ApplicantLookupResponse_Data_Activation with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ApplicantLookupResponse_Data_ActivationMultiError, or nil if none found.
func (m *ApplicantLookupResponse_Data_Activation) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicantLookupResponse_Data_Activation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Agreement

	// no validation rules for Mandate

	if len(errors) > 0 {
		return ApplicantLookupResponse_Data_ActivationMultiError(errors)
	}

	return nil
}

// ApplicantLookupResponse_Data_ActivationMultiError is an error wrapping
// multiple validation errors returned by
// ApplicantLookupResponse_Data_Activation.ValidateAll() if the designated
// constraints aren't met.
type ApplicantLookupResponse_Data_ActivationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantLookupResponse_Data_ActivationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantLookupResponse_Data_ActivationMultiError) AllErrors() []error { return m }

// ApplicantLookupResponse_Data_ActivationValidationError is the validation
// error returned by ApplicantLookupResponse_Data_Activation.Validate if the
// designated constraints aren't met.
type ApplicantLookupResponse_Data_ActivationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantLookupResponse_Data_ActivationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantLookupResponse_Data_ActivationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicantLookupResponse_Data_ActivationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantLookupResponse_Data_ActivationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantLookupResponse_Data_ActivationValidationError) ErrorName() string {
	return "ApplicantLookupResponse_Data_ActivationValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicantLookupResponse_Data_ActivationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicantLookupResponse_Data_Activation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantLookupResponse_Data_ActivationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantLookupResponse_Data_ActivationValidationError{}

// Validate checks the field values on GetMandateLinkResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMandateLinkResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMandateLinkResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMandateLinkResponse_DataMultiError, or nil if none found.
func (m *GetMandateLinkResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMandateLinkResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MandateLink

	// no validation rules for MandateId

	if len(errors) > 0 {
		return GetMandateLinkResponse_DataMultiError(errors)
	}

	return nil
}

// GetMandateLinkResponse_DataMultiError is an error wrapping multiple
// validation errors returned by GetMandateLinkResponse_Data.ValidateAll() if
// the designated constraints aren't met.
type GetMandateLinkResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMandateLinkResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMandateLinkResponse_DataMultiError) AllErrors() []error { return m }

// GetMandateLinkResponse_DataValidationError is the validation error returned
// by GetMandateLinkResponse_Data.Validate if the designated constraints
// aren't met.
type GetMandateLinkResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMandateLinkResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMandateLinkResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMandateLinkResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMandateLinkResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMandateLinkResponse_DataValidationError) ErrorName() string {
	return "GetMandateLinkResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetMandateLinkResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMandateLinkResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMandateLinkResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMandateLinkResponse_DataValidationError{}

// Validate checks the field values on GetMandateStatusResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMandateStatusResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMandateStatusResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetMandateStatusResponse_DataMultiError, or nil if none found.
func (m *GetMandateStatusResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMandateStatusResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if len(errors) > 0 {
		return GetMandateStatusResponse_DataMultiError(errors)
	}

	return nil
}

// GetMandateStatusResponse_DataMultiError is an error wrapping multiple
// validation errors returned by GetMandateStatusResponse_Data.ValidateAll()
// if the designated constraints aren't met.
type GetMandateStatusResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMandateStatusResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMandateStatusResponse_DataMultiError) AllErrors() []error { return m }

// GetMandateStatusResponse_DataValidationError is the validation error
// returned by GetMandateStatusResponse_Data.Validate if the designated
// constraints aren't met.
type GetMandateStatusResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMandateStatusResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMandateStatusResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMandateStatusResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMandateStatusResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMandateStatusResponse_DataValidationError) ErrorName() string {
	return "GetMandateStatusResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetMandateStatusResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMandateStatusResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMandateStatusResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMandateStatusResponse_DataValidationError{}

// Validate checks the field values on MakeDrawdownResponse_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MakeDrawdownResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MakeDrawdownResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MakeDrawdownResponse_DataMultiError, or nil if none found.
func (m *MakeDrawdownResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *MakeDrawdownResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanId

	// no validation rules for ApplicantId

	// no validation rules for Urn

	if len(errors) > 0 {
		return MakeDrawdownResponse_DataMultiError(errors)
	}

	return nil
}

// MakeDrawdownResponse_DataMultiError is an error wrapping multiple validation
// errors returned by MakeDrawdownResponse_Data.ValidateAll() if the
// designated constraints aren't met.
type MakeDrawdownResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MakeDrawdownResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MakeDrawdownResponse_DataMultiError) AllErrors() []error { return m }

// MakeDrawdownResponse_DataValidationError is the validation error returned by
// MakeDrawdownResponse_Data.Validate if the designated constraints aren't met.
type MakeDrawdownResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MakeDrawdownResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MakeDrawdownResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MakeDrawdownResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MakeDrawdownResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MakeDrawdownResponse_DataValidationError) ErrorName() string {
	return "MakeDrawdownResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e MakeDrawdownResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMakeDrawdownResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MakeDrawdownResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MakeDrawdownResponse_DataValidationError{}

// Validate checks the field values on GetPdfAgreementResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPdfAgreementResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPdfAgreementResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPdfAgreementResponse_DataMultiError, or nil if none found.
func (m *GetPdfAgreementResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPdfAgreementResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocId

	// no validation rules for Pdf

	if len(errors) > 0 {
		return GetPdfAgreementResponse_DataMultiError(errors)
	}

	return nil
}

// GetPdfAgreementResponse_DataMultiError is an error wrapping multiple
// validation errors returned by GetPdfAgreementResponse_Data.ValidateAll() if
// the designated constraints aren't met.
type GetPdfAgreementResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPdfAgreementResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPdfAgreementResponse_DataMultiError) AllErrors() []error { return m }

// GetPdfAgreementResponse_DataValidationError is the validation error returned
// by GetPdfAgreementResponse_Data.Validate if the designated constraints
// aren't met.
type GetPdfAgreementResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPdfAgreementResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPdfAgreementResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPdfAgreementResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPdfAgreementResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPdfAgreementResponse_DataValidationError) ErrorName() string {
	return "GetPdfAgreementResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetPdfAgreementResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPdfAgreementResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPdfAgreementResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPdfAgreementResponse_DataValidationError{}

// Validate checks the field values on SendBorrowerAgreementOtpResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SendBorrowerAgreementOtpResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendBorrowerAgreementOtpResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SendBorrowerAgreementOtpResponse_DataMultiError, or nil if none found.
func (m *SendBorrowerAgreementOtpResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *SendBorrowerAgreementOtpResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SendBorrowerAgreementOtpResponse_DataMultiError(errors)
	}

	return nil
}

// SendBorrowerAgreementOtpResponse_DataMultiError is an error wrapping
// multiple validation errors returned by
// SendBorrowerAgreementOtpResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type SendBorrowerAgreementOtpResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendBorrowerAgreementOtpResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendBorrowerAgreementOtpResponse_DataMultiError) AllErrors() []error { return m }

// SendBorrowerAgreementOtpResponse_DataValidationError is the validation error
// returned by SendBorrowerAgreementOtpResponse_Data.Validate if the
// designated constraints aren't met.
type SendBorrowerAgreementOtpResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendBorrowerAgreementOtpResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendBorrowerAgreementOtpResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendBorrowerAgreementOtpResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendBorrowerAgreementOtpResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendBorrowerAgreementOtpResponse_DataValidationError) ErrorName() string {
	return "SendBorrowerAgreementOtpResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e SendBorrowerAgreementOtpResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendBorrowerAgreementOtpResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendBorrowerAgreementOtpResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendBorrowerAgreementOtpResponse_DataValidationError{}

// Validate checks the field values on VerifyBorrowerAgreementOtpResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *VerifyBorrowerAgreementOtpResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// VerifyBorrowerAgreementOtpResponse_Data with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// VerifyBorrowerAgreementOtpResponse_DataMultiError, or nil if none found.
func (m *VerifyBorrowerAgreementOtpResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyBorrowerAgreementOtpResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgreementSignedCopy

	if len(errors) > 0 {
		return VerifyBorrowerAgreementOtpResponse_DataMultiError(errors)
	}

	return nil
}

// VerifyBorrowerAgreementOtpResponse_DataMultiError is an error wrapping
// multiple validation errors returned by
// VerifyBorrowerAgreementOtpResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type VerifyBorrowerAgreementOtpResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyBorrowerAgreementOtpResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyBorrowerAgreementOtpResponse_DataMultiError) AllErrors() []error { return m }

// VerifyBorrowerAgreementOtpResponse_DataValidationError is the validation
// error returned by VerifyBorrowerAgreementOtpResponse_Data.Validate if the
// designated constraints aren't met.
type VerifyBorrowerAgreementOtpResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyBorrowerAgreementOtpResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyBorrowerAgreementOtpResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyBorrowerAgreementOtpResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyBorrowerAgreementOtpResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyBorrowerAgreementOtpResponse_DataValidationError) ErrorName() string {
	return "VerifyBorrowerAgreementOtpResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyBorrowerAgreementOtpResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyBorrowerAgreementOtpResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyBorrowerAgreementOtpResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyBorrowerAgreementOtpResponse_DataValidationError{}

// Validate checks the field values on GetLoanStatusResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanStatusResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanStatusResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanStatusResponse_DataMultiError, or nil if none found.
func (m *GetLoanStatusResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanStatusResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanStatusResponse_DataValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanStatusResponse_DataValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanStatusResponse_DataValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanStatusResponse_DataMultiError(errors)
	}

	return nil
}

// GetLoanStatusResponse_DataMultiError is an error wrapping multiple
// validation errors returned by GetLoanStatusResponse_Data.ValidateAll() if
// the designated constraints aren't met.
type GetLoanStatusResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanStatusResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanStatusResponse_DataMultiError) AllErrors() []error { return m }

// GetLoanStatusResponse_DataValidationError is the validation error returned
// by GetLoanStatusResponse_Data.Validate if the designated constraints aren't met.
type GetLoanStatusResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanStatusResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanStatusResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanStatusResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanStatusResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanStatusResponse_DataValidationError) ErrorName() string {
	return "GetLoanStatusResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanStatusResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanStatusResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanStatusResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanStatusResponse_DataValidationError{}

// Validate checks the field values on GetLoanStatusResponse_Data_Status with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLoanStatusResponse_Data_Status) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanStatusResponse_Data_Status
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetLoanStatusResponse_Data_StatusMultiError, or nil if none found.
func (m *GetLoanStatusResponse_Data_Status) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanStatusResponse_Data_Status) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanId

	// no validation rules for LoanCode

	// no validation rules for Status

	// no validation rules for OrderId

	// no validation rules for Urn

	// no validation rules for Amount

	// no validation rules for ProductAmount

	// no validation rules for DisbursedAmount

	// no validation rules for DisbursedDate

	// no validation rules for Emi

	// no validation rules for Tenure

	// no validation rules for Roi

	// no validation rules for Utr

	// no validation rules for LastStatusTimestamp

	if len(errors) > 0 {
		return GetLoanStatusResponse_Data_StatusMultiError(errors)
	}

	return nil
}

// GetLoanStatusResponse_Data_StatusMultiError is an error wrapping multiple
// validation errors returned by
// GetLoanStatusResponse_Data_Status.ValidateAll() if the designated
// constraints aren't met.
type GetLoanStatusResponse_Data_StatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanStatusResponse_Data_StatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanStatusResponse_Data_StatusMultiError) AllErrors() []error { return m }

// GetLoanStatusResponse_Data_StatusValidationError is the validation error
// returned by GetLoanStatusResponse_Data_Status.Validate if the designated
// constraints aren't met.
type GetLoanStatusResponse_Data_StatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanStatusResponse_Data_StatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanStatusResponse_Data_StatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanStatusResponse_Data_StatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanStatusResponse_Data_StatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanStatusResponse_Data_StatusValidationError) ErrorName() string {
	return "GetLoanStatusResponse_Data_StatusValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanStatusResponse_Data_StatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanStatusResponse_Data_Status.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanStatusResponse_Data_StatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanStatusResponse_Data_StatusValidationError{}

// Validate checks the field values on VerifyAndDownloadCkycResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *VerifyAndDownloadCkycResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyAndDownloadCkycResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// VerifyAndDownloadCkycResponse_DataMultiError, or nil if none found.
func (m *VerifyAndDownloadCkycResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyAndDownloadCkycResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPersonalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_DataValidationError{
					field:  "PersonalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_DataValidationError{
					field:  "PersonalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPersonalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponse_DataValidationError{
				field:  "PersonalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyAndDownloadCkycResponse_DataMultiError(errors)
	}

	return nil
}

// VerifyAndDownloadCkycResponse_DataMultiError is an error wrapping multiple
// validation errors returned by
// VerifyAndDownloadCkycResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type VerifyAndDownloadCkycResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyAndDownloadCkycResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyAndDownloadCkycResponse_DataMultiError) AllErrors() []error { return m }

// VerifyAndDownloadCkycResponse_DataValidationError is the validation error
// returned by VerifyAndDownloadCkycResponse_Data.Validate if the designated
// constraints aren't met.
type VerifyAndDownloadCkycResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyAndDownloadCkycResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyAndDownloadCkycResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyAndDownloadCkycResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyAndDownloadCkycResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyAndDownloadCkycResponse_DataValidationError) ErrorName() string {
	return "VerifyAndDownloadCkycResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyAndDownloadCkycResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyAndDownloadCkycResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyAndDownloadCkycResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyAndDownloadCkycResponse_DataValidationError{}

// Validate checks the field values on
// VerifyAndDownloadCkycResponse_Data_PersonalDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyAndDownloadCkycResponse_Data_PersonalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// VerifyAndDownloadCkycResponse_Data_PersonalDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// VerifyAndDownloadCkycResponse_Data_PersonalDetailsMultiError, or nil if
// none found.
func (m *VerifyAndDownloadCkycResponse_Data_PersonalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyAndDownloadCkycResponse_Data_PersonalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CkycNo

	// no validation rules for Name

	// no validation rules for KycDate

	// no validation rules for Gender

	// no validation rules for Dob

	// no validation rules for Fname

	// no validation rules for Lname

	// no validation rules for FatherName

	// no validation rules for MotherName

	// no validation rules for MobNum

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ImageType

	// no validation rules for Photo

	if len(errors) > 0 {
		return VerifyAndDownloadCkycResponse_Data_PersonalDetailsMultiError(errors)
	}

	return nil
}

// VerifyAndDownloadCkycResponse_Data_PersonalDetailsMultiError is an error
// wrapping multiple validation errors returned by
// VerifyAndDownloadCkycResponse_Data_PersonalDetails.ValidateAll() if the
// designated constraints aren't met.
type VerifyAndDownloadCkycResponse_Data_PersonalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyAndDownloadCkycResponse_Data_PersonalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyAndDownloadCkycResponse_Data_PersonalDetailsMultiError) AllErrors() []error { return m }

// VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError is the
// validation error returned by
// VerifyAndDownloadCkycResponse_Data_PersonalDetails.Validate if the
// designated constraints aren't met.
type VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError) ErrorName() string {
	return "VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyAndDownloadCkycResponse_Data_PersonalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyAndDownloadCkycResponse_Data_PersonalDetailsValidationError{}

// Validate checks the field values on
// VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressMultiError, or
// nil if none found.
func (m *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PermLine1

	// no validation rules for PermLine2

	// no validation rules for PermLine3

	// no validation rules for PermCity

	// no validation rules for PermDist

	// no validation rules for PermState

	// no validation rules for PermCountry

	// no validation rules for PermPin

	// no validation rules for PermPoa

	// no validation rules for PermCorresSameFlag

	// no validation rules for CorresLine1

	// no validation rules for CorresLine2

	// no validation rules for CorresLine3

	// no validation rules for CorresCity

	// no validation rules for CorresDist

	// no validation rules for CorresState

	// no validation rules for CorresCountry

	// no validation rules for CorresPin

	// no validation rules for CorresPoa

	if len(errors) > 0 {
		return VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressMultiError(errors)
	}

	return nil
}

// VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressMultiError is an
// error wrapping multiple validation errors returned by
// VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address.ValidateAll() if
// the designated constraints aren't met.
type VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressMultiError) AllErrors() []error {
	return m
}

// VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressValidationError is
// the validation error returned by
// VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address.Validate if the
// designated constraints aren't met.
type VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressValidationError) ErrorName() string {
	return "VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyAndDownloadCkycResponse_Data_PersonalDetails_Address.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyAndDownloadCkycResponse_Data_PersonalDetails_AddressValidationError{}

// Validate checks the field values on GetRepaymentScheduleResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetRepaymentScheduleResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRepaymentScheduleResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRepaymentScheduleResponse_DataMultiError, or nil if none found.
func (m *GetRepaymentScheduleResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRepaymentScheduleResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSchedule() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRepaymentScheduleResponse_DataValidationError{
						field:  fmt.Sprintf("Schedule[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRepaymentScheduleResponse_DataValidationError{
						field:  fmt.Sprintf("Schedule[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRepaymentScheduleResponse_DataValidationError{
					field:  fmt.Sprintf("Schedule[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRepaymentScheduleResponse_DataMultiError(errors)
	}

	return nil
}

// GetRepaymentScheduleResponse_DataMultiError is an error wrapping multiple
// validation errors returned by
// GetRepaymentScheduleResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type GetRepaymentScheduleResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRepaymentScheduleResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRepaymentScheduleResponse_DataMultiError) AllErrors() []error { return m }

// GetRepaymentScheduleResponse_DataValidationError is the validation error
// returned by GetRepaymentScheduleResponse_Data.Validate if the designated
// constraints aren't met.
type GetRepaymentScheduleResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRepaymentScheduleResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRepaymentScheduleResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRepaymentScheduleResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRepaymentScheduleResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRepaymentScheduleResponse_DataValidationError) ErrorName() string {
	return "GetRepaymentScheduleResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetRepaymentScheduleResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRepaymentScheduleResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRepaymentScheduleResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRepaymentScheduleResponse_DataValidationError{}

// Validate checks the field values on GetRepaymentScheduleErrorResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRepaymentScheduleErrorResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRepaymentScheduleErrorResponse_Data with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetRepaymentScheduleErrorResponse_DataMultiError, or nil if none found.
func (m *GetRepaymentScheduleErrorResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRepaymentScheduleErrorResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSchedule() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRepaymentScheduleErrorResponse_DataValidationError{
						field:  fmt.Sprintf("Schedule[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRepaymentScheduleErrorResponse_DataValidationError{
						field:  fmt.Sprintf("Schedule[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRepaymentScheduleErrorResponse_DataValidationError{
					field:  fmt.Sprintf("Schedule[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRepaymentScheduleErrorResponse_DataMultiError(errors)
	}

	return nil
}

// GetRepaymentScheduleErrorResponse_DataMultiError is an error wrapping
// multiple validation errors returned by
// GetRepaymentScheduleErrorResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type GetRepaymentScheduleErrorResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRepaymentScheduleErrorResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRepaymentScheduleErrorResponse_DataMultiError) AllErrors() []error { return m }

// GetRepaymentScheduleErrorResponse_DataValidationError is the validation
// error returned by GetRepaymentScheduleErrorResponse_Data.Validate if the
// designated constraints aren't met.
type GetRepaymentScheduleErrorResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRepaymentScheduleErrorResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRepaymentScheduleErrorResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRepaymentScheduleErrorResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRepaymentScheduleErrorResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRepaymentScheduleErrorResponse_DataValidationError) ErrorName() string {
	return "GetRepaymentScheduleErrorResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetRepaymentScheduleErrorResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRepaymentScheduleErrorResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRepaymentScheduleErrorResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRepaymentScheduleErrorResponse_DataValidationError{}

// Validate checks the field values on ErrorResponse_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ErrorResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ErrorResponse_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ErrorResponse_DataMultiError, or nil if none found.
func (m *ErrorResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ErrorResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ErrorResponse_DataMultiError(errors)
	}

	return nil
}

// ErrorResponse_DataMultiError is an error wrapping multiple validation errors
// returned by ErrorResponse_Data.ValidateAll() if the designated constraints
// aren't met.
type ErrorResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ErrorResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ErrorResponse_DataMultiError) AllErrors() []error { return m }

// ErrorResponse_DataValidationError is the validation error returned by
// ErrorResponse_Data.Validate if the designated constraints aren't met.
type ErrorResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ErrorResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ErrorResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ErrorResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ErrorResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ErrorResponse_DataValidationError) ErrorName() string {
	return "ErrorResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ErrorResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sErrorResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ErrorResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ErrorResponse_DataValidationError{}

// Validate checks the field values on UploadDocumentResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadDocumentResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadDocumentResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadDocumentResponse_DataMultiError, or nil if none found.
func (m *UploadDocumentResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadDocumentResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for ApplicantId

	// no validation rules for DocumentTypeId

	// no validation rules for NbfcId

	// no validation rules for Id

	// no validation rules for FileName

	// no validation rules for FilePath

	// no validation rules for Remarks

	// no validation rules for IsPasswordProtected

	// no validation rules for CreatedBy

	// no validation rules for Source

	// no validation rules for DocumentPassword

	// no validation rules for UpdatedAt

	// no validation rules for CreatedAt

	if len(errors) > 0 {
		return UploadDocumentResponse_DataMultiError(errors)
	}

	return nil
}

// UploadDocumentResponse_DataMultiError is an error wrapping multiple
// validation errors returned by UploadDocumentResponse_Data.ValidateAll() if
// the designated constraints aren't met.
type UploadDocumentResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadDocumentResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadDocumentResponse_DataMultiError) AllErrors() []error { return m }

// UploadDocumentResponse_DataValidationError is the validation error returned
// by UploadDocumentResponse_Data.Validate if the designated constraints
// aren't met.
type UploadDocumentResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadDocumentResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadDocumentResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadDocumentResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadDocumentResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadDocumentResponse_DataValidationError) ErrorName() string {
	return "UploadDocumentResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e UploadDocumentResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadDocumentResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadDocumentResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadDocumentResponse_DataValidationError{}

// Validate checks the field values on SaveCollectionRequest_PaymentSchedule
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SaveCollectionRequest_PaymentSchedule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveCollectionRequest_PaymentSchedule
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SaveCollectionRequest_PaymentScheduleMultiError, or nil if none found.
func (m *SaveCollectionRequest_PaymentSchedule) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveCollectionRequest_PaymentSchedule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModeOfPayment

	// no validation rules for TransactionDate

	// no validation rules for PaymentStatus

	// no validation rules for PaidTotalAmount

	// no validation rules for VoucherNo

	// no validation rules for Udf1

	// no validation rules for Udf2

	// no validation rules for BounceCharges

	// no validation rules for CollectionCharges

	// no validation rules for OtherCharges

	// no validation rules for DueDate

	// no validation rules for SettelmentDate

	// no validation rules for LpiCharges

	if len(errors) > 0 {
		return SaveCollectionRequest_PaymentScheduleMultiError(errors)
	}

	return nil
}

// SaveCollectionRequest_PaymentScheduleMultiError is an error wrapping
// multiple validation errors returned by
// SaveCollectionRequest_PaymentSchedule.ValidateAll() if the designated
// constraints aren't met.
type SaveCollectionRequest_PaymentScheduleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveCollectionRequest_PaymentScheduleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveCollectionRequest_PaymentScheduleMultiError) AllErrors() []error { return m }

// SaveCollectionRequest_PaymentScheduleValidationError is the validation error
// returned by SaveCollectionRequest_PaymentSchedule.Validate if the
// designated constraints aren't met.
type SaveCollectionRequest_PaymentScheduleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveCollectionRequest_PaymentScheduleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveCollectionRequest_PaymentScheduleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveCollectionRequest_PaymentScheduleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveCollectionRequest_PaymentScheduleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveCollectionRequest_PaymentScheduleValidationError) ErrorName() string {
	return "SaveCollectionRequest_PaymentScheduleValidationError"
}

// Error satisfies the builtin error interface
func (e SaveCollectionRequest_PaymentScheduleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveCollectionRequest_PaymentSchedule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveCollectionRequest_PaymentScheduleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveCollectionRequest_PaymentScheduleValidationError{}

// Validate checks the field values on SaveCollectionResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveCollectionResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveCollectionResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveCollectionResponse_DataMultiError, or nil if none found.
func (m *SaveCollectionResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveCollectionResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanId

	if len(errors) > 0 {
		return SaveCollectionResponse_DataMultiError(errors)
	}

	return nil
}

// SaveCollectionResponse_DataMultiError is an error wrapping multiple
// validation errors returned by SaveCollectionResponse_Data.ValidateAll() if
// the designated constraints aren't met.
type SaveCollectionResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveCollectionResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveCollectionResponse_DataMultiError) AllErrors() []error { return m }

// SaveCollectionResponse_DataValidationError is the validation error returned
// by SaveCollectionResponse_Data.Validate if the designated constraints
// aren't met.
type SaveCollectionResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveCollectionResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveCollectionResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveCollectionResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveCollectionResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveCollectionResponse_DataValidationError) ErrorName() string {
	return "SaveCollectionResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e SaveCollectionResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveCollectionResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveCollectionResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveCollectionResponse_DataValidationError{}

// Validate checks the field values on HashGenerationForOkycResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *HashGenerationForOkycResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HashGenerationForOkycResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// HashGenerationForOkycResponse_DataMultiError, or nil if none found.
func (m *HashGenerationForOkycResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *HashGenerationForOkycResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Hash

	if len(errors) > 0 {
		return HashGenerationForOkycResponse_DataMultiError(errors)
	}

	return nil
}

// HashGenerationForOkycResponse_DataMultiError is an error wrapping multiple
// validation errors returned by
// HashGenerationForOkycResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type HashGenerationForOkycResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HashGenerationForOkycResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HashGenerationForOkycResponse_DataMultiError) AllErrors() []error { return m }

// HashGenerationForOkycResponse_DataValidationError is the validation error
// returned by HashGenerationForOkycResponse_Data.Validate if the designated
// constraints aren't met.
type HashGenerationForOkycResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HashGenerationForOkycResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HashGenerationForOkycResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HashGenerationForOkycResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HashGenerationForOkycResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HashGenerationForOkycResponse_DataValidationError) ErrorName() string {
	return "HashGenerationForOkycResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e HashGenerationForOkycResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHashGenerationForOkycResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HashGenerationForOkycResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HashGenerationForOkycResponse_DataValidationError{}

// Validate checks the field values on CaptchaGenerationForOkycResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CaptchaGenerationForOkycResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaptchaGenerationForOkycResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CaptchaGenerationForOkycResponse_DataMultiError, or nil if none found.
func (m *CaptchaGenerationForOkycResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *CaptchaGenerationForOkycResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaptchaImage

	// no validation rules for RequestToken

	// no validation rules for CaptchaTxnId

	if len(errors) > 0 {
		return CaptchaGenerationForOkycResponse_DataMultiError(errors)
	}

	return nil
}

// CaptchaGenerationForOkycResponse_DataMultiError is an error wrapping
// multiple validation errors returned by
// CaptchaGenerationForOkycResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type CaptchaGenerationForOkycResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaptchaGenerationForOkycResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaptchaGenerationForOkycResponse_DataMultiError) AllErrors() []error { return m }

// CaptchaGenerationForOkycResponse_DataValidationError is the validation error
// returned by CaptchaGenerationForOkycResponse_Data.Validate if the
// designated constraints aren't met.
type CaptchaGenerationForOkycResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaptchaGenerationForOkycResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaptchaGenerationForOkycResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaptchaGenerationForOkycResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaptchaGenerationForOkycResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaptchaGenerationForOkycResponse_DataValidationError) ErrorName() string {
	return "CaptchaGenerationForOkycResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e CaptchaGenerationForOkycResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaptchaGenerationForOkycResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaptchaGenerationForOkycResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaptchaGenerationForOkycResponse_DataValidationError{}

// Validate checks the field values on GenerateOtpForOkycResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpForOkycResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpForOkycResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GenerateOtpForOkycResponse_DataMultiError, or nil if none found.
func (m *GenerateOtpForOkycResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpForOkycResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnId

	if len(errors) > 0 {
		return GenerateOtpForOkycResponse_DataMultiError(errors)
	}

	return nil
}

// GenerateOtpForOkycResponse_DataMultiError is an error wrapping multiple
// validation errors returned by GenerateOtpForOkycResponse_Data.ValidateAll()
// if the designated constraints aren't met.
type GenerateOtpForOkycResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpForOkycResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpForOkycResponse_DataMultiError) AllErrors() []error { return m }

// GenerateOtpForOkycResponse_DataValidationError is the validation error
// returned by GenerateOtpForOkycResponse_Data.Validate if the designated
// constraints aren't met.
type GenerateOtpForOkycResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpForOkycResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpForOkycResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpForOkycResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpForOkycResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpForOkycResponse_DataValidationError) ErrorName() string {
	return "GenerateOtpForOkycResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpForOkycResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpForOkycResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpForOkycResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpForOkycResponse_DataValidationError{}

// Validate checks the field values on ValidateOtpForOkycResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateOtpForOkycResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateOtpForOkycResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ValidateOtpForOkycResponse_DataMultiError, or nil if none found.
func (m *ValidateOtpForOkycResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateOtpForOkycResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ValidateOtpForOkycResponse_DataMultiError(errors)
	}

	return nil
}

// ValidateOtpForOkycResponse_DataMultiError is an error wrapping multiple
// validation errors returned by ValidateOtpForOkycResponse_Data.ValidateAll()
// if the designated constraints aren't met.
type ValidateOtpForOkycResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateOtpForOkycResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateOtpForOkycResponse_DataMultiError) AllErrors() []error { return m }

// ValidateOtpForOkycResponse_DataValidationError is the validation error
// returned by ValidateOtpForOkycResponse_Data.Validate if the designated
// constraints aren't met.
type ValidateOtpForOkycResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateOtpForOkycResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateOtpForOkycResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateOtpForOkycResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateOtpForOkycResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateOtpForOkycResponse_DataValidationError) ErrorName() string {
	return "ValidateOtpForOkycResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateOtpForOkycResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateOtpForOkycResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateOtpForOkycResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateOtpForOkycResponse_DataValidationError{}

// Validate checks the field values on UpdateLeadRequest_SchemeDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLeadRequest_SchemeDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLeadRequest_SchemeDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateLeadRequest_SchemeDetailsMultiError, or nil if none found.
func (m *UpdateLeadRequest_SchemeDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLeadRequest_SchemeDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InstallmentFrequency

	// no validation rules for InstallmentTenure

	// no validation rules for ProcessingFeesValue

	if all {
		switch v := interface{}(m.GetRoiPercentage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLeadRequest_SchemeDetailsValidationError{
					field:  "RoiPercentage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLeadRequest_SchemeDetailsValidationError{
					field:  "RoiPercentage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRoiPercentage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLeadRequest_SchemeDetailsValidationError{
				field:  "RoiPercentage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InstallmentStartDate

	// no validation rules for SubventionPercentage

	// no validation rules for ProcessingFeesType

	// no validation rules for RoiType

	// no validation rules for RoiAppliedOn

	// no validation rules for ProcessingFeesCustomerAppliedOn

	// no validation rules for ProcessingFeesCustomerGst

	// no validation rules for RoiGst

	// no validation rules for GstType

	// no validation rules for GstValue

	if len(errors) > 0 {
		return UpdateLeadRequest_SchemeDetailsMultiError(errors)
	}

	return nil
}

// UpdateLeadRequest_SchemeDetailsMultiError is an error wrapping multiple
// validation errors returned by UpdateLeadRequest_SchemeDetails.ValidateAll()
// if the designated constraints aren't met.
type UpdateLeadRequest_SchemeDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLeadRequest_SchemeDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLeadRequest_SchemeDetailsMultiError) AllErrors() []error { return m }

// UpdateLeadRequest_SchemeDetailsValidationError is the validation error
// returned by UpdateLeadRequest_SchemeDetails.Validate if the designated
// constraints aren't met.
type UpdateLeadRequest_SchemeDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLeadRequest_SchemeDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLeadRequest_SchemeDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLeadRequest_SchemeDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLeadRequest_SchemeDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLeadRequest_SchemeDetailsValidationError) ErrorName() string {
	return "UpdateLeadRequest_SchemeDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLeadRequest_SchemeDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLeadRequest_SchemeDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLeadRequest_SchemeDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLeadRequest_SchemeDetailsValidationError{}

// Validate checks the field values on UpdateLeadResponse_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLeadResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLeadResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLeadResponse_DataMultiError, or nil if none found.
func (m *UpdateLeadResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLeadResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanId

	if len(errors) > 0 {
		return UpdateLeadResponse_DataMultiError(errors)
	}

	return nil
}

// UpdateLeadResponse_DataMultiError is an error wrapping multiple validation
// errors returned by UpdateLeadResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type UpdateLeadResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLeadResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLeadResponse_DataMultiError) AllErrors() []error { return m }

// UpdateLeadResponse_DataValidationError is the validation error returned by
// UpdateLeadResponse_Data.Validate if the designated constraints aren't met.
type UpdateLeadResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLeadResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLeadResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLeadResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLeadResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLeadResponse_DataValidationError) ErrorName() string {
	return "UpdateLeadResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLeadResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLeadResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLeadResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLeadResponse_DataValidationError{}

// Validate checks the field values on CancelLeadResponse_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelLeadResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelLeadResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelLeadResponse_DataMultiError, or nil if none found.
func (m *CancelLeadResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelLeadResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for ApplicantId

	if len(errors) > 0 {
		return CancelLeadResponse_DataMultiError(errors)
	}

	return nil
}

// CancelLeadResponse_DataMultiError is an error wrapping multiple validation
// errors returned by CancelLeadResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type CancelLeadResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelLeadResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelLeadResponse_DataMultiError) AllErrors() []error { return m }

// CancelLeadResponse_DataValidationError is the validation error returned by
// CancelLeadResponse_Data.Validate if the designated constraints aren't met.
type CancelLeadResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelLeadResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelLeadResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelLeadResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelLeadResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelLeadResponse_DataValidationError) ErrorName() string {
	return "CancelLeadResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e CancelLeadResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelLeadResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelLeadResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelLeadResponse_DataValidationError{}

// Validate checks the field values on ForeClosureDetailsResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForeClosureDetailsResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForeClosureDetailsResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ForeClosureDetailsResponse_DataMultiError, or nil if none found.
func (m *ForeClosureDetailsResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ForeClosureDetailsResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for TotalOutstanding

	// no validation rules for PrincipalOutstanding

	// no validation rules for InterestOutstanding

	// no validation rules for PenaltyCharges

	// no validation rules for FeesCharges

	// no validation rules for OtherCharges

	if len(errors) > 0 {
		return ForeClosureDetailsResponse_DataMultiError(errors)
	}

	return nil
}

// ForeClosureDetailsResponse_DataMultiError is an error wrapping multiple
// validation errors returned by ForeClosureDetailsResponse_Data.ValidateAll()
// if the designated constraints aren't met.
type ForeClosureDetailsResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForeClosureDetailsResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForeClosureDetailsResponse_DataMultiError) AllErrors() []error { return m }

// ForeClosureDetailsResponse_DataValidationError is the validation error
// returned by ForeClosureDetailsResponse_Data.Validate if the designated
// constraints aren't met.
type ForeClosureDetailsResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForeClosureDetailsResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForeClosureDetailsResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForeClosureDetailsResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForeClosureDetailsResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForeClosureDetailsResponse_DataValidationError) ErrorName() string {
	return "ForeClosureDetailsResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ForeClosureDetailsResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForeClosureDetailsResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForeClosureDetailsResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForeClosureDetailsResponse_DataValidationError{}

// Validate checks the field values on CreateRepaymentScheduleRequest_Schedule
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateRepaymentScheduleRequest_Schedule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateRepaymentScheduleRequest_Schedule with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CreateRepaymentScheduleRequest_ScheduleMultiError, or nil if none found.
func (m *CreateRepaymentScheduleRequest_Schedule) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRepaymentScheduleRequest_Schedule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Date

	if all {
		switch v := interface{}(m.GetPrincipal()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "Principal",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "Principal",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipal()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleRequest_ScheduleValidationError{
				field:  "Principal",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "Interest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "Interest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleRequest_ScheduleValidationError{
				field:  "Interest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOtherCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtherCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleRequest_ScheduleValidationError{
				field:  "OtherCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "TotalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "TotalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleRequest_ScheduleValidationError{
				field:  "TotalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "PrincipalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "PrincipalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleRequest_ScheduleValidationError{
				field:  "PrincipalOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRepaymentScheduleRequest_ScheduleMultiError(errors)
	}

	return nil
}

// CreateRepaymentScheduleRequest_ScheduleMultiError is an error wrapping
// multiple validation errors returned by
// CreateRepaymentScheduleRequest_Schedule.ValidateAll() if the designated
// constraints aren't met.
type CreateRepaymentScheduleRequest_ScheduleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRepaymentScheduleRequest_ScheduleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRepaymentScheduleRequest_ScheduleMultiError) AllErrors() []error { return m }

// CreateRepaymentScheduleRequest_ScheduleValidationError is the validation
// error returned by CreateRepaymentScheduleRequest_Schedule.Validate if the
// designated constraints aren't met.
type CreateRepaymentScheduleRequest_ScheduleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRepaymentScheduleRequest_ScheduleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRepaymentScheduleRequest_ScheduleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRepaymentScheduleRequest_ScheduleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRepaymentScheduleRequest_ScheduleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRepaymentScheduleRequest_ScheduleValidationError) ErrorName() string {
	return "CreateRepaymentScheduleRequest_ScheduleValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRepaymentScheduleRequest_ScheduleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRepaymentScheduleRequest_Schedule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRepaymentScheduleRequest_ScheduleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRepaymentScheduleRequest_ScheduleValidationError{}

// Validate checks the field values on CreateRepaymentScheduleResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateRepaymentScheduleResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRepaymentScheduleResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateRepaymentScheduleResponse_DataMultiError, or nil if none found.
func (m *CreateRepaymentScheduleResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRepaymentScheduleResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanId

	if len(errors) > 0 {
		return CreateRepaymentScheduleResponse_DataMultiError(errors)
	}

	return nil
}

// CreateRepaymentScheduleResponse_DataMultiError is an error wrapping multiple
// validation errors returned by
// CreateRepaymentScheduleResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type CreateRepaymentScheduleResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRepaymentScheduleResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRepaymentScheduleResponse_DataMultiError) AllErrors() []error { return m }

// CreateRepaymentScheduleResponse_DataValidationError is the validation error
// returned by CreateRepaymentScheduleResponse_Data.Validate if the designated
// constraints aren't met.
type CreateRepaymentScheduleResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRepaymentScheduleResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRepaymentScheduleResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRepaymentScheduleResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRepaymentScheduleResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRepaymentScheduleResponse_DataValidationError) ErrorName() string {
	return "CreateRepaymentScheduleResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRepaymentScheduleResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRepaymentScheduleResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRepaymentScheduleResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRepaymentScheduleResponse_DataValidationError{}
