// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/liquiloans/lending/preapproved_loan.proto

package lending

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetCreditLineDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid         string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	ApplicantId string `protobuf:"bytes,2,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	Checksum    string `protobuf:"bytes,3,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *GetCreditLineDetailsRequest) Reset() {
	*x = GetCreditLineDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditLineDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditLineDetailsRequest) ProtoMessage() {}

func (x *GetCreditLineDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditLineDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetCreditLineDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{0}
}

func (x *GetCreditLineDetailsRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *GetCreditLineDetailsRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetCreditLineDetailsRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type GetCreditLineDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                               `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                             `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *GetCreditLineDetailsResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                              `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                             `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *GetCreditLineDetailsResponse) Reset() {
	*x = GetCreditLineDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditLineDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditLineDetailsResponse) ProtoMessage() {}

func (x *GetCreditLineDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditLineDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetCreditLineDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{1}
}

func (x *GetCreditLineDetailsResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *GetCreditLineDetailsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetCreditLineDetailsResponse) GetData() *GetCreditLineDetailsResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetCreditLineDetailsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetCreditLineDetailsResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type AddPersonalDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid           string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email         string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	ContactNumber string `protobuf:"bytes,4,opt,name=contact_number,proto3" json:"contact_number,omitempty"`
	Pan           string `protobuf:"bytes,5,opt,name=pan,proto3" json:"pan,omitempty"`
	Gender        string `protobuf:"bytes,6,opt,name=gender,proto3" json:"gender,omitempty"`
	Dob           string `protobuf:"bytes,7,opt,name=dob,proto3" json:"dob,omitempty"`
	Checksum      string `protobuf:"bytes,8,opt,name=checksum,proto3" json:"checksum,omitempty"`
	Urn           string `protobuf:"bytes,9,opt,name=urn,proto3" json:"urn,omitempty"`
	Udf1          string `protobuf:"bytes,10,opt,name=udf1,proto3" json:"udf1,omitempty"`
	Udf8          string `protobuf:"bytes,11,opt,name=udf8,json=UDF8,proto3" json:"udf8,omitempty"`
	Udf9          string `protobuf:"bytes,12,opt,name=udf9,json=UDF9,proto3" json:"udf9,omitempty"`
	// represents the monthly income of the user
	Udf4 string `protobuf:"bytes,13,opt,name=udf4,json=UDF4,proto3" json:"udf4,omitempty"`
	// represents the income data source
	// "AA" - income data source is txns from account aggregator
	Udf5 string `protobuf:"bytes,14,opt,name=udf5,json=UDF5,proto3" json:"udf5,omitempty"`
	Udf2 string `protobuf:"bytes,15,opt,name=udf2,json=UDF2,proto3" json:"udf2,omitempty"`
}

func (x *AddPersonalDetailsRequest) Reset() {
	*x = AddPersonalDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPersonalDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPersonalDetailsRequest) ProtoMessage() {}

func (x *AddPersonalDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPersonalDetailsRequest.ProtoReflect.Descriptor instead.
func (*AddPersonalDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{2}
}

func (x *AddPersonalDetailsRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetContactNumber() string {
	if x != nil {
		return x.ContactNumber
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetDob() string {
	if x != nil {
		return x.Dob
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetUdf1() string {
	if x != nil {
		return x.Udf1
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetUdf8() string {
	if x != nil {
		return x.Udf8
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetUdf9() string {
	if x != nil {
		return x.Udf9
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetUdf4() string {
	if x != nil {
		return x.Udf4
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetUdf5() string {
	if x != nil {
		return x.Udf5
	}
	return ""
}

func (x *AddPersonalDetailsRequest) GetUdf2() string {
	if x != nil {
		return x.Udf2
	}
	return ""
}

type AddPersonalDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                             `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                           `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *AddPersonalDetailsResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                            `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                           `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *AddPersonalDetailsResponse) Reset() {
	*x = AddPersonalDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPersonalDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPersonalDetailsResponse) ProtoMessage() {}

func (x *AddPersonalDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPersonalDetailsResponse.ProtoReflect.Descriptor instead.
func (*AddPersonalDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{3}
}

func (x *AddPersonalDetailsResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *AddPersonalDetailsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *AddPersonalDetailsResponse) GetData() *AddPersonalDetailsResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *AddPersonalDetailsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AddPersonalDetailsResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type AddBankingDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantId       string `protobuf:"bytes,1,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	AccountType       string `protobuf:"bytes,2,opt,name=account_type,proto3" json:"account_type,omitempty"`
	BankName          string `protobuf:"bytes,3,opt,name=bank_name,proto3" json:"bank_name,omitempty"`
	Ifsc              string `protobuf:"bytes,4,opt,name=ifsc,proto3" json:"ifsc,omitempty"`
	AccountNumber     string `protobuf:"bytes,5,opt,name=account_number,proto3" json:"account_number,omitempty"`
	AccountHolderName string `protobuf:"bytes,6,opt,name=account_holder_name,proto3" json:"account_holder_name,omitempty"`
	Sid               string `protobuf:"bytes,7,opt,name=sid,proto3" json:"sid,omitempty"`
	Checksum          string `protobuf:"bytes,8,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *AddBankingDetailsRequest) Reset() {
	*x = AddBankingDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddBankingDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBankingDetailsRequest) ProtoMessage() {}

func (x *AddBankingDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBankingDetailsRequest.ProtoReflect.Descriptor instead.
func (*AddBankingDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{4}
}

func (x *AddBankingDetailsRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *AddBankingDetailsRequest) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

func (x *AddBankingDetailsRequest) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *AddBankingDetailsRequest) GetIfsc() string {
	if x != nil {
		return x.Ifsc
	}
	return ""
}

func (x *AddBankingDetailsRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *AddBankingDetailsRequest) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

func (x *AddBankingDetailsRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *AddBankingDetailsRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type AddAddressDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantId   string `protobuf:"bytes,1,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	AddressLine_1 string `protobuf:"bytes,2,opt,name=address_line_1,proto3" json:"address_line_1,omitempty"`
	AddressLine_2 string `protobuf:"bytes,3,opt,name=address_line_2,proto3" json:"address_line_2,omitempty"`
	Area          string `protobuf:"bytes,4,opt,name=area,proto3" json:"area,omitempty"`
	PinCode       string `protobuf:"bytes,5,opt,name=pin_code,proto3" json:"pin_code,omitempty"`
	City          string `protobuf:"bytes,6,opt,name=city,proto3" json:"city,omitempty"`
	State         string `protobuf:"bytes,7,opt,name=state,proto3" json:"state,omitempty"`
	AddressType   string `protobuf:"bytes,8,opt,name=address_type,proto3" json:"address_type,omitempty"`
	Sid           string `protobuf:"bytes,9,opt,name=sid,proto3" json:"sid,omitempty"`
	Checksum      string `protobuf:"bytes,10,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *AddAddressDetailsRequest) Reset() {
	*x = AddAddressDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAddressDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAddressDetailsRequest) ProtoMessage() {}

func (x *AddAddressDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAddressDetailsRequest.ProtoReflect.Descriptor instead.
func (*AddAddressDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{5}
}

func (x *AddAddressDetailsRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *AddAddressDetailsRequest) GetAddressLine_1() string {
	if x != nil {
		return x.AddressLine_1
	}
	return ""
}

func (x *AddAddressDetailsRequest) GetAddressLine_2() string {
	if x != nil {
		return x.AddressLine_2
	}
	return ""
}

func (x *AddAddressDetailsRequest) GetArea() string {
	if x != nil {
		return x.Area
	}
	return ""
}

func (x *AddAddressDetailsRequest) GetPinCode() string {
	if x != nil {
		return x.PinCode
	}
	return ""
}

func (x *AddAddressDetailsRequest) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *AddAddressDetailsRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *AddAddressDetailsRequest) GetAddressType() string {
	if x != nil {
		return x.AddressType
	}
	return ""
}

func (x *AddAddressDetailsRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *AddAddressDetailsRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

// This can have income_transfer_type, joining_date -- Optional Fields
type AddEmploymentDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid              string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	ApplicantId      string `protobuf:"bytes,2,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	Occupation       string `protobuf:"bytes,3,opt,name=occupation,proto3" json:"occupation,omitempty"`
	OrganizationName string `protobuf:"bytes,4,opt,name=organization_name,proto3" json:"organization_name,omitempty"`
	MonthlyIncome    string `protobuf:"bytes,5,opt,name=monthly_income,proto3" json:"monthly_income,omitempty"`
	Checksum         string `protobuf:"bytes,6,opt,name=checksum,proto3" json:"checksum,omitempty"`
	Designation      string `protobuf:"bytes,7,opt,name=designation,proto3" json:"designation,omitempty"`
}

func (x *AddEmploymentDetailsRequest) Reset() {
	*x = AddEmploymentDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddEmploymentDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddEmploymentDetailsRequest) ProtoMessage() {}

func (x *AddEmploymentDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddEmploymentDetailsRequest.ProtoReflect.Descriptor instead.
func (*AddEmploymentDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{6}
}

func (x *AddEmploymentDetailsRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *AddEmploymentDetailsRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *AddEmploymentDetailsRequest) GetOccupation() string {
	if x != nil {
		return x.Occupation
	}
	return ""
}

func (x *AddEmploymentDetailsRequest) GetOrganizationName() string {
	if x != nil {
		return x.OrganizationName
	}
	return ""
}

func (x *AddEmploymentDetailsRequest) GetMonthlyIncome() string {
	if x != nil {
		return x.MonthlyIncome
	}
	return ""
}

func (x *AddEmploymentDetailsRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *AddEmploymentDetailsRequest) GetDesignation() string {
	if x != nil {
		return x.Designation
	}
	return ""
}

// Common response used across multiple APIs (Add banking, employment and address details) as structure is same
type AddDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool             `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string           `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *structpb.Struct `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32            `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string           `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *AddDetailsResponse) Reset() {
	*x = AddDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddDetailsResponse) ProtoMessage() {}

func (x *AddDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddDetailsResponse.ProtoReflect.Descriptor instead.
func (*AddDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{7}
}

func (x *AddDetailsResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *AddDetailsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *AddDetailsResponse) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *AddDetailsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AddDetailsResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type GetLimitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid         string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	ApplicantId string `protobuf:"bytes,2,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	Checksum    string `protobuf:"bytes,3,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *GetLimitRequest) Reset() {
	*x = GetLimitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLimitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLimitRequest) ProtoMessage() {}

func (x *GetLimitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLimitRequest.ProtoReflect.Descriptor instead.
func (*GetLimitRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{8}
}

func (x *GetLimitRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *GetLimitRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetLimitRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type GetLimitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *GetLimitResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                  `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                 `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *GetLimitResponse) Reset() {
	*x = GetLimitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLimitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLimitResponse) ProtoMessage() {}

func (x *GetLimitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLimitResponse.ProtoReflect.Descriptor instead.
func (*GetLimitResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{9}
}

func (x *GetLimitResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *GetLimitResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetLimitResponse) GetData() *GetLimitResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetLimitResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetLimitResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type ApplicantLookupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid      string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	Pan      string `protobuf:"bytes,2,opt,name=pan,proto3" json:"pan,omitempty"`
	Checksum string `protobuf:"bytes,3,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *ApplicantLookupRequest) Reset() {
	*x = ApplicantLookupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicantLookupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicantLookupRequest) ProtoMessage() {}

func (x *ApplicantLookupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicantLookupRequest.ProtoReflect.Descriptor instead.
func (*ApplicantLookupRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{10}
}

func (x *ApplicantLookupRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *ApplicantLookupRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *ApplicantLookupRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type ApplicantLookupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                          `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                        `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *ApplicantLookupResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                         `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                        `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *ApplicantLookupResponse) Reset() {
	*x = ApplicantLookupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicantLookupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicantLookupResponse) ProtoMessage() {}

func (x *ApplicantLookupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicantLookupResponse.ProtoReflect.Descriptor instead.
func (*ApplicantLookupResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{11}
}

func (x *ApplicantLookupResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *ApplicantLookupResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ApplicantLookupResponse) GetData() *ApplicantLookupResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ApplicantLookupResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ApplicantLookupResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type GetMandateLinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid         string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	ApplicantId string `protobuf:"bytes,2,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	Checksum    string `protobuf:"bytes,3,opt,name=checksum,proto3" json:"checksum,omitempty"`
	RecreateUrl string `protobuf:"bytes,4,opt,name=recreate_url,proto3" json:"recreate_url,omitempty"`
}

func (x *GetMandateLinkRequest) Reset() {
	*x = GetMandateLinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateLinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateLinkRequest) ProtoMessage() {}

func (x *GetMandateLinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateLinkRequest.ProtoReflect.Descriptor instead.
func (*GetMandateLinkRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{12}
}

func (x *GetMandateLinkRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *GetMandateLinkRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetMandateLinkRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *GetMandateLinkRequest) GetRecreateUrl() string {
	if x != nil {
		return x.RecreateUrl
	}
	return ""
}

type GetMandateLinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                         `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                       `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *GetMandateLinkResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                        `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                       `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *GetMandateLinkResponse) Reset() {
	*x = GetMandateLinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateLinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateLinkResponse) ProtoMessage() {}

func (x *GetMandateLinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateLinkResponse.ProtoReflect.Descriptor instead.
func (*GetMandateLinkResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{13}
}

func (x *GetMandateLinkResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *GetMandateLinkResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetMandateLinkResponse) GetData() *GetMandateLinkResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetMandateLinkResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMandateLinkResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type GetMandateStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid         string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	ApplicantId string `protobuf:"bytes,2,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	Checksum    string `protobuf:"bytes,3,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *GetMandateStatusRequest) Reset() {
	*x = GetMandateStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateStatusRequest) ProtoMessage() {}

func (x *GetMandateStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateStatusRequest.ProtoReflect.Descriptor instead.
func (*GetMandateStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{14}
}

func (x *GetMandateStatusRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *GetMandateStatusRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetMandateStatusRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type GetMandateStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                           `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                         `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *GetMandateStatusResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                          `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                         `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *GetMandateStatusResponse) Reset() {
	*x = GetMandateStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateStatusResponse) ProtoMessage() {}

func (x *GetMandateStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateStatusResponse.ProtoReflect.Descriptor instead.
func (*GetMandateStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{15}
}

func (x *GetMandateStatusResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *GetMandateStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetMandateStatusResponse) GetData() *GetMandateStatusResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetMandateStatusResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMandateStatusResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type MakeDrawdownRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid             string  `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	ApplicantId     string  `protobuf:"bytes,2,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	Amount          float64 `protobuf:"fixed64,3,opt,name=amount,proto3" json:"amount,omitempty"`
	TenureFrequency string  `protobuf:"bytes,4,opt,name=tenure_frequency,proto3" json:"tenure_frequency,omitempty"`
	Tenure          int32   `protobuf:"varint,5,opt,name=tenure,proto3" json:"tenure,omitempty"`
	Checksum        string  `protobuf:"bytes,6,opt,name=checksum,proto3" json:"checksum,omitempty"`
	Urn             string  `protobuf:"bytes,7,opt,name=urn,proto3" json:"urn,omitempty"`
	SchemeCode      string  `protobuf:"bytes,8,opt,name=scheme_code,proto3" json:"scheme_code,omitempty"`
}

func (x *MakeDrawdownRequest) Reset() {
	*x = MakeDrawdownRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MakeDrawdownRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeDrawdownRequest) ProtoMessage() {}

func (x *MakeDrawdownRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeDrawdownRequest.ProtoReflect.Descriptor instead.
func (*MakeDrawdownRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{16}
}

func (x *MakeDrawdownRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *MakeDrawdownRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *MakeDrawdownRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *MakeDrawdownRequest) GetTenureFrequency() string {
	if x != nil {
		return x.TenureFrequency
	}
	return ""
}

func (x *MakeDrawdownRequest) GetTenure() int32 {
	if x != nil {
		return x.Tenure
	}
	return 0
}

func (x *MakeDrawdownRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *MakeDrawdownRequest) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

func (x *MakeDrawdownRequest) GetSchemeCode() string {
	if x != nil {
		return x.SchemeCode
	}
	return ""
}

type MakeDrawdownResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                       `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                     `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *MakeDrawdownResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                      `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                     `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *MakeDrawdownResponse) Reset() {
	*x = MakeDrawdownResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MakeDrawdownResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeDrawdownResponse) ProtoMessage() {}

func (x *MakeDrawdownResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeDrawdownResponse.ProtoReflect.Descriptor instead.
func (*MakeDrawdownResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{17}
}

func (x *MakeDrawdownResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *MakeDrawdownResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *MakeDrawdownResponse) GetData() *MakeDrawdownResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *MakeDrawdownResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *MakeDrawdownResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type GetPdfAgreementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid           string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	ApplicantId   string `protobuf:"bytes,2,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	ApplicationId string `protobuf:"bytes,3,opt,name=application_id,proto3" json:"application_id,omitempty"`
	Checksum      string `protobuf:"bytes,4,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *GetPdfAgreementRequest) Reset() {
	*x = GetPdfAgreementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPdfAgreementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPdfAgreementRequest) ProtoMessage() {}

func (x *GetPdfAgreementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPdfAgreementRequest.ProtoReflect.Descriptor instead.
func (*GetPdfAgreementRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{18}
}

func (x *GetPdfAgreementRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *GetPdfAgreementRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetPdfAgreementRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetPdfAgreementRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type GetPdfAgreementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                          `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                        `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *GetPdfAgreementResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                         `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                        `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *GetPdfAgreementResponse) Reset() {
	*x = GetPdfAgreementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPdfAgreementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPdfAgreementResponse) ProtoMessage() {}

func (x *GetPdfAgreementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPdfAgreementResponse.ProtoReflect.Descriptor instead.
func (*GetPdfAgreementResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{19}
}

func (x *GetPdfAgreementResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *GetPdfAgreementResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetPdfAgreementResponse) GetData() *GetPdfAgreementResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetPdfAgreementResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetPdfAgreementResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type SendBorrowerAgreementOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid           string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	ApplicantId   string `protobuf:"bytes,2,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	ApplicationId string `protobuf:"bytes,3,opt,name=application_id,proto3" json:"application_id,omitempty"`
	Checksum      string `protobuf:"bytes,4,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *SendBorrowerAgreementOtpRequest) Reset() {
	*x = SendBorrowerAgreementOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendBorrowerAgreementOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendBorrowerAgreementOtpRequest) ProtoMessage() {}

func (x *SendBorrowerAgreementOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendBorrowerAgreementOtpRequest.ProtoReflect.Descriptor instead.
func (*SendBorrowerAgreementOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{20}
}

func (x *SendBorrowerAgreementOtpRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *SendBorrowerAgreementOtpRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *SendBorrowerAgreementOtpRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *SendBorrowerAgreementOtpRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type SendBorrowerAgreementOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                                     `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     []*SendBorrowerAgreementOtpResponse_Data `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	Code     int32                                    `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                                   `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *SendBorrowerAgreementOtpResponse) Reset() {
	*x = SendBorrowerAgreementOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendBorrowerAgreementOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendBorrowerAgreementOtpResponse) ProtoMessage() {}

func (x *SendBorrowerAgreementOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendBorrowerAgreementOtpResponse.ProtoReflect.Descriptor instead.
func (*SendBorrowerAgreementOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{21}
}

func (x *SendBorrowerAgreementOtpResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *SendBorrowerAgreementOtpResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SendBorrowerAgreementOtpResponse) GetData() []*SendBorrowerAgreementOtpResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SendBorrowerAgreementOtpResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SendBorrowerAgreementOtpResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type VerifyBorrowerAgreementOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid           string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	ApplicantId   string `protobuf:"bytes,2,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	ApplicationId string `protobuf:"bytes,3,opt,name=application_id,proto3" json:"application_id,omitempty"`
	Checksum      string `protobuf:"bytes,4,opt,name=checksum,proto3" json:"checksum,omitempty"`
	DocId         string `protobuf:"bytes,5,opt,name=doc_id,proto3" json:"doc_id,omitempty"`
	Otp           string `protobuf:"bytes,6,opt,name=otp,proto3" json:"otp,omitempty"`
}

func (x *VerifyBorrowerAgreementOtpRequest) Reset() {
	*x = VerifyBorrowerAgreementOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyBorrowerAgreementOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyBorrowerAgreementOtpRequest) ProtoMessage() {}

func (x *VerifyBorrowerAgreementOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyBorrowerAgreementOtpRequest.ProtoReflect.Descriptor instead.
func (*VerifyBorrowerAgreementOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{22}
}

func (x *VerifyBorrowerAgreementOtpRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *VerifyBorrowerAgreementOtpRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *VerifyBorrowerAgreementOtpRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *VerifyBorrowerAgreementOtpRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *VerifyBorrowerAgreementOtpRequest) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

func (x *VerifyBorrowerAgreementOtpRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

type VerifyBorrowerAgreementOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                                     `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *VerifyBorrowerAgreementOtpResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                                    `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                                   `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *VerifyBorrowerAgreementOtpResponse) Reset() {
	*x = VerifyBorrowerAgreementOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyBorrowerAgreementOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyBorrowerAgreementOtpResponse) ProtoMessage() {}

func (x *VerifyBorrowerAgreementOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyBorrowerAgreementOtpResponse.ProtoReflect.Descriptor instead.
func (*VerifyBorrowerAgreementOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{23}
}

func (x *VerifyBorrowerAgreementOtpResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *VerifyBorrowerAgreementOtpResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *VerifyBorrowerAgreementOtpResponse) GetData() *VerifyBorrowerAgreementOtpResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *VerifyBorrowerAgreementOtpResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VerifyBorrowerAgreementOtpResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type GetLoanStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid           string `protobuf:"bytes,1,opt,name=sid,json=SID,proto3" json:"sid,omitempty"`
	ApplicationId string `protobuf:"bytes,2,opt,name=application_id,proto3" json:"application_id,omitempty"`
	Timestamp     string `protobuf:"bytes,3,opt,name=timestamp,json=Timestamp,proto3" json:"timestamp,omitempty"` //YYYY-MM-DD HH:MM:SS
	Checksum      string `protobuf:"bytes,4,opt,name=checksum,json=Checksum,proto3" json:"checksum,omitempty"`
	Urn           string `protobuf:"bytes,9,opt,name=urn,proto3" json:"urn,omitempty"`
}

func (x *GetLoanStatusRequest) Reset() {
	*x = GetLoanStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanStatusRequest) ProtoMessage() {}

func (x *GetLoanStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanStatusRequest.ProtoReflect.Descriptor instead.
func (*GetLoanStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{24}
}

func (x *GetLoanStatusRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *GetLoanStatusRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetLoanStatusRequest) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *GetLoanStatusRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *GetLoanStatusRequest) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

type GetLoanStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                        `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                      `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *GetLoanStatusResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                       `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                      `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *GetLoanStatusResponse) Reset() {
	*x = GetLoanStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanStatusResponse) ProtoMessage() {}

func (x *GetLoanStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanStatusResponse.ProtoReflect.Descriptor instead.
func (*GetLoanStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{25}
}

func (x *GetLoanStatusResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *GetLoanStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetLoanStatusResponse) GetData() *GetLoanStatusResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetLoanStatusResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetLoanStatusResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type VerifyAndDownloadCkycRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid            string `protobuf:"bytes,1,opt,name=sid,json=SID,proto3" json:"sid,omitempty"`
	ApplicantId    string `protobuf:"bytes,2,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	Checksum       string `protobuf:"bytes,3,opt,name=checksum,json=Checksum,proto3" json:"checksum,omitempty"`
	Timestamp      string `protobuf:"bytes,4,opt,name=timestamp,json=Timestamp,proto3" json:"timestamp,omitempty"` //YYYY-MM-DD HH:MM:SS
	IdType         string `protobuf:"bytes,5,opt,name=id_type,proto3" json:"id_type,omitempty"`
	IdNo           string `protobuf:"bytes,6,opt,name=id_no,proto3" json:"id_no,omitempty"`
	AuthFactorType string `protobuf:"bytes,7,opt,name=auth_factor_type,proto3" json:"auth_factor_type,omitempty"`
	AuthFactor     string `protobuf:"bytes,58,opt,name=auth_factor,proto3" json:"auth_factor,omitempty"`
}

func (x *VerifyAndDownloadCkycRequest) Reset() {
	*x = VerifyAndDownloadCkycRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyAndDownloadCkycRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAndDownloadCkycRequest) ProtoMessage() {}

func (x *VerifyAndDownloadCkycRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAndDownloadCkycRequest.ProtoReflect.Descriptor instead.
func (*VerifyAndDownloadCkycRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{26}
}

func (x *VerifyAndDownloadCkycRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *VerifyAndDownloadCkycRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *VerifyAndDownloadCkycRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *VerifyAndDownloadCkycRequest) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *VerifyAndDownloadCkycRequest) GetIdType() string {
	if x != nil {
		return x.IdType
	}
	return ""
}

func (x *VerifyAndDownloadCkycRequest) GetIdNo() string {
	if x != nil {
		return x.IdNo
	}
	return ""
}

func (x *VerifyAndDownloadCkycRequest) GetAuthFactorType() string {
	if x != nil {
		return x.AuthFactorType
	}
	return ""
}

func (x *VerifyAndDownloadCkycRequest) GetAuthFactor() string {
	if x != nil {
		return x.AuthFactor
	}
	return ""
}

type VerifyAndDownloadCkycResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                                `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                              `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *VerifyAndDownloadCkycResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                               `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                              `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *VerifyAndDownloadCkycResponse) Reset() {
	*x = VerifyAndDownloadCkycResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyAndDownloadCkycResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAndDownloadCkycResponse) ProtoMessage() {}

func (x *VerifyAndDownloadCkycResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAndDownloadCkycResponse.ProtoReflect.Descriptor instead.
func (*VerifyAndDownloadCkycResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{27}
}

func (x *VerifyAndDownloadCkycResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *VerifyAndDownloadCkycResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse) GetData() *VerifyAndDownloadCkycResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *VerifyAndDownloadCkycResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VerifyAndDownloadCkycResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

// used for v2 RepaymentDetails API
// api/apiintegration/v2/GetRepaymentDetails
type GetRepaymentScheduleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid      string `protobuf:"bytes,1,opt,name=sid,json=SID,proto3" json:"sid,omitempty"`
	LoanId   string `protobuf:"bytes,2,opt,name=loan_id,proto3" json:"loan_id,omitempty"`
	Checksum string `protobuf:"bytes,3,opt,name=checksum,json=Checksum,proto3" json:"checksum,omitempty"`
}

func (x *GetRepaymentScheduleRequest) Reset() {
	*x = GetRepaymentScheduleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleRequest) ProtoMessage() {}

func (x *GetRepaymentScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleRequest.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{28}
}

func (x *GetRepaymentScheduleRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *GetRepaymentScheduleRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *GetRepaymentScheduleRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

// used for v4 RepaymentDetails API
// api/apiintegration/v4/GetRepaymentDetails
type GetRepaymentScheduleRequestV4 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid      string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	LoanId   string `protobuf:"bytes,2,opt,name=loan_id,json=application_id,proto3" json:"loan_id,omitempty"`
	Checksum string `protobuf:"bytes,3,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *GetRepaymentScheduleRequestV4) Reset() {
	*x = GetRepaymentScheduleRequestV4{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleRequestV4) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleRequestV4) ProtoMessage() {}

func (x *GetRepaymentScheduleRequestV4) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleRequestV4.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleRequestV4) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{29}
}

func (x *GetRepaymentScheduleRequestV4) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *GetRepaymentScheduleRequestV4) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *GetRepaymentScheduleRequestV4) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

// used for both v2 and v4 RepaymentDetails APIs
// 1. api/apiintegration/v2/GetRepaymentDetails
// 2. api/apiintegration/v4/GetRepaymentDetails
type GetRepaymentScheduleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  bool                               `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message string                             `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *GetRepaymentScheduleResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetRepaymentScheduleResponse) Reset() {
	*x = GetRepaymentScheduleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse) ProtoMessage() {}

func (x *GetRepaymentScheduleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{30}
}

func (x *GetRepaymentScheduleResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *GetRepaymentScheduleResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetRepaymentScheduleResponse) GetData() *GetRepaymentScheduleResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

// Example JSON:
//
//	{
//	  "application_id": "272847",
//	  "installment_number": 2,
//	  "due_date": "2024-01-05",
//	  "due_amount": 424,
//	  "principal_amount": 178,
//	  "interest_amount": 246,
//	  "lpi": 6.02,
//	  "other_charges": 0,
//	  "bounce_charges": 0,
//	  "waived_charges": 0,
//	  "payment_status": "Unpaid",
//	  "received_date": null,
//	  "received_amount": 0,
//	  "paid_principal_amount": 0,
//	  "paid_interest_amount": 0,
//	  "paid_lpi": 0,
//	  "paid_other_charges": 0,
//	  "paid_bounce_charges": 0,
//	  "post_payment_principal_outstanding": 0,
//	  "post_payment_interest_outstanding": 0,
//	  "post_payment_charges_outstanding": 6.02
//	}
type Schedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId                   int64   `protobuf:"varint,1,opt,name=application_id,proto3" json:"application_id,omitempty"`
	InstallmentNumber               int32   `protobuf:"varint,2,opt,name=installment_number,proto3" json:"installment_number,omitempty"`
	DueDate                         string  `protobuf:"bytes,3,opt,name=due_date,proto3" json:"due_date,omitempty"` //YYYY-MM-DD
	DueAmount                       float64 `protobuf:"fixed64,4,opt,name=due_amount,proto3" json:"due_amount,omitempty"`
	PrincipalAmount                 float64 `protobuf:"fixed64,5,opt,name=principal_amount,proto3" json:"principal_amount,omitempty"`
	InterestAmount                  float64 `protobuf:"fixed64,6,opt,name=interest_amount,proto3" json:"interest_amount,omitempty"`
	PaymentStatus                   string  `protobuf:"bytes,7,opt,name=payment_status,proto3" json:"payment_status,omitempty"`
	ReceivedDate                    string  `protobuf:"bytes,8,opt,name=received_date,proto3" json:"received_date,omitempty"`
	ReceivedAmount                  float64 `protobuf:"fixed64,9,opt,name=received_amount,proto3" json:"received_amount,omitempty"`
	PaidPrincipalAmount             float64 `protobuf:"fixed64,10,opt,name=paid_principal_amount,proto3" json:"paid_principal_amount,omitempty"`
	PaidInterestAmount              float64 `protobuf:"fixed64,11,opt,name=paid_interest_amount,proto3" json:"paid_interest_amount,omitempty"`
	Lpi                             float64 `protobuf:"fixed64,12,opt,name=lpi,proto3" json:"lpi,omitempty"`
	OtherCharges                    float64 `protobuf:"fixed64,13,opt,name=other_charges,proto3" json:"other_charges,omitempty"`
	BounceCharges                   float64 `protobuf:"fixed64,14,opt,name=bounce_charges,proto3" json:"bounce_charges,omitempty"`
	PostPaymentPrincipalOutstanding float64 `protobuf:"fixed64,15,opt,name=post_payment_principal_outstanding,proto3" json:"post_payment_principal_outstanding,omitempty"`
	PostPaymentInterestOutstanding  float64 `protobuf:"fixed64,16,opt,name=post_payment_interest_outstanding,proto3" json:"post_payment_interest_outstanding,omitempty"`
	// populated only in v4
	WaivedCharges float64 `protobuf:"fixed64,17,opt,name=waived_charges,proto3" json:"waived_charges,omitempty"`
	// populated only in v4
	PaidLpi float64 `protobuf:"fixed64,18,opt,name=paid_lpi,proto3" json:"paid_lpi,omitempty"`
	// populated only in v4
	PaidOtherCharges float64 `protobuf:"fixed64,19,opt,name=paid_other_charges,proto3" json:"paid_other_charges,omitempty"`
	// populated only in v4
	PaidBounceCharges float64 `protobuf:"fixed64,20,opt,name=paid_bounce_charges,proto3" json:"paid_bounce_charges,omitempty"`
	// populated only in v4
	PostPaymentChargesOutstanding float64 `protobuf:"fixed64,21,opt,name=post_payment_charges_outstanding,proto3" json:"post_payment_charges_outstanding,omitempty"`
}

func (x *Schedule) Reset() {
	*x = Schedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Schedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Schedule) ProtoMessage() {}

func (x *Schedule) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Schedule.ProtoReflect.Descriptor instead.
func (*Schedule) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{31}
}

func (x *Schedule) GetApplicationId() int64 {
	if x != nil {
		return x.ApplicationId
	}
	return 0
}

func (x *Schedule) GetInstallmentNumber() int32 {
	if x != nil {
		return x.InstallmentNumber
	}
	return 0
}

func (x *Schedule) GetDueDate() string {
	if x != nil {
		return x.DueDate
	}
	return ""
}

func (x *Schedule) GetDueAmount() float64 {
	if x != nil {
		return x.DueAmount
	}
	return 0
}

func (x *Schedule) GetPrincipalAmount() float64 {
	if x != nil {
		return x.PrincipalAmount
	}
	return 0
}

func (x *Schedule) GetInterestAmount() float64 {
	if x != nil {
		return x.InterestAmount
	}
	return 0
}

func (x *Schedule) GetPaymentStatus() string {
	if x != nil {
		return x.PaymentStatus
	}
	return ""
}

func (x *Schedule) GetReceivedDate() string {
	if x != nil {
		return x.ReceivedDate
	}
	return ""
}

func (x *Schedule) GetReceivedAmount() float64 {
	if x != nil {
		return x.ReceivedAmount
	}
	return 0
}

func (x *Schedule) GetPaidPrincipalAmount() float64 {
	if x != nil {
		return x.PaidPrincipalAmount
	}
	return 0
}

func (x *Schedule) GetPaidInterestAmount() float64 {
	if x != nil {
		return x.PaidInterestAmount
	}
	return 0
}

func (x *Schedule) GetLpi() float64 {
	if x != nil {
		return x.Lpi
	}
	return 0
}

func (x *Schedule) GetOtherCharges() float64 {
	if x != nil {
		return x.OtherCharges
	}
	return 0
}

func (x *Schedule) GetBounceCharges() float64 {
	if x != nil {
		return x.BounceCharges
	}
	return 0
}

func (x *Schedule) GetPostPaymentPrincipalOutstanding() float64 {
	if x != nil {
		return x.PostPaymentPrincipalOutstanding
	}
	return 0
}

func (x *Schedule) GetPostPaymentInterestOutstanding() float64 {
	if x != nil {
		return x.PostPaymentInterestOutstanding
	}
	return 0
}

func (x *Schedule) GetWaivedCharges() float64 {
	if x != nil {
		return x.WaivedCharges
	}
	return 0
}

func (x *Schedule) GetPaidLpi() float64 {
	if x != nil {
		return x.PaidLpi
	}
	return 0
}

func (x *Schedule) GetPaidOtherCharges() float64 {
	if x != nil {
		return x.PaidOtherCharges
	}
	return 0
}

func (x *Schedule) GetPaidBounceCharges() float64 {
	if x != nil {
		return x.PaidBounceCharges
	}
	return 0
}

func (x *Schedule) GetPostPaymentChargesOutstanding() float64 {
	if x != nil {
		return x.PostPaymentChargesOutstanding
	}
	return 0
}

type GetRepaymentScheduleErrorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  bool                                      `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message string                                    `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    []*GetRepaymentScheduleErrorResponse_Data `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GetRepaymentScheduleErrorResponse) Reset() {
	*x = GetRepaymentScheduleErrorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleErrorResponse) ProtoMessage() {}

func (x *GetRepaymentScheduleErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleErrorResponse.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleErrorResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{32}
}

func (x *GetRepaymentScheduleErrorResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *GetRepaymentScheduleErrorResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetRepaymentScheduleErrorResponse) GetData() []*GetRepaymentScheduleErrorResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type ErrorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     []*ErrorResponse_Data `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	Code     int32                 `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *ErrorResponse) Reset() {
	*x = ErrorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorResponse) ProtoMessage() {}

func (x *ErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorResponse.ProtoReflect.Descriptor instead.
func (*ErrorResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{33}
}

func (x *ErrorResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *ErrorResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ErrorResponse) GetData() []*ErrorResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ErrorResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ErrorResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type UploadDocumentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// epifi credentials provided by liquiloans
	Sid string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	// Application id/ loan id we got from vendor post drawdown
	ApplicationId string `protobuf:"bytes,2,opt,name=application_id,json=Applicationid,proto3" json:"application_id,omitempty"`
	// document type to be uploaded
	DocumentType string `protobuf:"bytes,3,opt,name=document_type,json=DocumentType,proto3" json:"document_type,omitempty"`
	// extra info that can be sent, Max size 255 chars
	Remarks string `protobuf:"bytes,4,opt,name=remarks,json=Remarks,proto3" json:"remarks,omitempty"`
	// hmac hash checksum of the request
	Checksum string `protobuf:"bytes,5,opt,name=checksum,json=Checksum,proto3" json:"checksum,omitempty"`
	// binary data of the file to be uploaded
	File []byte `protobuf:"bytes,6,opt,name=file,json=Files[],proto3" json:"file,omitempty"`
}

func (x *UploadDocumentRequest) Reset() {
	*x = UploadDocumentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadDocumentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadDocumentRequest) ProtoMessage() {}

func (x *UploadDocumentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadDocumentRequest.ProtoReflect.Descriptor instead.
func (*UploadDocumentRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{34}
}

func (x *UploadDocumentRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *UploadDocumentRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *UploadDocumentRequest) GetDocumentType() string {
	if x != nil {
		return x.DocumentType
	}
	return ""
}

func (x *UploadDocumentRequest) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *UploadDocumentRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *UploadDocumentRequest) GetFile() []byte {
	if x != nil {
		return x.File
	}
	return nil
}

type UploadDocumentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                         `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                       `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *UploadDocumentResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                        `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                       `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *UploadDocumentResponse) Reset() {
	*x = UploadDocumentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadDocumentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadDocumentResponse) ProtoMessage() {}

func (x *UploadDocumentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadDocumentResponse.ProtoReflect.Descriptor instead.
func (*UploadDocumentResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{35}
}

func (x *UploadDocumentResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *UploadDocumentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UploadDocumentResponse) GetData() *UploadDocumentResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UploadDocumentResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UploadDocumentResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type SaveCollectionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid             string                                   `protobuf:"bytes,1,opt,name=sid,json=SID,proto3" json:"sid,omitempty"`
	LoanId          string                                   `protobuf:"bytes,2,opt,name=loan_id,proto3" json:"loan_id,omitempty"`
	Checksum        string                                   `protobuf:"bytes,3,opt,name=checksum,json=Checksum,proto3" json:"checksum,omitempty"`
	PaymentSchedule []*SaveCollectionRequest_PaymentSchedule `protobuf:"bytes,4,rep,name=payment_schedule,proto3" json:"payment_schedule,omitempty"`
}

func (x *SaveCollectionRequest) Reset() {
	*x = SaveCollectionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveCollectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCollectionRequest) ProtoMessage() {}

func (x *SaveCollectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCollectionRequest.ProtoReflect.Descriptor instead.
func (*SaveCollectionRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{36}
}

func (x *SaveCollectionRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *SaveCollectionRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *SaveCollectionRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *SaveCollectionRequest) GetPaymentSchedule() []*SaveCollectionRequest_PaymentSchedule {
	if x != nil {
		return x.PaymentSchedule
	}
	return nil
}

type SaveCollectionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                         `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                       `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *SaveCollectionResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                        `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                       `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *SaveCollectionResponse) Reset() {
	*x = SaveCollectionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveCollectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCollectionResponse) ProtoMessage() {}

func (x *SaveCollectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCollectionResponse.ProtoReflect.Descriptor instead.
func (*SaveCollectionResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{37}
}

func (x *SaveCollectionResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *SaveCollectionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SaveCollectionResponse) GetData() *SaveCollectionResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SaveCollectionResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SaveCollectionResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type HashGenerationForOkycRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid           string `protobuf:"bytes,1,opt,name=sid,json=SID,proto3" json:"sid,omitempty"`
	ApplicationId int64  `protobuf:"varint,2,opt,name=application_id,proto3" json:"application_id,omitempty"`
	Checksum      string `protobuf:"bytes,3,opt,name=checksum,json=Checksum,proto3" json:"checksum,omitempty"`
}

func (x *HashGenerationForOkycRequest) Reset() {
	*x = HashGenerationForOkycRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HashGenerationForOkycRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HashGenerationForOkycRequest) ProtoMessage() {}

func (x *HashGenerationForOkycRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HashGenerationForOkycRequest.ProtoReflect.Descriptor instead.
func (*HashGenerationForOkycRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{38}
}

func (x *HashGenerationForOkycRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *HashGenerationForOkycRequest) GetApplicationId() int64 {
	if x != nil {
		return x.ApplicationId
	}
	return 0
}

func (x *HashGenerationForOkycRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type HashGenerationForOkycResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                                `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                              `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *HashGenerationForOkycResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                               `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                              `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *HashGenerationForOkycResponse) Reset() {
	*x = HashGenerationForOkycResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HashGenerationForOkycResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HashGenerationForOkycResponse) ProtoMessage() {}

func (x *HashGenerationForOkycResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HashGenerationForOkycResponse.ProtoReflect.Descriptor instead.
func (*HashGenerationForOkycResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{39}
}

func (x *HashGenerationForOkycResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *HashGenerationForOkycResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *HashGenerationForOkycResponse) GetData() *HashGenerationForOkycResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *HashGenerationForOkycResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HashGenerationForOkycResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type CaptchaGenerationForOkycRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid           string `protobuf:"bytes,1,opt,name=sid,json=SID,proto3" json:"sid,omitempty"`
	ApplicationId int64  `protobuf:"varint,2,opt,name=application_id,proto3" json:"application_id,omitempty"`
	Hash          string `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	Checksum      string `protobuf:"bytes,4,opt,name=checksum,json=Checksum,proto3" json:"checksum,omitempty"`
}

func (x *CaptchaGenerationForOkycRequest) Reset() {
	*x = CaptchaGenerationForOkycRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaptchaGenerationForOkycRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaptchaGenerationForOkycRequest) ProtoMessage() {}

func (x *CaptchaGenerationForOkycRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaptchaGenerationForOkycRequest.ProtoReflect.Descriptor instead.
func (*CaptchaGenerationForOkycRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{40}
}

func (x *CaptchaGenerationForOkycRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *CaptchaGenerationForOkycRequest) GetApplicationId() int64 {
	if x != nil {
		return x.ApplicationId
	}
	return 0
}

func (x *CaptchaGenerationForOkycRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *CaptchaGenerationForOkycRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type CaptchaGenerationForOkycResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *CaptchaGenerationForOkycResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                                  `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                                 `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *CaptchaGenerationForOkycResponse) Reset() {
	*x = CaptchaGenerationForOkycResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaptchaGenerationForOkycResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaptchaGenerationForOkycResponse) ProtoMessage() {}

func (x *CaptchaGenerationForOkycResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaptchaGenerationForOkycResponse.ProtoReflect.Descriptor instead.
func (*CaptchaGenerationForOkycResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{41}
}

func (x *CaptchaGenerationForOkycResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *CaptchaGenerationForOkycResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CaptchaGenerationForOkycResponse) GetData() *CaptchaGenerationForOkycResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *CaptchaGenerationForOkycResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CaptchaGenerationForOkycResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type GenerateOtpForOkycRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid           string `protobuf:"bytes,1,opt,name=sid,json=SID,proto3" json:"sid,omitempty"`
	ApplicationId int64  `protobuf:"varint,2,opt,name=application_id,proto3" json:"application_id,omitempty"`
	Hash          string `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	UidNo         int64  `protobuf:"varint,4,opt,name=uid_no,proto3" json:"uid_no,omitempty"`
	CaptchaCode   string `protobuf:"bytes,5,opt,name=captcha_code,proto3" json:"captcha_code,omitempty"`
	RequestToken  string `protobuf:"bytes,6,opt,name=request_token,proto3" json:"request_token,omitempty"`
	CaptchaTxnId  string `protobuf:"bytes,7,opt,name=captcha_txn_id,proto3" json:"captcha_txn_id,omitempty"`
	Checksum      string `protobuf:"bytes,8,opt,name=checksum,json=Checksum,proto3" json:"checksum,omitempty"`
}

func (x *GenerateOtpForOkycRequest) Reset() {
	*x = GenerateOtpForOkycRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateOtpForOkycRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateOtpForOkycRequest) ProtoMessage() {}

func (x *GenerateOtpForOkycRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateOtpForOkycRequest.ProtoReflect.Descriptor instead.
func (*GenerateOtpForOkycRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{42}
}

func (x *GenerateOtpForOkycRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *GenerateOtpForOkycRequest) GetApplicationId() int64 {
	if x != nil {
		return x.ApplicationId
	}
	return 0
}

func (x *GenerateOtpForOkycRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *GenerateOtpForOkycRequest) GetUidNo() int64 {
	if x != nil {
		return x.UidNo
	}
	return 0
}

func (x *GenerateOtpForOkycRequest) GetCaptchaCode() string {
	if x != nil {
		return x.CaptchaCode
	}
	return ""
}

func (x *GenerateOtpForOkycRequest) GetRequestToken() string {
	if x != nil {
		return x.RequestToken
	}
	return ""
}

func (x *GenerateOtpForOkycRequest) GetCaptchaTxnId() string {
	if x != nil {
		return x.CaptchaTxnId
	}
	return ""
}

func (x *GenerateOtpForOkycRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type GenerateOtpForOkycResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                             `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                           `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *GenerateOtpForOkycResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                            `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                           `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *GenerateOtpForOkycResponse) Reset() {
	*x = GenerateOtpForOkycResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateOtpForOkycResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateOtpForOkycResponse) ProtoMessage() {}

func (x *GenerateOtpForOkycResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateOtpForOkycResponse.ProtoReflect.Descriptor instead.
func (*GenerateOtpForOkycResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{43}
}

func (x *GenerateOtpForOkycResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *GenerateOtpForOkycResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GenerateOtpForOkycResponse) GetData() *GenerateOtpForOkycResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GenerateOtpForOkycResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GenerateOtpForOkycResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type ValidateOtpForOkycRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid           string `protobuf:"bytes,1,opt,name=sid,json=SID,proto3" json:"sid,omitempty"`
	ApplicationId int64  `protobuf:"varint,2,opt,name=application_id,proto3" json:"application_id,omitempty"`
	Otp           int64  `protobuf:"varint,4,opt,name=otp,proto3" json:"otp,omitempty"`
	Hash          string `protobuf:"bytes,5,opt,name=hash,proto3" json:"hash,omitempty"`
	RequestToken  string `protobuf:"bytes,6,opt,name=request_token,proto3" json:"request_token,omitempty"`
	Checksum      string `protobuf:"bytes,7,opt,name=checksum,json=Checksum,proto3" json:"checksum,omitempty"`
}

func (x *ValidateOtpForOkycRequest) Reset() {
	*x = ValidateOtpForOkycRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateOtpForOkycRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateOtpForOkycRequest) ProtoMessage() {}

func (x *ValidateOtpForOkycRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateOtpForOkycRequest.ProtoReflect.Descriptor instead.
func (*ValidateOtpForOkycRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{44}
}

func (x *ValidateOtpForOkycRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *ValidateOtpForOkycRequest) GetApplicationId() int64 {
	if x != nil {
		return x.ApplicationId
	}
	return 0
}

func (x *ValidateOtpForOkycRequest) GetOtp() int64 {
	if x != nil {
		return x.Otp
	}
	return 0
}

func (x *ValidateOtpForOkycRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *ValidateOtpForOkycRequest) GetRequestToken() string {
	if x != nil {
		return x.RequestToken
	}
	return ""
}

func (x *ValidateOtpForOkycRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type ValidateOtpForOkycResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                               `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                             `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     []*ValidateOtpForOkycResponse_Data `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	Code     int32                              `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                             `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *ValidateOtpForOkycResponse) Reset() {
	*x = ValidateOtpForOkycResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateOtpForOkycResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateOtpForOkycResponse) ProtoMessage() {}

func (x *ValidateOtpForOkycResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateOtpForOkycResponse.ProtoReflect.Descriptor instead.
func (*ValidateOtpForOkycResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{45}
}

func (x *ValidateOtpForOkycResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *ValidateOtpForOkycResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ValidateOtpForOkycResponse) GetData() []*ValidateOtpForOkycResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ValidateOtpForOkycResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ValidateOtpForOkycResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

// vendor doc: https://drive.google.com/file/d/1iZTrS7lPkIuKYs-_voeoHjF6c2ghJ4qv/view?usp=drive_link
type UpdateLeadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid           string `protobuf:"bytes,1,opt,name=sid,json=SID,proto3" json:"sid,omitempty"`
	Checksum      string `protobuf:"bytes,2,opt,name=checksum,json=Checksum,proto3" json:"checksum,omitempty"`
	Urn           string `protobuf:"bytes,3,opt,name=urn,proto3" json:"urn,omitempty"`
	ApplicationId int64  `protobuf:"varint,4,opt,name=application_id,proto3" json:"application_id,omitempty"`
	// Loan amount
	Amount        float64                          `protobuf:"fixed64,5,opt,name=amount,proto3" json:"amount,omitempty"`
	SchemeDetails *UpdateLeadRequest_SchemeDetails `protobuf:"bytes,6,opt,name=scheme_details,proto3" json:"scheme_details,omitempty"`
}

func (x *UpdateLeadRequest) Reset() {
	*x = UpdateLeadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLeadRequest) ProtoMessage() {}

func (x *UpdateLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLeadRequest.ProtoReflect.Descriptor instead.
func (*UpdateLeadRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{46}
}

func (x *UpdateLeadRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *UpdateLeadRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *UpdateLeadRequest) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

func (x *UpdateLeadRequest) GetApplicationId() int64 {
	if x != nil {
		return x.ApplicationId
	}
	return 0
}

func (x *UpdateLeadRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *UpdateLeadRequest) GetSchemeDetails() *UpdateLeadRequest_SchemeDetails {
	if x != nil {
		return x.SchemeDetails
	}
	return nil
}

type UpdateLeadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                     `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *UpdateLeadResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int32                    `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                   `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *UpdateLeadResponse) Reset() {
	*x = UpdateLeadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLeadResponse) ProtoMessage() {}

func (x *UpdateLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLeadResponse.ProtoReflect.Descriptor instead.
func (*UpdateLeadResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{47}
}

func (x *UpdateLeadResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *UpdateLeadResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UpdateLeadResponse) GetData() *UpdateLeadResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateLeadResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateLeadResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type CancelLeadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid           string `protobuf:"bytes,1,opt,name=sid,json=SID,proto3" json:"sid,omitempty"`
	ApplicationId int64  `protobuf:"varint,2,opt,name=application_id,json=ApplicationId,proto3" json:"application_id,omitempty"`
	ApplicantId   int64  `protobuf:"varint,3,opt,name=applicant_id,json=ApplicantId,proto3" json:"applicant_id,omitempty"`
	Checksum      string `protobuf:"bytes,4,opt,name=checksum,json=Checksum,proto3" json:"checksum,omitempty"`
	Timestamp     string `protobuf:"bytes,5,opt,name=timestamp,json=Timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *CancelLeadRequest) Reset() {
	*x = CancelLeadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelLeadRequest) ProtoMessage() {}

func (x *CancelLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelLeadRequest.ProtoReflect.Descriptor instead.
func (*CancelLeadRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{48}
}

func (x *CancelLeadRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *CancelLeadRequest) GetApplicationId() int64 {
	if x != nil {
		return x.ApplicationId
	}
	return 0
}

func (x *CancelLeadRequest) GetApplicantId() int64 {
	if x != nil {
		return x.ApplicantId
	}
	return 0
}

func (x *CancelLeadRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *CancelLeadRequest) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

type CancelLeadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                     `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *CancelLeadResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     string                   `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                   `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *CancelLeadResponse) Reset() {
	*x = CancelLeadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelLeadResponse) ProtoMessage() {}

func (x *CancelLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelLeadResponse.ProtoReflect.Descriptor instead.
func (*CancelLeadResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{49}
}

func (x *CancelLeadResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *CancelLeadResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CancelLeadResponse) GetData() *CancelLeadResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *CancelLeadResponse) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CancelLeadResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type CancelLeadErrorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Code     int64  `protobuf:"varint,3,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string `protobuf:"bytes,4,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *CancelLeadErrorResponse) Reset() {
	*x = CancelLeadErrorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelLeadErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelLeadErrorResponse) ProtoMessage() {}

func (x *CancelLeadErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelLeadErrorResponse.ProtoReflect.Descriptor instead.
func (*CancelLeadErrorResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{50}
}

func (x *CancelLeadErrorResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *CancelLeadErrorResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CancelLeadErrorResponse) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CancelLeadErrorResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

type ForeClosureDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid           string `protobuf:"bytes,1,opt,name=sid,json=SID,proto3" json:"sid,omitempty"`
	ApplicationId int64  `protobuf:"varint,2,opt,name=application_id,proto3" json:"application_id,omitempty"`
	Checksum      string `protobuf:"bytes,4,opt,name=checksum,json=Checksum,proto3" json:"checksum,omitempty"`
	Timestamp     string `protobuf:"bytes,5,opt,name=timestamp,json=Timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *ForeClosureDetailsRequest) Reset() {
	*x = ForeClosureDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeClosureDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeClosureDetailsRequest) ProtoMessage() {}

func (x *ForeClosureDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeClosureDetailsRequest.ProtoReflect.Descriptor instead.
func (*ForeClosureDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{51}
}

func (x *ForeClosureDetailsRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *ForeClosureDetailsRequest) GetApplicationId() int64 {
	if x != nil {
		return x.ApplicationId
	}
	return 0
}

func (x *ForeClosureDetailsRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *ForeClosureDetailsRequest) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

type ForeClosureDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                             `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                           `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data     *ForeClosureDetailsResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Code     int64                            `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                           `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *ForeClosureDetailsResponse) Reset() {
	*x = ForeClosureDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeClosureDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeClosureDetailsResponse) ProtoMessage() {}

func (x *ForeClosureDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeClosureDetailsResponse.ProtoReflect.Descriptor instead.
func (*ForeClosureDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{52}
}

func (x *ForeClosureDetailsResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *ForeClosureDetailsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ForeClosureDetailsResponse) GetData() *ForeClosureDetailsResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ForeClosureDetailsResponse) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ForeClosureDetailsResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

// https://drive.google.com/file/d/1V0NVAo0hJMUvlEAQPlm-Yc0xPmNbz2uJ/view?usp=drive_link
type UpdateApplicantUdfRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid         string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	ApplicantId int64  `protobuf:"varint,2,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	Checksum    string `protobuf:"bytes,3,opt,name=checksum,proto3" json:"checksum,omitempty"`
	Udf8        string `protobuf:"bytes,4,opt,name=udf8,json=UDF8,proto3" json:"udf8,omitempty"`
	Udf9        string `protobuf:"bytes,5,opt,name=udf9,json=UDF9,proto3" json:"udf9,omitempty"`
	// monthly income
	Udf4 string `protobuf:"bytes,6,opt,name=udf4,json=UDF4,proto3" json:"udf4,omitempty"`
	// income data source - AA (account aggregator)
	Udf5 string `protobuf:"bytes,7,opt,name=udf5,json=UDF5,proto3" json:"udf5,omitempty"`
}

func (x *UpdateApplicantUdfRequest) Reset() {
	*x = UpdateApplicantUdfRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateApplicantUdfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateApplicantUdfRequest) ProtoMessage() {}

func (x *UpdateApplicantUdfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateApplicantUdfRequest.ProtoReflect.Descriptor instead.
func (*UpdateApplicantUdfRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{53}
}

func (x *UpdateApplicantUdfRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *UpdateApplicantUdfRequest) GetApplicantId() int64 {
	if x != nil {
		return x.ApplicantId
	}
	return 0
}

func (x *UpdateApplicantUdfRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *UpdateApplicantUdfRequest) GetUdf8() string {
	if x != nil {
		return x.Udf8
	}
	return ""
}

func (x *UpdateApplicantUdfRequest) GetUdf9() string {
	if x != nil {
		return x.Udf9
	}
	return ""
}

func (x *UpdateApplicantUdfRequest) GetUdf4() string {
	if x != nil {
		return x.Udf4
	}
	return ""
}

func (x *UpdateApplicantUdfRequest) GetUdf5() string {
	if x != nil {
		return x.Udf5
	}
	return ""
}

type UpdateApplicantUdfResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Code     int64  `protobuf:"varint,3,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string `protobuf:"bytes,4,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *UpdateApplicantUdfResponse) Reset() {
	*x = UpdateApplicantUdfResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateApplicantUdfResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateApplicantUdfResponse) ProtoMessage() {}

func (x *UpdateApplicantUdfResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateApplicantUdfResponse.ProtoReflect.Descriptor instead.
func (*UpdateApplicantUdfResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{54}
}

func (x *UpdateApplicantUdfResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *UpdateApplicantUdfResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UpdateApplicantUdfResponse) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateApplicantUdfResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

// https://drive.google.com/file/d/1qqavHt_DkSrdG4-VbvfnX9yUnVkq4qLU/view?usp=drive_link
type CreateRepaymentScheduleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanId int64  `protobuf:"varint,1,opt,name=loan_id,proto3" json:"loan_id,omitempty"`
	Sid    string `protobuf:"bytes,2,opt,name=sid,json=SID,proto3" json:"sid,omitempty"`
	// possible values: Daily/Weekly/Monthly
	RepaymentFrequency string `protobuf:"bytes,3,opt,name=repayment_frequency,proto3" json:"repayment_frequency,omitempty"`
	EmiTenure          int32  `protobuf:"varint,4,opt,name=emi_tenure,proto3" json:"emi_tenure,omitempty"`
	// checksum logic
	// sid||emi_start_date||emi_tenure||loan_id||EMI1 date||EMI1 interest||EMI1 other_charges||EMI1 principle||EMI1 total_amount||EMI2 date||EMI2 interest||EMI2 other_charges||EMI2 principle||EMI2 total_amount...||repayment_frequency
	Checksum string `protobuf:"bytes,5,opt,name=checksum,json=Checksum,proto3" json:"checksum,omitempty"`
	// YYYY-MM-DD
	EmiStartDate string `protobuf:"bytes,6,opt,name=emi_start_date,proto3" json:"emi_start_date,omitempty"`
	// represents the repayment schedule with due date and due amount along with other fields
	// emi_tenure and length of payment_schedules should match
	PaymentSchedules []*CreateRepaymentScheduleRequest_Schedule `protobuf:"bytes,7,rep,name=payment_schedules,json=payment_schedule,proto3" json:"payment_schedules,omitempty"`
}

func (x *CreateRepaymentScheduleRequest) Reset() {
	*x = CreateRepaymentScheduleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRepaymentScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRepaymentScheduleRequest) ProtoMessage() {}

func (x *CreateRepaymentScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRepaymentScheduleRequest.ProtoReflect.Descriptor instead.
func (*CreateRepaymentScheduleRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{55}
}

func (x *CreateRepaymentScheduleRequest) GetLoanId() int64 {
	if x != nil {
		return x.LoanId
	}
	return 0
}

func (x *CreateRepaymentScheduleRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *CreateRepaymentScheduleRequest) GetRepaymentFrequency() string {
	if x != nil {
		return x.RepaymentFrequency
	}
	return ""
}

func (x *CreateRepaymentScheduleRequest) GetEmiTenure() int32 {
	if x != nil {
		return x.EmiTenure
	}
	return 0
}

func (x *CreateRepaymentScheduleRequest) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *CreateRepaymentScheduleRequest) GetEmiStartDate() string {
	if x != nil {
		return x.EmiStartDate
	}
	return ""
}

func (x *CreateRepaymentScheduleRequest) GetPaymentSchedules() []*CreateRepaymentScheduleRequest_Schedule {
	if x != nil {
		return x.PaymentSchedules
	}
	return nil
}

// Errors:
// If schedule is already created - http status - 400
//
//	{
//	  "status": false,
//	    "message": "Payment schedule is already available",
//	    "data": {
//	    "loan_id": "309392"
//	     },
//	    "code": 400,
//	    "checksum": null
//	}
//
// If loan is already created - http status - 400
//
//	{
//	  "status": false,
//	    "message": "Cannot add repayment schedule for a loan that has already been disbursed",
//	    "data": {},
//	  "code": 400,
//	  "checksum": null
//	}
type CreateRepaymentScheduleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   bool                                  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message  string                                `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Code     int64                                 `protobuf:"varint,3,opt,name=code,proto3" json:"code,omitempty"`
	Checksum string                                `protobuf:"bytes,4,opt,name=checksum,proto3" json:"checksum,omitempty"`
	Data     *CreateRepaymentScheduleResponse_Data `protobuf:"bytes,5,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CreateRepaymentScheduleResponse) Reset() {
	*x = CreateRepaymentScheduleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRepaymentScheduleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRepaymentScheduleResponse) ProtoMessage() {}

func (x *CreateRepaymentScheduleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRepaymentScheduleResponse.ProtoReflect.Descriptor instead.
func (*CreateRepaymentScheduleResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{56}
}

func (x *CreateRepaymentScheduleResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *CreateRepaymentScheduleResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateRepaymentScheduleResponse) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateRepaymentScheduleResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *CreateRepaymentScheduleResponse) GetData() *CreateRepaymentScheduleResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetCreditLineDetailsResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditLineDetails *GetCreditLineDetailsResponse_Data_CreditLineDetails   `protobuf:"bytes,1,opt,name=credit_line_details,proto3" json:"credit_line_details,omitempty"`
	CreditLineSchemes []*GetCreditLineDetailsResponse_Data_CreditLineSchemes `protobuf:"bytes,2,rep,name=credit_line_schemes,proto3" json:"credit_line_schemes,omitempty"`
}

func (x *GetCreditLineDetailsResponse_Data) Reset() {
	*x = GetCreditLineDetailsResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditLineDetailsResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditLineDetailsResponse_Data) ProtoMessage() {}

func (x *GetCreditLineDetailsResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditLineDetailsResponse_Data.ProtoReflect.Descriptor instead.
func (*GetCreditLineDetailsResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetCreditLineDetailsResponse_Data) GetCreditLineDetails() *GetCreditLineDetailsResponse_Data_CreditLineDetails {
	if x != nil {
		return x.CreditLineDetails
	}
	return nil
}

func (x *GetCreditLineDetailsResponse_Data) GetCreditLineSchemes() []*GetCreditLineDetailsResponse_Data_CreditLineSchemes {
	if x != nil {
		return x.CreditLineSchemes
	}
	return nil
}

type GetCreditLineDetailsResponse_Data_CreditLineDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantId    string  `protobuf:"bytes,1,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	UpperLimit     float64 `protobuf:"fixed64,2,opt,name=upper_limit,proto3" json:"upper_limit,omitempty"`
	AvailableLimit float64 `protobuf:"fixed64,3,opt,name=available_limit,proto3" json:"available_limit,omitempty"`
	BlockLimit     float64 `protobuf:"fixed64,4,opt,name=block_limit,proto3" json:"block_limit,omitempty"`
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineDetails) Reset() {
	*x = GetCreditLineDetailsResponse_Data_CreditLineDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditLineDetailsResponse_Data_CreditLineDetails) ProtoMessage() {}

func (x *GetCreditLineDetailsResponse_Data_CreditLineDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditLineDetailsResponse_Data_CreditLineDetails.ProtoReflect.Descriptor instead.
func (*GetCreditLineDetailsResponse_Data_CreditLineDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineDetails) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineDetails) GetUpperLimit() float64 {
	if x != nil {
		return x.UpperLimit
	}
	return 0
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineDetails) GetAvailableLimit() float64 {
	if x != nil {
		return x.AvailableLimit
	}
	return 0
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineDetails) GetBlockLimit() float64 {
	if x != nil {
		return x.BlockLimit
	}
	return 0
}

type GetCreditLineDetailsResponse_Data_CreditLineSchemes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Roi               float64 `protobuf:"fixed64,1,opt,name=roi,proto3" json:"roi,omitempty"`
	RoiType           string  `protobuf:"bytes,2,opt,name=roi_type,proto3" json:"roi_type,omitempty"`
	MaxEmiAllowed     float64 `protobuf:"fixed64,3,opt,name=max_emi_allowed,proto3" json:"max_emi_allowed,omitempty"`
	MinTenure         int32   `protobuf:"varint,4,opt,name=min_tenure,proto3" json:"min_tenure,omitempty"`
	MaxTenure         int32   `protobuf:"varint,5,opt,name=max_tenure,proto3" json:"max_tenure,omitempty"`
	MinDrawdownAmount float64 `protobuf:"fixed64,6,opt,name=min_drawdown_amount,proto3" json:"min_drawdown_amount,omitempty"`
	MaxDrawdownAmount float64 `protobuf:"fixed64,7,opt,name=max_drawdown_amount,proto3" json:"max_drawdown_amount,omitempty"`
	EmiDueDate        string  `protobuf:"bytes,8,opt,name=emi_due_date,proto3" json:"emi_due_date,omitempty"` //YYYY-MM-DD
	PfType            string  `protobuf:"bytes,9,opt,name=pf_type,proto3" json:"pf_type,omitempty"`
	PfFees            float64 `protobuf:"fixed64,10,opt,name=pf_fees,proto3" json:"pf_fees,omitempty"`
	FrankingType      string  `protobuf:"bytes,11,opt,name=franking_type,proto3" json:"franking_type,omitempty"`
	FrankingCharges   string  `protobuf:"bytes,12,opt,name=franking_charges,proto3" json:"franking_charges,omitempty"`
	TenureFrequency   string  `protobuf:"bytes,13,opt,name=tenure_frequency,proto3" json:"tenure_frequency,omitempty"`
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) Reset() {
	*x = GetCreditLineDetailsResponse_Data_CreditLineSchemes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditLineDetailsResponse_Data_CreditLineSchemes) ProtoMessage() {}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditLineDetailsResponse_Data_CreditLineSchemes.ProtoReflect.Descriptor instead.
func (*GetCreditLineDetailsResponse_Data_CreditLineSchemes) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{1, 0, 1}
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) GetRoi() float64 {
	if x != nil {
		return x.Roi
	}
	return 0
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) GetRoiType() string {
	if x != nil {
		return x.RoiType
	}
	return ""
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) GetMaxEmiAllowed() float64 {
	if x != nil {
		return x.MaxEmiAllowed
	}
	return 0
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) GetMinTenure() int32 {
	if x != nil {
		return x.MinTenure
	}
	return 0
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) GetMaxTenure() int32 {
	if x != nil {
		return x.MaxTenure
	}
	return 0
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) GetMinDrawdownAmount() float64 {
	if x != nil {
		return x.MinDrawdownAmount
	}
	return 0
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) GetMaxDrawdownAmount() float64 {
	if x != nil {
		return x.MaxDrawdownAmount
	}
	return 0
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) GetEmiDueDate() string {
	if x != nil {
		return x.EmiDueDate
	}
	return ""
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) GetPfType() string {
	if x != nil {
		return x.PfType
	}
	return ""
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) GetPfFees() float64 {
	if x != nil {
		return x.PfFees
	}
	return 0
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) GetFrankingType() string {
	if x != nil {
		return x.FrankingType
	}
	return ""
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) GetFrankingCharges() string {
	if x != nil {
		return x.FrankingCharges
	}
	return ""
}

func (x *GetCreditLineDetailsResponse_Data_CreditLineSchemes) GetTenureFrequency() string {
	if x != nil {
		return x.TenureFrequency
	}
	return ""
}

type AddPersonalDetailsResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantId   int64  `protobuf:"varint,1,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email         string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	ContactNumber string `protobuf:"bytes,4,opt,name=contact_number,proto3" json:"contact_number,omitempty"`
}

func (x *AddPersonalDetailsResponse_Data) Reset() {
	*x = AddPersonalDetailsResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPersonalDetailsResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPersonalDetailsResponse_Data) ProtoMessage() {}

func (x *AddPersonalDetailsResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPersonalDetailsResponse_Data.ProtoReflect.Descriptor instead.
func (*AddPersonalDetailsResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{3, 0}
}

func (x *AddPersonalDetailsResponse_Data) GetApplicantId() int64 {
	if x != nil {
		return x.ApplicantId
	}
	return 0
}

func (x *AddPersonalDetailsResponse_Data) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddPersonalDetailsResponse_Data) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *AddPersonalDetailsResponse_Data) GetContactNumber() string {
	if x != nil {
		return x.ContactNumber
	}
	return ""
}

type GetLimitResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantId    int32                             `protobuf:"varint,1,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	Name           string                            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email          string                            `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	ContactNumber  string                            `protobuf:"bytes,4,opt,name=contact_number,proto3" json:"contact_number,omitempty"`
	Pan            string                            `protobuf:"bytes,5,opt,name=pan,proto3" json:"pan,omitempty"`
	UpperLimit     *GetLimitResponse_Data_LimitValue `protobuf:"bytes,6,opt,name=upper_limit,proto3" json:"upper_limit,omitempty"`
	AvailableLimit *GetLimitResponse_Data_LimitValue `protobuf:"bytes,7,opt,name=available_limit,proto3" json:"available_limit,omitempty"`
	LimitExpiry    string                            `protobuf:"bytes,8,opt,name=limit_expiry,proto3" json:"limit_expiry,omitempty"`
	Status         string                            `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *GetLimitResponse_Data) Reset() {
	*x = GetLimitResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLimitResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLimitResponse_Data) ProtoMessage() {}

func (x *GetLimitResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLimitResponse_Data.ProtoReflect.Descriptor instead.
func (*GetLimitResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{9, 0}
}

func (x *GetLimitResponse_Data) GetApplicantId() int32 {
	if x != nil {
		return x.ApplicantId
	}
	return 0
}

func (x *GetLimitResponse_Data) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetLimitResponse_Data) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetLimitResponse_Data) GetContactNumber() string {
	if x != nil {
		return x.ContactNumber
	}
	return ""
}

func (x *GetLimitResponse_Data) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *GetLimitResponse_Data) GetUpperLimit() *GetLimitResponse_Data_LimitValue {
	if x != nil {
		return x.UpperLimit
	}
	return nil
}

func (x *GetLimitResponse_Data) GetAvailableLimit() *GetLimitResponse_Data_LimitValue {
	if x != nil {
		return x.AvailableLimit
	}
	return nil
}

func (x *GetLimitResponse_Data) GetLimitExpiry() string {
	if x != nil {
		return x.LimitExpiry
	}
	return ""
}

func (x *GetLimitResponse_Data) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type GetLimitResponse_Data_LimitValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value       float64 `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
	LastUpdated string  `protobuf:"bytes,2,opt,name=last_updated,proto3" json:"last_updated,omitempty"`
}

func (x *GetLimitResponse_Data_LimitValue) Reset() {
	*x = GetLimitResponse_Data_LimitValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLimitResponse_Data_LimitValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLimitResponse_Data_LimitValue) ProtoMessage() {}

func (x *GetLimitResponse_Data_LimitValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLimitResponse_Data_LimitValue.ProtoReflect.Descriptor instead.
func (*GetLimitResponse_Data_LimitValue) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{9, 0, 0}
}

func (x *GetLimitResponse_Data_LimitValue) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *GetLimitResponse_Data_LimitValue) GetLastUpdated() string {
	if x != nil {
		return x.LastUpdated
	}
	return ""
}

type ApplicantLookupResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantId   int64                                    `protobuf:"varint,1,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	Name          string                                   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email         string                                   `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	ContactNumber string                                   `protobuf:"bytes,4,opt,name=contact_number,proto3" json:"contact_number,omitempty"`
	Pan           string                                   `protobuf:"bytes,5,opt,name=pan,proto3" json:"pan,omitempty"`
	Details       *ApplicantLookupResponse_Data_Details    `protobuf:"bytes,6,opt,name=details,proto3" json:"details,omitempty"`
	Activation    *ApplicantLookupResponse_Data_Activation `protobuf:"bytes,7,opt,name=activation,proto3" json:"activation,omitempty"`
}

func (x *ApplicantLookupResponse_Data) Reset() {
	*x = ApplicantLookupResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicantLookupResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicantLookupResponse_Data) ProtoMessage() {}

func (x *ApplicantLookupResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicantLookupResponse_Data.ProtoReflect.Descriptor instead.
func (*ApplicantLookupResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{11, 0}
}

func (x *ApplicantLookupResponse_Data) GetApplicantId() int64 {
	if x != nil {
		return x.ApplicantId
	}
	return 0
}

func (x *ApplicantLookupResponse_Data) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ApplicantLookupResponse_Data) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ApplicantLookupResponse_Data) GetContactNumber() string {
	if x != nil {
		return x.ContactNumber
	}
	return ""
}

func (x *ApplicantLookupResponse_Data) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *ApplicantLookupResponse_Data) GetDetails() *ApplicantLookupResponse_Data_Details {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *ApplicantLookupResponse_Data) GetActivation() *ApplicantLookupResponse_Data_Activation {
	if x != nil {
		return x.Activation
	}
	return nil
}

type ApplicantLookupResponse_Data_Details struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Banking    bool `protobuf:"varint,1,opt,name=banking,proto3" json:"banking,omitempty"`
	Address    bool `protobuf:"varint,2,opt,name=address,proto3" json:"address,omitempty"`
	Employment bool `protobuf:"varint,3,opt,name=employment,proto3" json:"employment,omitempty"`
}

func (x *ApplicantLookupResponse_Data_Details) Reset() {
	*x = ApplicantLookupResponse_Data_Details{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicantLookupResponse_Data_Details) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicantLookupResponse_Data_Details) ProtoMessage() {}

func (x *ApplicantLookupResponse_Data_Details) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicantLookupResponse_Data_Details.ProtoReflect.Descriptor instead.
func (*ApplicantLookupResponse_Data_Details) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{11, 0, 0}
}

func (x *ApplicantLookupResponse_Data_Details) GetBanking() bool {
	if x != nil {
		return x.Banking
	}
	return false
}

func (x *ApplicantLookupResponse_Data_Details) GetAddress() bool {
	if x != nil {
		return x.Address
	}
	return false
}

func (x *ApplicantLookupResponse_Data_Details) GetEmployment() bool {
	if x != nil {
		return x.Employment
	}
	return false
}

type ApplicantLookupResponse_Data_Activation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Agreement bool `protobuf:"varint,1,opt,name=agreement,proto3" json:"agreement,omitempty"`
	Mandate   bool `protobuf:"varint,2,opt,name=mandate,proto3" json:"mandate,omitempty"`
}

func (x *ApplicantLookupResponse_Data_Activation) Reset() {
	*x = ApplicantLookupResponse_Data_Activation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicantLookupResponse_Data_Activation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicantLookupResponse_Data_Activation) ProtoMessage() {}

func (x *ApplicantLookupResponse_Data_Activation) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicantLookupResponse_Data_Activation.ProtoReflect.Descriptor instead.
func (*ApplicantLookupResponse_Data_Activation) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{11, 0, 1}
}

func (x *ApplicantLookupResponse_Data_Activation) GetAgreement() bool {
	if x != nil {
		return x.Agreement
	}
	return false
}

func (x *ApplicantLookupResponse_Data_Activation) GetMandate() bool {
	if x != nil {
		return x.Mandate
	}
	return false
}

type GetMandateLinkResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MandateLink string `protobuf:"bytes,1,opt,name=mandate_link,proto3" json:"mandate_link,omitempty"`
	MandateId   string `protobuf:"bytes,2,opt,name=mandate_id,proto3" json:"mandate_id,omitempty"`
}

func (x *GetMandateLinkResponse_Data) Reset() {
	*x = GetMandateLinkResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateLinkResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateLinkResponse_Data) ProtoMessage() {}

func (x *GetMandateLinkResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateLinkResponse_Data.ProtoReflect.Descriptor instead.
func (*GetMandateLinkResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{13, 0}
}

func (x *GetMandateLinkResponse_Data) GetMandateLink() string {
	if x != nil {
		return x.MandateLink
	}
	return ""
}

func (x *GetMandateLinkResponse_Data) GetMandateId() string {
	if x != nil {
		return x.MandateId
	}
	return ""
}

type GetMandateStatusResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *GetMandateStatusResponse_Data) Reset() {
	*x = GetMandateStatusResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateStatusResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateStatusResponse_Data) ProtoMessage() {}

func (x *GetMandateStatusResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateStatusResponse_Data.ProtoReflect.Descriptor instead.
func (*GetMandateStatusResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{15, 0}
}

func (x *GetMandateStatusResponse_Data) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type MakeDrawdownResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanId      int64  `protobuf:"varint,1,opt,name=loan_id,proto3" json:"loan_id,omitempty"`
	ApplicantId string `protobuf:"bytes,2,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	Urn         string `protobuf:"bytes,3,opt,name=urn,proto3" json:"urn,omitempty"`
}

func (x *MakeDrawdownResponse_Data) Reset() {
	*x = MakeDrawdownResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MakeDrawdownResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeDrawdownResponse_Data) ProtoMessage() {}

func (x *MakeDrawdownResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeDrawdownResponse_Data.ProtoReflect.Descriptor instead.
func (*MakeDrawdownResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{17, 0}
}

func (x *MakeDrawdownResponse_Data) GetLoanId() int64 {
	if x != nil {
		return x.LoanId
	}
	return 0
}

func (x *MakeDrawdownResponse_Data) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *MakeDrawdownResponse_Data) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

type GetPdfAgreementResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DocId int32  `protobuf:"varint,1,opt,name=doc_id,proto3" json:"doc_id,omitempty"`
	Pdf   string `protobuf:"bytes,2,opt,name=pdf,proto3" json:"pdf,omitempty"`
}

func (x *GetPdfAgreementResponse_Data) Reset() {
	*x = GetPdfAgreementResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPdfAgreementResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPdfAgreementResponse_Data) ProtoMessage() {}

func (x *GetPdfAgreementResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPdfAgreementResponse_Data.ProtoReflect.Descriptor instead.
func (*GetPdfAgreementResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{19, 0}
}

func (x *GetPdfAgreementResponse_Data) GetDocId() int32 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *GetPdfAgreementResponse_Data) GetPdf() string {
	if x != nil {
		return x.Pdf
	}
	return ""
}

type SendBorrowerAgreementOtpResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendBorrowerAgreementOtpResponse_Data) Reset() {
	*x = SendBorrowerAgreementOtpResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendBorrowerAgreementOtpResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendBorrowerAgreementOtpResponse_Data) ProtoMessage() {}

func (x *SendBorrowerAgreementOtpResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendBorrowerAgreementOtpResponse_Data.ProtoReflect.Descriptor instead.
func (*SendBorrowerAgreementOtpResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{21, 0}
}

type VerifyBorrowerAgreementOtpResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgreementSignedCopy string `protobuf:"bytes,1,opt,name=agreement_signed_copy,json=AgreementSignedCopy,proto3" json:"agreement_signed_copy,omitempty"`
}

func (x *VerifyBorrowerAgreementOtpResponse_Data) Reset() {
	*x = VerifyBorrowerAgreementOtpResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyBorrowerAgreementOtpResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyBorrowerAgreementOtpResponse_Data) ProtoMessage() {}

func (x *VerifyBorrowerAgreementOtpResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyBorrowerAgreementOtpResponse_Data.ProtoReflect.Descriptor instead.
func (*VerifyBorrowerAgreementOtpResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{23, 0}
}

func (x *VerifyBorrowerAgreementOtpResponse_Data) GetAgreementSignedCopy() string {
	if x != nil {
		return x.AgreementSignedCopy
	}
	return ""
}

type GetLoanStatusResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *GetLoanStatusResponse_Data_Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *GetLoanStatusResponse_Data) Reset() {
	*x = GetLoanStatusResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanStatusResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanStatusResponse_Data) ProtoMessage() {}

func (x *GetLoanStatusResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanStatusResponse_Data.ProtoReflect.Descriptor instead.
func (*GetLoanStatusResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{25, 0}
}

func (x *GetLoanStatusResponse_Data) GetStatus() *GetLoanStatusResponse_Data_Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetLoanStatusResponse_Data_Status struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanId              int32   `protobuf:"varint,1,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	LoanCode            string  `protobuf:"bytes,2,opt,name=loan_code,json=loanCode,proto3" json:"loan_code,omitempty"`
	Status              string  `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	OrderId             string  `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Urn                 string  `protobuf:"bytes,5,opt,name=urn,proto3" json:"urn,omitempty"`
	Amount              float64 `protobuf:"fixed64,6,opt,name=amount,proto3" json:"amount,omitempty"`
	ProductAmount       float64 `protobuf:"fixed64,7,opt,name=product_amount,json=productAmount,proto3" json:"product_amount,omitempty"`
	DisbursedAmount     float64 `protobuf:"fixed64,8,opt,name=disbursed_amount,json=disbursedAmount,proto3" json:"disbursed_amount,omitempty"`
	DisbursedDate       string  `protobuf:"bytes,9,opt,name=disbursed_date,json=disbursementDate,proto3" json:"disbursed_date,omitempty"`
	Emi                 float64 `protobuf:"fixed64,10,opt,name=emi,proto3" json:"emi,omitempty"`
	Tenure              int32   `protobuf:"varint,11,opt,name=tenure,proto3" json:"tenure,omitempty"`
	Roi                 float64 `protobuf:"fixed64,12,opt,name=roi,proto3" json:"roi,omitempty"`
	Utr                 string  `protobuf:"bytes,13,opt,name=utr,proto3" json:"utr,omitempty"`
	LastStatusTimestamp string  `protobuf:"bytes,14,opt,name=last_status_timestamp,proto3" json:"last_status_timestamp,omitempty"`
}

func (x *GetLoanStatusResponse_Data_Status) Reset() {
	*x = GetLoanStatusResponse_Data_Status{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanStatusResponse_Data_Status) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanStatusResponse_Data_Status) ProtoMessage() {}

func (x *GetLoanStatusResponse_Data_Status) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanStatusResponse_Data_Status.ProtoReflect.Descriptor instead.
func (*GetLoanStatusResponse_Data_Status) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{25, 0, 0}
}

func (x *GetLoanStatusResponse_Data_Status) GetLoanId() int32 {
	if x != nil {
		return x.LoanId
	}
	return 0
}

func (x *GetLoanStatusResponse_Data_Status) GetLoanCode() string {
	if x != nil {
		return x.LoanCode
	}
	return ""
}

func (x *GetLoanStatusResponse_Data_Status) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetLoanStatusResponse_Data_Status) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *GetLoanStatusResponse_Data_Status) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

func (x *GetLoanStatusResponse_Data_Status) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *GetLoanStatusResponse_Data_Status) GetProductAmount() float64 {
	if x != nil {
		return x.ProductAmount
	}
	return 0
}

func (x *GetLoanStatusResponse_Data_Status) GetDisbursedAmount() float64 {
	if x != nil {
		return x.DisbursedAmount
	}
	return 0
}

func (x *GetLoanStatusResponse_Data_Status) GetDisbursedDate() string {
	if x != nil {
		return x.DisbursedDate
	}
	return ""
}

func (x *GetLoanStatusResponse_Data_Status) GetEmi() float64 {
	if x != nil {
		return x.Emi
	}
	return 0
}

func (x *GetLoanStatusResponse_Data_Status) GetTenure() int32 {
	if x != nil {
		return x.Tenure
	}
	return 0
}

func (x *GetLoanStatusResponse_Data_Status) GetRoi() float64 {
	if x != nil {
		return x.Roi
	}
	return 0
}

func (x *GetLoanStatusResponse_Data_Status) GetUtr() string {
	if x != nil {
		return x.Utr
	}
	return ""
}

func (x *GetLoanStatusResponse_Data_Status) GetLastStatusTimestamp() string {
	if x != nil {
		return x.LastStatusTimestamp
	}
	return ""
}

type VerifyAndDownloadCkycResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PersonalDetails *VerifyAndDownloadCkycResponse_Data_PersonalDetails `protobuf:"bytes,1,opt,name=personal_details,json=PERSONAL_DETAILS,proto3" json:"personal_details,omitempty"`
}

func (x *VerifyAndDownloadCkycResponse_Data) Reset() {
	*x = VerifyAndDownloadCkycResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyAndDownloadCkycResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAndDownloadCkycResponse_Data) ProtoMessage() {}

func (x *VerifyAndDownloadCkycResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAndDownloadCkycResponse_Data.ProtoReflect.Descriptor instead.
func (*VerifyAndDownloadCkycResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{27, 0}
}

func (x *VerifyAndDownloadCkycResponse_Data) GetPersonalDetails() *VerifyAndDownloadCkycResponse_Data_PersonalDetails {
	if x != nil {
		return x.PersonalDetails
	}
	return nil
}

type VerifyAndDownloadCkycResponse_Data_PersonalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CkycNo     string                                                      `protobuf:"bytes,1,opt,name=ckyc_no,json=CKYC_NO,proto3" json:"ckyc_no,omitempty"`
	Name       string                                                      `protobuf:"bytes,2,opt,name=name,json=NAME,proto3" json:"name,omitempty"`
	KycDate    string                                                      `protobuf:"bytes,3,opt,name=kyc_date,json=KYC_DATE,proto3" json:"kyc_date,omitempty"`
	Gender     string                                                      `protobuf:"bytes,4,opt,name=gender,json=GENDER,proto3" json:"gender,omitempty"`
	Dob        string                                                      `protobuf:"bytes,5,opt,name=dob,json=DOB,proto3" json:"dob,omitempty"`
	Fname      string                                                      `protobuf:"bytes,6,opt,name=fname,json=FNAME,proto3" json:"fname,omitempty"`
	Lname      string                                                      `protobuf:"bytes,7,opt,name=lname,json=LNAME,proto3" json:"lname,omitempty"`
	FatherName string                                                      `protobuf:"bytes,8,opt,name=father_name,json=FATHER_NAME,proto3" json:"father_name,omitempty"`
	MotherName string                                                      `protobuf:"bytes,9,opt,name=mother_name,json=MOTHER_NAME,proto3" json:"mother_name,omitempty"`
	MobNum     string                                                      `protobuf:"bytes,10,opt,name=mob_num,json=MOB_NUM,proto3" json:"mob_num,omitempty"`
	Email      string                                                      `protobuf:"bytes,11,opt,name=email,json=EMAIL,proto3" json:"email,omitempty"`
	Address    *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address `protobuf:"bytes,12,opt,name=address,json=ADDRESS,proto3" json:"address,omitempty"`
	ImageType  string                                                      `protobuf:"bytes,13,opt,name=image_type,json=IMAGE_TYPE,proto3" json:"image_type,omitempty"`
	Photo      string                                                      `protobuf:"bytes,14,opt,name=photo,json=PHOTO,proto3" json:"photo,omitempty"`
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) Reset() {
	*x = VerifyAndDownloadCkycResponse_Data_PersonalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAndDownloadCkycResponse_Data_PersonalDetails) ProtoMessage() {}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAndDownloadCkycResponse_Data_PersonalDetails.ProtoReflect.Descriptor instead.
func (*VerifyAndDownloadCkycResponse_Data_PersonalDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{27, 0, 0}
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetCkycNo() string {
	if x != nil {
		return x.CkycNo
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetKycDate() string {
	if x != nil {
		return x.KycDate
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetDob() string {
	if x != nil {
		return x.Dob
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetFname() string {
	if x != nil {
		return x.Fname
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetLname() string {
	if x != nil {
		return x.Lname
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetFatherName() string {
	if x != nil {
		return x.FatherName
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetMotherName() string {
	if x != nil {
		return x.MotherName
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetMobNum() string {
	if x != nil {
		return x.MobNum
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetAddress() *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetImageType() string {
	if x != nil {
		return x.ImageType
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails) GetPhoto() string {
	if x != nil {
		return x.Photo
	}
	return ""
}

type VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermLine1          string `protobuf:"bytes,1,opt,name=perm_line1,json=PERM_LINE1,proto3" json:"perm_line1,omitempty"`
	PermLine2          string `protobuf:"bytes,2,opt,name=perm_line2,json=PERM_LINE2,proto3" json:"perm_line2,omitempty"`
	PermLine3          string `protobuf:"bytes,3,opt,name=perm_line3,json=PERM_LINE3,proto3" json:"perm_line3,omitempty"`
	PermCity           string `protobuf:"bytes,4,opt,name=perm_city,json=PERM_CITY,proto3" json:"perm_city,omitempty"`
	PermDist           string `protobuf:"bytes,5,opt,name=perm_dist,json=PERM_DIST,proto3" json:"perm_dist,omitempty"`
	PermState          string `protobuf:"bytes,6,opt,name=perm_state,json=PERM_STATE,proto3" json:"perm_state,omitempty"`
	PermCountry        string `protobuf:"bytes,7,opt,name=perm_country,json=PERM_COUNTRY,proto3" json:"perm_country,omitempty"`
	PermPin            string `protobuf:"bytes,8,opt,name=perm_pin,json=PERM_PIN,proto3" json:"perm_pin,omitempty"`
	PermPoa            string `protobuf:"bytes,9,opt,name=perm_poa,json=PERM_POA,proto3" json:"perm_poa,omitempty"`
	PermCorresSameFlag string `protobuf:"bytes,10,opt,name=perm_corres_same_flag,json=PERM_CORRES_SAMEFLAG,proto3" json:"perm_corres_same_flag,omitempty"`
	CorresLine1        string `protobuf:"bytes,11,opt,name=corres_line1,json=CORRES_LINE1,proto3" json:"corres_line1,omitempty"`
	CorresLine2        string `protobuf:"bytes,12,opt,name=corres_line2,json=CORRES_LINE2,proto3" json:"corres_line2,omitempty"`
	CorresLine3        string `protobuf:"bytes,13,opt,name=corres_line3,json=CORRES_LINE3,proto3" json:"corres_line3,omitempty"`
	CorresCity         string `protobuf:"bytes,14,opt,name=corres_city,json=CORRES_CITY,proto3" json:"corres_city,omitempty"`
	CorresDist         string `protobuf:"bytes,15,opt,name=corres_dist,json=CORRES_DIST,proto3" json:"corres_dist,omitempty"`
	CorresState        string `protobuf:"bytes,16,opt,name=corres_state,json=CORRES_STATE,proto3" json:"corres_state,omitempty"`
	CorresCountry      string `protobuf:"bytes,17,opt,name=corres_country,json=CORRES_COUNTRY,proto3" json:"corres_country,omitempty"`
	CorresPin          string `protobuf:"bytes,18,opt,name=corres_pin,json=CORRES_PIN,proto3" json:"corres_pin,omitempty"`
	CorresPoa          string `protobuf:"bytes,19,opt,name=corres_poa,json=CORRES_POA,proto3" json:"corres_poa,omitempty"`
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) Reset() {
	*x = VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) ProtoMessage() {}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address.ProtoReflect.Descriptor instead.
func (*VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{27, 0, 0, 0}
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetPermLine1() string {
	if x != nil {
		return x.PermLine1
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetPermLine2() string {
	if x != nil {
		return x.PermLine2
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetPermLine3() string {
	if x != nil {
		return x.PermLine3
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetPermCity() string {
	if x != nil {
		return x.PermCity
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetPermDist() string {
	if x != nil {
		return x.PermDist
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetPermState() string {
	if x != nil {
		return x.PermState
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetPermCountry() string {
	if x != nil {
		return x.PermCountry
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetPermPin() string {
	if x != nil {
		return x.PermPin
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetPermPoa() string {
	if x != nil {
		return x.PermPoa
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetPermCorresSameFlag() string {
	if x != nil {
		return x.PermCorresSameFlag
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetCorresLine1() string {
	if x != nil {
		return x.CorresLine1
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetCorresLine2() string {
	if x != nil {
		return x.CorresLine2
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetCorresLine3() string {
	if x != nil {
		return x.CorresLine3
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetCorresCity() string {
	if x != nil {
		return x.CorresCity
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetCorresDist() string {
	if x != nil {
		return x.CorresDist
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetCorresState() string {
	if x != nil {
		return x.CorresState
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetCorresCountry() string {
	if x != nil {
		return x.CorresCountry
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetCorresPin() string {
	if x != nil {
		return x.CorresPin
	}
	return ""
}

func (x *VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address) GetCorresPoa() string {
	if x != nil {
		return x.CorresPoa
	}
	return ""
}

type GetRepaymentScheduleResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Schedule []*Schedule `protobuf:"bytes,1,rep,name=schedule,proto3" json:"schedule,omitempty"`
}

func (x *GetRepaymentScheduleResponse_Data) Reset() {
	*x = GetRepaymentScheduleResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse_Data) ProtoMessage() {}

func (x *GetRepaymentScheduleResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse_Data.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{30, 0}
}

func (x *GetRepaymentScheduleResponse_Data) GetSchedule() []*Schedule {
	if x != nil {
		return x.Schedule
	}
	return nil
}

type GetRepaymentScheduleErrorResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Schedule []*Schedule `protobuf:"bytes,1,rep,name=schedule,proto3" json:"schedule,omitempty"`
}

func (x *GetRepaymentScheduleErrorResponse_Data) Reset() {
	*x = GetRepaymentScheduleErrorResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleErrorResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleErrorResponse_Data) ProtoMessage() {}

func (x *GetRepaymentScheduleErrorResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleErrorResponse_Data.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleErrorResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{32, 0}
}

func (x *GetRepaymentScheduleErrorResponse_Data) GetSchedule() []*Schedule {
	if x != nil {
		return x.Schedule
	}
	return nil
}

type ErrorResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ErrorResponse_Data) Reset() {
	*x = ErrorResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorResponse_Data) ProtoMessage() {}

func (x *ErrorResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorResponse_Data.ProtoReflect.Descriptor instead.
func (*ErrorResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{33, 0}
}

type UploadDocumentResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId       int64  `protobuf:"varint,1,opt,name=application_id,proto3" json:"application_id,omitempty"`
	ApplicantId         int64  `protobuf:"varint,2,opt,name=applicant_id,proto3" json:"applicant_id,omitempty"`
	DocumentTypeId      int64  `protobuf:"varint,3,opt,name=document_type_id,proto3" json:"document_type_id,omitempty"`
	NbfcId              int64  `protobuf:"varint,4,opt,name=nbfc_id,proto3" json:"nbfc_id,omitempty"`
	Id                  int64  `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	FileName            string `protobuf:"bytes,6,opt,name=file_name,proto3" json:"file_name,omitempty"`
	FilePath            string `protobuf:"bytes,7,opt,name=file_path,proto3" json:"file_path,omitempty"`
	Remarks             string `protobuf:"bytes,8,opt,name=remarks,proto3" json:"remarks,omitempty"`
	IsPasswordProtected string `protobuf:"bytes,9,opt,name=is_password_protected,proto3" json:"is_password_protected,omitempty"`
	CreatedBy           string `protobuf:"bytes,10,opt,name=created_by,proto3" json:"created_by,omitempty"`               // check if string
	Source              string `protobuf:"bytes,11,opt,name=source,proto3" json:"source,omitempty"`                       // check if string
	DocumentPassword    string `protobuf:"bytes,12,opt,name=document_password,proto3" json:"document_password,omitempty"` // check if string
	UpdatedAt           string `protobuf:"bytes,13,opt,name=updated_at,proto3" json:"updated_at,omitempty"`
	CreatedAt           string `protobuf:"bytes,14,opt,name=created_at,proto3" json:"created_at,omitempty"`
}

func (x *UploadDocumentResponse_Data) Reset() {
	*x = UploadDocumentResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadDocumentResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadDocumentResponse_Data) ProtoMessage() {}

func (x *UploadDocumentResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadDocumentResponse_Data.ProtoReflect.Descriptor instead.
func (*UploadDocumentResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{35, 0}
}

func (x *UploadDocumentResponse_Data) GetApplicationId() int64 {
	if x != nil {
		return x.ApplicationId
	}
	return 0
}

func (x *UploadDocumentResponse_Data) GetApplicantId() int64 {
	if x != nil {
		return x.ApplicantId
	}
	return 0
}

func (x *UploadDocumentResponse_Data) GetDocumentTypeId() int64 {
	if x != nil {
		return x.DocumentTypeId
	}
	return 0
}

func (x *UploadDocumentResponse_Data) GetNbfcId() int64 {
	if x != nil {
		return x.NbfcId
	}
	return 0
}

func (x *UploadDocumentResponse_Data) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UploadDocumentResponse_Data) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *UploadDocumentResponse_Data) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *UploadDocumentResponse_Data) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *UploadDocumentResponse_Data) GetIsPasswordProtected() string {
	if x != nil {
		return x.IsPasswordProtected
	}
	return ""
}

func (x *UploadDocumentResponse_Data) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *UploadDocumentResponse_Data) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *UploadDocumentResponse_Data) GetDocumentPassword() string {
	if x != nil {
		return x.DocumentPassword
	}
	return ""
}

func (x *UploadDocumentResponse_Data) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *UploadDocumentResponse_Data) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type SaveCollectionRequest_PaymentSchedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModeOfPayment     string `protobuf:"bytes,1,opt,name=mode_of_payment,proto3" json:"mode_of_payment,omitempty"`
	TransactionDate   string `protobuf:"bytes,2,opt,name=transaction_date,proto3" json:"transaction_date,omitempty"`
	PaymentStatus     string `protobuf:"bytes,3,opt,name=payment_status,proto3" json:"payment_status,omitempty"`
	PaidTotalAmount   string `protobuf:"bytes,4,opt,name=paid_total_amount,proto3" json:"paid_total_amount,omitempty"`
	VoucherNo         string `protobuf:"bytes,5,opt,name=voucher_no,proto3" json:"voucher_no,omitempty"`
	Udf1              string `protobuf:"bytes,6,opt,name=udf1,proto3" json:"udf1,omitempty"`
	Udf2              string `protobuf:"bytes,7,opt,name=udf2,proto3" json:"udf2,omitempty"`
	BounceCharges     string `protobuf:"bytes,8,opt,name=bounce_charges,proto3" json:"bounce_charges,omitempty"`
	CollectionCharges string `protobuf:"bytes,9,opt,name=collection_charges,proto3" json:"collection_charges,omitempty"`
	OtherCharges      string `protobuf:"bytes,10,opt,name=other_charges,proto3" json:"other_charges,omitempty"`
	DueDate           string `protobuf:"bytes,11,opt,name=due_date,proto3" json:"due_date,omitempty"`
	// settelment_date is optional field, all the operations and decisions are taken based on transaction_date
	SettelmentDate string `protobuf:"bytes,12,opt,name=settelment_date,proto3" json:"settelment_date,omitempty"`
	LpiCharges     string `protobuf:"bytes,13,opt,name=lpi_charges,proto3" json:"lpi_charges,omitempty"`
}

func (x *SaveCollectionRequest_PaymentSchedule) Reset() {
	*x = SaveCollectionRequest_PaymentSchedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveCollectionRequest_PaymentSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCollectionRequest_PaymentSchedule) ProtoMessage() {}

func (x *SaveCollectionRequest_PaymentSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCollectionRequest_PaymentSchedule.ProtoReflect.Descriptor instead.
func (*SaveCollectionRequest_PaymentSchedule) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{36, 0}
}

func (x *SaveCollectionRequest_PaymentSchedule) GetModeOfPayment() string {
	if x != nil {
		return x.ModeOfPayment
	}
	return ""
}

func (x *SaveCollectionRequest_PaymentSchedule) GetTransactionDate() string {
	if x != nil {
		return x.TransactionDate
	}
	return ""
}

func (x *SaveCollectionRequest_PaymentSchedule) GetPaymentStatus() string {
	if x != nil {
		return x.PaymentStatus
	}
	return ""
}

func (x *SaveCollectionRequest_PaymentSchedule) GetPaidTotalAmount() string {
	if x != nil {
		return x.PaidTotalAmount
	}
	return ""
}

func (x *SaveCollectionRequest_PaymentSchedule) GetVoucherNo() string {
	if x != nil {
		return x.VoucherNo
	}
	return ""
}

func (x *SaveCollectionRequest_PaymentSchedule) GetUdf1() string {
	if x != nil {
		return x.Udf1
	}
	return ""
}

func (x *SaveCollectionRequest_PaymentSchedule) GetUdf2() string {
	if x != nil {
		return x.Udf2
	}
	return ""
}

func (x *SaveCollectionRequest_PaymentSchedule) GetBounceCharges() string {
	if x != nil {
		return x.BounceCharges
	}
	return ""
}

func (x *SaveCollectionRequest_PaymentSchedule) GetCollectionCharges() string {
	if x != nil {
		return x.CollectionCharges
	}
	return ""
}

func (x *SaveCollectionRequest_PaymentSchedule) GetOtherCharges() string {
	if x != nil {
		return x.OtherCharges
	}
	return ""
}

func (x *SaveCollectionRequest_PaymentSchedule) GetDueDate() string {
	if x != nil {
		return x.DueDate
	}
	return ""
}

func (x *SaveCollectionRequest_PaymentSchedule) GetSettelmentDate() string {
	if x != nil {
		return x.SettelmentDate
	}
	return ""
}

func (x *SaveCollectionRequest_PaymentSchedule) GetLpiCharges() string {
	if x != nil {
		return x.LpiCharges
	}
	return ""
}

type SaveCollectionResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanId string `protobuf:"bytes,1,opt,name=loan_id,proto3" json:"loan_id,omitempty"`
}

func (x *SaveCollectionResponse_Data) Reset() {
	*x = SaveCollectionResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveCollectionResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCollectionResponse_Data) ProtoMessage() {}

func (x *SaveCollectionResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCollectionResponse_Data.ProtoReflect.Descriptor instead.
func (*SaveCollectionResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{37, 0}
}

func (x *SaveCollectionResponse_Data) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

type HashGenerationForOkycResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hash string `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
}

func (x *HashGenerationForOkycResponse_Data) Reset() {
	*x = HashGenerationForOkycResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HashGenerationForOkycResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HashGenerationForOkycResponse_Data) ProtoMessage() {}

func (x *HashGenerationForOkycResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HashGenerationForOkycResponse_Data.ProtoReflect.Descriptor instead.
func (*HashGenerationForOkycResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{39, 0}
}

func (x *HashGenerationForOkycResponse_Data) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type CaptchaGenerationForOkycResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaptchaImage string `protobuf:"bytes,1,opt,name=captcha_image,proto3" json:"captcha_image,omitempty"`
	RequestToken string `protobuf:"bytes,2,opt,name=request_token,proto3" json:"request_token,omitempty"`
	CaptchaTxnId string `protobuf:"bytes,3,opt,name=captcha_txn_id,proto3" json:"captcha_txn_id,omitempty"`
}

func (x *CaptchaGenerationForOkycResponse_Data) Reset() {
	*x = CaptchaGenerationForOkycResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaptchaGenerationForOkycResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaptchaGenerationForOkycResponse_Data) ProtoMessage() {}

func (x *CaptchaGenerationForOkycResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaptchaGenerationForOkycResponse_Data.ProtoReflect.Descriptor instead.
func (*CaptchaGenerationForOkycResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{41, 0}
}

func (x *CaptchaGenerationForOkycResponse_Data) GetCaptchaImage() string {
	if x != nil {
		return x.CaptchaImage
	}
	return ""
}

func (x *CaptchaGenerationForOkycResponse_Data) GetRequestToken() string {
	if x != nil {
		return x.RequestToken
	}
	return ""
}

func (x *CaptchaGenerationForOkycResponse_Data) GetCaptchaTxnId() string {
	if x != nil {
		return x.CaptchaTxnId
	}
	return ""
}

type GenerateOtpForOkycResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnId string `protobuf:"bytes,1,opt,name=txn_id,proto3" json:"txn_id,omitempty"`
}

func (x *GenerateOtpForOkycResponse_Data) Reset() {
	*x = GenerateOtpForOkycResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateOtpForOkycResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateOtpForOkycResponse_Data) ProtoMessage() {}

func (x *GenerateOtpForOkycResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateOtpForOkycResponse_Data.ProtoReflect.Descriptor instead.
func (*GenerateOtpForOkycResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{43, 0}
}

func (x *GenerateOtpForOkycResponse_Data) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

type ValidateOtpForOkycResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ValidateOtpForOkycResponse_Data) Reset() {
	*x = ValidateOtpForOkycResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateOtpForOkycResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateOtpForOkycResponse_Data) ProtoMessage() {}

func (x *ValidateOtpForOkycResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateOtpForOkycResponse_Data.ProtoReflect.Descriptor instead.
func (*ValidateOtpForOkycResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{45, 0}
}

type UpdateLeadRequest_SchemeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// possible values: Daily/Weekly/Monthly
	InstallmentFrequency string `protobuf:"bytes,1,opt,name=installment_frequency,proto3" json:"installment_frequency,omitempty"`
	InstallmentTenure    int32  `protobuf:"varint,2,opt,name=installment_tenure,proto3" json:"installment_tenure,omitempty"`
	// amount or percentage of processing fee
	ProcessingFeesValue float64 `protobuf:"fixed64,3,opt,name=processing_fees_value,proto3" json:"processing_fees_value,omitempty"`
	// interest rate - mandatory
	// using wrapper type here so that it is not omitted in json marshaling in case the interest is 0
	RoiPercentage        *wrapperspb.DoubleValue `protobuf:"bytes,4,opt,name=roi_percentage,proto3" json:"roi_percentage,omitempty"`
	InstallmentStartDate string                  `protobuf:"bytes,5,opt,name=installment_start_date,proto3" json:"installment_start_date,omitempty"`
	// not needed, this is hard coded as per the business agreement
	SubventionPercentage float64 `protobuf:"fixed64,6,opt,name=subvention_percentage,proto3" json:"subvention_percentage,omitempty"`
	// Possible values: Standard/Percentage/FixedAmount
	ProcessingFeesType string `protobuf:"bytes,7,opt,name=processing_fees_type,proto3" json:"processing_fees_type,omitempty"`
	// Possible values: Declining/Flat/Balloon
	RoiType string `protobuf:"bytes,8,opt,name=roi_type,proto3" json:"roi_type,omitempty"`
	// Possible values: loan_amount/product_amount
	// Product amount is applicable for zero cost EMI for consumer products like electronics
	// For our FLDG and ES cases, both of these mean the same thing
	RoiAppliedOn string `protobuf:"bytes,9,opt,name=roi_applied_on,proto3" json:"roi_applied_on,omitempty"`
	// Possible values: loan_amount/product_amount
	ProcessingFeesCustomerAppliedOn string `protobuf:"bytes,10,opt,name=processing_fees_customer_applied_on,proto3" json:"processing_fees_customer_applied_on,omitempty"`
	// Possible values: Yes/No
	// implies whether to apply gst on the processing fees or not
	ProcessingFeesCustomerGst string `protobuf:"bytes,11,opt,name=processing_fees_customer_gst,proto3" json:"processing_fees_customer_gst,omitempty"`
	// Possible values: Yes/No
	// implies whether to apply gst on the interest or not
	RoiGst string `protobuf:"bytes,12,opt,name=roi_gst,proto3" json:"roi_gst,omitempty"`
	// Possible values: Standard/Percentage/FixedAmount
	GstType string `protobuf:"bytes,13,opt,name=gst_type,proto3" json:"gst_type,omitempty"`
	// gst value to be applied based on gst_type
	GstValue float64 `protobuf:"fixed64,14,opt,name=gst_value,proto3" json:"gst_value,omitempty"`
}

func (x *UpdateLeadRequest_SchemeDetails) Reset() {
	*x = UpdateLeadRequest_SchemeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLeadRequest_SchemeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLeadRequest_SchemeDetails) ProtoMessage() {}

func (x *UpdateLeadRequest_SchemeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLeadRequest_SchemeDetails.ProtoReflect.Descriptor instead.
func (*UpdateLeadRequest_SchemeDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{46, 0}
}

func (x *UpdateLeadRequest_SchemeDetails) GetInstallmentFrequency() string {
	if x != nil {
		return x.InstallmentFrequency
	}
	return ""
}

func (x *UpdateLeadRequest_SchemeDetails) GetInstallmentTenure() int32 {
	if x != nil {
		return x.InstallmentTenure
	}
	return 0
}

func (x *UpdateLeadRequest_SchemeDetails) GetProcessingFeesValue() float64 {
	if x != nil {
		return x.ProcessingFeesValue
	}
	return 0
}

func (x *UpdateLeadRequest_SchemeDetails) GetRoiPercentage() *wrapperspb.DoubleValue {
	if x != nil {
		return x.RoiPercentage
	}
	return nil
}

func (x *UpdateLeadRequest_SchemeDetails) GetInstallmentStartDate() string {
	if x != nil {
		return x.InstallmentStartDate
	}
	return ""
}

func (x *UpdateLeadRequest_SchemeDetails) GetSubventionPercentage() float64 {
	if x != nil {
		return x.SubventionPercentage
	}
	return 0
}

func (x *UpdateLeadRequest_SchemeDetails) GetProcessingFeesType() string {
	if x != nil {
		return x.ProcessingFeesType
	}
	return ""
}

func (x *UpdateLeadRequest_SchemeDetails) GetRoiType() string {
	if x != nil {
		return x.RoiType
	}
	return ""
}

func (x *UpdateLeadRequest_SchemeDetails) GetRoiAppliedOn() string {
	if x != nil {
		return x.RoiAppliedOn
	}
	return ""
}

func (x *UpdateLeadRequest_SchemeDetails) GetProcessingFeesCustomerAppliedOn() string {
	if x != nil {
		return x.ProcessingFeesCustomerAppliedOn
	}
	return ""
}

func (x *UpdateLeadRequest_SchemeDetails) GetProcessingFeesCustomerGst() string {
	if x != nil {
		return x.ProcessingFeesCustomerGst
	}
	return ""
}

func (x *UpdateLeadRequest_SchemeDetails) GetRoiGst() string {
	if x != nil {
		return x.RoiGst
	}
	return ""
}

func (x *UpdateLeadRequest_SchemeDetails) GetGstType() string {
	if x != nil {
		return x.GstType
	}
	return ""
}

func (x *UpdateLeadRequest_SchemeDetails) GetGstValue() float64 {
	if x != nil {
		return x.GstValue
	}
	return 0
}

type UpdateLeadResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanId string `protobuf:"bytes,1,opt,name=loan_id,proto3" json:"loan_id,omitempty"`
}

func (x *UpdateLeadResponse_Data) Reset() {
	*x = UpdateLeadResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLeadResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLeadResponse_Data) ProtoMessage() {}

func (x *UpdateLeadResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLeadResponse_Data.ProtoReflect.Descriptor instead.
func (*UpdateLeadResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{47, 0}
}

func (x *UpdateLeadResponse_Data) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

type CancelLeadResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId string `protobuf:"bytes,1,opt,name=application_id,json=ApplicationId,proto3" json:"application_id,omitempty"`
	ApplicantId   string `protobuf:"bytes,2,opt,name=applicant_id,json=ApplicantId,proto3" json:"applicant_id,omitempty"`
}

func (x *CancelLeadResponse_Data) Reset() {
	*x = CancelLeadResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelLeadResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelLeadResponse_Data) ProtoMessage() {}

func (x *CancelLeadResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelLeadResponse_Data.ProtoReflect.Descriptor instead.
func (*CancelLeadResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{49, 0}
}

func (x *CancelLeadResponse_Data) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *CancelLeadResponse_Data) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

type ForeClosureDetailsResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId        string  `protobuf:"bytes,1,opt,name=application_id,json=loan_id,proto3" json:"application_id,omitempty"`
	TotalOutstanding     float64 `protobuf:"fixed64,2,opt,name=total_outstanding,proto3" json:"total_outstanding,omitempty"`
	PrincipalOutstanding float64 `protobuf:"fixed64,3,opt,name=principal_outstanding,proto3" json:"principal_outstanding,omitempty"`
	InterestOutstanding  float64 `protobuf:"fixed64,4,opt,name=interest_outstanding,proto3" json:"interest_outstanding,omitempty"`
	PenaltyCharges       float64 `protobuf:"fixed64,5,opt,name=penalty_charges,proto3" json:"penalty_charges,omitempty"`
	FeesCharges          float64 `protobuf:"fixed64,6,opt,name=fees_charges,proto3" json:"fees_charges,omitempty"`
	OtherCharges         float64 `protobuf:"fixed64,7,opt,name=other_charges,proto3" json:"other_charges,omitempty"`
}

func (x *ForeClosureDetailsResponse_Data) Reset() {
	*x = ForeClosureDetailsResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeClosureDetailsResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeClosureDetailsResponse_Data) ProtoMessage() {}

func (x *ForeClosureDetailsResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeClosureDetailsResponse_Data.ProtoReflect.Descriptor instead.
func (*ForeClosureDetailsResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{52, 0}
}

func (x *ForeClosureDetailsResponse_Data) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *ForeClosureDetailsResponse_Data) GetTotalOutstanding() float64 {
	if x != nil {
		return x.TotalOutstanding
	}
	return 0
}

func (x *ForeClosureDetailsResponse_Data) GetPrincipalOutstanding() float64 {
	if x != nil {
		return x.PrincipalOutstanding
	}
	return 0
}

func (x *ForeClosureDetailsResponse_Data) GetInterestOutstanding() float64 {
	if x != nil {
		return x.InterestOutstanding
	}
	return 0
}

func (x *ForeClosureDetailsResponse_Data) GetPenaltyCharges() float64 {
	if x != nil {
		return x.PenaltyCharges
	}
	return 0
}

func (x *ForeClosureDetailsResponse_Data) GetFeesCharges() float64 {
	if x != nil {
		return x.FeesCharges
	}
	return 0
}

func (x *ForeClosureDetailsResponse_Data) GetOtherCharges() float64 {
	if x != nil {
		return x.OtherCharges
	}
	return 0
}

type CreateRepaymentScheduleRequest_Schedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// YYYY-MM-DD
	Date         string                  `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Principal    *wrapperspb.DoubleValue `protobuf:"bytes,2,opt,name=principal,json=principle,proto3" json:"principal,omitempty"`
	Interest     *wrapperspb.DoubleValue `protobuf:"bytes,3,opt,name=interest,proto3" json:"interest,omitempty"`
	OtherCharges *wrapperspb.DoubleValue `protobuf:"bytes,4,opt,name=other_charges,proto3" json:"other_charges,omitempty"`
	// total sum should be equal to principal+interest+other_charges
	TotalAmount *wrapperspb.DoubleValue `protobuf:"bytes,5,opt,name=total_amount,proto3" json:"total_amount,omitempty"`
	// optional field
	PrincipalOutstanding *wrapperspb.DoubleValue `protobuf:"bytes,6,opt,name=principal_outstanding,json=principle_outstanding,proto3" json:"principal_outstanding,omitempty"`
}

func (x *CreateRepaymentScheduleRequest_Schedule) Reset() {
	*x = CreateRepaymentScheduleRequest_Schedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRepaymentScheduleRequest_Schedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRepaymentScheduleRequest_Schedule) ProtoMessage() {}

func (x *CreateRepaymentScheduleRequest_Schedule) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRepaymentScheduleRequest_Schedule.ProtoReflect.Descriptor instead.
func (*CreateRepaymentScheduleRequest_Schedule) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{55, 0}
}

func (x *CreateRepaymentScheduleRequest_Schedule) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *CreateRepaymentScheduleRequest_Schedule) GetPrincipal() *wrapperspb.DoubleValue {
	if x != nil {
		return x.Principal
	}
	return nil
}

func (x *CreateRepaymentScheduleRequest_Schedule) GetInterest() *wrapperspb.DoubleValue {
	if x != nil {
		return x.Interest
	}
	return nil
}

func (x *CreateRepaymentScheduleRequest_Schedule) GetOtherCharges() *wrapperspb.DoubleValue {
	if x != nil {
		return x.OtherCharges
	}
	return nil
}

func (x *CreateRepaymentScheduleRequest_Schedule) GetTotalAmount() *wrapperspb.DoubleValue {
	if x != nil {
		return x.TotalAmount
	}
	return nil
}

func (x *CreateRepaymentScheduleRequest_Schedule) GetPrincipalOutstanding() *wrapperspb.DoubleValue {
	if x != nil {
		return x.PrincipalOutstanding
	}
	return nil
}

type CreateRepaymentScheduleResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanId string `protobuf:"bytes,1,opt,name=loan_id,proto3" json:"loan_id,omitempty"`
}

func (x *CreateRepaymentScheduleResponse_Data) Reset() {
	*x = CreateRepaymentScheduleResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRepaymentScheduleResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRepaymentScheduleResponse_Data) ProtoMessage() {}

func (x *CreateRepaymentScheduleResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRepaymentScheduleResponse_Data.ProtoReflect.Descriptor instead.
func (*CreateRepaymentScheduleResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP(), []int{56, 0}
}

func (x *CreateRepaymentScheduleResponse_Data) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

var File_api_vendors_liquiloans_lending_preapproved_loan_proto protoreflect.FileDescriptor

var file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDesc = []byte{
	0x0a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x69,
	0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x6f, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69,
	0x6e, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73,
	0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73,
	0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73,
	0x75, 0x6d, 0x22, 0xf4, 0x08, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x4c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x51, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69,
	0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0x9e, 0x07, 0x0a, 0x04, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x81, 0x01, 0x0a, 0x13, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x4f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x13, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x81, 0x01, 0x0a, 0x13, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69,
	0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x73, 0x52, 0x13, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x1a, 0xa5, 0x01, 0x0a, 0x11, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x20, 0x0a, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x1a, 0xe5, 0x03, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x69, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x72, 0x6f, 0x69, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x6f, 0x69,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x69,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x61, 0x78, 0x5f, 0x65, 0x6d, 0x69,
	0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f,
	0x6d, 0x61, 0x78, 0x5f, 0x65, 0x6d, 0x69, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12,
	0x30, 0x0a, 0x13, 0x6d, 0x69, 0x6e, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x64, 0x6f, 0x77, 0x6e, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13, 0x6d, 0x69,
	0x6e, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x30, 0x0a, 0x13, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x64, 0x6f, 0x77,
	0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13,
	0x6d, 0x61, 0x78, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6d, 0x69, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6d, 0x69, 0x5f, 0x64,
	0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x66, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x66, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x66, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x07, 0x70, 0x66, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x66,
	0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x66, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x2a, 0x0a, 0x10, 0x66, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x66, 0x72, 0x61,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x2a, 0x0a,
	0x10, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f,
	0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x22, 0xe1, 0x02, 0x0a, 0x19, 0x41, 0x64,
	0x64, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x70,
	0x61, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x66, 0x31, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x64, 0x66, 0x31, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x66,
	0x38, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x55, 0x44, 0x46, 0x38, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x64, 0x66, 0x39, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x55, 0x44, 0x46,
	0x39, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x66, 0x34, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x55, 0x44, 0x46, 0x34, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x66, 0x35, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x55, 0x44, 0x46, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x66,
	0x32, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x55, 0x44, 0x46, 0x32, 0x22, 0xcd, 0x02,
	0x0a, 0x1a, 0x41, 0x64, 0x64, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4f,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e,
	0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a,
	0x7c, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x9c, 0x02,
	0x0a, 0x18, 0x41, 0x64, 0x64, 0x42, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x22,
	0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x69, 0x66, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x69, 0x66, 0x73, 0x63, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x13,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22, 0xba, 0x02, 0x0a,
	0x18, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x26, 0x0a,
	0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x31, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x31, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x32, 0x12, 0x12, 0x0a,
	0x04, 0x61, 0x72, 0x65, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x65,
	0x61, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22, 0x87, 0x02, 0x0a, 0x1b, 0x41, 0x64,
	0x64, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2c, 0x0a, 0x11, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x69,
	0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75,
	0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75,
	0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0xa3, 0x01, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2b, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22, 0x63, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x22,
	0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22, 0x98,
	0x05, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x45, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69,
	0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0xda, 0x03, 0x0a,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x70,
	0x61, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x5e, 0x0a,
	0x0b, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x0b, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x66, 0x0a,
	0x0f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x1a, 0x46, 0x0a, 0x0a, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x22, 0x58, 0x0a, 0x16, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x75, 0x6d, 0x22, 0xc0, 0x05, 0x0a, 0x17, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e,
	0x74, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x4c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x38, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a,
	0xf4, 0x03, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x10,
	0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e,
	0x12, 0x5a, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x40, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x63, 0x0a, 0x0a,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x43, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69,
	0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x1a, 0x5d, 0x0a, 0x07, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x62,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x1a, 0x44, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c,
	0x0a, 0x09, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x22, 0x8d, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73,
	0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73,
	0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73,
	0x75, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0x93, 0x02, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x4b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d,
	0x1a, 0x4a, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x1e, 0x0a, 0x0a,
	0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x22, 0x6b, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22, 0xeb, 0x01, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4d, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0x1e, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xf7, 0x01, 0x0a, 0x13, 0x4d, 0x61, 0x6b, 0x65,
	0x44, 0x72, 0x61, 0x77, 0x64, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69,
	0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a,
	0x10, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f,
	0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65, 0x6e,
	0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6e, 0x12,
	0x20, 0x0a, 0x0b, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x22, 0x9b, 0x02, 0x0a, 0x14, 0x4d, 0x61, 0x6b, 0x65, 0x44, 0x72, 0x61, 0x77, 0x64, 0x6f,
	0x77, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x49, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x44, 0x72, 0x61, 0x77,
	0x64, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0x56, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x18, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6e, 0x22,
	0x92, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x64, 0x66, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x12, 0x26, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x73, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x73, 0x75, 0x6d, 0x22, 0xfb, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x64, 0x66, 0x41,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x4c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x38, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69,
	0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x64, 0x66, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d,
	0x1a, 0x30, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x70, 0x64, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70,
	0x64, 0x66, 0x22, 0x9b, 0x01, 0x0a, 0x1f, 0x53, 0x65, 0x6e, 0x64, 0x42, 0x6f, 0x72, 0x72, 0x6f,
	0x77, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d,
	0x22, 0xe3, 0x01, 0x0a, 0x20, 0x53, 0x65, 0x6e, 0x64, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x65,
	0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x55, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x65, 0x72, 0x41,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0x06,
	0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x22, 0xc7, 0x01, 0x0a, 0x21, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x22,
	0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x74, 0x70,
	0x22, 0x9b, 0x02, 0x0a, 0x22, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x42, 0x6f, 0x72, 0x72, 0x6f,
	0x77, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74, 0x70, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x57, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x42, 0x6f, 0x72, 0x72, 0x6f,
	0x77, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74, 0x70, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73,
	0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73,
	0x75, 0x6d, 0x1a, 0x3a, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x70, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x43, 0x6f, 0x70, 0x79, 0x22, 0x9c,
	0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x53, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6e, 0x22, 0xc3, 0x05,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4a, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0xfb, 0x03, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x55,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x9b, 0x03, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x6f, 0x61,
	0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f,
	0x61, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x69,
	0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x64, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73,
	0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64,
	0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x65, 0x6d, 0x69, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x65, 0x6d,
	0x69, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x69,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x72, 0x6f, 0x69, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x74, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x74, 0x72, 0x12, 0x34, 0x0a,
	0x15, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x22, 0x8c, 0x02, 0x0a, 0x1c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x41, 0x6e,
	0x64, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x53, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x64, 0x5f, 0x6e, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x64, 0x5f, 0x6e, 0x6f, 0x12, 0x2a, 0x0a, 0x10, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18,
	0x3a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x22, 0xbf, 0x0b, 0x0a, 0x1d, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x41, 0x6e, 0x64,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x52, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x41, 0x6e, 0x64, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x43, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0xe7, 0x09, 0x0a, 0x04, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x7a, 0x0a, 0x10, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4e, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61,
	0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x41, 0x6e, 0x64, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6b, 0x79, 0x63,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x50, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x10, 0x50,
	0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x1a,
	0xe2, 0x08, 0x0a, 0x0f, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x4f, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x41, 0x4d,
	0x45, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x79, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x4b, 0x59, 0x43, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x12, 0x16, 0x0a,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x47,
	0x45, 0x4e, 0x44, 0x45, 0x52, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x44, 0x4f, 0x42, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x46, 0x4e, 0x41, 0x4d, 0x45, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x4c, 0x4e,
	0x41, 0x4d, 0x45, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x46, 0x41, 0x54, 0x48, 0x45, 0x52,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4d, 0x4f, 0x54, 0x48,
	0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x62, 0x5f, 0x6e,
	0x75, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4d, 0x4f, 0x42, 0x5f, 0x4e, 0x55,
	0x4d, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x12, 0x70, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x41, 0x6e, 0x64, 0x44,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61,
	0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x07, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x49,
	0x4d, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f,
	0x74, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x50, 0x48, 0x4f, 0x54, 0x4f, 0x1a,
	0x92, 0x05, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x70,
	0x65, 0x72, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x50, 0x45, 0x52, 0x4d, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x31, 0x12, 0x1e, 0x0a, 0x0a, 0x70,
	0x65, 0x72, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x50, 0x45, 0x52, 0x4d, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x32, 0x12, 0x1e, 0x0a, 0x0a, 0x70,
	0x65, 0x72, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x50, 0x45, 0x52, 0x4d, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x33, 0x12, 0x1c, 0x0a, 0x09, 0x70,
	0x65, 0x72, 0x6d, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x50, 0x45, 0x52, 0x4d, 0x5f, 0x43, 0x49, 0x54, 0x59, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x65, 0x72,
	0x6d, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x45,
	0x52, 0x4d, 0x5f, 0x44, 0x49, 0x53, 0x54, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x50, 0x45, 0x52,
	0x4d, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x50,
	0x45, 0x52, 0x4d, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x65, 0x72, 0x6d, 0x5f, 0x70, 0x69, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50,
	0x45, 0x52, 0x4d, 0x5f, 0x50, 0x49, 0x4e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x6d, 0x5f,
	0x70, 0x6f, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x45, 0x52, 0x4d, 0x5f,
	0x50, 0x4f, 0x41, 0x12, 0x33, 0x0a, 0x15, 0x70, 0x65, 0x72, 0x6d, 0x5f, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x73, 0x5f, 0x73, 0x61, 0x6d, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x14, 0x50, 0x45, 0x52, 0x4d, 0x5f, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x53, 0x5f,
	0x53, 0x41, 0x4d, 0x45, 0x46, 0x4c, 0x41, 0x47, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x43, 0x4f, 0x52, 0x52, 0x45, 0x53, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x31, 0x12, 0x22, 0x0a, 0x0c,
	0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x53, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x32,
	0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x33,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x53, 0x5f, 0x4c,
	0x49, 0x4e, 0x45, 0x33, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x5f, 0x63,
	0x69, 0x74, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x4f, 0x52, 0x52, 0x45,
	0x53, 0x5f, 0x43, 0x49, 0x54, 0x59, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73,
	0x5f, 0x64, 0x69, 0x73, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x4f, 0x52,
	0x52, 0x45, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x54, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x43, 0x4f, 0x52, 0x52, 0x45, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x12, 0x26, 0x0a, 0x0e,
	0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x53, 0x5f, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x52, 0x59, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x5f, 0x70,
	0x69, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x53,
	0x5f, 0x50, 0x49, 0x4e, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x5f, 0x70,
	0x6f, 0x61, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x53,
	0x5f, 0x50, 0x4f, 0x41, 0x22, 0x65, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x53, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22, 0x6e, 0x0a, 0x1d, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x34, 0x12, 0x10, 0x0a, 0x03,
	0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22, 0xed, 0x01, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x51,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e,
	0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x1a, 0x48, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x08, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x22, 0xc6, 0x07, 0x0a, 0x08,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x12, 0x2e, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x64, 0x75, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0a, 0x64, 0x75, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x10,
	0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61,
	0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x15, 0x70, 0x61,
	0x69, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x70, 0x61, 0x69, 0x64, 0x5f,
	0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x32, 0x0a, 0x14, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x14,
	0x70, 0x61, 0x69, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x70, 0x69, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x03, 0x6c, 0x70, 0x69, 0x12, 0x24, 0x0a, 0x0d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0e,
	0x62, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x62, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x22, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x6f,
	0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x22, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70,
	0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x4c, 0x0a, 0x21, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x6f, 0x75,
	0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x21, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x12, 0x26, 0x0a, 0x0e, 0x77, 0x61, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x77, 0x61, 0x69, 0x76,
	0x65, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x69, 0x64, 0x5f, 0x6c, 0x70, 0x69, 0x18, 0x12, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x70, 0x61,
	0x69, 0x64, 0x5f, 0x6c, 0x70, 0x69, 0x12, 0x2e, 0x0a, 0x12, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x12, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x13, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x62,
	0x6f, 0x75, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x13, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x62, 0x6f, 0x75, 0x6e, 0x63, 0x65,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x4a, 0x0a, 0x20, 0x70, 0x6f, 0x73, 0x74,
	0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x20, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x22, 0xf7, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x56, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x1a, 0x48, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x08,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x22, 0xbd,
	0x01, 0x0a, 0x0d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x42, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69,
	0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0x06, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x22, 0xc2,
	0x01, 0x0a, 0x15, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x69,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x15, 0x0a, 0x04,
	0x66, 0x69, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x46, 0x69, 0x6c, 0x65,
	0x73, 0x5b, 0x5d, 0x22, 0xa4, 0x05, 0x0a, 0x16, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x4b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0xda, 0x03,
	0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x22,
	0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x64, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x6e, 0x62, 0x66, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x6e, 0x62, 0x66, 0x63, 0x5f, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x34,
	0x0a, 0x15, 0x69, 0x73, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x69,
	0x73, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x11,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0xbc, 0x05, 0x0a, 0x15, 0x53,
	0x61, 0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x53, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x6d, 0x0a, 0x10,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x1a, 0xeb, 0x03, 0x0a, 0x0f,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12,
	0x28, 0x0a, 0x0f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x6f,
	0x66, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a,
	0x11, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x76,
	0x6f, 0x75, 0x63, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x76, 0x6f, 0x75, 0x63, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x64, 0x66, 0x31, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x64, 0x66, 0x31, 0x12,
	0x12, 0x0a, 0x04, 0x75, 0x64, 0x66, 0x32, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75,
	0x64, 0x66, 0x32, 0x12, 0x26, 0x0a, 0x0e, 0x62, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x6f, 0x75,
	0x6e, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x73, 0x65, 0x74, 0x74, 0x65, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x65, 0x74, 0x74, 0x65, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x70, 0x69, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x70,
	0x69, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x22, 0xe9, 0x01, 0x0a, 0x16, 0x53, 0x61,
	0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c,
	0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x75, 0x6d, 0x1a, 0x20, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x6c,
	0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x69, 0x64, 0x22, 0x74, 0x0a, 0x1c, 0x48, 0x61, 0x73, 0x68, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x53, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22, 0xf1, 0x01, 0x0a, 0x1d,
	0x48, 0x61, 0x73, 0x68, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f,
	0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x52, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61,
	0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x48, 0x61, 0x73, 0x68, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x75, 0x6d, 0x1a, 0x1a, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x68,
	0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x22,
	0x8b, 0x01, 0x0a, 0x1f, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x53, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73,
	0x68, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22, 0xd7, 0x02,
	0x0a, 0x20, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x55, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x41, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0x7a, 0x0a, 0x04, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x26, 0x0a, 0x0e, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61,
	0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x22, 0x8f, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x53, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68,
	0x61, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x69, 0x64, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x69, 0x64, 0x5f, 0x6e, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x63,
	0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x24, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61,
	0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63,
	0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22, 0xef, 0x01, 0x0a, 0x1a, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4f, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x74,
	0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0x1e, 0x0a, 0x04, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x22, 0xbd, 0x01, 0x0a, 0x19,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b,
	0x79, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x53, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22, 0xd7, 0x01, 0x0a, 0x1a,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b,
	0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4f, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x4f, 0x74, 0x70, 0x46, 0x6f, 0x72, 0x4f, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0x06, 0x0a,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x22, 0xbc, 0x07, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x53, 0x49, 0x44, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x63, 0x0a, 0x0e, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69,
	0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x1a, 0xc1, 0x05, 0x0a, 0x0d, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x34, 0x0a, 0x15, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x66,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x2e, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x44,
	0x0a, 0x0e, 0x72, 0x6f, 0x69, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x72, 0x6f, 0x69, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x16, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x15,
	0x73, 0x75, 0x62, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x73, 0x75, 0x62,
	0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x66, 0x65, 0x65, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65,
	0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x6f, 0x69, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x69, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x6f, 0x69, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65,
	0x64, 0x5f, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x6f, 0x69, 0x5f,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x23, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x5f, 0x6f,
	0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x23, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x1c,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x67, 0x73, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x1c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66,
	0x65, 0x65, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x67, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x69, 0x5f, 0x67, 0x73, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x72, 0x6f, 0x69, 0x5f, 0x67, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x73,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x73,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x73, 0x74, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x67, 0x73, 0x74, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0xe1, 0x01, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c,
	0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x47, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c,
	0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0x20, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18,
	0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x22, 0xa9, 0x01, 0x0a, 0x11, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x53, 0x49, 0x44,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x22, 0x91, 0x02, 0x0a, 0x12, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4c,
	0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x47, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4c,
	0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0x50, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x7b, 0x0a, 0x17, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x4c, 0x65, 0x61, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22, 0x8f, 0x01, 0x0a, 0x19, 0x46, 0x6f, 0x72, 0x65, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x53, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x85, 0x04, 0x0a, 0x1a, 0x46, 0x6f, 0x72, 0x65,
	0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x1a, 0xb3, 0x02, 0x0a, 0x04, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x61, 0x6e,
	0x5f, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6f, 0x75, 0x74,
	0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x34, 0x0a, 0x15, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x6f,
	0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x15, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x32, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x65, 0x73, 0x74, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f,
	0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x28, 0x0a, 0x0f, 0x70,
	0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x66, 0x65, 0x65, 0x73, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x66, 0x65, 0x65,
	0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x22,
	0xbd, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x6e, 0x74, 0x55, 0x64, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12,
	0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12,
	0x12, 0x0a, 0x04, 0x75, 0x64, 0x66, 0x38, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x55,
	0x44, 0x46, 0x38, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x66, 0x39, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x55, 0x44, 0x46, 0x39, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x66, 0x34, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x55, 0x44, 0x46, 0x34, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x64, 0x66, 0x35, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x55, 0x44, 0x46, 0x35, 0x22,
	0x7e, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x6e, 0x74, 0x55, 0x64, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22,
	0xc5, 0x05, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x73, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x53, 0x49, 0x44, 0x12, 0x30,
	0x0a, 0x13, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x72, 0x65, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6d, 0x69, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x65, 0x6d, 0x69, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0e,
	0x65, 0x6d, 0x69, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6d, 0x69, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x70, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x43, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c,
	0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x1a, 0xee, 0x02, 0x0a, 0x08, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x70, 0x72, 0x69, 0x6e, 0x63,
	0x69, 0x70, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69,
	0x70, 0x6c, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x12, 0x42, 0x0a,
	0x0d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x0d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x12, 0x40, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x52, 0x0a, 0x15, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c,
	0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x15, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x6f, 0x75, 0x74, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x22, 0xfb, 0x01, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x54, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0x20, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x6c,
	0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x69, 0x64, 0x42, 0x6e, 0x0a, 0x35, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x69, 0x71, 0x75,
	0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5a, 0x35,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2f, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2f, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescOnce sync.Once
	file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescData = file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDesc
)

func file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescGZIP() []byte {
	file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescOnce.Do(func() {
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescData)
	})
	return file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDescData
}

var file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes = make([]protoimpl.MessageInfo, 93)
var file_api_vendors_liquiloans_lending_preapproved_loan_proto_goTypes = []interface{}{
	(*GetCreditLineDetailsRequest)(nil),                                // 0: vendors.liquiloans.lending.GetCreditLineDetailsRequest
	(*GetCreditLineDetailsResponse)(nil),                               // 1: vendors.liquiloans.lending.GetCreditLineDetailsResponse
	(*AddPersonalDetailsRequest)(nil),                                  // 2: vendors.liquiloans.lending.AddPersonalDetailsRequest
	(*AddPersonalDetailsResponse)(nil),                                 // 3: vendors.liquiloans.lending.AddPersonalDetailsResponse
	(*AddBankingDetailsRequest)(nil),                                   // 4: vendors.liquiloans.lending.AddBankingDetailsRequest
	(*AddAddressDetailsRequest)(nil),                                   // 5: vendors.liquiloans.lending.AddAddressDetailsRequest
	(*AddEmploymentDetailsRequest)(nil),                                // 6: vendors.liquiloans.lending.AddEmploymentDetailsRequest
	(*AddDetailsResponse)(nil),                                         // 7: vendors.liquiloans.lending.AddDetailsResponse
	(*GetLimitRequest)(nil),                                            // 8: vendors.liquiloans.lending.GetLimitRequest
	(*GetLimitResponse)(nil),                                           // 9: vendors.liquiloans.lending.GetLimitResponse
	(*ApplicantLookupRequest)(nil),                                     // 10: vendors.liquiloans.lending.ApplicantLookupRequest
	(*ApplicantLookupResponse)(nil),                                    // 11: vendors.liquiloans.lending.ApplicantLookupResponse
	(*GetMandateLinkRequest)(nil),                                      // 12: vendors.liquiloans.lending.GetMandateLinkRequest
	(*GetMandateLinkResponse)(nil),                                     // 13: vendors.liquiloans.lending.GetMandateLinkResponse
	(*GetMandateStatusRequest)(nil),                                    // 14: vendors.liquiloans.lending.GetMandateStatusRequest
	(*GetMandateStatusResponse)(nil),                                   // 15: vendors.liquiloans.lending.GetMandateStatusResponse
	(*MakeDrawdownRequest)(nil),                                        // 16: vendors.liquiloans.lending.MakeDrawdownRequest
	(*MakeDrawdownResponse)(nil),                                       // 17: vendors.liquiloans.lending.MakeDrawdownResponse
	(*GetPdfAgreementRequest)(nil),                                     // 18: vendors.liquiloans.lending.GetPdfAgreementRequest
	(*GetPdfAgreementResponse)(nil),                                    // 19: vendors.liquiloans.lending.GetPdfAgreementResponse
	(*SendBorrowerAgreementOtpRequest)(nil),                            // 20: vendors.liquiloans.lending.SendBorrowerAgreementOtpRequest
	(*SendBorrowerAgreementOtpResponse)(nil),                           // 21: vendors.liquiloans.lending.SendBorrowerAgreementOtpResponse
	(*VerifyBorrowerAgreementOtpRequest)(nil),                          // 22: vendors.liquiloans.lending.VerifyBorrowerAgreementOtpRequest
	(*VerifyBorrowerAgreementOtpResponse)(nil),                         // 23: vendors.liquiloans.lending.VerifyBorrowerAgreementOtpResponse
	(*GetLoanStatusRequest)(nil),                                       // 24: vendors.liquiloans.lending.GetLoanStatusRequest
	(*GetLoanStatusResponse)(nil),                                      // 25: vendors.liquiloans.lending.GetLoanStatusResponse
	(*VerifyAndDownloadCkycRequest)(nil),                               // 26: vendors.liquiloans.lending.VerifyAndDownloadCkycRequest
	(*VerifyAndDownloadCkycResponse)(nil),                              // 27: vendors.liquiloans.lending.VerifyAndDownloadCkycResponse
	(*GetRepaymentScheduleRequest)(nil),                                // 28: vendors.liquiloans.lending.GetRepaymentScheduleRequest
	(*GetRepaymentScheduleRequestV4)(nil),                              // 29: vendors.liquiloans.lending.GetRepaymentScheduleRequestV4
	(*GetRepaymentScheduleResponse)(nil),                               // 30: vendors.liquiloans.lending.GetRepaymentScheduleResponse
	(*Schedule)(nil),                                                   // 31: vendors.liquiloans.lending.Schedule
	(*GetRepaymentScheduleErrorResponse)(nil),                          // 32: vendors.liquiloans.lending.GetRepaymentScheduleErrorResponse
	(*ErrorResponse)(nil),                                              // 33: vendors.liquiloans.lending.ErrorResponse
	(*UploadDocumentRequest)(nil),                                      // 34: vendors.liquiloans.lending.UploadDocumentRequest
	(*UploadDocumentResponse)(nil),                                     // 35: vendors.liquiloans.lending.UploadDocumentResponse
	(*SaveCollectionRequest)(nil),                                      // 36: vendors.liquiloans.lending.SaveCollectionRequest
	(*SaveCollectionResponse)(nil),                                     // 37: vendors.liquiloans.lending.SaveCollectionResponse
	(*HashGenerationForOkycRequest)(nil),                               // 38: vendors.liquiloans.lending.HashGenerationForOkycRequest
	(*HashGenerationForOkycResponse)(nil),                              // 39: vendors.liquiloans.lending.HashGenerationForOkycResponse
	(*CaptchaGenerationForOkycRequest)(nil),                            // 40: vendors.liquiloans.lending.CaptchaGenerationForOkycRequest
	(*CaptchaGenerationForOkycResponse)(nil),                           // 41: vendors.liquiloans.lending.CaptchaGenerationForOkycResponse
	(*GenerateOtpForOkycRequest)(nil),                                  // 42: vendors.liquiloans.lending.GenerateOtpForOkycRequest
	(*GenerateOtpForOkycResponse)(nil),                                 // 43: vendors.liquiloans.lending.GenerateOtpForOkycResponse
	(*ValidateOtpForOkycRequest)(nil),                                  // 44: vendors.liquiloans.lending.ValidateOtpForOkycRequest
	(*ValidateOtpForOkycResponse)(nil),                                 // 45: vendors.liquiloans.lending.ValidateOtpForOkycResponse
	(*UpdateLeadRequest)(nil),                                          // 46: vendors.liquiloans.lending.UpdateLeadRequest
	(*UpdateLeadResponse)(nil),                                         // 47: vendors.liquiloans.lending.UpdateLeadResponse
	(*CancelLeadRequest)(nil),                                          // 48: vendors.liquiloans.lending.CancelLeadRequest
	(*CancelLeadResponse)(nil),                                         // 49: vendors.liquiloans.lending.CancelLeadResponse
	(*CancelLeadErrorResponse)(nil),                                    // 50: vendors.liquiloans.lending.CancelLeadErrorResponse
	(*ForeClosureDetailsRequest)(nil),                                  // 51: vendors.liquiloans.lending.ForeClosureDetailsRequest
	(*ForeClosureDetailsResponse)(nil),                                 // 52: vendors.liquiloans.lending.ForeClosureDetailsResponse
	(*UpdateApplicantUdfRequest)(nil),                                  // 53: vendors.liquiloans.lending.UpdateApplicantUdfRequest
	(*UpdateApplicantUdfResponse)(nil),                                 // 54: vendors.liquiloans.lending.UpdateApplicantUdfResponse
	(*CreateRepaymentScheduleRequest)(nil),                             // 55: vendors.liquiloans.lending.CreateRepaymentScheduleRequest
	(*CreateRepaymentScheduleResponse)(nil),                            // 56: vendors.liquiloans.lending.CreateRepaymentScheduleResponse
	(*GetCreditLineDetailsResponse_Data)(nil),                          // 57: vendors.liquiloans.lending.GetCreditLineDetailsResponse.Data
	(*GetCreditLineDetailsResponse_Data_CreditLineDetails)(nil),        // 58: vendors.liquiloans.lending.GetCreditLineDetailsResponse.Data.CreditLineDetails
	(*GetCreditLineDetailsResponse_Data_CreditLineSchemes)(nil),        // 59: vendors.liquiloans.lending.GetCreditLineDetailsResponse.Data.CreditLineSchemes
	(*AddPersonalDetailsResponse_Data)(nil),                            // 60: vendors.liquiloans.lending.AddPersonalDetailsResponse.Data
	(*GetLimitResponse_Data)(nil),                                      // 61: vendors.liquiloans.lending.GetLimitResponse.Data
	(*GetLimitResponse_Data_LimitValue)(nil),                           // 62: vendors.liquiloans.lending.GetLimitResponse.Data.LimitValue
	(*ApplicantLookupResponse_Data)(nil),                               // 63: vendors.liquiloans.lending.ApplicantLookupResponse.Data
	(*ApplicantLookupResponse_Data_Details)(nil),                       // 64: vendors.liquiloans.lending.ApplicantLookupResponse.Data.Details
	(*ApplicantLookupResponse_Data_Activation)(nil),                    // 65: vendors.liquiloans.lending.ApplicantLookupResponse.Data.Activation
	(*GetMandateLinkResponse_Data)(nil),                                // 66: vendors.liquiloans.lending.GetMandateLinkResponse.Data
	(*GetMandateStatusResponse_Data)(nil),                              // 67: vendors.liquiloans.lending.GetMandateStatusResponse.Data
	(*MakeDrawdownResponse_Data)(nil),                                  // 68: vendors.liquiloans.lending.MakeDrawdownResponse.Data
	(*GetPdfAgreementResponse_Data)(nil),                               // 69: vendors.liquiloans.lending.GetPdfAgreementResponse.Data
	(*SendBorrowerAgreementOtpResponse_Data)(nil),                      // 70: vendors.liquiloans.lending.SendBorrowerAgreementOtpResponse.Data
	(*VerifyBorrowerAgreementOtpResponse_Data)(nil),                    // 71: vendors.liquiloans.lending.VerifyBorrowerAgreementOtpResponse.Data
	(*GetLoanStatusResponse_Data)(nil),                                 // 72: vendors.liquiloans.lending.GetLoanStatusResponse.Data
	(*GetLoanStatusResponse_Data_Status)(nil),                          // 73: vendors.liquiloans.lending.GetLoanStatusResponse.Data.Status
	(*VerifyAndDownloadCkycResponse_Data)(nil),                         // 74: vendors.liquiloans.lending.VerifyAndDownloadCkycResponse.Data
	(*VerifyAndDownloadCkycResponse_Data_PersonalDetails)(nil),         // 75: vendors.liquiloans.lending.VerifyAndDownloadCkycResponse.Data.PersonalDetails
	(*VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address)(nil), // 76: vendors.liquiloans.lending.VerifyAndDownloadCkycResponse.Data.PersonalDetails.Address
	(*GetRepaymentScheduleResponse_Data)(nil),                          // 77: vendors.liquiloans.lending.GetRepaymentScheduleResponse.Data
	(*GetRepaymentScheduleErrorResponse_Data)(nil),                     // 78: vendors.liquiloans.lending.GetRepaymentScheduleErrorResponse.Data
	(*ErrorResponse_Data)(nil),                                         // 79: vendors.liquiloans.lending.ErrorResponse.Data
	(*UploadDocumentResponse_Data)(nil),                                // 80: vendors.liquiloans.lending.UploadDocumentResponse.Data
	(*SaveCollectionRequest_PaymentSchedule)(nil),                      // 81: vendors.liquiloans.lending.SaveCollectionRequest.PaymentSchedule
	(*SaveCollectionResponse_Data)(nil),                                // 82: vendors.liquiloans.lending.SaveCollectionResponse.Data
	(*HashGenerationForOkycResponse_Data)(nil),                         // 83: vendors.liquiloans.lending.HashGenerationForOkycResponse.Data
	(*CaptchaGenerationForOkycResponse_Data)(nil),                      // 84: vendors.liquiloans.lending.CaptchaGenerationForOkycResponse.Data
	(*GenerateOtpForOkycResponse_Data)(nil),                            // 85: vendors.liquiloans.lending.GenerateOtpForOkycResponse.Data
	(*ValidateOtpForOkycResponse_Data)(nil),                            // 86: vendors.liquiloans.lending.ValidateOtpForOkycResponse.Data
	(*UpdateLeadRequest_SchemeDetails)(nil),                            // 87: vendors.liquiloans.lending.UpdateLeadRequest.SchemeDetails
	(*UpdateLeadResponse_Data)(nil),                                    // 88: vendors.liquiloans.lending.UpdateLeadResponse.Data
	(*CancelLeadResponse_Data)(nil),                                    // 89: vendors.liquiloans.lending.CancelLeadResponse.Data
	(*ForeClosureDetailsResponse_Data)(nil),                            // 90: vendors.liquiloans.lending.ForeClosureDetailsResponse.Data
	(*CreateRepaymentScheduleRequest_Schedule)(nil),                    // 91: vendors.liquiloans.lending.CreateRepaymentScheduleRequest.Schedule
	(*CreateRepaymentScheduleResponse_Data)(nil),                       // 92: vendors.liquiloans.lending.CreateRepaymentScheduleResponse.Data
	(*structpb.Struct)(nil),                                            // 93: google.protobuf.Struct
	(*wrapperspb.DoubleValue)(nil),                                     // 94: google.protobuf.DoubleValue
}
var file_api_vendors_liquiloans_lending_preapproved_loan_proto_depIdxs = []int32{
	57, // 0: vendors.liquiloans.lending.GetCreditLineDetailsResponse.data:type_name -> vendors.liquiloans.lending.GetCreditLineDetailsResponse.Data
	60, // 1: vendors.liquiloans.lending.AddPersonalDetailsResponse.data:type_name -> vendors.liquiloans.lending.AddPersonalDetailsResponse.Data
	93, // 2: vendors.liquiloans.lending.AddDetailsResponse.data:type_name -> google.protobuf.Struct
	61, // 3: vendors.liquiloans.lending.GetLimitResponse.data:type_name -> vendors.liquiloans.lending.GetLimitResponse.Data
	63, // 4: vendors.liquiloans.lending.ApplicantLookupResponse.data:type_name -> vendors.liquiloans.lending.ApplicantLookupResponse.Data
	66, // 5: vendors.liquiloans.lending.GetMandateLinkResponse.data:type_name -> vendors.liquiloans.lending.GetMandateLinkResponse.Data
	67, // 6: vendors.liquiloans.lending.GetMandateStatusResponse.data:type_name -> vendors.liquiloans.lending.GetMandateStatusResponse.Data
	68, // 7: vendors.liquiloans.lending.MakeDrawdownResponse.data:type_name -> vendors.liquiloans.lending.MakeDrawdownResponse.Data
	69, // 8: vendors.liquiloans.lending.GetPdfAgreementResponse.data:type_name -> vendors.liquiloans.lending.GetPdfAgreementResponse.Data
	70, // 9: vendors.liquiloans.lending.SendBorrowerAgreementOtpResponse.data:type_name -> vendors.liquiloans.lending.SendBorrowerAgreementOtpResponse.Data
	71, // 10: vendors.liquiloans.lending.VerifyBorrowerAgreementOtpResponse.data:type_name -> vendors.liquiloans.lending.VerifyBorrowerAgreementOtpResponse.Data
	72, // 11: vendors.liquiloans.lending.GetLoanStatusResponse.data:type_name -> vendors.liquiloans.lending.GetLoanStatusResponse.Data
	74, // 12: vendors.liquiloans.lending.VerifyAndDownloadCkycResponse.data:type_name -> vendors.liquiloans.lending.VerifyAndDownloadCkycResponse.Data
	77, // 13: vendors.liquiloans.lending.GetRepaymentScheduleResponse.data:type_name -> vendors.liquiloans.lending.GetRepaymentScheduleResponse.Data
	78, // 14: vendors.liquiloans.lending.GetRepaymentScheduleErrorResponse.data:type_name -> vendors.liquiloans.lending.GetRepaymentScheduleErrorResponse.Data
	79, // 15: vendors.liquiloans.lending.ErrorResponse.data:type_name -> vendors.liquiloans.lending.ErrorResponse.Data
	80, // 16: vendors.liquiloans.lending.UploadDocumentResponse.data:type_name -> vendors.liquiloans.lending.UploadDocumentResponse.Data
	81, // 17: vendors.liquiloans.lending.SaveCollectionRequest.payment_schedule:type_name -> vendors.liquiloans.lending.SaveCollectionRequest.PaymentSchedule
	82, // 18: vendors.liquiloans.lending.SaveCollectionResponse.data:type_name -> vendors.liquiloans.lending.SaveCollectionResponse.Data
	83, // 19: vendors.liquiloans.lending.HashGenerationForOkycResponse.data:type_name -> vendors.liquiloans.lending.HashGenerationForOkycResponse.Data
	84, // 20: vendors.liquiloans.lending.CaptchaGenerationForOkycResponse.data:type_name -> vendors.liquiloans.lending.CaptchaGenerationForOkycResponse.Data
	85, // 21: vendors.liquiloans.lending.GenerateOtpForOkycResponse.data:type_name -> vendors.liquiloans.lending.GenerateOtpForOkycResponse.Data
	86, // 22: vendors.liquiloans.lending.ValidateOtpForOkycResponse.data:type_name -> vendors.liquiloans.lending.ValidateOtpForOkycResponse.Data
	87, // 23: vendors.liquiloans.lending.UpdateLeadRequest.scheme_details:type_name -> vendors.liquiloans.lending.UpdateLeadRequest.SchemeDetails
	88, // 24: vendors.liquiloans.lending.UpdateLeadResponse.data:type_name -> vendors.liquiloans.lending.UpdateLeadResponse.Data
	89, // 25: vendors.liquiloans.lending.CancelLeadResponse.data:type_name -> vendors.liquiloans.lending.CancelLeadResponse.Data
	90, // 26: vendors.liquiloans.lending.ForeClosureDetailsResponse.data:type_name -> vendors.liquiloans.lending.ForeClosureDetailsResponse.Data
	91, // 27: vendors.liquiloans.lending.CreateRepaymentScheduleRequest.payment_schedules:type_name -> vendors.liquiloans.lending.CreateRepaymentScheduleRequest.Schedule
	92, // 28: vendors.liquiloans.lending.CreateRepaymentScheduleResponse.data:type_name -> vendors.liquiloans.lending.CreateRepaymentScheduleResponse.Data
	58, // 29: vendors.liquiloans.lending.GetCreditLineDetailsResponse.Data.credit_line_details:type_name -> vendors.liquiloans.lending.GetCreditLineDetailsResponse.Data.CreditLineDetails
	59, // 30: vendors.liquiloans.lending.GetCreditLineDetailsResponse.Data.credit_line_schemes:type_name -> vendors.liquiloans.lending.GetCreditLineDetailsResponse.Data.CreditLineSchemes
	62, // 31: vendors.liquiloans.lending.GetLimitResponse.Data.upper_limit:type_name -> vendors.liquiloans.lending.GetLimitResponse.Data.LimitValue
	62, // 32: vendors.liquiloans.lending.GetLimitResponse.Data.available_limit:type_name -> vendors.liquiloans.lending.GetLimitResponse.Data.LimitValue
	64, // 33: vendors.liquiloans.lending.ApplicantLookupResponse.Data.details:type_name -> vendors.liquiloans.lending.ApplicantLookupResponse.Data.Details
	65, // 34: vendors.liquiloans.lending.ApplicantLookupResponse.Data.activation:type_name -> vendors.liquiloans.lending.ApplicantLookupResponse.Data.Activation
	73, // 35: vendors.liquiloans.lending.GetLoanStatusResponse.Data.status:type_name -> vendors.liquiloans.lending.GetLoanStatusResponse.Data.Status
	75, // 36: vendors.liquiloans.lending.VerifyAndDownloadCkycResponse.Data.personal_details:type_name -> vendors.liquiloans.lending.VerifyAndDownloadCkycResponse.Data.PersonalDetails
	76, // 37: vendors.liquiloans.lending.VerifyAndDownloadCkycResponse.Data.PersonalDetails.address:type_name -> vendors.liquiloans.lending.VerifyAndDownloadCkycResponse.Data.PersonalDetails.Address
	31, // 38: vendors.liquiloans.lending.GetRepaymentScheduleResponse.Data.schedule:type_name -> vendors.liquiloans.lending.Schedule
	31, // 39: vendors.liquiloans.lending.GetRepaymentScheduleErrorResponse.Data.schedule:type_name -> vendors.liquiloans.lending.Schedule
	94, // 40: vendors.liquiloans.lending.UpdateLeadRequest.SchemeDetails.roi_percentage:type_name -> google.protobuf.DoubleValue
	94, // 41: vendors.liquiloans.lending.CreateRepaymentScheduleRequest.Schedule.principal:type_name -> google.protobuf.DoubleValue
	94, // 42: vendors.liquiloans.lending.CreateRepaymentScheduleRequest.Schedule.interest:type_name -> google.protobuf.DoubleValue
	94, // 43: vendors.liquiloans.lending.CreateRepaymentScheduleRequest.Schedule.other_charges:type_name -> google.protobuf.DoubleValue
	94, // 44: vendors.liquiloans.lending.CreateRepaymentScheduleRequest.Schedule.total_amount:type_name -> google.protobuf.DoubleValue
	94, // 45: vendors.liquiloans.lending.CreateRepaymentScheduleRequest.Schedule.principal_outstanding:type_name -> google.protobuf.DoubleValue
	46, // [46:46] is the sub-list for method output_type
	46, // [46:46] is the sub-list for method input_type
	46, // [46:46] is the sub-list for extension type_name
	46, // [46:46] is the sub-list for extension extendee
	0,  // [0:46] is the sub-list for field type_name
}

func init() { file_api_vendors_liquiloans_lending_preapproved_loan_proto_init() }
func file_api_vendors_liquiloans_lending_preapproved_loan_proto_init() {
	if File_api_vendors_liquiloans_lending_preapproved_loan_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditLineDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditLineDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPersonalDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPersonalDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddBankingDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAddressDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddEmploymentDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLimitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLimitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicantLookupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicantLookupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateLinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateLinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MakeDrawdownRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MakeDrawdownResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPdfAgreementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPdfAgreementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendBorrowerAgreementOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendBorrowerAgreementOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyBorrowerAgreementOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyBorrowerAgreementOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyAndDownloadCkycRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyAndDownloadCkycResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleRequestV4); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Schedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleErrorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErrorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadDocumentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadDocumentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveCollectionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveCollectionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HashGenerationForOkycRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HashGenerationForOkycResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaptchaGenerationForOkycRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaptchaGenerationForOkycResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateOtpForOkycRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateOtpForOkycResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateOtpForOkycRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateOtpForOkycResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLeadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLeadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelLeadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelLeadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelLeadErrorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeClosureDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeClosureDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateApplicantUdfRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateApplicantUdfResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRepaymentScheduleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRepaymentScheduleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditLineDetailsResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditLineDetailsResponse_Data_CreditLineDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditLineDetailsResponse_Data_CreditLineSchemes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPersonalDetailsResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLimitResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLimitResponse_Data_LimitValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicantLookupResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicantLookupResponse_Data_Details); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicantLookupResponse_Data_Activation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateLinkResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateStatusResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MakeDrawdownResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPdfAgreementResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendBorrowerAgreementOtpResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyBorrowerAgreementOtpResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanStatusResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanStatusResponse_Data_Status); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyAndDownloadCkycResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyAndDownloadCkycResponse_Data_PersonalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyAndDownloadCkycResponse_Data_PersonalDetails_Address); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleErrorResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErrorResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadDocumentResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveCollectionRequest_PaymentSchedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveCollectionResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HashGenerationForOkycResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaptchaGenerationForOkycResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateOtpForOkycResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateOtpForOkycResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLeadRequest_SchemeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLeadResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelLeadResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeClosureDetailsResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRepaymentScheduleRequest_Schedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRepaymentScheduleResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   93,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_liquiloans_lending_preapproved_loan_proto_goTypes,
		DependencyIndexes: file_api_vendors_liquiloans_lending_preapproved_loan_proto_depIdxs,
		MessageInfos:      file_api_vendors_liquiloans_lending_preapproved_loan_proto_msgTypes,
	}.Build()
	File_api_vendors_liquiloans_lending_preapproved_loan_proto = out.File
	file_api_vendors_liquiloans_lending_preapproved_loan_proto_rawDesc = nil
	file_api_vendors_liquiloans_lending_preapproved_loan_proto_goTypes = nil
	file_api_vendors_liquiloans_lending_preapproved_loan_proto_depIdxs = nil
}
