// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/finflux/auth_token.proto

package finflux

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GenerateAuthTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId  string `protobuf:"bytes,1,opt,name=client_id,proto3" json:"client_id,omitempty"`
	GrantType string `protobuf:"bytes,2,opt,name=grant_type,proto3" json:"grant_type,omitempty"`
	Username  string `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password  string `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	// "true" or "false"
	IsPasswordEncrypted string `protobuf:"bytes,5,opt,name=is_password_encrypted,json=isPasswordEncrypted,proto3" json:"is_password_encrypted,omitempty"`
}

func (x *GenerateAuthTokenRequest) Reset() {
	*x = GenerateAuthTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_finflux_auth_token_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateAuthTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateAuthTokenRequest) ProtoMessage() {}

func (x *GenerateAuthTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_finflux_auth_token_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateAuthTokenRequest.ProtoReflect.Descriptor instead.
func (*GenerateAuthTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_finflux_auth_token_proto_rawDescGZIP(), []int{0}
}

func (x *GenerateAuthTokenRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *GenerateAuthTokenRequest) GetGrantType() string {
	if x != nil {
		return x.GrantType
	}
	return ""
}

func (x *GenerateAuthTokenRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *GenerateAuthTokenRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *GenerateAuthTokenRequest) GetIsPasswordEncrypted() string {
	if x != nil {
		return x.IsPasswordEncrypted
	}
	return ""
}

// Example JSON:
//
//	{
//	    "access_token": "TlEjL-AV1zg3t3dL64mt3K2OnDQ",
//	    "token_type": "bearer",
//	    "refresh_token": "NcPY3w352Q_x3mj_tdNPAMEdwIg",
//	    "expires_in": 31535999,
//	    "scope": "access-token"
//	}
type GenerateAuthTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessToken      string `protobuf:"bytes,1,opt,name=access_token,proto3" json:"access_token,omitempty"`
	TokenType        string `protobuf:"bytes,2,opt,name=token_type,proto3" json:"token_type,omitempty"`
	RefreshToken     string `protobuf:"bytes,3,opt,name=refresh_token,proto3" json:"refresh_token,omitempty"`
	ExpiresInSeconds int32  `protobuf:"varint,4,opt,name=expires_in_seconds,json=expires_in,proto3" json:"expires_in_seconds,omitempty"`
	Scope            string `protobuf:"bytes,5,opt,name=scope,proto3" json:"scope,omitempty"`
}

func (x *GenerateAuthTokenResponse) Reset() {
	*x = GenerateAuthTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_finflux_auth_token_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateAuthTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateAuthTokenResponse) ProtoMessage() {}

func (x *GenerateAuthTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_finflux_auth_token_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateAuthTokenResponse.ProtoReflect.Descriptor instead.
func (*GenerateAuthTokenResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_finflux_auth_token_proto_rawDescGZIP(), []int{1}
}

func (x *GenerateAuthTokenResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *GenerateAuthTokenResponse) GetTokenType() string {
	if x != nil {
		return x.TokenType
	}
	return ""
}

func (x *GenerateAuthTokenResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *GenerateAuthTokenResponse) GetExpiresInSeconds() int32 {
	if x != nil {
		return x.ExpiresInSeconds
	}
	return 0
}

func (x *GenerateAuthTokenResponse) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

var File_api_vendors_finflux_auth_token_proto protoreflect.FileDescriptor

var file_api_vendors_finflux_auth_token_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x69,
	0x6e, 0x66, 0x6c, 0x75, 0x78, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x78, 0x22, 0xc4, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x73,
	0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x69, 0x73, 0x50, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x22, 0xc3,
	0x01, 0x0a, 0x19, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x68, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x26, 0x0a, 0x12, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x66, 0x6c,
	0x75, 0x78, 0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x78, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_finflux_auth_token_proto_rawDescOnce sync.Once
	file_api_vendors_finflux_auth_token_proto_rawDescData = file_api_vendors_finflux_auth_token_proto_rawDesc
)

func file_api_vendors_finflux_auth_token_proto_rawDescGZIP() []byte {
	file_api_vendors_finflux_auth_token_proto_rawDescOnce.Do(func() {
		file_api_vendors_finflux_auth_token_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_finflux_auth_token_proto_rawDescData)
	})
	return file_api_vendors_finflux_auth_token_proto_rawDescData
}

var file_api_vendors_finflux_auth_token_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_vendors_finflux_auth_token_proto_goTypes = []interface{}{
	(*GenerateAuthTokenRequest)(nil),  // 0: vendors.finflux.GenerateAuthTokenRequest
	(*GenerateAuthTokenResponse)(nil), // 1: vendors.finflux.GenerateAuthTokenResponse
}
var file_api_vendors_finflux_auth_token_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendors_finflux_auth_token_proto_init() }
func file_api_vendors_finflux_auth_token_proto_init() {
	if File_api_vendors_finflux_auth_token_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_finflux_auth_token_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateAuthTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_finflux_auth_token_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateAuthTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_finflux_auth_token_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_finflux_auth_token_proto_goTypes,
		DependencyIndexes: file_api_vendors_finflux_auth_token_proto_depIdxs,
		MessageInfos:      file_api_vendors_finflux_auth_token_proto_msgTypes,
	}.Build()
	File_api_vendors_finflux_auth_token_proto = out.File
	file_api_vendors_finflux_auth_token_proto_rawDesc = nil
	file_api_vendors_finflux_auth_token_proto_goTypes = nil
	file_api_vendors_finflux_auth_token_proto_depIdxs = nil
}
