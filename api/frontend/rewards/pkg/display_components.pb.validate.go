// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/rewards/pkg/display_components.proto

package pkg

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Cta with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Cta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Cta with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CtaMultiError, or nil if none found.
func (m *Cta) ValidateAll() error {
	return m.validate(true)
}

func (m *Cta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetItc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CtaValidationError{
					field:  "Itc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CtaValidationError{
					field:  "Itc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetItc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CtaValidationError{
				field:  "Itc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetShadow()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CtaValidationError{
					field:  "Shadow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CtaValidationError{
					field:  "Shadow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShadow()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CtaValidationError{
				field:  "Shadow",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Action.(type) {
	case *Cta_DeeplinkAction:
		if v == nil {
			err := CtaValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDeeplinkAction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CtaValidationError{
						field:  "DeeplinkAction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CtaValidationError{
						field:  "DeeplinkAction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeeplinkAction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CtaValidationError{
					field:  "DeeplinkAction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Cta_CustomAction:
		if v == nil {
			err := CtaValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCustomAction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CtaValidationError{
						field:  "CustomAction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CtaValidationError{
						field:  "CustomAction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCustomAction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CtaValidationError{
					field:  "CustomAction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CtaMultiError(errors)
	}

	return nil
}

// CtaMultiError is an error wrapping multiple validation errors returned by
// Cta.ValidateAll() if the designated constraints aren't met.
type CtaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CtaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CtaMultiError) AllErrors() []error { return m }

// CtaValidationError is the validation error returned by Cta.Validate if the
// designated constraints aren't met.
type CtaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CtaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CtaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CtaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CtaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CtaValidationError) ErrorName() string { return "CtaValidationError" }

// Error satisfies the builtin error interface
func (e CtaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CtaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CtaValidationError{}

// Validate checks the field values on CustomAction with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CustomAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomAction with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CustomActionMultiError, or
// nil if none found.
func (m *CustomAction) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActionType

	// no validation rules for ActionApi

	switch v := m.ActionData.(type) {
	case *CustomAction_GetRedeemOfferInputScreenApiActionData_:
		if v == nil {
			err := CustomActionValidationError{
				field:  "ActionData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGetRedeemOfferInputScreenApiActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomActionValidationError{
						field:  "GetRedeemOfferInputScreenApiActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomActionValidationError{
						field:  "GetRedeemOfferInputScreenApiActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGetRedeemOfferInputScreenApiActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomActionValidationError{
					field:  "GetRedeemOfferInputScreenApiActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomAction_GetInitiateRedemptionApiActionData_:
		if v == nil {
			err := CustomActionValidationError{
				field:  "ActionData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGetInitiateRedemptionApiActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomActionValidationError{
						field:  "GetInitiateRedemptionApiActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomActionValidationError{
						field:  "GetInitiateRedemptionApiActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGetInitiateRedemptionApiActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomActionValidationError{
					field:  "GetInitiateRedemptionApiActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CustomActionMultiError(errors)
	}

	return nil
}

// CustomActionMultiError is an error wrapping multiple validation errors
// returned by CustomAction.ValidateAll() if the designated constraints aren't met.
type CustomActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomActionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomActionMultiError) AllErrors() []error { return m }

// CustomActionValidationError is the validation error returned by
// CustomAction.Validate if the designated constraints aren't met.
type CustomActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomActionValidationError) ErrorName() string { return "CustomActionValidationError" }

// Error satisfies the builtin error interface
func (e CustomActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomActionValidationError{}

// Validate checks the field values on Banner with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Banner) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Banner with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in BannerMultiError, or nil if none found.
func (m *Banner) ValidateAll() error {
	return m.validate(true)
}

func (m *Banner) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeftVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "LeftVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "LeftVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BannerValidationError{
				field:  "LeftVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BannerValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "RightVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "RightVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BannerValidationError{
				field:  "RightVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIndicatorDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "IndicatorDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "IndicatorDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIndicatorDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BannerValidationError{
				field:  "IndicatorDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "BackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "BackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BannerValidationError{
				field:  "BackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetShadow()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "Shadow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "Shadow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShadow()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BannerValidationError{
				field:  "Shadow",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BannerValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAnalyticsDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "AnalyticsDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BannerValidationError{
					field:  "AnalyticsDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnalyticsDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BannerValidationError{
				field:  "AnalyticsDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BannerMultiError(errors)
	}

	return nil
}

// BannerMultiError is an error wrapping multiple validation errors returned by
// Banner.ValidateAll() if the designated constraints aren't met.
type BannerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BannerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BannerMultiError) AllErrors() []error { return m }

// BannerValidationError is the validation error returned by Banner.Validate if
// the designated constraints aren't met.
type BannerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BannerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BannerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BannerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BannerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BannerValidationError) ErrorName() string { return "BannerValidationError" }

// Error satisfies the builtin error interface
func (e BannerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBanner.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BannerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BannerValidationError{}

// Validate checks the field values on ScrollBehaviour with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ScrollBehaviour) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScrollBehaviour with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScrollBehaviourMultiError, or nil if none found.
func (m *ScrollBehaviour) ValidateAll() error {
	return m.validate(true)
}

func (m *ScrollBehaviour) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ScrollOrientation

	// no validation rules for ScrollMode

	// no validation rules for ScrollType

	switch v := m.ScrollTypeData.(type) {
	case *ScrollBehaviour_ScrollTypeAutoScrollData_:
		if v == nil {
			err := ScrollBehaviourValidationError{
				field:  "ScrollTypeData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScrollTypeAutoScrollData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ScrollBehaviourValidationError{
						field:  "ScrollTypeAutoScrollData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ScrollBehaviourValidationError{
						field:  "ScrollTypeAutoScrollData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScrollTypeAutoScrollData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ScrollBehaviourValidationError{
					field:  "ScrollTypeAutoScrollData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ScrollBehaviourMultiError(errors)
	}

	return nil
}

// ScrollBehaviourMultiError is an error wrapping multiple validation errors
// returned by ScrollBehaviour.ValidateAll() if the designated constraints
// aren't met.
type ScrollBehaviourMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScrollBehaviourMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScrollBehaviourMultiError) AllErrors() []error { return m }

// ScrollBehaviourValidationError is the validation error returned by
// ScrollBehaviour.Validate if the designated constraints aren't met.
type ScrollBehaviourValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScrollBehaviourValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScrollBehaviourValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScrollBehaviourValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScrollBehaviourValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScrollBehaviourValidationError) ErrorName() string { return "ScrollBehaviourValidationError" }

// Error satisfies the builtin error interface
func (e ScrollBehaviourValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScrollBehaviour.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScrollBehaviourValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScrollBehaviourValidationError{}

// Validate checks the field values on
// CustomAction_GetRedeemOfferInputScreenApiActionData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CustomAction_GetRedeemOfferInputScreenApiActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CustomAction_GetRedeemOfferInputScreenApiActionData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CustomAction_GetRedeemOfferInputScreenApiActionDataMultiError, or nil if
// none found.
func (m *CustomAction_GetRedeemOfferInputScreenApiActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomAction_GetRedeemOfferInputScreenApiActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	// no validation rules for RedemptionPrice

	if len(errors) > 0 {
		return CustomAction_GetRedeemOfferInputScreenApiActionDataMultiError(errors)
	}

	return nil
}

// CustomAction_GetRedeemOfferInputScreenApiActionDataMultiError is an error
// wrapping multiple validation errors returned by
// CustomAction_GetRedeemOfferInputScreenApiActionData.ValidateAll() if the
// designated constraints aren't met.
type CustomAction_GetRedeemOfferInputScreenApiActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomAction_GetRedeemOfferInputScreenApiActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomAction_GetRedeemOfferInputScreenApiActionDataMultiError) AllErrors() []error { return m }

// CustomAction_GetRedeemOfferInputScreenApiActionDataValidationError is the
// validation error returned by
// CustomAction_GetRedeemOfferInputScreenApiActionData.Validate if the
// designated constraints aren't met.
type CustomAction_GetRedeemOfferInputScreenApiActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomAction_GetRedeemOfferInputScreenApiActionDataValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CustomAction_GetRedeemOfferInputScreenApiActionDataValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CustomAction_GetRedeemOfferInputScreenApiActionDataValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CustomAction_GetRedeemOfferInputScreenApiActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomAction_GetRedeemOfferInputScreenApiActionDataValidationError) ErrorName() string {
	return "CustomAction_GetRedeemOfferInputScreenApiActionDataValidationError"
}

// Error satisfies the builtin error interface
func (e CustomAction_GetRedeemOfferInputScreenApiActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomAction_GetRedeemOfferInputScreenApiActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomAction_GetRedeemOfferInputScreenApiActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomAction_GetRedeemOfferInputScreenApiActionDataValidationError{}

// Validate checks the field values on
// CustomAction_GetInitiateRedemptionApiActionData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CustomAction_GetInitiateRedemptionApiActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CustomAction_GetInitiateRedemptionApiActionData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CustomAction_GetInitiateRedemptionApiActionDataMultiError, or nil if none found.
func (m *CustomAction_GetInitiateRedemptionApiActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomAction_GetInitiateRedemptionApiActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	if len(errors) > 0 {
		return CustomAction_GetInitiateRedemptionApiActionDataMultiError(errors)
	}

	return nil
}

// CustomAction_GetInitiateRedemptionApiActionDataMultiError is an error
// wrapping multiple validation errors returned by
// CustomAction_GetInitiateRedemptionApiActionData.ValidateAll() if the
// designated constraints aren't met.
type CustomAction_GetInitiateRedemptionApiActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomAction_GetInitiateRedemptionApiActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomAction_GetInitiateRedemptionApiActionDataMultiError) AllErrors() []error { return m }

// CustomAction_GetInitiateRedemptionApiActionDataValidationError is the
// validation error returned by
// CustomAction_GetInitiateRedemptionApiActionData.Validate if the designated
// constraints aren't met.
type CustomAction_GetInitiateRedemptionApiActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomAction_GetInitiateRedemptionApiActionDataValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CustomAction_GetInitiateRedemptionApiActionDataValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CustomAction_GetInitiateRedemptionApiActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomAction_GetInitiateRedemptionApiActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomAction_GetInitiateRedemptionApiActionDataValidationError) ErrorName() string {
	return "CustomAction_GetInitiateRedemptionApiActionDataValidationError"
}

// Error satisfies the builtin error interface
func (e CustomAction_GetInitiateRedemptionApiActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomAction_GetInitiateRedemptionApiActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomAction_GetInitiateRedemptionApiActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomAction_GetInitiateRedemptionApiActionDataValidationError{}

// Validate checks the field values on Banner_PageControlDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Banner_PageControlDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Banner_PageControlDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Banner_PageControlDetailsMultiError, or nil if none found.
func (m *Banner_PageControlDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *Banner_PageControlDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDefaultIndicatorColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Banner_PageControlDetailsValidationError{
					field:  "DefaultIndicatorColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Banner_PageControlDetailsValidationError{
					field:  "DefaultIndicatorColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultIndicatorColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Banner_PageControlDetailsValidationError{
				field:  "DefaultIndicatorColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSelectedIndicatorColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Banner_PageControlDetailsValidationError{
					field:  "SelectedIndicatorColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Banner_PageControlDetailsValidationError{
					field:  "SelectedIndicatorColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectedIndicatorColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Banner_PageControlDetailsValidationError{
				field:  "SelectedIndicatorColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return Banner_PageControlDetailsMultiError(errors)
	}

	return nil
}

// Banner_PageControlDetailsMultiError is an error wrapping multiple validation
// errors returned by Banner_PageControlDetails.ValidateAll() if the
// designated constraints aren't met.
type Banner_PageControlDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Banner_PageControlDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Banner_PageControlDetailsMultiError) AllErrors() []error { return m }

// Banner_PageControlDetailsValidationError is the validation error returned by
// Banner_PageControlDetails.Validate if the designated constraints aren't met.
type Banner_PageControlDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Banner_PageControlDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Banner_PageControlDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Banner_PageControlDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Banner_PageControlDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Banner_PageControlDetailsValidationError) ErrorName() string {
	return "Banner_PageControlDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e Banner_PageControlDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBanner_PageControlDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Banner_PageControlDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Banner_PageControlDetailsValidationError{}

// Validate checks the field values on Banner_AnalyticsDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Banner_AnalyticsDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Banner_AnalyticsDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Banner_AnalyticsDetailsMultiError, or nil if none found.
func (m *Banner_AnalyticsDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *Banner_AnalyticsDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EventProperties

	if len(errors) > 0 {
		return Banner_AnalyticsDetailsMultiError(errors)
	}

	return nil
}

// Banner_AnalyticsDetailsMultiError is an error wrapping multiple validation
// errors returned by Banner_AnalyticsDetails.ValidateAll() if the designated
// constraints aren't met.
type Banner_AnalyticsDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Banner_AnalyticsDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Banner_AnalyticsDetailsMultiError) AllErrors() []error { return m }

// Banner_AnalyticsDetailsValidationError is the validation error returned by
// Banner_AnalyticsDetails.Validate if the designated constraints aren't met.
type Banner_AnalyticsDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Banner_AnalyticsDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Banner_AnalyticsDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Banner_AnalyticsDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Banner_AnalyticsDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Banner_AnalyticsDetailsValidationError) ErrorName() string {
	return "Banner_AnalyticsDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e Banner_AnalyticsDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBanner_AnalyticsDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Banner_AnalyticsDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Banner_AnalyticsDetailsValidationError{}

// Validate checks the field values on ScrollBehaviour_ScrollTypeAutoScrollData
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ScrollBehaviour_ScrollTypeAutoScrollData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ScrollBehaviour_ScrollTypeAutoScrollData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ScrollBehaviour_ScrollTypeAutoScrollDataMultiError, or nil if none found.
func (m *ScrollBehaviour_ScrollTypeAutoScrollData) ValidateAll() error {
	return m.validate(true)
}

func (m *ScrollBehaviour_ScrollTypeAutoScrollData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FirstScrollDelay

	// no validation rules for ScrollInterval

	if len(errors) > 0 {
		return ScrollBehaviour_ScrollTypeAutoScrollDataMultiError(errors)
	}

	return nil
}

// ScrollBehaviour_ScrollTypeAutoScrollDataMultiError is an error wrapping
// multiple validation errors returned by
// ScrollBehaviour_ScrollTypeAutoScrollData.ValidateAll() if the designated
// constraints aren't met.
type ScrollBehaviour_ScrollTypeAutoScrollDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScrollBehaviour_ScrollTypeAutoScrollDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScrollBehaviour_ScrollTypeAutoScrollDataMultiError) AllErrors() []error { return m }

// ScrollBehaviour_ScrollTypeAutoScrollDataValidationError is the validation
// error returned by ScrollBehaviour_ScrollTypeAutoScrollData.Validate if the
// designated constraints aren't met.
type ScrollBehaviour_ScrollTypeAutoScrollDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScrollBehaviour_ScrollTypeAutoScrollDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScrollBehaviour_ScrollTypeAutoScrollDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScrollBehaviour_ScrollTypeAutoScrollDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScrollBehaviour_ScrollTypeAutoScrollDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScrollBehaviour_ScrollTypeAutoScrollDataValidationError) ErrorName() string {
	return "ScrollBehaviour_ScrollTypeAutoScrollDataValidationError"
}

// Error satisfies the builtin error interface
func (e ScrollBehaviour_ScrollTypeAutoScrollDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScrollBehaviour_ScrollTypeAutoScrollData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScrollBehaviour_ScrollTypeAutoScrollDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScrollBehaviour_ScrollTypeAutoScrollDataValidationError{}
