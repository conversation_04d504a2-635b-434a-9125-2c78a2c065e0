// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/securities/catalog/service.proto

package catalog

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetSecurityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecurityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecurityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecurityRequestMultiError, or nil if none found.
func (m *GetSecurityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecurityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetSecurityRequestMultiError(errors)
	}

	return nil
}

// GetSecurityRequestMultiError is an error wrapping multiple validation errors
// returned by GetSecurityRequest.ValidateAll() if the designated constraints
// aren't met.
type GetSecurityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecurityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecurityRequestMultiError) AllErrors() []error { return m }

// GetSecurityRequestValidationError is the validation error returned by
// GetSecurityRequest.Validate if the designated constraints aren't met.
type GetSecurityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecurityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecurityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecurityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecurityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecurityRequestValidationError) ErrorName() string {
	return "GetSecurityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecurityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecurityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecurityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecurityRequestValidationError{}

// Validate checks the field values on GetSecurityResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecurityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecurityResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecurityResponseMultiError, or nil if none found.
func (m *GetSecurityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecurityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecurityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecurityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecurityResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSecurity()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecurityResponseValidationError{
					field:  "Security",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecurityResponseValidationError{
					field:  "Security",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecurity()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecurityResponseValidationError{
				field:  "Security",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSecurityResponseMultiError(errors)
	}

	return nil
}

// GetSecurityResponseMultiError is an error wrapping multiple validation
// errors returned by GetSecurityResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSecurityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecurityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecurityResponseMultiError) AllErrors() []error { return m }

// GetSecurityResponseValidationError is the validation error returned by
// GetSecurityResponse.Validate if the designated constraints aren't met.
type GetSecurityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecurityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecurityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecurityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecurityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecurityResponseValidationError) ErrorName() string {
	return "GetSecurityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecurityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecurityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecurityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecurityResponseValidationError{}

// Validate checks the field values on GetSecuritiesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecuritiesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecuritiesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecuritiesRequestMultiError, or nil if none found.
func (m *GetSecuritiesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecuritiesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetSecuritiesRequestMultiError(errors)
	}

	return nil
}

// GetSecuritiesRequestMultiError is an error wrapping multiple validation
// errors returned by GetSecuritiesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSecuritiesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecuritiesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecuritiesRequestMultiError) AllErrors() []error { return m }

// GetSecuritiesRequestValidationError is the validation error returned by
// GetSecuritiesRequest.Validate if the designated constraints aren't met.
type GetSecuritiesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecuritiesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecuritiesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecuritiesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecuritiesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecuritiesRequestValidationError) ErrorName() string {
	return "GetSecuritiesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecuritiesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecuritiesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecuritiesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecuritiesRequestValidationError{}

// Validate checks the field values on GetSecuritiesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecuritiesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecuritiesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecuritiesResponseMultiError, or nil if none found.
func (m *GetSecuritiesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecuritiesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecuritiesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecuritiesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecuritiesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetSecuritiesMap()))
		i := 0
		for key := range m.GetSecuritiesMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetSecuritiesMap()[key]
			_ = val

			// no validation rules for SecuritiesMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetSecuritiesResponseValidationError{
							field:  fmt.Sprintf("SecuritiesMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetSecuritiesResponseValidationError{
							field:  fmt.Sprintf("SecuritiesMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetSecuritiesResponseValidationError{
						field:  fmt.Sprintf("SecuritiesMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetSecuritiesResponseMultiError(errors)
	}

	return nil
}

// GetSecuritiesResponseMultiError is an error wrapping multiple validation
// errors returned by GetSecuritiesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSecuritiesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecuritiesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecuritiesResponseMultiError) AllErrors() []error { return m }

// GetSecuritiesResponseValidationError is the validation error returned by
// GetSecuritiesResponse.Validate if the designated constraints aren't met.
type GetSecuritiesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecuritiesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecuritiesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecuritiesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecuritiesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecuritiesResponseValidationError) ErrorName() string {
	return "GetSecuritiesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecuritiesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecuritiesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecuritiesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecuritiesResponseValidationError{}

// Validate checks the field values on ExchangeSymbol with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExchangeSymbol) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExchangeSymbol with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExchangeSymbolMultiError,
// or nil if none found.
func (m *ExchangeSymbol) ValidateAll() error {
	return m.validate(true)
}

func (m *ExchangeSymbol) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Symbol

	// no validation rules for Exchange

	if len(errors) > 0 {
		return ExchangeSymbolMultiError(errors)
	}

	return nil
}

// ExchangeSymbolMultiError is an error wrapping multiple validation errors
// returned by ExchangeSymbol.ValidateAll() if the designated constraints
// aren't met.
type ExchangeSymbolMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExchangeSymbolMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExchangeSymbolMultiError) AllErrors() []error { return m }

// ExchangeSymbolValidationError is the validation error returned by
// ExchangeSymbol.Validate if the designated constraints aren't met.
type ExchangeSymbolValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExchangeSymbolValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExchangeSymbolValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExchangeSymbolValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExchangeSymbolValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExchangeSymbolValidationError) ErrorName() string { return "ExchangeSymbolValidationError" }

// Error satisfies the builtin error interface
func (e ExchangeSymbolValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExchangeSymbol.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExchangeSymbolValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExchangeSymbolValidationError{}

// Validate checks the field values on GetSecurityListingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecurityListingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecurityListingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecurityListingRequestMultiError, or nil if none found.
func (m *GetSecurityListingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecurityListingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *GetSecurityListingRequest_ExternalId:
		if v == nil {
			err := GetSecurityListingRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ExternalId
	case *GetSecurityListingRequest_ExchangeSymbol:
		if v == nil {
			err := GetSecurityListingRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExchangeSymbol()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSecurityListingRequestValidationError{
						field:  "ExchangeSymbol",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSecurityListingRequestValidationError{
						field:  "ExchangeSymbol",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExchangeSymbol()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSecurityListingRequestValidationError{
					field:  "ExchangeSymbol",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetSecurityListingRequestMultiError(errors)
	}

	return nil
}

// GetSecurityListingRequestMultiError is an error wrapping multiple validation
// errors returned by GetSecurityListingRequest.ValidateAll() if the
// designated constraints aren't met.
type GetSecurityListingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecurityListingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecurityListingRequestMultiError) AllErrors() []error { return m }

// GetSecurityListingRequestValidationError is the validation error returned by
// GetSecurityListingRequest.Validate if the designated constraints aren't met.
type GetSecurityListingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecurityListingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecurityListingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecurityListingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecurityListingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecurityListingRequestValidationError) ErrorName() string {
	return "GetSecurityListingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecurityListingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecurityListingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecurityListingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecurityListingRequestValidationError{}

// Validate checks the field values on GetSecurityListingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecurityListingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecurityListingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecurityListingResponseMultiError, or nil if none found.
func (m *GetSecurityListingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecurityListingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecurityListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecurityListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecurityListingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSecurity()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecurityListingResponseValidationError{
					field:  "Security",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecurityListingResponseValidationError{
					field:  "Security",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecurity()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecurityListingResponseValidationError{
				field:  "Security",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSecurityListing()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecurityListingResponseValidationError{
					field:  "SecurityListing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecurityListingResponseValidationError{
					field:  "SecurityListing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecurityListing()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecurityListingResponseValidationError{
				field:  "SecurityListing",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSecurityListingResponseMultiError(errors)
	}

	return nil
}

// GetSecurityListingResponseMultiError is an error wrapping multiple
// validation errors returned by GetSecurityListingResponse.ValidateAll() if
// the designated constraints aren't met.
type GetSecurityListingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecurityListingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecurityListingResponseMultiError) AllErrors() []error { return m }

// GetSecurityListingResponseValidationError is the validation error returned
// by GetSecurityListingResponse.Validate if the designated constraints aren't met.
type GetSecurityListingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecurityListingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecurityListingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecurityListingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecurityListingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecurityListingResponseValidationError) ErrorName() string {
	return "GetSecurityListingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecurityListingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecurityListingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecurityListingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecurityListingResponseValidationError{}

// Validate checks the field values on GetSecurityListingsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecurityListingsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecurityListingsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecurityListingsRequestMultiError, or nil if none found.
func (m *GetSecurityListingsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecurityListingsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifiers.(type) {
	case *GetSecurityListingsRequest_ExternalIds_:
		if v == nil {
			err := GetSecurityListingsRequestValidationError{
				field:  "Identifiers",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExternalIds()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSecurityListingsRequestValidationError{
						field:  "ExternalIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSecurityListingsRequestValidationError{
						field:  "ExternalIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExternalIds()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSecurityListingsRequestValidationError{
					field:  "ExternalIds",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetSecurityListingsRequest_ExchangeSymbols_:
		if v == nil {
			err := GetSecurityListingsRequestValidationError{
				field:  "Identifiers",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExchangeSymbols()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSecurityListingsRequestValidationError{
						field:  "ExchangeSymbols",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSecurityListingsRequestValidationError{
						field:  "ExchangeSymbols",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExchangeSymbols()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSecurityListingsRequestValidationError{
					field:  "ExchangeSymbols",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetSecurityListingsRequestMultiError(errors)
	}

	return nil
}

// GetSecurityListingsRequestMultiError is an error wrapping multiple
// validation errors returned by GetSecurityListingsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetSecurityListingsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecurityListingsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecurityListingsRequestMultiError) AllErrors() []error { return m }

// GetSecurityListingsRequestValidationError is the validation error returned
// by GetSecurityListingsRequest.Validate if the designated constraints aren't met.
type GetSecurityListingsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecurityListingsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecurityListingsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecurityListingsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecurityListingsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecurityListingsRequestValidationError) ErrorName() string {
	return "GetSecurityListingsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecurityListingsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecurityListingsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecurityListingsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecurityListingsRequestValidationError{}

// Validate checks the field values on GetSecurityListingsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecurityListingsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecurityListingsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecurityListingsResponseMultiError, or nil if none found.
func (m *GetSecurityListingsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecurityListingsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecurityListingsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecurityListingsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecurityListingsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSecurityAndSecurityListings() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSecurityListingsResponseValidationError{
						field:  fmt.Sprintf("SecurityAndSecurityListings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSecurityListingsResponseValidationError{
						field:  fmt.Sprintf("SecurityAndSecurityListings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSecurityListingsResponseValidationError{
					field:  fmt.Sprintf("SecurityAndSecurityListings[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetSecurityListingsResponseMultiError(errors)
	}

	return nil
}

// GetSecurityListingsResponseMultiError is an error wrapping multiple
// validation errors returned by GetSecurityListingsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetSecurityListingsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecurityListingsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecurityListingsResponseMultiError) AllErrors() []error { return m }

// GetSecurityListingsResponseValidationError is the validation error returned
// by GetSecurityListingsResponse.Validate if the designated constraints
// aren't met.
type GetSecurityListingsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecurityListingsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecurityListingsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecurityListingsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecurityListingsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecurityListingsResponseValidationError) ErrorName() string {
	return "GetSecurityListingsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecurityListingsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecurityListingsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecurityListingsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecurityListingsResponseValidationError{}

// Validate checks the field values on SecurityAndSecurityListing with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecurityAndSecurityListing) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecurityAndSecurityListing with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecurityAndSecurityListingMultiError, or nil if none found.
func (m *SecurityAndSecurityListing) ValidateAll() error {
	return m.validate(true)
}

func (m *SecurityAndSecurityListing) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSecurity()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityAndSecurityListingValidationError{
					field:  "Security",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityAndSecurityListingValidationError{
					field:  "Security",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecurity()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityAndSecurityListingValidationError{
				field:  "Security",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSecurityListing()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityAndSecurityListingValidationError{
					field:  "SecurityListing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityAndSecurityListingValidationError{
					field:  "SecurityListing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecurityListing()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityAndSecurityListingValidationError{
				field:  "SecurityListing",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SecurityAndSecurityListingMultiError(errors)
	}

	return nil
}

// SecurityAndSecurityListingMultiError is an error wrapping multiple
// validation errors returned by SecurityAndSecurityListing.ValidateAll() if
// the designated constraints aren't met.
type SecurityAndSecurityListingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecurityAndSecurityListingMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecurityAndSecurityListingMultiError) AllErrors() []error { return m }

// SecurityAndSecurityListingValidationError is the validation error returned
// by SecurityAndSecurityListing.Validate if the designated constraints aren't met.
type SecurityAndSecurityListingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecurityAndSecurityListingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecurityAndSecurityListingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecurityAndSecurityListingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecurityAndSecurityListingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecurityAndSecurityListingValidationError) ErrorName() string {
	return "SecurityAndSecurityListingValidationError"
}

// Error satisfies the builtin error interface
func (e SecurityAndSecurityListingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecurityAndSecurityListing.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecurityAndSecurityListingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecurityAndSecurityListingValidationError{}

// Validate checks the field values on GetPriceByDateAndSecListingIDsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPriceByDateAndSecListingIDsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPriceByDateAndSecListingIDsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPriceByDateAndSecListingIDsRequestMultiError, or nil if none found.
func (m *GetPriceByDateAndSecListingIDsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPriceByDateAndSecListingIDsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPriceDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPriceByDateAndSecListingIDsRequestValidationError{
					field:  "PriceDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPriceByDateAndSecListingIDsRequestValidationError{
					field:  "PriceDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPriceDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPriceByDateAndSecListingIDsRequestValidationError{
				field:  "PriceDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPriceByDateAndSecListingIDsRequestMultiError(errors)
	}

	return nil
}

// GetPriceByDateAndSecListingIDsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetPriceByDateAndSecListingIDsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPriceByDateAndSecListingIDsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPriceByDateAndSecListingIDsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPriceByDateAndSecListingIDsRequestMultiError) AllErrors() []error { return m }

// GetPriceByDateAndSecListingIDsRequestValidationError is the validation error
// returned by GetPriceByDateAndSecListingIDsRequest.Validate if the
// designated constraints aren't met.
type GetPriceByDateAndSecListingIDsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPriceByDateAndSecListingIDsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPriceByDateAndSecListingIDsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPriceByDateAndSecListingIDsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPriceByDateAndSecListingIDsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPriceByDateAndSecListingIDsRequestValidationError) ErrorName() string {
	return "GetPriceByDateAndSecListingIDsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPriceByDateAndSecListingIDsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPriceByDateAndSecListingIDsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPriceByDateAndSecListingIDsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPriceByDateAndSecListingIDsRequestValidationError{}

// Validate checks the field values on GetPriceByDateAndSecListingIDsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPriceByDateAndSecListingIDsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetPriceByDateAndSecListingIDsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetPriceByDateAndSecListingIDsResponseMultiError, or nil if none found.
func (m *GetPriceByDateAndSecListingIDsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPriceByDateAndSecListingIDsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPriceByDateAndSecListingIDsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPriceByDateAndSecListingIDsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPriceByDateAndSecListingIDsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetPrices()))
		i := 0
		for key := range m.GetPrices() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetPrices()[key]
			_ = val

			// no validation rules for Prices[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetPriceByDateAndSecListingIDsResponseValidationError{
							field:  fmt.Sprintf("Prices[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetPriceByDateAndSecListingIDsResponseValidationError{
							field:  fmt.Sprintf("Prices[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetPriceByDateAndSecListingIDsResponseValidationError{
						field:  fmt.Sprintf("Prices[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetPriceByDateAndSecListingIDsResponseMultiError(errors)
	}

	return nil
}

// GetPriceByDateAndSecListingIDsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetPriceByDateAndSecListingIDsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPriceByDateAndSecListingIDsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPriceByDateAndSecListingIDsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPriceByDateAndSecListingIDsResponseMultiError) AllErrors() []error { return m }

// GetPriceByDateAndSecListingIDsResponseValidationError is the validation
// error returned by GetPriceByDateAndSecListingIDsResponse.Validate if the
// designated constraints aren't met.
type GetPriceByDateAndSecListingIDsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPriceByDateAndSecListingIDsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPriceByDateAndSecListingIDsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPriceByDateAndSecListingIDsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPriceByDateAndSecListingIDsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPriceByDateAndSecListingIDsResponseValidationError) ErrorName() string {
	return "GetPriceByDateAndSecListingIDsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPriceByDateAndSecListingIDsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPriceByDateAndSecListingIDsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPriceByDateAndSecListingIDsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPriceByDateAndSecListingIDsResponseValidationError{}

// Validate checks the field values on GetSecListingIdsByISINsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecListingIdsByISINsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecListingIdsByISINsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetSecListingIdsByISINsRequestMultiError, or nil if none found.
func (m *GetSecListingIdsByISINsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecListingIdsByISINsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetIsinExchangePairs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSecListingIdsByISINsRequestValidationError{
						field:  fmt.Sprintf("IsinExchangePairs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSecListingIdsByISINsRequestValidationError{
						field:  fmt.Sprintf("IsinExchangePairs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSecListingIdsByISINsRequestValidationError{
					field:  fmt.Sprintf("IsinExchangePairs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetSecListingIdsByISINsRequestMultiError(errors)
	}

	return nil
}

// GetSecListingIdsByISINsRequestMultiError is an error wrapping multiple
// validation errors returned by GetSecListingIdsByISINsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetSecListingIdsByISINsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecListingIdsByISINsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecListingIdsByISINsRequestMultiError) AllErrors() []error { return m }

// GetSecListingIdsByISINsRequestValidationError is the validation error
// returned by GetSecListingIdsByISINsRequest.Validate if the designated
// constraints aren't met.
type GetSecListingIdsByISINsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecListingIdsByISINsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecListingIdsByISINsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecListingIdsByISINsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecListingIdsByISINsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecListingIdsByISINsRequestValidationError) ErrorName() string {
	return "GetSecListingIdsByISINsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecListingIdsByISINsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecListingIdsByISINsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecListingIdsByISINsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecListingIdsByISINsRequestValidationError{}

// Validate checks the field values on ISINExchangePair with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ISINExchangePair) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ISINExchangePair with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ISINExchangePairMultiError, or nil if none found.
func (m *ISINExchangePair) ValidateAll() error {
	return m.validate(true)
}

func (m *ISINExchangePair) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Isin

	// no validation rules for Exchange

	if len(errors) > 0 {
		return ISINExchangePairMultiError(errors)
	}

	return nil
}

// ISINExchangePairMultiError is an error wrapping multiple validation errors
// returned by ISINExchangePair.ValidateAll() if the designated constraints
// aren't met.
type ISINExchangePairMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ISINExchangePairMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ISINExchangePairMultiError) AllErrors() []error { return m }

// ISINExchangePairValidationError is the validation error returned by
// ISINExchangePair.Validate if the designated constraints aren't met.
type ISINExchangePairValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ISINExchangePairValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ISINExchangePairValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ISINExchangePairValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ISINExchangePairValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ISINExchangePairValidationError) ErrorName() string { return "ISINExchangePairValidationError" }

// Error satisfies the builtin error interface
func (e ISINExchangePairValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sISINExchangePair.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ISINExchangePairValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ISINExchangePairValidationError{}

// Validate checks the field values on SecListingIdWithISIN with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecListingIdWithISIN) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecListingIdWithISIN with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecListingIdWithISINMultiError, or nil if none found.
func (m *SecListingIdWithISIN) ValidateAll() error {
	return m.validate(true)
}

func (m *SecListingIdWithISIN) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIsinExchangePair()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecListingIdWithISINValidationError{
					field:  "IsinExchangePair",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecListingIdWithISINValidationError{
					field:  "IsinExchangePair",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIsinExchangePair()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecListingIdWithISINValidationError{
				field:  "IsinExchangePair",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SecurityListingExternalId

	if len(errors) > 0 {
		return SecListingIdWithISINMultiError(errors)
	}

	return nil
}

// SecListingIdWithISINMultiError is an error wrapping multiple validation
// errors returned by SecListingIdWithISIN.ValidateAll() if the designated
// constraints aren't met.
type SecListingIdWithISINMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecListingIdWithISINMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecListingIdWithISINMultiError) AllErrors() []error { return m }

// SecListingIdWithISINValidationError is the validation error returned by
// SecListingIdWithISIN.Validate if the designated constraints aren't met.
type SecListingIdWithISINValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecListingIdWithISINValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecListingIdWithISINValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecListingIdWithISINValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecListingIdWithISINValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecListingIdWithISINValidationError) ErrorName() string {
	return "SecListingIdWithISINValidationError"
}

// Error satisfies the builtin error interface
func (e SecListingIdWithISINValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecListingIdWithISIN.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecListingIdWithISINValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecListingIdWithISINValidationError{}

// Validate checks the field values on GetSecListingIdsByISINsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecListingIdsByISINsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecListingIdsByISINsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetSecListingIdsByISINsResponseMultiError, or nil if none found.
func (m *GetSecListingIdsByISINsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecListingIdsByISINsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecListingIdsByISINsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecListingIdsByISINsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecListingIdsByISINsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSecListingIdsWithIsins() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSecListingIdsByISINsResponseValidationError{
						field:  fmt.Sprintf("SecListingIdsWithIsins[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSecListingIdsByISINsResponseValidationError{
						field:  fmt.Sprintf("SecListingIdsWithIsins[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSecListingIdsByISINsResponseValidationError{
					field:  fmt.Sprintf("SecListingIdsWithIsins[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetSecListingIdsByISINsResponseMultiError(errors)
	}

	return nil
}

// GetSecListingIdsByISINsResponseMultiError is an error wrapping multiple
// validation errors returned by GetSecListingIdsByISINsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetSecListingIdsByISINsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecListingIdsByISINsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecListingIdsByISINsResponseMultiError) AllErrors() []error { return m }

// GetSecListingIdsByISINsResponseValidationError is the validation error
// returned by GetSecListingIdsByISINsResponse.Validate if the designated
// constraints aren't met.
type GetSecListingIdsByISINsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecListingIdsByISINsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecListingIdsByISINsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecListingIdsByISINsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecListingIdsByISINsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecListingIdsByISINsResponseValidationError) ErrorName() string {
	return "GetSecListingIdsByISINsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecListingIdsByISINsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecListingIdsByISINsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecListingIdsByISINsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecListingIdsByISINsResponseValidationError{}

// Validate checks the field values on GetSecurityListingsRequest_ExternalIds
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetSecurityListingsRequest_ExternalIds) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetSecurityListingsRequest_ExternalIds with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetSecurityListingsRequest_ExternalIdsMultiError, or nil if none found.
func (m *GetSecurityListingsRequest_ExternalIds) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecurityListingsRequest_ExternalIds) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetSecurityListingsRequest_ExternalIdsMultiError(errors)
	}

	return nil
}

// GetSecurityListingsRequest_ExternalIdsMultiError is an error wrapping
// multiple validation errors returned by
// GetSecurityListingsRequest_ExternalIds.ValidateAll() if the designated
// constraints aren't met.
type GetSecurityListingsRequest_ExternalIdsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecurityListingsRequest_ExternalIdsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecurityListingsRequest_ExternalIdsMultiError) AllErrors() []error { return m }

// GetSecurityListingsRequest_ExternalIdsValidationError is the validation
// error returned by GetSecurityListingsRequest_ExternalIds.Validate if the
// designated constraints aren't met.
type GetSecurityListingsRequest_ExternalIdsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecurityListingsRequest_ExternalIdsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecurityListingsRequest_ExternalIdsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecurityListingsRequest_ExternalIdsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecurityListingsRequest_ExternalIdsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecurityListingsRequest_ExternalIdsValidationError) ErrorName() string {
	return "GetSecurityListingsRequest_ExternalIdsValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecurityListingsRequest_ExternalIdsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecurityListingsRequest_ExternalIds.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecurityListingsRequest_ExternalIdsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecurityListingsRequest_ExternalIdsValidationError{}

// Validate checks the field values on
// GetSecurityListingsRequest_ExchangeSymbols with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetSecurityListingsRequest_ExchangeSymbols) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetSecurityListingsRequest_ExchangeSymbols with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetSecurityListingsRequest_ExchangeSymbolsMultiError, or nil if none found.
func (m *GetSecurityListingsRequest_ExchangeSymbols) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecurityListingsRequest_ExchangeSymbols) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetExchangeSymbols() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSecurityListingsRequest_ExchangeSymbolsValidationError{
						field:  fmt.Sprintf("ExchangeSymbols[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSecurityListingsRequest_ExchangeSymbolsValidationError{
						field:  fmt.Sprintf("ExchangeSymbols[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSecurityListingsRequest_ExchangeSymbolsValidationError{
					field:  fmt.Sprintf("ExchangeSymbols[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetSecurityListingsRequest_ExchangeSymbolsMultiError(errors)
	}

	return nil
}

// GetSecurityListingsRequest_ExchangeSymbolsMultiError is an error wrapping
// multiple validation errors returned by
// GetSecurityListingsRequest_ExchangeSymbols.ValidateAll() if the designated
// constraints aren't met.
type GetSecurityListingsRequest_ExchangeSymbolsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecurityListingsRequest_ExchangeSymbolsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecurityListingsRequest_ExchangeSymbolsMultiError) AllErrors() []error { return m }

// GetSecurityListingsRequest_ExchangeSymbolsValidationError is the validation
// error returned by GetSecurityListingsRequest_ExchangeSymbols.Validate if
// the designated constraints aren't met.
type GetSecurityListingsRequest_ExchangeSymbolsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecurityListingsRequest_ExchangeSymbolsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecurityListingsRequest_ExchangeSymbolsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecurityListingsRequest_ExchangeSymbolsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecurityListingsRequest_ExchangeSymbolsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecurityListingsRequest_ExchangeSymbolsValidationError) ErrorName() string {
	return "GetSecurityListingsRequest_ExchangeSymbolsValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecurityListingsRequest_ExchangeSymbolsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecurityListingsRequest_ExchangeSymbols.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecurityListingsRequest_ExchangeSymbolsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecurityListingsRequest_ExchangeSymbolsValidationError{}
