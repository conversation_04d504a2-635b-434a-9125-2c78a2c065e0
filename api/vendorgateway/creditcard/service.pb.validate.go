// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/creditcard/service.proto

package creditcard

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/firefly/v2/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.CreditCardApplicantType(0)
)

// Validate checks the field values on GenerateCreditCardSdkAuthTokenRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GenerateCreditCardSdkAuthTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateCreditCardSdkAuthTokenRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GenerateCreditCardSdkAuthTokenRequestMultiError, or nil if none found.
func (m *GenerateCreditCardSdkAuthTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateCreditCardSdkAuthTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateCreditCardSdkAuthTokenRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _GenerateCreditCardSdkAuthTokenRequest_ApplicantType_NotInLookup[m.GetApplicantType()]; ok {
		err := GenerateCreditCardSdkAuthTokenRequestValidationError{
			field:  "ApplicantType",
			reason: "value must not be in list [CREDIT_CARD_APPLICANT_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetUserInfo() == nil {
		err := GenerateCreditCardSdkAuthTokenRequestValidationError{
			field:  "UserInfo",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetUserInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "UserInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "UserInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateCreditCardSdkAuthTokenRequestValidationError{
				field:  "UserInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetDeviceInfo() == nil {
		err := GenerateCreditCardSdkAuthTokenRequestValidationError{
			field:  "DeviceInfo",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDeviceInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "DeviceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "DeviceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateCreditCardSdkAuthTokenRequestValidationError{
				field:  "DeviceInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "PanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "PanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateCreditCardSdkAuthTokenRequestValidationError{
				field:  "PanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsentInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "ConsentInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "ConsentInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateCreditCardSdkAuthTokenRequestValidationError{
				field:  "ConsentInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPreApprovedInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "PreApprovedInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenRequestValidationError{
					field:  "PreApprovedInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreApprovedInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateCreditCardSdkAuthTokenRequestValidationError{
				field:  "PreApprovedInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateCreditCardSdkAuthTokenRequestMultiError(errors)
	}

	return nil
}

// GenerateCreditCardSdkAuthTokenRequestMultiError is an error wrapping
// multiple validation errors returned by
// GenerateCreditCardSdkAuthTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateCreditCardSdkAuthTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateCreditCardSdkAuthTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateCreditCardSdkAuthTokenRequestMultiError) AllErrors() []error { return m }

// GenerateCreditCardSdkAuthTokenRequestValidationError is the validation error
// returned by GenerateCreditCardSdkAuthTokenRequest.Validate if the
// designated constraints aren't met.
type GenerateCreditCardSdkAuthTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateCreditCardSdkAuthTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateCreditCardSdkAuthTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateCreditCardSdkAuthTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateCreditCardSdkAuthTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateCreditCardSdkAuthTokenRequestValidationError) ErrorName() string {
	return "GenerateCreditCardSdkAuthTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateCreditCardSdkAuthTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateCreditCardSdkAuthTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateCreditCardSdkAuthTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateCreditCardSdkAuthTokenRequestValidationError{}

var _GenerateCreditCardSdkAuthTokenRequest_ApplicantType_NotInLookup = map[enums.CreditCardApplicantType]struct{}{
	0: {},
}

// Validate checks the field values on GenerateCreditCardSdkAuthTokenResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GenerateCreditCardSdkAuthTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GenerateCreditCardSdkAuthTokenResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GenerateCreditCardSdkAuthTokenResponseMultiError, or nil if none found.
func (m *GenerateCreditCardSdkAuthTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateCreditCardSdkAuthTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateCreditCardSdkAuthTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ModuleName

	// no validation rules for AuthToken

	if all {
		switch v := interface{}(m.GetAdditionalInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenResponseValidationError{
					field:  "AdditionalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateCreditCardSdkAuthTokenResponseValidationError{
					field:  "AdditionalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateCreditCardSdkAuthTokenResponseValidationError{
				field:  "AdditionalInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateCreditCardSdkAuthTokenResponseMultiError(errors)
	}

	return nil
}

// GenerateCreditCardSdkAuthTokenResponseMultiError is an error wrapping
// multiple validation errors returned by
// GenerateCreditCardSdkAuthTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateCreditCardSdkAuthTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateCreditCardSdkAuthTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateCreditCardSdkAuthTokenResponseMultiError) AllErrors() []error { return m }

// GenerateCreditCardSdkAuthTokenResponseValidationError is the validation
// error returned by GenerateCreditCardSdkAuthTokenResponse.Validate if the
// designated constraints aren't met.
type GenerateCreditCardSdkAuthTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateCreditCardSdkAuthTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateCreditCardSdkAuthTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateCreditCardSdkAuthTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateCreditCardSdkAuthTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateCreditCardSdkAuthTokenResponseValidationError) ErrorName() string {
	return "GenerateCreditCardSdkAuthTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateCreditCardSdkAuthTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateCreditCardSdkAuthTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateCreditCardSdkAuthTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateCreditCardSdkAuthTokenResponseValidationError{}

// Validate checks the field values on TokenGenerationAdditionalInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TokenGenerationAdditionalInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TokenGenerationAdditionalInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TokenGenerationAdditionalInfoMultiError, or nil if none found.
func (m *TokenGenerationAdditionalInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *TokenGenerationAdditionalInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkflowId

	// no validation rules for UserLocalId

	// no validation rules for ExternalUserId

	// no validation rules for WorkflowStatus

	// no validation rules for WorkflowState

	// no validation rules for WorkflowMessage

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TokenGenerationAdditionalInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TokenGenerationAdditionalInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TokenGenerationAdditionalInfoValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TokenGenerationAdditionalInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TokenGenerationAdditionalInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TokenGenerationAdditionalInfoValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TokenGenerationAdditionalInfoMultiError(errors)
	}

	return nil
}

// TokenGenerationAdditionalInfoMultiError is an error wrapping multiple
// validation errors returned by TokenGenerationAdditionalInfo.ValidateAll()
// if the designated constraints aren't met.
type TokenGenerationAdditionalInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TokenGenerationAdditionalInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TokenGenerationAdditionalInfoMultiError) AllErrors() []error { return m }

// TokenGenerationAdditionalInfoValidationError is the validation error
// returned by TokenGenerationAdditionalInfo.Validate if the designated
// constraints aren't met.
type TokenGenerationAdditionalInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TokenGenerationAdditionalInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TokenGenerationAdditionalInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TokenGenerationAdditionalInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TokenGenerationAdditionalInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TokenGenerationAdditionalInfoValidationError) ErrorName() string {
	return "TokenGenerationAdditionalInfoValidationError"
}

// Error satisfies the builtin error interface
func (e TokenGenerationAdditionalInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTokenGenerationAdditionalInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TokenGenerationAdditionalInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TokenGenerationAdditionalInfoValidationError{}

// Validate checks the field values on DeviceInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeviceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeviceInfoMultiError, or
// nil if none found.
func (m *DeviceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceInfoValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceInfoValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceInfoValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeviceInfoMultiError(errors)
	}

	return nil
}

// DeviceInfoMultiError is an error wrapping multiple validation errors
// returned by DeviceInfo.ValidateAll() if the designated constraints aren't met.
type DeviceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceInfoMultiError) AllErrors() []error { return m }

// DeviceInfoValidationError is the validation error returned by
// DeviceInfo.Validate if the designated constraints aren't met.
type DeviceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceInfoValidationError) ErrorName() string { return "DeviceInfoValidationError" }

// Error satisfies the builtin error interface
func (e DeviceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceInfoValidationError{}

// Validate checks the field values on UserInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserInfoMultiError, or nil
// if none found.
func (m *UserInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UserInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetEmailAddress()) < 1 {
		err := UserInfoValidationError{
			field:  "EmailAddress",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPhoneNumber() == nil {
		err := UserInfoValidationError{
			field:  "PhoneNumber",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserInfoValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserInfoValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserInfoValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PhoneType

	if l := utf8.RuneCountInString(m.GetInternalUserId()); l < 1 || l > 100 {
		err := UserInfoValidationError{
			field:  "InternalUserId",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UserInfoMultiError(errors)
	}

	return nil
}

// UserInfoMultiError is an error wrapping multiple validation errors returned
// by UserInfo.ValidateAll() if the designated constraints aren't met.
type UserInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserInfoMultiError) AllErrors() []error { return m }

// UserInfoValidationError is the validation error returned by
// UserInfo.Validate if the designated constraints aren't met.
type UserInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserInfoValidationError) ErrorName() string { return "UserInfoValidationError" }

// Error satisfies the builtin error interface
func (e UserInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserInfoValidationError{}

// Validate checks the field values on PanInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PanInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PanInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PanInfoMultiError, or nil if none found.
func (m *PanInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PanInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetPanNumber()); l < 1 || l > 100 {
		err := PanInfoValidationError{
			field:  "PanNumber",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetUserName() == nil {
		err := PanInfoValidationError{
			field:  "UserName",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetUserName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanInfoValidationError{
					field:  "UserName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanInfoValidationError{
					field:  "UserName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanInfoValidationError{
				field:  "UserName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PanInfoMultiError(errors)
	}

	return nil
}

// PanInfoMultiError is an error wrapping multiple validation errors returned
// by PanInfo.ValidateAll() if the designated constraints aren't met.
type PanInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PanInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PanInfoMultiError) AllErrors() []error { return m }

// PanInfoValidationError is the validation error returned by PanInfo.Validate
// if the designated constraints aren't met.
type PanInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PanInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PanInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PanInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PanInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PanInfoValidationError) ErrorName() string { return "PanInfoValidationError" }

// Error satisfies the builtin error interface
func (e PanInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPanInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PanInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PanInfoValidationError{}

// Validate checks the field values on PreApprovedInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PreApprovedInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreApprovedInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PreApprovedInfoMultiError, or nil if none found.
func (m *PreApprovedInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PreApprovedInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPreApprovedLimit() == nil {
		err := PreApprovedInfoValidationError{
			field:  "PreApprovedLimit",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPreApprovedLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreApprovedInfoValidationError{
					field:  "PreApprovedLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreApprovedInfoValidationError{
					field:  "PreApprovedLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreApprovedLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreApprovedInfoValidationError{
				field:  "PreApprovedLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetPreApprovedExp() == nil {
		err := PreApprovedInfoValidationError{
			field:  "PreApprovedExp",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPreApprovedExp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreApprovedInfoValidationError{
					field:  "PreApprovedExp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreApprovedInfoValidationError{
					field:  "PreApprovedExp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreApprovedExp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreApprovedInfoValidationError{
				field:  "PreApprovedExp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PreApprovedInfoMultiError(errors)
	}

	return nil
}

// PreApprovedInfoMultiError is an error wrapping multiple validation errors
// returned by PreApprovedInfo.ValidateAll() if the designated constraints
// aren't met.
type PreApprovedInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreApprovedInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreApprovedInfoMultiError) AllErrors() []error { return m }

// PreApprovedInfoValidationError is the validation error returned by
// PreApprovedInfo.Validate if the designated constraints aren't met.
type PreApprovedInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreApprovedInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreApprovedInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreApprovedInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreApprovedInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreApprovedInfoValidationError) ErrorName() string { return "PreApprovedInfoValidationError" }

// Error satisfies the builtin error interface
func (e PreApprovedInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreApprovedInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreApprovedInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreApprovedInfoValidationError{}

// Validate checks the field values on ConsentInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ConsentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsentInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ConsentInfoMultiError, or
// nil if none found.
func (m *ConsentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetConsents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ConsentInfoValidationError{
						field:  fmt.Sprintf("Consents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ConsentInfoValidationError{
						field:  fmt.Sprintf("Consents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ConsentInfoValidationError{
					field:  fmt.Sprintf("Consents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ConsentInfoMultiError(errors)
	}

	return nil
}

// ConsentInfoMultiError is an error wrapping multiple validation errors
// returned by ConsentInfo.ValidateAll() if the designated constraints aren't met.
type ConsentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsentInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsentInfoMultiError) AllErrors() []error { return m }

// ConsentInfoValidationError is the validation error returned by
// ConsentInfo.Validate if the designated constraints aren't met.
type ConsentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsentInfoValidationError) ErrorName() string { return "ConsentInfoValidationError" }

// Error satisfies the builtin error interface
func (e ConsentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsentInfoValidationError{}

// Validate checks the field values on Consent with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Consent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Consent with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ConsentMultiError, or nil if none found.
func (m *Consent) ValidateAll() error {
	return m.validate(true)
}

func (m *Consent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsConsentRecorded

	// no validation rules for ConsentType

	// no validation rules for ConsentCategory

	if len(errors) > 0 {
		return ConsentMultiError(errors)
	}

	return nil
}

// ConsentMultiError is an error wrapping multiple validation errors returned
// by Consent.ValidateAll() if the designated constraints aren't met.
type ConsentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsentMultiError) AllErrors() []error { return m }

// ConsentValidationError is the validation error returned by Consent.Validate
// if the designated constraints aren't met.
type ConsentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsentValidationError) ErrorName() string { return "ConsentValidationError" }

// Error satisfies the builtin error interface
func (e ConsentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsentValidationError{}
