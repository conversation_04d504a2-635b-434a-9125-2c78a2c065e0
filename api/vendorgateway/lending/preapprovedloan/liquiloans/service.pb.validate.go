// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/lending/preapprovedloan/liquiloans/service.proto

package liquiloans

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	preapprovedloan "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.ImageType(0)

	_ = preapprovedloan.IncomeDataSource(0)

	_ = typesv2.Gender(0)
)

// Validate checks the field values on SaveChargesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveChargesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveChargesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveChargesRequestMultiError, or nil if none found.
func (m *SaveChargesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveChargesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveChargesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveChargesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveChargesRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanProgram

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := SaveChargesRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveChargesRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveChargesRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveChargesRequestValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveChargesRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveChargesRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveChargesRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ChargeId

	// no validation rules for Remarks

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return SaveChargesRequestMultiError(errors)
	}

	return nil
}

// SaveChargesRequestMultiError is an error wrapping multiple validation errors
// returned by SaveChargesRequest.ValidateAll() if the designated constraints
// aren't met.
type SaveChargesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveChargesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveChargesRequestMultiError) AllErrors() []error { return m }

// SaveChargesRequestValidationError is the validation error returned by
// SaveChargesRequest.Validate if the designated constraints aren't met.
type SaveChargesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveChargesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveChargesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveChargesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveChargesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveChargesRequestValidationError) ErrorName() string {
	return "SaveChargesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SaveChargesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveChargesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveChargesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveChargesRequestValidationError{}

// Validate checks the field values on SaveChargesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveChargesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveChargesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveChargesResponseMultiError, or nil if none found.
func (m *SaveChargesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveChargesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveChargesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveChargesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveChargesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SaveChargesResponseMultiError(errors)
	}

	return nil
}

// SaveChargesResponseMultiError is an error wrapping multiple validation
// errors returned by SaveChargesResponse.ValidateAll() if the designated
// constraints aren't met.
type SaveChargesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveChargesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveChargesResponseMultiError) AllErrors() []error { return m }

// SaveChargesResponseValidationError is the validation error returned by
// SaveChargesResponse.Validate if the designated constraints aren't met.
type SaveChargesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveChargesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveChargesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveChargesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveChargesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveChargesResponseValidationError) ErrorName() string {
	return "SaveChargesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SaveChargesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveChargesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveChargesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveChargesResponseValidationError{}

// Validate checks the field values on CreateRepaymentScheduleRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRepaymentScheduleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRepaymentScheduleRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateRepaymentScheduleRequestMultiError, or nil if none found.
func (m *CreateRepaymentScheduleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRepaymentScheduleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanId

	// no validation rules for LoanProgram

	// no validation rules for RepaymentFrequency

	// no validation rules for EmiTenure

	if all {
		switch v := interface{}(m.GetEmiStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequestValidationError{
					field:  "EmiStartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequestValidationError{
					field:  "EmiStartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmiStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleRequestValidationError{
				field:  "EmiStartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSchedules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateRepaymentScheduleRequestValidationError{
						field:  fmt.Sprintf("Schedules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateRepaymentScheduleRequestValidationError{
						field:  fmt.Sprintf("Schedules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateRepaymentScheduleRequestValidationError{
					field:  fmt.Sprintf("Schedules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return CreateRepaymentScheduleRequestMultiError(errors)
	}

	return nil
}

// CreateRepaymentScheduleRequestMultiError is an error wrapping multiple
// validation errors returned by CreateRepaymentScheduleRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateRepaymentScheduleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRepaymentScheduleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRepaymentScheduleRequestMultiError) AllErrors() []error { return m }

// CreateRepaymentScheduleRequestValidationError is the validation error
// returned by CreateRepaymentScheduleRequest.Validate if the designated
// constraints aren't met.
type CreateRepaymentScheduleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRepaymentScheduleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRepaymentScheduleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRepaymentScheduleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRepaymentScheduleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRepaymentScheduleRequestValidationError) ErrorName() string {
	return "CreateRepaymentScheduleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRepaymentScheduleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRepaymentScheduleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRepaymentScheduleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRepaymentScheduleRequestValidationError{}

// Validate checks the field values on CreateRepaymentScheduleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRepaymentScheduleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRepaymentScheduleResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateRepaymentScheduleResponseMultiError, or nil if none found.
func (m *CreateRepaymentScheduleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRepaymentScheduleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRepaymentScheduleResponseMultiError(errors)
	}

	return nil
}

// CreateRepaymentScheduleResponseMultiError is an error wrapping multiple
// validation errors returned by CreateRepaymentScheduleResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateRepaymentScheduleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRepaymentScheduleResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRepaymentScheduleResponseMultiError) AllErrors() []error { return m }

// CreateRepaymentScheduleResponseValidationError is the validation error
// returned by CreateRepaymentScheduleResponse.Validate if the designated
// constraints aren't met.
type CreateRepaymentScheduleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRepaymentScheduleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRepaymentScheduleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRepaymentScheduleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRepaymentScheduleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRepaymentScheduleResponseValidationError) ErrorName() string {
	return "CreateRepaymentScheduleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRepaymentScheduleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRepaymentScheduleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRepaymentScheduleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRepaymentScheduleResponseValidationError{}

// Validate checks the field values on UpdateApplicantUdfRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateApplicantUdfRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateApplicantUdfRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateApplicantUdfRequestMultiError, or nil if none found.
func (m *UpdateApplicantUdfRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateApplicantUdfRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateApplicantUdfRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateApplicantUdfRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateApplicantUdfRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	// no validation rules for LoanProgram

	if all {
		switch v := interface{}(m.GetMonthlyIncome()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateApplicantUdfRequestValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateApplicantUdfRequestValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthlyIncome()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateApplicantUdfRequestValidationError{
				field:  "MonthlyIncome",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IncomeDataSource

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return UpdateApplicantUdfRequestMultiError(errors)
	}

	return nil
}

// UpdateApplicantUdfRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateApplicantUdfRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateApplicantUdfRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateApplicantUdfRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateApplicantUdfRequestMultiError) AllErrors() []error { return m }

// UpdateApplicantUdfRequestValidationError is the validation error returned by
// UpdateApplicantUdfRequest.Validate if the designated constraints aren't met.
type UpdateApplicantUdfRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateApplicantUdfRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateApplicantUdfRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateApplicantUdfRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateApplicantUdfRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateApplicantUdfRequestValidationError) ErrorName() string {
	return "UpdateApplicantUdfRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateApplicantUdfRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateApplicantUdfRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateApplicantUdfRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateApplicantUdfRequestValidationError{}

// Validate checks the field values on UpdateApplicantUdfResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateApplicantUdfResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateApplicantUdfResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateApplicantUdfResponseMultiError, or nil if none found.
func (m *UpdateApplicantUdfResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateApplicantUdfResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateApplicantUdfResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateApplicantUdfResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateApplicantUdfResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateApplicantUdfResponseMultiError(errors)
	}

	return nil
}

// UpdateApplicantUdfResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateApplicantUdfResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateApplicantUdfResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateApplicantUdfResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateApplicantUdfResponseMultiError) AllErrors() []error { return m }

// UpdateApplicantUdfResponseValidationError is the validation error returned
// by UpdateApplicantUdfResponse.Validate if the designated constraints aren't met.
type UpdateApplicantUdfResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateApplicantUdfResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateApplicantUdfResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateApplicantUdfResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateApplicantUdfResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateApplicantUdfResponseValidationError) ErrorName() string {
	return "UpdateApplicantUdfResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateApplicantUdfResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateApplicantUdfResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateApplicantUdfResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateApplicantUdfResponseValidationError{}

// Validate checks the field values on ForeClosureDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForeClosureDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForeClosureDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForeClosureDetailsRequestMultiError, or nil if none found.
func (m *ForeClosureDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ForeClosureDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeClosureDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeClosureDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeClosureDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicationId

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return ForeClosureDetailsRequestMultiError(errors)
	}

	return nil
}

// ForeClosureDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by ForeClosureDetailsRequest.ValidateAll() if the
// designated constraints aren't met.
type ForeClosureDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForeClosureDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForeClosureDetailsRequestMultiError) AllErrors() []error { return m }

// ForeClosureDetailsRequestValidationError is the validation error returned by
// ForeClosureDetailsRequest.Validate if the designated constraints aren't met.
type ForeClosureDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForeClosureDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForeClosureDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForeClosureDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForeClosureDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForeClosureDetailsRequestValidationError) ErrorName() string {
	return "ForeClosureDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ForeClosureDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForeClosureDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForeClosureDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForeClosureDetailsRequestValidationError{}

// Validate checks the field values on ForeClosureDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForeClosureDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForeClosureDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForeClosureDetailsResponseMultiError, or nil if none found.
func (m *ForeClosureDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ForeClosureDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeClosureDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeClosureDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeClosureDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeClosureDetailsResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeClosureDetailsResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeClosureDetailsResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return ForeClosureDetailsResponseMultiError(errors)
	}

	return nil
}

// ForeClosureDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by ForeClosureDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type ForeClosureDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForeClosureDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForeClosureDetailsResponseMultiError) AllErrors() []error { return m }

// ForeClosureDetailsResponseValidationError is the validation error returned
// by ForeClosureDetailsResponse.Validate if the designated constraints aren't met.
type ForeClosureDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForeClosureDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForeClosureDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForeClosureDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForeClosureDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForeClosureDetailsResponseValidationError) ErrorName() string {
	return "ForeClosureDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ForeClosureDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForeClosureDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForeClosureDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForeClosureDetailsResponseValidationError{}

// Validate checks the field values on CancelLeadRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CancelLeadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelLeadRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelLeadRequestMultiError, or nil if none found.
func (m *CancelLeadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelLeadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CancelLeadRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CancelLeadRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CancelLeadRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicationId

	// no validation rules for ApplicantId

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return CancelLeadRequestMultiError(errors)
	}

	return nil
}

// CancelLeadRequestMultiError is an error wrapping multiple validation errors
// returned by CancelLeadRequest.ValidateAll() if the designated constraints
// aren't met.
type CancelLeadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelLeadRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelLeadRequestMultiError) AllErrors() []error { return m }

// CancelLeadRequestValidationError is the validation error returned by
// CancelLeadRequest.Validate if the designated constraints aren't met.
type CancelLeadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelLeadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelLeadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelLeadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelLeadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelLeadRequestValidationError) ErrorName() string {
	return "CancelLeadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CancelLeadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelLeadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelLeadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelLeadRequestValidationError{}

// Validate checks the field values on CancelLeadResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelLeadResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelLeadResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelLeadResponseMultiError, or nil if none found.
func (m *CancelLeadResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelLeadResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CancelLeadResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CancelLeadResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CancelLeadResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CancelLeadResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CancelLeadResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CancelLeadResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return CancelLeadResponseMultiError(errors)
	}

	return nil
}

// CancelLeadResponseMultiError is an error wrapping multiple validation errors
// returned by CancelLeadResponse.ValidateAll() if the designated constraints
// aren't met.
type CancelLeadResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelLeadResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelLeadResponseMultiError) AllErrors() []error { return m }

// CancelLeadResponseValidationError is the validation error returned by
// CancelLeadResponse.Validate if the designated constraints aren't met.
type CancelLeadResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelLeadResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelLeadResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelLeadResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelLeadResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelLeadResponseValidationError) ErrorName() string {
	return "CancelLeadResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CancelLeadResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelLeadResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelLeadResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelLeadResponseValidationError{}

// Validate checks the field values on UpdateLeadRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateLeadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLeadRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLeadRequestMultiError, or nil if none found.
func (m *UpdateLeadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLeadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLeadRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLeadRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLeadRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicationId

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLeadRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLeadRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLeadRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Urn

	if all {
		switch v := interface{}(m.GetSchemeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLeadRequestValidationError{
					field:  "SchemeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLeadRequestValidationError{
					field:  "SchemeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSchemeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLeadRequestValidationError{
				field:  "SchemeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return UpdateLeadRequestMultiError(errors)
	}

	return nil
}

// UpdateLeadRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateLeadRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateLeadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLeadRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLeadRequestMultiError) AllErrors() []error { return m }

// UpdateLeadRequestValidationError is the validation error returned by
// UpdateLeadRequest.Validate if the designated constraints aren't met.
type UpdateLeadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLeadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLeadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLeadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLeadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLeadRequestValidationError) ErrorName() string {
	return "UpdateLeadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLeadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLeadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLeadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLeadRequestValidationError{}

// Validate checks the field values on UpdateLeadSchemeDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLeadSchemeDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLeadSchemeDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLeadSchemeDetailsMultiError, or nil if none found.
func (m *UpdateLeadSchemeDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLeadSchemeDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InstallmentFrequency

	// no validation rules for InstallmentTenure

	if all {
		switch v := interface{}(m.GetProcessingFees()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLeadSchemeDetailsValidationError{
					field:  "ProcessingFees",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLeadSchemeDetailsValidationError{
					field:  "ProcessingFees",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessingFees()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLeadSchemeDetailsValidationError{
				field:  "ProcessingFees",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetRoiPercentage(); val < 0 || val > 100 {
		err := UpdateLeadSchemeDetailsValidationError{
			field:  "RoiPercentage",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInstallmentStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLeadSchemeDetailsValidationError{
					field:  "InstallmentStartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLeadSchemeDetailsValidationError{
					field:  "InstallmentStartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstallmentStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLeadSchemeDetailsValidationError{
				field:  "InstallmentStartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetSubventionPercentage(); val < 0 || val > 100 {
		err := UpdateLeadSchemeDetailsValidationError{
			field:  "SubventionPercentage",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ProcessingFeesType

	// no validation rules for RoiType

	// no validation rules for RoiAppliedOn

	// no validation rules for ProcessingFeesCustomerAppliedOn

	// no validation rules for ProcessingFeesCustomerGst

	// no validation rules for RoiGst

	// no validation rules for GstType

	// no validation rules for GstValue

	if len(errors) > 0 {
		return UpdateLeadSchemeDetailsMultiError(errors)
	}

	return nil
}

// UpdateLeadSchemeDetailsMultiError is an error wrapping multiple validation
// errors returned by UpdateLeadSchemeDetails.ValidateAll() if the designated
// constraints aren't met.
type UpdateLeadSchemeDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLeadSchemeDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLeadSchemeDetailsMultiError) AllErrors() []error { return m }

// UpdateLeadSchemeDetailsValidationError is the validation error returned by
// UpdateLeadSchemeDetails.Validate if the designated constraints aren't met.
type UpdateLeadSchemeDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLeadSchemeDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLeadSchemeDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLeadSchemeDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLeadSchemeDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLeadSchemeDetailsValidationError) ErrorName() string {
	return "UpdateLeadSchemeDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLeadSchemeDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLeadSchemeDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLeadSchemeDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLeadSchemeDetailsValidationError{}

// Validate checks the field values on UpdateLeadResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLeadResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLeadResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLeadResponseMultiError, or nil if none found.
func (m *UpdateLeadResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLeadResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLeadResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLeadResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLeadResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanId

	if len(errors) > 0 {
		return UpdateLeadResponseMultiError(errors)
	}

	return nil
}

// UpdateLeadResponseMultiError is an error wrapping multiple validation errors
// returned by UpdateLeadResponse.ValidateAll() if the designated constraints
// aren't met.
type UpdateLeadResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLeadResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLeadResponseMultiError) AllErrors() []error { return m }

// UpdateLeadResponseValidationError is the validation error returned by
// UpdateLeadResponse.Validate if the designated constraints aren't met.
type UpdateLeadResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLeadResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLeadResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLeadResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLeadResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLeadResponseValidationError) ErrorName() string {
	return "UpdateLeadResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLeadResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLeadResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLeadResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLeadResponseValidationError{}

// Validate checks the field values on GetCreditLineSchemesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditLineSchemesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditLineSchemesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCreditLineSchemesRequestMultiError, or nil if none found.
func (m *GetCreditLineSchemesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditLineSchemesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return GetCreditLineSchemesRequestMultiError(errors)
	}

	return nil
}

// GetCreditLineSchemesRequestMultiError is an error wrapping multiple
// validation errors returned by GetCreditLineSchemesRequest.ValidateAll() if
// the designated constraints aren't met.
type GetCreditLineSchemesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditLineSchemesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditLineSchemesRequestMultiError) AllErrors() []error { return m }

// GetCreditLineSchemesRequestValidationError is the validation error returned
// by GetCreditLineSchemesRequest.Validate if the designated constraints
// aren't met.
type GetCreditLineSchemesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditLineSchemesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditLineSchemesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditLineSchemesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditLineSchemesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditLineSchemesRequestValidationError) ErrorName() string {
	return "GetCreditLineSchemesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditLineSchemesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditLineSchemesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditLineSchemesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditLineSchemesRequestValidationError{}

// Validate checks the field values on GetCreditLineSchemesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditLineSchemesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditLineSchemesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCreditLineSchemesResponseMultiError, or nil if none found.
func (m *GetCreditLineSchemesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditLineSchemesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreditLineDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponseValidationError{
					field:  "CreditLineDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponseValidationError{
					field:  "CreditLineDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditLineDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesResponseValidationError{
				field:  "CreditLineDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCreditLineSchemes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCreditLineSchemesResponseValidationError{
						field:  fmt.Sprintf("CreditLineSchemes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCreditLineSchemesResponseValidationError{
						field:  fmt.Sprintf("CreditLineSchemes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCreditLineSchemesResponseValidationError{
					field:  fmt.Sprintf("CreditLineSchemes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCreditLineSchemesResponseMultiError(errors)
	}

	return nil
}

// GetCreditLineSchemesResponseMultiError is an error wrapping multiple
// validation errors returned by GetCreditLineSchemesResponse.ValidateAll() if
// the designated constraints aren't met.
type GetCreditLineSchemesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditLineSchemesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditLineSchemesResponseMultiError) AllErrors() []error { return m }

// GetCreditLineSchemesResponseValidationError is the validation error returned
// by GetCreditLineSchemesResponse.Validate if the designated constraints
// aren't met.
type GetCreditLineSchemesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditLineSchemesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditLineSchemesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditLineSchemesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditLineSchemesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditLineSchemesResponseValidationError) ErrorName() string {
	return "GetCreditLineSchemesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditLineSchemesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditLineSchemesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditLineSchemesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditLineSchemesResponseValidationError{}

// Validate checks the field values on AddPersonalDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddPersonalDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddPersonalDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddPersonalDetailsRequestMultiError, or nil if none found.
func (m *AddPersonalDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddPersonalDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddPersonalDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddPersonalDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddPersonalDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddPersonalDetailsRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddPersonalDetailsRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddPersonalDetailsRequestValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetContactNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddPersonalDetailsRequestValidationError{
					field:  "ContactNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddPersonalDetailsRequestValidationError{
					field:  "ContactNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContactNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddPersonalDetailsRequestValidationError{
				field:  "ContactNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Pan

	// no validation rules for Gender

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddPersonalDetailsRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddPersonalDetailsRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddPersonalDetailsRequestValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Urn

	// no validation rules for LoanProgram

	if all {
		switch v := interface{}(m.GetMonthlyIncome()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddPersonalDetailsRequestValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddPersonalDetailsRequestValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthlyIncome()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddPersonalDetailsRequestValidationError{
				field:  "MonthlyIncome",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IncomeDataSource

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return AddPersonalDetailsRequestMultiError(errors)
	}

	return nil
}

// AddPersonalDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by AddPersonalDetailsRequest.ValidateAll() if the
// designated constraints aren't met.
type AddPersonalDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddPersonalDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddPersonalDetailsRequestMultiError) AllErrors() []error { return m }

// AddPersonalDetailsRequestValidationError is the validation error returned by
// AddPersonalDetailsRequest.Validate if the designated constraints aren't met.
type AddPersonalDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddPersonalDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddPersonalDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddPersonalDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddPersonalDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddPersonalDetailsRequestValidationError) ErrorName() string {
	return "AddPersonalDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddPersonalDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddPersonalDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddPersonalDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddPersonalDetailsRequestValidationError{}

// Validate checks the field values on AddPersonalDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddPersonalDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddPersonalDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddPersonalDetailsResponseMultiError, or nil if none found.
func (m *AddPersonalDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddPersonalDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddPersonalDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddPersonalDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddPersonalDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddPersonalDetailsResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddPersonalDetailsResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddPersonalDetailsResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return AddPersonalDetailsResponseMultiError(errors)
	}

	return nil
}

// AddPersonalDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by AddPersonalDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type AddPersonalDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddPersonalDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddPersonalDetailsResponseMultiError) AllErrors() []error { return m }

// AddPersonalDetailsResponseValidationError is the validation error returned
// by AddPersonalDetailsResponse.Validate if the designated constraints aren't met.
type AddPersonalDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddPersonalDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddPersonalDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddPersonalDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddPersonalDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddPersonalDetailsResponseValidationError) ErrorName() string {
	return "AddPersonalDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddPersonalDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddPersonalDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddPersonalDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddPersonalDetailsResponseValidationError{}

// Validate checks the field values on AddBankingDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddBankingDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddBankingDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddBankingDetailsRequestMultiError, or nil if none found.
func (m *AddBankingDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddBankingDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddBankingDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddBankingDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddBankingDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	if all {
		switch v := interface{}(m.GetBankAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddBankingDetailsRequestValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddBankingDetailsRequestValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddBankingDetailsRequestValidationError{
				field:  "BankAccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return AddBankingDetailsRequestMultiError(errors)
	}

	return nil
}

// AddBankingDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by AddBankingDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type AddBankingDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddBankingDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddBankingDetailsRequestMultiError) AllErrors() []error { return m }

// AddBankingDetailsRequestValidationError is the validation error returned by
// AddBankingDetailsRequest.Validate if the designated constraints aren't met.
type AddBankingDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddBankingDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddBankingDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddBankingDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddBankingDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddBankingDetailsRequestValidationError) ErrorName() string {
	return "AddBankingDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddBankingDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddBankingDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddBankingDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddBankingDetailsRequestValidationError{}

// Validate checks the field values on AddAddressDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddAddressDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddAddressDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddAddressDetailsRequestMultiError, or nil if none found.
func (m *AddAddressDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddAddressDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddAddressDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddAddressDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddAddressDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddAddressDetailsRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddAddressDetailsRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddAddressDetailsRequestValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AddressType

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return AddAddressDetailsRequestMultiError(errors)
	}

	return nil
}

// AddAddressDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by AddAddressDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type AddAddressDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddAddressDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddAddressDetailsRequestMultiError) AllErrors() []error { return m }

// AddAddressDetailsRequestValidationError is the validation error returned by
// AddAddressDetailsRequest.Validate if the designated constraints aren't met.
type AddAddressDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddAddressDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddAddressDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddAddressDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddAddressDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddAddressDetailsRequestValidationError) ErrorName() string {
	return "AddAddressDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddAddressDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddAddressDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddAddressDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddAddressDetailsRequestValidationError{}

// Validate checks the field values on AddEmploymentDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddEmploymentDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddEmploymentDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddEmploymentDetailsRequestMultiError, or nil if none found.
func (m *AddEmploymentDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddEmploymentDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddEmploymentDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddEmploymentDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddEmploymentDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	// no validation rules for Occupation

	// no validation rules for OrganizationName

	if all {
		switch v := interface{}(m.GetMonthlyIncome()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddEmploymentDetailsRequestValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddEmploymentDetailsRequestValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthlyIncome()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddEmploymentDetailsRequestValidationError{
				field:  "MonthlyIncome",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for WorkEmail

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return AddEmploymentDetailsRequestMultiError(errors)
	}

	return nil
}

// AddEmploymentDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by AddEmploymentDetailsRequest.ValidateAll() if
// the designated constraints aren't met.
type AddEmploymentDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddEmploymentDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddEmploymentDetailsRequestMultiError) AllErrors() []error { return m }

// AddEmploymentDetailsRequestValidationError is the validation error returned
// by AddEmploymentDetailsRequest.Validate if the designated constraints
// aren't met.
type AddEmploymentDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddEmploymentDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddEmploymentDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddEmploymentDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddEmploymentDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddEmploymentDetailsRequestValidationError) ErrorName() string {
	return "AddEmploymentDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddEmploymentDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddEmploymentDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddEmploymentDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddEmploymentDetailsRequestValidationError{}

// Validate checks the field values on AddDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddDetailsResponseMultiError, or nil if none found.
func (m *AddDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return AddDetailsResponseMultiError(errors)
	}

	return nil
}

// AddDetailsResponseMultiError is an error wrapping multiple validation errors
// returned by AddDetailsResponse.ValidateAll() if the designated constraints
// aren't met.
type AddDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddDetailsResponseMultiError) AllErrors() []error { return m }

// AddDetailsResponseValidationError is the validation error returned by
// AddDetailsResponse.Validate if the designated constraints aren't met.
type AddDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddDetailsResponseValidationError) ErrorName() string {
	return "AddDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddDetailsResponseValidationError{}

// Validate checks the field values on ApplicantLookupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApplicantLookupRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicantLookupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplicantLookupRequestMultiError, or nil if none found.
func (m *ApplicantLookupRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicantLookupRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicantLookupRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicantLookupRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicantLookupRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Pan

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return ApplicantLookupRequestMultiError(errors)
	}

	return nil
}

// ApplicantLookupRequestMultiError is an error wrapping multiple validation
// errors returned by ApplicantLookupRequest.ValidateAll() if the designated
// constraints aren't met.
type ApplicantLookupRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantLookupRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantLookupRequestMultiError) AllErrors() []error { return m }

// ApplicantLookupRequestValidationError is the validation error returned by
// ApplicantLookupRequest.Validate if the designated constraints aren't met.
type ApplicantLookupRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantLookupRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantLookupRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicantLookupRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantLookupRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantLookupRequestValidationError) ErrorName() string {
	return "ApplicantLookupRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicantLookupRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicantLookupRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantLookupRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantLookupRequestValidationError{}

// Validate checks the field values on ApplicantLookupResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApplicantLookupResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicantLookupResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplicantLookupResponseMultiError, or nil if none found.
func (m *ApplicantLookupResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicantLookupResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicantLookupResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicantLookupResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicantLookupResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicantLookupResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicantLookupResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicantLookupResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return ApplicantLookupResponseMultiError(errors)
	}

	return nil
}

// ApplicantLookupResponseMultiError is an error wrapping multiple validation
// errors returned by ApplicantLookupResponse.ValidateAll() if the designated
// constraints aren't met.
type ApplicantLookupResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantLookupResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantLookupResponseMultiError) AllErrors() []error { return m }

// ApplicantLookupResponseValidationError is the validation error returned by
// ApplicantLookupResponse.Validate if the designated constraints aren't met.
type ApplicantLookupResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantLookupResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantLookupResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicantLookupResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantLookupResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantLookupResponseValidationError) ErrorName() string {
	return "ApplicantLookupResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicantLookupResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicantLookupResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantLookupResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantLookupResponseValidationError{}

// Validate checks the field values on GetMandateLinkRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMandateLinkRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMandateLinkRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMandateLinkRequestMultiError, or nil if none found.
func (m *GetMandateLinkRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMandateLinkRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMandateLinkRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMandateLinkRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMandateLinkRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	// no validation rules for LoanProgram

	// no validation rules for RecreateUrl

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return GetMandateLinkRequestMultiError(errors)
	}

	return nil
}

// GetMandateLinkRequestMultiError is an error wrapping multiple validation
// errors returned by GetMandateLinkRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMandateLinkRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMandateLinkRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMandateLinkRequestMultiError) AllErrors() []error { return m }

// GetMandateLinkRequestValidationError is the validation error returned by
// GetMandateLinkRequest.Validate if the designated constraints aren't met.
type GetMandateLinkRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMandateLinkRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMandateLinkRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMandateLinkRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMandateLinkRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMandateLinkRequestValidationError) ErrorName() string {
	return "GetMandateLinkRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMandateLinkRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMandateLinkRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMandateLinkRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMandateLinkRequestValidationError{}

// Validate checks the field values on GetMandateLinkResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMandateLinkResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMandateLinkResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMandateLinkResponseMultiError, or nil if none found.
func (m *GetMandateLinkResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMandateLinkResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMandateLinkResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMandateLinkResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMandateLinkResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MandateUrl

	// no validation rules for MandateId

	if len(errors) > 0 {
		return GetMandateLinkResponseMultiError(errors)
	}

	return nil
}

// GetMandateLinkResponseMultiError is an error wrapping multiple validation
// errors returned by GetMandateLinkResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMandateLinkResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMandateLinkResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMandateLinkResponseMultiError) AllErrors() []error { return m }

// GetMandateLinkResponseValidationError is the validation error returned by
// GetMandateLinkResponse.Validate if the designated constraints aren't met.
type GetMandateLinkResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMandateLinkResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMandateLinkResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMandateLinkResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMandateLinkResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMandateLinkResponseValidationError) ErrorName() string {
	return "GetMandateLinkResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMandateLinkResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMandateLinkResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMandateLinkResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMandateLinkResponseValidationError{}

// Validate checks the field values on GetMandateStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMandateStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMandateStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMandateStatusRequestMultiError, or nil if none found.
func (m *GetMandateStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMandateStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMandateStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMandateStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMandateStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return GetMandateStatusRequestMultiError(errors)
	}

	return nil
}

// GetMandateStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetMandateStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMandateStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMandateStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMandateStatusRequestMultiError) AllErrors() []error { return m }

// GetMandateStatusRequestValidationError is the validation error returned by
// GetMandateStatusRequest.Validate if the designated constraints aren't met.
type GetMandateStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMandateStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMandateStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMandateStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMandateStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMandateStatusRequestValidationError) ErrorName() string {
	return "GetMandateStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMandateStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMandateStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMandateStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMandateStatusRequestValidationError{}

// Validate checks the field values on GetMandateStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMandateStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMandateStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMandateStatusResponseMultiError, or nil if none found.
func (m *GetMandateStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMandateStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMandateStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMandateStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMandateStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MandateStatus

	if len(errors) > 0 {
		return GetMandateStatusResponseMultiError(errors)
	}

	return nil
}

// GetMandateStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetMandateStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMandateStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMandateStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMandateStatusResponseMultiError) AllErrors() []error { return m }

// GetMandateStatusResponseValidationError is the validation error returned by
// GetMandateStatusResponse.Validate if the designated constraints aren't met.
type GetMandateStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMandateStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMandateStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMandateStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMandateStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMandateStatusResponseValidationError) ErrorName() string {
	return "GetMandateStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMandateStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMandateStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMandateStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMandateStatusResponseValidationError{}

// Validate checks the field values on MakeDrawdownRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MakeDrawdownRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MakeDrawdownRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MakeDrawdownRequestMultiError, or nil if none found.
func (m *MakeDrawdownRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MakeDrawdownRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MakeDrawdownRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MakeDrawdownRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MakeDrawdownRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	if all {
		switch v := interface{}(m.GetTenure()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MakeDrawdownRequestValidationError{
					field:  "Tenure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MakeDrawdownRequestValidationError{
					field:  "Tenure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTenure()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MakeDrawdownRequestValidationError{
				field:  "Tenure",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MakeDrawdownRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MakeDrawdownRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MakeDrawdownRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Urn

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return MakeDrawdownRequestMultiError(errors)
	}

	return nil
}

// MakeDrawdownRequestMultiError is an error wrapping multiple validation
// errors returned by MakeDrawdownRequest.ValidateAll() if the designated
// constraints aren't met.
type MakeDrawdownRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MakeDrawdownRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MakeDrawdownRequestMultiError) AllErrors() []error { return m }

// MakeDrawdownRequestValidationError is the validation error returned by
// MakeDrawdownRequest.Validate if the designated constraints aren't met.
type MakeDrawdownRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MakeDrawdownRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MakeDrawdownRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MakeDrawdownRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MakeDrawdownRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MakeDrawdownRequestValidationError) ErrorName() string {
	return "MakeDrawdownRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MakeDrawdownRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMakeDrawdownRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MakeDrawdownRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MakeDrawdownRequestValidationError{}

// Validate checks the field values on MakeDrawdownResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MakeDrawdownResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MakeDrawdownResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MakeDrawdownResponseMultiError, or nil if none found.
func (m *MakeDrawdownResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MakeDrawdownResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MakeDrawdownResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MakeDrawdownResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MakeDrawdownResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanId

	// no validation rules for ApplicantId

	// no validation rules for Urn

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return MakeDrawdownResponseMultiError(errors)
	}

	return nil
}

// MakeDrawdownResponseMultiError is an error wrapping multiple validation
// errors returned by MakeDrawdownResponse.ValidateAll() if the designated
// constraints aren't met.
type MakeDrawdownResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MakeDrawdownResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MakeDrawdownResponseMultiError) AllErrors() []error { return m }

// MakeDrawdownResponseValidationError is the validation error returned by
// MakeDrawdownResponse.Validate if the designated constraints aren't met.
type MakeDrawdownResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MakeDrawdownResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MakeDrawdownResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MakeDrawdownResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MakeDrawdownResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MakeDrawdownResponseValidationError) ErrorName() string {
	return "MakeDrawdownResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MakeDrawdownResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMakeDrawdownResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MakeDrawdownResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MakeDrawdownResponseValidationError{}

// Validate checks the field values on GetPdfAgreementRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPdfAgreementRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPdfAgreementRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPdfAgreementRequestMultiError, or nil if none found.
func (m *GetPdfAgreementRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPdfAgreementRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPdfAgreementRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPdfAgreementRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPdfAgreementRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	// no validation rules for ApplicationId

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return GetPdfAgreementRequestMultiError(errors)
	}

	return nil
}

// GetPdfAgreementRequestMultiError is an error wrapping multiple validation
// errors returned by GetPdfAgreementRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPdfAgreementRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPdfAgreementRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPdfAgreementRequestMultiError) AllErrors() []error { return m }

// GetPdfAgreementRequestValidationError is the validation error returned by
// GetPdfAgreementRequest.Validate if the designated constraints aren't met.
type GetPdfAgreementRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPdfAgreementRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPdfAgreementRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPdfAgreementRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPdfAgreementRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPdfAgreementRequestValidationError) ErrorName() string {
	return "GetPdfAgreementRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPdfAgreementRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPdfAgreementRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPdfAgreementRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPdfAgreementRequestValidationError{}

// Validate checks the field values on GetPdfAgreementResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPdfAgreementResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPdfAgreementResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPdfAgreementResponseMultiError, or nil if none found.
func (m *GetPdfAgreementResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPdfAgreementResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPdfAgreementResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPdfAgreementResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPdfAgreementResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PdfBase64Text

	// no validation rules for DocId

	if len(errors) > 0 {
		return GetPdfAgreementResponseMultiError(errors)
	}

	return nil
}

// GetPdfAgreementResponseMultiError is an error wrapping multiple validation
// errors returned by GetPdfAgreementResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPdfAgreementResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPdfAgreementResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPdfAgreementResponseMultiError) AllErrors() []error { return m }

// GetPdfAgreementResponseValidationError is the validation error returned by
// GetPdfAgreementResponse.Validate if the designated constraints aren't met.
type GetPdfAgreementResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPdfAgreementResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPdfAgreementResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPdfAgreementResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPdfAgreementResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPdfAgreementResponseValidationError) ErrorName() string {
	return "GetPdfAgreementResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPdfAgreementResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPdfAgreementResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPdfAgreementResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPdfAgreementResponseValidationError{}

// Validate checks the field values on SendBorrowerAgreementOtpRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendBorrowerAgreementOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendBorrowerAgreementOtpRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SendBorrowerAgreementOtpRequestMultiError, or nil if none found.
func (m *SendBorrowerAgreementOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendBorrowerAgreementOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendBorrowerAgreementOtpRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendBorrowerAgreementOtpRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendBorrowerAgreementOtpRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	// no validation rules for ApplicationId

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return SendBorrowerAgreementOtpRequestMultiError(errors)
	}

	return nil
}

// SendBorrowerAgreementOtpRequestMultiError is an error wrapping multiple
// validation errors returned by SendBorrowerAgreementOtpRequest.ValidateAll()
// if the designated constraints aren't met.
type SendBorrowerAgreementOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendBorrowerAgreementOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendBorrowerAgreementOtpRequestMultiError) AllErrors() []error { return m }

// SendBorrowerAgreementOtpRequestValidationError is the validation error
// returned by SendBorrowerAgreementOtpRequest.Validate if the designated
// constraints aren't met.
type SendBorrowerAgreementOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendBorrowerAgreementOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendBorrowerAgreementOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendBorrowerAgreementOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendBorrowerAgreementOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendBorrowerAgreementOtpRequestValidationError) ErrorName() string {
	return "SendBorrowerAgreementOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendBorrowerAgreementOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendBorrowerAgreementOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendBorrowerAgreementOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendBorrowerAgreementOtpRequestValidationError{}

// Validate checks the field values on SendBorrowerAgreementOtpResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SendBorrowerAgreementOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendBorrowerAgreementOtpResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SendBorrowerAgreementOtpResponseMultiError, or nil if none found.
func (m *SendBorrowerAgreementOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SendBorrowerAgreementOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendBorrowerAgreementOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendBorrowerAgreementOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendBorrowerAgreementOtpResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SendBorrowerAgreementOtpResponseMultiError(errors)
	}

	return nil
}

// SendBorrowerAgreementOtpResponseMultiError is an error wrapping multiple
// validation errors returned by
// SendBorrowerAgreementOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type SendBorrowerAgreementOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendBorrowerAgreementOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendBorrowerAgreementOtpResponseMultiError) AllErrors() []error { return m }

// SendBorrowerAgreementOtpResponseValidationError is the validation error
// returned by SendBorrowerAgreementOtpResponse.Validate if the designated
// constraints aren't met.
type SendBorrowerAgreementOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendBorrowerAgreementOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendBorrowerAgreementOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendBorrowerAgreementOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendBorrowerAgreementOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendBorrowerAgreementOtpResponseValidationError) ErrorName() string {
	return "SendBorrowerAgreementOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SendBorrowerAgreementOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendBorrowerAgreementOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendBorrowerAgreementOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendBorrowerAgreementOtpResponseValidationError{}

// Validate checks the field values on VerifyBorrowerAgreementOtpRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *VerifyBorrowerAgreementOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyBorrowerAgreementOtpRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// VerifyBorrowerAgreementOtpRequestMultiError, or nil if none found.
func (m *VerifyBorrowerAgreementOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyBorrowerAgreementOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyBorrowerAgreementOtpRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyBorrowerAgreementOtpRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyBorrowerAgreementOtpRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	// no validation rules for ApplicationId

	// no validation rules for DocId

	// no validation rules for Otp

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return VerifyBorrowerAgreementOtpRequestMultiError(errors)
	}

	return nil
}

// VerifyBorrowerAgreementOtpRequestMultiError is an error wrapping multiple
// validation errors returned by
// VerifyBorrowerAgreementOtpRequest.ValidateAll() if the designated
// constraints aren't met.
type VerifyBorrowerAgreementOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyBorrowerAgreementOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyBorrowerAgreementOtpRequestMultiError) AllErrors() []error { return m }

// VerifyBorrowerAgreementOtpRequestValidationError is the validation error
// returned by VerifyBorrowerAgreementOtpRequest.Validate if the designated
// constraints aren't met.
type VerifyBorrowerAgreementOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyBorrowerAgreementOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyBorrowerAgreementOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyBorrowerAgreementOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyBorrowerAgreementOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyBorrowerAgreementOtpRequestValidationError) ErrorName() string {
	return "VerifyBorrowerAgreementOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyBorrowerAgreementOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyBorrowerAgreementOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyBorrowerAgreementOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyBorrowerAgreementOtpRequestValidationError{}

// Validate checks the field values on VerifyBorrowerAgreementOtpResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *VerifyBorrowerAgreementOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyBorrowerAgreementOtpResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// VerifyBorrowerAgreementOtpResponseMultiError, or nil if none found.
func (m *VerifyBorrowerAgreementOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyBorrowerAgreementOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyBorrowerAgreementOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyBorrowerAgreementOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyBorrowerAgreementOtpResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AgreementSignedCopy

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return VerifyBorrowerAgreementOtpResponseMultiError(errors)
	}

	return nil
}

// VerifyBorrowerAgreementOtpResponseMultiError is an error wrapping multiple
// validation errors returned by
// VerifyBorrowerAgreementOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type VerifyBorrowerAgreementOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyBorrowerAgreementOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyBorrowerAgreementOtpResponseMultiError) AllErrors() []error { return m }

// VerifyBorrowerAgreementOtpResponseValidationError is the validation error
// returned by VerifyBorrowerAgreementOtpResponse.Validate if the designated
// constraints aren't met.
type VerifyBorrowerAgreementOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyBorrowerAgreementOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyBorrowerAgreementOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyBorrowerAgreementOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyBorrowerAgreementOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyBorrowerAgreementOtpResponseValidationError) ErrorName() string {
	return "VerifyBorrowerAgreementOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyBorrowerAgreementOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyBorrowerAgreementOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyBorrowerAgreementOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyBorrowerAgreementOtpResponseValidationError{}

// Validate checks the field values on GetLoanStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanStatusRequestMultiError, or nil if none found.
func (m *GetLoanStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicationId

	// no validation rules for Urn

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return GetLoanStatusRequestMultiError(errors)
	}

	return nil
}

// GetLoanStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetLoanStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLoanStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanStatusRequestMultiError) AllErrors() []error { return m }

// GetLoanStatusRequestValidationError is the validation error returned by
// GetLoanStatusRequest.Validate if the designated constraints aren't met.
type GetLoanStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanStatusRequestValidationError) ErrorName() string {
	return "GetLoanStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanStatusRequestValidationError{}

// Validate checks the field values on GetLoanStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanStatusResponseMultiError, or nil if none found.
func (m *GetLoanStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanStatusResponseValidationError{
					field:  "LoanStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanStatusResponseValidationError{
					field:  "LoanStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanStatusResponseValidationError{
				field:  "LoanStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastStatusTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanStatusResponseValidationError{
					field:  "LastStatusTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanStatusResponseValidationError{
					field:  "LastStatusTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastStatusTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanStatusResponseValidationError{
				field:  "LastStatusTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return GetLoanStatusResponseMultiError(errors)
	}

	return nil
}

// GetLoanStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetLoanStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLoanStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanStatusResponseMultiError) AllErrors() []error { return m }

// GetLoanStatusResponseValidationError is the validation error returned by
// GetLoanStatusResponse.Validate if the designated constraints aren't met.
type GetLoanStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanStatusResponseValidationError) ErrorName() string {
	return "GetLoanStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanStatusResponseValidationError{}

// Validate checks the field values on VerifyAndDownloadCkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyAndDownloadCkycRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyAndDownloadCkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyAndDownloadCkycRequestMultiError, or nil if none found.
func (m *VerifyAndDownloadCkycRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyAndDownloadCkycRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	// no validation rules for Pan

	// no validation rules for AuthFactor

	// no validation rules for AuthFactorType

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return VerifyAndDownloadCkycRequestMultiError(errors)
	}

	return nil
}

// VerifyAndDownloadCkycRequestMultiError is an error wrapping multiple
// validation errors returned by VerifyAndDownloadCkycRequest.ValidateAll() if
// the designated constraints aren't met.
type VerifyAndDownloadCkycRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyAndDownloadCkycRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyAndDownloadCkycRequestMultiError) AllErrors() []error { return m }

// VerifyAndDownloadCkycRequestValidationError is the validation error returned
// by VerifyAndDownloadCkycRequest.Validate if the designated constraints
// aren't met.
type VerifyAndDownloadCkycRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyAndDownloadCkycRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyAndDownloadCkycRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyAndDownloadCkycRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyAndDownloadCkycRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyAndDownloadCkycRequestValidationError) ErrorName() string {
	return "VerifyAndDownloadCkycRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyAndDownloadCkycRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyAndDownloadCkycRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyAndDownloadCkycRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyAndDownloadCkycRequestValidationError{}

// Validate checks the field values on VerifyAndDownloadCkycResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyAndDownloadCkycResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyAndDownloadCkycResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// VerifyAndDownloadCkycResponseMultiError, or nil if none found.
func (m *VerifyAndDownloadCkycResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyAndDownloadCkycResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCkycData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponseValidationError{
					field:  "CkycData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponseValidationError{
					field:  "CkycData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCkycData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponseValidationError{
				field:  "CkycData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return VerifyAndDownloadCkycResponseMultiError(errors)
	}

	return nil
}

// VerifyAndDownloadCkycResponseMultiError is an error wrapping multiple
// validation errors returned by VerifyAndDownloadCkycResponse.ValidateAll()
// if the designated constraints aren't met.
type VerifyAndDownloadCkycResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyAndDownloadCkycResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyAndDownloadCkycResponseMultiError) AllErrors() []error { return m }

// VerifyAndDownloadCkycResponseValidationError is the validation error
// returned by VerifyAndDownloadCkycResponse.Validate if the designated
// constraints aren't met.
type VerifyAndDownloadCkycResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyAndDownloadCkycResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyAndDownloadCkycResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyAndDownloadCkycResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyAndDownloadCkycResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyAndDownloadCkycResponseValidationError) ErrorName() string {
	return "VerifyAndDownloadCkycResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyAndDownloadCkycResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyAndDownloadCkycResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyAndDownloadCkycResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyAndDownloadCkycResponseValidationError{}

// Validate checks the field values on GetApplicantStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetApplicantStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetApplicantStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetApplicantStatusRequestMultiError, or nil if none found.
func (m *GetApplicantStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetApplicantStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetApplicantStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetApplicantStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetApplicantStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantId

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return GetApplicantStatusRequestMultiError(errors)
	}

	return nil
}

// GetApplicantStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetApplicantStatusRequest.ValidateAll() if the
// designated constraints aren't met.
type GetApplicantStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetApplicantStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetApplicantStatusRequestMultiError) AllErrors() []error { return m }

// GetApplicantStatusRequestValidationError is the validation error returned by
// GetApplicantStatusRequest.Validate if the designated constraints aren't met.
type GetApplicantStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetApplicantStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetApplicantStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetApplicantStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetApplicantStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetApplicantStatusRequestValidationError) ErrorName() string {
	return "GetApplicantStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetApplicantStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetApplicantStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetApplicantStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetApplicantStatusRequestValidationError{}

// Validate checks the field values on GetApplicantStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetApplicantStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetApplicantStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetApplicantStatusResponseMultiError, or nil if none found.
func (m *GetApplicantStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetApplicantStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetApplicantStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetApplicantStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetApplicantStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicantStatus

	if len(errors) > 0 {
		return GetApplicantStatusResponseMultiError(errors)
	}

	return nil
}

// GetApplicantStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetApplicantStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type GetApplicantStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetApplicantStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetApplicantStatusResponseMultiError) AllErrors() []error { return m }

// GetApplicantStatusResponseValidationError is the validation error returned
// by GetApplicantStatusResponse.Validate if the designated constraints aren't met.
type GetApplicantStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetApplicantStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetApplicantStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetApplicantStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetApplicantStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetApplicantStatusResponseValidationError) ErrorName() string {
	return "GetApplicantStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetApplicantStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetApplicantStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetApplicantStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetApplicantStatusResponseValidationError{}

// Validate checks the field values on Tenure with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Tenure) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Tenure with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TenureMultiError, or nil if none found.
func (m *Tenure) ValidateAll() error {
	return m.validate(true)
}

func (m *Tenure) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MinTenure

	// no validation rules for MaxTenure

	// no validation rules for TenureFrequency

	if len(errors) > 0 {
		return TenureMultiError(errors)
	}

	return nil
}

// TenureMultiError is an error wrapping multiple validation errors returned by
// Tenure.ValidateAll() if the designated constraints aren't met.
type TenureMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TenureMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TenureMultiError) AllErrors() []error { return m }

// TenureValidationError is the validation error returned by Tenure.Validate if
// the designated constraints aren't met.
type TenureValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TenureValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TenureValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TenureValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TenureValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TenureValidationError) ErrorName() string { return "TenureValidationError" }

// Error satisfies the builtin error interface
func (e TenureValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTenure.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TenureValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TenureValidationError{}

// Validate checks the field values on Roi with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Roi) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Roi with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RoiMultiError, or nil if none found.
func (m *Roi) ValidateAll() error {
	return m.validate(true)
}

func (m *Roi) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RoiValue

	// no validation rules for RoiType

	if len(errors) > 0 {
		return RoiMultiError(errors)
	}

	return nil
}

// RoiMultiError is an error wrapping multiple validation errors returned by
// Roi.ValidateAll() if the designated constraints aren't met.
type RoiMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RoiMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RoiMultiError) AllErrors() []error { return m }

// RoiValidationError is the validation error returned by Roi.Validate if the
// designated constraints aren't met.
type RoiValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RoiValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RoiValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RoiValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RoiValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RoiValidationError) ErrorName() string { return "RoiValidationError" }

// Error satisfies the builtin error interface
func (e RoiValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRoi.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RoiValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RoiValidationError{}

// Validate checks the field values on PfFees with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PfFees) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PfFees with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PfFeesMultiError, or nil if none found.
func (m *PfFees) ValidateAll() error {
	return m.validate(true)
}

func (m *PfFees) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PfType

	// no validation rules for PfFees

	if len(errors) > 0 {
		return PfFeesMultiError(errors)
	}

	return nil
}

// PfFeesMultiError is an error wrapping multiple validation errors returned by
// PfFees.ValidateAll() if the designated constraints aren't met.
type PfFeesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PfFeesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PfFeesMultiError) AllErrors() []error { return m }

// PfFeesValidationError is the validation error returned by PfFees.Validate if
// the designated constraints aren't met.
type PfFeesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PfFeesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PfFeesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PfFeesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PfFeesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PfFeesValidationError) ErrorName() string { return "PfFeesValidationError" }

// Error satisfies the builtin error interface
func (e PfFeesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPfFees.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PfFeesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PfFeesValidationError{}

// Validate checks the field values on GetRepaymentScheduleRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRepaymentScheduleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRepaymentScheduleRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRepaymentScheduleRequestMultiError, or nil if none found.
func (m *GetRepaymentScheduleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRepaymentScheduleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanId

	// no validation rules for LoanProgram

	// no validation rules for ApiVersion

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return GetRepaymentScheduleRequestMultiError(errors)
	}

	return nil
}

// GetRepaymentScheduleRequestMultiError is an error wrapping multiple
// validation errors returned by GetRepaymentScheduleRequest.ValidateAll() if
// the designated constraints aren't met.
type GetRepaymentScheduleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRepaymentScheduleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRepaymentScheduleRequestMultiError) AllErrors() []error { return m }

// GetRepaymentScheduleRequestValidationError is the validation error returned
// by GetRepaymentScheduleRequest.Validate if the designated constraints
// aren't met.
type GetRepaymentScheduleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRepaymentScheduleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRepaymentScheduleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRepaymentScheduleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRepaymentScheduleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRepaymentScheduleRequestValidationError) ErrorName() string {
	return "GetRepaymentScheduleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRepaymentScheduleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRepaymentScheduleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRepaymentScheduleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRepaymentScheduleRequestValidationError{}

// Validate checks the field values on GetRepaymentScheduleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRepaymentScheduleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRepaymentScheduleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRepaymentScheduleResponseMultiError, or nil if none found.
func (m *GetRepaymentScheduleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRepaymentScheduleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSchedules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRepaymentScheduleResponseValidationError{
						field:  fmt.Sprintf("Schedules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRepaymentScheduleResponseValidationError{
						field:  fmt.Sprintf("Schedules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRepaymentScheduleResponseValidationError{
					field:  fmt.Sprintf("Schedules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return GetRepaymentScheduleResponseMultiError(errors)
	}

	return nil
}

// GetRepaymentScheduleResponseMultiError is an error wrapping multiple
// validation errors returned by GetRepaymentScheduleResponse.ValidateAll() if
// the designated constraints aren't met.
type GetRepaymentScheduleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRepaymentScheduleResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRepaymentScheduleResponseMultiError) AllErrors() []error { return m }

// GetRepaymentScheduleResponseValidationError is the validation error returned
// by GetRepaymentScheduleResponse.Validate if the designated constraints
// aren't met.
type GetRepaymentScheduleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRepaymentScheduleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRepaymentScheduleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRepaymentScheduleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRepaymentScheduleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRepaymentScheduleResponseValidationError) ErrorName() string {
	return "GetRepaymentScheduleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRepaymentScheduleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRepaymentScheduleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRepaymentScheduleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRepaymentScheduleResponseValidationError{}

// Validate checks the field values on UploadDocumentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadDocumentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadDocumentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadDocumentRequestMultiError, or nil if none found.
func (m *UploadDocumentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadDocumentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadDocumentRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadDocumentRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadDocumentRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanProgram

	// no validation rules for File

	// no validation rules for LoanId

	// no validation rules for DocumentType

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return UploadDocumentRequestMultiError(errors)
	}

	return nil
}

// UploadDocumentRequestMultiError is an error wrapping multiple validation
// errors returned by UploadDocumentRequest.ValidateAll() if the designated
// constraints aren't met.
type UploadDocumentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadDocumentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadDocumentRequestMultiError) AllErrors() []error { return m }

// UploadDocumentRequestValidationError is the validation error returned by
// UploadDocumentRequest.Validate if the designated constraints aren't met.
type UploadDocumentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadDocumentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadDocumentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadDocumentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadDocumentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadDocumentRequestValidationError) ErrorName() string {
	return "UploadDocumentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadDocumentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadDocumentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadDocumentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadDocumentRequestValidationError{}

// Validate checks the field values on UploadDocumentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadDocumentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadDocumentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadDocumentResponseMultiError, or nil if none found.
func (m *UploadDocumentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadDocumentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadDocumentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadDocumentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadDocumentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DocumentId

	// no validation rules for DocumentTypeId

	// no validation rules for FileName

	// no validation rules for FilePath

	if len(errors) > 0 {
		return UploadDocumentResponseMultiError(errors)
	}

	return nil
}

// UploadDocumentResponseMultiError is an error wrapping multiple validation
// errors returned by UploadDocumentResponse.ValidateAll() if the designated
// constraints aren't met.
type UploadDocumentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadDocumentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadDocumentResponseMultiError) AllErrors() []error { return m }

// UploadDocumentResponseValidationError is the validation error returned by
// UploadDocumentResponse.Validate if the designated constraints aren't met.
type UploadDocumentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadDocumentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadDocumentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadDocumentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadDocumentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadDocumentResponseValidationError) ErrorName() string {
	return "UploadDocumentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadDocumentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadDocumentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadDocumentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadDocumentResponseValidationError{}

// Validate checks the field values on SaveCollectionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveCollectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveCollectionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveCollectionRequestMultiError, or nil if none found.
func (m *SaveCollectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveCollectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveCollectionRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveCollectionRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveCollectionRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanId

	// no validation rules for LoanProgram

	for idx, item := range m.GetPaymentSchedules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SaveCollectionRequestValidationError{
						field:  fmt.Sprintf("PaymentSchedules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SaveCollectionRequestValidationError{
						field:  fmt.Sprintf("PaymentSchedules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SaveCollectionRequestValidationError{
					field:  fmt.Sprintf("PaymentSchedules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return SaveCollectionRequestMultiError(errors)
	}

	return nil
}

// SaveCollectionRequestMultiError is an error wrapping multiple validation
// errors returned by SaveCollectionRequest.ValidateAll() if the designated
// constraints aren't met.
type SaveCollectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveCollectionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveCollectionRequestMultiError) AllErrors() []error { return m }

// SaveCollectionRequestValidationError is the validation error returned by
// SaveCollectionRequest.Validate if the designated constraints aren't met.
type SaveCollectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveCollectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveCollectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveCollectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveCollectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveCollectionRequestValidationError) ErrorName() string {
	return "SaveCollectionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SaveCollectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveCollectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveCollectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveCollectionRequestValidationError{}

// Validate checks the field values on SaveCollectionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveCollectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveCollectionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveCollectionResponseMultiError, or nil if none found.
func (m *SaveCollectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveCollectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveCollectionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveCollectionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return SaveCollectionResponseMultiError(errors)
	}

	return nil
}

// SaveCollectionResponseMultiError is an error wrapping multiple validation
// errors returned by SaveCollectionResponse.ValidateAll() if the designated
// constraints aren't met.
type SaveCollectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveCollectionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveCollectionResponseMultiError) AllErrors() []error { return m }

// SaveCollectionResponseValidationError is the validation error returned by
// SaveCollectionResponse.Validate if the designated constraints aren't met.
type SaveCollectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveCollectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveCollectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveCollectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveCollectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveCollectionResponseValidationError) ErrorName() string {
	return "SaveCollectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SaveCollectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveCollectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveCollectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveCollectionResponseValidationError{}

// Validate checks the field values on HashGenerationForOkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HashGenerationForOkycRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HashGenerationForOkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HashGenerationForOkycRequestMultiError, or nil if none found.
func (m *HashGenerationForOkycRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HashGenerationForOkycRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HashGenerationForOkycRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HashGenerationForOkycRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HashGenerationForOkycRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicationId

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return HashGenerationForOkycRequestMultiError(errors)
	}

	return nil
}

// HashGenerationForOkycRequestMultiError is an error wrapping multiple
// validation errors returned by HashGenerationForOkycRequest.ValidateAll() if
// the designated constraints aren't met.
type HashGenerationForOkycRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HashGenerationForOkycRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HashGenerationForOkycRequestMultiError) AllErrors() []error { return m }

// HashGenerationForOkycRequestValidationError is the validation error returned
// by HashGenerationForOkycRequest.Validate if the designated constraints
// aren't met.
type HashGenerationForOkycRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HashGenerationForOkycRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HashGenerationForOkycRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HashGenerationForOkycRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HashGenerationForOkycRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HashGenerationForOkycRequestValidationError) ErrorName() string {
	return "HashGenerationForOkycRequestValidationError"
}

// Error satisfies the builtin error interface
func (e HashGenerationForOkycRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHashGenerationForOkycRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HashGenerationForOkycRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HashGenerationForOkycRequestValidationError{}

// Validate checks the field values on HashGenerationForOkycResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HashGenerationForOkycResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HashGenerationForOkycResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// HashGenerationForOkycResponseMultiError, or nil if none found.
func (m *HashGenerationForOkycResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *HashGenerationForOkycResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HashGenerationForOkycResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HashGenerationForOkycResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HashGenerationForOkycResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HashGenerationForOkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HashGenerationForOkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HashGenerationForOkycResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HashGenerationForOkycResponseMultiError(errors)
	}

	return nil
}

// HashGenerationForOkycResponseMultiError is an error wrapping multiple
// validation errors returned by HashGenerationForOkycResponse.ValidateAll()
// if the designated constraints aren't met.
type HashGenerationForOkycResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HashGenerationForOkycResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HashGenerationForOkycResponseMultiError) AllErrors() []error { return m }

// HashGenerationForOkycResponseValidationError is the validation error
// returned by HashGenerationForOkycResponse.Validate if the designated
// constraints aren't met.
type HashGenerationForOkycResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HashGenerationForOkycResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HashGenerationForOkycResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HashGenerationForOkycResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HashGenerationForOkycResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HashGenerationForOkycResponseValidationError) ErrorName() string {
	return "HashGenerationForOkycResponseValidationError"
}

// Error satisfies the builtin error interface
func (e HashGenerationForOkycResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHashGenerationForOkycResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HashGenerationForOkycResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HashGenerationForOkycResponseValidationError{}

// Validate checks the field values on CaptchaGenerationForOkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CaptchaGenerationForOkycRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaptchaGenerationForOkycRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CaptchaGenerationForOkycRequestMultiError, or nil if none found.
func (m *CaptchaGenerationForOkycRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CaptchaGenerationForOkycRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaptchaGenerationForOkycRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaptchaGenerationForOkycRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaptchaGenerationForOkycRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Hash

	// no validation rules for ApplicationId

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return CaptchaGenerationForOkycRequestMultiError(errors)
	}

	return nil
}

// CaptchaGenerationForOkycRequestMultiError is an error wrapping multiple
// validation errors returned by CaptchaGenerationForOkycRequest.ValidateAll()
// if the designated constraints aren't met.
type CaptchaGenerationForOkycRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaptchaGenerationForOkycRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaptchaGenerationForOkycRequestMultiError) AllErrors() []error { return m }

// CaptchaGenerationForOkycRequestValidationError is the validation error
// returned by CaptchaGenerationForOkycRequest.Validate if the designated
// constraints aren't met.
type CaptchaGenerationForOkycRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaptchaGenerationForOkycRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaptchaGenerationForOkycRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaptchaGenerationForOkycRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaptchaGenerationForOkycRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaptchaGenerationForOkycRequestValidationError) ErrorName() string {
	return "CaptchaGenerationForOkycRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CaptchaGenerationForOkycRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaptchaGenerationForOkycRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaptchaGenerationForOkycRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaptchaGenerationForOkycRequestValidationError{}

// Validate checks the field values on CaptchaGenerationForOkycResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CaptchaGenerationForOkycResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaptchaGenerationForOkycResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CaptchaGenerationForOkycResponseMultiError, or nil if none found.
func (m *CaptchaGenerationForOkycResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CaptchaGenerationForOkycResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaptchaGenerationForOkycResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaptchaGenerationForOkycResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaptchaGenerationForOkycResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaptchaGenerationForOkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaptchaGenerationForOkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaptchaGenerationForOkycResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CaptchaGenerationForOkycResponseMultiError(errors)
	}

	return nil
}

// CaptchaGenerationForOkycResponseMultiError is an error wrapping multiple
// validation errors returned by
// CaptchaGenerationForOkycResponse.ValidateAll() if the designated
// constraints aren't met.
type CaptchaGenerationForOkycResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaptchaGenerationForOkycResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaptchaGenerationForOkycResponseMultiError) AllErrors() []error { return m }

// CaptchaGenerationForOkycResponseValidationError is the validation error
// returned by CaptchaGenerationForOkycResponse.Validate if the designated
// constraints aren't met.
type CaptchaGenerationForOkycResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaptchaGenerationForOkycResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaptchaGenerationForOkycResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaptchaGenerationForOkycResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaptchaGenerationForOkycResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaptchaGenerationForOkycResponseValidationError) ErrorName() string {
	return "CaptchaGenerationForOkycResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CaptchaGenerationForOkycResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaptchaGenerationForOkycResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaptchaGenerationForOkycResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaptchaGenerationForOkycResponseValidationError{}

// Validate checks the field values on GenerateOtpForOkycRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpForOkycRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpForOkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOtpForOkycRequestMultiError, or nil if none found.
func (m *GenerateOtpForOkycRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpForOkycRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpForOkycRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpForOkycRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpForOkycRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Hash

	// no validation rules for UidNumber

	// no validation rules for ApplicantId

	// no validation rules for CaptchaCode

	// no validation rules for RequestToken

	// no validation rules for CaptchaTxnId

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return GenerateOtpForOkycRequestMultiError(errors)
	}

	return nil
}

// GenerateOtpForOkycRequestMultiError is an error wrapping multiple validation
// errors returned by GenerateOtpForOkycRequest.ValidateAll() if the
// designated constraints aren't met.
type GenerateOtpForOkycRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpForOkycRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpForOkycRequestMultiError) AllErrors() []error { return m }

// GenerateOtpForOkycRequestValidationError is the validation error returned by
// GenerateOtpForOkycRequest.Validate if the designated constraints aren't met.
type GenerateOtpForOkycRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpForOkycRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpForOkycRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpForOkycRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpForOkycRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpForOkycRequestValidationError) ErrorName() string {
	return "GenerateOtpForOkycRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpForOkycRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpForOkycRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpForOkycRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpForOkycRequestValidationError{}

// Validate checks the field values on GenerateOtpForOkycResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpForOkycResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpForOkycResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOtpForOkycResponseMultiError, or nil if none found.
func (m *GenerateOtpForOkycResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpForOkycResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpForOkycResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpForOkycResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpForOkycResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpForOkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpForOkycResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpForOkycResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateOtpForOkycResponseMultiError(errors)
	}

	return nil
}

// GenerateOtpForOkycResponseMultiError is an error wrapping multiple
// validation errors returned by GenerateOtpForOkycResponse.ValidateAll() if
// the designated constraints aren't met.
type GenerateOtpForOkycResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpForOkycResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpForOkycResponseMultiError) AllErrors() []error { return m }

// GenerateOtpForOkycResponseValidationError is the validation error returned
// by GenerateOtpForOkycResponse.Validate if the designated constraints aren't met.
type GenerateOtpForOkycResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpForOkycResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpForOkycResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpForOkycResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpForOkycResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpForOkycResponseValidationError) ErrorName() string {
	return "GenerateOtpForOkycResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpForOkycResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpForOkycResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpForOkycResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpForOkycResponseValidationError{}

// Validate checks the field values on ValidateOtpForOkycRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateOtpForOkycRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateOtpForOkycRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateOtpForOkycRequestMultiError, or nil if none found.
func (m *ValidateOtpForOkycRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateOtpForOkycRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateOtpForOkycRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateOtpForOkycRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateOtpForOkycRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Hash

	// no validation rules for Otp

	// no validation rules for ApplicantId

	// no validation rules for RequestToken

	// no validation rules for LoanProgram

	// no validation rules for SchemeVersion

	if len(errors) > 0 {
		return ValidateOtpForOkycRequestMultiError(errors)
	}

	return nil
}

// ValidateOtpForOkycRequestMultiError is an error wrapping multiple validation
// errors returned by ValidateOtpForOkycRequest.ValidateAll() if the
// designated constraints aren't met.
type ValidateOtpForOkycRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateOtpForOkycRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateOtpForOkycRequestMultiError) AllErrors() []error { return m }

// ValidateOtpForOkycRequestValidationError is the validation error returned by
// ValidateOtpForOkycRequest.Validate if the designated constraints aren't met.
type ValidateOtpForOkycRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateOtpForOkycRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateOtpForOkycRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateOtpForOkycRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateOtpForOkycRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateOtpForOkycRequestValidationError) ErrorName() string {
	return "ValidateOtpForOkycRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateOtpForOkycRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateOtpForOkycRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateOtpForOkycRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateOtpForOkycRequestValidationError{}

// Validate checks the field values on ValidateOtpForOkycResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateOtpForOkycResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateOtpForOkycResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateOtpForOkycResponseMultiError, or nil if none found.
func (m *ValidateOtpForOkycResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateOtpForOkycResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateOtpForOkycResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateOtpForOkycResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateOtpForOkycResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateOtpForOkycResponseMultiError(errors)
	}

	return nil
}

// ValidateOtpForOkycResponseMultiError is an error wrapping multiple
// validation errors returned by ValidateOtpForOkycResponse.ValidateAll() if
// the designated constraints aren't met.
type ValidateOtpForOkycResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateOtpForOkycResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateOtpForOkycResponseMultiError) AllErrors() []error { return m }

// ValidateOtpForOkycResponseValidationError is the validation error returned
// by ValidateOtpForOkycResponse.Validate if the designated constraints aren't met.
type ValidateOtpForOkycResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateOtpForOkycResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateOtpForOkycResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateOtpForOkycResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateOtpForOkycResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateOtpForOkycResponseValidationError) ErrorName() string {
	return "ValidateOtpForOkycResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateOtpForOkycResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateOtpForOkycResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateOtpForOkycResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateOtpForOkycResponseValidationError{}

// Validate checks the field values on CreateRepaymentScheduleRequest_Schedule
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateRepaymentScheduleRequest_Schedule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateRepaymentScheduleRequest_Schedule with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CreateRepaymentScheduleRequest_ScheduleMultiError, or nil if none found.
func (m *CreateRepaymentScheduleRequest_Schedule) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRepaymentScheduleRequest_Schedule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleRequest_ScheduleValidationError{
				field:  "DueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipal()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "Principal",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "Principal",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipal()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleRequest_ScheduleValidationError{
				field:  "Principal",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "Interest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "Interest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleRequest_ScheduleValidationError{
				field:  "Interest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "TotalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "TotalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleRequest_ScheduleValidationError{
				field:  "TotalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOtherCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtherCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleRequest_ScheduleValidationError{
				field:  "OtherCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "PrincipalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepaymentScheduleRequest_ScheduleValidationError{
					field:  "PrincipalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepaymentScheduleRequest_ScheduleValidationError{
				field:  "PrincipalOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRepaymentScheduleRequest_ScheduleMultiError(errors)
	}

	return nil
}

// CreateRepaymentScheduleRequest_ScheduleMultiError is an error wrapping
// multiple validation errors returned by
// CreateRepaymentScheduleRequest_Schedule.ValidateAll() if the designated
// constraints aren't met.
type CreateRepaymentScheduleRequest_ScheduleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRepaymentScheduleRequest_ScheduleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRepaymentScheduleRequest_ScheduleMultiError) AllErrors() []error { return m }

// CreateRepaymentScheduleRequest_ScheduleValidationError is the validation
// error returned by CreateRepaymentScheduleRequest_Schedule.Validate if the
// designated constraints aren't met.
type CreateRepaymentScheduleRequest_ScheduleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRepaymentScheduleRequest_ScheduleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRepaymentScheduleRequest_ScheduleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRepaymentScheduleRequest_ScheduleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRepaymentScheduleRequest_ScheduleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRepaymentScheduleRequest_ScheduleValidationError) ErrorName() string {
	return "CreateRepaymentScheduleRequest_ScheduleValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRepaymentScheduleRequest_ScheduleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRepaymentScheduleRequest_Schedule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRepaymentScheduleRequest_ScheduleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRepaymentScheduleRequest_ScheduleValidationError{}

// Validate checks the field values on ForeClosureDetailsResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForeClosureDetailsResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForeClosureDetailsResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ForeClosureDetailsResponse_DataMultiError, or nil if none found.
func (m *ForeClosureDetailsResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ForeClosureDetailsResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	if all {
		switch v := interface{}(m.GetTotalOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeClosureDetailsResponse_DataValidationError{
					field:  "TotalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeClosureDetailsResponse_DataValidationError{
					field:  "TotalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeClosureDetailsResponse_DataValidationError{
				field:  "TotalOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeClosureDetailsResponse_DataValidationError{
					field:  "PrincipalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeClosureDetailsResponse_DataValidationError{
					field:  "PrincipalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeClosureDetailsResponse_DataValidationError{
				field:  "PrincipalOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeClosureDetailsResponse_DataValidationError{
					field:  "InterestOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeClosureDetailsResponse_DataValidationError{
					field:  "InterestOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeClosureDetailsResponse_DataValidationError{
				field:  "InterestOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPenaltyCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeClosureDetailsResponse_DataValidationError{
					field:  "PenaltyCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeClosureDetailsResponse_DataValidationError{
					field:  "PenaltyCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPenaltyCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeClosureDetailsResponse_DataValidationError{
				field:  "PenaltyCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeesCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeClosureDetailsResponse_DataValidationError{
					field:  "FeesCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeClosureDetailsResponse_DataValidationError{
					field:  "FeesCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeesCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeClosureDetailsResponse_DataValidationError{
				field:  "FeesCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOtherCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeClosureDetailsResponse_DataValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeClosureDetailsResponse_DataValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtherCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeClosureDetailsResponse_DataValidationError{
				field:  "OtherCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ForeClosureDetailsResponse_DataMultiError(errors)
	}

	return nil
}

// ForeClosureDetailsResponse_DataMultiError is an error wrapping multiple
// validation errors returned by ForeClosureDetailsResponse_Data.ValidateAll()
// if the designated constraints aren't met.
type ForeClosureDetailsResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForeClosureDetailsResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForeClosureDetailsResponse_DataMultiError) AllErrors() []error { return m }

// ForeClosureDetailsResponse_DataValidationError is the validation error
// returned by ForeClosureDetailsResponse_Data.Validate if the designated
// constraints aren't met.
type ForeClosureDetailsResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForeClosureDetailsResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForeClosureDetailsResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForeClosureDetailsResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForeClosureDetailsResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForeClosureDetailsResponse_DataValidationError) ErrorName() string {
	return "ForeClosureDetailsResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ForeClosureDetailsResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForeClosureDetailsResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForeClosureDetailsResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForeClosureDetailsResponse_DataValidationError{}

// Validate checks the field values on CancelLeadResponse_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelLeadResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelLeadResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelLeadResponse_DataMultiError, or nil if none found.
func (m *CancelLeadResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelLeadResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for ApplicantId

	if len(errors) > 0 {
		return CancelLeadResponse_DataMultiError(errors)
	}

	return nil
}

// CancelLeadResponse_DataMultiError is an error wrapping multiple validation
// errors returned by CancelLeadResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type CancelLeadResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelLeadResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelLeadResponse_DataMultiError) AllErrors() []error { return m }

// CancelLeadResponse_DataValidationError is the validation error returned by
// CancelLeadResponse_Data.Validate if the designated constraints aren't met.
type CancelLeadResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelLeadResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelLeadResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelLeadResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelLeadResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelLeadResponse_DataValidationError) ErrorName() string {
	return "CancelLeadResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e CancelLeadResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelLeadResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelLeadResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelLeadResponse_DataValidationError{}

// Validate checks the field values on
// GetCreditLineSchemesResponse_CreditLineDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetCreditLineSchemesResponse_CreditLineDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCreditLineSchemesResponse_CreditLineDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetCreditLineSchemesResponse_CreditLineDetailsMultiError, or nil if none found.
func (m *GetCreditLineSchemesResponse_CreditLineDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditLineSchemesResponse_CreditLineDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicantId

	if all {
		switch v := interface{}(m.GetUpperLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineDetailsValidationError{
					field:  "UpperLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineDetailsValidationError{
					field:  "UpperLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpperLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesResponse_CreditLineDetailsValidationError{
				field:  "UpperLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAvailableLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineDetailsValidationError{
					field:  "AvailableLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineDetailsValidationError{
					field:  "AvailableLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvailableLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesResponse_CreditLineDetailsValidationError{
				field:  "AvailableLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBlockLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineDetailsValidationError{
					field:  "BlockLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineDetailsValidationError{
					field:  "BlockLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBlockLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesResponse_CreditLineDetailsValidationError{
				field:  "BlockLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCreditLineSchemesResponse_CreditLineDetailsMultiError(errors)
	}

	return nil
}

// GetCreditLineSchemesResponse_CreditLineDetailsMultiError is an error
// wrapping multiple validation errors returned by
// GetCreditLineSchemesResponse_CreditLineDetails.ValidateAll() if the
// designated constraints aren't met.
type GetCreditLineSchemesResponse_CreditLineDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditLineSchemesResponse_CreditLineDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditLineSchemesResponse_CreditLineDetailsMultiError) AllErrors() []error { return m }

// GetCreditLineSchemesResponse_CreditLineDetailsValidationError is the
// validation error returned by
// GetCreditLineSchemesResponse_CreditLineDetails.Validate if the designated
// constraints aren't met.
type GetCreditLineSchemesResponse_CreditLineDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditLineSchemesResponse_CreditLineDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditLineSchemesResponse_CreditLineDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCreditLineSchemesResponse_CreditLineDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditLineSchemesResponse_CreditLineDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditLineSchemesResponse_CreditLineDetailsValidationError) ErrorName() string {
	return "GetCreditLineSchemesResponse_CreditLineDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditLineSchemesResponse_CreditLineDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditLineSchemesResponse_CreditLineDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditLineSchemesResponse_CreditLineDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditLineSchemesResponse_CreditLineDetailsValidationError{}

// Validate checks the field values on
// GetCreditLineSchemesResponse_CreditLineScheme with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetCreditLineSchemesResponse_CreditLineScheme) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCreditLineSchemesResponse_CreditLineScheme with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetCreditLineSchemesResponse_CreditLineSchemeMultiError, or nil if none found.
func (m *GetCreditLineSchemesResponse_CreditLineScheme) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditLineSchemesResponse_CreditLineScheme) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRoi()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
					field:  "Roi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
					field:  "Roi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRoi()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
				field:  "Roi",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmountConstraints()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
					field:  "AmountConstraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
					field:  "AmountConstraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountConstraints()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
				field:  "AmountConstraints",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmiDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
					field:  "EmiDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
					field:  "EmiDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmiDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
				field:  "EmiDueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTenure()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
					field:  "Tenure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
					field:  "Tenure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTenure()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
				field:  "Tenure",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPfFees()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
					field:  "PfFees",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
					field:  "PfFees",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPfFees()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesResponse_CreditLineSchemeValidationError{
				field:  "PfFees",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCreditLineSchemesResponse_CreditLineSchemeMultiError(errors)
	}

	return nil
}

// GetCreditLineSchemesResponse_CreditLineSchemeMultiError is an error wrapping
// multiple validation errors returned by
// GetCreditLineSchemesResponse_CreditLineScheme.ValidateAll() if the
// designated constraints aren't met.
type GetCreditLineSchemesResponse_CreditLineSchemeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditLineSchemesResponse_CreditLineSchemeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditLineSchemesResponse_CreditLineSchemeMultiError) AllErrors() []error { return m }

// GetCreditLineSchemesResponse_CreditLineSchemeValidationError is the
// validation error returned by
// GetCreditLineSchemesResponse_CreditLineScheme.Validate if the designated
// constraints aren't met.
type GetCreditLineSchemesResponse_CreditLineSchemeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditLineSchemesResponse_CreditLineSchemeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditLineSchemesResponse_CreditLineSchemeValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCreditLineSchemesResponse_CreditLineSchemeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditLineSchemesResponse_CreditLineSchemeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditLineSchemesResponse_CreditLineSchemeValidationError) ErrorName() string {
	return "GetCreditLineSchemesResponse_CreditLineSchemeValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditLineSchemesResponse_CreditLineSchemeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditLineSchemesResponse_CreditLineScheme.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditLineSchemesResponse_CreditLineSchemeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditLineSchemesResponse_CreditLineSchemeValidationError{}

// Validate checks the field values on
// GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsMultiError,
// or nil if none found.
func (m *GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMaxEmiAllowed()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError{
					field:  "MaxEmiAllowed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError{
					field:  "MaxEmiAllowed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxEmiAllowed()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError{
				field:  "MaxEmiAllowed",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinDrawdownAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError{
					field:  "MinDrawdownAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError{
					field:  "MinDrawdownAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinDrawdownAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError{
				field:  "MinDrawdownAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxDrawdownAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError{
					field:  "MaxDrawdownAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError{
					field:  "MaxDrawdownAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxDrawdownAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError{
				field:  "MaxDrawdownAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsMultiError(errors)
	}

	return nil
}

// GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsMultiError is
// an error wrapping multiple validation errors returned by
// GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints.ValidateAll()
// if the designated constraints aren't met.
type GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsMultiError) AllErrors() []error {
	return m
}

// GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError
// is the validation error returned by
// GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints.Validate if
// the designated constraints aren't met.
type GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError) ErrorName() string {
	return "GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditLineSchemesResponse_CreditLineScheme_AmountConstraints.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditLineSchemesResponse_CreditLineScheme_AmountConstraintsValidationError{}

// Validate checks the field values on AddPersonalDetailsResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddPersonalDetailsResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddPersonalDetailsResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AddPersonalDetailsResponse_DataMultiError, or nil if none found.
func (m *AddPersonalDetailsResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *AddPersonalDetailsResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicantId

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddPersonalDetailsResponse_DataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddPersonalDetailsResponse_DataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddPersonalDetailsResponse_DataValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetContactNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddPersonalDetailsResponse_DataValidationError{
					field:  "ContactNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddPersonalDetailsResponse_DataValidationError{
					field:  "ContactNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContactNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddPersonalDetailsResponse_DataValidationError{
				field:  "ContactNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddPersonalDetailsResponse_DataMultiError(errors)
	}

	return nil
}

// AddPersonalDetailsResponse_DataMultiError is an error wrapping multiple
// validation errors returned by AddPersonalDetailsResponse_Data.ValidateAll()
// if the designated constraints aren't met.
type AddPersonalDetailsResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddPersonalDetailsResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddPersonalDetailsResponse_DataMultiError) AllErrors() []error { return m }

// AddPersonalDetailsResponse_DataValidationError is the validation error
// returned by AddPersonalDetailsResponse_Data.Validate if the designated
// constraints aren't met.
type AddPersonalDetailsResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddPersonalDetailsResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddPersonalDetailsResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddPersonalDetailsResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddPersonalDetailsResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddPersonalDetailsResponse_DataValidationError) ErrorName() string {
	return "AddPersonalDetailsResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e AddPersonalDetailsResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddPersonalDetailsResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddPersonalDetailsResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddPersonalDetailsResponse_DataValidationError{}

// Validate checks the field values on ApplicantLookupResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApplicantLookupResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicantLookupResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplicantLookupResponse_DataMultiError, or nil if none found.
func (m *ApplicantLookupResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicantLookupResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicantId

	// no validation rules for Name

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetDetailsStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicantLookupResponse_DataValidationError{
					field:  "DetailsStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicantLookupResponse_DataValidationError{
					field:  "DetailsStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetailsStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicantLookupResponse_DataValidationError{
				field:  "DetailsStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActivationStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicantLookupResponse_DataValidationError{
					field:  "ActivationStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicantLookupResponse_DataValidationError{
					field:  "ActivationStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActivationStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicantLookupResponse_DataValidationError{
				field:  "ActivationStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ApplicantLookupResponse_DataMultiError(errors)
	}

	return nil
}

// ApplicantLookupResponse_DataMultiError is an error wrapping multiple
// validation errors returned by ApplicantLookupResponse_Data.ValidateAll() if
// the designated constraints aren't met.
type ApplicantLookupResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantLookupResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantLookupResponse_DataMultiError) AllErrors() []error { return m }

// ApplicantLookupResponse_DataValidationError is the validation error returned
// by ApplicantLookupResponse_Data.Validate if the designated constraints
// aren't met.
type ApplicantLookupResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantLookupResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantLookupResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicantLookupResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantLookupResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantLookupResponse_DataValidationError) ErrorName() string {
	return "ApplicantLookupResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicantLookupResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicantLookupResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantLookupResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantLookupResponse_DataValidationError{}

// Validate checks the field values on
// ApplicantLookupResponse_Data_DetailsStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ApplicantLookupResponse_Data_DetailsStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ApplicantLookupResponse_Data_DetailsStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ApplicantLookupResponse_Data_DetailsStatusMultiError, or nil if none found.
func (m *ApplicantLookupResponse_Data_DetailsStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicantLookupResponse_Data_DetailsStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BankingDetailStatus

	// no validation rules for AddressDetailStatus

	// no validation rules for EmploymentDetailStatus

	if len(errors) > 0 {
		return ApplicantLookupResponse_Data_DetailsStatusMultiError(errors)
	}

	return nil
}

// ApplicantLookupResponse_Data_DetailsStatusMultiError is an error wrapping
// multiple validation errors returned by
// ApplicantLookupResponse_Data_DetailsStatus.ValidateAll() if the designated
// constraints aren't met.
type ApplicantLookupResponse_Data_DetailsStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantLookupResponse_Data_DetailsStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantLookupResponse_Data_DetailsStatusMultiError) AllErrors() []error { return m }

// ApplicantLookupResponse_Data_DetailsStatusValidationError is the validation
// error returned by ApplicantLookupResponse_Data_DetailsStatus.Validate if
// the designated constraints aren't met.
type ApplicantLookupResponse_Data_DetailsStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantLookupResponse_Data_DetailsStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantLookupResponse_Data_DetailsStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicantLookupResponse_Data_DetailsStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantLookupResponse_Data_DetailsStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantLookupResponse_Data_DetailsStatusValidationError) ErrorName() string {
	return "ApplicantLookupResponse_Data_DetailsStatusValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicantLookupResponse_Data_DetailsStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicantLookupResponse_Data_DetailsStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantLookupResponse_Data_DetailsStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantLookupResponse_Data_DetailsStatusValidationError{}

// Validate checks the field values on
// ApplicantLookupResponse_Data_ActivationStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ApplicantLookupResponse_Data_ActivationStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ApplicantLookupResponse_Data_ActivationStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ApplicantLookupResponse_Data_ActivationStatusMultiError, or nil if none found.
func (m *ApplicantLookupResponse_Data_ActivationStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicantLookupResponse_Data_ActivationStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgreementStatus

	// no validation rules for MandateStatus

	if len(errors) > 0 {
		return ApplicantLookupResponse_Data_ActivationStatusMultiError(errors)
	}

	return nil
}

// ApplicantLookupResponse_Data_ActivationStatusMultiError is an error wrapping
// multiple validation errors returned by
// ApplicantLookupResponse_Data_ActivationStatus.ValidateAll() if the
// designated constraints aren't met.
type ApplicantLookupResponse_Data_ActivationStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantLookupResponse_Data_ActivationStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantLookupResponse_Data_ActivationStatusMultiError) AllErrors() []error { return m }

// ApplicantLookupResponse_Data_ActivationStatusValidationError is the
// validation error returned by
// ApplicantLookupResponse_Data_ActivationStatus.Validate if the designated
// constraints aren't met.
type ApplicantLookupResponse_Data_ActivationStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantLookupResponse_Data_ActivationStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantLookupResponse_Data_ActivationStatusValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ApplicantLookupResponse_Data_ActivationStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantLookupResponse_Data_ActivationStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantLookupResponse_Data_ActivationStatusValidationError) ErrorName() string {
	return "ApplicantLookupResponse_Data_ActivationStatusValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicantLookupResponse_Data_ActivationStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicantLookupResponse_Data_ActivationStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantLookupResponse_Data_ActivationStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantLookupResponse_Data_ActivationStatusValidationError{}

// Validate checks the field values on MakeDrawdownRequest_LoanTenure with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MakeDrawdownRequest_LoanTenure) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MakeDrawdownRequest_LoanTenure with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// MakeDrawdownRequest_LoanTenureMultiError, or nil if none found.
func (m *MakeDrawdownRequest_LoanTenure) ValidateAll() error {
	return m.validate(true)
}

func (m *MakeDrawdownRequest_LoanTenure) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenureFrequency

	// no validation rules for Value

	if len(errors) > 0 {
		return MakeDrawdownRequest_LoanTenureMultiError(errors)
	}

	return nil
}

// MakeDrawdownRequest_LoanTenureMultiError is an error wrapping multiple
// validation errors returned by MakeDrawdownRequest_LoanTenure.ValidateAll()
// if the designated constraints aren't met.
type MakeDrawdownRequest_LoanTenureMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MakeDrawdownRequest_LoanTenureMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MakeDrawdownRequest_LoanTenureMultiError) AllErrors() []error { return m }

// MakeDrawdownRequest_LoanTenureValidationError is the validation error
// returned by MakeDrawdownRequest_LoanTenure.Validate if the designated
// constraints aren't met.
type MakeDrawdownRequest_LoanTenureValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MakeDrawdownRequest_LoanTenureValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MakeDrawdownRequest_LoanTenureValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MakeDrawdownRequest_LoanTenureValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MakeDrawdownRequest_LoanTenureValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MakeDrawdownRequest_LoanTenureValidationError) ErrorName() string {
	return "MakeDrawdownRequest_LoanTenureValidationError"
}

// Error satisfies the builtin error interface
func (e MakeDrawdownRequest_LoanTenureValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMakeDrawdownRequest_LoanTenure.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MakeDrawdownRequest_LoanTenureValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MakeDrawdownRequest_LoanTenureValidationError{}

// Validate checks the field values on GetLoanStatusResponse_LoanStatus with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLoanStatusResponse_LoanStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanStatusResponse_LoanStatus with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLoanStatusResponse_LoanStatusMultiError, or nil if none found.
func (m *GetLoanStatusResponse_LoanStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanStatusResponse_LoanStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLoanData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanStatusResponse_LoanStatusValidationError{
					field:  "LoanData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanStatusResponse_LoanStatusValidationError{
					field:  "LoanData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanStatusResponse_LoanStatusValidationError{
				field:  "LoanData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanStatusResponse_LoanStatusValidationError{
					field:  "LoanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanStatusResponse_LoanStatusValidationError{
					field:  "LoanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanStatusResponse_LoanStatusValidationError{
				field:  "LoanDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanStatusResponse_LoanStatusMultiError(errors)
	}

	return nil
}

// GetLoanStatusResponse_LoanStatusMultiError is an error wrapping multiple
// validation errors returned by
// GetLoanStatusResponse_LoanStatus.ValidateAll() if the designated
// constraints aren't met.
type GetLoanStatusResponse_LoanStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanStatusResponse_LoanStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanStatusResponse_LoanStatusMultiError) AllErrors() []error { return m }

// GetLoanStatusResponse_LoanStatusValidationError is the validation error
// returned by GetLoanStatusResponse_LoanStatus.Validate if the designated
// constraints aren't met.
type GetLoanStatusResponse_LoanStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanStatusResponse_LoanStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanStatusResponse_LoanStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanStatusResponse_LoanStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanStatusResponse_LoanStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanStatusResponse_LoanStatusValidationError) ErrorName() string {
	return "GetLoanStatusResponse_LoanStatusValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanStatusResponse_LoanStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanStatusResponse_LoanStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanStatusResponse_LoanStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanStatusResponse_LoanStatusValidationError{}

// Validate checks the field values on
// GetLoanStatusResponse_LoanStatus_LoanData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLoanStatusResponse_LoanStatus_LoanData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanStatusResponse_LoanStatus_LoanData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetLoanStatusResponse_LoanStatus_LoanDataMultiError, or nil if none found.
func (m *GetLoanStatusResponse_LoanStatus_LoanData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanStatusResponse_LoanStatus_LoanData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanId

	// no validation rules for LoanCode

	// no validation rules for Status

	// no validation rules for Urn

	// no validation rules for Utr

	if len(errors) > 0 {
		return GetLoanStatusResponse_LoanStatus_LoanDataMultiError(errors)
	}

	return nil
}

// GetLoanStatusResponse_LoanStatus_LoanDataMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanStatusResponse_LoanStatus_LoanData.ValidateAll() if the designated
// constraints aren't met.
type GetLoanStatusResponse_LoanStatus_LoanDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanStatusResponse_LoanStatus_LoanDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanStatusResponse_LoanStatus_LoanDataMultiError) AllErrors() []error { return m }

// GetLoanStatusResponse_LoanStatus_LoanDataValidationError is the validation
// error returned by GetLoanStatusResponse_LoanStatus_LoanData.Validate if the
// designated constraints aren't met.
type GetLoanStatusResponse_LoanStatus_LoanDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanStatusResponse_LoanStatus_LoanDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanStatusResponse_LoanStatus_LoanDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanStatusResponse_LoanStatus_LoanDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanStatusResponse_LoanStatus_LoanDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanStatusResponse_LoanStatus_LoanDataValidationError) ErrorName() string {
	return "GetLoanStatusResponse_LoanStatus_LoanDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanStatusResponse_LoanStatus_LoanDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanStatusResponse_LoanStatus_LoanData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanStatusResponse_LoanStatus_LoanDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanStatusResponse_LoanStatus_LoanDataValidationError{}

// Validate checks the field values on
// GetLoanStatusResponse_LoanStatus_LoanDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLoanStatusResponse_LoanStatus_LoanDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanStatusResponse_LoanStatus_LoanDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetLoanStatusResponse_LoanStatus_LoanDetailsMultiError, or nil if none found.
func (m *GetLoanStatusResponse_LoanStatus_LoanDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanStatusResponse_LoanStatus_LoanDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisbursedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{
					field:  "DisbursedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{
					field:  "DisbursedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisbursedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{
				field:  "DisbursedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisbursementDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{
					field:  "DisbursementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{
					field:  "DisbursementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisbursementDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{
				field:  "DisbursementDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmiAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{
					field:  "EmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{
					field:  "EmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmiAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{
				field:  "EmiAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Tenure

	// no validation rules for Roi

	if len(errors) > 0 {
		return GetLoanStatusResponse_LoanStatus_LoanDetailsMultiError(errors)
	}

	return nil
}

// GetLoanStatusResponse_LoanStatus_LoanDetailsMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanStatusResponse_LoanStatus_LoanDetails.ValidateAll() if the
// designated constraints aren't met.
type GetLoanStatusResponse_LoanStatus_LoanDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanStatusResponse_LoanStatus_LoanDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanStatusResponse_LoanStatus_LoanDetailsMultiError) AllErrors() []error { return m }

// GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError is the
// validation error returned by
// GetLoanStatusResponse_LoanStatus_LoanDetails.Validate if the designated
// constraints aren't met.
type GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError) ErrorName() string {
	return "GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanStatusResponse_LoanStatus_LoanDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanStatusResponse_LoanStatus_LoanDetailsValidationError{}

// Validate checks the field values on VerifyAndDownloadCkycResponse_CkycData
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *VerifyAndDownloadCkycResponse_CkycData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// VerifyAndDownloadCkycResponse_CkycData with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// VerifyAndDownloadCkycResponse_CkycDataMultiError, or nil if none found.
func (m *VerifyAndDownloadCkycResponse_CkycData) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyAndDownloadCkycResponse_CkycData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPersonalData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_CkycDataValidationError{
					field:  "PersonalData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_CkycDataValidationError{
					field:  "PersonalData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPersonalData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponse_CkycDataValidationError{
				field:  "PersonalData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ImageType

	// no validation rules for Photo

	if len(errors) > 0 {
		return VerifyAndDownloadCkycResponse_CkycDataMultiError(errors)
	}

	return nil
}

// VerifyAndDownloadCkycResponse_CkycDataMultiError is an error wrapping
// multiple validation errors returned by
// VerifyAndDownloadCkycResponse_CkycData.ValidateAll() if the designated
// constraints aren't met.
type VerifyAndDownloadCkycResponse_CkycDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyAndDownloadCkycResponse_CkycDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyAndDownloadCkycResponse_CkycDataMultiError) AllErrors() []error { return m }

// VerifyAndDownloadCkycResponse_CkycDataValidationError is the validation
// error returned by VerifyAndDownloadCkycResponse_CkycData.Validate if the
// designated constraints aren't met.
type VerifyAndDownloadCkycResponse_CkycDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyAndDownloadCkycResponse_CkycDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyAndDownloadCkycResponse_CkycDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyAndDownloadCkycResponse_CkycDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyAndDownloadCkycResponse_CkycDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyAndDownloadCkycResponse_CkycDataValidationError) ErrorName() string {
	return "VerifyAndDownloadCkycResponse_CkycDataValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyAndDownloadCkycResponse_CkycDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyAndDownloadCkycResponse_CkycData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyAndDownloadCkycResponse_CkycDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyAndDownloadCkycResponse_CkycDataValidationError{}

// Validate checks the field values on
// VerifyAndDownloadCkycResponse_PersonalData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VerifyAndDownloadCkycResponse_PersonalData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// VerifyAndDownloadCkycResponse_PersonalData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// VerifyAndDownloadCkycResponse_PersonalDataMultiError, or nil if none found.
func (m *VerifyAndDownloadCkycResponse_PersonalData) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyAndDownloadCkycResponse_PersonalData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CkycNo

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponse_PersonalDataValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetKycDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "KycDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "KycDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponse_PersonalDataValidationError{
				field:  "KycDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponse_PersonalDataValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFatherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFatherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponse_PersonalDataValidationError{
				field:  "FatherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMotherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMotherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponse_PersonalDataValidationError{
				field:  "MotherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMobNum()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "MobNum",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "MobNum",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMobNum()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponse_PersonalDataValidationError{
				field:  "MobNum",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetAddresses()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "Addresses",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalDataValidationError{
					field:  "Addresses",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddresses()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponse_PersonalDataValidationError{
				field:  "Addresses",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyAndDownloadCkycResponse_PersonalDataMultiError(errors)
	}

	return nil
}

// VerifyAndDownloadCkycResponse_PersonalDataMultiError is an error wrapping
// multiple validation errors returned by
// VerifyAndDownloadCkycResponse_PersonalData.ValidateAll() if the designated
// constraints aren't met.
type VerifyAndDownloadCkycResponse_PersonalDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyAndDownloadCkycResponse_PersonalDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyAndDownloadCkycResponse_PersonalDataMultiError) AllErrors() []error { return m }

// VerifyAndDownloadCkycResponse_PersonalDataValidationError is the validation
// error returned by VerifyAndDownloadCkycResponse_PersonalData.Validate if
// the designated constraints aren't met.
type VerifyAndDownloadCkycResponse_PersonalDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyAndDownloadCkycResponse_PersonalDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyAndDownloadCkycResponse_PersonalDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyAndDownloadCkycResponse_PersonalDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyAndDownloadCkycResponse_PersonalDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyAndDownloadCkycResponse_PersonalDataValidationError) ErrorName() string {
	return "VerifyAndDownloadCkycResponse_PersonalDataValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyAndDownloadCkycResponse_PersonalDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyAndDownloadCkycResponse_PersonalData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyAndDownloadCkycResponse_PersonalDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyAndDownloadCkycResponse_PersonalDataValidationError{}

// Validate checks the field values on
// VerifyAndDownloadCkycResponse_PersonalData_Addresses with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyAndDownloadCkycResponse_PersonalData_Addresses) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// VerifyAndDownloadCkycResponse_PersonalData_Addresses with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// VerifyAndDownloadCkycResponse_PersonalData_AddressesMultiError, or nil if
// none found.
func (m *VerifyAndDownloadCkycResponse_PersonalData_Addresses) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyAndDownloadCkycResponse_PersonalData_Addresses) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPermanentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermanentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError{
				field:  "PermanentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCorrespondenceAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError{
					field:  "CorrespondenceAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError{
					field:  "CorrespondenceAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCorrespondenceAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError{
				field:  "CorrespondenceAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PermCorrAddressSame

	if len(errors) > 0 {
		return VerifyAndDownloadCkycResponse_PersonalData_AddressesMultiError(errors)
	}

	return nil
}

// VerifyAndDownloadCkycResponse_PersonalData_AddressesMultiError is an error
// wrapping multiple validation errors returned by
// VerifyAndDownloadCkycResponse_PersonalData_Addresses.ValidateAll() if the
// designated constraints aren't met.
type VerifyAndDownloadCkycResponse_PersonalData_AddressesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyAndDownloadCkycResponse_PersonalData_AddressesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyAndDownloadCkycResponse_PersonalData_AddressesMultiError) AllErrors() []error { return m }

// VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError is the
// validation error returned by
// VerifyAndDownloadCkycResponse_PersonalData_Addresses.Validate if the
// designated constraints aren't met.
type VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError) ErrorName() string {
	return "VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyAndDownloadCkycResponse_PersonalData_Addresses.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyAndDownloadCkycResponse_PersonalData_AddressesValidationError{}

// Validate checks the field values on GetRepaymentScheduleResponse_Schedule
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRepaymentScheduleResponse_Schedule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRepaymentScheduleResponse_Schedule
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRepaymentScheduleResponse_ScheduleMultiError, or nil if none found.
func (m *GetRepaymentScheduleResponse_Schedule) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRepaymentScheduleResponse_Schedule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for InstallmentNumber

	if all {
		switch v := interface{}(m.GetDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "DueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDueAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "DueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "DueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDueAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "DueAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "PrincipalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "InterestAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "InterestAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "InterestAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PaymentStatus

	if all {
		switch v := interface{}(m.GetReceivedDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "ReceivedDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "ReceivedDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReceivedDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "ReceivedDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReceivedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "ReceivedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "ReceivedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReceivedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "ReceivedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaidPrincipalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PaidPrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PaidPrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaidPrincipalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "PaidPrincipalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaidInterestAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PaidInterestAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PaidInterestAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaidInterestAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "PaidInterestAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLpi()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "Lpi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "Lpi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLpi()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "Lpi",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOtherCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtherCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "OtherCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBounceCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "BounceCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "BounceCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBounceCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "BounceCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPostPaymentPrincipalOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PostPaymentPrincipalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PostPaymentPrincipalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostPaymentPrincipalOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "PostPaymentPrincipalOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPostPaymentInterestOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PostPaymentInterestOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PostPaymentInterestOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostPaymentInterestOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "PostPaymentInterestOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWaivedCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "WaivedCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "WaivedCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWaivedCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "WaivedCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaidLatePaymentInterest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PaidLatePaymentInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PaidLatePaymentInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaidLatePaymentInterest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "PaidLatePaymentInterest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaidOtherCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PaidOtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PaidOtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaidOtherCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "PaidOtherCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaidBounceCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PaidBounceCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PaidBounceCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaidBounceCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "PaidBounceCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPostPaymentChargesOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PostPaymentChargesOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRepaymentScheduleResponse_ScheduleValidationError{
					field:  "PostPaymentChargesOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostPaymentChargesOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRepaymentScheduleResponse_ScheduleValidationError{
				field:  "PostPaymentChargesOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRepaymentScheduleResponse_ScheduleMultiError(errors)
	}

	return nil
}

// GetRepaymentScheduleResponse_ScheduleMultiError is an error wrapping
// multiple validation errors returned by
// GetRepaymentScheduleResponse_Schedule.ValidateAll() if the designated
// constraints aren't met.
type GetRepaymentScheduleResponse_ScheduleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRepaymentScheduleResponse_ScheduleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRepaymentScheduleResponse_ScheduleMultiError) AllErrors() []error { return m }

// GetRepaymentScheduleResponse_ScheduleValidationError is the validation error
// returned by GetRepaymentScheduleResponse_Schedule.Validate if the
// designated constraints aren't met.
type GetRepaymentScheduleResponse_ScheduleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRepaymentScheduleResponse_ScheduleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRepaymentScheduleResponse_ScheduleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRepaymentScheduleResponse_ScheduleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRepaymentScheduleResponse_ScheduleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRepaymentScheduleResponse_ScheduleValidationError) ErrorName() string {
	return "GetRepaymentScheduleResponse_ScheduleValidationError"
}

// Error satisfies the builtin error interface
func (e GetRepaymentScheduleResponse_ScheduleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRepaymentScheduleResponse_Schedule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRepaymentScheduleResponse_ScheduleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRepaymentScheduleResponse_ScheduleValidationError{}

// Validate checks the field values on SaveCollectionRequest_PaymentSchedule
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SaveCollectionRequest_PaymentSchedule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveCollectionRequest_PaymentSchedule
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SaveCollectionRequest_PaymentScheduleMultiError, or nil if none found.
func (m *SaveCollectionRequest_PaymentSchedule) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveCollectionRequest_PaymentSchedule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PaymentMode

	if all {
		switch v := interface{}(m.GetTransactionDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "TransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "TransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveCollectionRequest_PaymentScheduleValidationError{
				field:  "TransactionDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PaymentStatus

	if all {
		switch v := interface{}(m.GetPaidTotalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "PaidTotalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "PaidTotalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaidTotalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveCollectionRequest_PaymentScheduleValidationError{
				field:  "PaidTotalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VoucherNo

	if all {
		switch v := interface{}(m.GetBounceCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "BounceCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "BounceCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBounceCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveCollectionRequest_PaymentScheduleValidationError{
				field:  "BounceCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCollectionCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "CollectionCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "CollectionCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCollectionCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveCollectionRequest_PaymentScheduleValidationError{
				field:  "CollectionCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOtherCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtherCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveCollectionRequest_PaymentScheduleValidationError{
				field:  "OtherCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLpiCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "LpiCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "LpiCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLpiCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveCollectionRequest_PaymentScheduleValidationError{
				field:  "LpiCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSettelmentDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "SettelmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "SettelmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSettelmentDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveCollectionRequest_PaymentScheduleValidationError{
				field:  "SettelmentDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveCollectionRequest_PaymentScheduleValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveCollectionRequest_PaymentScheduleValidationError{
				field:  "DueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SaveCollectionRequest_PaymentScheduleMultiError(errors)
	}

	return nil
}

// SaveCollectionRequest_PaymentScheduleMultiError is an error wrapping
// multiple validation errors returned by
// SaveCollectionRequest_PaymentSchedule.ValidateAll() if the designated
// constraints aren't met.
type SaveCollectionRequest_PaymentScheduleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveCollectionRequest_PaymentScheduleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveCollectionRequest_PaymentScheduleMultiError) AllErrors() []error { return m }

// SaveCollectionRequest_PaymentScheduleValidationError is the validation error
// returned by SaveCollectionRequest_PaymentSchedule.Validate if the
// designated constraints aren't met.
type SaveCollectionRequest_PaymentScheduleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveCollectionRequest_PaymentScheduleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveCollectionRequest_PaymentScheduleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveCollectionRequest_PaymentScheduleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveCollectionRequest_PaymentScheduleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveCollectionRequest_PaymentScheduleValidationError) ErrorName() string {
	return "SaveCollectionRequest_PaymentScheduleValidationError"
}

// Error satisfies the builtin error interface
func (e SaveCollectionRequest_PaymentScheduleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveCollectionRequest_PaymentSchedule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveCollectionRequest_PaymentScheduleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveCollectionRequest_PaymentScheduleValidationError{}

// Validate checks the field values on HashGenerationForOkycResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *HashGenerationForOkycResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HashGenerationForOkycResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// HashGenerationForOkycResponse_DataMultiError, or nil if none found.
func (m *HashGenerationForOkycResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *HashGenerationForOkycResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Hash

	if len(errors) > 0 {
		return HashGenerationForOkycResponse_DataMultiError(errors)
	}

	return nil
}

// HashGenerationForOkycResponse_DataMultiError is an error wrapping multiple
// validation errors returned by
// HashGenerationForOkycResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type HashGenerationForOkycResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HashGenerationForOkycResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HashGenerationForOkycResponse_DataMultiError) AllErrors() []error { return m }

// HashGenerationForOkycResponse_DataValidationError is the validation error
// returned by HashGenerationForOkycResponse_Data.Validate if the designated
// constraints aren't met.
type HashGenerationForOkycResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HashGenerationForOkycResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HashGenerationForOkycResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HashGenerationForOkycResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HashGenerationForOkycResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HashGenerationForOkycResponse_DataValidationError) ErrorName() string {
	return "HashGenerationForOkycResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e HashGenerationForOkycResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHashGenerationForOkycResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HashGenerationForOkycResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HashGenerationForOkycResponse_DataValidationError{}

// Validate checks the field values on CaptchaGenerationForOkycResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CaptchaGenerationForOkycResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaptchaGenerationForOkycResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CaptchaGenerationForOkycResponse_DataMultiError, or nil if none found.
func (m *CaptchaGenerationForOkycResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *CaptchaGenerationForOkycResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaptchaImage

	// no validation rules for RequestToken

	// no validation rules for CaptchaTxnId

	if len(errors) > 0 {
		return CaptchaGenerationForOkycResponse_DataMultiError(errors)
	}

	return nil
}

// CaptchaGenerationForOkycResponse_DataMultiError is an error wrapping
// multiple validation errors returned by
// CaptchaGenerationForOkycResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type CaptchaGenerationForOkycResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaptchaGenerationForOkycResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaptchaGenerationForOkycResponse_DataMultiError) AllErrors() []error { return m }

// CaptchaGenerationForOkycResponse_DataValidationError is the validation error
// returned by CaptchaGenerationForOkycResponse_Data.Validate if the
// designated constraints aren't met.
type CaptchaGenerationForOkycResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaptchaGenerationForOkycResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaptchaGenerationForOkycResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaptchaGenerationForOkycResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaptchaGenerationForOkycResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaptchaGenerationForOkycResponse_DataValidationError) ErrorName() string {
	return "CaptchaGenerationForOkycResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e CaptchaGenerationForOkycResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaptchaGenerationForOkycResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaptchaGenerationForOkycResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaptchaGenerationForOkycResponse_DataValidationError{}

// Validate checks the field values on GenerateOtpForOkycResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpForOkycResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpForOkycResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GenerateOtpForOkycResponse_DataMultiError, or nil if none found.
func (m *GenerateOtpForOkycResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpForOkycResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnId

	if len(errors) > 0 {
		return GenerateOtpForOkycResponse_DataMultiError(errors)
	}

	return nil
}

// GenerateOtpForOkycResponse_DataMultiError is an error wrapping multiple
// validation errors returned by GenerateOtpForOkycResponse_Data.ValidateAll()
// if the designated constraints aren't met.
type GenerateOtpForOkycResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpForOkycResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpForOkycResponse_DataMultiError) AllErrors() []error { return m }

// GenerateOtpForOkycResponse_DataValidationError is the validation error
// returned by GenerateOtpForOkycResponse_Data.Validate if the designated
// constraints aren't met.
type GenerateOtpForOkycResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpForOkycResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpForOkycResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpForOkycResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpForOkycResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpForOkycResponse_DataValidationError) ErrorName() string {
	return "GenerateOtpForOkycResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpForOkycResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpForOkycResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpForOkycResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpForOkycResponse_DataValidationError{}
