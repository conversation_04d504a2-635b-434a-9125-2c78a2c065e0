//nolint:goimports,dupl
package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/activity/brecaller"
	"github.com/epifi/gamma/preapprovedloan/helper"
	reProvider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/provider"
)

func (p *Processor) OfferCreation(ctx context.Context, actReq *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := ExecuteWork(ctx, p.loanStepExecutionDao, actReq.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}

		lg := activity.GetLogger(ctx)
		var filteredLoecs, loecs []*palPb.LoanOfferEligibilityCriteria
		userFeatProp, err := helper.GetUserFeatureProperty(ctx, lse.GetActorId(), p.onbClient, p.savingsClient)
		if err != nil {
			lg.Error("error getting user feature property", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in fetching user feature property: %v", err))
		}

		loecs, err = helper.GetCDCEligibleLoecs(ctx, p.loanOfferEligibilityCriteriaDao, lse.GetActorId(), userFeatProp.IsFiSAHolder)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("failed to fetch loecs", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get eligible loecs: %v", err))
		}
		// we need to call bre only for LOEC which were approved by pre bre
		for _, loec := range loecs {
			if loec.GetSubStatus() == palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_PRE_BRE || loec.GetSubStatus() == palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI_PRE_BRE {
				filteredLoecs = append(filteredLoecs, loec)
			}
		}
		if len(filteredLoecs) == 0 {
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED
			res.LseFieldMasks = append(res.LseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
			err = p.updateLrNextActionBasisOfRecommendationEngine(ctx, lse.GetRefId())
			if err != nil {
				return nil, err
			}
			return res, nil
		}
		for _, filteredLoec := range filteredLoecs {
			breCaller, breCallerErr := p.breCallerFactory.GetBreCaller(ctx, &brecaller.GetBreCallerRequest{
				Loec: filteredLoec,
			})
			if breCallerErr != nil {
				lg.Error("failed to get bre caller", zap.Error(breCallerErr), zap.String("LOEC_ID", filteredLoec.GetId()))
				return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("failed to get bre caller: %v", breCallerErr))
			}
			checkAndGetLOResp, checkAndGetLOErr := breCaller.CheckAndGetLoanOffer(ctx, &brecaller.CheckAndGetLoanOfferRequest{
				Loec: filteredLoec,
			})
			if checkAndGetLOErr != nil {
				lg.Error("failed to get and get loan offer", zap.Error(checkAndGetLOErr), zap.String("LOEC_ID", filteredLoec.GetId()))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get and get loan offer: %v", checkAndGetLOErr))
			}

			ownership := helper.GetPalOwnership(filteredLoec.GetVendor())
			ctxWithOwnership := epificontext.WithOwnership(ctx, ownership)
			txnExec, txnExecErr := helper.GetTxnExecutorByOwnership(ctxWithOwnership, p.txnExecutorProvider)
			if txnExecErr != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get txn executor by ownership, err: %v", txnExecErr))
			}
			// This transaction should be the last thing in this activity because once the txn is committed, the next activity attempt will skip since LOECs would have been marked as approved
			txnErr := txnExec.RunTxn(ctxWithOwnership, func(txnCtx context.Context) error {
				if checkAndGetLOResp.LoanOffer != nil {
					oldOffer, oldOfferErr := p.loanOffersDao.GetActiveOffersByActorIdAndLoanPrograms(txnCtx, filteredLoec.GetActorId(), []palPb.LoanProgram{filteredLoec.GetLoanProgram()})
					if oldOfferErr != nil && !errors.Is(oldOfferErr, epifierrors.ErrRecordNotFound) {
						return errors.Wrap(oldOfferErr, fmt.Sprintf("failed to fetch old loan offer for actor and loan program, err: %v", oldOfferErr))
					}
					// TODO: dedupe offer to avoid creating new offer for every retry
					for _, oldLoanOffer := range oldOffer {
						if err = p.loanOffersDao.DeactivateLoanOffer(txnCtx, oldLoanOffer.GetId()); err != nil {
							return errors.Wrap(err, fmt.Sprintf("failed to deactivate already existing offer, err: %v", err))
						}
					}
					lo, loErr := p.loanOffersDao.Create(txnCtx, checkAndGetLOResp.LoanOffer)
					if loErr != nil {
						return errors.Wrap(loErr, fmt.Sprintf("failed to create loan offer, err: %v", loErr))
					}
					checkAndGetLOResp.UpdatedLoec.OfferId = lo.GetId()
					checkAndGetLOResp.UpdateFieldMask = append(checkAndGetLOResp.UpdateFieldMask, palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_OFFER_ID)
				}

				if len(checkAndGetLOResp.UpdateFieldMask) > 0 {
					err = p.loanOfferEligibilityCriteriaDao.Update(txnCtx, checkAndGetLOResp.UpdatedLoec, checkAndGetLOResp.UpdateFieldMask)
					if err != nil {
						lg.Error("failed to update loec", zap.Error(err), zap.String("LOEC_ID", filteredLoec.GetId()))
						return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update non fi-core loec, err: %v", err))
					}
				}
				return nil
			})
			if txnErr != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update ls in txn, err: %v", txnErr.Error()))
			}
		}

		err = p.updateLrNextActionBasisOfRecommendationEngine(ctx, lse.GetRefId())
		if err != nil {
			return nil, err
		}

		return res, nil
	})
	return actRes, actErr
}

// todo(Anupam): Cleanup
func (p *Processor) updateLrNextActionBasisOfRecommendationEngine(ctx context.Context, lrId string) error {
	lg := activity.GetLogger(ctx)

	loanRequest, err := p.loanRequestDao.GetById(ctx, lrId)
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting lr by id, err: %v", err))
	}

	reResponse, reErr := p.recommendationEngine.GetRecommendedLoanOption(ctx, &reProvider.GetRecommendedLoanOptionRequest{
		ActorId: loanRequest.GetActorId(),
	})
	if reErr != nil {
		lg.Error("unable to get details from recommendation engine", zap.Error(reErr), zap.String(logger.ACTOR_ID_V2, loanRequest.GetActorId()))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to get details from recommendation engine,err: %v", reErr))
	}
	if reResponse != nil && reResponse.ActiveOffer != nil {
		loanRequest.NextAction = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
		}
	} else {
		loanRequest.NextAction = nil
	}

	updateErr := p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
	})
	if updateErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lr next action, err: %v", updateErr))
	}

	return nil
}

func (p *Processor) VendorHardOfferCreation(ctx context.Context, actReq *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := ExecuteWork(ctx, p.loanStepExecutionDao, actReq.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}

		lg := activity.GetLogger(ctx)
		ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(actReq.GetVendor()))

		loecs, err := p.loanOfferEligibilityCriteriaDao.GetActiveLoecsByActorIdLoanProgramsAndStatuses(ctx, lse.GetActorId(), []palPb.LoanProgram{actReq.GetLoanProgram()}, nil, 0, true)
		if err != nil || len(loecs) == 0 {
			lg.Error("failed to get active loec", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get active loec, err: %v", err))
		}
		loec := loecs[0]

		offerAlreadyCreated := false
		if loec.GetOfferId() != "" {
			lo, loErr := p.loanOffersDao.GetById(ctx, loec.GetOfferId())
			if loErr != nil && !errors.Is(loErr, epifierrors.ErrRecordNotFound) {
				lg.Error("failed to get offer by id", zap.Error(loErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get loan offer, err: %v", loErr))
			}
			offerAlreadyCreated = lo != nil && lo.GetLoanOfferType() == palPb.LoanOfferType_LOAN_OFFER_TYPE_HARD
		}

		if !offerAlreadyCreated {
			breCaller, breCallerErr := p.breCallerFactory.GetBreCaller(ctx, &brecaller.GetBreCallerRequest{
				Loec:              loec,
				IsVendorHardOffer: true,
			})
			if breCallerErr != nil {
				lg.Error("failed to get bre caller", zap.Error(breCallerErr), zap.String("LOEC_ID", loec.GetId()))
				return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("failed to get bre caller: %v", breCallerErr))
			}
			fetchHardVendorLOResp, fetchHardVendorLOErr := breCaller.FetchHardVendorLoanOffer(ctx, &brecaller.CheckAndGetLoanOfferRequest{
				Loec: loec,
			})
			if fetchHardVendorLOErr != nil {
				lg.Error("failed to fetch vendor hard loan offer", zap.Error(fetchHardVendorLOErr), zap.String("LOEC_ID", loec.GetId()))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch vendor hard loan offer, err: %v", fetchHardVendorLOErr))
			}

			txnExec, txnExecErr := helper.GetTxnExecutorByOwnership(ctx, p.txnExecutorProvider)
			if txnExecErr != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get txn executor by ownership, err: %v", txnExecErr))
			}
			// This transaction should be the last thing in this activity because once the txn is committed, the next activity attempt will skip since LOECs would have been marked as approved
			txnErr := txnExec.RunTxn(ctx, func(ctx context.Context) error {
				if fetchHardVendorLOResp.LoanOffer != nil {
					oldOffer, oldOfferErr := p.loanOffersDao.GetActiveOffersByActorIdAndLoanPrograms(ctx, loec.GetActorId(), []palPb.LoanProgram{loec.GetLoanProgram()})
					if oldOfferErr != nil && !errors.Is(oldOfferErr, epifierrors.ErrRecordNotFound) {
						return errors.Wrap(oldOfferErr, "failed to fetch old loan offer for actor and loan program")
					}
					// TODO: dedupe offer to avoid creating new offer for every retry
					for _, oldLoanOffer := range oldOffer {
						if err = p.loanOffersDao.DeactivateLoanOffer(ctx, oldLoanOffer.GetId()); err != nil {
							return errors.Wrap(err, "failed to deactivate already existing offer")
						}
					}
					lo, loErr := p.loanOffersDao.Create(ctx, fetchHardVendorLOResp.LoanOffer)
					if loErr != nil {
						return errors.Wrap(loErr, "failed to create loan offer")
					}

					lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
					if lrErr != nil {
						return errors.Wrap(lrErr, "failed to get loan request")
					}
					lr.OfferId = lo.GetId()
					lrErr = p.loanRequestDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_OFFER_ID})
					if lrErr != nil {
						return errors.Wrap(lrErr, "failed to update loan request")
					}

					fetchHardVendorLOResp.UpdatedLoec.OfferId = lo.GetId()
					fetchHardVendorLOResp.UpdateFieldMask = append(fetchHardVendorLOResp.UpdateFieldMask, palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_OFFER_ID)
				}

				if len(fetchHardVendorLOResp.UpdateFieldMask) > 0 {
					err = p.loanOfferEligibilityCriteriaDao.Update(ctx, fetchHardVendorLOResp.UpdatedLoec, fetchHardVendorLOResp.UpdateFieldMask)
					if err != nil {
						lg.Error("failed to update loec", zap.Error(err), zap.String("LOEC_ID", loec.GetId()))
						return errors.Wrap(err, "failed to update non fi-core loec")
					}
				}
				return nil
			})
			if txnErr != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update ls in txn, err: %v", txnErr.Error()))
			}
		}

		// TODO: this might not be needed
		err = p.updateLrNextActionBasisOfRecommendationEngine(ctx, lse.GetRefId())
		if err != nil {
			return nil, err
		}

		return res, nil
	})
	return actRes, actErr
}
