// nolint:goimports
package preapprovedloan

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/landing_provider"
)

// GetLoanUserStatusV2 - This rpc will return the user status of the Loan user.
func (s *Service) GetLoanUserStatusV2(ctx context.Context, req *palPb.GetLoanUserStatusV2Request) (*palPb.GetLoanUserStatusV2Response, error) {
	// landing data tells the current user status what user is eligible for
	dataExistenceRes := s.dataExistenceManager.GetOrRefreshLoanDataExistenceCache(ctx, req.GetActorId())
	landingRes, reErr := s.landingProvider.GetLandingPageDataForActor(ctx, &landing_provider.GetLandingPageDataForActorRequest{
		ActorId:                    req.GetActorId(),
		OwnershipFilterMap:         dataExistenceRes.GetDataExistenceMap(),
		OwnershipFilterMapForLoecs: dataExistenceRes.GetLoecDataExistenceMap(),
	})
	if reErr != nil {
		logger.Error(ctx, "unable to get details from landing provider", zap.Error(reErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &palPb.GetLoanUserStatusV2Response{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// Take out all the loan requests from the DB
	loanRequests, lrErr := s.getMultiDBLoanRequests(ctx, &SegregatedLoanRequestsRequest{
		actorId:        req.GetActorId(),
		updatedAtAfter: req.GetUpdatedAtAfter(),
	})
	if lrErr != nil {
		logger.Error(ctx, "unable to get loan requests", zap.Error(lrErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &palPb.GetLoanUserStatusV2Response{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// latest loan request of eligibility and application
	var latestApplicationLoanRequest, latestEligibilityLoanRequest *palPb.LoanRequest
	if len(loanRequests.GetEligibilityLoanRequests()) > 0 {
		latestEligibilityLoanRequest = loanRequests.GetEligibilityLoanRequests()[0]
	}
	if len(loanRequests.GetApplicationLoanRequests()) > 0 {
		latestApplicationLoanRequest = loanRequests.GetApplicationLoanRequests()[0]
	}

	// If user is having loan account active then return the status
	// Todo: Parallel Loans Support (Ticket - 97386)
	if landingRes.ActiveLoanAccount != nil {
		return &palPb.GetLoanUserStatusV2Response{
			Status:     rpc.StatusOk(),
			UserStatus: palPb.UserStatus_USER_STATUS_ACTIVE_LOAN,
			LoanEventTimestamps: &palPb.GetLoanUserStatusV2Response_LoanEventTimestamps{
				LatestEligibilityReqStartTimestamp: latestEligibilityLoanRequest.GetCreatedAt(),
				LatestApplicationReqStartTimestamp: latestApplicationLoanRequest.GetCreatedAt(),
				LatestLoanDisbursedTimestamp:       landingRes.ActiveLoanAccount.GetCreatedAt(),
			},
		}, nil
	}

	// If user is having active application loan request then return the status
	if loanRequests.GetActiveApplicationLoanRequest() != nil {
		return &palPb.GetLoanUserStatusV2Response{
			Status:     rpc.StatusOk(),
			UserStatus: palPb.UserStatus_USER_STATUS_ACTIVE_APPLICATION,
			LoanEventTimestamps: &palPb.GetLoanUserStatusV2Response_LoanEventTimestamps{
				LatestEligibilityReqStartTimestamp: latestEligibilityLoanRequest.GetCreatedAt(),
				LatestApplicationReqStartTimestamp: loanRequests.GetActiveApplicationLoanRequest().GetCreatedAt(),
			},
		}, nil
	}

	// If user is having active eligibility loan request then return the status
	if loanRequests.GetActiveEligibilityLoanRequest() != nil {
		return &palPb.GetLoanUserStatusV2Response{
			Status:     rpc.StatusOk(),
			UserStatus: palPb.UserStatus_USER_STATUS_ACTIVE_ELIGIBILITY,
			LoanEventTimestamps: &palPb.GetLoanUserStatusV2Response_LoanEventTimestamps{
				LatestEligibilityReqStartTimestamp: loanRequests.GetActiveEligibilityLoanRequest().GetCreatedAt(),
				LatestApplicationReqStartTimestamp: latestApplicationLoanRequest.GetCreatedAt(),
			},
		}, nil
	}

	// If user is having active loan offer then return the status
	if landingRes.ActiveLoanOffers != nil {
		return &palPb.GetLoanUserStatusV2Response{
			Status:     rpc.StatusOk(),
			UserStatus: palPb.UserStatus_USER_STATUS_OFFER_AVAILABLE,
			LoanEventTimestamps: &palPb.GetLoanUserStatusV2Response_LoanEventTimestamps{
				LatestEligibilityReqStartTimestamp: latestEligibilityLoanRequest.GetCreatedAt(),
				LatestApplicationReqStartTimestamp: latestApplicationLoanRequest.GetCreatedAt(),
			},
		}, nil
	}

	// If user is eligible to apply for loan offer then return the status
	if landingRes.Eligibility != nil && landingRes.Eligibility.CheckEligibility {
		return &palPb.GetLoanUserStatusV2Response{
			Status:     rpc.StatusOk(),
			UserStatus: palPb.UserStatus_USER_STATUS_ELIGIBLE_TO_APPLY,
			LoanEventTimestamps: &palPb.GetLoanUserStatusV2Response_LoanEventTimestamps{
				LatestEligibilityReqStartTimestamp: latestEligibilityLoanRequest.GetCreatedAt(),
				LatestApplicationReqStartTimestamp: latestApplicationLoanRequest.GetCreatedAt(),
			},
		}, nil
	}

	// If user is rejected then return the status
	return &palPb.GetLoanUserStatusV2Response{
		Status:     rpc.StatusOk(),
		UserStatus: palPb.UserStatus_USER_STATUS_REJECTED,
		LoanEventTimestamps: &palPb.GetLoanUserStatusV2Response_LoanEventTimestamps{
			LatestEligibilityReqStartTimestamp: latestEligibilityLoanRequest.GetCreatedAt(),
			LatestApplicationReqStartTimestamp: latestApplicationLoanRequest.GetCreatedAt(),
			LatestRejectionTimestamp:           loanRequests.GetLatestFailedLoanRequest().GetCompletedAt(),
		},
	}, nil
}
