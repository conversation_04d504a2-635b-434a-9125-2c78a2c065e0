package helper

import (
	"math"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/datetime"
	pkgMoney "github.com/epifi/be-common/pkg/money"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
)

const (
	minLoanTenureInMonths         = 12
	emiDeductionDateFederal       = 5
	minLoanAmountInPaisa          = 5000000
	defaultSliderAmountPercentage = 40
	defaultSliderTenurePercentage = 60
	minLoanAmountForSliderInPaisa = 5100000
	minLoanAmount                 = 1000
)

// TODO(Vikas): write uts and fix the calculations
type loanCalculator struct {
	*palPb.LoanOffer
}

func NewLoanCalculator(offer *palPb.LoanOffer) *loanCalculator {
	return &loanCalculator{offer}
}

func (o *loanCalculator) GetDisbursalAmount(loanAmount *money.Money) *money.Money {
	return pkgMoney.ParseFloat(o.getDisbursalAmountPaise(loanAmount)/100, "INR")
}

func (o *loanCalculator) getDisbursalAmountPaise(loanAmount *money.Money) float64 {
	loanAmountPaise, _ := pkgMoney.ToPaise(loanAmount)
	return float64(loanAmountPaise) - o.getTotalDeductionsPaise(loanAmount)
}

func (o *loanCalculator) GetProcessingFee(loanAmount *money.Money) *money.Money {
	return pkgMoney.ParseFloat(o.getProcessingFeePaise(loanAmount)/100, "INR")
}

func (o *loanCalculator) getProcessingFeePaise(loanAmount *money.Money) float64 {
	loanAmountPaise, _ := pkgMoney.ToPaise(loanAmount)
	return float64(loanAmountPaise) * o.GetProcessingFeeRate() / 100
}

func (o *loanCalculator) GetInterest(loanAmount *money.Money, tenureMonths int32) *money.Money {
	return pkgMoney.ParseFloat(o.getInterestPaise(loanAmount, tenureMonths)/100, "INR")
}

func (o *loanCalculator) getInterestPaise(loanAmount *money.Money, tenureMonths int32) float64 {
	loanAmountPaise, _ := pkgMoney.ToPaise(loanAmount)
	return o.getTotalPayablePaise(loanAmount, tenureMonths) - float64(loanAmountPaise)
}

func (o *loanCalculator) GetTotalPayable(loanAmount *money.Money, tenureMonths int32) *money.Money {
	return pkgMoney.ParseFloat(o.getTotalPayablePaise(loanAmount, tenureMonths)/100, "INR")
}

func (o *loanCalculator) getTotalPayablePaise(loanAmount *money.Money, tenureMonths int32) float64 {
	return o.getEmiAmountPaise(loanAmount, tenureMonths) * float64(tenureMonths)
}

func (o *loanCalculator) GetAdvanceInterest(loanAmount *money.Money, loanIssueDate *date.Date) *money.Money {
	return pkgMoney.ParseFloat(o.getAdvanceInterestPaise(loanAmount, loanIssueDate)/100, "INR")
}

func (o *loanCalculator) getAdvanceInterestPaise(loanAmount *money.Money, loanIssueDate *date.Date) float64 {
	if o.GetVendor() == palPb.Vendor_LIQUILOANS || o.GetVendor() == palPb.Vendor_IDFC {
		return 0
	}
	loanAmountPaise, _ := pkgMoney.ToPaise(loanAmount)
	aiDays := 0
	if loanIssueDate.GetDay() <= emiDeductionDateFederal {
		aiDays = emiDeductionDateFederal - int(loanIssueDate.GetDay())
	} else {
		loanIssueDateTime := datetime.DateToTime(loanIssueDate, datetime.IST)
		somTime := datetime.StartOfMonth(*loanIssueDateTime)
		eomTime := somTime.AddDate(0, 1, 4)
		aiDays = int(eomTime.Sub(*loanIssueDateTime).Hours() / 24)
	}
	ai := (float64(loanAmountPaise) * o.GetInterestRate() / 36500) * float64(aiDays)
	return ai
}

func (o *loanCalculator) GetTotalDeductions(loanAmount *money.Money) *money.Money {
	return pkgMoney.ParseFloat(o.getTotalDeductionsPaise(loanAmount)/100, "INR")
}

func (o *loanCalculator) getTotalDeductionsPaise(loanAmount *money.Money) float64 {
	return o.getProcessingFeePaise(loanAmount) + o.getGstPaise(loanAmount) + o.getAdvanceInterestPaise(loanAmount, datetime.TimeToDateInLoc(time.Now(), datetime.IST))
}

func (o *loanCalculator) GetGst(loanAmount *money.Money) *money.Money {
	return pkgMoney.ParseFloat(o.getGstPaise(loanAmount)/100, "INR")
}

func (o *loanCalculator) getGstPaise(loanAmount *money.Money) float64 {
	processingFeePaise := o.getProcessingFeePaise(loanAmount)
	return processingFeePaise * (o.GetProcessingInfo().GetGst()) / 100
}

func (o *loanCalculator) GetInterestRate() float64 {
	if o.GetProcessingInfo().GetInterestRate() == nil || len(o.GetProcessingInfo().GetInterestRate()) == 0 {
		return 0
	}
	return o.GetProcessingInfo().GetInterestRate()[0].GetPercentage()
}

func (o *loanCalculator) GetProcessingFeeRate() float64 {
	if o.GetProcessingInfo().GetProcessingFee() == nil || len(o.GetProcessingInfo().GetProcessingFee()) == 0 {
		return 0
	}
	return o.GetProcessingInfo().GetProcessingFee()[0].GetPercentage()
}

func (o *loanCalculator) GetEmiAmount(loanAmount *money.Money, tenureMonths int32) *money.Money {
	return pkgMoney.ParseFloat(o.getEmiAmountPaise(loanAmount, tenureMonths)/100, "INR")
}

func (o *loanCalculator) getEmiAmountPaise(loanAmount *money.Money, tenureMonths int32) float64 {
	loanAmountPaise, _ := pkgMoney.ToPaise(loanAmount)
	r := o.GetInterestRate() / (12 * 100)
	crp := math.Pow(1+r, float64(tenureMonths))
	if crp == 1 || r == 0 || crp == 0 {
		return float64(loanAmountPaise) / float64(tenureMonths)
	}
	emi := (float64(loanAmountPaise) * r * crp) / (crp - 1)
	return emi
}

func (o *loanCalculator) GetMinLoanAmount() *money.Money {
	return pkgMoney.ParseFloat(o.getMinLoanAmountPaise()/100, "INR")
}

func (o *loanCalculator) getMinLoanAmountPaise() float64 {
	if o.GetOfferConstraints().GetMinLoanAmount() != nil && o.GetOfferConstraints().GetMinLoanAmount().GetUnits() >= minLoanAmount {
		minAmount, _ := pkgMoney.ToDecimal(o.GetOfferConstraints().GetMinLoanAmount()).Float64()
		return minAmount * 100
	}
	if o.GetProcessingInfo().GetProcessingFee() == nil || len(o.GetProcessingInfo().GetProcessingFee()) == 0 {
		return 0
	}
	// setting the minimum loan amount to a maximum of 50k or processing fee starting range
	return math.Max(float64(o.GetProcessingInfo().GetProcessingFee()[0].GetStart()*100), float64(minLoanAmountInPaisa))
}

func (o *loanCalculator) GetMinOfferTenureInMonths() int32 {
	return minLoanTenureInMonths
}

func (o *loanCalculator) GetMinTenureInMonths() int32 {
	maxAllowedEmiPaise, _ := pkgMoney.ToPaise(o.GetOfferConstraints().GetMaxEmiAmount())
	minLoanAmountPaise := o.getMinLoanAmountPaise()
	return int32(math.Ceil(minLoanAmountPaise / float64(maxAllowedEmiPaise)))
}

func (o *loanCalculator) GetMaxLoanAmount(tenureMonths int32) *money.Money {
	return pkgMoney.ParseFloat(o.getMaxLoanAmountPaise(tenureMonths)/100, "INR")
}

func (o *loanCalculator) getMaxLoanAmountPaise(tenureMonths int32) float64 {
	return o.getLoanAmountPaise(o.GetOfferConstraints().GetMaxEmiAmount(), float64(tenureMonths))
}

func (o *loanCalculator) getLoanAmountPaise(emiAmount *money.Money, tenureMonths float64) float64 {
	emiAmountPaise, _ := pkgMoney.ToPaise(emiAmount)
	maxOfferLoanAmount, _ := pkgMoney.ToPaise(o.GetOfferConstraints().GetMaxLoanAmount())
	r := o.GetInterestRate() / (12 * 100)
	crp := math.Pow(1+r, tenureMonths)
	var loanAmountPaise float64
	if r == 0 || crp == 0 || crp == 1 {
		loanAmountPaise = float64(emiAmountPaise) * tenureMonths
	} else {
		loanAmountPaise = float64(emiAmountPaise) * (crp - 1) / (r * crp)
	}
	return math.Min(float64(maxOfferLoanAmount), math.Floor(loanAmountPaise))
}

func (o *loanCalculator) GetMinTenure(loanAmount *money.Money) int32 {
	return int32(math.Ceil(math.Min(float64(o.GetOfferConstraints().GetMaxTenureMonths()), o.getTenure(loanAmount, o.GetOfferConstraints().GetMaxEmiAmount()))))
}

func (o *loanCalculator) getTenure(loanAmount *money.Money, emiAmount *money.Money) float64 {
	loanAmountPaise, _ := pkgMoney.ToPaise(loanAmount)
	emiAmountPaise, _ := pkgMoney.ToPaise(emiAmount)
	r := o.GetInterestRate() / (12 * 100)
	// crp := math.Pow(1+r, float64(tenureMonths))
	// loanAmountPaise := float64(emiAmountPaise) * (crp - 1) / (r * crp)
	// 1-1/crp = r*la/ea
	// 1-r*la/ea = 1/crp
	// crp = 1/(1-r*la/ea)
	crp := 1 / (1 - r*float64(loanAmountPaise)/float64(emiAmountPaise))
	tenure := math.Log(crp) / math.Log(1+r)
	return tenure
}

func (o *loanCalculator) GetDefaultOfferTenure(maxTenure int32) int32 {
	if o.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY {
		return 1
	}
	return o.GetMinOfferTenureInMonths() + int32((float64(maxTenure-o.GetMinOfferTenureInMonths()))*(float64(defaultSliderTenurePercentage)/float64(100)))
}

// GetNextEmiDate takes time as parameter and returns the next emi date string and date, accordingly
func GetNextEmiDate(time2 time.Time) (string, *date.Date) {
	delayByMonth := 0
	if time2.Day() > emiDeductionDateFederal {
		delayByMonth += 1
	}
	year, month, _ := time2.In(datetime.IST).Date()
	fifthOfCurrMonth := time.Date(year, month, 5, 23, 59, 59, 0, datetime.IST)

	nextEmiDate := datetime.TimeToDateInLoc(fifthOfCurrMonth.AddDate(0, 1+delayByMonth, 0), datetime.IST)
	return datetime.DateToString(nextEmiDate, "02-01-2006", datetime.IST), nextEmiDate
}

func GetApr(loanDisbursedAmount *money.Money, emi *money.Money, tenure int32) float64 {
	loanDisbursedAmountFloat, _ := pkgMoney.ToDecimal(loanDisbursedAmount).Float64()
	emiFloat, _ := pkgMoney.ToDecimal(emi).Float64()

	return (GetIrr(loanDisbursedAmountFloat, emiFloat, tenure) * 12) * 100
}

// GetAprRate is used to get the apr rate
// Getting used in GetOfferDetails./**
func (o *loanCalculator) GetAprRate(loanAmount *money.Money, tenure int32) float64 {
	return GetApr(o.GetDisbursalAmount(loanAmount), o.GetEmiAmount(loanAmount, tenure), tenure)
}

func GetMinLoanAmountInPaisa(loanOffer *palPb.LoanOffer) int64 {
	if loanOffer != nil && loanOffer.GetOfferConstraints() != nil && loanOffer.GetOfferConstraints().GetMinLoanAmount().GetUnits() >= minLoanAmount {
		minAmountPaise, _ := pkgMoney.ToPaise(loanOffer.GetOfferConstraints().GetMinLoanAmount())
		return minAmountPaise
	}
	return minLoanAmountInPaisa
}

func GetMinLoanAmountForSliderInPaisa(loanOffer *palPb.LoanOffer) (int64, error) {
	minAmountPaise, err := pkgMoney.ToPaise(loanOffer.GetOfferConstraints().GetMinLoanAmount())
	if err != nil {
		return 0, errors.Wrapf(err, "error getting min loan amount in paise for lenden: %v", loanOffer.GetOfferConstraints().GetMinLoanAmount())
	}
	return minAmountPaise, nil
}
