// nolint: funlen, dupl
//
//go:generate mockgen -source=landing.go -destination=mocks/mock_landing.go
//go:generate dao_metrics_gen .
package landing_provider

import (
	"context"
	"fmt"
	"time"

	"github.com/google/wire"
	cmap "github.com/orcaman/concurrent-map"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/savings"
	typesPb "github.com/epifi/gamma/api/typesv2"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/feature/release"
	calculatorTypes "github.com/epifi/gamma/preapprovedloan/calculator/types"
	"github.com/epifi/gamma/preapprovedloan/config/genconf"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/helper"
	multiDbProvider "github.com/epifi/gamma/preapprovedloan/multidb_provider"
	de "github.com/epifi/gamma/preapprovedloan/multidb_provider/decision_engine"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers"
	priorityProviderFactory "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/provider"
)

type ILandingProvider interface {
	GetLandingPageDataForActor(ctx context.Context, req *GetLandingPageDataForActorRequest) (*GetLandingPageDataForActorResponse, error)
}

type LandingProvider struct {
	rpcHelper                   *helper.RpcHelper
	offersDecisionEngine        de.IOffersDecisionEngine
	loanOffersDao               dao.LoanOffersDao
	onbClient                   onbPb.OnboardingClient
	limitEstimatorClient        limitEstimatorPb.CreditLimitEstimatorClient
	calculatorFactory           calculatorTypes.FactoryProvider
	priorityProviderFactory     priorityProviderFactory.IPriorityProviderFactory
	eligibilityEvaluatorFactory eligibility_evaluator.EligibilityEvaluatorFactory
	DynConf                     *genconf.Config
	multiDBDataProvider         multiDbProvider.IMultiDbProvider
	releaseEvaluator            release.IEvaluator
	recommendationEngine        provider.ILoanRecommendationEngine
	lseDao                      dao.LoanStepExecutionsDao
	loecDao                     dao.LoanOfferEligibilityCriteriaDao
	savingsClient               savings.SavingsClient
}

func NewLandingProvider(
	rpcHelper *helper.RpcHelper,
	offersDecisionEngine de.IOffersDecisionEngine,
	loanOffersDao dao.LoanOffersDao,
	onbClient onbPb.OnboardingClient,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	calculatorFactory calculatorTypes.FactoryProvider,
	priorityProviderFactory priorityProviderFactory.IPriorityProviderFactory,
	eligibilityEvaluatorFactory eligibility_evaluator.EligibilityEvaluatorFactory,
	dynConf *genconf.Config,
	multiDBDataProvider multiDbProvider.IMultiDbProvider,
	releaseEvaluator release.IEvaluator,
	recommendationEngine provider.ILoanRecommendationEngine,
	lseDao dao.LoanStepExecutionsDao,
	loecDao dao.LoanOfferEligibilityCriteriaDao,
	savingsClient savings.SavingsClient,
) *LandingProvider {
	return &LandingProvider{
		rpcHelper:                   rpcHelper,
		offersDecisionEngine:        offersDecisionEngine,
		loanOffersDao:               loanOffersDao,
		onbClient:                   onbClient,
		limitEstimatorClient:        limitEstimatorClient,
		calculatorFactory:           calculatorFactory,
		priorityProviderFactory:     priorityProviderFactory,
		eligibilityEvaluatorFactory: eligibilityEvaluatorFactory,
		DynConf:                     dynConf,
		multiDBDataProvider:         multiDBDataProvider,
		releaseEvaluator:            releaseEvaluator,
		recommendationEngine:        recommendationEngine,
		lseDao:                      lseDao,
		loecDao:                     loecDao,
		savingsClient:               savingsClient,
	}
}

var _ ILandingProvider = &LandingProvider{}

var LandingProviderWireSet = wire.NewSet(NewLandingProvider, wire.Bind(new(ILandingProvider), new(*LandingProvider)))

type GetLandingPageDataForActorRequest struct {
	ActorId             string
	LoanRequestStatuses []palPb.LoanRequestStatus
	LoanProgram         palPb.LoanProgram
	LoanType            palPb.LoanType
	// map of ownership to bool,
	//     if true for an ownership, data will be fetched from that ownership's DB for loan accounts (or) loan requests (or) loan offers
	//     if false for an ownership, data will not be fetched from that ownership's DB for loan accounts (or) loan requests (or) loan offers
	// 	   if an ownership is not present in the map, it'll be considered as true and data will be fetched from that ownership's DB
	// e.g.: if we have 3 total ownerships {LIQUILOANS, IDFC, FIFTYFIN} and the map is
	//			{commontypes.Ownership_LIQUILOANS: true, commontypes.Ownership_IDFC: false}
	// then data will be fetched from LIQUILOANS and from FIFTYFIN but not from IDFC
	// (optional) if map is empty, data will be fetched from all ownerships
	OwnershipFilterMap map[commontypes.Ownership]bool
	// map of ownership to bool,
	//     if true for an ownership, data will be fetched from that ownership's DB for loan offer eligibility criteria
	//     if false for an ownership, data will not be fetched from that ownership's DB for loan offer eligibility criteria
	// 	   if an ownership is not present in the map, it'll be considered as true and data will be fetched from that ownership's DB
	// e.g.: if we have 3 total ownerships {LIQUILOANS, IDFC, FIFTYFIN} and the map is
	//			{commontypes.Ownership_LIQUILOANS: true, commontypes.Ownership_IDFC: false}
	// then data will be fetched from LIQUILOANS and from FIFTYFIN but not from IDFC
	// (optional) if map is empty, data will be fetched from all ownerships
	OwnershipFilterMapForLoecs map[commontypes.Ownership]bool
	FetchEligibilityRequest    bool
	SkipFailedLR               bool
}
type GetLandingPageDataForActorResponse struct {
	LoanHeader        *palPb.LoanHeader
	ActiveLoanAccount *palPb.LoanAccount
	// LoanRequests are in descending order of created_at timestamp
	LoanRequests []*palPb.LoanRequest
	// ActiveLoanOffers are in descending order of offer priority
	ActiveLoanOffers []*palPb.LoanOffer
	Eligibility      *LoanEligibility
}

type LoanEligibility struct {
	// used by BE service to set status for check eligibility
	// used to show if a user can check their real time loan eligibility
	CheckEligibility bool
}

func (m *LandingProvider) GetLandingPageDataForActor(ctx context.Context, req *GetLandingPageDataForActorRequest) (*GetLandingPageDataForActorResponse, error) {
	if req == nil || req.ActorId == "" {
		return nil, fmt.Errorf("ActorID cannot be empty")
	}

	res := &GetLandingPageDataForActorResponse{
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: req.LoanProgram,
			Vendor:      palPb.Vendor_VENDOR_UNSPECIFIED,
		},
	}

	loanProgramsToFetch := getLoanProgramsFromGetLandingPageDataForActorRequest(req)

	closedAccountMap := map[string]bool{}
	loanAccountRes, laErr := m.multiDBDataProvider.CheckAndGetLoanAccountsForActor(ctx, &multiDbProvider.CheckAndGetLoanAccountsForActorRequest{
		ActorId:            req.ActorId,
		AccountStatuses:    []palPb.LoanAccountStatus{palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE, palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED},
		LoanPrograms:       loanProgramsToFetch,
		OwnershipFilterMap: req.OwnershipFilterMap,
	})
	if laErr != nil && !errors.Is(laErr, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("%w. could not fetch loan accounts from multiDB provider", laErr)
	}
	// If loan accounts exist
	if laErr == nil {
		var activeLoanAccount *palPb.LoanAccount
		for _, la := range loanAccountRes.Accounts {
			if la.GetStatus() == palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
				activeLoanAccount = la
				break
			}
			if la.GetStatus() == palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED {
				closedAccountMap[la.GetVendor().String()+"_"+la.GetLoanProgram().String()] = true
			}
		}
		// no need to pass closed accounts here
		if activeLoanAccount != nil {
			res.LoanHeader = &palPb.LoanHeader{
				LoanProgram: activeLoanAccount.GetLoanProgram(),
				Vendor:      activeLoanAccount.GetVendor(),
			}
			res.ActiveLoanAccount = activeLoanAccount
			return res, nil
		}
	}

	var nonSuccessLoanRequest *palPb.LoanRequest
	lrFetchRequest := &multiDbProvider.CheckAndGetLoanRequestsForActorRequest{
		ActorId:           req.ActorId,
		Statuses:          req.LoanRequestStatuses,
		LoanPrograms:      loanProgramsToFetch,
		CompletedAtWithin: &helper.ActiveLoanRequestDuration,
		Types: []palPb.LoanRequestType{
			palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
		},
		OwnershipFilterMap: req.OwnershipFilterMap,
	}
	if req.FetchEligibilityRequest {
		lrFetchRequest.Types = append(lrFetchRequest.Types, palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY)
	}

	// Fetch loan requests that have completed at as null or have completed_at set within the last 24 hours
	loanRequestsRes, lrErr := m.multiDBDataProvider.CheckAndGetLoanRequestsForActor(ctx, lrFetchRequest)
	if lrErr != nil && !errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("%w. could not fetch loan requests from multiDB provider", lrErr)
	}
	// in cases of repeat loans, do not want last loan request which is in success state. To avoid that, only passing non success loan request
	if loanRequestsRes != nil && len(loanRequestsRes.Requests) > 0 {
		if !loanRequestsRes.Requests[0].IsSuccess() {
			if !req.SkipFailedLR || (req.SkipFailedLR && !loanRequestsRes.Requests[0].IsFailedTerminal()) {
				nonSuccessLoanRequest = loanRequestsRes.Requests[0]
				res.LoanRequests = []*palPb.LoanRequest{nonSuccessLoanRequest}

				lse, lseErr := m.lseDao.GetLatestByRefIdAndFlow(ctx, nonSuccessLoanRequest.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION)
				if lseErr != nil && !errors.Is(lseErr, epifierrors.ErrRecordNotFound) {
					return nil, fmt.Errorf("could not fetch loan step execution by non success lr id: %s, err: %w", nonSuccessLoanRequest.GetId(), lseErr)
				}
				if lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_OFFER_SELECTION {
					nonSuccessLoanRequest = nil
					res.LoanRequests = nil
				}
			}
		}
	}

	var exploreOtherLoanOffers bool
	var exploreOffersWithActiveApplication bool
	if epificontext.AppPlatformFromContext(ctx) != commontypes.Platform_PLATFORM_UNSPECIFIED {
		appVersionConstraintData := release.NewAppVersionConstraintData(m.DynConf.AutoCancelCurrentLrConfig())
		isAppVersionCompatible, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
		if appVerErr != nil {
			return nil, errors.Wrap(appVerErr, "unable to evaluate app version constraint")
		}
		exploreOffersWithActiveApplication = isAppVersionCompatible
	}

	if nonSuccessLoanRequest != nil {
		res.LoanHeader = &palPb.LoanHeader{
			LoanProgram: nonSuccessLoanRequest.GetLoanProgram(),
			Vendor:      nonSuccessLoanRequest.GetVendor(),
		}
		if nonSuccessLoanRequest.IsFailedTerminal() || exploreOffersWithActiveApplication {
			// loan request was in terminal state
			// set the flag to explore loan offer from other vendors
			exploreOtherLoanOffers = true
			req.LoanType = helper.GetLoanTypeFromLoanProgram(req.LoanProgram)
			req.LoanProgram = palPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED
			loanProgramsToFetch = getLoanProgramsFromGetLandingPageDataForActorRequest(req)
		}
		// pending LRs / failed PR where we do not show other offer
		if !exploreOtherLoanOffers {
			return res, nil
		}
	}

	if len(loanProgramsToFetch) > 1 {
		rankedLoanOptions, err := m.recommendationEngine.GetRankedLoanOptions(ctx, &provider.GetRankedLoanOptionsRequest{
			ActorId: req.ActorId,
		})
		if err != nil {
			return nil, fmt.Errorf("error while fetching ranked loan options: %w", err)
		}

		if len(rankedLoanOptions.ActiveOffers) > 0 {
			logger.Info(ctx, fmt.Sprintf("Reacommendation engine gave offer for vendor %v and loan program %v in landing.go and actor %v", rankedLoanOptions.ActiveOffers[0].GetVendor(), rankedLoanOptions.ActiveOffers[0].GetLoanProgram(), req.ActorId))
		}
		if rankedLoanOptions.Eligibility != nil {
			logger.Info(ctx, "Reacommendation engine gave eligibility in landing.go ", zap.String(logger.ACTOR_ID_V2, req.ActorId))
		}

		landingPageDataRes := &GetLandingPageDataForActorResponse{}
		allowedRepeatLoanOffers := make([]*palPb.LoanOffer, 0)
		for _, loanOffer := range rankedLoanOptions.ActiveOffers {
			ignoreOffer, ignoreOfferErr := m.ignoreOfferForRepeatLoan(ctx, loanOffer, closedAccountMap)
			if ignoreOfferErr != nil {
				logger.Error(ctx, fmt.Sprintf("failed to check ignore offer: %v", ignoreOfferErr))
				return nil, fmt.Errorf("failed to evaluate offer ignore or not for repeat loan, err: %v", ignoreOfferErr)
			}
			if !ignoreOffer {
				allowedRepeatLoanOffers = append(allowedRepeatLoanOffers, loanOffer)
			}
		}
		// temp fix to move sg eligibility above abfl offer
		if len(allowedRepeatLoanOffers) > 0 {
			allowedOffer := allowedRepeatLoanOffers[0]

			isSgEligPrio, err := CheckIfPrioritizeSgEligOverLoanOffer(ctx, m.DynConf, allowedOffer, m.eligibilityEvaluatorFactory, m.onbClient, m.savingsClient)
			if err != nil {
				return nil, errors.Wrap(err, fmt.Sprintf("failed to check if sg elig is prioritised over loan offer or not, err: %v", err))
			}

			if isSgEligPrio {
				landingPageDataRes.Eligibility = &LoanEligibility{
					CheckEligibility: true,
				}
				landingPageDataRes.LoanHeader = &palPb.LoanHeader{
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
					Vendor:      palPb.Vendor_EPIFI_TECH,
				}

				if res.LoanRequests != nil {
					landingPageDataRes.LoanRequests = res.LoanRequests
					landingPageDataRes.LoanHeader = res.LoanHeader
				}
				return landingPageDataRes, nil
			}
		}

		if len(allowedRepeatLoanOffers) != 0 {
			landingPageDataRes.ActiveLoanOffers = allowedRepeatLoanOffers
			landingPageDataRes.LoanHeader = &palPb.LoanHeader{
				LoanProgram: allowedRepeatLoanOffers[0].GetLoanProgram(),
				Vendor:      allowedRepeatLoanOffers[0].GetVendor(),
			}
		}
		if rankedLoanOptions.Eligibility != nil {
			landingPageDataRes.Eligibility = &LoanEligibility{
				CheckEligibility: rankedLoanOptions.Eligibility.CheckEligibility,
			}
			landingPageDataRes.LoanHeader = rankedLoanOptions.Eligibility.LoanHeader
		}
		if res.LoanRequests != nil {
			landingPageDataRes.LoanRequests = res.LoanRequests
			landingPageDataRes.LoanHeader = res.LoanHeader
		}
		return landingPageDataRes, nil
	}

	// if we already have loan program then, no need to call recommendation engine, fetch lo or loec according to old landing info logic
	if len(loanProgramsToFetch) == 1 {
		loanOffersRes, loErr := m.multiDBDataProvider.CheckAndGetLoanOfferForActor(ctx, &multiDbProvider.CheckAndGetLoanOfferForActorRequest{
			ActorId:            req.ActorId,
			LoanPrograms:       getLoanProgramsToExploreAllOffers(req),
			OwnershipFilterMap: req.OwnershipFilterMap,
		})
		if loErr != nil && !errors.Is(loErr, epifierrors.ErrRecordNotFound) {
			return nil, fmt.Errorf("%w. could not fetch loan offer from multiDB provider", loErr)
		}
		isFiLiteUser, isRiskyUser, userErr := m.checkUserProperty(ctx, req.ActorId)
		if userErr != nil {
			return nil, fmt.Errorf("%w. failed to check filite and riskiness for user", userErr)
		}

		var isValidOffer, ignoreOffer bool
		var offerErr, ignoreOfferErr error
		if loanOffersRes != nil && loanOffersRes.Offer != nil {
			isValidOffer, offerErr = m.isValidLoanOffer(ctx, loanOffersRes.Offer)
			if offerErr != nil {
				logger.Error(ctx, fmt.Sprintf("failed to check valid offer: %v", offerErr))
			}
			ignoreOffer, ignoreOfferErr = m.ignoreOfferForRepeatLoan(ctx, loanOffersRes.Offer, closedAccountMap)
			if ignoreOfferErr != nil {
				logger.Error(ctx, fmt.Sprintf("failed to check ignore offer: %v", ignoreOfferErr))
				return nil, fmt.Errorf("failed to evaluate offer ignore or not for repeat loan, err: %v", ignoreOfferErr)
			}
		}

		if loErr == nil && !isRiskyUser && isValidOffer && !ignoreOffer {
			res.ActiveLoanOffers = []*palPb.LoanOffer{loanOffersRes.Offer}
			// if loan request was not found, only then set the loan header from loan offer
			// if loan request was present, loan header from loan request will be returned
			if res.LoanRequests == nil {
				res.LoanHeader = &palPb.LoanHeader{
					LoanProgram: loanOffersRes.Offer.GetLoanProgram(),
					Vendor:      loanOffersRes.Offer.GetVendor(),
				}
			}
			return res, nil
		}

		// this is for real time eligibility flow
		var realtimeEligibilityErr error
		res, realtimeEligibilityErr = m.checkRealtimeEligibility(ctx, req, res, isFiLiteUser)
		if realtimeEligibilityErr != nil {
			return nil, fmt.Errorf("failed to handle for check eligibility real time, err: %w", realtimeEligibilityErr)
		}
	}

	return res, nil
}

func getLoanProgramsFromGetLandingPageDataForActorRequest(req *GetLandingPageDataForActorRequest) []palPb.LoanProgram {
	if req.LoanProgram != palPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		return []palPb.LoanProgram{req.LoanProgram}
	}
	if req.LoanType != palPb.LoanType_LOAN_TYPE_UNSPECIFIED {
		return helper.GetLoanProgramsForLoanType(req.LoanType)
	}
	return helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL)
}

func (m *LandingProvider) fetchLoanOfferForGivenVendor(ctx context.Context, req *GetLandingPageDataForActorRequest, vendor palPb.Vendor, lp palPb.LoanProgram) (*palPb.LoanOffer, error) {
	// If the recent loan request is in failed terminal state,
	// then check for loan offer for that particular vendor.
	ownership := helper.GetPalOwnership(vendor)
	loanOffers, loErr := m.loanOffersDao.GetActiveOffersByActorIdAndLoanPrograms(
		epificontext.WithOwnership(ctx, ownership),
		req.ActorId,
		[]palPb.LoanProgram{lp})
	if loErr != nil && !errors.Is(loErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to fetch loan offers from multiDB", zap.Error(loErr))
		return nil, fmt.Errorf("%w. could not fetch loan offer from multiDB provider", loErr)
	}

	// Select one offer from the multiple offers from the same vendor.
	selectedLoanOffer, deErr := m.selectSingleOfferFromSliceOfOffers(ctx, ownership, loanOffers)
	if deErr == nil && selectedLoanOffer.IsActiveNow() {
		return selectedLoanOffer, nil
	}
	return nil, epifierrors.ErrRecordNotFound
}

func (m *LandingProvider) selectSingleOfferFromSliceOfOffers(ctx context.Context, ownership commontypes.Ownership, loanOffers []*palPb.LoanOffer) (*palPb.LoanOffer, error) {
	dataMap := cmap.New()
	dataMap.Set(ownership.String(), loanOffers)
	deRes, deErr := m.offersDecisionEngine.FilterLoanOffers(
		ctx,
		&de.OffersDecisionEngineRequest{
			DataMap: &dataMap,
		})
	if deErr != nil && !errors.Is(deErr, epifierrors.ErrRecordNotFound) {
		logger.Error(epificontext.WithOwnership(ctx, ownership), "could not find a single offer from decision engine", zap.Error(deErr))
	}
	if deErr != nil {
		return nil, deErr
	}
	return deRes.LoanOffer, nil
}

// getLoanProgramsToExploreAllOffers will return all the loan programs that needs to be looked for to get the loan offer
// if loan type is present in the request, fetch the lona programs from it.
// if not, and specific loan program is present, fetch the loan programs using that.
func getLoanProgramsToExploreAllOffers(req *GetLandingPageDataForActorRequest) []palPb.LoanProgram {
	loanType := req.LoanType
	if req.LoanProgram != palPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		loanType = helper.GetLoanTypeFromLoanProgram(req.LoanProgram)
	}
	if loanType != palPb.LoanType_LOAN_TYPE_UNSPECIFIED {
		return helper.GetLoanProgramsForLoanType(loanType)
	}
	return helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL)
}

func (m *LandingProvider) isFiLiteUser(ctx context.Context, actorId string) (bool, error) {
	getFeatResp, errGetFeat := m.onbClient.GetFeatureDetails(ctx, &onbPb.GetFeatureDetailsRequest{
		ActorId: actorId,
		Feature: onbPb.Feature_FEATURE_SA,
	})
	if grpcErr := epifigrpc.RPCError(getFeatResp, errGetFeat); grpcErr != nil {
		logger.Error(ctx, "failed to get feature details from onboarding", zap.Error(grpcErr))
		return false, grpcErr
	}
	return getFeatResp.GetIsFiLiteUser(), nil
}

func (m *LandingProvider) isValidLoanOffer(ctx context.Context, loanOffer *palPb.LoanOffer) (bool, error) {
	limitExhausted, limitExhaustedErr := m.isPlCCLimitAlreadyExhausted(ctx, loanOffer, false)
	if limitExhaustedErr != nil {
		return false, limitExhaustedErr
	}
	if limitExhausted {
		err := m.loanOffersDao.DeactivateLoanOffer(ctx, loanOffer.GetId())
		if err != nil {
			return false, err
		}
		return false, fmt.Errorf("offer limit already exhausted, %w", epifierrors.ErrResourceExhausted)
	}

	// getting defaultValueCalculator
	defaultValueCalculator := m.calculatorFactory.GetDefaultValueCalculator(ctx, loanOffer)

	// if the max loan amount constraint not able to honour the max emi constraint`then correct the max amount
	maxLoanAmount := defaultValueCalculator.GetMaxAmountBasisOnMaxTenureAndMaxEmi()
	moneyComp, moneyCompErr := moneyPkg.CompareV2(maxLoanAmount, loanOffer.GetOfferConstraints().GetMaxLoanAmount())
	if moneyCompErr != nil {
		logger.Error(ctx, "could not compare actual max loan amount and offers max loan amount: ", zap.Error(moneyCompErr))
		return false, moneyCompErr
	}
	if moneyComp == -1 {
		// soft updating the max loan amount here, as we are updating it in offer in GetOfferDetails rpc
		loanOffer.GetOfferConstraints().MaxLoanAmount = maxLoanAmount
	}

	maxAmount, err := moneyPkg.ToPaise(loanOffer.GetOfferConstraints().GetMaxLoanAmount())
	if err != nil {
		return false, errors.Wrap(err, "error getting max loan amount")
	}
	minAmount, err := helper.GetMinLoanAmountForSliderInPaisa(loanOffer)
	if err != nil {
		return false, errors.Wrap(err, "error getting min loan amount for slider")
	}
	if loanOffer.IsActiveNow() && maxAmount >= minAmount {
		return true, nil
	}
	return false, nil
}

func (m *LandingProvider) isPlCCLimitAlreadyExhausted(ctx context.Context, lo *palPb.LoanOffer, callVendor bool) (bool, error) {
	// in case of realtime distribution, setu will be doing limit check, so we need not to do this check here.
	if lo.GetVendor() == palPb.Vendor_FEDERAL && lo.GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION &&
		lo.GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB {
		limitResp, rpcErr := m.limitEstimatorClient.GetLoanConservativeLimit(ctx, &limitEstimatorPb.GetLoanConservativeLimitRequest{
			ActorId:          lo.GetActorId(),
			Vendor:           lo.GetVendor(),
			OfferId:          lo.GetId(),
			ShouldCallVendor: callVendor,
		})
		if err := epifigrpc.RPCError(limitResp, rpcErr); err != nil {
			if limitResp.GetStatus().IsResourceExhausted() {
				return true, nil
			}
			return false, err
		}
	}
	return false, nil
}

func (m *LandingProvider) checkRealtimeEligibility(ctx context.Context, req *GetLandingPageDataForActorRequest,
	res *GetLandingPageDataForActorResponse, isFiLiteUser bool) (*GetLandingPageDataForActorResponse, error) {
	var loanPrograms []palPb.LoanProgram
	var loanHeader *palPb.LoanHeader
	if res.LoanHeader.GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		loanPrograms = []palPb.LoanProgram{res.LoanHeader.GetLoanProgram()}
		loanHeader = res.LoanHeader
	}
	loecResp, checkLoanErr := m.multiDBDataProvider.CheckAndGetLoecsForActor(ctx, &multiDbProvider.CheckAndGetLoecsForActorRequest{
		ActorId:            req.ActorId,
		Statuses:           nil,
		LoanPrograms:       loanPrograms,
		CompletedAtWithin:  time.Hour * 24 * 90, // 90 days
		SortByDesc:         true,
		OwnershipFilterMap: req.OwnershipFilterMapForLoecs,
	})
	if checkLoanErr != nil && !errors.Is(checkLoanErr, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("%w. could not fetch loan offer eligibility criteria from multiDB provider", checkLoanErr)
	}
	if errors.Is(checkLoanErr, epifierrors.ErrRecordNotFound) {
		loecResp = &multiDbProvider.CheckAndGetLoecsForActorResponse{}
	}

	eligibilityResp, eligibilityErr := m.offersDecisionEngine.GetLoanEligibilityDecision(ctx, &de.GetLoanEligibilityDecisionRequest{
		Loecs:        loecResp.Loecs,
		IsFiLiteUser: isFiLiteUser,
		LoanHeader:   loanHeader,
	})
	if eligibilityErr != nil {
		return nil, fmt.Errorf("failed to get loan eligbility from decision engine, err: %w", eligibilityErr)
	}

	res.Eligibility = &LoanEligibility{
		CheckEligibility: eligibilityResp.CheckEligibility,
	}
	res.LoanHeader = eligibilityResp.LoanHeader
	return res, nil
}

func (m *LandingProvider) checkUserProperty(ctx context.Context, actorId string) (bool, bool, error) {
	isLiteUser, err := m.isFiLiteUser(ctx, actorId)
	if err != nil {
		return false, false, fmt.Errorf("failed to fetch fi lite user status, err: %w", err)
	}
	var isRiskyUser bool
	var riskErr error

	if !isLiteUser {
		isRiskyUser, riskErr = m.rpcHelper.IsRiskyUser(ctx, actorId)
		if riskErr != nil {
			return false, false, fmt.Errorf("%w. failed to check risk profile for user", riskErr)
		}
	}
	return isLiteUser, isRiskyUser, nil
}

func (m *LandingProvider) ignoreOfferForRepeatLoan(ctx context.Context, lo *palPb.LoanOffer, closedAccountMap map[string]bool) (bool, error) {
	// adding log to monitor repeat loans flow after release, can remove after some time
	if len(closedAccountMap) > 0 {
		closedAccountMapStrings := ""
		for key := range closedAccountMap {
			closedAccountMapStrings += key + "|"
		}
		logger.Info(ctx, fmt.Sprintf("entered repeat loan offer flow"), zap.String(logger.ACTOR_ID_V2, lo.GetActorId()),
			zap.String(logger.LOAN_OFFER_ID, lo.GetId()), zap.String("closed_loan_header_string", closedAccountMapStrings))
	}

	vendorProgramString := lo.GetVendor().String() + "_" + lo.GetLoanProgram().String()
	if closedAccountMap[vendorProgramString] {
		if ok := m.DynConf.RepeatLoans().AllowNewLoanOfferAfterSameOldAccountClosure().Get("REPEAT_LOANS_" + vendorProgramString); ok {
			releaseConstraint := release.NewCommonConstraintData(typesPb.Feature(typesPb.Feature_value["REPEAT_LOANS_"+vendorProgramString])).WithActorId(lo.GetActorId())
			isAllowed, err := m.releaseEvaluator.Evaluate(ctx, releaseConstraint)
			if err != nil {
				return true, fmt.Errorf("failed to evaluate for REPEAT_LOANS_%s: %w", vendorProgramString, err)
			}
			return !isAllowed, nil
		} else {
			return true, nil
		}
	}
	return false, nil
}

func CheckSGEligibility(ctx context.Context, actorId string, eligEval eligibility_evaluator.EligibilityEvaluatorFactory,
	onbClient onbPb.OnboardingClient, savClient savings.SavingsClient) (bool, error) {
	lh := &palPb.LoanHeader{
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Vendor:      palPb.Vendor_STOCK_GUARDIAN_LSP,
	}
	userFeatRes, err := helper.GetUserFeatureProperty(ctx, actorId, onbClient, savClient)
	if err != nil {
		return false, fmt.Errorf("failed to get user feature property, err: %w", err)
	}
	isNonFiCoreUser := !userFeatRes.IsFiSAHolder
	evalProvider, err := eligEval.GetEligibilityEvaluatorProvider(lh, isNonFiCoreUser)
	if err != nil {
		return false, fmt.Errorf("failed to get eligibilityEvaluator, err: %w", err)
	}

	res, err := evalProvider.EvaluateLoanEligibility(ctx, &providers.EvaluateLoanEligibilityRequest{
		ActorId:         actorId,
		IsNonFiCoreUser: isNonFiCoreUser,
	}, lh)
	if err != nil {
		return false, err
	}

	if res.ShouldCheckLoanEligibility() {
		return true, nil
	}
	return false, nil
}

// CheckIfPrioritizeSgEligOverLoanOffer temporary function to check if SG priority to move above any specific offer
func CheckIfPrioritizeSgEligOverLoanOffer(ctx context.Context, genConf *genconf.Config, prioritizedLO *palPb.LoanOffer,
	eligEval eligibility_evaluator.EligibilityEvaluatorFactory, onbClient onbPb.OnboardingClient, savClient savings.SavingsClient) (bool, error) {
	if genConf.LopeOverrideConfig().ShowSgEligibilityOverAbflSoftOffer(ctx) &&
		prioritizedLO.GetLoanOfferType() == palPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT &&
		prioritizedLO.GetVendor() == palPb.Vendor_ABFL &&
		prioritizedLO.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION {
		logger.Info(ctx, fmt.Sprintf("got %s soft offer and experiment was true now checking eligibility in landing.go", prioritizedLO.GetVendor().String()), zap.String(logger.ACTOR_ID_V2, prioritizedLO.GetActorId()))

		checkEligibility, checkEligibilityErr := CheckSGEligibility(ctx, prioritizedLO.GetActorId(), eligEval, onbClient, savClient)
		if checkEligibilityErr != nil {
			return false, errors.Wrap(checkEligibilityErr, "error in checking loec for SG")
		}

		if checkEligibility {
			logger.Info(ctx, fmt.Sprintf("giving SG eligibility priority over %s offer, value for in landing.go", prioritizedLO.GetVendor().String()), zap.String(logger.ACTOR_ID_V2, prioritizedLO.GetActorId()))
			return true, nil
		}
		logger.Info(ctx, fmt.Sprintf("got %s soft offer but sg eligibility was false in landing.go", prioritizedLO.GetVendor().String()), zap.String(logger.ACTOR_ID_V2, prioritizedLO.GetActorId()))
	}

	if genConf.LopeOverrideConfig().ShowSgEligibilityOverMvSoftOffer(ctx) &&
		prioritizedLO.GetLoanOfferType() == palPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT &&
		prioritizedLO.GetVendor() == palPb.Vendor_MONEYVIEW &&
		prioritizedLO.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION {
		logger.Info(ctx, fmt.Sprintf("got %s soft offer and experiment was true now checking eligibility in landing.go", prioritizedLO.GetVendor().String()), zap.String(logger.ACTOR_ID_V2, prioritizedLO.GetActorId()))

		checkEligibility, checkEligibilityErr := CheckSGEligibility(ctx, prioritizedLO.GetActorId(), eligEval, onbClient, savClient)
		if checkEligibilityErr != nil {
			return false, errors.Wrap(checkEligibilityErr, "error in checking loec for SG")
		}

		if checkEligibility {
			logger.Info(ctx, fmt.Sprintf("giving SG eligibility priority over %s offer, value for in landing.go", prioritizedLO.GetVendor().String()), zap.String(logger.ACTOR_ID_V2, prioritizedLO.GetActorId()))
			return true, nil
		}
		logger.Info(ctx, fmt.Sprintf("got %s soft offer but sg eligibility was false in landing.go", prioritizedLO.GetVendor().String()), zap.String(logger.ACTOR_ID_V2, prioritizedLO.GetActorId()))
	}
	return false, nil
}
