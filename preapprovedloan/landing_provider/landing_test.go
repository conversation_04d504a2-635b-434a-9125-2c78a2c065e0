// nolint:goimports
package landing_provider_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	datePb "google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	mocks3 "github.com/epifi/gamma/api/savings/mocks"
	"github.com/epifi/gamma/api/typesv2/account"
	eeProviders "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers"
	eeMock "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/mocks"

	"github.com/epifi/gamma/api/credit_limit_estimator/mocks"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	profileMocks "github.com/epifi/gamma/api/risk/profile/mocks"
	sav "github.com/epifi/gamma/api/savings"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	onbMocks "github.com/epifi/gamma/api/user/onboarding/mocks"
	mock_types "github.com/epifi/gamma/preapprovedloan/calculator/types/mocks"
	"github.com/epifi/gamma/preapprovedloan/config/genconf"
	mock_dao "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/landing_provider"
	multiDbProvider "github.com/epifi/gamma/preapprovedloan/multidb_provider"
	de "github.com/epifi/gamma/preapprovedloan/multidb_provider/decision_engine"
	mock_eligibility_evaluator "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/mocks"
	priorityProviderMock "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/mocks"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/provider"
	mock_provider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/provider/mocks"
)

var (
	loanOfferSampleFederal = &palPb.LoanOffer{
		Id:            "loan-offer-id-4",
		ActorId:       "act-4",
		VendorOfferId: "vendor-offer-id-4",
		Vendor:        palPb.Vendor_FEDERAL,
		OfferConstraints: &palPb.OfferConstraints{
			MaxLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        100000,
			},
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        10000,
			},
			MaxTenureMonths: 24,
			MinTenureMonths: 12,
			MaxEmiAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        1000,
			},
		},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-2",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		ValidTill:                      timestamppb.New(time.Now().AddDate(0, 0, 1)),
	}
	activeLoanOfferFed = &palPb.LoanOffer{
		Id:            "loan-offer-id-7",
		ActorId:       "actor-7",
		VendorOfferId: "vendor-offer-id-7",
		Vendor:        palPb.Vendor_FEDERAL,
		OfferConstraints: &palPb.OfferConstraints{
			MaxEmiAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        6000,
			},
			MaxLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        216000,
			},
			MaxTenureMonths: 36,
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        10000,
			},
		},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-2",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		ValidTill:                      timestamppb.New(time.Now().AddDate(0, 0, 1)),
	}
	activeLoanOfferAbfl = &palPb.LoanOffer{
		Id:            "loan-offer-id-7",
		ActorId:       "actor-7",
		VendorOfferId: "vendor-offer-id-7",
		Vendor:        palPb.Vendor_ABFL,
		OfferConstraints: &palPb.OfferConstraints{
			MaxEmiAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        6000,
			},
			MaxLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        216000,
			},
			MaxTenureMonths: 36,
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        10000,
			},
		},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-2",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		ValidTill:                      timestamppb.New(time.Now().AddDate(0, 0, 1)),
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}
	activeLoanOfferMv = &palPb.LoanOffer{
		Id:            "loan-offer-id-8",
		ActorId:       "actor-8",
		VendorOfferId: "vendor-offer-id-8",
		Vendor:        palPb.Vendor_MONEYVIEW,
		OfferConstraints: &palPb.OfferConstraints{
			MaxEmiAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        6000,
			},
			MaxLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        216000,
			},
			MaxTenureMonths: 36,
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        10000,
			},
		},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-2",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		ValidTill:                      timestamppb.New(time.Now().AddDate(0, 0, 1)),
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}
	activeLoanOfferLL = &palPb.LoanOffer{
		Id:            "loan-offer-id-7",
		ActorId:       "actor-7",
		VendorOfferId: "vendor-offer-id-7",
		Vendor:        palPb.Vendor_LIQUILOANS,
		OfferConstraints: &palPb.OfferConstraints{
			MaxEmiAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        6000,
			},
			MaxLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        216000,
			},
			MaxTenureMonths: 36,
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        10000,
			},
		},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-2",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		ValidTill:                      timestamppb.New(time.Now().AddDate(0, 0, 1)),
	}

	loanOfferSample1 = &palPb.LoanOffer{
		Id:                             "loan-offer-id-1",
		ActorId:                        "actor-1",
		VendorOfferId:                  "vendor-offer-id-1",
		Vendor:                         palPb.Vendor_FEDERAL,
		OfferConstraints:               &palPb.OfferConstraints{},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-1",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}

	loanOfferSample3 = &palPb.LoanOffer{
		Id:            "loan-offer-id-4",
		ActorId:       "actor-4",
		VendorOfferId: "vendor-offer-id-5",
		Vendor:        palPb.Vendor_FEDERAL,
		OfferConstraints: &palPb.OfferConstraints{
			MaxEmiAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        6000,
			},
			MaxLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        100000,
			},
			MaxTenureMonths: 36,
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        10000,
			},
		},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-2",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}

	loanOfferSample4 = &palPb.LoanOffer{
		Id:                             "loan-offer-id-4",
		ActorId:                        "act-4",
		VendorOfferId:                  "vendor-offer-id-4",
		Vendor:                         palPb.Vendor_IDFC,
		OfferConstraints:               &palPb.OfferConstraints{},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-2",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}

	loanReqSample1 = &palPb.LoanRequest{
		Id:              "request-id-10",
		ClientReqId:     "cr-id-3",
		ActorId:         "act-10",
		OfferId:         "off-10",
		OrchId:          "orch-10",
		LoanAccountId:   "acc-10",
		VendorRequestId: "req-10",
		Details:         &palPb.LoanRequestDetails{},
		Vendor:          palPb.Vendor_FEDERAL,
		Type:            palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
		Status:          palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
		SubStatus:       palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED,
		LoanProgram:     palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}

	loanReqSample3 = &palPb.LoanRequest{
		Id:              "request-id-7",
		ActorId:         "act-7",
		OfferId:         "off-7",
		OrchId:          "orch-7",
		LoanAccountId:   "acc-7",
		VendorRequestId: "req-7",
		Vendor:          palPb.Vendor_LIQUILOANS,
		Details:         &palPb.LoanRequestDetails{},
		Type:            palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
		Status:          palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED,
		SubStatus:       palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED,
		LoanProgram:     palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		CompletedAt:     timestamppb.Now(),
	}
	loanReqSample4 = &palPb.LoanRequest{
		Id:              "request-id-4",
		ActorId:         "act-4",
		OfferId:         "off-4",
		OrchId:          "orch-4",
		LoanAccountId:   "acc-4",
		VendorRequestId: "req-4",
		Details:         &palPb.LoanRequestDetails{},
		Vendor:          palPb.Vendor_IDFC,
		Type:            palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
		Status:          palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED,
		SubStatus:       palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CANCELLED,
		LoanProgram:     palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		CompletedAt:     timestamppb.Now(),
	}

	loanAccountSample3 = &palPb.LoanAccount{
		Id:            "account-id-ll-3",
		ActorId:       "act-ll-3",
		Vendor:        palPb.Vendor_LIQUILOANS,
		AccountNumber: "acc-ll-3",
		LoanType:      palPb.LoanType_LOAN_TYPE_PERSONAL,
		IfscCode:      "ifsc-ll-3",
		LoanAmountInfo: &palPb.LoanAmountInfo{
			LoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        14000,
			},
			DisbursedAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        12500,
			},
			OutstandingAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        18000,
			},
			TotalPayableAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        18000,
			},
		},
		LoanEndDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   15,
		},
		MaturityDate: &datePb.Date{
			Year:  2022,
			Month: 8,
			Day:   10,
		},
		Details:     &palPb.LoanAccountDetails{},
		Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	}
	sampleloecSg = &palPb.LoanOfferEligibilityCriteria{
		Id:              "PALEC_SG_RTD_EXAMPLE_001",
		ActorId:         "AC_EXAMPLE_USER_7788",
		Vendor:          palPb.Vendor_STOCK_GUARDIAN_LSP,
		LoanProgram:     palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Status:          palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED,
		SubStatus:       palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED,
		OfferId:         "",
		BatchId:         "BATCH_RTD_SG_20240731",
		VendorRequestId: "VENDOR_REQ_SG_RTD_990011",
		CreatedAt:       timestamppb.Now(),
		UpdatedAt:       timestamppb.Now(),
		ExpiredAt:       timestamppb.New(time.Now().AddDate(0, 0, 60)),
		DataRequirementDetails: &palPb.DataRequirementDetails{
			DataRequirements: []*palPb.DataRequirement{{DataRequirementType: palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_EPFO, IsCollected: false}},
		},
	}
)

func TestMultiDbProviderImpl_GetLandingPageDataForActor(t *testing.T) {
	t.Parallel()
	ctrl := gomock.NewController(t)
	mockProfileClient := profileMocks.NewMockProfileClient(ctrl)
	onbClient := onbMocks.NewMockOnboardingClient(ctrl)
	mockLimitEstimatorClient := mocks.NewMockCreditLimitEstimatorClient(ctrl)
	mockEligibilityFactory := mock_eligibility_evaluator.NewMockEligibilityEvaluatorFactory(ctrl)
	mockFactoryProvider := mock_types.NewMockFactoryProvider(ctrl)
	mockLoanAccount := mock_dao.NewMockLoanAccountsDao(ctrl)
	mockLoanRequest := mock_dao.NewMockLoanRequestsDao(ctrl)
	mockLoanOffer := mock_dao.NewMockLoanOffersDao(ctrl)
	mockLseDao := mock_dao.NewMockLoanStepExecutionsDao(ctrl)
	mockLoec := mock_dao.NewMockLoanOfferEligibilityCriteriaDao(ctrl)
	mockRecommendationEngine := mock_provider.NewMockILoanRecommendationEngine(ctrl)
	fedOffer := CloneLoanOffer(loanOfferSampleFederal)
	multiDBDataProvider := multiDbProvider.NewMultiDBProvider(nil, mockLoanAccount, mockLoanOffer, mockLoanRequest, mockLoec)
	ppMock := priorityProviderMock.NewMockIPriorityProviderFactory(ctrl)
	eligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
	savingClient := mocks3.NewMockSavingsClient(ctrl)
	savingClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&sav.GetAccountResponse{
		Account: &sav.Account{
			Id:      "account-id-1",
			EmailId: "<EMAIL>",
			ActorId: "actor-4",
		},
	}, nil).AnyTimes()

	onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
		Status:       rpc.StatusOk(),
		IsFiLiteUser: false,
		FeatureInfo:  nil,
	}, nil).AnyTimes()

	type args struct {
		ctx context.Context
		req *landing_provider.GetLandingPageDataForActorRequest
	}
	type expectedOutput struct {
		res *landing_provider.GetLandingPageDataForActorResponse
		err error
	}

	tests := []struct {
		name           string
		args           args
		setupMocks     func()
		expectedOutput expectedOutput
	}{
		{
			name: "should return loan request and offer, if terminal loan request is within 1 day and offer is still active from same vendor",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*palPb.LoanRequest{{
						Status:      palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED,
						Vendor:      palPb.Vendor_LIQUILOANS,
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						CompletedAt: timestamppb.Now(),
					}}, nil)
				mockLseDao.EXPECT().GetLatestByRefIdAndFlow(gomock.Any(), gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
				}, nil)
				mockRecommendationEngine.EXPECT().GetRankedLoanOptions(gomock.Any(), gomock.Any()).Return(&provider.GetRankedLoanOptionsResponse{
					ActiveOffers: nil,
					Eligibility:  nil,
				}, nil).Times(1)
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &palPb.LoanHeader{
						Vendor:      palPb.Vendor_LIQUILOANS,
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					},
					LoanRequests: []*palPb.LoanRequest{{
						Status:      palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED,
						Vendor:      palPb.Vendor_LIQUILOANS,
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					}},
				},
				err: nil,
			},
		},
		{
			// in this case, will try to explore other offers also
			name: "should return only loan request , if terminal loan request exist within 1 day , prev account is closed and no active offer",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*palPb.LoanAccount{{
						Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED,
						Vendor:      palPb.Vendor_LIQUILOANS,
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					}}, nil)
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*palPb.LoanRequest{CloneLoanRequest(loanReqSample3)}, nil)
				mockRecommendationEngine.EXPECT().GetRankedLoanOptions(gomock.Any(), gomock.Any()).Return(&provider.GetRankedLoanOptionsResponse{
					ActiveOffers: nil,
					Eligibility:  nil,
				}, nil).Times(1)
				mockLseDao.EXPECT().GetLatestByRefIdAndFlow(gomock.Any(), gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
				}, nil)
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: loanReqSample3.GetLoanProgram(),
						Vendor:      loanReqSample3.GetVendor(),
					},
					LoanRequests: []*palPb.LoanRequest{CloneLoanRequest(loanReqSample3)},
				},
				err: nil,
			},
		},
		{
			name: "should return no offer, if one closed account and no offer,request is present",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*palPb.LoanAccount{{
						Status: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED},
					}, nil)
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockRecommendationEngine.EXPECT().GetRankedLoanOptions(gomock.Any(), gomock.Any()).Return(&provider.GetRankedLoanOptionsResponse{
					ActiveOffers: nil,
					Eligibility:  nil,
				}, nil).Times(1)
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{},
				err: nil,
			},
		},
		{
			name: "should return no offer if , no account, request ,loan offer and loec exist for ineligible case",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockRecommendationEngine.EXPECT().GetRankedLoanOptions(gomock.Any(), gomock.Any()).Return(&provider.GetRankedLoanOptionsResponse{
					ActiveOffers: nil,
					Eligibility:  nil,
				}, nil).Times(1)
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{},
				err: nil,
			},
		},
		{
			name: "should return no offer if , no account and request is there but invalid offer present",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockRecommendationEngine.EXPECT().GetRankedLoanOptions(gomock.Any(), gomock.Any()).Return(&provider.GetRankedLoanOptionsResponse{
					ActiveOffers: nil,
					Eligibility:  nil,
				}, nil).Times(1)
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{},
				err: nil,
			},
		},
		{
			name: "should return one active loan request",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*palPb.LoanRequest{
						CloneLoanRequest(loanReqSample1),
					}, nil)
				mockLseDao.EXPECT().GetLatestByRefIdAndFlow(gomock.Any(), gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
				}, nil)
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanRequests: []*palPb.LoanRequest{
						CloneLoanRequest(loanReqSample1),
					},
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: loanReqSample1.GetLoanProgram(),
						Vendor:      loanReqSample1.GetVendor(),
					},
				},
				err: nil,
			},
		},
		{
			name: "should return realtime subvention loec, if no account, request, offer is present",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockRecommendationEngine.EXPECT().GetRankedLoanOptions(gomock.Any(), gomock.Any()).Return(&provider.GetRankedLoanOptionsResponse{
					ActiveOffers: nil,
					Eligibility: &provider.Eligibility{
						CheckEligibility: true,
						LoanHeader: &palPb.LoanHeader{
							LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION,
							Vendor:      palPb.Vendor_LIQUILOANS,
						},
					},
				}, nil).Times(1)
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION,
						Vendor:      palPb.Vendor_LIQUILOANS,
					},
					Eligibility: &landing_provider.LoanEligibility{
						CheckEligibility: true,
					},
				},
				err: nil,
			},
		},
		{
			name: "should return  request if, one closed account and active loan request from same vendor",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*palPb.LoanAccount{{
						Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED,
						Vendor:      palPb.Vendor_LIQUILOANS,
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					},
					}, nil)

				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*palPb.LoanRequest{
						CloneLoanRequest(loanReqSample1),
					}, nil)
				mockLseDao.EXPECT().GetLatestByRefIdAndFlow(gomock.Any(), gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
				}, nil)

			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: loanReqSample1.GetLoanProgram(),
						Vendor:      loanReqSample1.GetVendor(),
					},
					LoanRequests: []*palPb.LoanRequest{
						CloneLoanRequest(loanReqSample1),
					},
				},
				err: nil,
			},
		},
		{
			name: "should return active loan account, if one active and one closed account is present",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId:     loanAccountSample3.GetActorId(),
					LoanProgram: loanAccountSample3.GetLoanProgram(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*palPb.LoanAccount{
						loanAccountSample3,
						{Status: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED},
					}, nil).Times(1)
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: loanAccountSample3.GetLoanProgram(),
						Vendor:      loanAccountSample3.GetVendor(),
					},
					ActiveLoanAccount: loanAccountSample3,
				},
				err: nil,
			},
		},
		{
			name: "if loan account exists, return only loan account",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId:     loanAccountSample3.GetActorId(),
					LoanProgram: loanAccountSample3.GetLoanProgram(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*palPb.LoanAccount{loanAccountSample3}, nil)
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
						Vendor:      palPb.Vendor_LIQUILOANS,
					},
					ActiveLoanAccount: loanAccountSample3,
				},
				err: nil,
			},
		},
		{
			name: "should return  offer if, one closed account and another active offer from same vendor and loan program",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*palPb.LoanAccount{{
						Status: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED,
					},
					}, nil)
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockRecommendationEngine.EXPECT().GetRankedLoanOptions(gomock.Any(), gomock.Any()).Return(&provider.GetRankedLoanOptionsResponse{
					ActiveOffers: []*palPb.LoanOffer{fedOffer},
					Eligibility:  nil,
				}, nil).Times(1)
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: loanOfferSampleFederal.GetLoanProgram(),
						Vendor:      loanOfferSampleFederal.GetVendor(),
					},
					ActiveLoanOffers: []*palPb.LoanOffer{fedOffer},
				},
				err: nil,
			},
		},
		{
			name: "should return terminal loan request with active offer of different vendor, if offer of current loan request's vendor is deactivated",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*palPb.LoanRequest{CloneLoanRequest(loanReqSample4)}, nil)
				mockRecommendationEngine.EXPECT().GetRankedLoanOptions(gomock.Any(), gomock.Any()).Return(&provider.GetRankedLoanOptionsResponse{
					ActiveOffers: []*palPb.LoanOffer{activeLoanOfferFed},
				}, nil).Times(1)
				mockLseDao.EXPECT().GetLatestByRefIdAndFlow(gomock.Any(), gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{
					StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
				}, nil)
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: loanReqSample4.GetLoanProgram(),
						Vendor:      loanReqSample4.GetVendor(),
					},
					LoanRequests:     []*palPb.LoanRequest{CloneLoanRequest(loanReqSample4)},
					ActiveLoanOffers: []*palPb.LoanOffer{CloneLoanOffer(activeLoanOfferFed)},
				},
				err: nil,
			},
		},
		{
			name: "should return offer, if terminal loan request is after 1 day and offer is still active from any vendor",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockRecommendationEngine.EXPECT().GetRankedLoanOptions(gomock.Any(), gomock.Any()).Return(&provider.GetRankedLoanOptionsResponse{
					ActiveOffers: []*palPb.LoanOffer{activeLoanOfferFed},
				}, nil).Times(1)

			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: activeLoanOfferFed.GetLoanProgram(),
						Vendor:      activeLoanOfferFed.GetVendor(),
					},
					ActiveLoanOffers: []*palPb.LoanOffer{CloneLoanOffer(activeLoanOfferFed)},
				},
				err: nil,
			},
		},
		{
			name: "should return all offers if no account, request and multiple offers are present",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockRecommendationEngine.EXPECT().GetRankedLoanOptions(gomock.Any(), gomock.Any()).Return(&provider.GetRankedLoanOptionsResponse{
					ActiveOffers: []*palPb.LoanOffer{activeLoanOfferFed, activeLoanOfferLL},
				}, nil).Times(1)

			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: activeLoanOfferFed.GetLoanProgram(),
						Vendor:      activeLoanOfferFed.GetVendor(),
					},
					ActiveLoanOffers: []*palPb.LoanOffer{CloneLoanOffer(activeLoanOfferFed), CloneLoanOffer(activeLoanOfferLL)},
				},
				err: nil,
			},
		},
		{
			name: "should return abfl offer over eligibility since flag is off",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				// First check for existing loan accounts
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)

				// Then check for existing loan requests
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)

				// Get ranked loan options
				mockRecommendationEngine.EXPECT().GetRankedLoanOptions(gomock.Any(), gomock.Any()).Return(&provider.GetRankedLoanOptionsResponse{
					ActiveOffers: []*palPb.LoanOffer{activeLoanOfferAbfl, activeLoanOfferLL},
				}, nil)

				// Get account details
				savingClient.EXPECT().GetAccount(gomock.Any(), &sav.GetAccountRequest{
					Identifier: &sav.GetAccountRequest_ActorUniqueAccountIdentifier{
						ActorUniqueAccountIdentifier: &sav.ActorUniqueAccountIdentifier{
							ActorId:                "actor-4",
							PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
							AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
						},
					},
				}).Return(&sav.GetAccountResponse{
					Account: &sav.Account{
						Id:      "account-id-1",
						EmailId: "<EMAIL>",
						ActorId: "actor-4",
					},
				}, nil).AnyTimes()

				// Get feature details
				onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
					FeatureInfo:  nil,
				}, nil).AnyTimes()

				// Get active LOECs
				mockLoec.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*palPb.LoanOfferEligibilityCriteria{sampleloecSg}, nil).AnyTimes()
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: activeLoanOfferAbfl.GetLoanProgram(),
						Vendor:      activeLoanOfferAbfl.GetVendor(),
					},
					ActiveLoanOffers: []*palPb.LoanOffer{CloneLoanOffer(activeLoanOfferAbfl), CloneLoanOffer(activeLoanOfferLL)},
				},
				err: nil,
			},
		},
		{
			name: "should return eligibility when the offer is MV at priority",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId: loanOfferSample3.GetActorId(),
				},
			},
			setupMocks: func() {
				// First check for existing loan accounts
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)

				// Then check for existing loan requests
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)

				// Get ranked loan options
				mockRecommendationEngine.EXPECT().GetRankedLoanOptions(gomock.Any(), gomock.Any()).Return(&provider.GetRankedLoanOptionsResponse{
					ActiveOffers: []*palPb.LoanOffer{activeLoanOfferMv, activeLoanOfferLL},
				}, nil)

				// Get account details
				savingClient.EXPECT().GetAccount(gomock.Any(), &sav.GetAccountRequest{
					Identifier: &sav.GetAccountRequest_ActorUniqueAccountIdentifier{
						ActorUniqueAccountIdentifier: &sav.ActorUniqueAccountIdentifier{
							ActorId:                "actor-4",
							PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
							AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
						},
					},
				}).Return(&sav.GetAccountResponse{
					Account: &sav.Account{
						Id:      "account-id-1",
						EmailId: "<EMAIL>",
						ActorId: "actor-4",
					},
				}, nil).AnyTimes()

				// Get feature details
				onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
					FeatureInfo:  nil,
				}, nil).AnyTimes()

				// Get active LOECs
				mockLoec.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*palPb.LoanOfferEligibilityCriteria{sampleloecSg}, nil).AnyTimes()

				// Get eligibility evaluator
				mockEligibilityFactory.EXPECT().GetEligibilityEvaluatorProvider(&palPb.LoanHeader{
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
					Vendor:      palPb.Vendor_STOCK_GUARDIAN_LSP,
				}, false).Return(eligibilityEvaluatorMock, nil).AnyTimes()

				// Evaluate loan eligibility
				eligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), &palPb.LoanHeader{
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
					Vendor:      palPb.Vendor_STOCK_GUARDIAN_LSP,
				}).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: false,
					CheckEligibility: &eeProviders.CheckEligibility{
						ShouldCheckEligibility: true,
					},
				}, nil).AnyTimes()
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
						Vendor:      palPb.Vendor_EPIFI_TECH,
					},
					Eligibility: &landing_provider.LoanEligibility{
						CheckEligibility: true,
					},
				},
				err: nil,
			},
		},
		{
			name: "should return with all active offers of all vendor, if skip failed LR in request is present",
			args: args{
				ctx: context.Background(),
				req: &landing_provider.GetLandingPageDataForActorRequest{
					ActorId:      loanOfferSample3.GetActorId(),
					SkipFailedLR: true,
				},
			},
			setupMocks: func() {
				mockLoanAccount.EXPECT().GetByActorIdStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					nil, epifierrors.ErrRecordNotFound)
				mockLoanRequest.EXPECT().GetByActorIdTypesStatusAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*palPb.LoanRequest{CloneLoanRequest(loanReqSample4)}, nil)
				mockRecommendationEngine.EXPECT().GetRankedLoanOptions(gomock.Any(), gomock.Any()).Return(&provider.GetRankedLoanOptionsResponse{
					ActiveOffers: []*palPb.LoanOffer{activeLoanOfferFed, activeLoanOfferAbfl},
				}, nil).Times(1)
				// mockLseDao.EXPECT().GetLatestByRefIdAndFlow(gomock.Any(), gomock.Any(), gomock.Any()).Return(&palPb.LoanStepExecution{
				//	StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
				// }, nil)
			},
			expectedOutput: expectedOutput{
				res: &landing_provider.GetLandingPageDataForActorResponse{
					LoanHeader: &palPb.LoanHeader{
						LoanProgram: activeLoanOfferFed.GetLoanProgram(),
						Vendor:      activeLoanOfferFed.GetVendor(),
					},
					ActiveLoanOffers: []*palPb.LoanOffer{CloneLoanOffer(activeLoanOfferFed), CloneLoanOffer(activeLoanOfferAbfl)},
				},
				err: nil,
			},
		},
	}
	for _, tc := range tests {
		tc := tc

		t.Run(tc.name, func(t *testing.T) {
			if tc.setupMocks != nil {
				tc.setupMocks()
			}
			provider := getLandingPageDataForActorsMock(
				mockProfileClient, onbClient, dynConf, mockLimitEstimatorClient, mockEligibilityFactory, mockFactoryProvider,
				mockLoanOffer, multiDBDataProvider, mockRecommendationEngine, ppMock, mockLseDao, mockLoec, savingClient,
			)
			res, err := provider.GetLandingPageDataForActor(epificontext.WithOwnership(context.Background(), commontypes.Ownership_EPIFI_TECH_V2), tc.args.req)
			if res != nil && res.ActiveLoanAccount != nil {
				res.ActiveLoanAccount.CreatedAt = nil
				res.ActiveLoanAccount.UpdatedAt = nil
			}
			for idx := range res.ActiveLoanOffers {
				res.ActiveLoanOffers[idx].ValidTill = nil
			}
			for idx := range tc.expectedOutput.res.ActiveLoanOffers {
				tc.expectedOutput.res.ActiveLoanOffers[idx].ValidTill = nil
			}

			require.True(t, proto.Equal(tc.expectedOutput.res.LoanHeader, res.LoanHeader), fmt.Sprintf("Returned loan header does not match expected loan header, %v, %v", tc.expectedOutput.res.LoanHeader, res.LoanHeader))
			require.True(t, proto.Equal(tc.expectedOutput.res.ActiveLoanAccount, res.ActiveLoanAccount), "Returned loan account does not match expected account")
			require.Equal(t, len(tc.expectedOutput.res.ActiveLoanOffers), len(res.ActiveLoanOffers), "No of returned loan offers is not same as expected number of loan offers")
			require.True(t, CompareLoanOffers(tc.expectedOutput.res.ActiveLoanOffers, res.ActiveLoanOffers), "Returned loan offers do not match expected offers")
			require.Equal(t, len(tc.expectedOutput.res.LoanRequests), len(res.LoanRequests), "No of returned loan requests is not same as expected number of loan requests")
			for index, lr := range res.LoanRequests {
				res.LoanRequests[index].CompletedAt = nil
				tc.expectedOutput.res.LoanRequests[index].CompletedAt = nil
				require.True(t, proto.Equal(tc.expectedOutput.res.LoanRequests[index], lr), fmt.Sprintf("Returned loan requests do not match at index %v", index))
			}
			require.Equal(t, tc.expectedOutput.err, err)

		})
	}
}

func CompareLoanOffers(expected, actual []*palPb.LoanOffer) bool {
	if len(expected) != len(actual) {
		return false
	}
	for i := range expected {
		if !proto.Equal(expected[i], actual[i]) {
			return false
		}
	}
	return true
}

func CloneLoanOffer(lo *palPb.LoanOffer) *palPb.LoanOffer {
	return proto.Clone(lo).(*palPb.LoanOffer)
}

func CloneLoanRequest(lr *palPb.LoanRequest) *palPb.LoanRequest {
	return proto.Clone(lr).(*palPb.LoanRequest)
}

func getLandingPageDataForActorsMock(
	mockProfileClient *profileMocks.MockProfileClient,
	onbClient *onbMocks.MockOnboardingClient,
	dynConf *genconf.Config,
	mockLimitEstomatorClient *mocks.MockCreditLimitEstimatorClient,
	mockEligibilityEvalFactory *mock_eligibility_evaluator.MockEligibilityEvaluatorFactory,
	mockcalculatorFactory *mock_types.MockFactoryProvider,
	mockLoanOffer *mock_dao.MockLoanOffersDao,
	multiDbProvider *multiDbProvider.MultiDbProviderImpl,
	mockRecommendationEngine *mock_provider.MockILoanRecommendationEngine,
	mockPP *priorityProviderMock.MockIPriorityProviderFactory,
	mockLseDao *mock_dao.MockLoanStepExecutionsDao,
	mockLoecDao *mock_dao.MockLoanOfferEligibilityCriteriaDao,
	savingClient *mocks3.MockSavingsClient,
) *landing_provider.LandingProvider {
	rpcHelper := helper.NewRpcHelper(nil, nil, nil, savingClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockProfileClient, nil, nil, nil, nil, nil, nil, nil, onbClient, nil, nil, nil, nil, nil, nil, mockLimitEstomatorClient, nil, nil, nil, nil, nil, nil)
	provider := landing_provider.NewLandingProvider(rpcHelper, de.NewSimpleOffersDecisionEngine(dynConf.Flags()), mockLoanOffer, onbClient, mockLimitEstomatorClient, mockcalculatorFactory, mockPP, mockEligibilityEvalFactory, dynConf, multiDbProvider, nil, mockRecommendationEngine, mockLseDao, mockLoecDao, savingClient)
	return provider
}
