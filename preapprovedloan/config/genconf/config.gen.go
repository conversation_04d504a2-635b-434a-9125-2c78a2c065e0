// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	pkgweb "github.com/epifi/be-common/api/pkg/web"
	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	romap "github.com/epifi/be-common/pkg/data_structs/romap"
	genapp "github.com/epifi/be-common/pkg/frontend/app/genconf"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	gentypes "github.com/epifi/be-common/pkg/types/genconf"
	questsdk "github.com/epifi/be-common/quest/sdk"
	genconfig "github.com/epifi/be-common/quest/sdk/config/genconf"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	preapprovedloan "github.com/epifi/gamma/api/preapprovedloan"
	gendeeplinkcfgv "github.com/epifi/gamma/pkg/deeplink/cfg/v2/genconf"
	genconfig2 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	config "github.com/epifi/gamma/preapprovedloan/config"
	common "github.com/epifi/gamma/preapprovedloan/config/common"
	gencommon "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EsignLinkExpirationInSecs int32
	// flag to decide whether a campaign related dynamic element for home screen is enabled or not, if enabled then campaign one would override the default one.
	// use-case is to show a custom loan banner on home for marketing campaign use-cases e.g. showing a custom loan banner on home during Diwali festival,
	// this banner can be disabled from dynamic config once the campaign is over.
	_IsCampaignRelatedHomeDynamicElementEnabled uint32
	_PgdbMigrationFlag                          uint32
	_EnableDataExistenceManager                 uint32
	// category format <vendor>:<loan_program>
	_CategoryToEmiComms *syncmap.Map[string, *EmiComms]
	// list of segments to whom the LAMF related dynamic element (instead of the default loan one) should be shown on home screen.
	_AllowedSegmentIdsForDisplayingLAMFHomeDynamicElement      roarray.ROArray[string]
	_AllowedSegmentIdsForDisplayingLAMFHomeDynamicElementMutex *sync.RWMutex
	_Flags                                                     *gencommon.Flags
	_ProcessLoanInboundTransactionSqsSubscriber                *gencfg.SqsSubscriber
	_OrderUpdatePreApprovedLoanSqsSubscriber                   *gencfg.SqsSubscriber
	_ProcessLoansFiftyfinCallbackSqsSubscriber                 *gencfg.SqsSubscriber
	_Prepay                                                    *gencommon.Prepay
	_HomeWidgetV1Config                                        *HomeWidgetV1Config
	_LamfConfig                                                *LamfConfig
	_Downtime                                                  *Downtime
	_DeeplinkConfig                                            *gencommon.DeeplinkConfig
	_QuestSdk                                                  *genconfig.Config
	_CreditReportConfig                                        *gencommon.CreditReportConfig
	_FeatureReleaseConfig                                      *genconfig2.FeatureReleaseConfig
	_RepeatLoans                                               *RepeatLoansConfig
	_MandateConfig                                             *gencommon.MandateConfig
	_WealthToTechDataSharingConsentConfig                      *ConsentConfig
	_SmsFetchingConsentConfig                                  *ConsentConfig
	_VendorProgramLevelFeature                                 *gencommon.VendorProgramLevelFeature
	_LopeOverrideConfig                                        *gencommon.LopeOverrideConfig
	_Lendability                                               *Lendability
	_SgEtbNewEligibilityFlow                                   *gencommon.FeatureConstraint
	_AutoCancelCurrentLrConfig                                 *genconfig2.AppVersionConstraintConfig
	_Application                                               *config.Application
	_Logging                                                   *cfg.Logging
	_SecureLogging                                             *cfg.SecureLogging
	_Server                                                    *config.Server
	_FederalDb                                                 *cfg.DB
	_FederalPgDb                                               *cfg.DB
	_AWS                                                       *config.Aws
	_RudderStack                                               *cfg.RudderStackBroker
	_Secrets                                                   *cfg.Secrets
	_Tracing                                                   *cfg.Tracing
	_Vendor                                                    *config.Vendor
	_KfsExitUrl                                                string
	_DbConfigMap                                               cfg.DbConfigMap
	_PgDbConfigMap                                             cfg.DbConfigMap
	_VendorsSupported                                          []preapprovedloan.Vendor
	_VendorUsed                                                preapprovedloan.Vendor
	_SignalWorkflowPublisher                                   *cfg.SqsPublisher
	_Notification                                              *common.Notification
	_NudgeExitEventPublisher                                   *cfg.SqsPublisher
	_EarlySalary                                               *config.EarlySalary
	_EnableDynamicElementForAllUsers                           bool
	_InstantCashSegmentId                                      string
	_PreApprovedLoanBucketName                                 string
	_Lms                                                       *common.Lms
	_LoanCalculator                                            *common.LoanCalculator
	_LoanOfferPrioritisation                                   *config.LoanOfferPrioritisation
}

func (obj *Config) EsignLinkExpirationInSecs() int64 {
	return int64(atomic.LoadInt32(&obj._EsignLinkExpirationInSecs))
}

// flag to decide whether a campaign related dynamic element for home screen is enabled or not, if enabled then campaign one would override the default one.
// use-case is to show a custom loan banner on home for marketing campaign use-cases e.g. showing a custom loan banner on home during Diwali festival,
// this banner can be disabled from dynamic config once the campaign is over.
func (obj *Config) IsCampaignRelatedHomeDynamicElementEnabled() bool {
	if atomic.LoadUint32(&obj._IsCampaignRelatedHomeDynamicElementEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) PgdbMigrationFlag() bool {
	if atomic.LoadUint32(&obj._PgdbMigrationFlag) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnableDataExistenceManager() bool {
	if atomic.LoadUint32(&obj._EnableDataExistenceManager) == 0 {
		return false
	} else {
		return true
	}
}

// category format <vendor>:<loan_program>
func (obj *Config) CategoryToEmiComms() *syncmap.Map[string, *EmiComms] {
	return obj._CategoryToEmiComms
}

// list of segments to whom the LAMF related dynamic element (instead of the default loan one) should be shown on home screen.
func (obj *Config) AllowedSegmentIdsForDisplayingLAMFHomeDynamicElement() roarray.ROArray[string] {
	obj._AllowedSegmentIdsForDisplayingLAMFHomeDynamicElementMutex.RLock()
	defer obj._AllowedSegmentIdsForDisplayingLAMFHomeDynamicElementMutex.RUnlock()
	return obj._AllowedSegmentIdsForDisplayingLAMFHomeDynamicElement
}
func (obj *Config) Flags() *gencommon.Flags {
	return obj._Flags
}
func (obj *Config) ProcessLoanInboundTransactionSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessLoanInboundTransactionSqsSubscriber
}
func (obj *Config) OrderUpdatePreApprovedLoanSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._OrderUpdatePreApprovedLoanSqsSubscriber
}
func (obj *Config) ProcessLoansFiftyfinCallbackSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessLoansFiftyfinCallbackSqsSubscriber
}
func (obj *Config) Prepay() *gencommon.Prepay {
	return obj._Prepay
}
func (obj *Config) HomeWidgetV1Config() *HomeWidgetV1Config {
	return obj._HomeWidgetV1Config
}
func (obj *Config) LamfConfig() *LamfConfig {
	return obj._LamfConfig
}
func (obj *Config) Downtime() *Downtime {
	return obj._Downtime
}
func (obj *Config) DeeplinkConfig() *gencommon.DeeplinkConfig {
	return obj._DeeplinkConfig
}
func (obj *Config) QuestSdk() *genconfig.Config {
	return obj._QuestSdk
}
func (obj *Config) CreditReportConfig() *gencommon.CreditReportConfig {
	return obj._CreditReportConfig
}
func (obj *Config) FeatureReleaseConfig() *genconfig2.FeatureReleaseConfig {
	return obj._FeatureReleaseConfig
}
func (obj *Config) RepeatLoans() *RepeatLoansConfig {
	return obj._RepeatLoans
}
func (obj *Config) MandateConfig() *gencommon.MandateConfig {
	return obj._MandateConfig
}
func (obj *Config) WealthToTechDataSharingConsentConfig() *ConsentConfig {
	return obj._WealthToTechDataSharingConsentConfig
}
func (obj *Config) SmsFetchingConsentConfig() *ConsentConfig {
	return obj._SmsFetchingConsentConfig
}
func (obj *Config) VendorProgramLevelFeature() *gencommon.VendorProgramLevelFeature {
	return obj._VendorProgramLevelFeature
}
func (obj *Config) LopeOverrideConfig() *gencommon.LopeOverrideConfig {
	return obj._LopeOverrideConfig
}
func (obj *Config) Lendability() *Lendability {
	return obj._Lendability
}
func (obj *Config) SgEtbNewEligibilityFlow() *gencommon.FeatureConstraint {
	return obj._SgEtbNewEligibilityFlow
}
func (obj *Config) AutoCancelCurrentLrConfig() *genconfig2.AppVersionConstraintConfig {
	return obj._AutoCancelCurrentLrConfig
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) SecureLogging() *cfg.SecureLogging {
	return obj._SecureLogging
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) FederalDb() *cfg.DB {
	return obj._FederalDb
}
func (obj *Config) FederalPgDb() *cfg.DB {
	return obj._FederalPgDb
}
func (obj *Config) AWS() *config.Aws {
	return obj._AWS
}
func (obj *Config) RudderStack() *cfg.RudderStackBroker {
	return obj._RudderStack
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Vendor() *config.Vendor {
	return obj._Vendor
}
func (obj *Config) KfsExitUrl() string {
	return obj._KfsExitUrl
}
func (obj *Config) DbConfigMap() cfg.DbConfigMap {
	return obj._DbConfigMap
}
func (obj *Config) PgDbConfigMap() cfg.DbConfigMap {
	return obj._PgDbConfigMap
}
func (obj *Config) VendorsSupported() []preapprovedloan.Vendor {
	return obj._VendorsSupported
}
func (obj *Config) VendorUsed() preapprovedloan.Vendor {
	return obj._VendorUsed
}
func (obj *Config) SignalWorkflowPublisher() *cfg.SqsPublisher {
	return obj._SignalWorkflowPublisher
}
func (obj *Config) Notification() *common.Notification {
	return obj._Notification
}
func (obj *Config) NudgeExitEventPublisher() *cfg.SqsPublisher {
	return obj._NudgeExitEventPublisher
}
func (obj *Config) EarlySalary() *config.EarlySalary {
	return obj._EarlySalary
}
func (obj *Config) EnableDynamicElementForAllUsers() bool {
	return obj._EnableDynamicElementForAllUsers
}
func (obj *Config) InstantCashSegmentId() string {
	return obj._InstantCashSegmentId
}
func (obj *Config) PreApprovedLoanBucketName() string {
	return obj._PreApprovedLoanBucketName
}
func (obj *Config) Lms() *common.Lms {
	return obj._Lms
}
func (obj *Config) LoanCalculator() *common.LoanCalculator {
	return obj._LoanCalculator
}
func (obj *Config) LoanOfferPrioritisation() *config.LoanOfferPrioritisation {
	return obj._LoanOfferPrioritisation
}

type HomeWidgetV1Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// denotes whether the v1 home widget is enabled or not
	_IsEnabled                 uint32
	_PersonalLoanDisplayConfig *LoanProductLevelHomeWidgetV1DisplayConfig
	_LAMFDisplayConfig         *LoanProductLevelHomeWidgetV1DisplayConfig
}

// denotes whether the v1 home widget is enabled or not
func (obj *HomeWidgetV1Config) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *HomeWidgetV1Config) PersonalLoanDisplayConfig() *LoanProductLevelHomeWidgetV1DisplayConfig {
	return obj._PersonalLoanDisplayConfig
}
func (obj *HomeWidgetV1Config) LAMFDisplayConfig() *LoanProductLevelHomeWidgetV1DisplayConfig {
	return obj._LAMFDisplayConfig
}

type LoanProductLevelHomeWidgetV1DisplayConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ActiveOfferDisplayConfig   *HomeWidgetV1DisplayConfig
	_NoActiveOfferDisplayConfig *HomeWidgetV1DisplayConfig
	_CampaignDisplayConfig      *HomeWidgetV1DisplayConfig
}

func (obj *LoanProductLevelHomeWidgetV1DisplayConfig) ActiveOfferDisplayConfig() *HomeWidgetV1DisplayConfig {
	return obj._ActiveOfferDisplayConfig
}
func (obj *LoanProductLevelHomeWidgetV1DisplayConfig) NoActiveOfferDisplayConfig() *HomeWidgetV1DisplayConfig {
	return obj._NoActiveOfferDisplayConfig
}
func (obj *LoanProductLevelHomeWidgetV1DisplayConfig) CampaignDisplayConfig() *HomeWidgetV1DisplayConfig {
	return obj._CampaignDisplayConfig
}

type HomeWidgetV1DisplayConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// denotes the list of tiles to be displayed on the widget,
	// **Note** : using a map instead of list DS for configuring multiple tiles as dynamic config does not support list DS
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7899-62631&mode=design&t=sU6gvaCI9rGHdd6l-0
	_WidgetTiles *syncmap.Map[string, *HomeWidgetV1TileConfig]
	// denotes the image to be used for the vertically aligned primary banner on home widget.
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7899-62631&mode=design&t=sU6gvaCI9rGHdd6l-0
	_VerticalPrimaryBannerImageUrl      string
	_VerticalPrimaryBannerImageUrlMutex *sync.RWMutex
	// denotes the image to be used for the horizontally aligned primary banner on home widget.
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7548-34721&mode=design&t=sU6gvaCI9rGHdd6l-0
	_HorizontalPrimaryBannerImageUrl      string
	_HorizontalPrimaryBannerImageUrlMutex *sync.RWMutex
	_Heading                              *gentypes.Text
	_PrimaryCta                           *CTA
}

// denotes the list of tiles to be displayed on the widget,
// **Note** : using a map instead of list DS for configuring multiple tiles as dynamic config does not support list DS
// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7899-62631&mode=design&t=sU6gvaCI9rGHdd6l-0
func (obj *HomeWidgetV1DisplayConfig) WidgetTiles() *syncmap.Map[string, *HomeWidgetV1TileConfig] {
	return obj._WidgetTiles
}

// denotes the image to be used for the vertically aligned primary banner on home widget.
// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7899-62631&mode=design&t=sU6gvaCI9rGHdd6l-0
func (obj *HomeWidgetV1DisplayConfig) VerticalPrimaryBannerImageUrl() string {
	obj._VerticalPrimaryBannerImageUrlMutex.RLock()
	defer obj._VerticalPrimaryBannerImageUrlMutex.RUnlock()
	return obj._VerticalPrimaryBannerImageUrl
}

// denotes the image to be used for the horizontally aligned primary banner on home widget.
// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7548-34721&mode=design&t=sU6gvaCI9rGHdd6l-0
func (obj *HomeWidgetV1DisplayConfig) HorizontalPrimaryBannerImageUrl() string {
	obj._HorizontalPrimaryBannerImageUrlMutex.RLock()
	defer obj._HorizontalPrimaryBannerImageUrlMutex.RUnlock()
	return obj._HorizontalPrimaryBannerImageUrl
}
func (obj *HomeWidgetV1DisplayConfig) Heading() *gentypes.Text {
	return obj._Heading
}
func (obj *HomeWidgetV1DisplayConfig) PrimaryCta() *CTA {
	return obj._PrimaryCta
}

type HomeWidgetV1TileConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// denotes the tile heading template like "Low interest starting at"
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7612-61407&mode=design&t=sU6gvaCI9rGHdd6l-0
	_HeadingTemplate      string
	_HeadingTemplateMutex *sync.RWMutex
	// denotes the tile subheading template like "INTEREST_AMOUNT %",
	// variable params like INTEREST_AMOUNT would be updated with appropriate value from offer during the config usage.
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7612-61407&mode=design&t=sU6gvaCI9rGHdd6l-0
	_SubHeadingTemplate      string
	_SubHeadingTemplateMutex *sync.RWMutex
	// denotes the left icon which would be displayed on the tile.
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7612-61407&mode=design&t=sU6gvaCI9rGHdd6l-0
	_LeftIconUrl      string
	_LeftIconUrlMutex *sync.RWMutex
	// denotes the right icon which would be displayed on the tile.
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7899-62631&mode=design&t=sU6gvaCI9rGHdd6l-0
	_RightIconUrl      string
	_RightIconUrlMutex *sync.RWMutex
	// denotes the background color of the tile
	_BgColor      string
	_BgColorMutex *sync.RWMutex
	_ArrayElement *gencfg.DynamicArrayElement
}

// denotes the tile heading template like "Low interest starting at"
// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7612-61407&mode=design&t=sU6gvaCI9rGHdd6l-0
func (obj *HomeWidgetV1TileConfig) HeadingTemplate() string {
	obj._HeadingTemplateMutex.RLock()
	defer obj._HeadingTemplateMutex.RUnlock()
	return obj._HeadingTemplate
}

// denotes the tile subheading template like "INTEREST_AMOUNT %",
// variable params like INTEREST_AMOUNT would be updated with appropriate value from offer during the config usage.
// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7612-61407&mode=design&t=sU6gvaCI9rGHdd6l-0
func (obj *HomeWidgetV1TileConfig) SubHeadingTemplate() string {
	obj._SubHeadingTemplateMutex.RLock()
	defer obj._SubHeadingTemplateMutex.RUnlock()
	return obj._SubHeadingTemplate
}

// denotes the left icon which would be displayed on the tile.
// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7612-61407&mode=design&t=sU6gvaCI9rGHdd6l-0
func (obj *HomeWidgetV1TileConfig) LeftIconUrl() string {
	obj._LeftIconUrlMutex.RLock()
	defer obj._LeftIconUrlMutex.RUnlock()
	return obj._LeftIconUrl
}

// denotes the right icon which would be displayed on the tile.
// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7899-62631&mode=design&t=sU6gvaCI9rGHdd6l-0
func (obj *HomeWidgetV1TileConfig) RightIconUrl() string {
	obj._RightIconUrlMutex.RLock()
	defer obj._RightIconUrlMutex.RUnlock()
	return obj._RightIconUrl
}

// denotes the background color of the tile
func (obj *HomeWidgetV1TileConfig) BgColor() string {
	obj._BgColorMutex.RLock()
	defer obj._BgColorMutex.RUnlock()
	return obj._BgColor
}
func (obj *HomeWidgetV1TileConfig) ArrayElement() *gencfg.DynamicArrayElement {
	return obj._ArrayElement
}

type CTA struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_BgColor      string
	_BgColorMutex *sync.RWMutex
	_ArrayElement *gencfg.DynamicArrayElement
	_Text         *gentypes.Text
	_Deeplink     *gendeeplinkcfgv.Deeplink
}

func (obj *CTA) BgColor() string {
	obj._BgColorMutex.RLock()
	defer obj._BgColorMutex.RUnlock()
	return obj._BgColor
}
func (obj *CTA) ArrayElement() *gencfg.DynamicArrayElement {
	return obj._ArrayElement
}
func (obj *CTA) Text() *gentypes.Text {
	return obj._Text
}
func (obj *CTA) Deeplink() *gendeeplinkcfgv.Deeplink {
	return obj._Deeplink
}

type LamfConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ReleaseSegmentDetails *ReleaseSegmentDetails
	_FiftyFinConfig        *config.FiftyFinConfig
}

func (obj *LamfConfig) ReleaseSegmentDetails() *ReleaseSegmentDetails {
	return obj._ReleaseSegmentDetails
}
func (obj *LamfConfig) FiftyFinConfig() *config.FiftyFinConfig {
	return obj._FiftyFinConfig
}

type ReleaseSegmentDetails struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Expression      string
	_ExpressionMutex *sync.RWMutex
}

func (obj *ReleaseSegmentDetails) Expression() string {
	obj._ExpressionMutex.RLock()
	defer obj._ExpressionMutex.RUnlock()
	return obj._Expression
}

type Downtime struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// palPb.Vendor -> *VendorDowntime
	_Vendors *syncmap.Map[string, *VendorDowntime]
}

// palPb.Vendor -> *VendorDowntime
func (obj *Downtime) Vendors() *syncmap.Map[string, *VendorDowntime] {
	return obj._Vendors
}

type VendorDowntime struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Daily *DailyVendorDowntime
}

func (obj *VendorDowntime) Daily() *DailyVendorDowntime {
	return obj._Daily
}

type DailyVendorDowntime struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnable uint32
	// time of the day in IST
	// e.g: "03:00"
	_StartTime      string
	_StartTimeMutex *sync.RWMutex
	// time of the day in IST
	// e.g: "04:00"
	_EndTime      string
	_EndTimeMutex *sync.RWMutex
}

func (obj *DailyVendorDowntime) IsEnable() bool {
	if atomic.LoadUint32(&obj._IsEnable) == 0 {
		return false
	} else {
		return true
	}
}

// time of the day in IST
// e.g: "03:00"
func (obj *DailyVendorDowntime) StartTime() string {
	obj._StartTimeMutex.RLock()
	defer obj._StartTimeMutex.RUnlock()
	return obj._StartTime
}

// time of the day in IST
// e.g: "04:00"
func (obj *DailyVendorDowntime) EndTime() string {
	obj._EndTimeMutex.RLock()
	defer obj._EndTimeMutex.RUnlock()
	return obj._EndTime
}

type EmiComms struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_LowBalComms        *syncmap.Map[string, *EmiCommsPhase]
	_SufficientBalComms *syncmap.Map[string, *EmiCommsPhase]
}

func (obj *EmiComms) LowBalComms() *syncmap.Map[string, *EmiCommsPhase] {
	return obj._LowBalComms
}
func (obj *EmiComms) SufficientBalComms() *syncmap.Map[string, *EmiCommsPhase] {
	return obj._SufficientBalComms
}

type EmiCommsPhase struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// start (inclusive) and end (exclusive) date of the phase relative to the due date
	// Example1: For the phase of 8,7,6,5 days before due date StartDaysPastDueDate should be -8 and EndDaysPastDueDate should be -4
	// Example2: For the phase of 2,3,4 days after due date StartDaysPastDueDate should be 2 and EndDaysPastDueDate should be 5
	_StartDaysPastDueDate int32
	_EndDaysPastDueDate   int32
	_ArrayElement         *gencfg.DynamicArrayElement
	_CommsChannelDetails  *ChannelLevelComms
}

// start (inclusive) and end (exclusive) date of the phase relative to the due date
// Example1: For the phase of 8,7,6,5 days before due date StartDaysPastDueDate should be -8 and EndDaysPastDueDate should be -4
// Example2: For the phase of 2,3,4 days after due date StartDaysPastDueDate should be 2 and EndDaysPastDueDate should be 5
func (obj *EmiCommsPhase) StartDaysPastDueDate() int32 {
	return int32(atomic.LoadInt32(&obj._StartDaysPastDueDate))
}
func (obj *EmiCommsPhase) EndDaysPastDueDate() int32 {
	return int32(atomic.LoadInt32(&obj._EndDaysPastDueDate))
}
func (obj *EmiCommsPhase) ArrayElement() *gencfg.DynamicArrayElement {
	return obj._ArrayElement
}
func (obj *EmiCommsPhase) CommsChannelDetails() *ChannelLevelComms {
	return obj._CommsChannelDetails
}

type ChannelLevelComms struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_GtmPopUp *GtmPopUpConfig
}

func (obj *ChannelLevelComms) GtmPopUp() *GtmPopUpConfig {
	return obj._GtmPopUp
}

type GtmPopUpConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled      uint32
	_CtaList        *syncmap.Map[string, *CTA]
	_Title          *gentypes.Text
	_Body           *gentypes.Text
	_VisualElement  *gentypes.VisualElementImage
	_RadialGradient *gentypes.RadialGradient
}

func (obj *GtmPopUpConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *GtmPopUpConfig) CtaList() *syncmap.Map[string, *CTA] {
	return obj._CtaList
}
func (obj *GtmPopUpConfig) Title() *gentypes.Text {
	return obj._Title
}
func (obj *GtmPopUpConfig) Body() *gentypes.Text {
	return obj._Body
}
func (obj *GtmPopUpConfig) VisualElement() *gentypes.VisualElementImage {
	return obj._VisualElement
}
func (obj *GtmPopUpConfig) RadialGradient() *gentypes.RadialGradient {
	return obj._RadialGradient
}

type RepeatLoansConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// map of string, bool which denotes for which vendor and loan program, we allow user to show a new offer when they had a closed loan with the same vendor and program
	// string to be in format "REPEAT_LOANS_<VENDOR>_<LOAN_PROGRAM>" e.g. REPEAT_LOANS_LIQUILOANS_LOAN_PROGRAM_STPL
	// use the same string to add feature release config if needed for controlled rollout.
	_AllowNewLoanOfferAfterSameOldAccountClosure *syncmap.Map[string, bool]
}

// map of string, bool which denotes for which vendor and loan program, we allow user to show a new offer when they had a closed loan with the same vendor and program
// string to be in format "REPEAT_LOANS_<VENDOR>_<LOAN_PROGRAM>" e.g. REPEAT_LOANS_LIQUILOANS_LOAN_PROGRAM_STPL
// use the same string to add feature release config if needed for controlled rollout.
func (obj *RepeatLoansConfig) AllowNewLoanOfferAfterSameOldAccountClosure() *syncmap.Map[string, bool] {
	return obj._AllowNewLoanOfferAfterSameOldAccountClosure
}

type ConsentConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_FeatureConfig *genapp.FeatureConfig
	_Enabled       bool
	_ScreenConfig  map[string]*config.ConsentScreenConfig
}

func (obj *ConsentConfig) FeatureConfig() *genapp.FeatureConfig {
	return obj._FeatureConfig
}
func (obj *ConsentConfig) Enabled() bool {
	return obj._Enabled
}
func (obj *ConsentConfig) ScreenConfig() map[string]*config.ConsentScreenConfig {
	return obj._ScreenConfig
}

type Lendability struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	// quest experimentation enabled matrix which is used to define the percent of users who will be passed
	// based on the PD category and Affinity Category (can be extended)
	// each value represents a percent of users that will be passed for the given PD and Affinity category (which are the joint key)
	_EvaluationRuleMatrix      romap.ROMap[string, *EvaluationRuleMatrixValue]
	_EvaluationRuleMatrixMutex *sync.RWMutex
	_Url                       string
	_UrlMutex                  *sync.RWMutex
	_ScoreCategoryDetails      *config.ScoreCategoryDetails
}

// quest experimentation enabled matrix which is used to define the percent of users who will be passed
// based on the PD category and Affinity Category (can be extended)
// each value represents a percent of users that will be passed for the given PD and Affinity category (which are the joint key)
func (obj *Lendability) EvaluationRuleMatrix() romap.ROMap[string, *EvaluationRuleMatrixValue] {
	obj._EvaluationRuleMatrixMutex.RLock()
	defer obj._EvaluationRuleMatrixMutex.RUnlock()
	return obj._EvaluationRuleMatrix
}
func (obj *Lendability) Url() string {
	obj._UrlMutex.RLock()
	defer obj._UrlMutex.RUnlock()
	return obj._Url
}
func (obj *Lendability) ScoreCategoryDetails() *config.ScoreCategoryDetails {
	return obj._ScoreCategoryDetails
}

type EvaluationRuleMatrixValue struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	_Value         int64
}

func (obj *EvaluationRuleMatrixValue) value() int {
	return int(atomic.LoadInt64(&obj._Value))
}
func (obj *EvaluationRuleMatrixValue) Value(ctx context.Context) int {
	defVal := obj.value()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Value"}, defVal)
	val, ok := res.(int64)
	if ok {
		return int(val)
	}
	return defVal
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["esignlinkexpirationinsecs"] = _obj.SetEsignLinkExpirationInSecs
	_setters["iscampaignrelatedhomedynamicelementenabled"] = _obj.SetIsCampaignRelatedHomeDynamicElementEnabled
	_setters["pgdbmigrationflag"] = _obj.SetPgdbMigrationFlag
	_setters["enabledataexistencemanager"] = _obj.SetEnableDataExistenceManager

	_obj._CategoryToEmiComms = &syncmap.Map[string, *EmiComms]{}
	_setters["categorytoemicomms"] = _obj.SetCategoryToEmiComms
	_setters["allowedsegmentidsfordisplayinglamfhomedynamicelement"] = _obj.SetAllowedSegmentIdsForDisplayingLAMFHomeDynamicElement
	_obj._AllowedSegmentIdsForDisplayingLAMFHomeDynamicElementMutex = &sync.RWMutex{}
	_Flags, _fieldSetters := gencommon.NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_ProcessLoanInboundTransactionSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessLoanInboundTransactionSqsSubscriber = _ProcessLoanInboundTransactionSqsSubscriber
	helper.AddFieldSetters("processloaninboundtransactionsqssubscriber", _fieldSetters, _setters)
	_OrderUpdatePreApprovedLoanSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderUpdatePreApprovedLoanSqsSubscriber = _OrderUpdatePreApprovedLoanSqsSubscriber
	helper.AddFieldSetters("orderupdatepreapprovedloansqssubscriber", _fieldSetters, _setters)
	_ProcessLoansFiftyfinCallbackSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessLoansFiftyfinCallbackSqsSubscriber = _ProcessLoansFiftyfinCallbackSqsSubscriber
	helper.AddFieldSetters("processloansfiftyfincallbacksqssubscriber", _fieldSetters, _setters)
	_Prepay, _fieldSetters := gencommon.NewPrepay()
	_obj._Prepay = _Prepay
	helper.AddFieldSetters("prepay", _fieldSetters, _setters)
	_HomeWidgetV1Config, _fieldSetters := NewHomeWidgetV1Config()
	_obj._HomeWidgetV1Config = _HomeWidgetV1Config
	helper.AddFieldSetters("homewidgetv1config", _fieldSetters, _setters)
	_LamfConfig, _fieldSetters := NewLamfConfig()
	_obj._LamfConfig = _LamfConfig
	helper.AddFieldSetters("lamfconfig", _fieldSetters, _setters)
	_Downtime, _fieldSetters := NewDowntime()
	_obj._Downtime = _Downtime
	helper.AddFieldSetters("downtime", _fieldSetters, _setters)
	_DeeplinkConfig, _fieldSetters := gencommon.NewDeeplinkConfig()
	_obj._DeeplinkConfig = _DeeplinkConfig
	helper.AddFieldSetters("deeplinkconfig", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_CreditReportConfig, _fieldSetters := gencommon.NewCreditReportConfig()
	_obj._CreditReportConfig = _CreditReportConfig
	helper.AddFieldSetters("creditreportconfig", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig2.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_RepeatLoans, _fieldSetters := NewRepeatLoansConfig()
	_obj._RepeatLoans = _RepeatLoans
	helper.AddFieldSetters("repeatloans", _fieldSetters, _setters)
	_MandateConfig, _fieldSetters := gencommon.NewMandateConfig()
	_obj._MandateConfig = _MandateConfig
	helper.AddFieldSetters("mandateconfig", _fieldSetters, _setters)
	_WealthToTechDataSharingConsentConfig, _fieldSetters := NewConsentConfig()
	_obj._WealthToTechDataSharingConsentConfig = _WealthToTechDataSharingConsentConfig
	helper.AddFieldSetters("wealthtotechdatasharingconsentconfig", _fieldSetters, _setters)
	_SmsFetchingConsentConfig, _fieldSetters := NewConsentConfig()
	_obj._SmsFetchingConsentConfig = _SmsFetchingConsentConfig
	helper.AddFieldSetters("smsfetchingconsentconfig", _fieldSetters, _setters)
	_VendorProgramLevelFeature, _fieldSetters := gencommon.NewVendorProgramLevelFeature()
	_obj._VendorProgramLevelFeature = _VendorProgramLevelFeature
	helper.AddFieldSetters("vendorprogramlevelfeature", _fieldSetters, _setters)
	_LopeOverrideConfig, _fieldSetters := gencommon.NewLopeOverrideConfig()
	_obj._LopeOverrideConfig = _LopeOverrideConfig
	helper.AddFieldSetters("lopeoverrideconfig", _fieldSetters, _setters)
	_Lendability, _fieldSetters := NewLendability()
	_obj._Lendability = _Lendability
	helper.AddFieldSetters("lendability", _fieldSetters, _setters)
	_SgEtbNewEligibilityFlow, _fieldSetters := gencommon.NewFeatureConstraint()
	_obj._SgEtbNewEligibilityFlow = _SgEtbNewEligibilityFlow
	helper.AddFieldSetters("sgetbneweligibilityflow", _fieldSetters, _setters)
	_AutoCancelCurrentLrConfig, _fieldSetters := genconfig2.NewAppVersionConstraintConfig()
	_obj._AutoCancelCurrentLrConfig = _AutoCancelCurrentLrConfig
	helper.AddFieldSetters("autocancelcurrentlrconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["esignlinkexpirationinsecs"] = _obj.SetEsignLinkExpirationInSecs
	_setters["iscampaignrelatedhomedynamicelementenabled"] = _obj.SetIsCampaignRelatedHomeDynamicElementEnabled
	_setters["pgdbmigrationflag"] = _obj.SetPgdbMigrationFlag
	_setters["enabledataexistencemanager"] = _obj.SetEnableDataExistenceManager

	_obj._CategoryToEmiComms = &syncmap.Map[string, *EmiComms]{}
	_setters["categorytoemicomms"] = _obj.SetCategoryToEmiComms
	_setters["allowedsegmentidsfordisplayinglamfhomedynamicelement"] = _obj.SetAllowedSegmentIdsForDisplayingLAMFHomeDynamicElement
	_obj._AllowedSegmentIdsForDisplayingLAMFHomeDynamicElementMutex = &sync.RWMutex{}
	_Flags, _fieldSetters := gencommon.NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_ProcessLoanInboundTransactionSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessLoanInboundTransactionSqsSubscriber = _ProcessLoanInboundTransactionSqsSubscriber
	helper.AddFieldSetters("processloaninboundtransactionsqssubscriber", _fieldSetters, _setters)
	_OrderUpdatePreApprovedLoanSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderUpdatePreApprovedLoanSqsSubscriber = _OrderUpdatePreApprovedLoanSqsSubscriber
	helper.AddFieldSetters("orderupdatepreapprovedloansqssubscriber", _fieldSetters, _setters)
	_ProcessLoansFiftyfinCallbackSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessLoansFiftyfinCallbackSqsSubscriber = _ProcessLoansFiftyfinCallbackSqsSubscriber
	helper.AddFieldSetters("processloansfiftyfincallbacksqssubscriber", _fieldSetters, _setters)
	_Prepay, _fieldSetters := gencommon.NewPrepay()
	_obj._Prepay = _Prepay
	helper.AddFieldSetters("prepay", _fieldSetters, _setters)
	_HomeWidgetV1Config, _fieldSetters := NewHomeWidgetV1Config()
	_obj._HomeWidgetV1Config = _HomeWidgetV1Config
	helper.AddFieldSetters("homewidgetv1config", _fieldSetters, _setters)
	_LamfConfig, _fieldSetters := NewLamfConfig()
	_obj._LamfConfig = _LamfConfig
	helper.AddFieldSetters("lamfconfig", _fieldSetters, _setters)
	_Downtime, _fieldSetters := NewDowntime()
	_obj._Downtime = _Downtime
	helper.AddFieldSetters("downtime", _fieldSetters, _setters)
	_DeeplinkConfig, _fieldSetters := gencommon.NewDeeplinkConfigWithQuest(questFieldPath + "/" + "DeeplinkConfig")
	_obj._DeeplinkConfig = _DeeplinkConfig
	helper.AddFieldSetters("deeplinkconfig", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_CreditReportConfig, _fieldSetters := gencommon.NewCreditReportConfig()
	_obj._CreditReportConfig = _CreditReportConfig
	helper.AddFieldSetters("creditreportconfig", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig2.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_RepeatLoans, _fieldSetters := NewRepeatLoansConfig()
	_obj._RepeatLoans = _RepeatLoans
	helper.AddFieldSetters("repeatloans", _fieldSetters, _setters)
	_MandateConfig, _fieldSetters := gencommon.NewMandateConfig()
	_obj._MandateConfig = _MandateConfig
	helper.AddFieldSetters("mandateconfig", _fieldSetters, _setters)
	_WealthToTechDataSharingConsentConfig, _fieldSetters := NewConsentConfig()
	_obj._WealthToTechDataSharingConsentConfig = _WealthToTechDataSharingConsentConfig
	helper.AddFieldSetters("wealthtotechdatasharingconsentconfig", _fieldSetters, _setters)
	_SmsFetchingConsentConfig, _fieldSetters := NewConsentConfig()
	_obj._SmsFetchingConsentConfig = _SmsFetchingConsentConfig
	helper.AddFieldSetters("smsfetchingconsentconfig", _fieldSetters, _setters)
	_VendorProgramLevelFeature, _fieldSetters := gencommon.NewVendorProgramLevelFeature()
	_obj._VendorProgramLevelFeature = _VendorProgramLevelFeature
	helper.AddFieldSetters("vendorprogramlevelfeature", _fieldSetters, _setters)
	_LopeOverrideConfig, _fieldSetters := gencommon.NewLopeOverrideConfigWithQuest(questFieldPath + "/" + "LopeOverrideConfig")
	_obj._LopeOverrideConfig = _LopeOverrideConfig
	helper.AddFieldSetters("lopeoverrideconfig", _fieldSetters, _setters)
	_Lendability, _fieldSetters := NewLendabilityWithQuest(questFieldPath + "/" + "Lendability")
	_obj._Lendability = _Lendability
	helper.AddFieldSetters("lendability", _fieldSetters, _setters)
	_SgEtbNewEligibilityFlow, _fieldSetters := gencommon.NewFeatureConstraint()
	_obj._SgEtbNewEligibilityFlow = _SgEtbNewEligibilityFlow
	helper.AddFieldSetters("sgetbneweligibilityflow", _fieldSetters, _setters)
	_AutoCancelCurrentLrConfig, _fieldSetters := genconfig2.NewAppVersionConstraintConfig()
	_obj._AutoCancelCurrentLrConfig = _AutoCancelCurrentLrConfig
	helper.AddFieldSetters("autocancelcurrentlrconfig", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init(questFieldPath string) {
	newObj, _ := NewConfig()
	*obj = *newObj
}
func (obj *Config) InitWithQuest(questFieldPath string) {
	newObj, _ := NewConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
	obj._DeeplinkConfig.SetQuestSDK(questSdk)
	obj._LopeOverrideConfig.SetQuestSDK(questSdk)
	obj._Lendability.SetQuestSDK(questSdk)
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	childVars, childVarsErr = obj._DeeplinkConfig.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}

	for _, v := range childVars {
		v.Area = "PersonalLoan" // from quest tag annotation for the component field
		vars = append(vars, v)
	}
	childVars, childVarsErr = obj._LopeOverrideConfig.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}

	for _, v := range childVars {
		v.Area = "PersonalLoan" // from quest tag annotation for the component field
		vars = append(vars, v)
	}
	childVars, childVarsErr = obj._Lendability.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}

	for _, v := range childVars {
		v.Area = "PersonalLoan" // from quest tag annotation for the component field
		vars = append(vars, v)
	}
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "esignlinkexpirationinsecs":
		return obj.SetEsignLinkExpirationInSecs(v.EsignLinkExpirationInSecs, true, nil)
	case "iscampaignrelatedhomedynamicelementenabled":
		return obj.SetIsCampaignRelatedHomeDynamicElementEnabled(v.IsCampaignRelatedHomeDynamicElementEnabled, true, nil)
	case "pgdbmigrationflag":
		return obj.SetPgdbMigrationFlag(v.PgdbMigrationFlag, true, nil)
	case "enabledataexistencemanager":
		return obj.SetEnableDataExistenceManager(v.EnableDataExistenceManager, true, nil)
	case "categorytoemicomms":
		return obj.SetCategoryToEmiComms(v.CategoryToEmiComms, true, path)
	case "allowedsegmentidsfordisplayinglamfhomedynamicelement":
		return obj.SetAllowedSegmentIdsForDisplayingLAMFHomeDynamicElement(v.AllowedSegmentIdsForDisplayingLAMFHomeDynamicElement, true, path)
	case "flags":
		return obj._Flags.Set(v.Flags, true, path)
	case "processloaninboundtransactionsqssubscriber":
		return obj._ProcessLoanInboundTransactionSqsSubscriber.Set(v.ProcessLoanInboundTransactionSqsSubscriber, true, path)
	case "orderupdatepreapprovedloansqssubscriber":
		return obj._OrderUpdatePreApprovedLoanSqsSubscriber.Set(v.OrderUpdatePreApprovedLoanSqsSubscriber, true, path)
	case "processloansfiftyfincallbacksqssubscriber":
		return obj._ProcessLoansFiftyfinCallbackSqsSubscriber.Set(v.ProcessLoansFiftyfinCallbackSqsSubscriber, true, path)
	case "prepay":
		return obj._Prepay.Set(v.Prepay, true, path)
	case "homewidgetv1config":
		return obj._HomeWidgetV1Config.Set(v.HomeWidgetV1Config, true, path)
	case "lamfconfig":
		return obj._LamfConfig.Set(v.LamfConfig, true, path)
	case "downtime":
		return obj._Downtime.Set(v.Downtime, true, path)
	case "deeplinkconfig":
		return obj._DeeplinkConfig.Set(v.DeeplinkConfig, true, path)
	case "questsdk":
		return obj._QuestSdk.Set(v.QuestSdk, true, path)
	case "creditreportconfig":
		return obj._CreditReportConfig.Set(v.CreditReportConfig, true, path)
	case "featurereleaseconfig":
		return obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, true, path)
	case "repeatloans":
		return obj._RepeatLoans.Set(v.RepeatLoans, true, path)
	case "mandateconfig":
		return obj._MandateConfig.Set(v.MandateConfig, true, path)
	case "wealthtotechdatasharingconsentconfig":
		return obj._WealthToTechDataSharingConsentConfig.Set(v.WealthToTechDataSharingConsentConfig, true, path)
	case "smsfetchingconsentconfig":
		return obj._SmsFetchingConsentConfig.Set(v.SmsFetchingConsentConfig, true, path)
	case "vendorprogramlevelfeature":
		return obj._VendorProgramLevelFeature.Set(v.VendorProgramLevelFeature, true, path)
	case "lopeoverrideconfig":
		return obj._LopeOverrideConfig.Set(v.LopeOverrideConfig, true, path)
	case "lendability":
		return obj._Lendability.Set(v.Lendability, true, path)
	case "sgetbneweligibilityflow":
		return obj._SgEtbNewEligibilityFlow.Set(v.SgEtbNewEligibilityFlow, true, path)
	case "autocancelcurrentlrconfig":
		return obj._AutoCancelCurrentLrConfig.Set(v.AutoCancelCurrentLrConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetEsignLinkExpirationInSecs(v.EsignLinkExpirationInSecs, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsCampaignRelatedHomeDynamicElementEnabled(v.IsCampaignRelatedHomeDynamicElementEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPgdbMigrationFlag(v.PgdbMigrationFlag, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableDataExistenceManager(v.EnableDataExistenceManager, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCategoryToEmiComms(v.CategoryToEmiComms, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetAllowedSegmentIdsForDisplayingLAMFHomeDynamicElement(v.AllowedSegmentIdsForDisplayingLAMFHomeDynamicElement, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Flags.Set(v.Flags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessLoanInboundTransactionSqsSubscriber.Set(v.ProcessLoanInboundTransactionSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderUpdatePreApprovedLoanSqsSubscriber.Set(v.OrderUpdatePreApprovedLoanSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessLoansFiftyfinCallbackSqsSubscriber.Set(v.ProcessLoansFiftyfinCallbackSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Prepay.Set(v.Prepay, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._HomeWidgetV1Config.Set(v.HomeWidgetV1Config, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._LamfConfig.Set(v.LamfConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Downtime.Set(v.Downtime, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DeeplinkConfig.Set(v.DeeplinkConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._QuestSdk.Set(v.QuestSdk, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreditReportConfig.Set(v.CreditReportConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RepeatLoans.Set(v.RepeatLoans, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._MandateConfig.Set(v.MandateConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._WealthToTechDataSharingConsentConfig.Set(v.WealthToTechDataSharingConsentConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SmsFetchingConsentConfig.Set(v.SmsFetchingConsentConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VendorProgramLevelFeature.Set(v.VendorProgramLevelFeature, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._LopeOverrideConfig.Set(v.LopeOverrideConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Lendability.Set(v.Lendability, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SgEtbNewEligibilityFlow.Set(v.SgEtbNewEligibilityFlow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AutoCancelCurrentLrConfig.Set(v.AutoCancelCurrentLrConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Logging = v.Logging
	obj._SecureLogging = v.SecureLogging
	obj._Server = v.Server
	obj._FederalDb = v.FederalDb
	obj._FederalPgDb = v.FederalPgDb
	obj._AWS = v.AWS
	obj._RudderStack = v.RudderStack
	obj._Secrets = v.Secrets
	obj._Tracing = v.Tracing
	obj._Vendor = v.Vendor
	obj._KfsExitUrl = v.KfsExitUrl
	obj._DbConfigMap = v.DbConfigMap
	obj._PgDbConfigMap = v.PgDbConfigMap
	obj._VendorsSupported = v.VendorsSupported
	obj._VendorUsed = v.VendorUsed
	obj._SignalWorkflowPublisher = v.SignalWorkflowPublisher
	obj._Notification = v.Notification
	obj._NudgeExitEventPublisher = v.NudgeExitEventPublisher
	obj._EarlySalary = v.EarlySalary
	obj._EnableDynamicElementForAllUsers = v.EnableDynamicElementForAllUsers
	obj._InstantCashSegmentId = v.InstantCashSegmentId
	obj._PreApprovedLoanBucketName = v.PreApprovedLoanBucketName
	obj._Lms = v.Lms
	obj._LoanCalculator = v.LoanCalculator
	obj._LoanOfferPrioritisation = v.LoanOfferPrioritisation
	return nil
}

func (obj *Config) SetEsignLinkExpirationInSecs(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EsignLinkExpirationInSecs", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._EsignLinkExpirationInSecs, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "EsignLinkExpirationInSecs")
	}
	return nil
}
func (obj *Config) SetIsCampaignRelatedHomeDynamicElementEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.IsCampaignRelatedHomeDynamicElementEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCampaignRelatedHomeDynamicElementEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCampaignRelatedHomeDynamicElementEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCampaignRelatedHomeDynamicElementEnabled")
	}
	return nil
}
func (obj *Config) SetPgdbMigrationFlag(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.PgdbMigrationFlag", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._PgdbMigrationFlag, 1)
	} else {
		atomic.StoreUint32(&obj._PgdbMigrationFlag, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "PgdbMigrationFlag")
	}
	return nil
}
func (obj *Config) SetEnableDataExistenceManager(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableDataExistenceManager", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableDataExistenceManager, 1)
	} else {
		atomic.StoreUint32(&obj._EnableDataExistenceManager, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableDataExistenceManager")
	}
	return nil
}
func (obj *Config) SetCategoryToEmiComms(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.EmiComms)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.CategoryToEmiComms", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._CategoryToEmiComms, v, dynamic, path)

}
func (obj *Config) SetAllowedSegmentIdsForDisplayingLAMFHomeDynamicElement(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.AllowedSegmentIdsForDisplayingLAMFHomeDynamicElement", reflect.TypeOf(val))
	}
	obj._AllowedSegmentIdsForDisplayingLAMFHomeDynamicElementMutex.Lock()
	defer obj._AllowedSegmentIdsForDisplayingLAMFHomeDynamicElementMutex.Unlock()
	obj._AllowedSegmentIdsForDisplayingLAMFHomeDynamicElement = roarray.New[string](v)
	return nil
}

func NewHomeWidgetV1Config() (_obj *HomeWidgetV1Config, _setters map[string]dynconf.SetFunc) {
	_obj = &HomeWidgetV1Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_PersonalLoanDisplayConfig, _fieldSetters := NewLoanProductLevelHomeWidgetV1DisplayConfig()
	_obj._PersonalLoanDisplayConfig = _PersonalLoanDisplayConfig
	helper.AddFieldSetters("personalloandisplayconfig", _fieldSetters, _setters)
	_LAMFDisplayConfig, _fieldSetters := NewLoanProductLevelHomeWidgetV1DisplayConfig()
	_obj._LAMFDisplayConfig = _LAMFDisplayConfig
	helper.AddFieldSetters("lamfdisplayconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *HomeWidgetV1Config) Init() {
	newObj, _ := NewHomeWidgetV1Config()
	*obj = *newObj
}

func (obj *HomeWidgetV1Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *HomeWidgetV1Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.HomeWidgetV1Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *HomeWidgetV1Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *HomeWidgetV1Config) setDynamicField(v *config.HomeWidgetV1Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "personalloandisplayconfig":
		return obj._PersonalLoanDisplayConfig.Set(v.PersonalLoanDisplayConfig, true, path)
	case "lamfdisplayconfig":
		return obj._LAMFDisplayConfig.Set(v.LAMFDisplayConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *HomeWidgetV1Config) setDynamicFields(v *config.HomeWidgetV1Config, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._PersonalLoanDisplayConfig.Set(v.PersonalLoanDisplayConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._LAMFDisplayConfig.Set(v.LAMFDisplayConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *HomeWidgetV1Config) setStaticFields(v *config.HomeWidgetV1Config) error {

	return nil
}

func (obj *HomeWidgetV1Config) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *HomeWidgetV1Config.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewLoanProductLevelHomeWidgetV1DisplayConfig() (_obj *LoanProductLevelHomeWidgetV1DisplayConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &LoanProductLevelHomeWidgetV1DisplayConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_ActiveOfferDisplayConfig, _fieldSetters := NewHomeWidgetV1DisplayConfig()
	_obj._ActiveOfferDisplayConfig = _ActiveOfferDisplayConfig
	helper.AddFieldSetters("activeofferdisplayconfig", _fieldSetters, _setters)
	_NoActiveOfferDisplayConfig, _fieldSetters := NewHomeWidgetV1DisplayConfig()
	_obj._NoActiveOfferDisplayConfig = _NoActiveOfferDisplayConfig
	helper.AddFieldSetters("noactiveofferdisplayconfig", _fieldSetters, _setters)
	_CampaignDisplayConfig, _fieldSetters := NewHomeWidgetV1DisplayConfig()
	_obj._CampaignDisplayConfig = _CampaignDisplayConfig
	helper.AddFieldSetters("campaigndisplayconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *LoanProductLevelHomeWidgetV1DisplayConfig) Init() {
	newObj, _ := NewLoanProductLevelHomeWidgetV1DisplayConfig()
	*obj = *newObj
}

func (obj *LoanProductLevelHomeWidgetV1DisplayConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *LoanProductLevelHomeWidgetV1DisplayConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.LoanProductLevelHomeWidgetV1DisplayConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *LoanProductLevelHomeWidgetV1DisplayConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *LoanProductLevelHomeWidgetV1DisplayConfig) setDynamicField(v *config.LoanProductLevelHomeWidgetV1DisplayConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "activeofferdisplayconfig":
		return obj._ActiveOfferDisplayConfig.Set(v.ActiveOfferDisplayConfig, true, path)
	case "noactiveofferdisplayconfig":
		return obj._NoActiveOfferDisplayConfig.Set(v.NoActiveOfferDisplayConfig, true, path)
	case "campaigndisplayconfig":
		return obj._CampaignDisplayConfig.Set(v.CampaignDisplayConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *LoanProductLevelHomeWidgetV1DisplayConfig) setDynamicFields(v *config.LoanProductLevelHomeWidgetV1DisplayConfig, dynamic bool, path []string) (err error) {

	err = obj._ActiveOfferDisplayConfig.Set(v.ActiveOfferDisplayConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._NoActiveOfferDisplayConfig.Set(v.NoActiveOfferDisplayConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CampaignDisplayConfig.Set(v.CampaignDisplayConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *LoanProductLevelHomeWidgetV1DisplayConfig) setStaticFields(v *config.LoanProductLevelHomeWidgetV1DisplayConfig) error {

	return nil
}

func NewHomeWidgetV1DisplayConfig() (_obj *HomeWidgetV1DisplayConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &HomeWidgetV1DisplayConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._WidgetTiles = &syncmap.Map[string, *HomeWidgetV1TileConfig]{}
	_setters["widgettiles"] = _obj.SetWidgetTiles
	_setters["verticalprimarybannerimageurl"] = _obj.SetVerticalPrimaryBannerImageUrl
	_obj._VerticalPrimaryBannerImageUrlMutex = &sync.RWMutex{}
	_setters["horizontalprimarybannerimageurl"] = _obj.SetHorizontalPrimaryBannerImageUrl
	_obj._HorizontalPrimaryBannerImageUrlMutex = &sync.RWMutex{}
	_Heading, _fieldSetters := gentypes.NewText()
	_obj._Heading = _Heading
	helper.AddFieldSetters("heading", _fieldSetters, _setters)
	_PrimaryCta, _fieldSetters := NewCTA()
	_obj._PrimaryCta = _PrimaryCta
	helper.AddFieldSetters("primarycta", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *HomeWidgetV1DisplayConfig) Init() {
	newObj, _ := NewHomeWidgetV1DisplayConfig()
	*obj = *newObj
}

func (obj *HomeWidgetV1DisplayConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *HomeWidgetV1DisplayConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.HomeWidgetV1DisplayConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *HomeWidgetV1DisplayConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *HomeWidgetV1DisplayConfig) setDynamicField(v *config.HomeWidgetV1DisplayConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "widgettiles":
		return obj.SetWidgetTiles(v.WidgetTiles, true, path)
	case "verticalprimarybannerimageurl":
		return obj.SetVerticalPrimaryBannerImageUrl(v.VerticalPrimaryBannerImageUrl, true, nil)
	case "horizontalprimarybannerimageurl":
		return obj.SetHorizontalPrimaryBannerImageUrl(v.HorizontalPrimaryBannerImageUrl, true, nil)
	case "heading":
		return obj._Heading.Set(v.Heading, true, path)
	case "primarycta":
		return obj._PrimaryCta.Set(v.PrimaryCta, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *HomeWidgetV1DisplayConfig) setDynamicFields(v *config.HomeWidgetV1DisplayConfig, dynamic bool, path []string) (err error) {

	err = obj.SetWidgetTiles(v.WidgetTiles, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetVerticalPrimaryBannerImageUrl(v.VerticalPrimaryBannerImageUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetHorizontalPrimaryBannerImageUrl(v.HorizontalPrimaryBannerImageUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._Heading.Set(v.Heading, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PrimaryCta.Set(v.PrimaryCta, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *HomeWidgetV1DisplayConfig) setStaticFields(v *config.HomeWidgetV1DisplayConfig) error {

	return nil
}

func (obj *HomeWidgetV1DisplayConfig) SetWidgetTiles(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.HomeWidgetV1TileConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *HomeWidgetV1DisplayConfig.WidgetTiles", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._WidgetTiles, v, dynamic, path)

}
func (obj *HomeWidgetV1DisplayConfig) SetVerticalPrimaryBannerImageUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *HomeWidgetV1DisplayConfig.VerticalPrimaryBannerImageUrl", reflect.TypeOf(val))
	}
	obj._VerticalPrimaryBannerImageUrlMutex.Lock()
	defer obj._VerticalPrimaryBannerImageUrlMutex.Unlock()
	obj._VerticalPrimaryBannerImageUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "VerticalPrimaryBannerImageUrl")
	}
	return nil
}
func (obj *HomeWidgetV1DisplayConfig) SetHorizontalPrimaryBannerImageUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *HomeWidgetV1DisplayConfig.HorizontalPrimaryBannerImageUrl", reflect.TypeOf(val))
	}
	obj._HorizontalPrimaryBannerImageUrlMutex.Lock()
	defer obj._HorizontalPrimaryBannerImageUrlMutex.Unlock()
	obj._HorizontalPrimaryBannerImageUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "HorizontalPrimaryBannerImageUrl")
	}
	return nil
}

func NewHomeWidgetV1TileConfig() (_obj *HomeWidgetV1TileConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &HomeWidgetV1TileConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["headingtemplate"] = _obj.SetHeadingTemplate
	_obj._HeadingTemplateMutex = &sync.RWMutex{}
	_setters["subheadingtemplate"] = _obj.SetSubHeadingTemplate
	_obj._SubHeadingTemplateMutex = &sync.RWMutex{}
	_setters["lefticonurl"] = _obj.SetLeftIconUrl
	_obj._LeftIconUrlMutex = &sync.RWMutex{}
	_setters["righticonurl"] = _obj.SetRightIconUrl
	_obj._RightIconUrlMutex = &sync.RWMutex{}
	_setters["bgcolor"] = _obj.SetBgColor
	_obj._BgColorMutex = &sync.RWMutex{}
	_ArrayElement, _fieldSetters := gencfg.NewDynamicArrayElement()
	_obj._ArrayElement = _ArrayElement
	helper.AddFieldSetters("arrayelement", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *HomeWidgetV1TileConfig) Init() {
	newObj, _ := NewHomeWidgetV1TileConfig()
	*obj = *newObj
}

func (obj *HomeWidgetV1TileConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *HomeWidgetV1TileConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.HomeWidgetV1TileConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *HomeWidgetV1TileConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *HomeWidgetV1TileConfig) setDynamicField(v *config.HomeWidgetV1TileConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "headingtemplate":
		return obj.SetHeadingTemplate(v.HeadingTemplate, true, nil)
	case "subheadingtemplate":
		return obj.SetSubHeadingTemplate(v.SubHeadingTemplate, true, nil)
	case "lefticonurl":
		return obj.SetLeftIconUrl(v.LeftIconUrl, true, nil)
	case "righticonurl":
		return obj.SetRightIconUrl(v.RightIconUrl, true, nil)
	case "bgcolor":
		return obj.SetBgColor(v.BgColor, true, nil)
	case "arrayelement":
		return obj._ArrayElement.Set(v.ArrayElement, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *HomeWidgetV1TileConfig) setDynamicFields(v *config.HomeWidgetV1TileConfig, dynamic bool, path []string) (err error) {

	err = obj.SetHeadingTemplate(v.HeadingTemplate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSubHeadingTemplate(v.SubHeadingTemplate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLeftIconUrl(v.LeftIconUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRightIconUrl(v.RightIconUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBgColor(v.BgColor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._ArrayElement.Set(v.ArrayElement, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *HomeWidgetV1TileConfig) setStaticFields(v *config.HomeWidgetV1TileConfig) error {

	return nil
}

func (obj *HomeWidgetV1TileConfig) SetHeadingTemplate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *HomeWidgetV1TileConfig.HeadingTemplate", reflect.TypeOf(val))
	}
	obj._HeadingTemplateMutex.Lock()
	defer obj._HeadingTemplateMutex.Unlock()
	obj._HeadingTemplate = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "HeadingTemplate")
	}
	return nil
}
func (obj *HomeWidgetV1TileConfig) SetSubHeadingTemplate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *HomeWidgetV1TileConfig.SubHeadingTemplate", reflect.TypeOf(val))
	}
	obj._SubHeadingTemplateMutex.Lock()
	defer obj._SubHeadingTemplateMutex.Unlock()
	obj._SubHeadingTemplate = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SubHeadingTemplate")
	}
	return nil
}
func (obj *HomeWidgetV1TileConfig) SetLeftIconUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *HomeWidgetV1TileConfig.LeftIconUrl", reflect.TypeOf(val))
	}
	obj._LeftIconUrlMutex.Lock()
	defer obj._LeftIconUrlMutex.Unlock()
	obj._LeftIconUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "LeftIconUrl")
	}
	return nil
}
func (obj *HomeWidgetV1TileConfig) SetRightIconUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *HomeWidgetV1TileConfig.RightIconUrl", reflect.TypeOf(val))
	}
	obj._RightIconUrlMutex.Lock()
	defer obj._RightIconUrlMutex.Unlock()
	obj._RightIconUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "RightIconUrl")
	}
	return nil
}
func (obj *HomeWidgetV1TileConfig) SetBgColor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *HomeWidgetV1TileConfig.BgColor", reflect.TypeOf(val))
	}
	obj._BgColorMutex.Lock()
	defer obj._BgColorMutex.Unlock()
	obj._BgColor = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BgColor")
	}
	return nil
}

func NewCTA() (_obj *CTA, _setters map[string]dynconf.SetFunc) {
	_obj = &CTA{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["bgcolor"] = _obj.SetBgColor
	_obj._BgColorMutex = &sync.RWMutex{}
	_ArrayElement, _fieldSetters := gencfg.NewDynamicArrayElement()
	_obj._ArrayElement = _ArrayElement
	helper.AddFieldSetters("arrayelement", _fieldSetters, _setters)
	_Text, _fieldSetters := gentypes.NewText()
	_obj._Text = _Text
	helper.AddFieldSetters("text", _fieldSetters, _setters)
	_Deeplink, _fieldSetters := gendeeplinkcfgv.NewDeeplink()
	_obj._Deeplink = _Deeplink
	helper.AddFieldSetters("deeplink", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *CTA) Init() {
	newObj, _ := NewCTA()
	*obj = *newObj
}

func (obj *CTA) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CTA) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CTA)
	if !ok {
		return fmt.Errorf("invalid data type %v *CTA", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CTA) setDynamicField(v *config.CTA, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "bgcolor":
		return obj.SetBgColor(v.BgColor, true, nil)
	case "arrayelement":
		return obj._ArrayElement.Set(v.ArrayElement, true, path)
	case "text":
		return obj._Text.Set(v.Text, true, path)
	case "deeplink":
		return obj._Deeplink.Set(v.Deeplink, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CTA) setDynamicFields(v *config.CTA, dynamic bool, path []string) (err error) {

	err = obj.SetBgColor(v.BgColor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._ArrayElement.Set(v.ArrayElement, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Text.Set(v.Text, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Deeplink.Set(v.Deeplink, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CTA) setStaticFields(v *config.CTA) error {

	return nil
}

func (obj *CTA) SetBgColor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CTA.BgColor", reflect.TypeOf(val))
	}
	obj._BgColorMutex.Lock()
	defer obj._BgColorMutex.Unlock()
	obj._BgColor = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BgColor")
	}
	return nil
}

func NewLamfConfig() (_obj *LamfConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &LamfConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_ReleaseSegmentDetails, _fieldSetters := NewReleaseSegmentDetails()
	_obj._ReleaseSegmentDetails = _ReleaseSegmentDetails
	helper.AddFieldSetters("releasesegmentdetails", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *LamfConfig) Init() {
	newObj, _ := NewLamfConfig()
	*obj = *newObj
}

func (obj *LamfConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *LamfConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.LamfConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *LamfConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *LamfConfig) setDynamicField(v *config.LamfConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "releasesegmentdetails":
		return obj._ReleaseSegmentDetails.Set(v.ReleaseSegmentDetails, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *LamfConfig) setDynamicFields(v *config.LamfConfig, dynamic bool, path []string) (err error) {

	err = obj._ReleaseSegmentDetails.Set(v.ReleaseSegmentDetails, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *LamfConfig) setStaticFields(v *config.LamfConfig) error {

	obj._FiftyFinConfig = v.FiftyFinConfig
	return nil
}

func NewReleaseSegmentDetails() (_obj *ReleaseSegmentDetails, _setters map[string]dynconf.SetFunc) {
	_obj = &ReleaseSegmentDetails{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["expression"] = _obj.SetExpression
	_obj._ExpressionMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *ReleaseSegmentDetails) Init() {
	newObj, _ := NewReleaseSegmentDetails()
	*obj = *newObj
}

func (obj *ReleaseSegmentDetails) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ReleaseSegmentDetails) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ReleaseSegmentDetails)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReleaseSegmentDetails", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ReleaseSegmentDetails) setDynamicField(v *config.ReleaseSegmentDetails, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "expression":
		return obj.SetExpression(v.Expression, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ReleaseSegmentDetails) setDynamicFields(v *config.ReleaseSegmentDetails, dynamic bool, path []string) (err error) {

	err = obj.SetExpression(v.Expression, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ReleaseSegmentDetails) setStaticFields(v *config.ReleaseSegmentDetails) error {

	return nil
}

func (obj *ReleaseSegmentDetails) SetExpression(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReleaseSegmentDetails.Expression", reflect.TypeOf(val))
	}
	obj._ExpressionMutex.Lock()
	defer obj._ExpressionMutex.Unlock()
	obj._Expression = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Expression")
	}
	return nil
}

func NewDowntime() (_obj *Downtime, _setters map[string]dynconf.SetFunc) {
	_obj = &Downtime{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._Vendors = &syncmap.Map[string, *VendorDowntime]{}
	_setters["vendors"] = _obj.SetVendors
	return _obj, _setters
}

func (obj *Downtime) Init() {
	newObj, _ := NewDowntime()
	*obj = *newObj
}

func (obj *Downtime) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Downtime) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Downtime)
	if !ok {
		return fmt.Errorf("invalid data type %v *Downtime", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Downtime) setDynamicField(v *config.Downtime, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "vendors":
		return obj.SetVendors(v.Vendors, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Downtime) setDynamicFields(v *config.Downtime, dynamic bool, path []string) (err error) {

	err = obj.SetVendors(v.Vendors, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Downtime) setStaticFields(v *config.Downtime) error {

	return nil
}

func (obj *Downtime) SetVendors(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.VendorDowntime)
	if !ok {
		return fmt.Errorf("invalid data type %v *Downtime.Vendors", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._Vendors, v, dynamic, path)

}

func NewVendorDowntime() (_obj *VendorDowntime, _setters map[string]dynconf.SetFunc) {
	_obj = &VendorDowntime{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_Daily, _fieldSetters := NewDailyVendorDowntime()
	_obj._Daily = _Daily
	helper.AddFieldSetters("daily", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *VendorDowntime) Init() {
	newObj, _ := NewVendorDowntime()
	*obj = *newObj
}

func (obj *VendorDowntime) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *VendorDowntime) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.VendorDowntime)
	if !ok {
		return fmt.Errorf("invalid data type %v *VendorDowntime", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *VendorDowntime) setDynamicField(v *config.VendorDowntime, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "daily":
		return obj._Daily.Set(v.Daily, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *VendorDowntime) setDynamicFields(v *config.VendorDowntime, dynamic bool, path []string) (err error) {

	err = obj._Daily.Set(v.Daily, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *VendorDowntime) setStaticFields(v *config.VendorDowntime) error {

	return nil
}

func NewDailyVendorDowntime() (_obj *DailyVendorDowntime, _setters map[string]dynconf.SetFunc) {
	_obj = &DailyVendorDowntime{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenable"] = _obj.SetIsEnable
	_setters["starttime"] = _obj.SetStartTime
	_obj._StartTimeMutex = &sync.RWMutex{}
	_setters["endtime"] = _obj.SetEndTime
	_obj._EndTimeMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *DailyVendorDowntime) Init() {
	newObj, _ := NewDailyVendorDowntime()
	*obj = *newObj
}

func (obj *DailyVendorDowntime) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DailyVendorDowntime) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DailyVendorDowntime)
	if !ok {
		return fmt.Errorf("invalid data type %v *DailyVendorDowntime", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DailyVendorDowntime) setDynamicField(v *config.DailyVendorDowntime, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenable":
		return obj.SetIsEnable(v.IsEnable, true, nil)
	case "starttime":
		return obj.SetStartTime(v.StartTime, true, nil)
	case "endtime":
		return obj.SetEndTime(v.EndTime, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DailyVendorDowntime) setDynamicFields(v *config.DailyVendorDowntime, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnable(v.IsEnable, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetStartTime(v.StartTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEndTime(v.EndTime, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DailyVendorDowntime) setStaticFields(v *config.DailyVendorDowntime) error {

	return nil
}

func (obj *DailyVendorDowntime) SetIsEnable(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DailyVendorDowntime.IsEnable", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnable, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnable, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnable")
	}
	return nil
}
func (obj *DailyVendorDowntime) SetStartTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DailyVendorDowntime.StartTime", reflect.TypeOf(val))
	}
	obj._StartTimeMutex.Lock()
	defer obj._StartTimeMutex.Unlock()
	obj._StartTime = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "StartTime")
	}
	return nil
}
func (obj *DailyVendorDowntime) SetEndTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DailyVendorDowntime.EndTime", reflect.TypeOf(val))
	}
	obj._EndTimeMutex.Lock()
	defer obj._EndTimeMutex.Unlock()
	obj._EndTime = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "EndTime")
	}
	return nil
}

func NewEmiComms() (_obj *EmiComms, _setters map[string]dynconf.SetFunc) {
	_obj = &EmiComms{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._LowBalComms = &syncmap.Map[string, *EmiCommsPhase]{}
	_setters["lowbalcomms"] = _obj.SetLowBalComms

	_obj._SufficientBalComms = &syncmap.Map[string, *EmiCommsPhase]{}
	_setters["sufficientbalcomms"] = _obj.SetSufficientBalComms
	return _obj, _setters
}

func (obj *EmiComms) Init() {
	newObj, _ := NewEmiComms()
	*obj = *newObj
}

func (obj *EmiComms) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EmiComms) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.EmiComms)
	if !ok {
		return fmt.Errorf("invalid data type %v *EmiComms", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EmiComms) setDynamicField(v *config.EmiComms, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "lowbalcomms":
		return obj.SetLowBalComms(v.LowBalComms, true, path)
	case "sufficientbalcomms":
		return obj.SetSufficientBalComms(v.SufficientBalComms, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EmiComms) setDynamicFields(v *config.EmiComms, dynamic bool, path []string) (err error) {

	err = obj.SetLowBalComms(v.LowBalComms, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetSufficientBalComms(v.SufficientBalComms, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EmiComms) setStaticFields(v *config.EmiComms) error {

	return nil
}

func (obj *EmiComms) SetLowBalComms(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.EmiCommsPhase)
	if !ok {
		return fmt.Errorf("invalid data type %v *EmiComms.LowBalComms", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._LowBalComms, v, dynamic, path)

}
func (obj *EmiComms) SetSufficientBalComms(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.EmiCommsPhase)
	if !ok {
		return fmt.Errorf("invalid data type %v *EmiComms.SufficientBalComms", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._SufficientBalComms, v, dynamic, path)

}

func NewEmiCommsPhase() (_obj *EmiCommsPhase, _setters map[string]dynconf.SetFunc) {
	_obj = &EmiCommsPhase{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["startdayspastduedate"] = _obj.SetStartDaysPastDueDate
	_setters["enddayspastduedate"] = _obj.SetEndDaysPastDueDate
	_ArrayElement, _fieldSetters := gencfg.NewDynamicArrayElement()
	_obj._ArrayElement = _ArrayElement
	helper.AddFieldSetters("arrayelement", _fieldSetters, _setters)
	_CommsChannelDetails, _fieldSetters := NewChannelLevelComms()
	_obj._CommsChannelDetails = _CommsChannelDetails
	helper.AddFieldSetters("commschanneldetails", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *EmiCommsPhase) Init() {
	newObj, _ := NewEmiCommsPhase()
	*obj = *newObj
}

func (obj *EmiCommsPhase) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EmiCommsPhase) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.EmiCommsPhase)
	if !ok {
		return fmt.Errorf("invalid data type %v *EmiCommsPhase", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EmiCommsPhase) setDynamicField(v *config.EmiCommsPhase, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "startdayspastduedate":
		return obj.SetStartDaysPastDueDate(v.StartDaysPastDueDate, true, nil)
	case "enddayspastduedate":
		return obj.SetEndDaysPastDueDate(v.EndDaysPastDueDate, true, nil)
	case "arrayelement":
		return obj._ArrayElement.Set(v.ArrayElement, true, path)
	case "commschanneldetails":
		return obj._CommsChannelDetails.Set(v.CommsChannelDetails, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EmiCommsPhase) setDynamicFields(v *config.EmiCommsPhase, dynamic bool, path []string) (err error) {

	err = obj.SetStartDaysPastDueDate(v.StartDaysPastDueDate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEndDaysPastDueDate(v.EndDaysPastDueDate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._ArrayElement.Set(v.ArrayElement, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CommsChannelDetails.Set(v.CommsChannelDetails, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EmiCommsPhase) setStaticFields(v *config.EmiCommsPhase) error {

	return nil
}

func (obj *EmiCommsPhase) SetStartDaysPastDueDate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *EmiCommsPhase.StartDaysPastDueDate", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._StartDaysPastDueDate, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "StartDaysPastDueDate")
	}
	return nil
}
func (obj *EmiCommsPhase) SetEndDaysPastDueDate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *EmiCommsPhase.EndDaysPastDueDate", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._EndDaysPastDueDate, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "EndDaysPastDueDate")
	}
	return nil
}

func NewChannelLevelComms() (_obj *ChannelLevelComms, _setters map[string]dynconf.SetFunc) {
	_obj = &ChannelLevelComms{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_GtmPopUp, _fieldSetters := NewGtmPopUpConfig()
	_obj._GtmPopUp = _GtmPopUp
	helper.AddFieldSetters("gtmpopup", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *ChannelLevelComms) Init() {
	newObj, _ := NewChannelLevelComms()
	*obj = *newObj
}

func (obj *ChannelLevelComms) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ChannelLevelComms) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ChannelLevelComms)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChannelLevelComms", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ChannelLevelComms) setDynamicField(v *config.ChannelLevelComms, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "gtmpopup":
		return obj._GtmPopUp.Set(v.GtmPopUp, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ChannelLevelComms) setDynamicFields(v *config.ChannelLevelComms, dynamic bool, path []string) (err error) {

	err = obj._GtmPopUp.Set(v.GtmPopUp, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ChannelLevelComms) setStaticFields(v *config.ChannelLevelComms) error {

	return nil
}

func NewGtmPopUpConfig() (_obj *GtmPopUpConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &GtmPopUpConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled

	_obj._CtaList = &syncmap.Map[string, *CTA]{}
	_setters["ctalist"] = _obj.SetCtaList
	_Title, _fieldSetters := gentypes.NewText()
	_obj._Title = _Title
	helper.AddFieldSetters("title", _fieldSetters, _setters)
	_Body, _fieldSetters := gentypes.NewText()
	_obj._Body = _Body
	helper.AddFieldSetters("body", _fieldSetters, _setters)
	_VisualElement, _fieldSetters := gentypes.NewVisualElementImage()
	_obj._VisualElement = _VisualElement
	helper.AddFieldSetters("visualelement", _fieldSetters, _setters)
	_RadialGradient, _fieldSetters := gentypes.NewRadialGradient()
	_obj._RadialGradient = _RadialGradient
	helper.AddFieldSetters("radialgradient", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *GtmPopUpConfig) Init() {
	newObj, _ := NewGtmPopUpConfig()
	*obj = *newObj
}

func (obj *GtmPopUpConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *GtmPopUpConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.GtmPopUpConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *GtmPopUpConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *GtmPopUpConfig) setDynamicField(v *config.GtmPopUpConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "ctalist":
		return obj.SetCtaList(v.CtaList, true, path)
	case "title":
		return obj._Title.Set(v.Title, true, path)
	case "body":
		return obj._Body.Set(v.Body, true, path)
	case "visualelement":
		return obj._VisualElement.Set(v.VisualElement, true, path)
	case "radialgradient":
		return obj._RadialGradient.Set(v.RadialGradient, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *GtmPopUpConfig) setDynamicFields(v *config.GtmPopUpConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaList(v.CtaList, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Title.Set(v.Title, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Body.Set(v.Body, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VisualElement.Set(v.VisualElement, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RadialGradient.Set(v.RadialGradient, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *GtmPopUpConfig) setStaticFields(v *config.GtmPopUpConfig) error {

	return nil
}

func (obj *GtmPopUpConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *GtmPopUpConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *GtmPopUpConfig) SetCtaList(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.CTA)
	if !ok {
		return fmt.Errorf("invalid data type %v *GtmPopUpConfig.CtaList", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._CtaList, v, dynamic, path)

}

func NewRepeatLoansConfig() (_obj *RepeatLoansConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &RepeatLoansConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._AllowNewLoanOfferAfterSameOldAccountClosure = &syncmap.Map[string, bool]{}
	_setters["allownewloanofferaftersameoldaccountclosure"] = _obj.SetAllowNewLoanOfferAfterSameOldAccountClosure
	return _obj, _setters
}

func (obj *RepeatLoansConfig) Init() {
	newObj, _ := NewRepeatLoansConfig()
	*obj = *newObj
}

func (obj *RepeatLoansConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RepeatLoansConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RepeatLoansConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RepeatLoansConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RepeatLoansConfig) setDynamicField(v *config.RepeatLoansConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "allownewloanofferaftersameoldaccountclosure":
		return obj.SetAllowNewLoanOfferAfterSameOldAccountClosure(v.AllowNewLoanOfferAfterSameOldAccountClosure, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RepeatLoansConfig) setDynamicFields(v *config.RepeatLoansConfig, dynamic bool, path []string) (err error) {

	err = obj.SetAllowNewLoanOfferAfterSameOldAccountClosure(v.AllowNewLoanOfferAfterSameOldAccountClosure, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RepeatLoansConfig) setStaticFields(v *config.RepeatLoansConfig) error {

	return nil
}

func (obj *RepeatLoansConfig) SetAllowNewLoanOfferAfterSameOldAccountClosure(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RepeatLoansConfig.AllowNewLoanOfferAfterSameOldAccountClosure", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._AllowNewLoanOfferAfterSameOldAccountClosure, v, path)
}

func NewConsentConfig() (_obj *ConsentConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ConsentConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_FeatureConfig, _fieldSetters := genapp.NewFeatureConfig()
	_obj._FeatureConfig = _FeatureConfig
	helper.AddFieldSetters("featureconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *ConsentConfig) Init() {
	newObj, _ := NewConsentConfig()
	*obj = *newObj
}

func (obj *ConsentConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ConsentConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ConsentConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ConsentConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ConsentConfig) setDynamicField(v *config.ConsentConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "featureconfig":
		return obj._FeatureConfig.Set(v.FeatureConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ConsentConfig) setDynamicFields(v *config.ConsentConfig, dynamic bool, path []string) (err error) {

	err = obj._FeatureConfig.Set(v.FeatureConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ConsentConfig) setStaticFields(v *config.ConsentConfig) error {

	obj._Enabled = v.Enabled
	obj._ScreenConfig = v.ScreenConfig
	return nil
}

func NewLendability() (_obj *Lendability, _setters map[string]dynconf.SetFunc) {
	_obj = &Lendability{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._EvaluationRuleMatrixMutex = &sync.RWMutex{}
	_setters["evaluationrulematrix"] = _obj.SetEvaluationRuleMatrix
	_setters["url"] = _obj.SetUrl
	_obj._UrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewLendabilityWithQuest(questFieldPath string) (_obj *Lendability, _setters map[string]dynconf.SetFunc) {
	_obj = &Lendability{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._EvaluationRuleMatrixMutex = &sync.RWMutex{}
	_setters["evaluationrulematrix"] = _obj.SetEvaluationRuleMatrix
	_setters["url"] = _obj.SetUrl
	_obj._UrlMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *Lendability) Init(questFieldPath string) {
	newObj, _ := NewLendability()
	*obj = *newObj
}
func (obj *Lendability) InitWithQuest(questFieldPath string) {
	newObj, _ := NewLendabilityWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *Lendability) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
	obj._EvaluationRuleMatrix.Iterate(func(key string, val *EvaluationRuleMatrixValue) (stop bool) {
		val.SetQuestSDK(questSdk)
		return false
	})

}

func (obj *Lendability) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Lendability) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	obj._EvaluationRuleMatrix.Iterate(func(key string, val *EvaluationRuleMatrixValue) (stop bool) {
		pathErr := helper.ValidateQuestPath(key)
		if pathErr != nil {
			err = pathErr
			return true
		}
		childVars, childVarsErr := obj._EvaluationRuleMatrix.Get(key).GetQuestVariables()
		if childVarsErr != nil {
			err = childVarsErr
			return true
		}
		for _, v := range childVars {
			vars = append(vars, v)
		}
		return false
	})
	if err != nil {
		return nil, err
	}
	return vars, nil
}

func (obj *Lendability) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Lendability)
	if !ok {
		return fmt.Errorf("invalid data type %v *Lendability", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Lendability) setDynamicField(v *config.Lendability, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "evaluationrulematrix":
		return obj.SetEvaluationRuleMatrix(v.EvaluationRuleMatrix, true, path)
	case "url":
		return obj.SetUrl(v.Url, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Lendability) setDynamicFields(v *config.Lendability, dynamic bool, path []string) (err error) {

	err = obj.SetEvaluationRuleMatrix(v.EvaluationRuleMatrix, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetUrl(v.Url, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Lendability) setStaticFields(v *config.Lendability) error {

	obj._ScoreCategoryDetails = v.ScoreCategoryDetails
	return nil
}

func (obj *Lendability) SetEvaluationRuleMatrix(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.EvaluationRuleMatrixValue)
	if !ok {
		return fmt.Errorf("invalid data type %v *Lendability.EvaluationRuleMatrix", reflect.TypeOf(val))
	}

	obj._EvaluationRuleMatrixMutex.Lock()
	defer obj._EvaluationRuleMatrixMutex.Unlock()
	if !dynamic || obj._EvaluationRuleMatrix.IsNil() {
		var err error
		obj._EvaluationRuleMatrix, err = helper.CreateReadOnlyDynamicMapForQuest[*config.EvaluationRuleMatrixValue, *EvaluationRuleMatrixValue](v, obj.questFieldPath+"/"+"EvaluationRuleMatrix")
		return err
	} else {
		return helper.ApplyDynamicValueROMap[*config.EvaluationRuleMatrixValue, *EvaluationRuleMatrixValue](obj._EvaluationRuleMatrix, v, dynamic, path)
	}

}
func (obj *Lendability) SetUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Lendability.Url", reflect.TypeOf(val))
	}
	obj._UrlMutex.Lock()
	defer obj._UrlMutex.Unlock()
	obj._Url = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Url")
	}
	return nil
}

func NewEvaluationRuleMatrixValue() (_obj *EvaluationRuleMatrixValue, _setters map[string]dynconf.SetFunc) {
	_obj = &EvaluationRuleMatrixValue{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["value"] = _obj.SetValue
	return _obj, _setters
}

func NewEvaluationRuleMatrixValueWithQuest(questFieldPath string) (_obj *EvaluationRuleMatrixValue, _setters map[string]dynconf.SetFunc) {
	_obj = &EvaluationRuleMatrixValue{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["value"] = _obj.SetValue
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *EvaluationRuleMatrixValue) Init(questFieldPath string) {
	newObj, _ := NewEvaluationRuleMatrixValue()
	*obj = *newObj
}
func (obj *EvaluationRuleMatrixValue) InitWithQuest(questFieldPath string) {
	newObj, _ := NewEvaluationRuleMatrixValueWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *EvaluationRuleMatrixValue) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *EvaluationRuleMatrixValue) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EvaluationRuleMatrixValue) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Value",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_INT},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *EvaluationRuleMatrixValue) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.EvaluationRuleMatrixValue)
	if !ok {
		return fmt.Errorf("invalid data type %v *EvaluationRuleMatrixValue", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EvaluationRuleMatrixValue) setDynamicField(v *config.EvaluationRuleMatrixValue, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "value":
		return obj.SetValue(v.Value, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EvaluationRuleMatrixValue) setDynamicFields(v *config.EvaluationRuleMatrixValue, dynamic bool, path []string) (err error) {

	err = obj.SetValue(v.Value, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EvaluationRuleMatrixValue) setStaticFields(v *config.EvaluationRuleMatrixValue) error {

	return nil
}

func (obj *EvaluationRuleMatrixValue) SetValue(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *EvaluationRuleMatrixValue.Value", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._Value, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Value")
	}
	return nil
}
