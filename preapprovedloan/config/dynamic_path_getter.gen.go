// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "esignlinkexpirationinsecs":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EsignLinkExpirationInSecs\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EsignLinkExpirationInSecs, nil
	case "iscampaignrelatedhomedynamicelementenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCampaignRelatedHomeDynamicElementEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCampaignRelatedHomeDynamicElementEnabled, nil
	case "pgdbmigrationflag":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PgdbMigrationFlag\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PgdbMigrationFlag, nil
	case "enabledataexistencemanager":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableDataExistenceManager\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableDataExistenceManager, nil
	case "categorytoemicomms":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.CategoryToEmiComms, nil
		case len(dynamicFieldPath) > 1:

			return obj.CategoryToEmiComms[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.CategoryToEmiComms, nil
	case "allowedsegmentidsfordisplayinglamfhomedynamicelement":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AllowedSegmentIdsForDisplayingLAMFHomeDynamicElement\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AllowedSegmentIdsForDisplayingLAMFHomeDynamicElement, nil
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "processloaninboundtransactionsqssubscriber":
		return obj.ProcessLoanInboundTransactionSqsSubscriber.Get(dynamicFieldPath[1:])
	case "orderupdatepreapprovedloansqssubscriber":
		return obj.OrderUpdatePreApprovedLoanSqsSubscriber.Get(dynamicFieldPath[1:])
	case "processloansfiftyfincallbacksqssubscriber":
		return obj.ProcessLoansFiftyfinCallbackSqsSubscriber.Get(dynamicFieldPath[1:])
	case "prepay":
		return obj.Prepay.Get(dynamicFieldPath[1:])
	case "homewidgetv1config":
		return obj.HomeWidgetV1Config.Get(dynamicFieldPath[1:])
	case "lamfconfig":
		return obj.LamfConfig.Get(dynamicFieldPath[1:])
	case "downtime":
		return obj.Downtime.Get(dynamicFieldPath[1:])
	case "deeplinkconfig":
		return obj.DeeplinkConfig.Get(dynamicFieldPath[1:])
	case "questsdk":
		return obj.QuestSdk.Get(dynamicFieldPath[1:])
	case "creditreportconfig":
		return obj.CreditReportConfig.Get(dynamicFieldPath[1:])
	case "featurereleaseconfig":
		return obj.FeatureReleaseConfig.Get(dynamicFieldPath[1:])
	case "repeatloans":
		return obj.RepeatLoans.Get(dynamicFieldPath[1:])
	case "mandateconfig":
		return obj.MandateConfig.Get(dynamicFieldPath[1:])
	case "wealthtotechdatasharingconsentconfig":
		return obj.WealthToTechDataSharingConsentConfig.Get(dynamicFieldPath[1:])
	case "smsfetchingconsentconfig":
		return obj.SmsFetchingConsentConfig.Get(dynamicFieldPath[1:])
	case "vendorprogramlevelfeature":
		return obj.VendorProgramLevelFeature.Get(dynamicFieldPath[1:])
	case "lopeoverrideconfig":
		return obj.LopeOverrideConfig.Get(dynamicFieldPath[1:])
	case "lendability":
		return obj.Lendability.Get(dynamicFieldPath[1:])
	case "sgetbneweligibilityflow":
		return obj.SgEtbNewEligibilityFlow.Get(dynamicFieldPath[1:])
	case "autocancelcurrentlrconfig":
		return obj.AutoCancelCurrentLrConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *HomeWidgetV1Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "personalloandisplayconfig":
		return obj.PersonalLoanDisplayConfig.Get(dynamicFieldPath[1:])
	case "lamfdisplayconfig":
		return obj.LAMFDisplayConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for HomeWidgetV1Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *LoanProductLevelHomeWidgetV1DisplayConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "activeofferdisplayconfig":
		return obj.ActiveOfferDisplayConfig.Get(dynamicFieldPath[1:])
	case "noactiveofferdisplayconfig":
		return obj.NoActiveOfferDisplayConfig.Get(dynamicFieldPath[1:])
	case "campaigndisplayconfig":
		return obj.CampaignDisplayConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for LoanProductLevelHomeWidgetV1DisplayConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *HomeWidgetV1DisplayConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "widgettiles":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.WidgetTiles, nil
		case len(dynamicFieldPath) > 1:

			return obj.WidgetTiles[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.WidgetTiles, nil
	case "verticalprimarybannerimageurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VerticalPrimaryBannerImageUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VerticalPrimaryBannerImageUrl, nil
	case "horizontalprimarybannerimageurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HorizontalPrimaryBannerImageUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.HorizontalPrimaryBannerImageUrl, nil
	case "heading":
		return obj.Heading.Get(dynamicFieldPath[1:])
	case "primarycta":
		return obj.PrimaryCta.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for HomeWidgetV1DisplayConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *HomeWidgetV1TileConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "headingtemplate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HeadingTemplate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.HeadingTemplate, nil
	case "subheadingtemplate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SubHeadingTemplate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SubHeadingTemplate, nil
	case "lefticonurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LeftIconUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LeftIconUrl, nil
	case "righticonurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RightIconUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RightIconUrl, nil
	case "bgcolor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BgColor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BgColor, nil
	case "arrayelement":
		return obj.ArrayElement.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for HomeWidgetV1TileConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CTA) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "bgcolor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BgColor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BgColor, nil
	case "arrayelement":
		return obj.ArrayElement.Get(dynamicFieldPath[1:])
	case "text":
		return obj.Text.Get(dynamicFieldPath[1:])
	case "deeplink":
		return obj.Deeplink.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CTA", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *LamfConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "releasesegmentdetails":
		return obj.ReleaseSegmentDetails.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for LamfConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ReleaseSegmentDetails) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "expression":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Expression\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Expression, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ReleaseSegmentDetails", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Downtime) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "vendors":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.Vendors, nil
		case len(dynamicFieldPath) > 1:

			return obj.Vendors[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.Vendors, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Downtime", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VendorDowntime) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "daily":
		return obj.Daily.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VendorDowntime", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DailyVendorDowntime) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnable, nil
	case "starttime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"StartTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.StartTime, nil
	case "endtime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EndTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EndTime, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DailyVendorDowntime", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EmiComms) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "lowbalcomms":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.LowBalComms, nil
		case len(dynamicFieldPath) > 1:

			return obj.LowBalComms[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.LowBalComms, nil
	case "sufficientbalcomms":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.SufficientBalComms, nil
		case len(dynamicFieldPath) > 1:

			return obj.SufficientBalComms[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.SufficientBalComms, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EmiComms", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EmiCommsPhase) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "startdayspastduedate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"StartDaysPastDueDate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.StartDaysPastDueDate, nil
	case "enddayspastduedate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EndDaysPastDueDate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EndDaysPastDueDate, nil
	case "arrayelement":
		return obj.ArrayElement.Get(dynamicFieldPath[1:])
	case "commschanneldetails":
		return obj.CommsChannelDetails.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EmiCommsPhase", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ChannelLevelComms) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "gtmpopup":
		return obj.GtmPopUp.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ChannelLevelComms", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *GtmPopUpConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "ctalist":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.CtaList, nil
		case len(dynamicFieldPath) > 1:

			return obj.CtaList[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.CtaList, nil
	case "title":
		return obj.Title.Get(dynamicFieldPath[1:])
	case "body":
		return obj.Body.Get(dynamicFieldPath[1:])
	case "visualelement":
		return obj.VisualElement.Get(dynamicFieldPath[1:])
	case "radialgradient":
		return obj.RadialGradient.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for GtmPopUpConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RepeatLoansConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "allownewloanofferaftersameoldaccountclosure":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.AllowNewLoanOfferAfterSameOldAccountClosure, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"AllowNewLoanOfferAfterSameOldAccountClosure\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.AllowNewLoanOfferAfterSameOldAccountClosure[dynamicFieldPath[1]], nil

		}
		return obj.AllowNewLoanOfferAfterSameOldAccountClosure, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RepeatLoansConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ConsentConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "featureconfig":
		return obj.FeatureConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ConsentConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Lendability) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "evaluationrulematrix":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.EvaluationRuleMatrix, nil
		case len(dynamicFieldPath) > 1:

			return obj.EvaluationRuleMatrix[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.EvaluationRuleMatrix, nil
	case "url":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Url\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Url, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Lendability", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EvaluationRuleMatrixValue) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "value":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Value\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Value, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EvaluationRuleMatrixValue", strings.Join(dynamicFieldPath, "."))
	}
}
