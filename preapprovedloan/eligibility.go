package preapprovedloan

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/names"

	onbPb "github.com/epifi/gamma/api/user/onboarding"

	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	"github.com/epifi/gamma/api/preapprovedloan/secured_loans"
	palWorkflowPb "github.com/epifi/gamma/api/preapprovedloan/workflow"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	userPb "github.com/epifi/gamma/api/user"
	userpb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/pkg/feature/release"
	loansPkg "github.com/epifi/gamma/pkg/loans"
	"github.com/epifi/gamma/preapprovedloan/dao/filters"
	"github.com/epifi/gamma/preapprovedloan/dcpp"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	palEvents "github.com/epifi/gamma/preapprovedloan/events"
	"github.com/epifi/gamma/preapprovedloan/helper"
	ldProviders "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
	priorityProvider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider"
	dcPriorityProvider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/data_collector/provider"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/providers"
)

// todo(naveed) move this inside dcpe.
var mapDataRequirementTypeToOwner = map[palPb.DataRequirementType]palPb.Vendor{
	palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_COMMON:                              palPb.Vendor_EPIFI_TECH,
	palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_EPFO:                                palPb.Vendor_EPIFI_TECH,
	palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_CIBIL:                               palPb.Vendor_EPIFI_TECH,
	palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_AA:                                  palPb.Vendor_EPIFI_TECH,
	palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_LENDEN_PRE_BRE_LOAN_DATA_COLLECTION: palPb.Vendor_LENDEN,
	palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_LENDEN_PRE_BRE_CONSENT:              palPb.Vendor_LENDEN,
}

// nolint: funlen
func (s *Service) CheckLoanEligibility(ctx context.Context, req *palPb.CheckLoanEligibilityRequest) (*palPb.CheckLoanEligibilityResponse, error) {
	res := &palPb.CheckLoanEligibilityResponse{
		RespHeader: &palPb.LoanRespHeader{
			LoanProgram: req.GetLoanHeader().GetLoanProgram(),
			Vendor:      req.GetLoanHeader().GetVendor(),
		},
	}
	// check if loan eligibility process already running for the user.
	// fetch for all the vendors but keep only if lr is relevant to request.
	ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH)
	nonTerminalLrs, nonTerminalLrsErr := s.loanRequestsDao.GetNonTerminalByActorId(ctx, req.GetActorId(), filters.WithLoanRequestType(palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY))
	if nonTerminalLrsErr != nil && !errors.Is(nonTerminalLrsErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in GetNonTerminalByActorId", zap.Error(nonTerminalLrsErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	nonTerminalLrs = filterOutNonRelevantLoanRequestsAccToLoanHeader(req.GetLoanHeader(), nonTerminalLrs)

	for _, nonTerminalLr := range nonTerminalLrs {
		logger.Info(ctx, "non terminal loan request already exists", zap.String(logger.LOAN_REQUEST_ID, nonTerminalLr.GetId()))
		if req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_LAMF {
			ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_FIFTYFIN_LAMF)
			cancelWorkflow, err := s.shouldCancelExistingWorkflowForLamf(ctx, req.GetLoanHeader(), nonTerminalLr)
			if err != nil {
				logger.Error(ctx, "error in processing workflow cancel check", zap.Error(err))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			if !cancelWorkflow {
				res.LoanRequestId = nonTerminalLr.GetId()
				res.Status = rpcPb.StatusOk()
				return res, nil
			}

			// cancel workflow and continue to create a new workflow
			err = s.cancelLoanRequest(ctx, nonTerminalLr.GetId())
			if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "error while canceling previous loan eligibility request", zap.Error(err),
					zap.String(logger.LOAN_REQUEST_ID, nonTerminalLr.GetId()))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
		} else {
			ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(nonTerminalLr.GetVendor()))
			isWfInitiated, err := s.isWorkflowActive(ctx, nonTerminalLr.GetOrchId())
			if err != nil {
				logger.Error(ctx, "error in checking if wf is already initiated", zap.Error(err),
					zap.String(logger.LOAN_REQUEST_ID, nonTerminalLr.GetId()))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			if !isWfInitiated {
				nonTerminalLr.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
				nonTerminalLr.CompletedAt = timestampPb.Now()
				err = s.loanRequestsDao.Update(ctx, nonTerminalLr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS, palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT})
				if err != nil {
					logger.Error(ctx, "error while failing previous loan eligibility request", zap.Error(err),
						zap.String(logger.LOAN_REQUEST_ID, nonTerminalLr.GetId()))
					res.Status = rpcPb.StatusInternal()
					return res, nil
				}
			} else {
				res.LoanRequestId = nonTerminalLr.GetId()
				res.Status = rpcPb.StatusOk()
				res.RespHeader = &palPb.LoanRespHeader{
					LoanProgram: nonTerminalLr.GetLoanProgram(),
					Vendor:      nonTerminalLr.GetVendor(),
				}
				return res, nil
			}
		}
	}

	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))

	userFeatProp, a2lErr := helper.GetUserFeatureProperty(ctx, req.GetActorId(), s.onbClient, s.savingsClient)
	if a2lErr != nil {
		logger.Error(ctx, "error in checking if user is a2l", zap.Error(a2lErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if !userFeatProp.IsFiSAHolder {
		return s.checkNonFiCoreEligibility(ctx, req, userFeatProp), nil
	}

	// check for already exists, if not, create new loan applicant
	// TODO: move the applicant creation to appropriate place, this is not required if common eligibility flow is being used
	_, loanApplicantErr := s.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, req.GetActorId(), req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
	if loanApplicantErr != nil && !errors.Is(loanApplicantErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in GetByActorIdAndVendorAndLoanProgram for loan applicant", zap.Error(loanApplicantErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if loanApplicantErr != nil && errors.Is(loanApplicantErr, epifierrors.ErrRecordNotFound) {
		_, loanApplicantErr = s.loanApplicantDao.Create(ctx, &palPb.LoanApplicant{
			ActorId:         req.GetActorId(),
			Vendor:          req.GetLoanHeader().GetVendor(),
			VendorRequestId: uuid.New().String(),
			LoanProgram:     req.GetLoanHeader().GetLoanProgram(),
			Status:          palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
			SubStatus:       palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI,
		})
		if loanApplicantErr != nil {
			logger.Error(ctx, "error in creating loan applicant", zap.Error(loanApplicantErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	// this section is for realtime eligibility for Fi-Core customers
	var loanRequest *palPb.LoanRequest
	// check if common eligibility is enabled for this vendor and program
	if helper.CheckCDCEligibility(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram(), userFeatProp.IsFiSAHolder) {
		var err error
		// TODO(Feb2025): remove post stabilization
		logger.Info(ctx, fmt.Sprintf("common eligibility is enabled for this vendor: %v and program: %v", req.GetLoanHeader().GetVendor().String(), req.GetLoanHeader().GetLoanProgram().String()))
		loecs, err := helper.GetCDCEligibleLoecs(ctx, s.loecDao, req.GetActorId(), userFeatProp.IsFiSAHolder)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "no active loecs found for actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
				res.Status = rpcPb.StatusPermissionDenied()
				return res, nil
			}
			logger.Error(ctx, "error in fetching active loecs for actor", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		loanRequest, err = s.initiateDataCollectionWorkflow(ctx, req.GetActorId(), loecs)
		if err != nil {
			logger.Error(ctx, "error in initiating data collection workflow", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	} else {
		// this is for the flows where eligibility is done in vendor ownership like LL Subvention programs
		var err error
		// create new loan request for eligibility
		loanRequest, err = s.loanRequestsDao.Create(ctx, &palPb.LoanRequest{
			ActorId:     req.GetActorId(),
			OrchId:      uuid.New().String(),
			Vendor:      req.GetLoanHeader().GetVendor(),
			Type:        palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY,
			Status:      palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
			SubStatus:   palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED,
			LoanProgram: req.GetLoanHeader().GetLoanProgram(),
		})
		if err != nil {
			logger.Error(ctx, "error in creating loan request", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		// creating LoanEligibility workflow initiation payload
		payload := &palWorkflowPb.LoanEligibilityPayload{
			LoanHeader: req.GetLoanHeader(),
		}
		marPayload, marPayloadErr := protojson.Marshal(payload)
		if marPayloadErr != nil {
			logger.Error(ctx, "failed to marshal eligibility workflow payload", zap.Error(marPayloadErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		// initiate LoanEligibility workflow
		err = s.initiateWorkflowV2(ctx, &workflow.ClientReqId{
			Id:     loanRequest.GetOrchId(),
			Client: workflow.Client_PRE_APPROVED_LOAN,
		}, req.GetActorId(), marPayload, celestialPkg.GetTypeEnumFromWorkflowType(palNs.LoanEligibility), workflow.Version_V0)
		if err != nil {
			logger.Error(ctx, "failed to initiate pre approved loan eligibility workflow", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	// publish pal-loan-request-event
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		if publishErr := s.PublishLoanRequestEventForNudgeExit(ctx, &palPb.PALEvent{
			ActorId:         req.GetActorId(),
			ActionTimestamp: timestampPb.New(time.Now()),
			EventInfo: &palPb.PALEvent_LoanRequestEvent{
				LoanRequestEvent: &palPb.LoanRequestEvent{
					LoanRequestEventDetails: &palPb.LoanRequestEventDetails{
						// TODO: Can add loan request type also in this event
						LoanRequestStatus:    loanRequest.GetStatus(),
						LoanRequestSubStatus: loanRequest.GetSubStatus(),
					},
				},
			},
		}); publishErr != nil {
			logger.Error(ctx, "failed to publish pal event for nudge exit", zap.Error(publishErr))
		}
	})

	s.publishAcqEvent(ctx, req.GetActorId(), palEvents.NewEligibilityStarted(req.GetActorId()))

	res.Status = rpcPb.StatusOk()
	res.RespHeader = &palPb.LoanRespHeader{
		LoanProgram: loanRequest.GetLoanProgram(),
		Vendor:      loanRequest.GetVendor(),
	}
	res.LoanRequestId = loanRequest.GetId()
	return res, nil
}

func (s *Service) publishAcqEvent(ctx context.Context, actorId string, event events.Event) {
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		err := s.acqEventPublisher.PublishAcqEventSelectively(ctx, actorId, event)
		if err != nil {
			logger.Error(ctx, "error in publishing acq event", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.PAYLOAD, event.GetEventProperties()))
		}
	})
}

func (s *Service) checkNonFiCoreEligibility(ctx context.Context, req *palPb.CheckLoanEligibilityRequest, userFeatProp *helper.UserFeatureProperty) *palPb.CheckLoanEligibilityResponse {
	res := &palPb.CheckLoanEligibilityResponse{}

	// As soon as an A2L user shows their intent to apply for a loan, they should be converted to Fi Lite so that
	// the next time they open the app, they should land on home so that they can explore other features along with loans.
	// This is being done primarily to provide a way for the user to explore other options when they are stuck or rejected from loans flow.
	if !userFeatProp.IsHomeAccessible {
		fiLiteRes, err := s.onbClient.UpdateFiLiteAccessibility(ctx, &onbPb.UpdateFiLiteAccessibilityRequest{
			ActorId:   req.GetActorId(),
			Source:    onbPb.FiLiteSource_FI_LITE_SOURCE_LOANS_ELIGIBILITY_CHECK,
			IsEnabled: commontypes.BooleanEnum_TRUE,
		})
		if err = epifigrpc.RPCError(fiLiteRes, err); err != nil {
			logger.Error(ctx, "error in updating feature status for a2l user", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res
		}
	}

	nonFiCoreEligEval, evalErr := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_LOANS_NON_FI_CORE_ELIGIBILITY_V2).WithActorId(req.GetActorId()))
	if evalErr != nil {
		logger.Error(ctx, "error in checking if eligibility v2 is enabled", zap.Error(evalErr))
		res.Status = rpcPb.StatusInternal()
		return res
	}

	if !nonFiCoreEligEval {
		logger.Error(ctx, "user not allowed enter non fi core eligibility flow", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpcPb.StatusPermissionDenied()
		return res
	}

	lr, err := s.checkAndInitiateNonFiCoreDataCollectionWf(ctx, req.GetActorId(), userFeatProp)
	if err != nil {
		if errors.Is(err, epifierrors.ErrPermissionDenied) {
			logger.Error(ctx, "data collection wf initiation failed", zap.Error(err))
			res.Status = rpcPb.StatusPermissionDenied()
			return res
		}
		logger.Error(ctx, "error in checking and initiating non fi core data collection workflow", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res
	}

	s.publishAcqEvent(ctx, req.GetActorId(), palEvents.NewEligibilityStarted(req.GetActorId()))

	return &palPb.CheckLoanEligibilityResponse{
		Status: rpcPb.StatusOk(),
		RespHeader: &palPb.LoanRespHeader{
			LoanProgram: lr.GetLoanProgram(),
			Vendor:      lr.GetVendor(),
		},
		LoanRequestId: lr.GetId(),
	}
}

// initiates data collection workflow for checking eligibility for multiple policies simultaneously
// nolint:funlen
func (s *Service) checkAndInitiateNonFiCoreDataCollectionWf(ctx context.Context, actorId string, userFeatProp *helper.UserFeatureProperty) (*palPb.LoanRequest, error) {
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(palPb.Vendor_EPIFI_TECH))
	nonTerminalLr, nonTerminalLrErr := s.loanRequestsDao.GetNonTerminalByActorIdVendorAndLoanProgram(ctx,
		actorId, palPb.Vendor_EPIFI_TECH, palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
		filters.WithLoanRequestType(palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY))
	if nonTerminalLrErr != nil && !errors.Is(nonTerminalLrErr, epifierrors.ErrRecordNotFound) {
		return nil, errors.Wrap(nonTerminalLrErr, "error in GetNonTerminalByActorIdAndVendor")
	}
	if nonTerminalLr != nil {
		isWfInitiated, err := s.isWorkflowActive(ctx, nonTerminalLr.GetOrchId())
		if err != nil {
			return nil, errors.Wrap(err, "error in checking if wf is already initiated")
		}
		if isWfInitiated {
			return nonTerminalLr, nil
		} else {
			nonTerminalLr.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
			nonTerminalLr.CompletedAt = timestampPb.Now()
			err = s.loanRequestsDao.Update(ctx, nonTerminalLr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS, palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT})
			if err != nil {
				return nil, errors.Wrap(err, "error in marking lr as failed when workflow initiation failed")
			}
		}
	}

	activeLoecs, err := s.createLoecAndApplicantForNonFiCore(ctx, actorId, userFeatProp)
	if err != nil {
		return nil, errors.Wrap(err, "error in creating loec and applicant for non fi core programs")
	}

	lr, err := s.initiateDataCollectionWorkflow(ctx, actorId, activeLoecs)
	if err != nil {
		return nil, errors.Wrap(err, "error in initiating data collection workflow")
	}
	return lr, nil
}

func (s *Service) getNonFiCoreVendorToProgramMap(ctx context.Context, actorId string) (map[palPb.Vendor][]palPb.LoanProgram, error) {
	pp, err := s.priorityProviderFactory.GetPriorityProvider(ctx, &priorityProvider.GetPriorityProviderFactoryRequest{IsNonFiCoreUser: true})
	if err != nil {
		return nil, errors.Wrap(err, "error in fetching priority provider")
	}
	loanHeaderPrioritisationRequest := providers.GetLoanHeaderPrioritisationRequest{
		ShouldCheckAvailability: true,
		ActorId:                 actorId,
	}
	lhPrio, err := pp.GetLoanHeaderPrioritisation(ctx, loanHeaderPrioritisationRequest)
	if err != nil {
		return nil, errors.Wrap(err, "error in fetching loan header prioritisation")
	}
	vendorToProgramMap := make(map[palPb.Vendor][]palPb.LoanProgram)
	for _, lh := range lhPrio {
		vendorToProgramMap[lh.GetVendor()] = append(vendorToProgramMap[lh.GetVendor()], lh.GetLoanProgram())
	}
	return vendorToProgramMap, nil
}

func (s *Service) createLoecAndApplicantForNonFiCore(ctx context.Context, actorId string, userFeatProp *helper.UserFeatureProperty) ([]*palPb.LoanOfferEligibilityCriteria, error) {
	var loecsToReturn []*palPb.LoanOfferEligibilityCriteria
	activeLoecs, err := helper.GetCDCEligibleLoecs(ctx, s.loecDao, actorId, userFeatProp.IsFiSAHolder)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, errors.Wrap(err, "error in fetching active loecs for actor")
	}
	loecsToReturn = append(loecsToReturn, activeLoecs...)

	// if common data is collected for any active loecs, user would have given that data recently
	// and we do not want to ask for it again while doing a new evaluation (new loec)
	isCommonDataCollected := false
loop:
	for _, loec := range activeLoecs {
		for _, dataReq := range loec.GetDataRequirementDetails().GetDataRequirements() {
			if dataReq.GetDataRequirementType() == palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_COMMON && dataReq.GetIsCollected() {
				isCommonDataCollected = true
				break loop
			}
		}
	}

	timeNow := time.Now()
	lenderToPrograms, err := s.getNonFiCoreVendorToProgramMap(ctx, actorId)
	if err != nil {
		return nil, err
	}

	for vendor, programs := range lenderToPrograms {
		// reset ownership to fetch loec from all vendor DBs
		ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(vendor))
		txnExec, err := helper.GetTxnExecutorByOwnership(ctx, s.txnExecutorProvider)
		if err != nil {
			return nil, errors.Wrap(err, "error in fetching txn executor")
		}
		var loecs []*palPb.LoanOfferEligibilityCriteria
		err = txnExec.RunTxn(ctx, func(ctx context.Context) error {
			for _, program := range programs {
				_, err = s.loecDao.GetActiveLoecsByActorIdLoanProgramsAndStatuses(ctx, actorId, []palPb.LoanProgram{program}, nil, helper.LoecExpiryDuration, true)
				if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
					return errors.Wrap(err, "error in fetching loecs for actor")
				}
				// Do not create LOEC if there is already a non expired one present
				if err == nil {
					logger.Info(ctx, "non expired LOEC already present, skipping LOEC creation", zap.String(logger.VENDOR, vendor.String()), zap.String(logger.LOAN_PROGRAM, program.String()))
					continue
				}

				_, err = s.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, actorId, vendor, program, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
				if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
					return errors.Wrap(err, "error in GetByActorIdAndVendorAndLoanProgram for loan applicant")
				}
				if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
					_, err = s.loanApplicantDao.Create(ctx, &palPb.LoanApplicant{
						ActorId:         actorId,
						Vendor:          vendor,
						VendorRequestId: uuid.New().String(),
						LoanProgram:     program,
						Status:          palPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_CREATED,
						SubStatus:       palPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI,
					})
					if err != nil {
						return errors.Wrap(err, "error in creating loan applicant")
					}
				}

				loecs = append(loecs, &palPb.LoanOfferEligibilityCriteria{
					ActorId:     actorId,
					Vendor:      vendor,
					Status:      palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED,
					LoanProgram: program,
					DataRequirementDetails: &palPb.DataRequirementDetails{
						DataRequirements: []*palPb.DataRequirement{
							{
								DataRequirementType: palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_COMMON,
								IsCollected:         isCommonDataCollected,
							},
						},
					},
					ExpiredAt: timestampPb.New(timeNow.Add(helper.LoecExpiryDuration)),
				})
			}
			if len(loecs) != 0 {
				err = s.loecDao.CreateBatch(ctx, loecs, len(loecs))
				if err != nil {
					return errors.Wrap(err, "error in creating loecs")
				}
			}
			loecsToReturn = append(loecsToReturn, loecs...)
			return nil
		})
		if err != nil {
			return nil, errors.Wrap(err, "error in creating loan applicant and loec in txn")
		}
	}
	if len(loecsToReturn) == 0 {
		return nil, errors.Wrap(epifierrors.ErrPermissionDenied, "no loecs created, non expired loecs found")
	}
	return loecsToReturn, nil
}

func (s *Service) initiateDataCollectionWorkflow(ctx context.Context, actorId string, loecs []*palPb.LoanOfferEligibilityCriteria) (*palPb.LoanRequest, error) {
	getDataReqResp, getDataReqRespErr := s.dataCollectionPriorityProvider.GetDataRequirement(ctx, &dcpp.GetDataRequirementRequest{
		Loecs: loecs,
	})
	if getDataReqRespErr != nil {
		return nil, errors.Wrap(getDataReqRespErr, "error in fetching data collection types")
	}
	// we want to initiate data collection workflow even if there is no data requirement
	// this can happen if all the data requirements are already fulfilled but BRE was not called
	// in such case the workflow will directly call BRE and update the LOEC status accordingly
	// if this is not done, LOECs will be stuck in CREATED status and never reach terminal status
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(palPb.Vendor_EPIFI_TECH))
	loanRequest, err := s.loanRequestsDao.Create(ctx, &palPb.LoanRequest{
		ActorId:     actorId,
		OrchId:      uuid.New().String(),
		Vendor:      palPb.Vendor_EPIFI_TECH,
		Type:        palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY,
		Status:      palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
		SubStatus:   palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED,
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to create loan request")
	}
	// todo(naveed) fix this.
	// right now there is an edge case here where len(dataReq)=0 we need to specify what bre
	// to call in that case.
	payload := &palWorkflowPb.LoanDataCollectionPayload{
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
			Vendor:      palPb.Vendor_EPIFI_TECH,
		},
		DataTypes: getDataReqResp.GetDataRequirements(),
	}
	marPayload, err := protojson.Marshal(payload)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal data collection workflow payload")
	}
	err = s.initiateWorkflowV2(ctx, &workflow.ClientReqId{
		Id:     loanRequest.GetOrchId(),
		Client: workflow.Client_PRE_APPROVED_LOAN,
	}, actorId, marPayload, celestialPkg.GetTypeEnumFromWorkflowType(palNs.LoanDataCollection), workflow.Version_V0)
	if err != nil {
		return nil, errors.Wrap(err, "failed to initiate pre approved loan data collection workflow")
	}
	return loanRequest, nil
}

func (s *Service) getDataCollectionTypes(ctx context.Context, loecs []*palPb.LoanOfferEligibilityCriteria) ([]palPb.DataRequirementType, error) {
	dcpProvider := s.dcPriorityProviderFactory.GetDataCollectorPriorityProvider()
	return dcpProvider.GetDataCollectionPriority(ctx, &dcPriorityProvider.GetDataCollectionPriorityRequest{
		Loecs:           loecs,
		IsNonFiCoreUser: true,
	})
}

// This method returns a boolean flag which is true if the existing loan eligibility workflow should be cancelled, else it is false
func (s *Service) shouldCancelExistingWorkflowForLamf(ctx context.Context, lh *palPb.LoanHeader, nonTerminalLr *palPb.LoanRequest) (bool, error) {
	latestLse, err := s.loanStepExecutionsDao.GetLatestByRefIdAndFlow(ctx, nonTerminalLr.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return false, fmt.Errorf("failed to get latest lse for loan eligibility flow for loan request: %s, err: %w", nonTerminalLr.GetId(), err)
	}
	if latestLse == nil || errors.Is(err, epifierrors.ErrRecordNotFound) {
		return true, nil
	}
	if latestLse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH {
		// Get portfolio fetch workflow status. If status is equal to Initiated, then we will not cancel the eligibility workflow, else we will.
		pfFetchStatus, pfFetchStatusErr := s.securedLoansClient.GetPortfolioFetchStatus(ctx, &secured_loans.GetPortfolioFetchStatusRequest{
			ClientRequestId: nonTerminalLr.GetOrchId(),
			LoanHeader:      lh,
		})
		if te := epifigrpc.RPCError(pfFetchStatus, pfFetchStatusErr); te != nil && !pfFetchStatus.GetStatus().IsRecordNotFound() {
			return false, fmt.Errorf("error in fetching portfolio fetch flow status: %w", te)
		}
		if pfFetchStatus.GetPortfolioFetchStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED ||
			pfFetchStatus.GetPortfolioFetchStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
			return false, nil
		}
		return true, nil
	}
	return false, nil
}

// nolint: funlen
func (s *Service) AddPanAndDobData(ctx context.Context, req *palPb.AddPanAndDobDataRequest) (*palPb.AddPanAndDobDataResponse, error) {
	res := &palPb.AddPanAndDobDataResponse{}

	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))

	lse, lr, marshalledPayload, err := s.getLseLrAndSignalPayload(ctx, req.GetReqId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_AND_DOB)
	if err != nil {
		logger.Error(ctx, "failed to get lse, lr or marshalled payload", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	user, err := s.rpcHelper.GetUserByActorId(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "failed to fetch user by actor ID", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	userDataPan := &userpb.DataVerificationDetail{
		DataType: userpb.DataType_DATA_TYPE_PAN,
		DataValue: &userpb.DataVerificationDetail_PanNumber{
			PanNumber: req.GetPan(),
		},
		VerificationMethod: userpb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
	}
	userDataDob := &userpb.DataVerificationDetail{
		DataType: userpb.DataType_DATA_TYPE_DOB,
		DataValue: &userpb.DataVerificationDetail_DOB{
			DOB: req.GetDob(),
		},
		VerificationMethod: userpb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
	}

	fieldMask := []userpb.UserFieldMask{userpb.UserFieldMask_DATA_VERIFICATION_DETAILS}
	// only send the additional verification details, userClient.UpdateUser rpc will append it to existing data
	if user.GetDataVerificationDetails() == nil {
		user.DataVerificationDetails = &userpb.DataVerificationDetails{}
	}
	user.GetDataVerificationDetails().DataVerificationDetails = []*userpb.DataVerificationDetail{userDataPan, userDataDob}

	// update the PAN and DOB entered by the user
	err = s.updateUserDetails(ctx, user, fieldMask)
	if err != nil {
		logger.Error(ctx, "failed to update the user details", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if lse.GetDetails().GetApplicantData() == nil {
		lse.Details = &palPb.LoanStepExecutionDetails{
			Details: &palPb.LoanStepExecutionDetails_ApplicantData{
				ApplicantData: &palPb.ApplicantData{
					Pan: req.GetPan(),
					Dob: req.GetDob(),
				},
			},
		}
	} else {
		lse.GetDetails().GetApplicantData().Dob = req.GetDob()
		lse.GetDetails().GetApplicantData().Pan = req.GetPan()
	}

	lseFieldMasks := []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
	}

	lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PAN_ADDED
	err = s.loanStepExecutionsDao.Update(ctx, lse, lseFieldMasks)
	if err != nil {
		logger.Error(ctx, "error while updating loan step")
		res.Status = rpc.StatusInternal()
		return res, err
	}

	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetLoanHeader().GetVendor(), LoanProgram: req.GetLoanHeader().GetLoanProgram()})
	lr.NextAction = deeplinkProvider.GetLoanApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())
	if updateErr := s.loanRequestsDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
	}); updateErr != nil {
		logger.Error(ctx, "error while updating loan request next action")
		res.Status = rpc.StatusInternal()
		return res, err
	}

	// signal workflow that user has entered the details
	if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanApplicationPanVerificationSignal), marshalledPayload); signalErr != nil {
		logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
		// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
	}

	s.sendATLStatusEventIfApplicable(
		ctx,
		req.GetLoanHeader().GetLoanProgram(),
		req.GetLoanHeader().GetVendor(),
		req.GetActorId(),
		"UserDetailsCaptured",
	)

	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) sendATLStatusEventIfApplicable(
	ctx context.Context,
	loanProgram palPb.LoanProgram,
	vendor palPb.Vendor,
	actorId string,
	applicationState string,
) {
	if loanProgram != palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND && loanProgram != palPb.
		LoanProgram_LOAN_PROGRAM_FI_LITE_PL {
		logger.Info(ctx, "skip sending AcquireToLendStatus event", zap.String(logger.ACTOR_ID_V2, actorId))
		return
	}

	deviceId := loansPkg.GetEventsDeviceIDByBestEffort(ctx, actorId, s.userClient, s.authClient)

	logger.Info(ctx, "sending AcquireToLendStatus event", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("applicationState", applicationState))
	s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewAcquireToLendStatus(
		actorId,
		deviceId,
		loanProgram.String(),
		vendor.String(),
		applicationState,
	))
	logger.Info(ctx, "sent AcquireToLendStatus event", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("applicationState", applicationState))
	return
}

// nolint: funlen
func (s *Service) AddNameAndGender(ctx context.Context, req *palPb.AddNameAndGenderRequest) (*palPb.AddNameAndGenderResponse, error) {
	res := &palPb.AddNameAndGenderResponse{
		Status: rpc.StatusOk(),
	}
	// set context to allow Dao to operate with correct DBs
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))

	lse, lr, marshalledPayload, err := s.getLseLrAndSignalPayload(ctx, req.GetLoanReqId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_NAME_AND_GENDER)
	if err != nil {
		logger.Error(ctx, "failed to get lse, lr or marshalled payload", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	user, err := s.rpcHelper.GetUserByActorId(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "failed to fetch user by actor ID", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if req.GetName() == "" {
		logger.Error(ctx, "got empty in name field", zap.Error(err))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}
	user.GetProfile().GivenName = names.ParseStringV2(req.GetName())
	user.GetProfile().GivenGender = req.GetGender()

	userDataPanName := &userpb.DataVerificationDetail{
		DataType: userpb.DataType_DATA_TYPE_PAN_NAME,
		DataValue: &userpb.DataVerificationDetail_PanName{
			PanName: names.ParseStringV2(req.GetName()),
		},
		VerificationMethod: userpb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
	}
	// only send the additional verification details, userClient.UpdateUser rpc will append it to existing data
	if user.GetDataVerificationDetails() == nil {
		user.DataVerificationDetails = &userpb.DataVerificationDetails{}
	}
	user.GetDataVerificationDetails().DataVerificationDetails = []*userpb.DataVerificationDetail{userDataPanName}

	fieldMask := []userpb.UserFieldMask{
		userpb.UserFieldMask_GIVEN_NAME,
		userpb.UserFieldMask_GIVEN_GENDER,
		userpb.UserFieldMask_DATA_VERIFICATION_DETAILS,
	}
	// update the PAN and DOB entered by the user
	err = s.updateUserDetails(ctx, user, fieldMask)
	if err != nil {
		logger.Error(ctx, "failed to update the user details", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if lr.GetNextAction().GetScreen() != deeplinkPb.Screen_PL_APPLICATION_REVIEW_DETAILS_SCREEN {
		txnExec, txnExecErr := helper.GetTxnExecutorByOwnership(ctx, s.txnExecutorProvider)
		if txnExecErr != nil {
			logger.Error(ctx, "error in fetching txn executor from ctx", zap.Error(txnExecErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetLoanHeader().GetVendor(), LoanProgram: req.GetLoanHeader().GetLoanProgram()})
		lr.NextAction = deeplinkProvider.GetLoanApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
		txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
			if updateErr := s.loanRequestsDao.Update(txnCtx, lr, []palPb.LoanRequestFieldMask{
				palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
			}); updateErr != nil {
				return errors.Wrap(updateErr, "error while updating lr")
			}
			if lseUpdateErr := s.loanStepExecutionsDao.Update(txnCtx, lse, []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			}); lseUpdateErr != nil {
				return errors.Wrap(lseUpdateErr, "failed to update loan step status")
			}
			return nil
		})
		if txnErr != nil {
			logger.Error(ctx, "failed to update details in txn", zap.Error(txnErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		// signal workflow
		if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanEligibilityNameAddedSignal), marshalledPayload); signalErr != nil {
			logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
			// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
		}
	}
	return res, nil
}

// InitiateCreditReportFetch starts credit report service
// pan is an option field and can be used in cases when one wants to override using provided pan and not from user service
func (s *Service) initiateCreditReportFetch(ctx context.Context, loanStep *palPb.LoanStepExecution, redirectionDl *deeplinkPb.Deeplink, pan string, vendor preapprovedloans.CreditReportVendor, provenance creditReportV2Pb.Provenance, name *commontypes.Name) (*deeplinkPb.Deeplink, error) {
	if s.creditReportConf.UseCreditReportV2() {
		return s.rpcHelper.InitCreditReportFetchV2(ctx, loanStep, redirectionDl, pan, vendor, provenance, name)
	}
	return s.rpcHelper.InitCreditReportFetchV1(ctx, loanStep, redirectionDl, pan)
}

// nolint: funlen
func (s *Service) FetchCreditReport(ctx context.Context, req *palPb.FetchCreditReportRequest) (*palPb.FetchCreditReportResponse, error) {
	res := &palPb.FetchCreditReportResponse{}

	var lse *palPb.LoanStepExecution
	var err error

	// check if lse id is coming in the request, if yes, fetch the lse using id, else fallback to the previous logic to support backward compatibility
	if req.GetLoanStepExecutionId() != "" {
		lse, err = s.loanStepExecutionsDao.GetById(ctx, req.GetLoanStepExecutionId())
		if err != nil {
			logger.Error(ctx, "failed to fetch the loan step from loan step execution id", zap.Error(err), zap.String(logger.LOAN_STEP_EXECUTION_ID, req.GetLoanStepExecutionId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	} else {
		// fetch the loan step for credit_report_fetch stage
		lse, err = s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanReqId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CREDIT_REPORT_FETCH)
		if err != nil {
			logger.Error(ctx, "failed to fetch the loan step for credit report fetch stage", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	// fetch the loan request
	lr, err := s.loanRequestsDao.GetById(ctx, req.GetLoanReqId())
	if err != nil {
		logger.Error(ctx, "failed to fetch the loan request", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	dlp := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{
		Vendor:      req.GetLoanHeader().GetVendor(),
		LoanProgram: req.GetLoanHeader().GetLoanProgram(),
	})

	pan := ""
	userDataProvider, userDataProviderErr := s.loanDataProvider.FetchUserDetailsProvider(ctx, req.GetLoanHeader())
	if userDataProviderErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get FetchUserDetailsProvider, error: %v", userDataProviderErr))
	}
	// if provider is not implemented for given vendor and loan program, it returns nil instead of error
	if userDataProvider != nil {
		userKycData, userKycDataErr := userDataProvider.FetchUserKycDetails(ctx, &ldProviders.FetchUserKycDetailsRequest{
			LrId: lse.GetRefId(),
			KycDetailsRequired: []ldProviders.UserKycDetailType{
				ldProviders.UserKycDetailTypePan,
			},
		})
		if userKycDataErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get FetchUserKycDetails, error: %v", userKycDataErr))
		}
		if data, ok := userKycData.KycDetailsMap[ldProviders.UserKycDetailTypePan]; ok {
			pan = data.(string)
		}
	}

	var name *commontypes.Name
	var user *userPb.User
	var userErr error
	// in case of fi core users, we can send pan empty and credit report service will fetch from user profile
	if pan == "" {
		user, userErr = s.rpcHelper.GetUserByActorId(ctx, lse.GetActorId())
		if userErr != nil {
			logger.Error(ctx, "failed to fetch user by actor ID", zap.Error(userErr))
			res.Status = rpc.StatusInternal()
		}
		pan = helper.GetPanFromUserDataVerificationDetails(user)
		name = user.GetProfile().GetPanName()
	}
	provenance := creditReportV2Pb.Provenance_PROVENANCE_PERSONAL_LOAN
	// credit report worker sets the TnC on consent screen based on the provenance.
	// adding same provenance for FI_LITE and A2L as we need to show the same TnC for both Loan programs.
	if req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND || req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL || req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY {
		provenance = creditReportV2Pb.Provenance_PROVENANCE_PERSONAL_LOAN_ATL
		if user != nil {
			user, userErr = s.rpcHelper.GetUserByActorId(ctx, lse.GetActorId())
			if userErr != nil {
				logger.Error(ctx, "failed to fetch user by actor ID", zap.Error(userErr))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			name = user.GetProfile().GetPanName()
		}

		if name == nil {
			name = user.GetProfile().GetGivenName()
		}

		if name == nil {
			dvds := user.GetDataVerificationDetails().GetDataVerificationDetails()
			// looping in reverse to get the latest name entry
			for i := len(dvds) - 1; i >= 0; i-- {
				if dvds[i].GetDataType() == userPb.DataType_DATA_TYPE_PAN_NAME {
					name = dvds[i].GetPanName()
					break
				}
			}
		}

		if name == nil {
			logger.Error(ctx, "failed to fetch name for the credit report fetch process")
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}
	getNextActionInSync := false
	if provenance == creditReportV2Pb.Provenance_PROVENANCE_PERSONAL_LOAN_ATL ||
		req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION ||
		req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL {
		getNextActionInSync = true
	}
	redirectionLink, err := dlp.GetLoansApplicationStatusPollDeeplink(ctx, dlp.GetLoanHeader(), req.GetActorId(), req.GetLoanReqId(), &provider.ApplicationStatusPollDeeplinkParams{
		GetNextActionInSync: getNextActionInSync,
	})

	// initiate the credit report download flow and update the LR next action with the returned deeplink
	dl, err := s.initiateCreditReportFetch(ctx, lse, redirectionLink, pan, req.GetCreditReportVendor(), provenance, name)
	if err != nil {
		logger.Error(ctx, "failed to initiate the credit report fetch process", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	lr.NextAction = dl
	lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CREDIT_REPORT_FETCH_INITIATED

	txnExec, err := helper.GetTxnExecutorByOwnership(ctx, s.txnExecutorProvider)
	if err != nil {
		logger.Error(ctx, "error in fetching txn executor from ctx", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
		err = s.loanRequestsDao.Update(txnCtx, lr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION})
		if err != nil {
			return errors.Wrap(err, "can't update LR next action")
		}
		err = s.loanStepExecutionsDao.Update(txnCtx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS})
		if err != nil {
			return errors.Wrap(err, "can't update lse sub status")
		}
		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "error in updating lse and lr in txn", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	// signal workflow
	marshalledPayload, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(&emptyPb.Empty{})
	if err != nil {
		logger.Error(ctx, "failed to marshal signal with empty", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanCreditReportFetchSignal), marshalledPayload); signalErr != nil {
		logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
		// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
	}

	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) getLseLrAndSignalPayload(ctx context.Context, lrId string, flowName palPb.LoanStepExecutionFlow, stepName palPb.LoanStepExecutionStepName) (*palPb.LoanStepExecution, *palPb.LoanRequest, []byte, error) {
	// fetch the loan step for the given flow and step name
	lse, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, lrId, flowName, stepName)
	if err != nil {
		logger.Error(ctx, "failed to fetch the loan step for credit report fetch stage", zap.Error(err))
		return nil, nil, nil, err
	}

	// fetch the loan request
	lr, err := s.loanRequestsDao.GetById(ctx, lrId)
	if err != nil {
		logger.Error(ctx, "failed to fetch the loan request", zap.Error(err))
		return nil, nil, nil, err
	}

	marshalledPayload, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(&emptyPb.Empty{})
	if err != nil {
		logger.Error(ctx, "failed to marshal signal with empty", zap.Error(err))
		return nil, nil, nil, err
	}

	return lse, lr, marshalledPayload, nil
}

func (s *Service) cancelLoanRequest(ctx context.Context, loanRequestId string) error {
	ow := epificontext.OwnershipFromContext[context.Context](ctx)
	// default to federal bank
	if ow == commontypes.Ownership_EPIFI_TECH {
		ow = commontypes.Ownership_FEDERAL_BANK
	}
	txnExecutor, err := s.txnExecutorProvider.GetResourceForOwnership(ow)
	if err != nil {
		return fmt.Errorf("error while fetching txn executor for ownership=%s : %w", ow.String(), err)
	}
	err = txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		lr, txnErr := s.loanRequestsDao.GetById(txnCtx, loanRequestId)
		switch {
		case errors.Is(txnErr, epifierrors.ErrRecordNotFound):
			return epifierrors.ErrRecordNotFound
		case err != nil:
			return fmt.Errorf("error while fetching loan request by id-%s : %w", loanRequestId, txnErr)
		}
		// send empty status array to fetch all loan steps.
		lseArr, txnErr := s.loanStepExecutionsDao.GetByRefIdAndStatuses(txnCtx, lr.GetId(), helper.GetLseNonTerminalStatuses())
		if txnErr != nil && !errors.Is(txnErr, epifierrors.ErrRecordNotFound) {
			return fmt.Errorf("error while fetching active loan step execution entries for refId-%s : %w", loanRequestId, txnErr)
		}
		currTime := timestampPb.Now()
		// update lr fields
		lr.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED
		lr.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CANCELLED
		lr.CompletedAt = currTime

		txnErr = s.loanRequestsDao.Update(txnCtx, lr, []palPb.LoanRequestFieldMask{
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
		})
		if txnErr != nil {
			return fmt.Errorf("error while cancelling loan request entry, id-%s : %w", loanRequestId, txnErr)
		}
		// update all LSE entries
		for _, lse := range lseArr {
			lse.CompletedAt = currTime
			lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED
			txnErr = s.loanStepExecutionsDao.Update(txnCtx, lse, []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_COMPLETED_AT,
			})
			if txnErr != nil {
				return fmt.Errorf("error while cancelling loan step execution entry, id-%s : %w", lse.GetId(), txnErr)
			}
		}
		return nil
	})
	if err != nil {
		return fmt.Errorf("error while canceling loan request(id = %s) : %w", loanRequestId, err)
	}
	return nil
}

func (s *Service) updateUserDetails(ctx context.Context, user *userPb.User, fieldMasks []userPb.UserFieldMask) error {
	updateRes, err := s.userClient.UpdateUser(ctx, &userPb.UpdateUserRequest{
		User:       user,
		UpdateMask: fieldMasks,
	})
	if te := epifigrpc.RPCError(updateRes, err); te != nil {
		return errors.Wrap(te, "failed to update the user info")
	}
	return nil
}

func filterOutNonRelevantLoanRequestsAccToLoanHeader(lh *palPb.LoanHeader, lrs []*palPb.LoanRequest) []*palPb.LoanRequest {
	return lo.Filter(lrs, func(lr *palPb.LoanRequest, _ int) bool {
		if lh.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_LAMF {
			return lr.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_LAMF
		} else {
			return lr.GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_LAMF
		}
	})
}
